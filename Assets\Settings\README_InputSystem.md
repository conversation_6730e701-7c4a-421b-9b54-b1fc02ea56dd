# Input System - Cinder of Darkness

## Overview
The Input System for Cinder of Darkness provides comprehensive multi-device support with automatic device detection, customizable controls, and seamless switching between keyboard/mouse and gamepad inputs. Built on Unity's Input System with custom enhancements for soulslike gameplay.

## Supported Devices

### 🖱️ **Keyboard & Mouse**
- **Movement**: WASD keys
- **Camera**: Mouse delta with sensitivity scaling
- **Actions**: Left/Right click, Space, E, Q, Shift, Escape
- **Magic**: Number keys 1-4
- **Weapon Switching**: Mouse wheel
- **Menu Navigation**: Arrow keys, Enter, Backspace

### 🎮 **Xbox Controllers**
- **Xbox One Controller**: Full support with vibration
- **Xbox Series X|S Controller**: Enhanced haptic feedback
- **Movement**: Left stick with dead zone configuration
- **Camera**: Right stick with sensitivity and dead zone
- **Actions**: A/B/X/Y buttons, triggers, bumpers
- **Magic**: D-Pad up/down cycling
- **Weapon Switching**: D-Pad left/right

### 🎮 **PlayStation Controllers**
- **DualShock 4**: Full support with vibration
- **DualSense**: Advanced haptic feedback and adaptive triggers
- **Movement**: Left stick with dead zone configuration
- **Camera**: Right stick with sensitivity and dead zone
- **Actions**: ✕/O/▢/△ buttons, L2/R2 triggers, L1/R1 bumpers
- **Magic**: D-Pad up/down cycling
- **Weapon Switching**: D-Pad left/right

## Action Maps

### 🎮 **Gameplay Action Map**
| Action | Keyboard/Mouse | Xbox | PlayStation | Description |
|--------|----------------|------|-------------|-------------|
| **Move** | WASD | Left Stick | Left Stick | Character movement |
| **Look** | Mouse Delta | Right Stick | Right Stick | Camera control |
| **Jump** | Space | A | ✕ | Jump/climb action |
| **Attack** | Left Click | RT | R2 | Primary attack |
| **Heavy Attack** | Right Click | LT | L2 | Charged/heavy attack |
| **Block/Parry** | Q | LB | L1 | Defensive action |
| **Dodge/Roll** | Left Shift | B | O | Evasive maneuver |
| **Interact** | E | X | ▢ | Context interaction |
| **Switch Weapon** | Mouse Wheel | D-Pad L/R | D-Pad L/R | Weapon cycling |
| **Switch Magic** | - | D-Pad U/D | D-Pad U/D | Magic cycling |
| **Magic 1-4** | 1-4 Keys | - | - | Direct magic cast |
| **Open Menu** | Escape | Menu/Start | Options | Main menu |

### 📋 **Menu Action Map**
| Action | Keyboard/Mouse | Xbox | PlayStation | Description |
|--------|----------------|------|-------------|-------------|
| **Navigate** | Arrow Keys | D-Pad/Left Stick | D-Pad/Left Stick | Menu navigation |
| **Confirm** | Enter | A | ✕ | Confirm selection |
| **Cancel** | Backspace | B | O | Cancel/back |
| **Close Menu** | Escape | Menu/Start | Options | Close menu |

## File Structure
```
Assets/Input/
├── CinderInputActions.inputactions (Input Action Asset)
├── CinderInput.cs (C# wrapper class)
├── InputConfigurationManager.cs (Settings & remapping)
├── DeviceButtonIconSystem.cs (Dynamic UI icons)
└── README_InputSystem.md (Documentation)

Assets/Scripts/Input/
├── MultiInputControlSystem.cs (Updated with CinderInput)
└── UIButtonPromptSystem.cs (UI integration)

Assets/Resources/
└── CinderInputActions.inputactions (Runtime copy)
```

## Technical Features

### 🔄 **Automatic Device Detection**
- **Real-time Switching**: Seamless transition between input devices
- **Device Identification**: Automatic detection of Xbox vs PlayStation controllers
- **Connection Monitoring**: Dynamic handling of device connect/disconnect
- **Fallback Support**: Graceful degradation for unknown devices

### ⚙️ **Advanced Configuration**
- **Sensitivity Settings**: Separate mouse and gamepad sensitivity
- **Dead Zone Configuration**: Customizable stick and trigger dead zones
- **Inversion Options**: X/Y axis inversion for mouse and gamepad
- **Vibration Control**: Intensity settings and trigger-specific profiles

### 🎯 **Input Remapping**
- **Runtime Remapping**: Change controls without restarting
- **Per-Device Bindings**: Different bindings for different devices
- **Binding Persistence**: Save/load custom control schemes
- **Reset Options**: Restore default bindings

### 📱 **Dynamic UI**
- **Context-Aware Icons**: Button prompts change based on active device
- **Real-time Updates**: UI updates immediately on device switch
- **Custom Icon Sets**: Device-specific button graphics
- **Text Fallbacks**: Display text when icons unavailable

## Integration with Game Systems

### 🎮 **CinderInput Class**
```csharp
// Initialize input system
CinderInput input = new CinderInput();

// Enable appropriate action map
input.EnableGameplay(); // For gameplay
input.EnableMenu();     // For menus
input.EnableAll();      // For both

// Read input values
Vector2 movement = input.GetMoveInput();
Vector2 look = input.GetLookInput();
bool attacking = input.IsAttackPressed();
bool blocking = input.IsBlockHeld();

// Device detection
bool usingGamepad = input.IsUsingGamepad();
string controlScheme = input.GetCurrentControlScheme();

// Vibration (gamepad only)
input.SetGamepadVibration(0.5f, 0.8f, 0.3f);
input.StopGamepadVibration();
```

### ⚙️ **InputConfigurationManager**
```csharp
// Get configuration manager
InputConfigurationManager config = FindObjectOfType<InputConfigurationManager>();

// Adjust sensitivity
config.SetMouseSensitivity(1.5f);
config.SetGamepadSensitivity(2.0f);

// Configure dead zones
config.SetLeftStickDeadZone(0.15f);
config.SetRightStickDeadZone(0.1f);

// Inversion settings
config.SetMouseYInversion(true);
config.SetGamepadYInversion(false);

// Vibration settings
config.SetVibrationEnabled(true);
config.SetVibrationIntensity(0.8f);

// Input remapping
config.StartRemapping("Attack", (success) => {
    Debug.Log($"Remapping {(success ? "successful" : "failed")}");
});

// Save/load settings
config.SaveSettings();
config.LoadSettings();
```

### 🎨 **DeviceButtonIconSystem**
```csharp
// Get icon system
DeviceButtonIconSystem iconSystem = FindObjectOfType<DeviceButtonIconSystem>();

// Get current device info
string deviceName = iconSystem.GetCurrentDeviceName();
bool usingGamepad = iconSystem.IsUsingGamepad();

// Get icon for action
Sprite attackIcon = iconSystem.GetIconForAction("Attack");

// Register button prompt
ButtonPrompt prompt = GetComponent<ButtonPrompt>();
iconSystem.RegisterButtonPrompt(prompt);
```

## Vibration Profiles

### 🎮 **Haptic Feedback System**
The system includes predefined vibration profiles for different game events:

| Trigger | Low Freq | High Freq | Duration | Usage |
|---------|----------|-----------|----------|-------|
| **Weapon Hit** | 0.3 | 0.7 | 0.2s | Successful attack |
| **Take Damage** | 0.8 | 0.4 | 0.3s | Player hurt |
| **Boss Hit** | 1.0 | 1.0 | 0.5s | Major enemy hit |
| **Innocent Killed** | 0.6 | 0.2 | 2.0s | Moral consequence |
| **Emotional Moment** | 0.4 | 0.1 | 1.5s | Story beats |
| **Heartbeat Sync** | 0.5 | 0.0 | 0.8s | Psychological state |

### 🎯 **Usage Example**
```csharp
// Trigger vibration for specific events
MultiInputControlSystem inputSystem = FindObjectOfType<MultiInputControlSystem>();
inputSystem.TriggerVibration(VibrationProfile.VibrationTrigger.WeaponHit);
```

## Performance Optimization

### 💾 **Memory Efficiency**
- **Lightweight Wrapper**: CinderInput class minimizes overhead
- **Lazy Loading**: Input actions loaded only when needed
- **Resource Management**: Proper disposal of input resources
- **Caching**: Icon and binding data cached for performance

### ⚡ **CPU Optimization**
- **Event-Driven**: Input callbacks instead of polling
- **Selective Updates**: Only update UI when device changes
- **Batched Operations**: Group multiple input operations
- **Dead Zone Filtering**: Hardware-level dead zone processing

### 🔧 **Build Optimization**
- **Platform Stripping**: Remove unused input backends
- **Asset Compression**: Optimized input action asset
- **Runtime Loading**: Input actions loaded from Resources
- **Fallback Systems**: Graceful degradation for missing assets

## Accessibility Features

### ♿ **Inclusive Design**
- **Remappable Controls**: All inputs can be reassigned
- **Hold/Toggle Options**: Alternative input methods
- **Visual Feedback**: Clear button prompts and feedback
- **Audio Cues**: Sound feedback for input actions

### 🎯 **Customization Options**
- **Sensitivity Scaling**: Wide range of sensitivity options
- **Dead Zone Adjustment**: Accommodate different motor abilities
- **One-Handed Layouts**: Alternative control schemes
- **Colorblind Support**: High contrast button icons

## Troubleshooting

### ❗ **Common Issues**

**"CinderInputActions not found!"**
- Ensure the .inputactions file is in Assets/Input/ folder
- Check that a copy exists in Assets/Resources/ for runtime loading
- Verify the file is not corrupted

**Controller not detected**
- Check USB connection or wireless pairing
- Verify controller drivers are installed
- Test controller in other applications
- Check Unity Input System package is installed

**Button prompts not updating**
- Ensure DeviceButtonIconSystem is in the scene
- Check that ButtonPrompt components are properly configured
- Verify icon sets are assigned in the inspector
- Force update with iconSystem.ForceUpdate()

**Input lag or responsiveness issues**
- Adjust dead zone settings (lower values = more sensitive)
- Check for conflicting input systems
- Verify frame rate is stable
- Reduce input processing overhead

### 🔧 **Debug Tools**
- **Console Logging**: Enable debug logs for input events
- **Input Debugger**: Use Unity's Input System debugger
- **Device Monitor**: Check connected devices in real-time
- **Binding Viewer**: Inspect current input bindings

## Future Enhancements

### 🚀 **Planned Features**
- **Adaptive Triggers**: Full DualSense adaptive trigger support
- **Motion Controls**: Gyroscope support for special actions
- **Voice Commands**: Voice input for accessibility
- **Custom Gestures**: Complex input combinations
- **Cloud Sync**: Save settings across devices

### 🎮 **Advanced Haptics**
- **Contextual Vibration**: Environment-based haptic feedback
- **Emotional Haptics**: Vibration patterns matching story beats
- **Spatial Haptics**: Directional feedback for 3D audio
- **Intensity Scaling**: Dynamic vibration based on game state

## Credits
Built on Unity's Input System with custom enhancements for Cinder of Darkness
Designed for accessibility, performance, and seamless multi-device support
All input configurations are customizable and persistent across sessions
