using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Pause Menu UI for Cinder of Darkness.
    /// Handles game pausing, settings access, and save/load functionality.
    /// </summary>
    public class PauseMenuUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Pause Menu")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button saveGameButton;
        [SerializeField] private Button loadGameButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button mainMenuButton;
        [SerializeField] private But<PERSON> quitGameButton;

        [Header("Save/Load Panel")]
        [SerializeField] private GameObject saveLoadPanel;
        [SerializeField] private Transform saveSlotContainer;
        [SerializeField] private GameObject saveSlotPrefab;
        [SerializeField] private Button saveLoadBackButton;
        [SerializeField] private TextMesh<PERSON><PERSON><PERSON><PERSON><PERSON> saveLoadTitleText;

        [Header("Confirmation Dialog")]
        [SerializeField] private GameObject confirmationPanel;
        [SerializeField] private TextMeshProUGUI confirmationText;
        [SerializeField] private Button confirmYesButton;
        [SerializeField] private Button confirmNoButton;

        [Header("Visual Effects")]
        [SerializeField] private Image backgroundBlur;
        [SerializeField] private CanvasGroup pauseCanvasGroup;
        [SerializeField] private AnimationCurve fadeInCurve;
        [SerializeField] private float fadeSpeed = 3f;

        [Header("Audio")]
        [SerializeField] private AudioClip pauseSound;
        [SerializeField] private AudioClip resumeSound;
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private bool isPaused = false;
        private bool isSaving = false;
        private SettingsMenuUI settingsMenu;
        private SaveSystem saveSystem;
        private System.Action pendingConfirmAction;
        private float originalTimeScale;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            settingsMenu = FindObjectOfType<SettingsMenuUI>();
            saveSystem = FindObjectOfType<SaveSystem>();
            
            if (saveSystem == null)
            {
                var saveSystemObj = new GameObject("SaveSystem");
                saveSystem = saveSystemObj.AddComponent<SaveSystem>();
            }
        }

        private void SetupUI()
        {
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);

            if (saveLoadPanel != null)
                saveLoadPanel.SetActive(false);

            if (confirmationPanel != null)
                confirmationPanel.SetActive(false);

            if (backgroundBlur != null)
                backgroundBlur.gameObject.SetActive(false);

            if (pauseCanvasGroup != null)
                pauseCanvasGroup.alpha = 0f;
        }

        private void SetupEventListeners()
        {
            if (resumeButton != null)
                resumeButton.onClick.AddListener(ResumeGame);

            if (saveGameButton != null)
                saveGameButton.onClick.AddListener(ShowSaveMenu);

            if (loadGameButton != null)
                loadGameButton.onClick.AddListener(ShowLoadMenu);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OpenSettings);

            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(ConfirmReturnToMainMenu);

            if (quitGameButton != null)
                quitGameButton.onClick.AddListener(ConfirmQuitGame);

            if (saveLoadBackButton != null)
                saveLoadBackButton.onClick.AddListener(HideSaveLoadMenu);

            if (confirmYesButton != null)
                confirmYesButton.onClick.AddListener(ConfirmAction);

            if (confirmNoButton != null)
                confirmNoButton.onClick.AddListener(CancelConfirmation);
        }
        #endregion

        #region Pause Management
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (confirmationPanel != null && confirmationPanel.activeInHierarchy)
                {
                    CancelConfirmation();
                }
                else if (saveLoadPanel != null && saveLoadPanel.activeInHierarchy)
                {
                    HideSaveLoadMenu();
                }
                else if (settingsMenu != null && settingsMenu.IsOpen)
                {
                    settingsMenu.CloseSettings();
                }
                else
                {
                    TogglePause();
                }
            }
        }

        public void TogglePause()
        {
            if (isPaused)
                ResumeGame();
            else
                PauseGame();
        }

        public void PauseGame()
        {
            if (isPaused) return;

            isPaused = true;
            originalTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            if (backgroundBlur != null)
                backgroundBlur.gameObject.SetActive(true);

            StartCoroutine(FadeInPauseMenu());
            PlaySound(pauseSound);

            // Disable player input
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = false;
        }

        public void ResumeGame()
        {
            if (!isPaused) return;

            StartCoroutine(FadeOutPauseMenu());
            PlaySound(resumeSound);
        }

        private IEnumerator FadeInPauseMenu()
        {
            if (pauseCanvasGroup == null) yield break;

            float elapsed = 0f;
            float duration = 1f / fadeSpeed;

            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                float curveValue = fadeInCurve.Evaluate(progress);
                
                pauseCanvasGroup.alpha = curveValue;
                yield return null;
            }

            pauseCanvasGroup.alpha = 1f;
        }

        private IEnumerator FadeOutPauseMenu()
        {
            if (pauseCanvasGroup == null)
            {
                CompletePauseExit();
                yield break;
            }

            float elapsed = 0f;
            float duration = 1f / fadeSpeed;
            float startAlpha = pauseCanvasGroup.alpha;

            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                
                pauseCanvasGroup.alpha = Mathf.Lerp(startAlpha, 0f, progress);
                yield return null;
            }

            pauseCanvasGroup.alpha = 0f;
            CompletePauseExit();
        }

        private void CompletePauseExit()
        {
            isPaused = false;
            Time.timeScale = originalTimeScale;

            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);

            if (backgroundBlur != null)
                backgroundBlur.gameObject.SetActive(false);

            if (saveLoadPanel != null)
                saveLoadPanel.SetActive(false);

            // Re-enable player input
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = true;
        }
        #endregion

        #region Menu Actions
        private void ShowSaveMenu()
        {
            PlayButtonSound();
            isSaving = true;
            ShowSaveLoadMenu("Save Game");
        }

        private void ShowLoadMenu()
        {
            PlayButtonSound();
            isSaving = false;
            ShowSaveLoadMenu("Load Game");
        }

        private void ShowSaveLoadMenu(string title)
        {
            if (saveLoadPanel != null)
            {
                saveLoadPanel.SetActive(true);
                pauseMenuPanel.SetActive(false);

                if (saveLoadTitleText != null)
                    saveLoadTitleText.text = title;

                CreateSaveSlots();
            }
        }

        private void HideSaveLoadMenu()
        {
            PlayButtonSound();
            
            if (saveLoadPanel != null)
            {
                saveLoadPanel.SetActive(false);
                pauseMenuPanel.SetActive(true);
                
                ClearSaveSlots();
            }
        }

        private void OpenSettings()
        {
            PlayButtonSound();
            
            if (settingsMenu != null)
            {
                settingsMenu.OpenSettings();
            }
        }

        private void ConfirmReturnToMainMenu()
        {
            PlayButtonSound();
            ShowConfirmation("Return to Main Menu?\nUnsaved progress will be lost.", ReturnToMainMenu);
        }

        private void ConfirmQuitGame()
        {
            PlayButtonSound();
            ShowConfirmation("Quit Game?\nUnsaved progress will be lost.", QuitGame);
        }
        #endregion

        #region Save/Load System
        private void CreateSaveSlots()
        {
            if (saveSlotPrefab == null || saveSlotContainer == null) return;

            ClearSaveSlots();

            for (int i = 0; i < 3; i++) // 3 save slots
            {
                GameObject slot = Instantiate(saveSlotPrefab, saveSlotContainer);
                var saveSlot = slot.GetComponent<PauseSaveSlotUI>();
                
                if (saveSlot != null)
                {
                    saveSlot.Setup(i, isSaving, this);
                }
            }
        }

        private void ClearSaveSlots()
        {
            foreach (Transform child in saveSlotContainer)
            {
                Destroy(child.gameObject);
            }
        }

        public void OnSaveSlotSelected(int slotIndex)
        {
            PlayButtonSound();
            
            if (isSaving)
            {
                SaveGame(slotIndex);
            }
            else
            {
                LoadGame(slotIndex);
            }
        }

        private void SaveGame(int slotIndex)
        {
            if (saveSystem == null) return;

            try
            {
                saveSystem.SaveGame(slotIndex);
                Debug.Log($"Game saved to slot {slotIndex}");
                HideSaveLoadMenu();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save game: {e.Message}");
            }
        }

        private void LoadGame(int slotIndex)
        {
            if (saveSystem == null) return;

            try
            {
                saveSystem.LoadGame(slotIndex);
                Debug.Log($"Game loaded from slot {slotIndex}");
                ResumeGame();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load game: {e.Message}");
            }
        }
        #endregion

        #region Confirmation Dialog
        private void ShowConfirmation(string message, System.Action confirmAction)
        {
            if (confirmationPanel != null)
            {
                confirmationPanel.SetActive(true);
                pauseMenuPanel.SetActive(false);

                if (confirmationText != null)
                    confirmationText.text = message;

                pendingConfirmAction = confirmAction;
            }
        }

        private void ConfirmAction()
        {
            PlayButtonSound();
            
            if (pendingConfirmAction != null)
            {
                pendingConfirmAction.Invoke();
                pendingConfirmAction = null;
            }

            CancelConfirmation();
        }

        private void CancelConfirmation()
        {
            PlayButtonSound();
            
            if (confirmationPanel != null)
            {
                confirmationPanel.SetActive(false);
                pauseMenuPanel.SetActive(true);
            }

            pendingConfirmAction = null;
        }
        #endregion

        #region Scene Management
        private void ReturnToMainMenu()
        {
            Time.timeScale = 1f; // Reset time scale before scene change
            SceneManager.LoadScene("MainMenu");
        }

        private void QuitGame()
        {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
        #endregion

        #region Audio
        private void PlayButtonSound()
        {
            PlaySound(buttonClickSound);
        }

        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public Properties
        public bool IsPaused => isPaused;
        #endregion
    }

    #region Supporting Classes
    public class PauseSaveSlotUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI slotNumberText;
        [SerializeField] private TextMeshProUGUI saveInfoText;
        [SerializeField] private TextMeshProUGUI timestampText;
        [SerializeField] private Button selectButton;
        [SerializeField] private Image screenshotImage;

        private int slotIndex;
        private bool isSaving;
        private PauseMenuUI pauseMenu;

        public void Setup(int index, bool saving, PauseMenuUI menu)
        {
            slotIndex = index;
            isSaving = saving;
            pauseMenu = menu;

            if (slotNumberText != null)
                slotNumberText.text = $"Slot {index + 1}";

            if (selectButton != null)
                selectButton.onClick.AddListener(() => pauseMenu.OnSaveSlotSelected(slotIndex));

            UpdateSaveInfo();
        }

        private void UpdateSaveInfo()
        {
            // Check if save file exists
            bool saveExists = PlayerPrefs.HasKey($"SaveSlot_{slotIndex}");

            if (saveInfoText != null)
            {
                if (isSaving)
                {
                    saveInfoText.text = saveExists ? "Overwrite Save" : "Save Game";
                }
                else
                {
                    saveInfoText.text = saveExists ? "Load Game" : "Empty Slot";
                }
            }

            if (timestampText != null)
            {
                if (saveExists)
                {
                    string timestamp = PlayerPrefs.GetString($"SaveSlot_{slotIndex}_Timestamp", "Unknown");
                    timestampText.text = timestamp;
                }
                else
                {
                    timestampText.text = "";
                }
            }

            // Disable button if trying to load from empty slot
            if (selectButton != null)
            {
                selectButton.interactable = isSaving || saveExists;
            }
        }
    }
    #endregion
}
