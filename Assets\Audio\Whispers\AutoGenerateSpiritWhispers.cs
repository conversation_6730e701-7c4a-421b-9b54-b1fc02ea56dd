using UnityEngine;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;

/// <summary>
/// Automatically generates spirit whisper audio clips when the project loads
/// Ensures RegretAfterKillingSystem has functional audio assets
/// </summary>
[InitializeOnLoad]
public class AutoGenerateSpiritWhispers
{
    static AutoGenerateSpiritWhispers()
    {
        // Check if spirit whispers exist, generate if missing
        EditorApplication.delayCall += CheckAndGenerateWhispers;
    }

    static void CheckAndGenerateWhispers()
    {
        string whisperDir = "Assets/Audio/Whispers";
        string assetPath = "Assets/Audio/Whispers/SpiritWhisperAsset.asset";

        // Check if the spirit whisper asset exists
        if (!File.Exists(assetPath))
        {
            Debug.Log("Spirit whisper assets not found. Generating placeholder audio...");
            GenerateAllSpiritWhispers();
        }
        else
        {
            // Verify the asset has valid clips
            SpiritWhisperAsset asset = AssetDatabase.LoadAssetAtPath<SpiritWhisperAsset>(assetPath);
            if (asset == null || asset.spiritWhispers == null || asset.spiritWhispers.Length == 0)
            {
                Debug.Log("Spirit whisper asset is empty. Regenerating audio...");
                GenerateAllSpiritWhispers();
            }
        }
    }

    static void GenerateAllSpiritWhispers()
    {
        // Create directory if it doesn't exist
        string whisperDir = "Assets/Audio/Whispers";
        if (!Directory.Exists(whisperDir))
        {
            Directory.CreateDirectory(whisperDir);
        }

        // Generate 10 spirit whisper variations with different characteristics
        AudioClip[] whisperClips = new AudioClip[10];

        whisperClips[0] = GenerateSpiritWhisper("spirit_whisper_01", 3.2f, 0.8f, 180f, WhisperType.Sorrowful);
        whisperClips[1] = GenerateSpiritWhisper("spirit_whisper_02", 4.1f, 0.6f, 220f, WhisperType.Regretful);
        whisperClips[2] = GenerateSpiritWhisper("spirit_whisper_03", 5.3f, 0.7f, 160f, WhisperType.Haunting);
        whisperClips[3] = GenerateSpiritWhisper("spirit_whisper_04", 3.8f, 0.9f, 200f, WhisperType.Peaceful);
        whisperClips[4] = GenerateSpiritWhisper("spirit_whisper_05", 6.2f, 0.5f, 140f, WhisperType.Ominous);
        whisperClips[5] = GenerateSpiritWhisper("spirit_whisper_06", 4.7f, 0.8f, 190f, WhisperType.Melancholic);
        whisperClips[6] = GenerateSpiritWhisper("spirit_whisper_07", 3.5f, 0.6f, 170f, WhisperType.Ethereal);
        whisperClips[7] = GenerateSpiritWhisper("spirit_whisper_08", 5.8f, 0.7f, 210f, WhisperType.Distant);
        whisperClips[8] = GenerateSpiritWhisper("spirit_whisper_09", 4.4f, 0.9f, 150f, WhisperType.Comforting);
        whisperClips[9] = GenerateSpiritWhisper("spirit_whisper_10", 6.7f, 0.5f, 230f, WhisperType.Mysterious);

        // Create the spirit whisper asset
        CreateSpiritWhisperAsset(whisperClips);

        AssetDatabase.Refresh();
        Debug.Log("Spirit whisper audio assets generated successfully!");
    }

    enum WhisperType
    {
        Sorrowful,
        Regretful,
        Haunting,
        Peaceful,
        Ominous,
        Melancholic,
        Ethereal,
        Distant,
        Comforting,
        Mysterious
    }

    static AudioClip GenerateSpiritWhisper(string name, float duration, float intensity, float baseFreq, WhisperType type)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * duration);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);

        float[] data = new float[samples];

        // Adjust parameters based on whisper type
        float[] typeModifiers = GetTypeModifiers(type);
        float freqMod = typeModifiers[0];
        float harmMod = typeModifiers[1];
        float noiseMod = typeModifiers[2];
        float reverbMod = typeModifiers[3];

        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            float normalizedTime = time / duration;

            // Create base whisper tone
            float whisperBase = GenerateWhisperTone(time, baseFreq * freqMod);

            // Add type-specific harmonics
            float harmonics = GenerateSpectralHarmonics(time, baseFreq, harmMod);

            // Add breath modulation
            float breathMod = GenerateBreathModulation(time, duration, type);

            // Add reverb effect
            float reverb = GenerateEerieReverb(time, normalizedTime, reverbMod);

            // Add type-specific noise
            float noise = (Mathf.PerlinNoise(time * 40f, (float)type * 10f) - 0.5f) * noiseMod * 0.1f;

            // Combine all elements
            float sample = (whisperBase + harmonics * 0.3f + noise) * breathMod * reverb * intensity;

            // Apply envelope
            float envelope = CalculateEnvelope(normalizedTime, type);
            sample *= envelope;

            // Clamp to prevent clipping
            data[i] = Mathf.Clamp(sample, -1f, 1f);
        }

        clip.SetData(data, 0);

        // Save the clip
        string path = $"Assets/Audio/Whispers/{name}.asset";
        AssetDatabase.CreateAsset(clip, path);

        return clip;
    }

    static float[] GetTypeModifiers(WhisperType type)
    {
        // Returns [frequency modifier, harmonic modifier, noise modifier, reverb modifier]
        switch (type)
        {
            case WhisperType.Sorrowful:
                return new float[] { 0.8f, 1.2f, 0.3f, 1.5f };
            case WhisperType.Regretful:
                return new float[] { 0.9f, 1.0f, 0.5f, 1.3f };
            case WhisperType.Haunting:
                return new float[] { 1.2f, 1.5f, 0.7f, 2.0f };
            case WhisperType.Peaceful:
                return new float[] { 0.7f, 0.8f, 0.2f, 1.0f };
            case WhisperType.Ominous:
                return new float[] { 1.3f, 1.8f, 0.8f, 1.8f };
            case WhisperType.Melancholic:
                return new float[] { 0.85f, 1.1f, 0.4f, 1.4f };
            case WhisperType.Ethereal:
                return new float[] { 1.1f, 2.0f, 0.1f, 2.5f };
            case WhisperType.Distant:
                return new float[] { 0.6f, 0.5f, 0.6f, 3.0f };
            case WhisperType.Comforting:
                return new float[] { 0.75f, 0.9f, 0.2f, 1.1f };
            case WhisperType.Mysterious:
                return new float[] { 1.4f, 1.6f, 0.9f, 2.2f };
            default:
                return new float[] { 1.0f, 1.0f, 0.5f, 1.5f };
        }
    }

    static float GenerateWhisperTone(float time, float baseFreq)
    {
        // Create a low-frequency whisper-like tone
        float primary = Mathf.Sin(time * baseFreq * 2f * Mathf.PI) * 0.4f;

        // Add subtle frequency modulation
        float freqMod = 1f + 0.1f * Mathf.Sin(time * 3f);
        float modulated = Mathf.Sin(time * baseFreq * freqMod * 2f * Mathf.PI) * 0.3f;

        return primary + modulated;
    }

    static float GenerateSpectralHarmonics(float time, float baseFreq, float harmMod)
    {
        // Add ethereal harmonics
        float harmonic1 = Mathf.Sin(time * baseFreq * 1.5f * 2f * Mathf.PI) * 0.15f * harmMod;
        float harmonic2 = Mathf.Sin(time * baseFreq * 2.3f * 2f * Mathf.PI) * 0.1f * harmMod;
        float harmonic3 = Mathf.Sin(time * baseFreq * 3.7f * 2f * Mathf.PI) * 0.05f * harmMod;

        // Modulate harmonics
        float modulation = 1f + 0.3f * Mathf.Sin(time * 0.7f);

        return (harmonic1 + harmonic2 + harmonic3) * modulation;
    }

    static float GenerateBreathModulation(float time, float duration, WhisperType type)
    {
        // Create breath-like amplitude modulation
        float breathRate = 2f / duration;
        float breath = 0.7f + 0.3f * Mathf.Sin(time * breathRate * 2f * Mathf.PI);

        // Add type-specific tremolo
        float tremoloRate = type == WhisperType.Haunting ? 12f : 8f;
        float tremolo = 1f + 0.1f * Mathf.Sin(time * tremoloRate);

        return breath * tremolo;
    }

    static float GenerateEerieReverb(float time, float normalizedTime, float reverbMod)
    {
        // Simulate reverb tail
        float reverb = 1f;

        // Add delayed echoes
        for (int i = 1; i <= 3; i++)
        {
            float delay = 0.1f * i * reverbMod;
            float delayedTime = time - delay;

            if (delayedTime > 0)
            {
                float echo = Mathf.Exp(-delayedTime * 2f) * (0.3f / i) * reverbMod;
                reverb += echo * Mathf.Sin(delayedTime * 100f * 2f * Mathf.PI);
            }
        }

        return reverb * (1f - 0.2f * normalizedTime);
    }

    static float CalculateEnvelope(float normalizedTime, WhisperType type)
    {
        // Create smooth fade in/out envelope
        float fadeInTime = type == WhisperType.Distant ? 0.2f : 0.1f;
        float fadeOutTime = type == WhisperType.Ethereal ? 0.5f : 0.3f;

        float envelope = 1f;

        // Fade in
        if (normalizedTime < fadeInTime)
        {
            envelope = normalizedTime / fadeInTime;
        }
        // Fade out
        else if (normalizedTime > (1f - fadeOutTime))
        {
            float fadeOutProgress = (normalizedTime - (1f - fadeOutTime)) / fadeOutTime;
            envelope = 1f - fadeOutProgress;
        }

        // Apply smooth curve
        envelope = Mathf.SmoothStep(0f, 1f, envelope);

        return envelope;
    }

    static void CreateSpiritWhisperAsset(AudioClip[] clips)
    {
        // Create a ScriptableObject asset to hold the whisper clips
        SpiritWhisperAsset asset = ScriptableObject.CreateInstance<SpiritWhisperAsset>();
        asset.spiritWhispers = clips;
        asset.defaultVolume = 0.3f;
        asset.pitchVariation = 0.2f;
        asset.cooldownTime = 2f;

        // Create main asset
        string assetPath = "Assets/Audio/Whispers/SpiritWhisperAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);

        // Also create a copy in Resources folder for runtime loading
        string resourcesDir = "Assets/Resources";
        if (!Directory.Exists(resourcesDir))
        {
            Directory.CreateDirectory(resourcesDir);
        }

        string resourcesPath = "Assets/Resources/SpiritWhisperAsset.asset";
        AssetDatabase.CopyAsset(assetPath, resourcesPath);

        AssetDatabase.SaveAssets();

        Debug.Log($"Created SpiritWhisperAsset with {clips.Length} clips at {assetPath}");
        Debug.Log($"Also created Resources copy at {resourcesPath} for runtime loading");
    }
}
#endif
