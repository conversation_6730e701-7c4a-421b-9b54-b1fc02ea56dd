using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// ScriptableObject definition for Dynamic World Events in Cinder of Darkness
/// Defines event metadata, conditions, and consequences
/// </summary>
[CreateAssetMenu(fileName = "NewWorldEvent", menuName = "Cinder of Darkness/World Event")]
public class WorldEvent : ScriptableObject
{
    [Header("Basic Event Information")]
    public string eventId;
    public string eventName;
    [TextArea(3, 5)]
    public string eventDescription;
    public EventType eventType;
    public EventSeverity severity = EventSeverity.Minor;
    public Sprite eventIcon;
    
    [Header("Trigger Conditions")]
    public EventTriggerType triggerType;
    public EventTriggerConditions triggerConditions;
    
    [Header("Duration and Timing")]
    public float baseDurationDays = 3f;
    public float durationVariance = 1f;
    public bool canRecur = true;
    public float cooldownDays = 30f;
    
    [Header("Affected Areas")]
    public string[] affectedRealms;
    public string[] affectedTowns;
    public string[] affectedZones;
    public bool affectsGlobalWorld = false;
    
    [Header("Event Effects")]
    public NPCBehaviorOverride[] npcOverrides;
    public EnvironmentalEffect[] environmentalEffects;
    public EconomicImpact economicImpact;
    public VisualChanges visualChanges;
    
    [Header("Player Interaction")]
    public bool allowsPlayerIntervention = true;
    public PlayerInteractionOption[] interventionOptions;
    public MoralityShift[] moralityConsequences;
    public ReputationChange[] reputationChanges;
    
    [Header("Escalation")]
    public bool canEscalate = false;
    public WorldEvent escalatedEvent;
    public float escalationThreshold = 0.5f;
    public string[] escalationConditions;
    
    [Header("Rewards and Consequences")]
    public EventReward[] completionRewards;
    public EventConsequence[] failureConsequences;
    public string[] unlockedQuests;
    public string[] unlockedItems;
    
    /// <summary>
    /// Event types for categorization and behavior
    /// </summary>
    public enum EventType
    {
        Invasion,
        NaturalDisaster,
        Disease,
        Religious,
        Political,
        Economic,
        Supernatural,
        Cultural
    }
    
    /// <summary>
    /// Event severity levels affecting impact and player attention
    /// </summary>
    public enum EventSeverity
    {
        Minor,      // Local impact, subtle changes
        Moderate,   // Regional impact, noticeable changes
        Major,      // Realm-wide impact, significant changes
        Critical    // World-changing impact, dramatic changes
    }
    
    /// <summary>
    /// How events are triggered
    /// </summary>
    public enum EventTriggerType
    {
        TimeBased,
        MoralityBased,
        QuestMilestone,
        PlayerChoice,
        RandomChance,
        ChainedEvent,
        PlayerLevel,
        WorldState
    }
    
    /// <summary>
    /// Specific conditions for event triggering
    /// </summary>
    [System.Serializable]
    public class EventTriggerConditions
    {
        [Header("Time-Based Triggers")]
        public int minDaysPassed = 0;
        public int maxDaysPassed = 999;
        public bool requiresSpecificSeason = false;
        public Season requiredSeason;
        
        [Header("Morality-Based Triggers")]
        public bool requiresMoralityAlignment = false;
        public MoralityAlignment requiredAlignment;
        public float minMoralityValue = -1f;
        public float maxMoralityValue = 1f;
        
        [Header("Quest/Progress Triggers")]
        public string[] requiredCompletedQuests;
        public string[] requiredActiveQuests;
        public string[] requiredDefeatedBosses;
        public int minPlayerLevel = 1;
        public int maxPlayerLevel = 100;
        
        [Header("World State Triggers")]
        public string[] requiredWorldFlags;
        public string[] forbiddenWorldFlags;
        public float randomChance = 0.1f;
        public string[] requiredPreviousEvents;
        
        [Header("Player Choice Triggers")]
        public string[] requiredDialogueChoices;
        public string[] requiredFactionStandings;
    }
    
    /// <summary>
    /// How NPCs behave during events
    /// </summary>
    [System.Serializable]
    public class NPCBehaviorOverride
    {
        public string npcId;
        public string npcTag; // For affecting groups of NPCs
        public NPCBehaviorType behaviorType;
        public string[] newDialogueLines;
        public bool changeSchedule = false;
        public NPCScheduleOverride scheduleOverride;
        public bool changeTradeInventory = false;
        public TradeInventoryChange tradeChanges;
        public float fearLevel = 0f;
        public float aggressionLevel = 0f;
    }
    
    public enum NPCBehaviorType
    {
        Normal,
        Fearful,
        Aggressive,
        Hiding,
        Evacuating,
        Celebrating,
        Mourning,
        Praying,
        Trading,
        Closed
    }
    
    [System.Serializable]
    public class NPCScheduleOverride
    {
        public bool stayIndoors = false;
        public bool closeShop = false;
        public bool gatherInGroups = false;
        public string newLocation;
        public float activityStartHour = 6f;
        public float activityEndHour = 22f;
    }
    
    [System.Serializable]
    public class TradeInventoryChange
    {
        public string[] addedItems;
        public string[] removedItems;
        public float priceMultiplier = 1f;
        public bool limitedStock = false;
        public int stockReduction = 0;
    }
    
    /// <summary>
    /// Environmental effects during events
    /// </summary>
    [System.Serializable]
    public class EnvironmentalEffect
    {
        public EnvironmentEffectType effectType;
        public float intensity = 1f;
        public Color effectColor = Color.white;
        public string particleEffectPrefab;
        public string audioClip;
        public bool affectsVisibility = false;
        public float visibilityReduction = 0f;
        public bool affectsMovement = false;
        public float movementSpeedMultiplier = 1f;
    }
    
    public enum EnvironmentEffectType
    {
        Fog,
        Rain,
        Snow,
        AshFall,
        Sandstorm,
        Fire,
        Darkness,
        BloodMoon,
        Aurora,
        Earthquake
    }
    
    /// <summary>
    /// Economic impact of events
    /// </summary>
    [System.Serializable]
    public class EconomicImpact
    {
        public float globalPriceMultiplier = 1f;
        public ItemPriceChange[] specificItemChanges;
        public bool affectsTrade = false;
        public float tradeVolumeMultiplier = 1f;
        public string[] unavailableItems;
        public string[] newAvailableItems;
    }
    
    [System.Serializable]
    public class ItemPriceChange
    {
        public string itemId;
        public float priceMultiplier = 1f;
        public bool makeUnavailable = false;
        public bool increaseRarity = false;
    }
    
    /// <summary>
    /// Visual changes to the world during events
    /// </summary>
    [System.Serializable]
    public class VisualChanges
    {
        public bool changeSkybox = false;
        public Material eventSkybox;
        public bool changeLighting = false;
        public Color ambientLightColor = Color.white;
        public float lightIntensityMultiplier = 1f;
        public bool addParticleEffects = false;
        public string[] particleEffectPrefabs;
        public bool changePostProcessing = false;
        public string postProcessingProfile;
        public bool addScreenEffects = false;
        public ScreenEffect[] screenEffects;
    }
    
    [System.Serializable]
    public class ScreenEffect
    {
        public ScreenEffectType effectType;
        public float intensity = 0.5f;
        public Color effectColor = Color.red;
        public float duration = 1f;
        public bool isPersistent = false;
    }
    
    public enum ScreenEffectType
    {
        Vignette,
        ColorGrading,
        Shake,
        Flash,
        Fade,
        Distortion
    }
    
    /// <summary>
    /// Player interaction options during events
    /// </summary>
    [System.Serializable]
    public class PlayerInteractionOption
    {
        public string optionId;
        public string optionText;
        public string optionDescription;
        public InteractionType interactionType;
        public string[] requirements;
        public EventOutcome outcome;
        public float moralityShift = 0f;
        public ReputationChange[] reputationChanges;
        public string[] unlockedContent;
    }
    
    public enum InteractionType
    {
        Ignore,
        Intervene,
        Manipulate,
        Assist,
        Oppose,
        Investigate,
        Negotiate,
        Fight
    }
    
    [System.Serializable]
    public class EventOutcome
    {
        public bool resolveEvent = false;
        public bool escalateEvent = false;
        public bool triggerNewEvent = false;
        public string newEventId;
        public float eventDurationModifier = 1f;
        public string[] worldFlagsToSet;
        public string[] worldFlagsToRemove;
    }
    
    /// <summary>
    /// Morality shifts from event participation
    /// </summary>
    [System.Serializable]
    public class MoralityShift
    {
        public MoralityAlignment alignment;
        public float shiftAmount = 0f;
        public string reason;
    }
    
    /// <summary>
    /// Reputation changes with factions
    /// </summary>
    [System.Serializable]
    public class ReputationChange
    {
        public string factionId;
        public float reputationDelta = 0f;
        public string reason;
    }
    
    /// <summary>
    /// Rewards for event completion
    /// </summary>
    [System.Serializable]
    public class EventReward
    {
        public RewardType rewardType;
        public string rewardId;
        public int quantity = 1;
        public string rewardDescription;
    }
    
    public enum RewardType
    {
        Experience,
        Item,
        Currency,
        Skill,
        Reputation,
        Unlock,
        Cosmetic
    }
    
    /// <summary>
    /// Consequences for event failure or neglect
    /// </summary>
    [System.Serializable]
    public class EventConsequence
    {
        public ConsequenceType consequenceType;
        public string consequenceId;
        public float severity = 1f;
        public string consequenceDescription;
        public bool isPermanent = false;
    }
    
    public enum ConsequenceType
    {
        NPCDeath,
        LocationDestroyed,
        QuestUnavailable,
        ItemLost,
        ReputationLoss,
        AccessBlocked,
        EnemyIncrease
    }
    
    /// <summary>
    /// Validate event data for consistency
    /// </summary>
    public bool ValidateEvent()
    {
        bool isValid = true;
        
        if (string.IsNullOrEmpty(eventId))
        {
            Debug.LogError($"Event {name} missing eventId");
            isValid = false;
        }
        
        if (string.IsNullOrEmpty(eventName))
        {
            Debug.LogError($"Event {eventId} missing eventName");
            isValid = false;
        }
        
        if (baseDurationDays <= 0)
        {
            Debug.LogError($"Event {eventId} has invalid duration");
            isValid = false;
        }
        
        if (affectedRealms.Length == 0 && affectedTowns.Length == 0 && affectedZones.Length == 0 && !affectsGlobalWorld)
        {
            Debug.LogWarning($"Event {eventId} affects no areas");
        }
        
        return isValid;
    }
    
    /// <summary>
    /// Get event duration with variance
    /// </summary>
    public float GetRandomDuration()
    {
        return baseDurationDays + Random.Range(-durationVariance, durationVariance);
    }
    
    /// <summary>
    /// Check if event can trigger based on current conditions
    /// </summary>
    public bool CanTrigger(EventTriggerContext context)
    {
        if (triggerConditions == null) return true;
        
        // Check time-based conditions
        if (triggerType == EventTriggerType.TimeBased)
        {
            if (context.daysPassed < triggerConditions.minDaysPassed || 
                context.daysPassed > triggerConditions.maxDaysPassed)
                return false;
        }
        
        // Check morality conditions
        if (triggerConditions.requiresMoralityAlignment)
        {
            if (context.playerMorality < triggerConditions.minMoralityValue || 
                context.playerMorality > triggerConditions.maxMoralityValue)
                return false;
        }
        
        // Check quest conditions
        foreach (string requiredQuest in triggerConditions.requiredCompletedQuests)
        {
            if (!context.completedQuests.Contains(requiredQuest))
                return false;
        }
        
        // Check world flags
        foreach (string requiredFlag in triggerConditions.requiredWorldFlags)
        {
            if (!context.worldFlags.Contains(requiredFlag))
                return false;
        }
        
        foreach (string forbiddenFlag in triggerConditions.forbiddenWorldFlags)
        {
            if (context.worldFlags.Contains(forbiddenFlag))
                return false;
        }
        
        // Check random chance
        if (triggerType == EventTriggerType.RandomChance)
        {
            return Random.value <= triggerConditions.randomChance;
        }
        
        return true;
    }
}

/// <summary>
/// Context information for event trigger evaluation
/// </summary>
[System.Serializable]
public class EventTriggerContext
{
    public int daysPassed;
    public Season currentSeason;
    public float playerMorality;
    public MoralityAlignment playerAlignment;
    public int playerLevel;
    public List<string> completedQuests;
    public List<string> activeQuests;
    public List<string> defeatedBosses;
    public List<string> worldFlags;
    public List<string> dialogueChoices;
    public Dictionary<string, float> factionStandings;
    public List<string> activeEvents;
}

/// <summary>
/// Enums for morality and seasons
/// </summary>
public enum MoralityAlignment
{
    Sun,
    Moon,
    Eclipse
}

public enum Season
{
    Spring,
    Summer,
    Autumn,
    Winter
}
