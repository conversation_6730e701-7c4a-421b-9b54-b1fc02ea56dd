using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace CinderOfDarkness.World
{
    /// <summary>
    /// Comprehensive World Expansion for Cinder of Darkness.
    /// Adds missing villages, cultural settlements, and trading posts without duplicating existing content.
    /// </summary>
    public class ComprehensiveWorldExpansion : MonoBehaviour
    {
        #region Serialized Fields
        [Header("World Expansion Settings")]
        [SerializeField] private bool enableExpansion = true;
        [SerializeField] private CulturalSettlement[] culturalSettlements;
        [SerializeField] private TradingPost[] tradingPosts;
        [SerializeField] private NomadCamp[] nomadCamps;

        [Header("Missing Villages")]
        [SerializeField] private ExpandedVillage[] missingVillages;

        [Header("Architectural Systems")]
        [SerializeField] private ArchitecturalTheme[] architecturalThemes;
        [SerializeField] private BuildingPrefab[] culturalBuildings;

        [Header("NPC Routine Systems")]
        [SerializeField] private NPCRoutineTemplate[] routineTemplates;
        [SerializeField] private CulturalBehavior[] culturalBehaviors;
        #endregion

        #region Private Fields
        private Dictionary<string, CulturalSettlement> settlementLookup = new Dictionary<string, CulturalSettlement>();
        private Dictionary<string, ExpandedVillage> villageLookup = new Dictionary<string, ExpandedVillage>();
        private ExpandedKingdomSystem kingdomSystem;
        private WorldExplorationSystem explorationSystem;
        #endregion

        #region Public Properties
        public static ComprehensiveWorldExpansion Instance { get; private set; }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeWorldExpansion();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (enableExpansion)
            {
                SetupMissingContent();
            }
        }
        #endregion

        #region Initialization
        private void InitializeWorldExpansion()
        {
            kingdomSystem = ExpandedKingdomSystem.Instance;
            explorationSystem = FindObjectOfType<WorldExplorationSystem>();

            Debug.Log("Comprehensive World Expansion initialized");
        }

        private void SetupMissingContent()
        {
            SetupMissingVillages();
            SetupCulturalSettlements();
            SetupTradingPosts();
            SetupNomadCamps();
            SetupArchitecturalThemes();

            Debug.Log("World expansion content setup complete");
        }

        private void SetupMissingVillages()
        {
            missingVillages = new ExpandedVillage[]
            {
                CreateVillageOfBrokenMirrors(),
                CreateVillageOfTimekeepers(),
                CreateVillageOfDreamweavers(),
                CreateVillageOfEndlessHarvest(),
                CreateVillageOfSilentBells(),
                CreateVillageOfWanderingLights()
            };

            foreach (var village in missingVillages)
            {
                villageLookup[village.villageName] = village;
            }

            Debug.Log($"Setup {missingVillages.Length} missing villages");
        }

        private ExpandedVillage CreateVillageOfBrokenMirrors()
        {
            return new ExpandedVillage
            {
                villageName = "Village of Broken Mirrors",
                culturalName = "قرية المرايا المكسورة", // Arabic: Village of Broken Mirrors
                villageType = ExpandedVillage.VillageType.ReflectionBreakers,

                uniqueBelief = "Truth is revealed only when illusions are shattered",
                corePhilosophy = "Every broken mirror shows a different aspect of reality",

                cinderbornPerception = ExpandedVillage.CinderbornPerception.TruthRevealer,
                strangerPolicy = ExpandedVillage.StrangerPolicy.MirrorTrial,
                suspicionLevel = 0.6f,

                culturalTaboos = new string[]
                {
                    "Looking into unbroken mirrors brings false visions",
                    "Repairing mirrors destroys their truth-telling power",
                    "Avoiding your reflection shows cowardice"
                },

                culturalVirtues = new string[]
                {
                    "Facing uncomfortable truths about yourself",
                    "Helping others see past their illusions",
                    "Creating art from broken reflections"
                },

                architecturalStyle = ExpandedVillage.ArchitecturalStyle.ShatteredGlass,
                primaryMaterial = "Broken mirrors and reflective shards embedded in walls",
                specialBuildings = new string[]
                {
                    "The Hall of Shattered Truths",
                    "Mirror Maze of Self-Discovery",
                    "The Reflection Workshop",
                    "Temple of Broken Illusions"
                },

                npcTypes = new ExpandedVillage.NPCType[]
                {
                    ExpandedVillage.NPCType.ReflectionBreaker,
                    ExpandedVillage.NPCType.TruthSeeker,
                    ExpandedVillage.NPCType.MirrorCrafter,
                    ExpandedVillage.NPCType.IllusionDestroyer,
                    ExpandedVillage.NPCType.RealityGuide
                },

                dailyRoutines = new ExpandedVillage.DailyRoutine[]
                {
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Morning Truth Ritual",
                        startTime = 7f,
                        duration = 1f,
                        participants = new string[] { "ReflectionBreaker", "All villagers" },
                        description = "Daily mirror-breaking ceremony to reveal new truths"
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Reflection Crafting",
                        startTime = 10f,
                        duration = 4f,
                        participants = new string[] { "MirrorCrafter", "Apprentices" },
                        description = "Creating art and tools from broken mirror pieces"
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Truth Circle",
                        startTime = 19f,
                        duration = 2f,
                        participants = new string[] { "TruthSeeker", "Villagers" },
                        description = "Evening sharing of truths discovered in broken reflections"
                    }
                },

                uniqueTraditions = new string[]
                {
                    "Coming-of-age mirror breaking ceremonies",
                    "Annual Festival of Shattered Illusions",
                    "Truth-telling competitions using mirror shards",
                    "Meditation retreats in the mirror maze"
                },

                primaryTrade = "Truth-revealing mirrors and reflection-based divination",
                socialStructure = ExpandedVillage.SocialStructure.ShardDemocracy,
                populationSize = 90,

                biome = BiomeData.BiomeType.BrokenLands,
                weatherPattern = WeatherSystem.WeatherType.ShiftingLight,
                ambientTemperature = 19f,

                // Cultural Behaviors
                culturalBehaviors = new CulturalBehavior[]
                {
                    new CulturalBehavior
                    {
                        behaviorName = "Mirror Avoidance",
                        description = "Villagers avoid looking into unbroken mirrors",
                        triggerCondition = "Near intact mirrors",
                        behaviorType = CulturalBehavior.BehaviorType.Avoidance
                    },
                    new CulturalBehavior
                    {
                        behaviorName = "Truth Compulsion",
                        description = "Compelled to speak honestly when holding mirror shards",
                        triggerCondition = "Holding broken mirror pieces",
                        behaviorType = CulturalBehavior.BehaviorType.Compulsion
                    }
                }
            };
        }

        private ExpandedVillage CreateVillageOfTimekeepers()
        {
            return new ExpandedVillage
            {
                villageName = "Village of Timekeepers",
                culturalName = "قرية حراس الزمن", // Arabic: Village of Time Guardians
                villageType = ExpandedVillage.VillageType.TimeKeepers,

                uniqueBelief = "Time is a sacred river that must be carefully tended and protected",
                corePhilosophy = "Every moment has its purpose, every hour its guardian",

                cinderbornPerception = ExpandedVillage.CinderbornPerception.TimeDisruptor,
                strangerPolicy = ExpandedVillage.StrangerPolicy.TimeQuestion,
                suspicionLevel = 0.7f,

                culturalTaboos = new string[]
                {
                    "Wasting time is the greatest sin",
                    "Disrupting the village schedule brings chaos",
                    "Ignoring the call of the time bells"
                },

                culturalVirtues = new string[]
                {
                    "Punctuality in all endeavors",
                    "Preserving historical moments",
                    "Teaching the value of time to children"
                },

                architecturalStyle = ExpandedVillage.ArchitecturalStyle.TimeSpirals,
                primaryMaterial = "Clockwork mechanisms and time-worn stone",
                specialBuildings = new string[]
                {
                    "The Great Chronometer Tower",
                    "Hall of Preserved Moments",
                    "The Temporal Archive",
                    "Workshop of Time Devices"
                },

                npcTypes = new ExpandedVillage.NPCType[]
                {
                    ExpandedVillage.NPCType.TimeKeeper,
                    ExpandedVillage.NPCType.ClockMaster,
                    ExpandedVillage.NPCType.MomentPreserver,
                    ExpandedVillage.NPCType.ScheduleGuardian,
                    ExpandedVillage.NPCType.TemporalScribe
                },

                dailyRoutines = new ExpandedVillage.DailyRoutine[]
                {
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Dawn Time Synchronization",
                        startTime = 6f,
                        duration = 0.5f,
                        participants = new string[] { "TimeKeeper", "ClockMaster" },
                        description = "Precise synchronization of all village timepieces"
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Hourly Bell Ceremony",
                        startTime = 0f, // Repeats every hour
                        duration = 0.1f,
                        participants = new string[] { "TimeKeeper" },
                        description = "Ceremonial ringing of time bells every hour",
                        isRepeating = true,
                        repeatInterval = 1f
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Evening Time Archive",
                        startTime = 20f,
                        duration = 1f,
                        participants = new string[] { "TemporalScribe", "MomentPreserver" },
                        description = "Recording and preserving the day's significant moments"
                    }
                },

                uniqueTraditions = new string[]
                {
                    "New Year's Great Time Reset ceremony",
                    "Moment preservation rituals for special events",
                    "Clockwork crafting competitions",
                    "Time meditation practices"
                },

                primaryTrade = "Precision timepieces and temporal consulting",
                socialStructure = ExpandedVillage.SocialStructure.TimeKeepers,
                populationSize = 110,

                biome = BiomeData.BiomeType.TimelessDeserts,
                weatherPattern = WeatherSystem.WeatherType.PrecisePatterns,
                ambientTemperature = 23f,

                culturalBehaviors = new CulturalBehavior[]
                {
                    new CulturalBehavior
                    {
                        behaviorName = "Punctuality Obsession",
                        description = "Extreme attention to precise timing in all activities",
                        triggerCondition = "Any scheduled activity",
                        behaviorType = CulturalBehavior.BehaviorType.Compulsion
                    },
                    new CulturalBehavior
                    {
                        behaviorName = "Time Anxiety",
                        description = "Visible distress when schedules are disrupted",
                        triggerCondition = "Schedule disruption",
                        behaviorType = CulturalBehavior.BehaviorType.Stress
                    }
                }
            };
        }

        private ExpandedVillage CreateVillageOfDreamweavers()
        {
            return new ExpandedVillage
            {
                villageName = "Village of Dreamweavers",
                culturalName = "قرية نساجي الأحلام", // Arabic: Village of Dream Weavers
                villageType = ExpandedVillage.VillageType.DreamWeavers,

                uniqueBelief = "Dreams are threads that weave the fabric of reality",
                corePhilosophy = "In sleep, we touch the realm of infinite possibilities",

                cinderbornPerception = ExpandedVillage.CinderbornPerception.DreamWalker,
                strangerPolicy = ExpandedVillage.StrangerPolicy.DreamShare,
                suspicionLevel = 0.2f,

                culturalTaboos = new string[]
                {
                    "Waking someone during deep dream states",
                    "Refusing to share significant dreams",
                    "Using dreams for harmful purposes"
                },

                culturalVirtues = new string[]
                {
                    "Remembering and interpreting dreams",
                    "Helping others understand their visions",
                    "Weaving dream-inspired art and stories"
                },

                architecturalStyle = ExpandedVillage.ArchitecturalStyle.DreamCatchers,
                primaryMaterial = "Soft fabrics and dream-catching crystals",
                specialBuildings = new string[]
                {
                    "The Great Dream Loom",
                    "Temple of Sleeping Visions",
                    "The Nightmare Sanctuary",
                    "Hall of Shared Dreams"
                },

                npcTypes = new ExpandedVillage.NPCType[]
                {
                    ExpandedVillage.NPCType.DreamWeaver,
                    ExpandedVillage.NPCType.VisionInterpreter,
                    ExpandedVillage.NPCType.NightmareHealer,
                    ExpandedVillage.NPCType.SleepGuardian,
                    ExpandedVillage.NPCType.DreamCatcher
                },

                dailyRoutines = new ExpandedVillage.DailyRoutine[]
                {
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Dawn Dream Sharing",
                        startTime = 7f,
                        duration = 1f,
                        participants = new string[] { "DreamWeaver", "All villagers" },
                        description = "Morning circle for sharing and interpreting night dreams"
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Dream Weaving Work",
                        startTime = 9f,
                        duration = 6f,
                        participants = new string[] { "DreamWeaver", "Apprentices" },
                        description = "Creating dream-inspired textiles and art"
                    },
                    new ExpandedVillage.DailyRoutine
                    {
                        routineName = "Evening Sleep Preparation",
                        startTime = 21f,
                        duration = 1f,
                        participants = new string[] { "SleepGuardian", "All villagers" },
                        description = "Preparing the community for restful, vision-rich sleep"
                    }
                },

                uniqueTraditions = new string[]
                {
                    "Annual Festival of Shared Dreams",
                    "Dream-catching ceremonies for children",
                    "Nightmare healing rituals",
                    "Vision quests in the dream temple"
                },

                primaryTrade = "Dream-woven textiles and sleep aids",
                socialStructure = ExpandedVillage.SocialStructure.DreamCouncil,
                populationSize = 130,

                biome = BiomeData.BiomeType.DreamScapes,
                weatherPattern = WeatherSystem.WeatherType.SoftMists,
                ambientTemperature = 21f,

                culturalBehaviors = new CulturalBehavior[]
                {
                    new CulturalBehavior
                    {
                        behaviorName = "Dream Reverence",
                        description = "Deep respect for dream experiences and visions",
                        triggerCondition = "Dream discussion",
                        behaviorType = CulturalBehavior.BehaviorType.Reverence
                    },
                    new CulturalBehavior
                    {
                        behaviorName = "Sleep Prioritization",
                        description = "Prioritizing sleep and dream time over other activities",
                        triggerCondition = "Evening hours",
                        behaviorType = CulturalBehavior.BehaviorType.Prioritization
                    }
                }
            };
        }

        private void SetupCulturalSettlements()
        {
            culturalSettlements = new CulturalSettlement[]
            {
                CreateArabicOasisSettlement(),
                CreateNorseSkaldCamp(),
                CreateCelticDruidGrove(),
                CreatePersianScholarOutpost()
            };

            foreach (var settlement in culturalSettlements)
            {
                settlementLookup[settlement.settlementName] = settlement;
            }

            Debug.Log($"Setup {culturalSettlements.Length} cultural settlements");
        }
        #endregion

        #region Cultural Settlements
        private CulturalSettlement CreateArabicOasisSettlement()
        {
            return new CulturalSettlement
            {
                settlementName = "Oasis of Whispering Palms",
                culturalName = "واحة النخيل الهامسة",
                culturalGroup = CulturalSettlement.CulturalGroup.Arabic,
                settlementType = CulturalSettlement.SettlementType.Oasis,

                coreValues = new string[]
                {
                    "Hospitality to all travelers",
                    "Preservation of ancient wisdom",
                    "Harmony with the desert"
                },

                architecturalFeatures = new string[]
                {
                    "Domed buildings with intricate geometric patterns",
                    "Central courtyard with fountain",
                    "Covered walkways for shade",
                    "Minaret for calling prayers"
                },

                culturalPractices = new string[]
                {
                    "Five daily prayer times",
                    "Storytelling under the stars",
                    "Camel racing competitions",
                    "Poetry recitation contests"
                },

                specialResources = new string[]
                {
                    "Date palms and desert fruits",
                    "Precious water from deep wells",
                    "Rare desert herbs and spices",
                    "Handwoven carpets and textiles"
                },

                populationSize = 75,
                biome = BiomeData.BiomeType.DesertOasis,
                weatherPattern = WeatherSystem.WeatherType.HotDays_CoolNights
            };
        }
        #endregion

        #region Public API
        public ExpandedVillage GetVillageByName(string name)
        {
            return villageLookup.ContainsKey(name) ? villageLookup[name] : null;
        }

        public CulturalSettlement GetSettlementByName(string name)
        {
            return settlementLookup.ContainsKey(name) ? settlementLookup[name] : null;
        }

        public List<ExpandedVillage> GetAllVillages()
        {
            return new List<ExpandedVillage>(missingVillages);
        }

        public List<CulturalSettlement> GetAllSettlements()
        {
            return new List<CulturalSettlement>(culturalSettlements);
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class ExpandedVillage
    {
        [Header("Basic Information")]
        public string villageName;
        public string culturalName;
        public VillageType villageType;

        [Header("Beliefs and Philosophy")]
        public string uniqueBelief;
        public string corePhilosophy;

        [Header("Cinderborn Relations")]
        public CinderbornPerception cinderbornPerception;
        public StrangerPolicy strangerPolicy;
        public float suspicionLevel;

        [Header("Cultural Practices")]
        public string[] culturalTaboos;
        public string[] culturalVirtues;

        [Header("Architecture")]
        public ArchitecturalStyle architecturalStyle;
        public string primaryMaterial;
        public string[] specialBuildings;

        [Header("Population")]
        public NPCType[] npcTypes;
        public int populationSize;

        [Header("Daily Life")]
        public DailyRoutine[] dailyRoutines;
        public string[] uniqueTraditions;

        [Header("Economy and Society")]
        public string primaryTrade;
        public SocialStructure socialStructure;

        [Header("Environment")]
        public BiomeData.BiomeType biome;
        public WeatherSystem.WeatherType weatherPattern;
        public float ambientTemperature;

        [Header("Cultural Behaviors")]
        public CulturalBehavior[] culturalBehaviors;

        public enum VillageType
        {
            MemoryKeepers,
            HarvestBound,
            SilentWatchers,
            LightChasers,
            ReflectionBreakers,
            TimeKeepers,
            DreamWeavers,
            SpiritCommuners,
            ElementalBound,
            AncestorWorshippers
        }

        public enum ArchitecturalStyle
        {
            NameStones,
            GrowingWood,
            SilentBells,
            LightCatchers,
            ShatteredGlass,
            TimeSpirals,
            DreamCatchers,
            SpiritTotems,
            ElementalShapes,
            AncestralShrines
        }

        public enum CinderbornPerception
        {
            NamelessWanderer,
            SeasonBringer,
            SilentThreat,
            LightBearer,
            TruthRevealer,
            TimeDisruptor,
            DreamWalker,
            SpiritGuide,
            ElementalForce,
            AncestralEcho
        }

        public enum StrangerPolicy
        {
            NameFirst,
            SeasonalWelcome,
            SilentObservation,
            LightTest,
            MirrorTrial,
            TimeQuestion,
            DreamShare,
            SpiritReading,
            ElementalProof,
            AncestralApproval
        }

        public enum SocialStructure
        {
            ElderCouncil,
            SeasonalRotation,
            SilentHierarchy,
            LightCircle,
            ShardDemocracy,
            TimeKeepers,
            DreamCouncil,
            SpiritGuided,
            ElementalBalance,
            AncestralLineage
        }

        public enum NPCType
        {
            NameKeeper,
            MemoryScribe,
            GenealogyExpert,
            StoryTeller,
            IdentityGuardian,
            HarvestMaster,
            SeasonKeeper,
            GrowthTender,
            SilentWatcher,
            BellKeeper,
            LightChaser,
            ReflectionBreaker,
            TruthSeeker,
            MirrorCrafter,
            IllusionDestroyer,
            RealityGuide,
            TimeKeeper,
            ClockMaster,
            MomentPreserver,
            ScheduleGuardian,
            TemporalScribe,
            DreamWeaver,
            VisionInterpreter,
            NightmareHealer,
            SleepGuardian,
            DreamCatcher,
            SeedSaver,
            FeastOrganizer,
            GestureTeacher,
            QuietGuardian,
            ObservationMaster,
            CrystalTender,
            LightInterpreter,
            BeaconKeeper,
            ShadowReader
        }

        [System.Serializable]
        public class DailyRoutine
        {
            public string routineName;
            public float startTime; // Hour of day (0-24)
            public float duration; // Duration in hours
            public string[] participants;
            public string description;
            public bool isWeatherDependent;
            public bool isSeasonDependent;
            public bool isRepeating;
            public float repeatInterval; // Hours between repetitions
        }
    }

    [System.Serializable]
    public class CulturalSettlement
    {
        [Header("Basic Information")]
        public string settlementName;
        public string culturalName;
        public CulturalGroup culturalGroup;
        public SettlementType settlementType;

        [Header("Cultural Identity")]
        public string[] coreValues;
        public string[] culturalPractices;
        public string[] architecturalFeatures;

        [Header("Resources and Trade")]
        public string[] specialResources;
        public string primaryTrade;

        [Header("Population and Environment")]
        public int populationSize;
        public BiomeData.BiomeType biome;
        public WeatherSystem.WeatherType weatherPattern;

        public enum CulturalGroup
        {
            Arabic,
            Norse,
            Celtic,
            Persian,
            Chinese,
            Japanese,
            Egyptian,
            Greek,
            Slavic,
            Indigenous,
            Mixed
        }

        public enum SettlementType
        {
            Oasis,
            MountainHold,
            ForestGrove,
            RiverDelta,
            DesertCaravan,
            CoastalPort,
            HighlandFortress,
            ValleyShrine,
            IslandRefuge,
            UndergroundCity
        }
    }

    [System.Serializable]
    public class TradingPost
    {
        public string postName;
        public string[] tradedGoods;
        public CulturalSettlement.CulturalGroup[] frequentTraders;
        public Vector3 location;
        public bool isNeutralGround;
    }

    [System.Serializable]
    public class NomadCamp
    {
        public string campName;
        public CulturalSettlement.CulturalGroup nomadCulture;
        public Vector3[] migrationRoute;
        public string[] seasonalActivities;
        public bool isHostileToStrangers;
    }

    [System.Serializable]
    public class ArchitecturalTheme
    {
        public string themeName;
        public CulturalSettlement.CulturalGroup associatedCulture;
        public Material[] themeMaterials;
        public GameObject[] themeBuildings;
        public Color[] themeColors;
    }

    [System.Serializable]
    public class BuildingPrefab
    {
        public string buildingName;
        public GameObject prefab;
        public CulturalSettlement.CulturalGroup culture;
        public BuildingType buildingType;
        public Vector3 defaultScale = Vector3.one;

        public enum BuildingType
        {
            Residential,
            Religious,
            Commercial,
            Administrative,
            Defensive,
            Cultural,
            Industrial,
            Agricultural
        }
    }

    [System.Serializable]
    public class NPCRoutineTemplate
    {
        public string templateName;
        public CulturalSettlement.CulturalGroup culture;
        public NPCAction[] actions;
        public float[] timings;
        public bool isWeatherDependent;

        [System.Serializable]
        public class NPCAction
        {
            public string actionName;
            public string actionDescription;
            public Vector3 actionLocation;
            public float actionDuration;
        }
    }

    [System.Serializable]
    public class CulturalBehavior
    {
        public string behaviorName;
        public string description;
        public string triggerCondition;
        public BehaviorType behaviorType;
        public float intensity = 1f;

        public enum BehaviorType
        {
            Avoidance,
            Compulsion,
            Stress,
            Reverence,
            Prioritization,
            Celebration,
            Mourning,
            Protection,
            Curiosity,
            Fear
        }
    }

    [System.Serializable]
    public class BiomeData
    {
        public BiomeType biomeType;
        public string biomeName;
        public Color ambientColor;
        public float fogDensity;
        public Material terrainMaterial;
        public GameObject[] environmentPrefabs;

        public enum BiomeType
        {
            VolcanicPlains,
            CrystalCaverns,
            MournfulMeadows,
            IronMountains,
            FlowingRivers,
            FloatingIslands,
            ShadowValleys,
            QuietHills,
            EndlessFields,
            SilentForests,
            LightMeadows,
            BrokenLands,
            TimelessDeserts,
            DreamScapes,
            DesertOasis,
            MysticGroves,
            AncientRuins,
            FrozenWastes,
            TropicalJungles,
            CoralReefs
        }
    }

    [System.Serializable]
    public class WeatherSystem
    {
        public enum WeatherType
        {
            HotAndDry,
            GentleRains,
            HarshWinds,
            MistyMornings,
            SeasonalCycles,
            StillAir,
            ClearSkies,
            ShiftingLight,
            PrecisePatterns,
            SoftMists,
            HotDays_CoolNights,
            StormySeas,
            EternalTwilight,
            ChangingSeasons,
            MagicalAuroras
        }
    }
    #endregion
}
