using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

/// <summary>
/// Comprehensive modding system for Cinder of Darkness
/// Enables player-driven content creation with safe execution and auto-loading
/// </summary>
public class ModdingSystem : MonoBehaviour
{
    [Header("Modding Configuration")]
    public bool enableModding = true;
    public bool enableAutoLoad = true;
    public bool enableSafeMode = true;
    public int maxModsPerCategory = 50;
    
    [Header("Mod Directories")]
    public string modsRootPath = "Mods";
    public string[] modSubdirectories = { "CustomNPCs", "Arenas", "Weapons", "Dialogue", "Audio", "Textures" };
    
    [Header("UI References")]
    public GameObject modsMenuUI;
    public Transform modListParent;
    public GameObject modEntryPrefab;
    
    // Static instance
    private static ModdingSystem instance;
    public static ModdingSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ModdingSystem>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ModdingSystem");
                    instance = go.AddComponent<ModdingSystem>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Mod management
    private Dictionary<string, LoadedMod> loadedMods = new Dictionary<string, LoadedMod>();
    private Dictionary<string, bool> modEnabledStates = new Dictionary<string, bool>();
    private ModConfiguration config;
    private List<string> unlockedTools = new List<string>();
    
    // Mod data structures
    [System.Serializable]
    public class LoadedMod
    {
        public string modId;
        public string name;
        public string author;
        public string version;
        public string description;
        public ModType type;
        public bool isEnabled;
        public bool isValid;
        public string filePath;
        public object modData;
        public List<string> dependencies;
        public string[] tags;
    }
    
    public enum ModType
    {
        Weapon,
        Armor,
        Magic,
        NPC,
        Arena,
        Quest,
        Dialogue,
        Audio,
        Texture,
        Script,
        Complete // Full conversion mod
    }
    
    [System.Serializable]
    public class ModConfiguration
    {
        public bool enableModding = true;
        public bool enableAutoLoad = true;
        public bool enableSafeMode = true;
        public List<string> enabledMods = new List<string>();
        public List<string> disabledMods = new List<string>();
        public Dictionary<string, object> modSettings = new Dictionary<string, object>();
    }
    
    // Mod definition structures
    [System.Serializable]
    public class WeaponMod
    {
        public string weaponId;
        public string name;
        public string description;
        public float damage;
        public float speed;
        public float range;
        public string[] specialAbilities;
        public string modelPath;
        public string texturePath;
        public string audioPath;
    }
    
    [System.Serializable]
    public class NPCMod
    {
        public string npcId;
        public string name;
        public string race;
        public string title;
        public string personality;
        public DialogueTree dialogue;
        public CombatAI combatAI;
        public string faction;
        public Vector3 spawnLocation;
        public string modelPath;
        public string[] voiceLines;
    }
    
    [System.Serializable]
    public class ArenaMod
    {
        public string arenaId;
        public string name;
        public string biome;
        public EnvironmentSettings environment;
        public List<EnemySpawn> enemies;
        public List<PropPlacement> props;
        public WinCondition winCondition;
        public string[] modifiers;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeModdingSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        CreateModDirectories();
        LoadModConfiguration();
        LoadUnlockedTools();
        
        if (enableAutoLoad)
        {
            ScanAndLoadMods();
        }
    }
    
    void InitializeModdingSystem()
    {
        Debug.Log("Modding System initialized");
    }
    
    void CreateModDirectories()
    {
        string fullModsPath = Path.Combine(Application.persistentDataPath, modsRootPath);
        
        // Create main mods directory
        if (!Directory.Exists(fullModsPath))
        {
            Directory.CreateDirectory(fullModsPath);
        }
        
        // Create subdirectories
        foreach (string subdir in modSubdirectories)
        {
            string subdirPath = Path.Combine(fullModsPath, subdir);
            if (!Directory.Exists(subdirPath))
            {
                Directory.CreateDirectory(subdirPath);
            }
        }
        
        // Create config file if it doesn't exist
        string configPath = Path.Combine(fullModsPath, "Config.json");
        if (!File.Exists(configPath))
        {
            CreateDefaultConfig(configPath);
        }
        
        Debug.Log($"Mod directories created at: {fullModsPath}");
    }
    
    void CreateDefaultConfig(string configPath)
    {
        var defaultConfig = new ModConfiguration
        {
            enableModding = true,
            enableAutoLoad = true,
            enableSafeMode = true
        };
        
        string json = JsonConvert.SerializeObject(defaultConfig, Formatting.Indented);
        File.WriteAllText(configPath, json);
    }
    
    void LoadModConfiguration()
    {
        string configPath = Path.Combine(Application.persistentDataPath, modsRootPath, "Config.json");
        
        if (File.Exists(configPath))
        {
            try
            {
                string json = File.ReadAllText(configPath);
                config = JsonConvert.DeserializeObject<ModConfiguration>(json);
                
                // Apply config settings
                enableModding = config.enableModding;
                enableAutoLoad = config.enableAutoLoad;
                enableSafeMode = config.enableSafeMode;
                
                Debug.Log("Mod configuration loaded");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load mod configuration: {e.Message}");
                config = new ModConfiguration();
            }
        }
        else
        {
            config = new ModConfiguration();
        }
    }
    
    void LoadUnlockedTools()
    {
        // Load unlocked mod tools from PlayerPrefs
        unlockedTools.Clear();
        
        string[] possibleTools = { "basic_editor", "advanced_editor", "npc_creator", "arena_creator", "script_editor" };
        foreach (string tool in possibleTools)
        {
            if (PlayerPrefs.GetInt($"ModTool_{tool}", 0) == 1)
            {
                unlockedTools.Add(tool);
            }
        }
        
        Debug.Log($"Unlocked mod tools: {string.Join(", ", unlockedTools)}");
    }
    
    // Public API
    public static bool IsModdingEnabled()
    {
        return Instance.enableModding;
    }
    
    public static void EnableModding(bool enabled)
    {
        Instance.enableModding = enabled;
        Instance.SaveModConfiguration();
    }
    
    public static void ScanForMods()
    {
        Instance.ScanAndLoadMods();
    }
    
    public static void EnableMod(string modId, bool enabled)
    {
        Instance.EnableModInternal(modId, enabled);
    }
    
    public static LoadedMod[] GetLoadedMods()
    {
        return Instance.loadedMods.Values.ToArray();
    }
    
    public static LoadedMod[] GetModsByType(ModType type)
    {
        return Instance.loadedMods.Values.Where(m => m.type == type).ToArray();
    }
    
    public static bool IsToolUnlocked(string toolId)
    {
        return Instance.unlockedTools.Contains(toolId);
    }
    
    public static void UnlockTool(string toolId)
    {
        Instance.UnlockToolInternal(toolId);
    }
    
    void ScanAndLoadMods()
    {
        if (!enableModding) return;
        
        loadedMods.Clear();
        string modsPath = Path.Combine(Application.persistentDataPath, modsRootPath);
        
        // Scan each subdirectory
        foreach (string subdir in modSubdirectories)
        {
            string subdirPath = Path.Combine(modsPath, subdir);
            if (Directory.Exists(subdirPath))
            {
                ScanDirectory(subdirPath, GetModTypeFromDirectory(subdir));
            }
        }
        
        // Load enabled mods
        LoadEnabledMods();
        
        Debug.Log($"Scanned and loaded {loadedMods.Count} mods");
    }
    
    ModType GetModTypeFromDirectory(string directory)
    {
        switch (directory.ToLower())
        {
            case "customnpcs": return ModType.NPC;
            case "arenas": return ModType.Arena;
            case "weapons": return ModType.Weapon;
            case "dialogue": return ModType.Dialogue;
            case "audio": return ModType.Audio;
            case "textures": return ModType.Texture;
            default: return ModType.Complete;
        }
    }
    
    void ScanDirectory(string directoryPath, ModType type)
    {
        string[] files = Directory.GetFiles(directoryPath, "*.json", SearchOption.AllDirectories);
        
        foreach (string file in files)
        {
            try
            {
                LoadModFromFile(file, type);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load mod from {file}: {e.Message}");
            }
        }
    }
    
    void LoadModFromFile(string filePath, ModType type)
    {
        string json = File.ReadAllText(filePath);
        
        // Parse basic mod info first
        var basicInfo = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
        
        if (!basicInfo.ContainsKey("modId") || !basicInfo.ContainsKey("name"))
        {
            Debug.LogWarning($"Invalid mod file (missing required fields): {filePath}");
            return;
        }
        
        string modId = basicInfo["modId"].ToString();
        
        var mod = new LoadedMod
        {
            modId = modId,
            name = basicInfo.ContainsKey("name") ? basicInfo["name"].ToString() : "Unknown",
            author = basicInfo.ContainsKey("author") ? basicInfo["author"].ToString() : "Unknown",
            version = basicInfo.ContainsKey("version") ? basicInfo["version"].ToString() : "1.0",
            description = basicInfo.ContainsKey("description") ? basicInfo["description"].ToString() : "",
            type = type,
            filePath = filePath,
            isValid = true,
            dependencies = basicInfo.ContainsKey("dependencies") ? 
                JsonConvert.DeserializeObject<List<string>>(basicInfo["dependencies"].ToString()) : 
                new List<string>(),
            tags = basicInfo.ContainsKey("tags") ? 
                JsonConvert.DeserializeObject<string[]>(basicInfo["tags"].ToString()) : 
                new string[0]
        };
        
        // Parse type-specific data
        switch (type)
        {
            case ModType.Weapon:
                mod.modData = JsonConvert.DeserializeObject<WeaponMod>(json);
                break;
            case ModType.NPC:
                mod.modData = JsonConvert.DeserializeObject<NPCMod>(json);
                break;
            case ModType.Arena:
                mod.modData = JsonConvert.DeserializeObject<ArenaMod>(json);
                break;
            default:
                mod.modData = basicInfo;
                break;
        }
        
        // Check if mod is enabled
        mod.isEnabled = config.enabledMods.Contains(modId);
        
        loadedMods[modId] = mod;
        Debug.Log($"Loaded mod: {mod.name} by {mod.author}");
    }
    
    void LoadEnabledMods()
    {
        foreach (var mod in loadedMods.Values.Where(m => m.isEnabled))
        {
            ApplyMod(mod);
        }
    }
    
    void ApplyMod(LoadedMod mod)
    {
        if (!mod.isValid) return;
        
        try
        {
            switch (mod.type)
            {
                case ModType.Weapon:
                    ApplyWeaponMod(mod);
                    break;
                case ModType.NPC:
                    ApplyNPCMod(mod);
                    break;
                case ModType.Arena:
                    ApplyArenaMod(mod);
                    break;
                case ModType.Dialogue:
                    ApplyDialogueMod(mod);
                    break;
                case ModType.Audio:
                    ApplyAudioMod(mod);
                    break;
                default:
                    Debug.LogWarning($"Mod type not implemented: {mod.type}");
                    break;
            }
            
            Debug.Log($"Applied mod: {mod.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to apply mod {mod.name}: {e.Message}");
            mod.isValid = false;
        }
    }
    
    void ApplyWeaponMod(LoadedMod mod)
    {
        var weaponData = (WeaponMod)mod.modData;
        
        // Register weapon with weapon system
        var weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.RegisterModdedWeapon(weaponData);
        }
    }
    
    void ApplyNPCMod(LoadedMod mod)
    {
        var npcData = (NPCMod)mod.modData;
        
        // Register NPC with NPC system
        var npcSystem = FindObjectOfType<NPCSystem>();
        if (npcSystem != null)
        {
            npcSystem.RegisterModdedNPC(npcData);
        }
    }
    
    void ApplyArenaMod(LoadedMod mod)
    {
        var arenaData = (ArenaMod)mod.modData;
        
        // Register arena with trials system
        var trialsSystem = FindObjectOfType<TrialsOfTheAsh>();
        if (trialsSystem != null)
        {
            trialsSystem.RegisterModdedArena(arenaData);
        }
    }
    
    void ApplyDialogueMod(LoadedMod mod)
    {
        // Apply dialogue modifications
        var dialogueSystem = FindObjectOfType<DialogueSystem>();
        if (dialogueSystem != null)
        {
            dialogueSystem.LoadModdedDialogue(mod);
        }
    }
    
    void ApplyAudioMod(LoadedMod mod)
    {
        // Apply audio modifications
        var audioManager = AudioManager.Instance;
        if (audioManager != null)
        {
            audioManager.LoadModdedAudio(mod);
        }
    }
    
    void EnableModInternal(string modId, bool enabled)
    {
        if (!loadedMods.ContainsKey(modId)) return;
        
        var mod = loadedMods[modId];
        mod.isEnabled = enabled;
        
        if (enabled)
        {
            // Check dependencies
            if (!CheckDependencies(mod))
            {
                Debug.LogWarning($"Cannot enable mod {mod.name}: missing dependencies");
                return;
            }
            
            ApplyMod(mod);
            
            if (!config.enabledMods.Contains(modId))
                config.enabledMods.Add(modId);
            
            config.disabledMods.Remove(modId);
        }
        else
        {
            RemoveMod(mod);
            
            config.enabledMods.Remove(modId);
            
            if (!config.disabledMods.Contains(modId))
                config.disabledMods.Add(modId);
        }
        
        SaveModConfiguration();
    }
    
    bool CheckDependencies(LoadedMod mod)
    {
        foreach (string dependency in mod.dependencies)
        {
            if (!loadedMods.ContainsKey(dependency) || !loadedMods[dependency].isEnabled)
            {
                return false;
            }
        }
        return true;
    }
    
    void RemoveMod(LoadedMod mod)
    {
        // Remove mod effects from game systems
        switch (mod.type)
        {
            case ModType.Weapon:
                var weaponSystem = FindObjectOfType<WeaponSystem>();
                if (weaponSystem != null)
                {
                    weaponSystem.UnregisterModdedWeapon(mod.modId);
                }
                break;
            case ModType.NPC:
                var npcSystem = FindObjectOfType<NPCSystem>();
                if (npcSystem != null)
                {
                    npcSystem.UnregisterModdedNPC(mod.modId);
                }
                break;
            // Add other mod types as needed
        }
        
        Debug.Log($"Removed mod: {mod.name}");
    }
    
    void UnlockToolInternal(string toolId)
    {
        if (!unlockedTools.Contains(toolId))
        {
            unlockedTools.Add(toolId);
            PlayerPrefs.SetInt($"ModTool_{toolId}", 1);
            PlayerPrefs.Save();
            
            Debug.Log($"Mod tool unlocked: {toolId}");
        }
    }
    
    void SaveModConfiguration()
    {
        string configPath = Path.Combine(Application.persistentDataPath, modsRootPath, "Config.json");
        
        try
        {
            string json = JsonConvert.SerializeObject(config, Formatting.Indented);
            File.WriteAllText(configPath, json);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save mod configuration: {e.Message}");
        }
    }
    
    // Mod creation helpers
    public static void CreateWeaponMod(WeaponMod weaponData)
    {
        Instance.CreateWeaponModInternal(weaponData);
    }
    
    public static void CreateNPCMod(NPCMod npcData)
    {
        Instance.CreateNPCModInternal(npcData);
    }
    
    public static void CreateArenaMod(ArenaMod arenaData)
    {
        Instance.CreateArenaModInternal(arenaData);
    }
    
    void CreateWeaponModInternal(WeaponMod weaponData)
    {
        string filePath = Path.Combine(Application.persistentDataPath, modsRootPath, "Weapons", $"{weaponData.weaponId}.json");
        
        try
        {
            string json = JsonConvert.SerializeObject(weaponData, Formatting.Indented);
            File.WriteAllText(filePath, json);
            
            Debug.Log($"Created weapon mod: {weaponData.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to create weapon mod: {e.Message}");
        }
    }
    
    void CreateNPCModInternal(NPCMod npcData)
    {
        string filePath = Path.Combine(Application.persistentDataPath, modsRootPath, "CustomNPCs", $"{npcData.npcId}.json");
        
        try
        {
            string json = JsonConvert.SerializeObject(npcData, Formatting.Indented);
            File.WriteAllText(filePath, json);
            
            Debug.Log($"Created NPC mod: {npcData.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to create NPC mod: {e.Message}");
        }
    }
    
    void CreateArenaModInternal(ArenaMod arenaData)
    {
        string filePath = Path.Combine(Application.persistentDataPath, modsRootPath, "Arenas", $"{arenaData.arenaId}.json");
        
        try
        {
            string json = JsonConvert.SerializeObject(arenaData, Formatting.Indented);
            File.WriteAllText(filePath, json);
            
            Debug.Log($"Created arena mod: {arenaData.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to create arena mod: {e.Message}");
        }
    }
    
    // Safe execution wrapper
    void ExecuteModSafely(System.Action modAction, string modName)
    {
        if (!enableSafeMode)
        {
            modAction();
            return;
        }
        
        try
        {
            modAction();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Mod execution failed safely for {modName}: {e.Message}");
            
            // Log to mod error file
            LogModError(modName, e);
        }
    }
    
    void LogModError(string modName, System.Exception error)
    {
        string logPath = Path.Combine(Application.persistentDataPath, modsRootPath, "mod_errors.log");
        string logEntry = $"[{System.DateTime.Now}] {modName}: {error.Message}\n{error.StackTrace}\n\n";
        
        try
        {
            File.AppendAllText(logPath, logEntry);
        }
        catch
        {
            // Ignore logging errors
        }
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Notify mods of game events
        foreach (var mod in loadedMods.Values.Where(m => m.isEnabled))
        {
            ExecuteModSafely(() => {
                // Send event to mod if it has event handlers
                NotifyModOfEvent(mod, eventName, parameters);
            }, mod.name);
        }
    }
    
    void NotifyModOfEvent(LoadedMod mod, string eventName, Dictionary<string, object> parameters)
    {
        // Implementation would depend on mod scripting system
        // For now, just log the event
        Debug.Log($"Notifying mod {mod.name} of event: {eventName}");
    }
    
    // Debug methods
    #if UNITY_EDITOR
    [ContextMenu("Reload All Mods")]
    public void DebugReloadAllMods()
    {
        ScanAndLoadMods();
    }
    
    [ContextMenu("Clear Mod Cache")]
    public void DebugClearModCache()
    {
        loadedMods.Clear();
        config.enabledMods.Clear();
        config.disabledMods.Clear();
        SaveModConfiguration();
    }
    
    [ContextMenu("Unlock All Tools")]
    public void DebugUnlockAllTools()
    {
        string[] allTools = { "basic_editor", "advanced_editor", "npc_creator", "arena_creator", "script_editor" };
        foreach (string tool in allTools)
        {
            UnlockToolInternal(tool);
        }
    }
    #endif
}

// Supporting data structures
[System.Serializable]
public class DialogueTree
{
    public string rootNodeId;
    public List<DialogueNode> nodes;
}

[System.Serializable]
public class DialogueNode
{
    public string nodeId;
    public string text;
    public string speakerId;
    public List<DialogueChoice> choices;
    public string[] conditions;
    public string[] actions;
}

[System.Serializable]
public class DialogueChoice
{
    public string text;
    public string targetNodeId;
    public string[] requirements;
}

[System.Serializable]
public class CombatAI
{
    public string aiType;
    public float aggressionLevel;
    public float detectionRange;
    public string[] abilities;
    public float health;
    public float damage;
}

[System.Serializable]
public class EnvironmentSettings
{
    public string skybox;
    public Color fogColor;
    public float fogDensity;
    public Color ambientLight;
    public string[] weatherEffects;
}

[System.Serializable]
public class EnemySpawn
{
    public string enemyId;
    public Vector3 position;
    public int count;
    public float spawnDelay;
}

[System.Serializable]
public class PropPlacement
{
    public string propId;
    public Vector3 position;
    public Vector3 rotation;
    public Vector3 scale;
    public bool isDestructible;
}

[System.Serializable]
public class WinCondition
{
    public string type; // "kill_all", "survive_time", "reach_location", etc.
    public Dictionary<string, object> parameters;
}
