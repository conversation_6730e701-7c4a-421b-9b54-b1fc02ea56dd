using UnityEngine;
using System.Collections.Generic;
using CinderOfDarkness.World;
using CinderOfDarkness.NPCs;

namespace CinderOfDarkness.Integration
{
    /// <summary>
    /// World Integration Manager for Cinder of Darkness.
    /// Coordinates all world-building systems and ensures seamless integration.
    /// </summary>
    public class WorldIntegrationManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Integration Settings")]
        [SerializeField] private bool enableFullIntegration = true;
        [SerializeField] private bool autoInitializeOnStart = true;
        [SerializeField] private float integrationCheckInterval = 60f;

        [Header("System References")]
        [SerializeField] private ExpandedKingdomSystem kingdomSystem;
        [SerializeField] private ComprehensiveWorldExpansion worldExpansion;
        [SerializeField] private NPCRoutineManager routineManager;

        [Header("World State")]
        [SerializeField] private string currentRegion;
        [SerializeField] private string currentSettlement;
        [SerializeField] private float worldTime;
        [SerializeField] private int worldDay;

        [Header("Debug")]
        [SerializeField] private bool showIntegrationStatus = false;
        [SerializeField] private bool logIntegrationEvents = true;
        #endregion

        #region Private Fields
        private Dictionary<string, WorldRegion> worldRegions = new Dictionary<string, WorldRegion>();
        private List<string> discoveredLocations = new List<string>();
        private float lastIntegrationCheck;
        private bool isInitialized = false;
        #endregion

        #region Public Properties
        public static WorldIntegrationManager Instance { get; private set; }
        public bool IsFullyIntegrated { get; private set; }
        public string CurrentRegion => currentRegion;
        public string CurrentSettlement => currentSettlement;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (autoInitializeOnStart)
            {
                InitializeWorldIntegration();
            }
        }

        private void Update()
        {
            if (isInitialized && enableFullIntegration)
            {
                UpdateIntegrationSystems();
            }
        }

        private void OnGUI()
        {
            if (showIntegrationStatus)
            {
                DisplayIntegrationStatus();
            }
        }
        #endregion

        #region Initialization
        public void InitializeWorldIntegration()
        {
            if (isInitialized) return;

            Debug.Log("🌍 Initializing World Integration Manager...");

            // Initialize system references
            InitializeSystemReferences();

            // Setup world regions
            SetupWorldRegions();

            // Validate integration
            ValidateSystemIntegration();

            // Mark as initialized
            isInitialized = true;
            IsFullyIntegrated = true;

            if (logIntegrationEvents)
                Debug.Log("✅ World Integration Manager fully initialized");
        }

        private void InitializeSystemReferences()
        {
            // Get or create kingdom system
            if (kingdomSystem == null)
                kingdomSystem = ExpandedKingdomSystem.Instance;

            // Get or create world expansion system
            if (worldExpansion == null)
                worldExpansion = ComprehensiveWorldExpansion.Instance;

            // Get or create routine manager
            if (routineManager == null)
                routineManager = FindObjectOfType<NPCRoutineManager>();

            if (routineManager == null)
            {
                var routineObj = new GameObject("NPCRoutineManager");
                routineManager = routineObj.AddComponent<NPCRoutineManager>();
                routineObj.transform.SetParent(transform);
            }

            // Initialize routine manager with kingdom system
            if (routineManager != null && kingdomSystem != null)
            {
                routineManager.Initialize(kingdomSystem);
            }
        }

        private void SetupWorldRegions()
        {
            worldRegions.Clear();

            // Create comprehensive world regions
            worldRegions["Eternal_Flame_Region"] = CreateEternalFlameRegion();
            worldRegions["Whispering_Stones_Region"] = CreateWhisperingStonesRegion();
            worldRegions["Crystal_Tears_Region"] = CreateCrystalTearsRegion();
            worldRegions["Iron_Will_Region"] = CreateIronWillRegion();
            worldRegions["Silent_Waters_Region"] = CreateSilentWatersRegion();
            worldRegions["Independent_Villages_Region"] = CreateIndependentVillagesRegion();
            worldRegions["Cultural_Settlements_Region"] = CreateCulturalSettlementsRegion();

            Debug.Log($"Setup {worldRegions.Count} world regions");
        }

        private WorldRegion CreateEternalFlameRegion()
        {
            return new WorldRegion
            {
                regionName = "Eternal Flame Region",
                regionType = WorldRegion.RegionType.Kingdom,
                primaryKingdom = "Kingdom of Eternal Flame",
                associatedVillages = new string[0], // No villages in this kingdom region
                culturalSettlements = new string[0],
                biome = "VolcanicPlains",
                weatherPattern = "HotAndDry",
                dangerLevel = WorldRegion.DangerLevel.Moderate,
                accessRequirements = new string[]
                {
                    "Fire resistance or protection",
                    "Respect for flame worship customs"
                },
                uniqueFeatures = new string[]
                {
                    "Eternal flame spires visible from great distances",
                    "Brass-lined channels carrying sacred fire",
                    "The Great Forge of Souls",
                    "Academy of Flame Arts"
                },
                economicResources = new string[]
                {
                    "Obsidian and volcanic glass",
                    "Rare fire-resistant metals",
                    "Sacred ash and ember essences",
                    "Masterwork brass items"
                }
            };
        }

        private WorldRegion CreateIndependentVillagesRegion()
        {
            return new WorldRegion
            {
                regionName = "Independent Villages Region",
                regionType = WorldRegion.RegionType.VillageCluster,
                primaryKingdom = null,
                associatedVillages = new string[]
                {
                    "Village of Forgotten Names",
                    "Village of Endless Harvest",
                    "Village of Silent Bells",
                    "Village of Wandering Lights",
                    "Village of Broken Mirrors",
                    "Village of Timekeepers",
                    "Village of Dreamweavers"
                },
                culturalSettlements = new string[0],
                biome = "Mixed",
                weatherPattern = "Varied",
                dangerLevel = WorldRegion.DangerLevel.Low,
                accessRequirements = new string[]
                {
                    "Respect for local customs",
                    "Willingness to follow village-specific protocols"
                },
                uniqueFeatures = new string[]
                {
                    "Each village has unique architectural style",
                    "Diverse cultural practices and beliefs",
                    "Independent governance systems",
                    "Rich oral traditions and storytelling"
                },
                economicResources = new string[]
                {
                    "Agricultural products and crafts",
                    "Specialized knowledge and services",
                    "Cultural artifacts and art",
                    "Hospitality and guidance services"
                }
            };
        }

        private WorldRegion CreateCulturalSettlementsRegion()
        {
            return new WorldRegion
            {
                regionName = "Cultural Settlements Region",
                regionType = WorldRegion.RegionType.CulturalHub,
                primaryKingdom = null,
                associatedVillages = new string[0],
                culturalSettlements = new string[]
                {
                    "Oasis of Whispering Palms",
                    "Norse Skald Camp",
                    "Celtic Druid Grove",
                    "Persian Scholar Outpost"
                },
                biome = "Diverse",
                weatherPattern = "Regional",
                dangerLevel = WorldRegion.DangerLevel.Variable,
                accessRequirements = new string[]
                {
                    "Cultural sensitivity and respect",
                    "Understanding of diverse customs",
                    "Peaceful intentions"
                },
                uniqueFeatures = new string[]
                {
                    "Multicultural trading posts",
                    "Centers of learning and wisdom",
                    "Diverse architectural styles",
                    "Rich cultural exchange opportunities"
                },
                economicResources = new string[]
                {
                    "Exotic goods and spices",
                    "Cultural knowledge and texts",
                    "Artistic works and crafts",
                    "Translation and diplomatic services"
                }
            };
        }

        private void ValidateSystemIntegration()
        {
            bool allSystemsValid = true;

            // Validate kingdom system
            if (kingdomSystem == null)
            {
                Debug.LogError("❌ Kingdom System not found!");
                allSystemsValid = false;
            }

            // Validate world expansion
            if (worldExpansion == null)
            {
                Debug.LogError("❌ World Expansion System not found!");
                allSystemsValid = false;
            }

            // Validate routine manager
            if (routineManager == null)
            {
                Debug.LogError("❌ NPC Routine Manager not found!");
                allSystemsValid = false;
            }

            IsFullyIntegrated = allSystemsValid;

            if (IsFullyIntegrated)
            {
                Debug.Log("✅ All world systems successfully integrated");
            }
            else
            {
                Debug.LogWarning("⚠️ Some world systems failed integration");
            }
        }
        #endregion

        #region Integration Updates
        private void UpdateIntegrationSystems()
        {
            if (Time.time - lastIntegrationCheck < integrationCheckInterval) return;

            lastIntegrationCheck = Time.time;

            // Update world time
            UpdateWorldTime();

            // Check for location changes
            CheckLocationChanges();

            // Synchronize systems
            SynchronizeSystems();

            // Update region states
            UpdateRegionStates();
        }

        private void UpdateWorldTime()
        {
            worldTime += Time.deltaTime * 0.1f; // Accelerated time
            
            if (worldTime >= 24f)
            {
                worldTime -= 24f;
                worldDay++;
                OnNewDay();
            }
        }

        private void CheckLocationChanges()
        {
            // Check if player has moved to a new region or settlement
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                string newRegion = DeterminePlayerRegion(player.transform.position);
                string newSettlement = DeterminePlayerSettlement(player.transform.position);

                if (newRegion != currentRegion)
                {
                    OnRegionChanged(currentRegion, newRegion);
                    currentRegion = newRegion;
                }

                if (newSettlement != currentSettlement)
                {
                    OnSettlementChanged(currentSettlement, newSettlement);
                    currentSettlement = newSettlement;
                }
            }
        }

        private void SynchronizeSystems()
        {
            // Synchronize time across all systems
            if (routineManager != null)
            {
                // Update routine manager with current time
                // routineManager.SetWorldTime(worldTime);
            }

            // Synchronize weather and environmental effects
            SynchronizeEnvironmentalEffects();

            // Update NPC behaviors based on current location
            UpdateLocationBasedBehaviors();
        }

        private void UpdateRegionStates()
        {
            foreach (var region in worldRegions.Values)
            {
                UpdateRegionState(region);
            }
        }

        private void UpdateRegionState(WorldRegion region)
        {
            // Update region based on time, weather, and player actions
            region.lastUpdateTime = worldTime;
            
            // Update economic activity
            UpdateRegionEconomy(region);
            
            // Update population dynamics
            UpdateRegionPopulation(region);
            
            // Update environmental conditions
            UpdateRegionEnvironment(region);
        }
        #endregion

        #region Event Handlers
        private void OnNewDay()
        {
            if (logIntegrationEvents)
                Debug.Log($"🌅 New day: Day {worldDay}");

            // Trigger daily events across all systems
            TriggerDailyEvents();
        }

        private void OnRegionChanged(string oldRegion, string newRegion)
        {
            if (logIntegrationEvents)
                Debug.Log($"🗺️ Region changed: {oldRegion} → {newRegion}");

            // Handle region transition
            HandleRegionTransition(oldRegion, newRegion);
        }

        private void OnSettlementChanged(string oldSettlement, string newSettlement)
        {
            if (logIntegrationEvents)
                Debug.Log($"🏘️ Settlement changed: {oldSettlement} → {newSettlement}");

            // Handle settlement transition
            HandleSettlementTransition(oldSettlement, newSettlement);
        }

        private void HandleRegionTransition(string oldRegion, string newRegion)
        {
            // Update kingdom system
            if (kingdomSystem != null && !string.IsNullOrEmpty(newRegion))
            {
                var region = GetRegionByName(newRegion);
                if (region != null && !string.IsNullOrEmpty(region.primaryKingdom))
                {
                    kingdomSystem.EnterKingdom(region.primaryKingdom);
                }
            }

            // Update environmental effects
            ApplyRegionalEffects(newRegion);
        }

        private void HandleSettlementTransition(string oldSettlement, string newSettlement)
        {
            // Update village/settlement systems
            if (kingdomSystem != null && !string.IsNullOrEmpty(newSettlement))
            {
                // Try kingdom system first
                var village = kingdomSystem.GetVillageByName(newSettlement);
                if (village != null)
                {
                    kingdomSystem.EnterVillage(newSettlement);
                }
                // Try world expansion system
                else if (worldExpansion != null)
                {
                    var expandedVillage = worldExpansion.GetVillageByName(newSettlement);
                    if (expandedVillage != null)
                    {
                        // Handle expanded village entry
                        HandleExpandedVillageEntry(expandedVillage);
                    }
                    else
                    {
                        var settlement = worldExpansion.GetSettlementByName(newSettlement);
                        if (settlement != null)
                        {
                            HandleCulturalSettlementEntry(settlement);
                        }
                    }
                }
            }
        }

        private void TriggerDailyEvents()
        {
            // Trigger daily events across all systems
            foreach (var region in worldRegions.Values)
            {
                TriggerRegionDailyEvents(region);
            }
        }
        #endregion

        #region Utility Methods
        private string DeterminePlayerRegion(Vector3 playerPosition)
        {
            // Determine which region the player is in based on position
            // This would use actual world coordinates and region boundaries
            return "Independent_Villages_Region"; // Placeholder
        }

        private string DeterminePlayerSettlement(Vector3 playerPosition)
        {
            // Determine which settlement the player is in based on position
            // This would use actual settlement boundaries
            return "Village of Forgotten Names"; // Placeholder
        }

        private WorldRegion GetRegionByName(string regionName)
        {
            return worldRegions.ContainsKey(regionName) ? worldRegions[regionName] : null;
        }

        private void SynchronizeEnvironmentalEffects()
        {
            // Synchronize weather, lighting, and environmental effects
            // This would integrate with actual weather and lighting systems
        }

        private void UpdateLocationBasedBehaviors()
        {
            // Update NPC behaviors based on current location
            if (routineManager != null && !string.IsNullOrEmpty(currentSettlement))
            {
                // Update routines for current location
            }
        }

        private void UpdateRegionEconomy(WorldRegion region)
        {
            // Update economic activity in the region
            // This would integrate with actual economy systems
        }

        private void UpdateRegionPopulation(WorldRegion region)
        {
            // Update population dynamics
            // This would integrate with actual population systems
        }

        private void UpdateRegionEnvironment(WorldRegion region)
        {
            // Update environmental conditions
            // This would integrate with actual environmental systems
        }

        private void ApplyRegionalEffects(string regionName)
        {
            var region = GetRegionByName(regionName);
            if (region != null)
            {
                // Apply region-specific effects
                ApplyRegionBiomeEffects(region.biome);
                ApplyRegionWeatherEffects(region.weatherPattern);
            }
        }

        private void ApplyRegionBiomeEffects(string biome)
        {
            // Apply biome-specific effects
            // This would integrate with actual biome systems
        }

        private void ApplyRegionWeatherEffects(string weatherPattern)
        {
            // Apply weather pattern effects
            // This would integrate with actual weather systems
        }

        private void HandleExpandedVillageEntry(ComprehensiveWorldExpansion.ExpandedVillage village)
        {
            // Handle entry into expanded village
            if (logIntegrationEvents)
                Debug.Log($"Entered expanded village: {village.villageName}");
        }

        private void HandleCulturalSettlementEntry(ComprehensiveWorldExpansion.CulturalSettlement settlement)
        {
            // Handle entry into cultural settlement
            if (logIntegrationEvents)
                Debug.Log($"Entered cultural settlement: {settlement.settlementName}");
        }

        private void TriggerRegionDailyEvents(WorldRegion region)
        {
            // Trigger daily events for the region
            // This would integrate with actual event systems
        }

        private void DisplayIntegrationStatus()
        {
            GUILayout.BeginArea(new Rect(Screen.width - 320, 10, 300, 250));
            GUILayout.Label("🌍 World Integration Status");
            GUILayout.Label($"Fully Integrated: {(IsFullyIntegrated ? "✅" : "❌")}");
            GUILayout.Label($"Current Region: {currentRegion}");
            GUILayout.Label($"Current Settlement: {currentSettlement}");
            GUILayout.Label($"World Time: {worldTime:F1}");
            GUILayout.Label($"World Day: {worldDay}");
            GUILayout.Label($"Regions: {worldRegions.Count}");
            GUILayout.Label($"Discovered Locations: {discoveredLocations.Count}");
            
            if (kingdomSystem != null)
            {
                GUILayout.Label($"Kingdom System: ✅");
                GUILayout.Label($"In Kingdom: {kingdomSystem.IsInKingdom()}");
                GUILayout.Label($"In Village: {kingdomSystem.IsInVillage()}");
            }
            else
            {
                GUILayout.Label($"Kingdom System: ❌");
            }
            
            if (worldExpansion != null)
            {
                GUILayout.Label($"World Expansion: ✅");
            }
            else
            {
                GUILayout.Label($"World Expansion: ❌");
            }
            
            if (routineManager != null)
            {
                GUILayout.Label($"Routine Manager: ✅");
                GUILayout.Label($"Active Routines: {routineManager.ActiveRoutineCount}");
            }
            else
            {
                GUILayout.Label($"Routine Manager: ❌");
            }
            
            GUILayout.EndArea();
        }
        #endregion

        #region Public API
        public void DiscoverLocation(string locationName)
        {
            if (!discoveredLocations.Contains(locationName))
            {
                discoveredLocations.Add(locationName);
                
                if (logIntegrationEvents)
                    Debug.Log($"🗺️ Discovered new location: {locationName}");
            }
        }

        public bool IsLocationDiscovered(string locationName)
        {
            return discoveredLocations.Contains(locationName);
        }

        public List<string> GetDiscoveredLocations()
        {
            return new List<string>(discoveredLocations);
        }

        public WorldRegion GetCurrentRegion()
        {
            return GetRegionByName(currentRegion);
        }

        public List<WorldRegion> GetAllRegions()
        {
            return new List<WorldRegion>(worldRegions.Values);
        }

        public void ForceSystemSynchronization()
        {
            SynchronizeSystems();
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class WorldRegion
    {
        [Header("Basic Information")]
        public string regionName;
        public RegionType regionType;
        public string primaryKingdom;
        public string[] associatedVillages;
        public string[] culturalSettlements;

        [Header("Environment")]
        public string biome;
        public string weatherPattern;
        public DangerLevel dangerLevel;

        [Header("Access and Features")]
        public string[] accessRequirements;
        public string[] uniqueFeatures;
        public string[] economicResources;

        [Header("State")]
        public float lastUpdateTime;
        public bool isDiscovered;
        public bool isAccessible;

        public enum RegionType
        {
            Kingdom,
            VillageCluster,
            CulturalHub,
            Wilderness,
            Ruins,
            Sacred,
            Dangerous,
            Trading
        }

        public enum DangerLevel
        {
            Safe,
            Low,
            Moderate,
            High,
            Extreme,
            Variable
        }
    }
    #endregion
}
