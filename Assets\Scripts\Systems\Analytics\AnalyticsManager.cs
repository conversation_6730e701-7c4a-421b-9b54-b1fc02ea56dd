using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System;

/// <summary>
/// Analytics Manager for Cinder of Darkness
/// Collects anonymous gameplay data for post-launch insights and improvements
/// </summary>
public class AnalyticsManager : MonoBehaviour
{
    [Header("Analytics Settings")]
    public bool enableAnalytics = true;
    public bool userOptedIn = false;
    public bool collectOnlyBasicData = true;
    public float dataFlushInterval = 300f; // 5 minutes
    
    [Header("Data Collection")]
    public bool trackPlaytime = true;
    public bool trackDeaths = true;
    public bool trackBossAttempts = true;
    public bool trackMoralChoices = true;
    public bool trackMagicUsage = true;
    public bool trackQuestCompletion = true;
    public bool trackPerformanceMetrics = false; // Opt-in only
    
    [Header("Privacy")]
    public bool anonymizeData = true;
    public bool encryptLocalData = true;
    public string dataRetentionDays = "30";
    
    // Static instance
    private static AnalyticsManager instance;
    public static AnalyticsManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<AnalyticsManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("AnalyticsManager");
                    instance = go.AddComponent<AnalyticsManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Analytics data structures
    [System.Serializable]
    public class AnalyticsSession
    {
        public string sessionId;
        public DateTime sessionStart;
        public DateTime sessionEnd;
        public float totalPlaytime;
        public string gameVersion;
        public string platform;
        public bool isFirstSession;
        
        // Gameplay metrics
        public int totalDeaths;
        public int totalKills;
        public int bossAttempts;
        public int bossDefeats;
        public int questsCompleted;
        public int moralChoicesMade;
        public Dictionary<string, int> magicUsageCount;
        public Dictionary<string, float> timeSpentInRegions;
        
        // Performance metrics (opt-in only)
        public float averageFPS;
        public float minFPS;
        public float maxFPS;
        public long memoryUsage;
        public float loadTimes;
        
        public AnalyticsSession()
        {
            sessionId = Guid.NewGuid().ToString();
            sessionStart = DateTime.Now;
            gameVersion = Application.version;
            platform = Application.platform.ToString();
            magicUsageCount = new Dictionary<string, int>();
            timeSpentInRegions = new Dictionary<string, float>();
        }
    }
    
    // Current session data
    private AnalyticsSession currentSession;
    private List<AnalyticsSession> pendingSessions = new List<AnalyticsSession>();
    private string analyticsDataPath;
    private float lastFlushTime;
    private float sessionStartTime;
    
    // Performance tracking
    private List<float> fpsHistory = new List<float>();
    private float fpsUpdateInterval = 1f;
    private float lastFPSUpdate;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAnalytics();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        LoadUserPreferences();
        
        if (enableAnalytics && userOptedIn)
        {
            StartNewSession();
        }
    }
    
    void Update()
    {
        if (!enableAnalytics || !userOptedIn || currentSession == null) return;
        
        UpdateSessionData();
        UpdatePerformanceMetrics();
        
        // Flush data periodically
        if (Time.time - lastFlushTime > dataFlushInterval)
        {
            FlushAnalyticsData();
            lastFlushTime = Time.time;
        }
    }
    
    void InitializeAnalytics()
    {
        analyticsDataPath = Path.Combine(Application.persistentDataPath, "Analytics");
        Directory.CreateDirectory(analyticsDataPath);
        
        #if UNITY_EDITOR
        Debug.Log("Analytics Manager initialized");
        #endif
    }
    
    void LoadUserPreferences()
    {
        userOptedIn = PlayerPrefs.GetInt("Analytics_OptIn", 0) == 1;
        collectOnlyBasicData = PlayerPrefs.GetInt("Analytics_BasicOnly", 1) == 1;
        
        // If user hasn't made a choice, show opt-in dialog
        if (!PlayerPrefs.HasKey("Analytics_OptIn"))
        {
            ShowOptInDialog();
        }
    }
    
    void ShowOptInDialog()
    {
        // This would show a proper UI dialog in the full implementation
        // For now, we'll default to opt-out for privacy
        userOptedIn = false;
        PlayerPrefs.SetInt("Analytics_OptIn", 0);
        PlayerPrefs.Save();
        
        #if UNITY_EDITOR
        Debug.Log("Analytics opt-in dialog would be shown here");
        #endif
    }
    
    void StartNewSession()
    {
        currentSession = new AnalyticsSession();
        currentSession.isFirstSession = !PlayerPrefs.HasKey("Analytics_HasPlayed");
        sessionStartTime = Time.time;
        
        if (currentSession.isFirstSession)
        {
            PlayerPrefs.SetInt("Analytics_HasPlayed", 1);
            PlayerPrefs.Save();
        }
        
        #if UNITY_EDITOR
        Debug.Log($"Analytics session started: {currentSession.sessionId}");
        #endif
    }
    
    void UpdateSessionData()
    {
        if (currentSession == null) return;
        
        currentSession.totalPlaytime = Time.time - sessionStartTime;
        
        // Track time spent in current region
        string currentRegion = GetCurrentRegion();
        if (!string.IsNullOrEmpty(currentRegion))
        {
            if (!currentSession.timeSpentInRegions.ContainsKey(currentRegion))
            {
                currentSession.timeSpentInRegions[currentRegion] = 0f;
            }
            currentSession.timeSpentInRegions[currentRegion] += Time.deltaTime;
        }
    }
    
    void UpdatePerformanceMetrics()
    {
        if (!trackPerformanceMetrics || collectOnlyBasicData) return;
        
        // Update FPS tracking
        if (Time.time - lastFPSUpdate > fpsUpdateInterval)
        {
            float currentFPS = 1f / Time.deltaTime;
            fpsHistory.Add(currentFPS);
            
            // Keep only recent FPS data
            if (fpsHistory.Count > 60) // Keep last 60 seconds
            {
                fpsHistory.RemoveAt(0);
            }
            
            lastFPSUpdate = Time.time;
        }
        
        // Update memory usage
        if (currentSession != null)
        {
            currentSession.memoryUsage = System.GC.GetTotalMemory(false);
        }
    }
    
    string GetCurrentRegion()
    {
        // Get current scene/region name
        return UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
    }
    
    void FlushAnalyticsData()
    {
        if (currentSession == null) return;
        
        // Calculate performance metrics
        if (fpsHistory.Count > 0 && trackPerformanceMetrics)
        {
            currentSession.averageFPS = CalculateAverageFPS();
            currentSession.minFPS = CalculateMinFPS();
            currentSession.maxFPS = CalculateMaxFPS();
        }
        
        // Save session data
        SaveSessionData(currentSession);
        
        #if UNITY_EDITOR
        Debug.Log("Analytics data flushed to local storage");
        #endif
    }
    
    void SaveSessionData(AnalyticsSession session)
    {
        try
        {
            string fileName = $"session_{session.sessionId}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            string filePath = Path.Combine(analyticsDataPath, fileName);
            
            string jsonData = JsonUtility.ToJson(session, true);
            
            if (encryptLocalData)
            {
                jsonData = EncryptData(jsonData);
            }
            
            File.WriteAllText(filePath, jsonData);
        }
        catch (Exception e)
        {
            #if UNITY_EDITOR
            Debug.LogError($"Failed to save analytics data: {e.Message}");
            #endif
        }
    }
    
    string EncryptData(string data)
    {
        // Simple XOR encryption for local storage
        char[] chars = data.ToCharArray();
        string key = "CinderAnalytics2024";
        
        for (int i = 0; i < chars.Length; i++)
        {
            chars[i] = (char)(chars[i] ^ key[i % key.Length]);
        }
        
        return new string(chars);
    }
    
    float CalculateAverageFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float sum = 0f;
        foreach (float fps in fpsHistory)
        {
            sum += fps;
        }
        return sum / fpsHistory.Count;
    }
    
    float CalculateMinFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float min = float.MaxValue;
        foreach (float fps in fpsHistory)
        {
            if (fps < min) min = fps;
        }
        return min;
    }
    
    float CalculateMaxFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float max = 0f;
        foreach (float fps in fpsHistory)
        {
            if (fps > max) max = fps;
        }
        return max;
    }
    
    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            EndCurrentSession();
        }
        else if (enableAnalytics && userOptedIn)
        {
            StartNewSession();
        }
    }
    
    void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            FlushAnalyticsData();
        }
    }
    
    void OnDestroy()
    {
        EndCurrentSession();
    }
    
    void EndCurrentSession()
    {
        if (currentSession == null) return;
        
        currentSession.sessionEnd = DateTime.Now;
        FlushAnalyticsData();
        currentSession = null;
    }
    
    // Public API for tracking specific events
    public static void TrackPlayerDeath(string cause = "")
    {
        if (Instance.currentSession != null && Instance.trackDeaths)
        {
            Instance.currentSession.totalDeaths++;
        }
    }
    
    public static void TrackBossAttempt(string bossName)
    {
        if (Instance.currentSession != null && Instance.trackBossAttempts)
        {
            Instance.currentSession.bossAttempts++;
        }
    }
    
    public static void TrackBossDefeat(string bossName)
    {
        if (Instance.currentSession != null && Instance.trackBossAttempts)
        {
            Instance.currentSession.bossDefeats++;
        }
    }
    
    public static void TrackMoralChoice(string choiceType)
    {
        if (Instance.currentSession != null && Instance.trackMoralChoices)
        {
            Instance.currentSession.moralChoicesMade++;
        }
    }
    
    public static void TrackMagicUsage(string magicType)
    {
        if (Instance.currentSession != null && Instance.trackMagicUsage)
        {
            if (!Instance.currentSession.magicUsageCount.ContainsKey(magicType))
            {
                Instance.currentSession.magicUsageCount[magicType] = 0;
            }
            Instance.currentSession.magicUsageCount[magicType]++;
        }
    }
    
    public static void TrackQuestCompletion(string questId)
    {
        if (Instance.currentSession != null && Instance.trackQuestCompletion)
        {
            Instance.currentSession.questsCompleted++;
        }
    }
    
    public static void SetUserOptIn(bool optIn)
    {
        Instance.userOptedIn = optIn;
        PlayerPrefs.SetInt("Analytics_OptIn", optIn ? 1 : 0);
        PlayerPrefs.Save();
        
        if (optIn && Instance.currentSession == null)
        {
            Instance.StartNewSession();
        }
        else if (!optIn && Instance.currentSession != null)
        {
            Instance.EndCurrentSession();
        }
    }
    
    public static bool IsUserOptedIn()
    {
        return Instance.userOptedIn;
    }
}
