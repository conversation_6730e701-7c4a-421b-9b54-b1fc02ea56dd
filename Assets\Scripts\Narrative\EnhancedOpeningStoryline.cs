using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;

namespace CinderOfDarkness.Narrative
{
    /// <summary>
    /// Enhanced Opening Storyline for Cinder of Darkness.
    /// Provides a complete emotional journey with cinematic cutscenes, dialogue trees, and quest integration.
    /// </summary>
    public class EnhancedOpeningStoryline : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Opening Sequence Settings")]
        [SerializeField] private bool autoStartOnFirstPlay = true;
        [SerializeField] private float totalSequenceDuration = 600f; // 10 minutes
        [SerializeField] private bool allowSkipping = true;
        [SerializeField] private KeyCode skipKey = KeyCode.Escape;

        [Header("Cinematic Components")]
        [SerializeField] private PlayableDirector cinematicDirector;
        [SerializeField] private TimelineAsset[] openingTimelines;
        [SerializeField] private Camera cinematicCamera;
        [SerializeField] private Camera gameplayCamera;

        [Header("UI Components")]
        [SerializeField] private GameObject openingUI;
        [SerializeField] private TextMeshProUGUI narrativeText;
        [SerializeField] private TextMeshProUGUI choiceText;
        [SerializeField] private GameObject choicePanel;
        [SerializeField] private GameObject skipPrompt;

        [Header("Audio")]
        [SerializeField] private AudioSource narrativeAudioSource;
        [SerializeField] private AudioClip[] voiceLines;
        [SerializeField] private AudioClip[] ambientSounds;
        [SerializeField] private AudioClip[] emotionalMusic;

        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem memoryParticles;
        [SerializeField] private ParticleSystem emberEffect;
        [SerializeField] private Light dreamLight;
        [SerializeField] private Material[] dreamMaterials;
        #endregion

        #region Private Fields
        private OpeningPhase currentPhase = OpeningPhase.NotStarted;
        private int currentStoryBeat = 0;
        private bool isSequenceActive = false;
        private bool canSkip = false;
        private float sequenceStartTime;

        // Story state
        private EmotionalChoice playerEmotionalChoice;
        private BackstoryElement[] selectedBackstory;
        private string playerOriginStory;
        private float emotionalResonance = 0f;

        // Integration references
        private DynamicNarrativeSystem narrativeSystem;
        private AdvancedDialogueSystem dialogueSystem;
        private QuestSystem questSystem;
        private PlayerStats playerStats;
        #endregion

        #region Enums
        public enum OpeningPhase
        {
            NotStarted,
            MemoryFragments,
            ChildhoodFlashback,
            TragedyReveal,
            EmotionalChoice,
            PowerAwakening,
            FirstQuest,
            WorldIntroduction,
            Complete
        }

        public enum EmotionalChoice
        {
            None,
            Vengeance,      // "I will burn those who wronged me"
            Protection,     // "I will burn to protect the innocent"
            Understanding,  // "I will burn to understand my nature"
            Acceptance,     // "I will burn because it is my fate"
            Rebellion,      // "I will burn to defy destiny itself"
            Sacrifice       // "I will burn so others don't have to"
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeReferences();
        }

        private void Start()
        {
            if (autoStartOnFirstPlay && !HasPlayedOpening())
            {
                StartCoroutine(BeginOpeningSequence());
            }
        }

        private void Update()
        {
            HandleInput();
            UpdateSequenceProgress();
        }
        #endregion

        #region Initialization
        private void InitializeReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            dialogueSystem = AdvancedDialogueSystem.Instance;
            questSystem = FindObjectOfType<QuestSystem>();
            playerStats = FindObjectOfType<PlayerStats>();

            if (cinematicDirector == null)
                cinematicDirector = GetComponent<PlayableDirector>();
        }

        private bool HasPlayedOpening()
        {
            return PlayerPrefs.GetInt("EnhancedOpeningPlayed", 0) == 1;
        }
        #endregion

        #region Opening Sequence
        public IEnumerator BeginOpeningSequence()
        {
            isSequenceActive = true;
            sequenceStartTime = Time.time;
            currentPhase = OpeningPhase.MemoryFragments;

            // Disable player control
            DisablePlayerControl();

            // Setup cinematic environment
            SetupCinematicMode();

            // Phase 1: Memory Fragments
            yield return StartCoroutine(MemoryFragmentsPhase());

            // Phase 2: Childhood Flashback
            yield return StartCoroutine(ChildhoodFlashbackPhase());

            // Phase 3: Tragedy Reveal
            yield return StartCoroutine(TragedyRevealPhase());

            // Phase 4: Emotional Choice
            yield return StartCoroutine(EmotionalChoicePhase());

            // Phase 5: Power Awakening
            yield return StartCoroutine(PowerAwakeningPhase());

            // Phase 6: First Quest
            yield return StartCoroutine(FirstQuestPhase());

            // Phase 7: World Introduction
            yield return StartCoroutine(WorldIntroductionPhase());

            // Complete sequence
            CompleteOpeningSequence();
        }

        private IEnumerator MemoryFragmentsPhase()
        {
            currentPhase = OpeningPhase.MemoryFragments;

            // Play memory fragments timeline
            if (openingTimelines.Length > 0)
            {
                cinematicDirector.playableAsset = openingTimelines[0];
                cinematicDirector.Play();
            }

            // Show fragmented memories
            yield return StartCoroutine(ShowMemoryFragments());

            // Narrative text
            yield return StartCoroutine(DisplayNarrativeText(
                "Fragments of memory drift through the void...\n" +
                "A child's laughter... the warmth of a hearth... the scent of bread baking...\n" +
                "Then... darkness."
            ));

            yield return new WaitForSeconds(3f);
        }

        private IEnumerator ChildhoodFlashbackPhase()
        {
            currentPhase = OpeningPhase.ChildhoodFlashback;

            // Play childhood timeline
            if (openingTimelines.Length > 1)
            {
                cinematicDirector.playableAsset = openingTimelines[1];
                cinematicDirector.Play();
            }

            // Show childhood scenes
            yield return StartCoroutine(ShowChildhoodScenes());

            // Interactive backstory selection
            yield return StartCoroutine(SelectBackstoryElements());

            yield return new WaitForSeconds(2f);
        }

        private IEnumerator TragedyRevealPhase()
        {
            currentPhase = OpeningPhase.TragedyReveal;

            // Play tragedy timeline
            if (openingTimelines.Length > 2)
            {
                cinematicDirector.playableAsset = openingTimelines[2];
                cinematicDirector.Play();
            }

            // Reveal the tragedy that created The Cinderborn
            yield return StartCoroutine(RevealTragedy());

            // Show the moment of transformation
            yield return StartCoroutine(ShowTransformation());

            yield return new WaitForSeconds(4f);
        }

        private IEnumerator EmotionalChoicePhase()
        {
            currentPhase = OpeningPhase.EmotionalChoice;

            // Present the core emotional choice
            yield return StartCoroutine(PresentEmotionalChoice());

            // Wait for player choice
            yield return StartCoroutine(WaitForEmotionalChoice());

            // Apply choice consequences
            ApplyEmotionalChoice();

            yield return new WaitForSeconds(2f);
        }

        private IEnumerator PowerAwakeningPhase()
        {
            currentPhase = OpeningPhase.PowerAwakening;

            // Show power awakening based on emotional choice
            yield return StartCoroutine(ShowPowerAwakening());

            // Teach basic controls
            yield return StartCoroutine(TeachBasicControls());

            yield return new WaitForSeconds(3f);
        }

        private IEnumerator FirstQuestPhase()
        {
            currentPhase = OpeningPhase.FirstQuest;

            // Give the first quest
            yield return StartCoroutine(GiveFirstQuest());

            // Show quest objectives
            yield return StartCoroutine(ShowQuestObjectives());

            yield return new WaitForSeconds(2f);
        }

        private IEnumerator WorldIntroductionPhase()
        {
            currentPhase = OpeningPhase.WorldIntroduction;

            // Introduce the world and its kingdoms
            yield return StartCoroutine(IntroduceWorld());

            // Show the player's place in this world
            yield return StartCoroutine(ShowPlayerRole());

            yield return new WaitForSeconds(3f);
        }
        #endregion

        #region Phase Implementations
        private IEnumerator ShowMemoryFragments()
        {
            if (memoryParticles != null)
            {
                memoryParticles.Play();
            }

            // Show fragmented visual memories
            string[] memoryTexts = {
                "A warm hand on your forehead...",
                "The sound of your mother's voice...",
                "The smell of your father's workshop...",
                "Playing with other children...",
                "The taste of honey cakes...",
                "Then... the fire came."
            };

            foreach (string memory in memoryTexts)
            {
                yield return StartCoroutine(DisplayNarrativeText(memory, 2f));
                yield return new WaitForSeconds(1f);
            }
        }

        private IEnumerator ShowChildhoodScenes()
        {
            // Show specific childhood scenes based on player choices
            yield return StartCoroutine(DisplayNarrativeText(
                "You remember your childhood in the village of Ashenheart...\n" +
                "A place where the hearth never grew cold, and stories were told by firelight."
            ));

            yield return new WaitForSeconds(3f);
        }

        private IEnumerator SelectBackstoryElements()
        {
            // Present backstory choices
            BackstoryChoice[] choices = {
                new BackstoryChoice {
                    choiceText = "You were the blacksmith's child, learning the forge",
                    backstoryElement = BackstoryElement.SmithChild,
                    emotionalWeight = 10f
                },
                new BackstoryChoice {
                    choiceText = "You were the healer's apprentice, tending wounds",
                    backstoryElement = BackstoryElement.HealerApprentice,
                    emotionalWeight = 15f
                },
                new BackstoryChoice {
                    choiceText = "You were the storyteller's grandchild, keeper of tales",
                    backstoryElement = BackstoryElement.StorytellerGrandchild,
                    emotionalWeight = 20f
                }
            };

            yield return StartCoroutine(PresentBackstoryChoices(choices));
        }

        private IEnumerator RevealTragedy()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Then came the night when everything changed...\n" +
                "The raiders arrived without warning, bringing death and flame.\n" +
                "Your village burned... your family... everyone you loved..."
            ));

            // Play tragic music
            if (emotionalMusic.Length > 0 && narrativeAudioSource != null)
            {
                narrativeAudioSource.clip = emotionalMusic[0];
                narrativeAudioSource.Play();
            }

            yield return new WaitForSeconds(5f);
        }

        private IEnumerator ShowTransformation()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "In that moment of ultimate loss, something awakened within you...\n" +
                "The fire that consumed your world became part of you.\n" +
                "You became... The Cinderborn."
            ));

            // Visual transformation effects
            if (emberEffect != null)
            {
                emberEffect.Play();
            }

            yield return new WaitForSeconds(4f);
        }

        private IEnumerator PresentEmotionalChoice()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Now, as you stand in the ashes of your past,\n" +
                "what drives the fire within you?"
            ));

            // Show choice panel
            if (choicePanel != null)
            {
                choicePanel.SetActive(true);
            }

            // Present emotional choices
            EmotionalChoiceData[] choices = {
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Vengeance,
                    text = "Vengeance - I will burn those who wronged me",
                    description = "Let rage fuel your flames"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Protection,
                    text = "Protection - I will burn to protect the innocent",
                    description = "Let compassion guide your fire"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Understanding,
                    text = "Understanding - I will burn to understand my nature",
                    description = "Let wisdom illuminate your path"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Acceptance,
                    text = "Acceptance - I will burn because it is my fate",
                    description = "Let destiny shape your journey"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Rebellion,
                    text = "Rebellion - I will burn to defy destiny itself",
                    description = "Let defiance forge your will"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Sacrifice,
                    text = "Sacrifice - I will burn so others don't have to",
                    description = "Let selflessness define your purpose"
                }
            };

            yield return StartCoroutine(PresentEmotionalChoices(choices));
        }

        private IEnumerator WaitForEmotionalChoice()
        {
            // Wait for player to make emotional choice
            while (playerEmotionalChoice == EmotionalChoice.None)
            {
                yield return null;
            }

            // Hide choice panel
            if (choicePanel != null)
            {
                choicePanel.SetActive(false);
            }
        }

        private IEnumerator ShowPowerAwakening()
        {
            string awakeningText = GetPowerAwakeningText(playerEmotionalChoice);
            yield return StartCoroutine(DisplayNarrativeText(awakeningText));

            // Visual effects based on choice
            ApplyChoiceVisualEffects(playerEmotionalChoice);

            yield return new WaitForSeconds(3f);
        }

        private IEnumerator TeachBasicControls()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Feel the power flowing through you...\n" +
                "Use WASD to move, Mouse to look around.\n" +
                "Left click to channel your fire, Right click for focused flame."
            ));

            // Enable basic player control for tutorial
            EnableBasicPlayerControl();

            yield return new WaitForSeconds(5f);
        }

        private IEnumerator GiveFirstQuest()
        {
            string questText = GetFirstQuestText(playerEmotionalChoice);
            yield return StartCoroutine(DisplayNarrativeText(questText));

            // Create and assign first quest
            if (questSystem != null)
            {
                CreateFirstQuest();
            }

            yield return new WaitForSeconds(3f);
        }

        private IEnumerator ShowQuestObjectives()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Your journey begins now, Cinderborn.\n" +
                "Press J to open your Quest Log and track your objectives."
            ));

            yield return new WaitForSeconds(2f);
        }

        private IEnumerator IntroduceWorld()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "The world before you is vast and divided...\n" +
                "Kingdoms of Light and Shadow, villages with their own beliefs,\n" +
                "and ancient powers that stir in the darkness."
            ));

            yield return new WaitForSeconds(4f);
        }

        private IEnumerator ShowPlayerRole()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "You are The Cinderborn - neither fully human nor spirit.\n" +
                "Your choices will shape not just your destiny,\n" +
                "but the fate of all who dwell in this realm."
            ));

            yield return new WaitForSeconds(4f);
        }
        #endregion

        #region Utility Methods
        private IEnumerator DisplayNarrativeText(string text, float duration = 3f)
        {
            if (narrativeText != null)
            {
                narrativeText.text = "";

                // Typewriter effect
                for (int i = 0; i <= text.Length; i++)
                {
                    narrativeText.text = text.Substring(0, i);
                    yield return new WaitForSeconds(0.03f);
                }

                yield return new WaitForSeconds(duration);
            }
        }

        private void SetupCinematicMode()
        {
            if (cinematicCamera != null)
                cinematicCamera.enabled = true;

            if (gameplayCamera != null)
                gameplayCamera.enabled = false;

            if (openingUI != null)
                openingUI.SetActive(true);
        }

        private void DisablePlayerControl()
        {
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = false;
        }

        private void EnableBasicPlayerControl()
        {
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = true;
        }

        private void HandleInput()
        {
            if (isSequenceActive && canSkip && Input.GetKeyDown(skipKey))
            {
                SkipOpeningSequence();
            }
        }

        private void UpdateSequenceProgress()
        {
            if (isSequenceActive)
            {
                float elapsed = Time.time - sequenceStartTime;
                if (elapsed > 5f) // Allow skipping after 5 seconds
                {
                    canSkip = true;
                    if (skipPrompt != null)
                        skipPrompt.SetActive(true);
                }
            }
        }

        private void SkipOpeningSequence()
        {
            StopAllCoroutines();
            CompleteOpeningSequence();
        }

        private void CompleteOpeningSequence()
        {
            isSequenceActive = false;
            currentPhase = OpeningPhase.Complete;

            // Mark as played
            PlayerPrefs.SetInt("EnhancedOpeningPlayed", 1);
            PlayerPrefs.Save();

            // Return to gameplay mode
            if (cinematicCamera != null)
                cinematicCamera.enabled = false;

            if (gameplayCamera != null)
                gameplayCamera.enabled = true;

            if (openingUI != null)
                openingUI.SetActive(false);

            EnableBasicPlayerControl();

            Debug.Log($"Enhanced opening sequence completed with emotional choice: {playerEmotionalChoice}");
        }
        #endregion

        #region Choice Handling
        public void SelectEmotionalChoice(int choiceIndex)
        {
            if (choiceIndex >= 0 && choiceIndex < 6)
            {
                playerEmotionalChoice = (EmotionalChoice)(choiceIndex + 1);
                emotionalResonance = CalculateEmotionalResonance(playerEmotionalChoice);
            }
        }

        private void ApplyEmotionalChoice()
        {
            if (playerStats != null)
            {
                // Apply choice effects to player stats and narrative system
                switch (playerEmotionalChoice)
                {
                    case EmotionalChoice.Vengeance:
                        playerStats.ModifyAlignment(-20f, 0f); // More shadow-aligned
                        break;
                    case EmotionalChoice.Protection:
                        playerStats.ModifyAlignment(20f, 0f); // More light-aligned
                        break;
                    case EmotionalChoice.Understanding:
                        playerStats.ModifyAlignment(0f, 20f); // More moon-aligned
                        break;
                    // Add more choice effects
                }
            }

            if (narrativeSystem != null)
            {
                narrativeSystem.SetStoryFlag($"opening_choice_{playerEmotionalChoice}", true);
                narrativeSystem.SetStoryFlag("opening_completed", true);
            }
        }

        private string GetPowerAwakeningText(EmotionalChoice choice)
        {
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return "Your flames burn with righteous fury, seeking to consume those who have wronged you.";
                case EmotionalChoice.Protection:
                    return "Your fire becomes a shield, warm and protective, ready to guard the innocent.";
                case EmotionalChoice.Understanding:
                    return "Your flames flicker with curiosity, seeking to illuminate the mysteries of your existence.";
                case EmotionalChoice.Acceptance:
                    return "Your fire burns steady and calm, accepting the burden of your destiny.";
                case EmotionalChoice.Rebellion:
                    return "Your flames rage against the very concept of fate, burning to forge your own path.";
                case EmotionalChoice.Sacrifice:
                    return "Your fire burns with selfless purpose, ready to consume itself for others' sake.";
                default:
                    return "Your flames awaken, ready to shape the world according to your will.";
            }
        }

        private string GetFirstQuestText(EmotionalChoice choice)
        {
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return "Seek out the raiders who destroyed your village. Begin with the nearest settlement - someone there will know of their whereabouts.";
                case EmotionalChoice.Protection:
                    return "There are others in need of protection. Travel to the Village of Forgotten Names - they face a threat you can help them overcome.";
                case EmotionalChoice.Understanding:
                    return "Seek the Sage of Whispering Stones. They may have answers about your transformation and what it means.";
                default:
                    return "Your path begins in the Village of Forgotten Names. There, you will find your first test as The Cinderborn.";
            }
        }

        private void CreateFirstQuest()
        {
            // Create appropriate first quest based on emotional choice
            if (questSystem == null)
            {
                questSystem = FindObjectOfType<QuestSystem>();
                if (questSystem == null)
                {
                    Debug.LogWarning("Quest System not found - creating placeholder quest");
                    return;
                }
            }

            Quest firstQuest = CreateQuestBasedOnChoice(playerEmotionalChoice);
            if (firstQuest != null)
            {
                questSystem.AddQuest(firstQuest);
                questSystem.SetActiveQuest(firstQuest.questId);

                // Set narrative flags for quest tracking
                if (narrativeSystem != null)
                {
                    narrativeSystem.SetStoryFlag($"first_quest_{playerEmotionalChoice}", true);
                    narrativeSystem.SetStoryFlag("tutorial_quest_active", true);
                }

                Debug.Log($"Created first quest: {firstQuest.questName} based on choice: {playerEmotionalChoice}");
            }
        }

        private Quest CreateQuestBasedOnChoice(EmotionalChoice choice)
        {
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return CreateVengeanceQuest();
                case EmotionalChoice.Protection:
                    return CreateProtectionQuest();
                case EmotionalChoice.Understanding:
                    return CreateUnderstandingQuest();
                case EmotionalChoice.Acceptance:
                    return CreateAcceptanceQuest();
                case EmotionalChoice.Rebellion:
                    return CreateRebellionQuest();
                case EmotionalChoice.Sacrifice:
                    return CreateSacrificeQuest();
                default:
                    return CreateDefaultQuest();
            }
        }

        private Quest CreateVengeanceQuest()
        {
            return new Quest
            {
                questId = "opening_vengeance_path",
                questName = "Trail of Ashes",
                questDescription = "The raiders who destroyed your village left a trail of destruction. Follow their path and discover their whereabouts. Justice demands retribution.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "find_raider_clues",
                        description = "Search the ruins of Ashenheart for clues about the raiders",
                        objectiveType = QuestObjective.ObjectiveType.Investigate,
                        targetLocation = "Ashenheart Ruins",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "talk_to_survivors",
                        description = "Speak with any survivors who might have seen the raiders",
                        objectiveType = QuestObjective.ObjectiveType.TalkTo,
                        targetNPC = "Village Survivor",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "track_raider_trail",
                        description = "Follow the raiders' trail to their next target",
                        objectiveType = QuestObjective.ObjectiveType.Travel,
                        targetLocation = "Raider Trail",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 100f,
                        description = "Experience gained from pursuing justice"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "vengeful_ember",
                        description = "A burning ember that grows stronger with righteous anger"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Alignment,
                        alignmentShift = -10f, // More shadow-aligned
                        description = "Your pursuit of vengeance darkens your soul"
                    }
                },

                questGiver = "The Cinderborn's Memory",
                questLocation = "Ashenheart Ruins",
                estimatedDuration = 30f, // 30 minutes
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("vengeance_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("vengeance_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("vengeance_quest_completed");
                    }
                }
            };
        }

        private Quest CreateProtectionQuest()
        {
            return new Quest
            {
                questId = "opening_protection_path",
                questName = "Guardian's First Light",
                questDescription = "The Village of Forgotten Names faces a mysterious threat. Your protective flames may be their salvation. Help them in their hour of need.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "reach_forgotten_names",
                        description = "Travel to the Village of Forgotten Names",
                        objectiveType = QuestObjective.ObjectiveType.Travel,
                        targetLocation = "Village of Forgotten Names",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "speak_with_name_keeper",
                        description = "Speak with the Name Keeper about the village's troubles",
                        objectiveType = QuestObjective.ObjectiveType.TalkTo,
                        targetNPC = "Name Keeper",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "investigate_threat",
                        description = "Investigate the source of the mysterious threat",
                        objectiveType = QuestObjective.ObjectiveType.Investigate,
                        targetLocation = "Village Outskirts",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "protect_villagers",
                        description = "Use your protective flames to shield the villagers",
                        objectiveType = QuestObjective.ObjectiveType.Defend,
                        targetLocation = "Village Center",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 100f,
                        description = "Experience gained from protecting the innocent"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "protective_ward",
                        description = "A warm ember that shields others from harm"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Alignment,
                        alignmentShift = 10f, // More light-aligned
                        description = "Your protective nature brightens your soul"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Reputation,
                        factionId = "Village of Forgotten Names",
                        amount = 25f,
                        description = "The villagers remember your kindness"
                    }
                },

                questGiver = "The Cinderborn's Compassion",
                questLocation = "Village of Forgotten Names",
                estimatedDuration = 25f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("protection_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("protection_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("protection_quest_completed");
                    }
                }
            };
        }

        private Quest CreateUnderstandingQuest()
        {
            return new Quest
            {
                questId = "opening_understanding_path",
                questName = "Seeking the Sage",
                questDescription = "The Sage of Whispering Stones possesses ancient knowledge about the Cinderborn. Seek them out to understand your true nature and purpose.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "find_whispering_stones",
                        description = "Locate the Kingdom of Whispering Stones",
                        objectiveType = QuestObjective.ObjectiveType.Travel,
                        targetLocation = "Kingdom of Whispering Stones",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "gain_stone_listener_trust",
                        description = "Prove yourself worthy to the Stone Listeners",
                        objectiveType = QuestObjective.ObjectiveType.Prove,
                        targetNPC = "Stone Listener",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "meditate_with_stones",
                        description = "Meditate with the ancient stones to hear their whispers",
                        objectiveType = QuestObjective.ObjectiveType.Interact,
                        targetLocation = "Great Listening Chamber",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "meet_the_sage",
                        description = "Seek audience with the Sage of Whispering Stones",
                        objectiveType = QuestObjective.ObjectiveType.TalkTo,
                        targetNPC = "Sage of Whispering Stones",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 120f,
                        description = "Wisdom gained from ancient knowledge"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "stone_whisper_crystal",
                        description = "A crystal that resonates with ancient wisdom"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Ability,
                        abilityId = "stone_communion",
                        description = "The ability to hear the whispers of ancient stones"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Knowledge,
                        knowledgeId = "cinderborn_origins",
                        description = "Understanding of your true nature as the Cinderborn"
                    }
                },

                questGiver = "The Cinderborn's Curiosity",
                questLocation = "Kingdom of Whispering Stones",
                estimatedDuration = 40f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("understanding_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("understanding_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.SetStoryFlag("cinderborn_origins_learned", true);
                        narrativeSystem.TriggerNarrativeEvent("understanding_quest_completed");
                    }
                }
            };
        }

        private Quest CreateAcceptanceQuest()
        {
            return new Quest
            {
                questId = "opening_acceptance_path",
                questName = "Embracing Destiny",
                questDescription = "Accept your role as the Cinderborn and seek to understand what fate has planned for you. The path of acceptance leads to inner peace.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "meditate_on_transformation",
                        description = "Meditate on your transformation and accept your new nature",
                        objectiveType = QuestObjective.ObjectiveType.Meditate,
                        targetLocation = "Ashenheart Memorial",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "visit_crystal_tears",
                        description = "Visit the Kingdom of Crystal Tears to learn about acceptance of sorrow",
                        objectiveType = QuestObjective.ObjectiveType.Travel,
                        targetLocation = "Kingdom of Crystal Tears",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "speak_with_tear_keeper",
                        description = "Speak with the Tear Keeper about accepting loss and transformation",
                        objectiveType = QuestObjective.ObjectiveType.TalkTo,
                        targetNPC = "Tear Keeper",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 110f,
                        description = "Wisdom gained from accepting your destiny"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "serene_ember",
                        description = "A calm ember that brings inner peace"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Ability,
                        abilityId = "inner_peace",
                        description = "The ability to remain calm in chaotic situations"
                    }
                },

                questGiver = "The Cinderborn's Wisdom",
                questLocation = "Kingdom of Crystal Tears",
                estimatedDuration = 35f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("acceptance_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("acceptance_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("acceptance_quest_completed");
                    }
                }
            };
        }

        private Quest CreateRebellionQuest()
        {
            return new Quest
            {
                questId = "opening_rebellion_path",
                questName = "Defying the Flames",
                questDescription = "Reject the destiny thrust upon you and forge your own path. Show the world that the Cinderborn bows to no fate.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "reject_cinderborn_title",
                        description = "Publicly reject the title of Cinderborn in a settlement",
                        objectiveType = QuestObjective.ObjectiveType.Declare,
                        targetLocation = "Any Settlement",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "challenge_authority",
                        description = "Challenge a figure of authority who tries to define your role",
                        objectiveType = QuestObjective.ObjectiveType.Confront,
                        targetNPC = "Authority Figure",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "forge_own_path",
                        description = "Create your own quest objective that defies expectations",
                        objectiveType = QuestObjective.ObjectiveType.Create,
                        targetLocation = "Anywhere",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 130f,
                        description = "Strength gained from defying destiny"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "rebellious_flame",
                        description = "A wild flame that refuses to be controlled"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Ability,
                        abilityId = "destiny_defiance",
                        description = "Resistance to fate-based magic and prophecies"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Reputation,
                        factionId = "Free Spirits",
                        amount = 30f,
                        description = "Respect from those who value independence"
                    }
                },

                questGiver = "The Cinderborn's Defiance",
                questLocation = "Independent Territory",
                estimatedDuration = 45f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("rebellion_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("rebellion_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("rebellion_quest_completed");
                    }
                }
            };
        }

        private Quest CreateSacrificeQuest()
        {
            return new Quest
            {
                questId = "opening_sacrifice_path",
                questName = "The Selfless Flame",
                questDescription = "Use your power to help others, even at great personal cost. Show that the Cinderborn's flame burns brightest when it lights the way for others.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "help_struggling_village",
                        description = "Find a village in need and offer your assistance",
                        objectiveType = QuestObjective.ObjectiveType.Help,
                        targetLocation = "Village in Need",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "sacrifice_for_others",
                        description = "Make a personal sacrifice to help the villagers",
                        objectiveType = QuestObjective.ObjectiveType.Sacrifice,
                        targetLocation = "Village Center",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "teach_others",
                        description = "Teach others how to help themselves without relying on your power",
                        objectiveType = QuestObjective.ObjectiveType.Teach,
                        targetNPC = "Village Leaders",
                        isCompleted = false,
                        isOptional = false
                    }
                },

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 150f,
                        description = "Fulfillment gained from selfless service"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "selfless_light",
                        description = "A gentle light that heals others at your expense"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Ability,
                        abilityId = "sacrificial_healing",
                        description = "The ability to heal others by transferring their wounds to yourself"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Alignment,
                        alignmentShift = 20f, // Strongly light-aligned
                        description = "Your selfless nature purifies your soul"
                    }
                },

                questGiver = "The Cinderborn's Compassion",
                questLocation = "Village in Need",
                estimatedDuration = 50f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("sacrifice_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("sacrifice_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("sacrifice_quest_completed");
                    }
                }
            };
        }

        private Quest CreateDefaultQuest()
        {
            return new Quest
            {
                questId = "opening_default_path",
                questName = "The Cinderborn's Journey Begins",
                questDescription = "Your path is unclear, but your journey must begin somewhere. Explore the world and discover what it means to be the Cinderborn.",
                questType = Quest.QuestType.Main,
                isActive = true,

                objectives = new QuestObjective[]
                {
                    new QuestObjective
                    {
                        objectiveId = "explore_first_settlement",
                        description = "Visit your first settlement as the Cinderborn",
                        objectiveType = QuestObjective.ObjectiveType.Travel,
                        targetLocation = "Village of Forgotten Names",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "learn_about_world",
                        description = "Speak with locals to learn about the current state of the world",
                        objectiveType = QuestObjective.ObjectiveType.Learn,
                        targetNPC = "Local Residents",
                        isCompleted = false,
                        isOptional = false
                    },
                    new QuestObjective
                    {
                        objectiveId = "make_first_choice",
                        description = "Make a significant choice that will define your path forward",
                        objectiveType = QuestObjective.ObjectiveType.Choose,
                        targetLocation = "Village of Forgotten Names",
                        isCompleted = false,
                        isOptional = false
                    }
                ],

                rewards = new QuestReward[]
                {
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Experience,
                        amount = 100f,
                        description = "Experience gained from beginning your journey"
                    },
                    new QuestReward
                    {
                        rewardType = QuestReward.RewardType.Item,
                        itemId = "travelers_ember",
                        description = "A steady ember that lights the way forward"
                    }
                },

                questGiver = "The Cinderborn's Destiny",
                questLocation = "Village of Forgotten Names",
                estimatedDuration = 20f,
                difficultyLevel = Quest.DifficultyLevel.Tutorial,

                onQuestStart = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("default_path_chosen", true);
                        narrativeSystem.TriggerNarrativeEvent("default_quest_started");
                    }
                },

                onQuestComplete = () => {
                    if (narrativeSystem != null)
                    {
                        narrativeSystem.SetStoryFlag("first_quest_completed", true);
                        narrativeSystem.TriggerNarrativeEvent("default_quest_completed");
                    }
                }
            };
        }

        private float CalculateEmotionalResonance(EmotionalChoice choice)
        {
            // Calculate emotional resonance value based on choice intensity
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return 1.5f; // High intensity
                case EmotionalChoice.Protection:
                    return 1.2f; // Moderate-high intensity
                case EmotionalChoice.Understanding:
                    return 1.0f; // Balanced intensity
                case EmotionalChoice.Acceptance:
                    return 0.8f; // Calm intensity
                case EmotionalChoice.Rebellion:
                    return 1.8f; // Very high intensity
                case EmotionalChoice.Sacrifice:
                    return 1.3f; // High intensity
                default:
                    return 1.0f; // Default intensity
            }
        }

        private void ApplyChoiceVisualEffects(EmotionalChoice choice)
        {
            // Apply visual effects based on emotional choice
            if (dreamLight != null)
            {
                switch (choice)
                {
                    case EmotionalChoice.Vengeance:
                        dreamLight.color = Color.red;
                        break;
                    case EmotionalChoice.Protection:
                        dreamLight.color = Color.white;
                        break;
                    case EmotionalChoice.Understanding:
                        dreamLight.color = Color.blue;
                        break;
                    // Add more visual effects
                }
            }
        }
        #endregion

        #region Data Structures
        [System.Serializable]
        public class BackstoryChoice
        {
            public string choiceText;
            public BackstoryElement backstoryElement;
            public float emotionalWeight;
        }

        [System.Serializable]
        public class EmotionalChoiceData
        {
            public EmotionalChoice choice;
            public string text;
            public string description;
        }

        public enum BackstoryElement
        {
            SmithChild,
            HealerApprentice,
            StorytellerGrandchild,
            MerchantChild,
            GuardChild,
            FarmerChild
        }

        private IEnumerator PresentBackstoryChoices(BackstoryChoice[] choices)
        {
            // Implementation for presenting backstory choices
            yield return new WaitForSeconds(1f);
        }

        private IEnumerator PresentEmotionalChoices(EmotionalChoiceData[] choices)
        {
            // Implementation for presenting emotional choices
            yield return new WaitForSeconds(1f);
        }
        #endregion
    }

    #region Quest System Data Structures
    [System.Serializable]
    public class Quest
    {
        [Header("Basic Information")]
        public string questId;
        public string questName;
        [TextArea(3, 5)]
        public string questDescription;
        public QuestType questType;
        public bool isActive;

        [Header("Objectives")]
        public QuestObjective[] objectives;

        [Header("Rewards")]
        public QuestReward[] rewards;

        [Header("Quest Details")]
        public string questGiver;
        public string questLocation;
        public float estimatedDuration; // in minutes
        public DifficultyLevel difficultyLevel;

        [Header("Events")]
        public System.Action onQuestStart;
        public System.Action onQuestComplete;
        public System.Action onQuestFailed;

        public enum QuestType
        {
            Main,
            Side,
            Tutorial,
            Hidden,
            Repeatable,
            Daily,
            Weekly
        }

        public enum DifficultyLevel
        {
            Tutorial,
            Easy,
            Normal,
            Hard,
            Expert,
            Legendary
        }

        public bool IsCompleted()
        {
            foreach (var objective in objectives)
            {
                if (!objective.isOptional && !objective.isCompleted)
                    return false;
            }
            return true;
        }

        public float GetCompletionPercentage()
        {
            if (objectives.Length == 0) return 0f;

            int completedCount = 0;
            foreach (var objective in objectives)
            {
                if (objective.isCompleted)
                    completedCount++;
            }

            return (float)completedCount / objectives.Length * 100f;
        }
    }

    [System.Serializable]
    public class QuestObjective
    {
        [Header("Objective Information")]
        public string objectiveId;
        [TextArea(2, 3)]
        public string description;
        public ObjectiveType objectiveType;

        [Header("Target Information")]
        public string targetLocation;
        public string targetNPC;
        public string targetItem;
        public int targetAmount = 1;

        [Header("Status")]
        public bool isCompleted;
        public bool isOptional;
        public int currentProgress;

        public enum ObjectiveType
        {
            Travel,
            TalkTo,
            Investigate,
            Collect,
            Defeat,
            Defend,
            Escort,
            Deliver,
            Craft,
            Use,
            Interact,
            Learn,
            Teach,
            Help,
            Sacrifice,
            Prove,
            Declare,
            Confront,
            Create,
            Choose,
            Meditate,
            Explore,
            Survive,
            Solve,
            Discover
        }

        public void UpdateProgress(int amount = 1)
        {
            currentProgress += amount;
            if (currentProgress >= targetAmount)
            {
                isCompleted = true;
            }
        }

        public float GetProgressPercentage()
        {
            if (targetAmount <= 0) return isCompleted ? 100f : 0f;
            return Mathf.Clamp01((float)currentProgress / targetAmount) * 100f;
        }
    }

    [System.Serializable]
    public class QuestReward
    {
        [Header("Reward Information")]
        public RewardType rewardType;
        public float amount;
        [TextArea(1, 2)]
        public string description;

        [Header("Specific Reward Data")]
        public string itemId;
        public string abilityId;
        public string knowledgeId;
        public string factionId;
        public float alignmentShift;

        public enum RewardType
        {
            Experience,
            Gold,
            Item,
            Ability,
            Knowledge,
            Reputation,
            Alignment,
            Skill,
            Attribute,
            Title,
            Access,
            Unlock
        }

        public void ApplyReward(PlayerStats playerStats = null)
        {
            switch (rewardType)
            {
                case RewardType.Experience:
                    if (playerStats != null)
                        playerStats.AddExperience(amount);
                    break;

                case RewardType.Gold:
                    // Apply gold reward
                    break;

                case RewardType.Item:
                    // Give item to player
                    break;

                case RewardType.Ability:
                    // Unlock ability
                    break;

                case RewardType.Knowledge:
                    // Add knowledge to player
                    break;

                case RewardType.Reputation:
                    // Modify faction reputation
                    break;

                case RewardType.Alignment:
                    if (playerStats != null)
                        playerStats.ModifyAlignment(alignmentShift, 0f);
                    break;

                // Add more reward applications
            }
        }
    }

    // Placeholder QuestSystem class - would be implemented separately
    public class QuestSystem : MonoBehaviour
    {
        private List<Quest> activeQuests = new List<Quest>();
        private List<Quest> completedQuests = new List<Quest>();
        private Quest currentActiveQuest;

        public void AddQuest(Quest quest)
        {
            if (!activeQuests.Contains(quest))
            {
                activeQuests.Add(quest);
                quest.onQuestStart?.Invoke();
                Debug.Log($"Quest added: {quest.questName}");
            }
        }

        public void SetActiveQuest(string questId)
        {
            var quest = activeQuests.Find(q => q.questId == questId);
            if (quest != null)
            {
                currentActiveQuest = quest;
                Debug.Log($"Active quest set: {quest.questName}");
            }
        }

        public void CompleteQuest(string questId)
        {
            var quest = activeQuests.Find(q => q.questId == questId);
            if (quest != null)
            {
                activeQuests.Remove(quest);
                completedQuests.Add(quest);
                quest.onQuestComplete?.Invoke();

                // Apply rewards
                foreach (var reward in quest.rewards)
                {
                    reward.ApplyReward();
                }

                Debug.Log($"Quest completed: {quest.questName}");
            }
        }

        public Quest GetActiveQuest()
        {
            return currentActiveQuest;
        }

        public List<Quest> GetActiveQuests()
        {
            return new List<Quest>(activeQuests);
        }

        public bool IsQuestCompleted(string questId)
        {
            return completedQuests.Any(q => q.questId == questId);
        }

        public void UpdateObjectiveProgress(string questId, string objectiveId, int amount = 1)
        {
            var quest = activeQuests.Find(q => q.questId == questId);
            if (quest != null)
            {
                var objective = quest.objectives.FirstOrDefault(o => o.objectiveId == objectiveId);
                if (objective != null)
                {
                    objective.UpdateProgress(amount);

                    if (quest.IsCompleted())
                    {
                        CompleteQuest(questId);
                    }
                }
            }
        }
    }
    #endregion
}
