using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System.Collections;
using System.Collections.Generic;
using TMPro;

namespace CinderOfDarkness.Narrative
{
    /// <summary>
    /// Enhanced Opening Storyline for Cinder of Darkness.
    /// Provides a complete emotional journey with cinematic cutscenes, dialogue trees, and quest integration.
    /// </summary>
    public class EnhancedOpeningStoryline : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Opening Sequence Settings")]
        [SerializeField] private bool autoStartOnFirstPlay = true;
        [SerializeField] private float totalSequenceDuration = 600f; // 10 minutes
        [SerializeField] private bool allowSkipping = true;
        [SerializeField] private KeyCode skipKey = KeyCode.Escape;

        [Header("Cinematic Components")]
        [SerializeField] private PlayableDirector cinematicDirector;
        [SerializeField] private TimelineAsset[] openingTimelines;
        [SerializeField] private Camera cinematicCamera;
        [SerializeField] private Camera gameplayCamera;

        [Header("UI Components")]
        [SerializeField] private GameObject openingUI;
        [SerializeField] private TextMeshProUGUI narrativeText;
        [SerializeField] private TextMeshProUGUI choiceText;
        [SerializeField] private GameObject choicePanel;
        [SerializeField] private GameObject skipPrompt;

        [Header("Audio")]
        [SerializeField] private AudioSource narrativeAudioSource;
        [SerializeField] private AudioClip[] voiceLines;
        [SerializeField] private AudioClip[] ambientSounds;
        [SerializeField] private AudioClip[] emotionalMusic;

        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem memoryParticles;
        [SerializeField] private ParticleSystem emberEffect;
        [SerializeField] private Light dreamLight;
        [SerializeField] private Material[] dreamMaterials;
        #endregion

        #region Private Fields
        private OpeningPhase currentPhase = OpeningPhase.NotStarted;
        private int currentStoryBeat = 0;
        private bool isSequenceActive = false;
        private bool canSkip = false;
        private float sequenceStartTime;
        
        // Story state
        private EmotionalChoice playerEmotionalChoice;
        private BackstoryElement[] selectedBackstory;
        private string playerOriginStory;
        private float emotionalResonance = 0f;
        
        // Integration references
        private DynamicNarrativeSystem narrativeSystem;
        private AdvancedDialogueSystem dialogueSystem;
        private QuestSystem questSystem;
        private PlayerStats playerStats;
        #endregion

        #region Enums
        public enum OpeningPhase
        {
            NotStarted,
            MemoryFragments,
            ChildhoodFlashback,
            TragedyReveal,
            EmotionalChoice,
            PowerAwakening,
            FirstQuest,
            WorldIntroduction,
            Complete
        }

        public enum EmotionalChoice
        {
            None,
            Vengeance,      // "I will burn those who wronged me"
            Protection,     // "I will burn to protect the innocent"
            Understanding,  // "I will burn to understand my nature"
            Acceptance,     // "I will burn because it is my fate"
            Rebellion,      // "I will burn to defy destiny itself"
            Sacrifice       // "I will burn so others don't have to"
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeReferences();
        }

        private void Start()
        {
            if (autoStartOnFirstPlay && !HasPlayedOpening())
            {
                StartCoroutine(BeginOpeningSequence());
            }
        }

        private void Update()
        {
            HandleInput();
            UpdateSequenceProgress();
        }
        #endregion

        #region Initialization
        private void InitializeReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            dialogueSystem = AdvancedDialogueSystem.Instance;
            questSystem = FindObjectOfType<QuestSystem>();
            playerStats = FindObjectOfType<PlayerStats>();

            if (cinematicDirector == null)
                cinematicDirector = GetComponent<PlayableDirector>();
        }

        private bool HasPlayedOpening()
        {
            return PlayerPrefs.GetInt("EnhancedOpeningPlayed", 0) == 1;
        }
        #endregion

        #region Opening Sequence
        public IEnumerator BeginOpeningSequence()
        {
            isSequenceActive = true;
            sequenceStartTime = Time.time;
            currentPhase = OpeningPhase.MemoryFragments;
            
            // Disable player control
            DisablePlayerControl();
            
            // Setup cinematic environment
            SetupCinematicMode();
            
            // Phase 1: Memory Fragments
            yield return StartCoroutine(MemoryFragmentsPhase());
            
            // Phase 2: Childhood Flashback
            yield return StartCoroutine(ChildhoodFlashbackPhase());
            
            // Phase 3: Tragedy Reveal
            yield return StartCoroutine(TragedyRevealPhase());
            
            // Phase 4: Emotional Choice
            yield return StartCoroutine(EmotionalChoicePhase());
            
            // Phase 5: Power Awakening
            yield return StartCoroutine(PowerAwakeningPhase());
            
            // Phase 6: First Quest
            yield return StartCoroutine(FirstQuestPhase());
            
            // Phase 7: World Introduction
            yield return StartCoroutine(WorldIntroductionPhase());
            
            // Complete sequence
            CompleteOpeningSequence();
        }

        private IEnumerator MemoryFragmentsPhase()
        {
            currentPhase = OpeningPhase.MemoryFragments;
            
            // Play memory fragments timeline
            if (openingTimelines.Length > 0)
            {
                cinematicDirector.playableAsset = openingTimelines[0];
                cinematicDirector.Play();
            }
            
            // Show fragmented memories
            yield return StartCoroutine(ShowMemoryFragments());
            
            // Narrative text
            yield return StartCoroutine(DisplayNarrativeText(
                "Fragments of memory drift through the void...\n" +
                "A child's laughter... the warmth of a hearth... the scent of bread baking...\n" +
                "Then... darkness."
            ));
            
            yield return new WaitForSeconds(3f);
        }

        private IEnumerator ChildhoodFlashbackPhase()
        {
            currentPhase = OpeningPhase.ChildhoodFlashback;
            
            // Play childhood timeline
            if (openingTimelines.Length > 1)
            {
                cinematicDirector.playableAsset = openingTimelines[1];
                cinematicDirector.Play();
            }
            
            // Show childhood scenes
            yield return StartCoroutine(ShowChildhoodScenes());
            
            // Interactive backstory selection
            yield return StartCoroutine(SelectBackstoryElements());
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator TragedyRevealPhase()
        {
            currentPhase = OpeningPhase.TragedyReveal;
            
            // Play tragedy timeline
            if (openingTimelines.Length > 2)
            {
                cinematicDirector.playableAsset = openingTimelines[2];
                cinematicDirector.Play();
            }
            
            // Reveal the tragedy that created The Cinderborn
            yield return StartCoroutine(RevealTragedy());
            
            // Show the moment of transformation
            yield return StartCoroutine(ShowTransformation());
            
            yield return new WaitForSeconds(4f);
        }

        private IEnumerator EmotionalChoicePhase()
        {
            currentPhase = OpeningPhase.EmotionalChoice;
            
            // Present the core emotional choice
            yield return StartCoroutine(PresentEmotionalChoice());
            
            // Wait for player choice
            yield return StartCoroutine(WaitForEmotionalChoice());
            
            // Apply choice consequences
            ApplyEmotionalChoice();
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator PowerAwakeningPhase()
        {
            currentPhase = OpeningPhase.PowerAwakening;
            
            // Show power awakening based on emotional choice
            yield return StartCoroutine(ShowPowerAwakening());
            
            // Teach basic controls
            yield return StartCoroutine(TeachBasicControls());
            
            yield return new WaitForSeconds(3f);
        }

        private IEnumerator FirstQuestPhase()
        {
            currentPhase = OpeningPhase.FirstQuest;
            
            // Give the first quest
            yield return StartCoroutine(GiveFirstQuest());
            
            // Show quest objectives
            yield return StartCoroutine(ShowQuestObjectives());
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator WorldIntroductionPhase()
        {
            currentPhase = OpeningPhase.WorldIntroduction;
            
            // Introduce the world and its kingdoms
            yield return StartCoroutine(IntroduceWorld());
            
            // Show the player's place in this world
            yield return StartCoroutine(ShowPlayerRole());
            
            yield return new WaitForSeconds(3f);
        }
        #endregion

        #region Phase Implementations
        private IEnumerator ShowMemoryFragments()
        {
            if (memoryParticles != null)
            {
                memoryParticles.Play();
            }
            
            // Show fragmented visual memories
            string[] memoryTexts = {
                "A warm hand on your forehead...",
                "The sound of your mother's voice...",
                "The smell of your father's workshop...",
                "Playing with other children...",
                "The taste of honey cakes...",
                "Then... the fire came."
            };
            
            foreach (string memory in memoryTexts)
            {
                yield return StartCoroutine(DisplayNarrativeText(memory, 2f));
                yield return new WaitForSeconds(1f);
            }
        }

        private IEnumerator ShowChildhoodScenes()
        {
            // Show specific childhood scenes based on player choices
            yield return StartCoroutine(DisplayNarrativeText(
                "You remember your childhood in the village of Ashenheart...\n" +
                "A place where the hearth never grew cold, and stories were told by firelight."
            ));
            
            yield return new WaitForSeconds(3f);
        }

        private IEnumerator SelectBackstoryElements()
        {
            // Present backstory choices
            BackstoryChoice[] choices = {
                new BackstoryChoice {
                    choiceText = "You were the blacksmith's child, learning the forge",
                    backstoryElement = BackstoryElement.SmithChild,
                    emotionalWeight = 10f
                },
                new BackstoryChoice {
                    choiceText = "You were the healer's apprentice, tending wounds",
                    backstoryElement = BackstoryElement.HealerApprentice,
                    emotionalWeight = 15f
                },
                new BackstoryChoice {
                    choiceText = "You were the storyteller's grandchild, keeper of tales",
                    backstoryElement = BackstoryElement.StorytellerGrandchild,
                    emotionalWeight = 20f
                }
            };
            
            yield return StartCoroutine(PresentBackstoryChoices(choices));
        }

        private IEnumerator RevealTragedy()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Then came the night when everything changed...\n" +
                "The raiders arrived without warning, bringing death and flame.\n" +
                "Your village burned... your family... everyone you loved..."
            ));
            
            // Play tragic music
            if (emotionalMusic.Length > 0 && narrativeAudioSource != null)
            {
                narrativeAudioSource.clip = emotionalMusic[0];
                narrativeAudioSource.Play();
            }
            
            yield return new WaitForSeconds(5f);
        }

        private IEnumerator ShowTransformation()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "In that moment of ultimate loss, something awakened within you...\n" +
                "The fire that consumed your world became part of you.\n" +
                "You became... The Cinderborn."
            ));
            
            // Visual transformation effects
            if (emberEffect != null)
            {
                emberEffect.Play();
            }
            
            yield return new WaitForSeconds(4f);
        }

        private IEnumerator PresentEmotionalChoice()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Now, as you stand in the ashes of your past,\n" +
                "what drives the fire within you?"
            ));
            
            // Show choice panel
            if (choicePanel != null)
            {
                choicePanel.SetActive(true);
            }
            
            // Present emotional choices
            EmotionalChoiceData[] choices = {
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Vengeance,
                    text = "Vengeance - I will burn those who wronged me",
                    description = "Let rage fuel your flames"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Protection,
                    text = "Protection - I will burn to protect the innocent",
                    description = "Let compassion guide your fire"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Understanding,
                    text = "Understanding - I will burn to understand my nature",
                    description = "Let wisdom illuminate your path"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Acceptance,
                    text = "Acceptance - I will burn because it is my fate",
                    description = "Let destiny shape your journey"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Rebellion,
                    text = "Rebellion - I will burn to defy destiny itself",
                    description = "Let defiance forge your will"
                },
                new EmotionalChoiceData {
                    choice = EmotionalChoice.Sacrifice,
                    text = "Sacrifice - I will burn so others don't have to",
                    description = "Let selflessness define your purpose"
                }
            };
            
            yield return StartCoroutine(PresentEmotionalChoices(choices));
        }

        private IEnumerator WaitForEmotionalChoice()
        {
            // Wait for player to make emotional choice
            while (playerEmotionalChoice == EmotionalChoice.None)
            {
                yield return null;
            }
            
            // Hide choice panel
            if (choicePanel != null)
            {
                choicePanel.SetActive(false);
            }
        }

        private IEnumerator ShowPowerAwakening()
        {
            string awakeningText = GetPowerAwakeningText(playerEmotionalChoice);
            yield return StartCoroutine(DisplayNarrativeText(awakeningText));
            
            // Visual effects based on choice
            ApplyChoiceVisualEffects(playerEmotionalChoice);
            
            yield return new WaitForSeconds(3f);
        }

        private IEnumerator TeachBasicControls()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Feel the power flowing through you...\n" +
                "Use WASD to move, Mouse to look around.\n" +
                "Left click to channel your fire, Right click for focused flame."
            ));
            
            // Enable basic player control for tutorial
            EnableBasicPlayerControl();
            
            yield return new WaitForSeconds(5f);
        }

        private IEnumerator GiveFirstQuest()
        {
            string questText = GetFirstQuestText(playerEmotionalChoice);
            yield return StartCoroutine(DisplayNarrativeText(questText));
            
            // Create and assign first quest
            if (questSystem != null)
            {
                CreateFirstQuest();
            }
            
            yield return new WaitForSeconds(3f);
        }

        private IEnumerator ShowQuestObjectives()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "Your journey begins now, Cinderborn.\n" +
                "Press J to open your Quest Log and track your objectives."
            ));
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator IntroduceWorld()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "The world before you is vast and divided...\n" +
                "Kingdoms of Light and Shadow, villages with their own beliefs,\n" +
                "and ancient powers that stir in the darkness."
            ));
            
            yield return new WaitForSeconds(4f);
        }

        private IEnumerator ShowPlayerRole()
        {
            yield return StartCoroutine(DisplayNarrativeText(
                "You are The Cinderborn - neither fully human nor spirit.\n" +
                "Your choices will shape not just your destiny,\n" +
                "but the fate of all who dwell in this realm."
            ));
            
            yield return new WaitForSeconds(4f);
        }
        #endregion

        #region Utility Methods
        private IEnumerator DisplayNarrativeText(string text, float duration = 3f)
        {
            if (narrativeText != null)
            {
                narrativeText.text = "";
                
                // Typewriter effect
                for (int i = 0; i <= text.Length; i++)
                {
                    narrativeText.text = text.Substring(0, i);
                    yield return new WaitForSeconds(0.03f);
                }
                
                yield return new WaitForSeconds(duration);
            }
        }

        private void SetupCinematicMode()
        {
            if (cinematicCamera != null)
                cinematicCamera.enabled = true;
            
            if (gameplayCamera != null)
                gameplayCamera.enabled = false;
            
            if (openingUI != null)
                openingUI.SetActive(true);
        }

        private void DisablePlayerControl()
        {
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = false;
        }

        private void EnableBasicPlayerControl()
        {
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
                playerController.enabled = true;
        }

        private void HandleInput()
        {
            if (isSequenceActive && canSkip && Input.GetKeyDown(skipKey))
            {
                SkipOpeningSequence();
            }
        }

        private void UpdateSequenceProgress()
        {
            if (isSequenceActive)
            {
                float elapsed = Time.time - sequenceStartTime;
                if (elapsed > 5f) // Allow skipping after 5 seconds
                {
                    canSkip = true;
                    if (skipPrompt != null)
                        skipPrompt.SetActive(true);
                }
            }
        }

        private void SkipOpeningSequence()
        {
            StopAllCoroutines();
            CompleteOpeningSequence();
        }

        private void CompleteOpeningSequence()
        {
            isSequenceActive = false;
            currentPhase = OpeningPhase.Complete;
            
            // Mark as played
            PlayerPrefs.SetInt("EnhancedOpeningPlayed", 1);
            PlayerPrefs.Save();
            
            // Return to gameplay mode
            if (cinematicCamera != null)
                cinematicCamera.enabled = false;
            
            if (gameplayCamera != null)
                gameplayCamera.enabled = true;
            
            if (openingUI != null)
                openingUI.SetActive(false);
            
            EnableBasicPlayerControl();
            
            Debug.Log($"Enhanced opening sequence completed with emotional choice: {playerEmotionalChoice}");
        }
        #endregion

        #region Choice Handling
        public void SelectEmotionalChoice(int choiceIndex)
        {
            if (choiceIndex >= 0 && choiceIndex < 6)
            {
                playerEmotionalChoice = (EmotionalChoice)(choiceIndex + 1);
                emotionalResonance = CalculateEmotionalResonance(playerEmotionalChoice);
            }
        }

        private void ApplyEmotionalChoice()
        {
            if (playerStats != null)
            {
                // Apply choice effects to player stats and narrative system
                switch (playerEmotionalChoice)
                {
                    case EmotionalChoice.Vengeance:
                        playerStats.ModifyAlignment(-20f, 0f); // More shadow-aligned
                        break;
                    case EmotionalChoice.Protection:
                        playerStats.ModifyAlignment(20f, 0f); // More light-aligned
                        break;
                    case EmotionalChoice.Understanding:
                        playerStats.ModifyAlignment(0f, 20f); // More moon-aligned
                        break;
                    // Add more choice effects
                }
            }
            
            if (narrativeSystem != null)
            {
                narrativeSystem.SetStoryFlag($"opening_choice_{playerEmotionalChoice}", true);
                narrativeSystem.SetStoryFlag("opening_completed", true);
            }
        }

        private string GetPowerAwakeningText(EmotionalChoice choice)
        {
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return "Your flames burn with righteous fury, seeking to consume those who have wronged you.";
                case EmotionalChoice.Protection:
                    return "Your fire becomes a shield, warm and protective, ready to guard the innocent.";
                case EmotionalChoice.Understanding:
                    return "Your flames flicker with curiosity, seeking to illuminate the mysteries of your existence.";
                case EmotionalChoice.Acceptance:
                    return "Your fire burns steady and calm, accepting the burden of your destiny.";
                case EmotionalChoice.Rebellion:
                    return "Your flames rage against the very concept of fate, burning to forge your own path.";
                case EmotionalChoice.Sacrifice:
                    return "Your fire burns with selfless purpose, ready to consume itself for others' sake.";
                default:
                    return "Your flames awaken, ready to shape the world according to your will.";
            }
        }

        private string GetFirstQuestText(EmotionalChoice choice)
        {
            switch (choice)
            {
                case EmotionalChoice.Vengeance:
                    return "Seek out the raiders who destroyed your village. Begin with the nearest settlement - someone there will know of their whereabouts.";
                case EmotionalChoice.Protection:
                    return "There are others in need of protection. Travel to the Village of Forgotten Names - they face a threat you can help them overcome.";
                case EmotionalChoice.Understanding:
                    return "Seek the Sage of Whispering Stones. They may have answers about your transformation and what it means.";
                default:
                    return "Your path begins in the Village of Forgotten Names. There, you will find your first test as The Cinderborn.";
            }
        }

        private void CreateFirstQuest()
        {
            // Create appropriate first quest based on emotional choice
            // This would integrate with the quest system
        }

        private float CalculateEmotionalResonance(EmotionalChoice choice)
        {
            // Calculate emotional resonance value
            return 1f; // Placeholder
        }

        private void ApplyChoiceVisualEffects(EmotionalChoice choice)
        {
            // Apply visual effects based on emotional choice
            if (dreamLight != null)
            {
                switch (choice)
                {
                    case EmotionalChoice.Vengeance:
                        dreamLight.color = Color.red;
                        break;
                    case EmotionalChoice.Protection:
                        dreamLight.color = Color.white;
                        break;
                    case EmotionalChoice.Understanding:
                        dreamLight.color = Color.blue;
                        break;
                    // Add more visual effects
                }
            }
        }
        #endregion

        #region Data Structures
        [System.Serializable]
        public class BackstoryChoice
        {
            public string choiceText;
            public BackstoryElement backstoryElement;
            public float emotionalWeight;
        }

        [System.Serializable]
        public class EmotionalChoiceData
        {
            public EmotionalChoice choice;
            public string text;
            public string description;
        }

        public enum BackstoryElement
        {
            SmithChild,
            HealerApprentice,
            StorytellerGrandchild,
            MerchantChild,
            GuardChild,
            FarmerChild
        }

        private IEnumerator PresentBackstoryChoices(BackstoryChoice[] choices)
        {
            // Implementation for presenting backstory choices
            yield return new WaitForSeconds(1f);
        }

        private IEnumerator PresentEmotionalChoices(EmotionalChoiceData[] choices)
        {
            // Implementation for presenting emotional choices
            yield return new WaitForSeconds(1f);
        }
        #endregion
    }
}
