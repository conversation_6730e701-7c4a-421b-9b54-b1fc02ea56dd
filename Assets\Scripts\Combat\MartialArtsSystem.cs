using UnityEngine;
using System.Collections.Generic;

public class MartialArtsSystem : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [<PERSON><PERSON>("Martial Arts Schools")]
    public MartialArtsSchool[] availableSchools;
    public List<string> learnedSchools = new List<string>();
    public MartialArtsSchool currentActiveSchool;
    
    [Header("Skill Points")]
    public int totalSkillPoints = 0;
    public int spentSkillPoints = 0;
    public int availableSkillPoints = 0;
    
    [Header("Combat Stance")]
    public CombatStance currentStance = CombatStance.Neutral;
    public float stanceTransitionTime = 1f;
    
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private Animator playerAnimator;
    private Dictionary<string, int> schoolMastery = new Dictionary<string, int>();
    
    public enum CombatStance
    {
        Neutral,        // Balanced stance
        Aggressive,     // High damage, low defense
        Defensive,      // High defense, low damage
        Mobile,         // High speed, medium damage
        Focused,        // High precision, medium speed
        Berserker       // Maximum damage, no defense
    }
    
    [System.Serializable]
    public class MartialArtsSchool
    {
        [Header("School Info")]
        public string schoolName;
        public string culturalName;
        public MartialArtsOrigin origin;
        public string philosophy;
        public string masterName;
        
        [Header("Requirements")]
        public int minimumLevel;
        public MoralRequirement moralRequirement;
        public string[] prerequisiteSchools;
        
        [Header("Techniques")]
        public MartialTechnique[] techniques;
        public ComboChain[] combos;
        public CounterAttack[] counters;
        
        [Header("Passive Benefits")]
        public SchoolPassive[] passiveBenefits;
        
        public enum MartialArtsOrigin
        {
            Korean,         // Taekwondo
            Japanese,       // Jujitsu, Karate
            Chinese,        // Kung Fu, Wing Chun
            Arabic,         // Sayf wa Tariq (Sword and Path)
            European,       // HEMA, Boxing
            Brazilian,      // Capoeira, BJJ
            Thai,           // Muay Thai
            Filipino,       // Kali, Escrima
            Russian,        // Sambo, Systema
            Mixed           // Custom combinations
        }
    }
    
    [System.Serializable]
    public class MartialTechnique
    {
        public string techniqueName;
        public string culturalName;
        public TechniqueType type;
        public int requiredMastery;
        public float damage;
        public float speed;
        public float staminaCost;
        public float range;
        public AnimationClip animation;
        public GameObject effectPrefab;
        public AudioClip techniqueSound;
        public string description;
        
        public enum TechniqueType
        {
            Strike,         // Punches, kicks
            Grapple,        // Throws, holds
            Block,          // Defensive moves
            Counter,        // Counterattacks
            Combo,          // Multi-hit sequences
            Finisher,       // Devastating final moves
            Evasion,        // Dodges, rolls
            Stance          // Stance changes
        }
    }
    
    [System.Serializable]
    public class ComboChain
    {
        public string comboName;
        public string[] techniqueSequence;
        public float timingWindow;
        public float bonusDamage;
        public GameObject comboEffect;
    }
    
    [System.Serializable]
    public class CounterAttack
    {
        public string counterName;
        public AttackType triggeredBy;
        public float counterWindow;
        public float counterDamage;
        public bool guaranteedHit;
        
        public enum AttackType
        {
            Any,
            High,
            Mid,
            Low,
            Thrust,
            Slash,
            Blunt
        }
    }
    
    [System.Serializable]
    public class SchoolPassive
    {
        public string passiveName;
        public PassiveType type;
        public float value;
        public string description;
        
        public enum PassiveType
        {
            DamageBonus,
            SpeedBonus,
            StaminaEfficiency,
            CounterChance,
            CriticalChance,
            HealthRegen,
            FocusGain,
            StanceBonus
        }
    }
    
    [System.Serializable]
    public class MoralRequirement
    {
        public PlayerStats.MoralPath requiredPath;
        public float minimumAlignment;
        public bool allowNeutral;
        public string reasoning;
    }
    
    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        playerCombat = GetComponent<PlayerCombat>();
        playerAnimator = GetComponent<Animator>();
        
        InitializeMartialArtsSchools();
        LoadPlayerProgress();
        
        // Subscribe to level up events
        if (playerStats != null)
        {
            playerStats.OnLevelUp += OnPlayerLevelUp;
        }
    }
    
    void Update()
    {
        HandleStanceInput();
        UpdateCombatStance();
    }
    
    void InitializeMartialArtsSchools()
    {
        // Taekwondo School
        MartialArtsSchool taekwondo = new MartialArtsSchool
        {
            schoolName = "Taekwondo",
            culturalName = "태권도",
            origin = MartialArtsSchool.MartialArtsOrigin.Korean,
            philosophy = "The way of the foot and fist. Emphasizes high kicks, speed, and precision.",
            masterName = "Master Kim Dae-jung",
            minimumLevel = 5,
            moralRequirement = new MoralRequirement
            {
                requiredPath = PlayerStats.MoralPath.Sun,
                minimumAlignment = 20f,
                allowNeutral = true,
                reasoning = "Taekwondo emphasizes discipline and self-control"
            }
        };
        
        // Jujitsu School
        MartialArtsSchool jujitsu = new MartialArtsSchool
        {
            schoolName = "Jujitsu",
            culturalName = "柔術",
            origin = MartialArtsSchool.MartialArtsOrigin.Japanese,
            philosophy = "The gentle art. Uses opponent's force against them through throws and joint locks.",
            masterName = "Sensei Takeshi Yamamoto",
            minimumLevel = 8,
            moralRequirement = new MoralRequirement
            {
                allowNeutral = true,
                reasoning = "Jujitsu teaches adaptability and using minimum force"
            }
        };
        
        // Arabic Swordplay
        MartialArtsSchool arabicSwordplay = new MartialArtsSchool
        {
            schoolName = "Sayf wa Tariq",
            culturalName = "سيف وطريق",
            origin = MartialArtsSchool.MartialArtsOrigin.Arabic,
            philosophy = "The sword and the path. Combines blade work with spiritual discipline.",
            masterName = "Ustad Hakim al-Dimashqi",
            minimumLevel = 10,
            moralRequirement = new MoralRequirement
            {
                allowNeutral = true,
                reasoning = "Requires understanding of both light and shadow"
            }
        };
        
        // Wing Chun
        MartialArtsSchool wingChun = new MartialArtsSchool
        {
            schoolName = "Wing Chun",
            culturalName = "詠春",
            origin = MartialArtsSchool.MartialArtsOrigin.Chinese,
            philosophy = "Centerline theory and simultaneous attack and defense.",
            masterName = "Sifu Chen Wei-ming",
            minimumLevel = 12,
            moralRequirement = new MoralRequirement
            {
                allowNeutral = true,
                reasoning = "Emphasizes efficiency and directness"
            }
        };
        
        availableSchools = new MartialArtsSchool[] { taekwondo, jujitsu, arabicSwordplay, wingChun };
    }
    
    void LoadPlayerProgress()
    {
        string learnedData = PlayerPrefs.GetString("LearnedMartialArts", "");
        if (!string.IsNullOrEmpty(learnedData))
        {
            learnedSchools = new List<string>(learnedData.Split(','));
        }
        
        totalSkillPoints = PlayerPrefs.GetInt("TotalSkillPoints", 0);
        spentSkillPoints = PlayerPrefs.GetInt("SpentSkillPoints", 0);
        availableSkillPoints = totalSkillPoints - spentSkillPoints;
    }
    
    void HandleStanceInput()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
            ChangeStance(CombatStance.Neutral);
        else if (Input.GetKeyDown(KeyCode.Alpha2))
            ChangeStance(CombatStance.Aggressive);
        else if (Input.GetKeyDown(KeyCode.Alpha3))
            ChangeStance(CombatStance.Defensive);
        else if (Input.GetKeyDown(KeyCode.Alpha4))
            ChangeStance(CombatStance.Mobile);
        else if (Input.GetKeyDown(KeyCode.Alpha5))
            ChangeStance(CombatStance.Focused);
    }
    
    void ChangeStance(CombatStance newStance)
    {
        if (currentStance != newStance)
        {
            currentStance = newStance;
            StartCoroutine(TransitionToStance(newStance));
        }
    }
    
    System.Collections.IEnumerator TransitionToStance(CombatStance stance)
    {
        // Play stance transition animation
        if (playerAnimator != null)
        {
            playerAnimator.SetTrigger($"Stance_{stance}");
        }
        
        yield return new WaitForSeconds(stanceTransitionTime);
        
        // Apply stance effects
        ApplyStanceEffects(stance);
        
        Debug.Log($"Changed to {stance} stance");
    }
    
    void ApplyStanceEffects(CombatStance stance)
    {
        // Reset all modifiers first
        ResetStanceModifiers();
        
        switch (stance)
        {
            case CombatStance.Aggressive:
                playerCombat.damageMultiplier = 1.3f;
                playerCombat.defenseMultiplier = 0.7f;
                break;
            case CombatStance.Defensive:
                playerCombat.damageMultiplier = 0.8f;
                playerCombat.defenseMultiplier = 1.4f;
                break;
            case CombatStance.Mobile:
                playerCombat.speedMultiplier = 1.3f;
                playerCombat.staminaCostMultiplier = 0.8f;
                break;
            case CombatStance.Focused:
                playerCombat.criticalChanceBonus = 15f;
                playerCombat.accuracyBonus = 20f;
                break;
            case CombatStance.Berserker:
                playerCombat.damageMultiplier = 1.8f;
                playerCombat.defenseMultiplier = 0.3f;
                playerCombat.staminaCostMultiplier = 1.5f;
                break;
            default: // Neutral
                // No modifiers
                break;
        }
    }
    
    void ResetStanceModifiers()
    {
        if (playerCombat != null)
        {
            playerCombat.damageMultiplier = 1f;
            playerCombat.defenseMultiplier = 1f;
            playerCombat.speedMultiplier = 1f;
            playerCombat.staminaCostMultiplier = 1f;
            playerCombat.criticalChanceBonus = 0f;
            playerCombat.accuracyBonus = 0f;
        }
    }
    
    void UpdateCombatStance()
    {
        if (playerAnimator != null)
        {
            playerAnimator.SetInt("CombatStance", (int)currentStance);
        }
    }
    
    public bool CanLearnSchool(string schoolName)
    {
        MartialArtsSchool school = GetSchoolByName(schoolName);
        if (school == null) return false;
        
        // Check level requirement
        if (playerStats.GetCurrentLevel() < school.minimumLevel)
            return false;
        
        // Check moral requirement
        if (!CheckMoralRequirement(school.moralRequirement))
            return false;
        
        // Check prerequisites
        foreach (string prerequisite in school.prerequisiteSchools)
        {
            if (!learnedSchools.Contains(prerequisite))
                return false;
        }
        
        return true;
    }
    
    bool CheckMoralRequirement(MoralRequirement requirement)
    {
        if (requirement.allowNeutral && playerStats.GetCurrentPath() == PlayerStats.MoralPath.Eclipse)
            return true;
        
        if (requirement.requiredPath != PlayerStats.MoralPath.Eclipse && 
            requirement.requiredPath != playerStats.GetCurrentPath())
            return false;
        
        float currentAlignment = 0f;
        switch (requirement.requiredPath)
        {
            case PlayerStats.MoralPath.Sun:
                currentAlignment = playerStats.GetSunAlignment();
                break;
            case PlayerStats.MoralPath.Moon:
                currentAlignment = playerStats.GetMoonAlignment();
                break;
        }
        
        return currentAlignment >= requirement.minimumAlignment;
    }
    
    public void LearnSchool(string schoolName)
    {
        if (CanLearnSchool(schoolName) && !learnedSchools.Contains(schoolName))
        {
            learnedSchools.Add(schoolName);
            schoolMastery[schoolName] = 0;
            SaveProgress();
            
            MartialArtsSchool school = GetSchoolByName(schoolName);
            if (school != null)
            {
                ShowSchoolLearned(school);
            }
        }
    }
    
    public void UseTechnique(string schoolName, string techniqueName)
    {
        MartialArtsSchool school = GetSchoolByName(schoolName);
        if (school == null || !learnedSchools.Contains(schoolName)) return;
        
        MartialTechnique technique = GetTechnique(school, techniqueName);
        if (technique != null && CanUseTechnique(schoolName, technique))
        {
            ExecuteTechnique(technique);
            GainMastery(schoolName, 1);
        }
    }
    
    bool CanUseTechnique(string schoolName, MartialTechnique technique)
    {
        int currentMastery = schoolMastery.ContainsKey(schoolName) ? schoolMastery[schoolName] : 0;
        
        if (currentMastery < technique.requiredMastery)
            return false;
        
        if (playerStats.GetCurrentStamina() < technique.staminaCost)
            return false;
        
        return true;
    }
    
    void ExecuteTechnique(MartialTechnique technique)
    {
        // Consume stamina
        playerStats.ConsumeStamina(technique.staminaCost);
        
        // Play animation
        if (playerAnimator != null && technique.animation != null)
        {
            playerAnimator.Play(technique.animation.name);
        }
        
        // Create effect
        if (technique.effectPrefab != null)
        {
            Instantiate(technique.effectPrefab, transform.position, transform.rotation);
        }
        
        // Play sound
        if (technique.techniqueSound != null)
        {
            AudioSource.PlayClipAtPoint(technique.techniqueSound, transform.position);
        }
        
        // Apply technique effects
        ApplyTechniqueEffects(technique);
        
        Debug.Log($"Used technique: {technique.techniqueName} ({technique.culturalName})");
    }
    
    void ApplyTechniqueEffects(MartialTechnique technique)
    {
        switch (technique.type)
        {
            case MartialTechnique.TechniqueType.Strike:
                // Enhanced strike attack
                if (playerCombat != null)
                {
                    playerCombat.PerformMartialStrike(technique);
                }
                break;
            case MartialTechnique.TechniqueType.Grapple:
                // Grappling move
                PerformGrapple(technique);
                break;
            case MartialTechnique.TechniqueType.Counter:
                // Set up counter state
                SetupCounter(technique);
                break;
        }
    }
    
    void PerformGrapple(MartialTechnique technique)
    {
        // Find nearby enemies for grappling
        Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, technique.range);
        
        foreach (Collider enemy in nearbyEnemies)
        {
            EnemyHealth enemyHealth = enemy.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                // Apply grapple damage and effects
                enemyHealth.TakeDamage(technique.damage);
                
                // Stun enemy briefly
                EnemyAI enemyAI = enemy.GetComponent<EnemyAI>();
                if (enemyAI != null)
                {
                    StartCoroutine(StunEnemy(enemyAI, 2f));
                }
                break; // Only grapple one enemy
            }
        }
    }
    
    System.Collections.IEnumerator StunEnemy(EnemyAI enemy, float duration)
    {
        EnemyAI.AIState previousState = enemy.currentState;
        enemy.currentState = EnemyAI.AIState.Stunned;
        
        yield return new WaitForSeconds(duration);
        
        enemy.currentState = previousState;
    }
    
    void SetupCounter(MartialTechnique technique)
    {
        // Set up counter window for incoming attacks
        if (playerCombat != null)
        {
            playerCombat.SetupCounterWindow(technique, 2f);
        }
    }
    
    void GainMastery(string schoolName, int amount)
    {
        if (schoolMastery.ContainsKey(schoolName))
        {
            schoolMastery[schoolName] += amount;
        }
        else
        {
            schoolMastery[schoolName] = amount;
        }
        
        // Check for mastery milestones
        CheckMasteryMilestones(schoolName);
    }
    
    void CheckMasteryMilestones(string schoolName)
    {
        int mastery = schoolMastery[schoolName];
        
        // Unlock new techniques at certain mastery levels
        if (mastery == 25 || mastery == 50 || mastery == 75 || mastery == 100)
        {
            ShowMasteryMilestone(schoolName, mastery);
        }
    }
    
    void ShowSchoolLearned(MartialArtsSchool school)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = $"Martial Art Learned: {school.schoolName} ({school.culturalName})\n\"{school.philosophy}\"";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 6f));
        }
    }
    
    void ShowMasteryMilestone(string schoolName, int mastery)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = $"Mastery Milestone: {schoolName} - Level {mastery}\nNew techniques unlocked!";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
    }
    
    System.Collections.IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    void OnPlayerLevelUp(int newLevel)
    {
        // Gain skill points on level up
        int skillPointsGained = Random.Range(2, 5);
        totalSkillPoints += skillPointsGained;
        availableSkillPoints += skillPointsGained;
        
        Debug.Log($"Gained {skillPointsGained} skill points! Total available: {availableSkillPoints}");
    }
    
    MartialArtsSchool GetSchoolByName(string schoolName)
    {
        foreach (MartialArtsSchool school in availableSchools)
        {
            if (school.schoolName == schoolName)
                return school;
        }
        return null;
    }
    
    MartialTechnique GetTechnique(MartialArtsSchool school, string techniqueName)
    {
        foreach (MartialTechnique technique in school.techniques)
        {
            if (technique.techniqueName == techniqueName)
                return technique;
        }
        return null;
    }
    
    void SaveProgress()
    {
        PlayerPrefs.SetString("LearnedMartialArts", string.Join(",", learnedSchools));
        PlayerPrefs.SetInt("TotalSkillPoints", totalSkillPoints);
        PlayerPrefs.SetInt("SpentSkillPoints", spentSkillPoints);
        PlayerPrefs.Save();
    }
    
    // Getters
    public List<string> GetLearnedSchools() => learnedSchools;
    public int GetSchoolMastery(string schoolName)
    {
        return schoolMastery.ContainsKey(schoolName) ? schoolMastery[schoolName] : 0;
    }
    public CombatStance GetCurrentStance() => currentStance;
    public int GetAvailableSkillPoints() => availableSkillPoints;
}
