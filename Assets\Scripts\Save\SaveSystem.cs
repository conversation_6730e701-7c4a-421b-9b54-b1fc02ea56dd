using UnityEngine;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;

namespace CinderOfDarkness
{
    /// <summary>
    /// Save System for Cinder of Darkness.
    /// Handles game state persistence across all systems.
    /// </summary>
    public class SaveSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Save Settings")]
        [SerializeField] private string saveFileName = "CinderSave";
        [SerializeField] private string saveFileExtension = ".json";
        [SerializeField] private bool encryptSaves = true;
        [SerializeField] private int maxSaveSlots = 3;
        #endregion

        #region Public Properties
        public static SaveSystem Instance { get; private set; }
        #endregion

        #region Private Fields
        private string savePath;
        private Dictionary<int, SaveData> loadedSaves = new Dictionary<int, SaveData>();
        #endregion

        #region Events
        public System.Action<int> OnGameSaved;
        public System.Action<int> OnGameLoaded;
        public System.Action<string> OnSaveError;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSaveSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        #endregion

        #region Initialization
        private void InitializeSaveSystem()
        {
            savePath = Path.Combine(Application.persistentDataPath, "Saves");
            
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            Debug.Log($"Save system initialized. Save path: {savePath}");
        }
        #endregion

        #region Save Operations
        public void SaveGame(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= maxSaveSlots)
            {
                OnSaveError?.Invoke($"Invalid save slot: {slotIndex}");
                return;
            }

            try
            {
                var saveData = CollectSaveData();
                saveData.slotIndex = slotIndex;
                saveData.saveTime = System.DateTime.Now.ToString();
                saveData.gameVersion = Application.version;

                string fileName = GetSaveFileName(slotIndex);
                string filePath = Path.Combine(savePath, fileName);
                
                string json = JsonConvert.SerializeObject(saveData, Formatting.Indented);
                
                if (encryptSaves)
                {
                    json = EncryptString(json);
                }

                File.WriteAllText(filePath, json);
                
                // Cache the save data
                loadedSaves[slotIndex] = saveData;
                
                // Save screenshot
                SaveScreenshot(slotIndex);
                
                OnGameSaved?.Invoke(slotIndex);
                Debug.Log($"Game saved to slot {slotIndex}");
            }
            catch (System.Exception e)
            {
                string error = $"Failed to save game: {e.Message}";
                OnSaveError?.Invoke(error);
                Debug.LogError(error);
            }
        }

        public void LoadGame(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= maxSaveSlots)
            {
                OnSaveError?.Invoke($"Invalid save slot: {slotIndex}");
                return;
            }

            try
            {
                string fileName = GetSaveFileName(slotIndex);
                string filePath = Path.Combine(savePath, fileName);
                
                if (!File.Exists(filePath))
                {
                    OnSaveError?.Invoke($"Save file not found for slot {slotIndex}");
                    return;
                }

                string json = File.ReadAllText(filePath);
                
                if (encryptSaves)
                {
                    json = DecryptString(json);
                }

                var saveData = JsonConvert.DeserializeObject<SaveData>(json);
                
                if (saveData == null)
                {
                    OnSaveError?.Invoke("Failed to deserialize save data");
                    return;
                }

                ApplySaveData(saveData);
                
                // Cache the save data
                loadedSaves[slotIndex] = saveData;
                
                OnGameLoaded?.Invoke(slotIndex);
                Debug.Log($"Game loaded from slot {slotIndex}");
            }
            catch (System.Exception e)
            {
                string error = $"Failed to load game: {e.Message}";
                OnSaveError?.Invoke(error);
                Debug.LogError(error);
            }
        }

        public void DeleteSave(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= maxSaveSlots)
            {
                OnSaveError?.Invoke($"Invalid save slot: {slotIndex}");
                return;
            }

            try
            {
                string fileName = GetSaveFileName(slotIndex);
                string filePath = Path.Combine(savePath, fileName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // Delete screenshot
                string screenshotPath = GetScreenshotPath(slotIndex);
                if (File.Exists(screenshotPath))
                {
                    File.Delete(screenshotPath);
                }

                // Remove from cache
                if (loadedSaves.ContainsKey(slotIndex))
                {
                    loadedSaves.Remove(slotIndex);
                }

                Debug.Log($"Save slot {slotIndex} deleted");
            }
            catch (System.Exception e)
            {
                string error = $"Failed to delete save: {e.Message}";
                OnSaveError?.Invoke(error);
                Debug.LogError(error);
            }
        }
        #endregion

        #region Save Data Collection
        private SaveData CollectSaveData()
        {
            var saveData = new SaveData();

            // Player data
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                saveData.playerPosition = player.transform.position;
                saveData.playerRotation = player.transform.rotation;
                
                var playerStats = player.GetComponent<PlayerStats>();
                if (playerStats != null)
                {
                    saveData.playerLevel = playerStats.Level;
                    saveData.playerExperience = playerStats.Experience;
                    saveData.playerHealth = playerStats.CurrentHealth;
                    saveData.playerMana = playerStats.CurrentMana;
                }
            }

            // Inventory data
            var inventory = FindObjectOfType<PlayerInventory>();
            if (inventory != null)
            {
                saveData.inventoryItems = inventory.GetAllItems();
            }

            // Narrative data
            var narrativeSystem = DynamicNarrativeSystem.Instance;
            if (narrativeSystem != null)
            {
                saveData.narrativeChoices = narrativeSystem.GetChoiceHistory();
                saveData.storyFlags = narrativeSystem.GetStoryFlags();
                saveData.factionReputation = narrativeSystem.GetFactionReputation();
            }

            // Magic data
            var magicSystem = MagicEvolutionSystem.Instance;
            if (magicSystem != null)
            {
                saveData.learnedSpells = new List<PlayerSpell>(magicSystem.LearnedSpells.Values);
                saveData.magicPoints = magicSystem.MagicPoints;
            }

            // Economy data
            var economySystem = EconomySystem.Instance;
            if (economySystem != null)
            {
                saveData.currencies = economySystem.GetAllCurrencies();
            }

            // Map data
            var mapSystem = WorldMapSystem.Instance;
            if (mapSystem != null)
            {
                saveData.exploredAreas = mapSystem.GetExploredAreas();
                saveData.mapMarkers = mapSystem.GetAllMarkers();
            }

            // Time data
            var timeSystem = DynamicTimeSystem.Instance;
            if (timeSystem != null)
            {
                saveData.gameTime = timeSystem.GetCurrentTime();
                saveData.dayCount = timeSystem.GetDayCount();
            }

            // Scene data
            saveData.currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

            return saveData;
        }

        private void ApplySaveData(SaveData saveData)
        {
            // Player data
            var player = FindObjectOfType<PlayerController>();
            if (player != null && saveData.playerPosition != Vector3.zero)
            {
                player.transform.position = saveData.playerPosition;
                player.transform.rotation = saveData.playerRotation;
                
                var playerStats = player.GetComponent<PlayerStats>();
                if (playerStats != null)
                {
                    playerStats.SetLevel(saveData.playerLevel);
                    playerStats.SetExperience(saveData.playerExperience);
                    playerStats.SetHealth(saveData.playerHealth);
                    playerStats.SetMana(saveData.playerMana);
                }
            }

            // Inventory data
            var inventory = FindObjectOfType<PlayerInventory>();
            if (inventory != null && saveData.inventoryItems != null)
            {
                // Clear current inventory and load saved items
                foreach (var item in saveData.inventoryItems)
                {
                    inventory.AddItem(item);
                }
            }

            // Narrative data
            var narrativeSystem = DynamicNarrativeSystem.Instance;
            if (narrativeSystem != null)
            {
                if (saveData.narrativeChoices != null)
                    narrativeSystem.LoadChoiceHistory(saveData.narrativeChoices);
                
                if (saveData.storyFlags != null)
                    narrativeSystem.LoadStoryFlags(saveData.storyFlags);
                
                if (saveData.factionReputation != null)
                    narrativeSystem.LoadFactionReputation(saveData.factionReputation);
            }

            // Magic data
            var magicSystem = MagicEvolutionSystem.Instance;
            if (magicSystem != null)
            {
                if (saveData.learnedSpells != null)
                {
                    foreach (var spell in saveData.learnedSpells)
                    {
                        magicSystem.LoadSpell(spell);
                    }
                }
                
                magicSystem.SetMagicPoints(saveData.magicPoints);
            }

            // Economy data
            var economySystem = EconomySystem.Instance;
            if (economySystem != null && saveData.currencies != null)
            {
                economySystem.LoadCurrencies(saveData.currencies);
            }

            // Map data
            var mapSystem = WorldMapSystem.Instance;
            if (mapSystem != null)
            {
                if (saveData.exploredAreas != null)
                    mapSystem.LoadExploredAreas(saveData.exploredAreas);
                
                if (saveData.mapMarkers != null)
                    mapSystem.LoadMarkers(saveData.mapMarkers);
            }

            // Time data
            var timeSystem = DynamicTimeSystem.Instance;
            if (timeSystem != null)
            {
                timeSystem.SetTime(saveData.gameTime);
                timeSystem.SetDayCount(saveData.dayCount);
            }

            // Load scene if different
            if (!string.IsNullOrEmpty(saveData.currentScene) && 
                saveData.currentScene != UnityEngine.SceneManagement.SceneManager.GetActiveScene().name)
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(saveData.currentScene);
            }
        }
        #endregion

        #region Utility Methods
        private string GetSaveFileName(int slotIndex)
        {
            return $"{saveFileName}_{slotIndex}{saveFileExtension}";
        }

        private string GetScreenshotPath(int slotIndex)
        {
            return Path.Combine(savePath, $"{saveFileName}_{slotIndex}_screenshot.png");
        }

        private void SaveScreenshot(int slotIndex)
        {
            try
            {
                string screenshotPath = GetScreenshotPath(slotIndex);
                ScreenCapture.CaptureScreenshot(screenshotPath);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to save screenshot: {e.Message}");
            }
        }

        public bool HasSaveFile(int slotIndex)
        {
            string fileName = GetSaveFileName(slotIndex);
            string filePath = Path.Combine(savePath, fileName);
            return File.Exists(filePath);
        }

        public bool HasAnySaveFiles()
        {
            for (int i = 0; i < maxSaveSlots; i++)
            {
                if (HasSaveFile(i))
                    return true;
            }
            return false;
        }

        public SaveData GetSaveInfo(int slotIndex)
        {
            if (loadedSaves.ContainsKey(slotIndex))
                return loadedSaves[slotIndex];

            if (!HasSaveFile(slotIndex))
                return null;

            try
            {
                string fileName = GetSaveFileName(slotIndex);
                string filePath = Path.Combine(savePath, fileName);
                string json = File.ReadAllText(filePath);
                
                if (encryptSaves)
                {
                    json = DecryptString(json);
                }

                var saveData = JsonConvert.DeserializeObject<SaveData>(json);
                loadedSaves[slotIndex] = saveData;
                return saveData;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load save info: {e.Message}");
                return null;
            }
        }

        private string EncryptString(string text)
        {
            // Simple XOR encryption (in production, use proper encryption)
            byte[] data = System.Text.Encoding.UTF8.GetBytes(text);
            byte key = 0x42; // Simple key
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = (byte)(data[i] ^ key);
            }
            
            return System.Convert.ToBase64String(data);
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                byte[] data = System.Convert.FromBase64String(encryptedText);
                byte key = 0x42; // Same key as encryption
                
                for (int i = 0; i < data.Length; i++)
                {
                    data[i] = (byte)(data[i] ^ key);
                }
                
                return System.Text.Encoding.UTF8.GetString(data);
            }
            catch
            {
                // If decryption fails, assume it's unencrypted
                return encryptedText;
            }
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class SaveData
    {
        [Header("Save Info")]
        public int slotIndex;
        public string saveTime;
        public string gameVersion;
        public string currentScene;

        [Header("Player Data")]
        public Vector3 playerPosition;
        public Quaternion playerRotation;
        public int playerLevel;
        public float playerExperience;
        public float playerHealth;
        public float playerMana;

        [Header("Inventory")]
        public List<InventoryItem> inventoryItems;

        [Header("Narrative")]
        public List<NarrativeChoice> narrativeChoices;
        public Dictionary<string, bool> storyFlags;
        public Dictionary<string, float> factionReputation;

        [Header("Magic")]
        public List<PlayerSpell> learnedSpells;
        public int magicPoints;

        [Header("Economy")]
        public Dictionary<string, int> currencies;

        [Header("Map")]
        public List<Vector2Int> exploredAreas;
        public List<MapMarker> mapMarkers;

        [Header("Time")]
        public float gameTime;
        public int dayCount;
    }
    #endregion
}
