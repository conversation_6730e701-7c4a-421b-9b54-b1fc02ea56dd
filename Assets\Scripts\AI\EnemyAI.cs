using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Base Enemy AI component that integrates with the Reactive AI System.
    /// Provides adaptive behavior based on player patterns.
    /// </summary>
    [RequireComponent(typeof(NavMeshAgent))]
    public class EnemyAI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("AI Settings")]
        [SerializeField] private EnemyType enemyType = EnemyType.Melee;
        [SerializeField] private float detectionRange = 10f;
        [SerializeField] private float attackRange = 2f;
        [SerializeField] private float patrolRadius = 5f;
        [SerializeField] private float maxHealth = 100f;

        [Header("Combat Settings")]
        [SerializeField] private float baseDamage = 20f;
        [SerializeField] private float attackCooldown = 2f;
        [SerializeField] private float blockDuration = 1f;
        [SerializeField] private float dodgeDistance = 3f;

        [Header("Adaptation Settings")]
        [SerializeField] private bool enableAdaptation = true;
        [SerializeField] private float adaptationSensitivity = 1f;
        [SerializeField] private LayerMask playerLayer = 1;

        [Head<PERSON>("Debug")]
        [SerializeField] private bool showDebugGizmos = false;
        #endregion

        #region Public Properties
        public EnemyState CurrentState { get; private set; } = EnemyState.Patrol;
        public float CurrentHealth { get; private set; }
        public bool IsAlive => CurrentHealth > 0f;
        public Transform Target { get; private set; }
        #endregion

        #region Private Fields
        private NavMeshAgent navAgent;
        private Animator animator;
        private ReactiveAISystem reactiveAI;
        private AIAdaptationData adaptationData;

        // Combat tracking
        private float lastAttackTime;
        private float lastBlockTime;
        private bool isBlocking;
        private bool isDodging;
        private float combatStartTime;

        // Patrol system
        private Vector3 originalPosition;
        private Vector3 currentPatrolTarget;
        private float lastPatrolTime;

        // State machine
        private Coroutine currentStateCoroutine;
        private float stateChangeTime;

        // Adaptation tracking
        private int playerDodgeCount;
        private int playerBlockCount;
        private int playerAttackCount;
        private float lastAdaptationUpdate;
        #endregion

        #region Events
        public System.Action<EnemyAI> OnEnemyDefeated;
        public System.Action<EnemyAI, float> OnDamageTaken;
        public System.Action<EnemyAI, float> OnDamageDealt;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            navAgent = GetComponent<NavMeshAgent>();
            animator = GetComponent<Animator>();
            CurrentHealth = maxHealth;
            originalPosition = transform.position;
        }

        private void Start()
        {
            reactiveAI = ReactiveAISystem.Instance;
            if (reactiveAI != null && enableAdaptation)
            {
                adaptationData = new AIAdaptationData();
                reactiveAI.OnBehaviorProfileChanged += OnPlayerBehaviorChanged;
            }

            ChangeState(EnemyState.Patrol);
        }

        private void Update()
        {
            if (!IsAlive) return;

            UpdateAdaptation();
            UpdateStateLogic();
            UpdateAnimations();
        }

        private void OnDestroy()
        {
            if (reactiveAI != null)
            {
                reactiveAI.OnBehaviorProfileChanged -= OnPlayerBehaviorChanged;
            }
        }
        #endregion

        #region State Machine
        /// <summary>
        /// Change AI state and start appropriate behavior.
        /// </summary>
        private void ChangeState(EnemyState newState)
        {
            if (CurrentState == newState) return;

            // Stop current state coroutine
            if (currentStateCoroutine != null)
            {
                StopCoroutine(currentStateCoroutine);
            }

            CurrentState = newState;
            stateChangeTime = Time.time;

            // Start new state behavior
            switch (newState)
            {
                case EnemyState.Patrol:
                    currentStateCoroutine = StartCoroutine(PatrolBehavior());
                    break;
                case EnemyState.Alert:
                    currentStateCoroutine = StartCoroutine(AlertBehavior());
                    break;
                case EnemyState.Combat:
                    currentStateCoroutine = StartCoroutine(CombatBehavior());
                    break;
                case EnemyState.Stunned:
                    currentStateCoroutine = StartCoroutine(StunnedBehavior());
                    break;
                case EnemyState.Dead:
                    currentStateCoroutine = StartCoroutine(DeathBehavior());
                    break;
            }
        }

        /// <summary>
        /// Update state logic and transitions.
        /// </summary>
        private void UpdateStateLogic()
        {
            if (!IsAlive && CurrentState != EnemyState.Dead)
            {
                ChangeState(EnemyState.Dead);
                return;
            }

            // Check for player detection
            Transform player = FindPlayer();

            switch (CurrentState)
            {
                case EnemyState.Patrol:
                    if (player != null && IsPlayerInRange(player, detectionRange * adaptationData.detectionRange))
                    {
                        Target = player;
                        ChangeState(EnemyState.Alert);
                    }
                    break;

                case EnemyState.Alert:
                    if (player != null && IsPlayerInRange(player, attackRange))
                    {
                        ChangeState(EnemyState.Combat);
                        combatStartTime = Time.time;
                    }
                    else if (player == null || !IsPlayerInRange(player, detectionRange * 1.5f))
                    {
                        Target = null;
                        ChangeState(EnemyState.Patrol);
                    }
                    break;

                case EnemyState.Combat:
                    if (player == null || !IsPlayerInRange(player, detectionRange * 2f))
                    {
                        Target = null;
                        ChangeState(EnemyState.Alert);
                    }
                    break;
            }
        }
        #endregion

        #region State Behaviors
        /// <summary>
        /// Patrol behavior coroutine.
        /// </summary>
        private IEnumerator PatrolBehavior()
        {
            while (CurrentState == EnemyState.Patrol)
            {
                // Move to patrol point
                if (Vector3.Distance(transform.position, currentPatrolTarget) < 1f ||
                    Time.time - lastPatrolTime > 10f)
                {
                    SetNewPatrolTarget();
                    lastPatrolTime = Time.time;
                }

                navAgent.SetDestination(currentPatrolTarget);
                navAgent.speed = 2f * adaptationData.movementSpeed;

                yield return new WaitForSeconds(0.1f);
            }
        }

        /// <summary>
        /// Alert behavior coroutine.
        /// </summary>
        private IEnumerator AlertBehavior()
        {
            while (CurrentState == EnemyState.Alert && Target != null)
            {
                // Move towards player
                Vector3 targetPosition = Target.position;

                // Apply zigzag movement if adapted
                if (adaptationData.zigzagMovement > 0.5f)
                {
                    float zigzagOffset = Mathf.Sin(Time.time * 3f) * 2f;
                    Vector3 rightVector = Vector3.Cross(Vector3.up, (targetPosition - transform.position).normalized);
                    targetPosition += rightVector * zigzagOffset;
                }

                navAgent.SetDestination(targetPosition);
                navAgent.speed = 4f * adaptationData.movementSpeed;

                yield return new WaitForSeconds(0.1f);
            }
        }

        /// <summary>
        /// Combat behavior coroutine.
        /// </summary>
        private IEnumerator CombatBehavior()
        {
            while (CurrentState == EnemyState.Combat && Target != null && IsAlive)
            {
                float distanceToTarget = Vector3.Distance(transform.position, Target.position);

                // Combat decision making based on adaptations
                if (distanceToTarget <= attackRange && CanAttack())
                {
                    yield return StartCoroutine(PerformAttack());
                }
                else if (ShouldBlock())
                {
                    yield return StartCoroutine(PerformBlock());
                }
                else if (ShouldDodge())
                {
                    yield return StartCoroutine(PerformDodge());
                }
                else
                {
                    // Move towards target
                    navAgent.SetDestination(Target.position);
                    navAgent.speed = 3f * adaptationData.movementSpeed;
                }

                yield return new WaitForSeconds(0.1f);
            }
        }

        /// <summary>
        /// Stunned behavior coroutine.
        /// </summary>
        private IEnumerator StunnedBehavior()
        {
            navAgent.isStopped = true;
            yield return new WaitForSeconds(2f);

            if (IsAlive)
            {
                navAgent.isStopped = false;
                ChangeState(Target != null ? EnemyState.Combat : EnemyState.Patrol);
            }
        }

        /// <summary>
        /// Death behavior coroutine.
        /// </summary>
        private IEnumerator DeathBehavior()
        {
            navAgent.isStopped = true;

            // Record combat encounter
            if (reactiveAI != null)
            {
                float combatDuration = Time.time - combatStartTime;
                reactiveAI.RecordCombatEncounter(enemyType.ToString(), false, combatDuration);
            }

            OnEnemyDefeated?.Invoke(this);

            // Death animation and cleanup
            yield return new WaitForSeconds(3f);

            gameObject.SetActive(false);
        }
        #endregion

        #region Combat Actions
        /// <summary>
        /// Perform attack action.
        /// </summary>
        private IEnumerator PerformAttack()
        {
            lastAttackTime = Time.time;
            navAgent.isStopped = true;

            // Face target
            Vector3 lookDirection = (Target.position - transform.position).normalized;
            transform.rotation = Quaternion.LookRotation(lookDirection);

            // Attack animation and damage
            yield return new WaitForSeconds(0.5f);

            // Apply damage if still in range
            if (Vector3.Distance(transform.position, Target.position) <= attackRange)
            {
                float damage = baseDamage * adaptationData.aggressionLevel;
                ApplyDamageToPlayer(damage);
                OnDamageDealt?.Invoke(this, damage);
            }

            navAgent.isStopped = false;
            yield return new WaitForSeconds(attackCooldown / adaptationData.attackFrequency);
        }

        /// <summary>
        /// Perform block action.
        /// </summary>
        private IEnumerator PerformBlock()
        {
            isBlocking = true;
            lastBlockTime = Time.time;
            navAgent.isStopped = true;

            yield return new WaitForSeconds(blockDuration);

            isBlocking = false;
            navAgent.isStopped = false;
        }

        /// <summary>
        /// Perform dodge action.
        /// </summary>
        private IEnumerator PerformDodge()
        {
            isDodging = true;
            navAgent.isStopped = true;

            // Calculate dodge direction
            Vector3 dodgeDirection = GetDodgeDirection();
            Vector3 dodgeTarget = transform.position + dodgeDirection * dodgeDistance;

            // Perform dodge movement
            float dodgeTime = 0.5f;
            float elapsedTime = 0f;
            Vector3 startPosition = transform.position;

            while (elapsedTime < dodgeTime)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / dodgeTime;
                transform.position = Vector3.Lerp(startPosition, dodgeTarget, progress);
                yield return null;
            }

            isDodging = false;
            navAgent.isStopped = false;
        }
        #endregion

        #region Combat Decision Making
        /// <summary>
        /// Check if enemy can attack.
        /// </summary>
        private bool CanAttack()
        {
            return Time.time - lastAttackTime >= attackCooldown / adaptationData.attackFrequency;
        }

        /// <summary>
        /// Check if enemy should block.
        /// </summary>
        private bool ShouldBlock()
        {
            if (isBlocking || Time.time - lastBlockTime < 3f) return false;

            // Increase block chance based on adaptation
            float blockChance = adaptationData.blockChance;

            // Increase chance if player is aggressive
            if (reactiveAI != null && reactiveAI.CurrentBehaviorProfile.aggressiveness > 0.7f)
            {
                blockChance *= 1.5f;
            }

            return Random.value < blockChance;
        }

        /// <summary>
        /// Check if enemy should dodge.
        /// </summary>
        private bool ShouldDodge()
        {
            if (isDodging) return false;

            float dodgeChance = adaptationData.dodgeChance;

            // Increase dodge chance against ranged players
            if (reactiveAI != null && reactiveAI.CurrentBehaviorProfile.rangedPreference > 0.7f)
            {
                dodgeChance *= 2f;
            }

            return Random.value < dodgeChance;
        }

        /// <summary>
        /// Get dodge direction based on player position.
        /// </summary>
        private Vector3 GetDodgeDirection()
        {
            if (Target == null) return Random.insideUnitSphere.normalized;

            Vector3 toPlayer = (Target.position - transform.position).normalized;
            Vector3 rightVector = Vector3.Cross(Vector3.up, toPlayer);

            // Choose left or right dodge
            return Random.value > 0.5f ? rightVector : -rightVector;
        }
        #endregion

        #region Adaptation System
        /// <summary>
        /// Update adaptation based on player behavior.
        /// </summary>
        private void UpdateAdaptation()
        {
            if (!enableAdaptation || reactiveAI == null) return;
            if (Time.time - lastAdaptationUpdate < 1f) return;

            // Apply difficulty multiplier
            float difficultyMultiplier = reactiveAI.GetDifficultyMultiplier();
            ApplyDifficultyScaling(difficultyMultiplier);

            lastAdaptationUpdate = Time.time;
        }

        /// <summary>
        /// Handle player behavior changes.
        /// </summary>
        private void OnPlayerBehaviorChanged(PlayerBehaviorProfile behaviorProfile)
        {
            if (!enableAdaptation) return;

            // Adapt based on dominant behavior
            switch (behaviorProfile.dominantBehavior)
            {
                case BehaviorType.Aggressive:
                    adaptationData.blockChance = Mathf.Min(0.8f, adaptationData.blockChance + 0.1f);
                    adaptationData.counterAttackChance = Mathf.Min(0.7f, adaptationData.counterAttackChance + 0.1f);
                    break;

                case BehaviorType.Ranged:
                    adaptationData.movementSpeed = Mathf.Min(2f, adaptationData.movementSpeed + 0.1f);
                    adaptationData.zigzagMovement = Mathf.Min(1f, adaptationData.zigzagMovement + 0.2f);
                    break;

                case BehaviorType.Stealth:
                    adaptationData.detectionRange = Mathf.Min(2f, adaptationData.detectionRange + 0.1f);
                    adaptationData.alertnessLevel = Mathf.Min(2f, adaptationData.alertnessLevel + 0.1f);
                    break;
            }
        }

        /// <summary>
        /// Apply difficulty scaling to enemy stats.
        /// </summary>
        private void ApplyDifficultyScaling(float multiplier)
        {
            // Scale health
            maxHealth = maxHealth * multiplier;
            if (CurrentHealth == maxHealth) // Only scale if at full health
            {
                CurrentHealth = maxHealth;
            }

            // Scale damage
            baseDamage = baseDamage * multiplier;

            // Scale movement speed
            if (navAgent != null)
            {
                navAgent.speed *= multiplier;
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Find the player in the scene.
        /// </summary>
        private Transform FindPlayer()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            return player != null ? player.transform : null;
        }

        /// <summary>
        /// Check if player is within specified range.
        /// </summary>
        private bool IsPlayerInRange(Transform player, float range)
        {
            return Vector3.Distance(transform.position, player.position) <= range;
        }

        /// <summary>
        /// Set new patrol target within patrol radius.
        /// </summary>
        private void SetNewPatrolTarget()
        {
            Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
            randomDirection += originalPosition;

            NavMeshHit hit;
            if (NavMesh.SamplePosition(randomDirection, out hit, patrolRadius, 1))
            {
                currentPatrolTarget = hit.position;
            }
            else
            {
                currentPatrolTarget = originalPosition;
            }
        }

        /// <summary>
        /// Apply damage to player.
        /// </summary>
        private void ApplyDamageToPlayer(float damage)
        {
            if (Target == null) return;

            PlayerHealth playerHealth = Target.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
            }
        }

        /// <summary>
        /// Update animation parameters.
        /// </summary>
        private void UpdateAnimations()
        {
            if (animator == null) return;

            // Set animation parameters based on state and movement
            animator.SetFloat("Speed", navAgent.velocity.magnitude);
            animator.SetBool("IsBlocking", isBlocking);
            animator.SetBool("IsDodging", isDodging);
            animator.SetInteger("State", (int)CurrentState);
        }
        #endregion

        #region Public API
        /// <summary>
        /// Take damage from external source.
        /// </summary>
        public void TakeDamage(float damage, Vector3 hitPoint = default)
        {
            if (!IsAlive) return;

            // Apply damage reduction if blocking
            if (isBlocking)
            {
                damage *= 0.3f; // 70% damage reduction when blocking
            }

            CurrentHealth = Mathf.Max(0f, CurrentHealth - damage);
            OnDamageTaken?.Invoke(this, damage);

            // Trigger combat state if not already in combat
            if (CurrentState != EnemyState.Combat && CurrentState != EnemyState.Dead)
            {
                Transform player = FindPlayer();
                if (player != null)
                {
                    Target = player;
                    ChangeState(EnemyState.Combat);
                }
            }

            // Check for death
            if (CurrentHealth <= 0f)
            {
                ChangeState(EnemyState.Dead);
            }
        }

        /// <summary>
        /// Stun the enemy for a duration.
        /// </summary>
        public void Stun(float duration)
        {
            if (!IsAlive) return;

            ChangeState(EnemyState.Stunned);
            StartCoroutine(StunCoroutine(duration));
        }

        /// <summary>
        /// Stun coroutine.
        /// </summary>
        private IEnumerator StunCoroutine(float duration)
        {
            yield return new WaitForSeconds(duration);

            if (IsAlive && CurrentState == EnemyState.Stunned)
            {
                ChangeState(Target != null ? EnemyState.Combat : EnemyState.Patrol);
            }
        }

        /// <summary>
        /// Force enemy to target specific transform.
        /// </summary>
        public void SetTarget(Transform newTarget)
        {
            Target = newTarget;
            if (newTarget != null && IsAlive)
            {
                ChangeState(EnemyState.Alert);
            }
        }

        /// <summary>
        /// Get current adaptation data.
        /// </summary>
        public AIAdaptationData GetAdaptationData()
        {
            return adaptationData;
        }

        /// <summary>
        /// Set adaptation data manually.
        /// </summary>
        public void SetAdaptationData(AIAdaptationData newData)
        {
            adaptationData = newData;
        }

        /// <summary>
        /// Reset enemy to initial state.
        /// </summary>
        public void ResetEnemy()
        {
            CurrentHealth = maxHealth;
            Target = null;
            isBlocking = false;
            isDodging = false;
            adaptationData = new AIAdaptationData();

            transform.position = originalPosition;
            ChangeState(EnemyState.Patrol);
        }

        /// <summary>
        /// Get distance to target.
        /// </summary>
        public float GetDistanceToTarget()
        {
            return Target != null ? Vector3.Distance(transform.position, Target.position) : float.MaxValue;
        }

        /// <summary>
        /// Check if enemy can see target.
        /// </summary>
        public bool CanSeeTarget()
        {
            if (Target == null) return false;

            Vector3 directionToTarget = (Target.position - transform.position).normalized;
            float distanceToTarget = Vector3.Distance(transform.position, Target.position);

            // Raycast to check for obstacles
            RaycastHit hit;
            if (Physics.Raycast(transform.position + Vector3.up, directionToTarget, out hit, distanceToTarget))
            {
                return hit.collider.transform == Target;
            }

            return true;
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            if (!showDebugGizmos) return;

            // Detection range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);

            // Attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);

            // Patrol radius
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(originalPosition, patrolRadius);

            // Current patrol target
            if (Application.isPlaying)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawSphere(currentPatrolTarget, 0.5f);
                Gizmos.DrawLine(transform.position, currentPatrolTarget);
            }

            // Target line
            if (Target != null)
            {
                Gizmos.color = Color.magenta;
                Gizmos.DrawLine(transform.position, Target.position);
            }
        }
        #endregion
    }

    #region Enums
    public enum EnemyState
    {
        Patrol,
        Alert,
        Combat,
        Stunned,
        Dead
    }

    public enum EnemyType
    {
        Melee,
        Ranged,
        Magic,
        Heavy,
        Scout
    }
    #endregion
}
