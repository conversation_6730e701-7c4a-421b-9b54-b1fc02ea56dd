using UnityEngine;
using System.Collections.Generic;

namespace CinderOfDarkness.VFX
{
    /// <summary>
    /// GPU-friendly weather system for atmospheric effects.
    /// Handles fog, ash, sparks, and other environmental particles.
    /// </summary>
    public class WeatherSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Weather Particles")]
        [SerializeField] private ParticleSystem fogParticles;
        [SerializeField] private ParticleSystem ashParticles;
        [SerializeField] private ParticleSystem sparkParticles;
        [SerializeField] private ParticleSystem rainParticles;
        [SerializeField] private ParticleSystem snowParticles;

        [Header("Weather Settings")]
        [SerializeField] private float transitionSpeed = 1f;
        [SerializeField] private float maxParticleDistance = 50f;
        [SerializeField] private bool followPlayer = true;
        [SerializeField] private Transform playerTransform;

        [Header("Performance")]
        [SerializeField] private int maxParticles = 1000;
        [SerializeField] private bool useGPUInstancing = true;
        [SerializeField] private float updateInterval = 0.1f;
        [SerializeField] private bool enableLOD = true;
        [SerializeField] private float lodDistance = 30f;

        [Header("Wind")]
        [SerializeField] private Vector3 windDirection = Vector3.right;
        [SerializeField] private float windStrength = 1f;
        [SerializeField] private float windVariation = 0.5f;
        [SerializeField] private float windChangeSpeed = 0.5f;
        #endregion

        #region Private Fields
        private Dictionary<WeatherType, ParticleSystem> weatherParticles;
        private WeatherType currentWeather = WeatherType.Clear;
        private WeatherType targetWeather = WeatherType.Clear;
        private float transitionProgress = 1f;
        private float lastUpdateTime;
        private Vector3 currentWindDirection;
        private float currentWindStrength;
        private Camera playerCamera;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize weather system.
        /// </summary>
        public void Initialize()
        {
            SetupWeatherParticles();
            FindPlayerComponents();
            InitializeWind();
            SetWeatherImmediate(WeatherType.Clear);
        }

        /// <summary>
        /// Update weather system.
        /// </summary>
        public void UpdateWeather()
        {
            if (Time.time - lastUpdateTime < updateInterval) return;
            lastUpdateTime = Time.time;

            UpdateWeatherTransition();
            UpdateWind();
            UpdateParticlePositions();
            UpdatePerformanceOptimizations();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Setup weather particle systems dictionary.
        /// </summary>
        private void SetupWeatherParticles()
        {
            weatherParticles = new Dictionary<WeatherType, ParticleSystem>();

            if (fogParticles != null) weatherParticles[WeatherType.Fog] = fogParticles;
            if (ashParticles != null) weatherParticles[WeatherType.AshStorm] = ashParticles;
            if (sparkParticles != null) weatherParticles[WeatherType.Sparks] = sparkParticles;
            if (rainParticles != null) weatherParticles[WeatherType.Rain] = rainParticles;
            if (snowParticles != null) weatherParticles[WeatherType.Snow] = snowParticles;

            // Initialize all particle systems
            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null)
                {
                    ConfigureParticleSystem(kvp.Value, kvp.Key);
                    kvp.Value.Stop();
                }
            }
        }

        /// <summary>
        /// Find player components for following.
        /// </summary>
        private void FindPlayerComponents()
        {
            if (playerTransform == null)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    playerTransform = player.transform;
                }
            }

            if (playerCamera == null)
            {
                playerCamera = Camera.main;
                if (playerCamera == null)
                {
                    playerCamera = FindObjectOfType<Camera>();
                }
            }
        }

        /// <summary>
        /// Initialize wind system.
        /// </summary>
        private void InitializeWind()
        {
            currentWindDirection = windDirection.normalized;
            currentWindStrength = windStrength;
        }

        /// <summary>
        /// Configure particle system for specific weather type.
        /// </summary>
        /// <param name="particles">Particle system to configure</param>
        /// <param name="weatherType">Weather type</param>
        private void ConfigureParticleSystem(ParticleSystem particles, WeatherType weatherType)
        {
            var main = particles.main;
            var emission = particles.emission;
            var shape = particles.shape;
            var velocityOverLifetime = particles.velocityOverLifetime;

            // Common settings
            main.maxParticles = maxParticles;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(maxParticleDistance, 20f, maxParticleDistance);

            switch (weatherType)
            {
                case WeatherType.Fog:
                    main.startLifetime = 10f;
                    main.startSpeed = 0.5f;
                    main.startSize = 5f;
                    emission.rateOverTime = 20f;
                    break;

                case WeatherType.AshStorm:
                    main.startLifetime = 8f;
                    main.startSpeed = 3f;
                    main.startSize = 0.2f;
                    emission.rateOverTime = 100f;
                    break;

                case WeatherType.Sparks:
                    main.startLifetime = 2f;
                    main.startSpeed = 5f;
                    main.startSize = 0.1f;
                    emission.rateOverTime = 50f;
                    break;

                case WeatherType.Rain:
                    main.startLifetime = 3f;
                    main.startSpeed = 10f;
                    main.startSize = 0.05f;
                    emission.rateOverTime = 200f;
                    break;

                case WeatherType.Snow:
                    main.startLifetime = 15f;
                    main.startSpeed = 1f;
                    main.startSize = 0.3f;
                    emission.rateOverTime = 80f;
                    break;
            }

            // Enable GPU instancing if supported
            if (useGPUInstancing && SystemInfo.supportsInstancing)
            {
                var renderer = particles.GetComponent<ParticleSystemRenderer>();
                if (renderer != null)
                {
                    renderer.enableGPUInstancing = true;
                }
            }
        }
        #endregion

        #region Weather Control
        /// <summary>
        /// Transition to new weather type.
        /// </summary>
        /// <param name="newWeather">Target weather type</param>
        /// <param name="duration">Transition duration</param>
        public void TransitionToWeather(WeatherType newWeather, float duration = 5f)
        {
            if (newWeather == currentWeather) return;

            targetWeather = newWeather;
            transitionProgress = 0f;
            transitionSpeed = 1f / duration;

            // Start target weather particles
            if (weatherParticles.ContainsKey(targetWeather) && weatherParticles[targetWeather] != null)
            {
                weatherParticles[targetWeather].Play();
            }
        }

        /// <summary>
        /// Set weather immediately without transition.
        /// </summary>
        /// <param name="newWeather">Weather type to set</param>
        public void SetWeatherImmediate(WeatherType newWeather)
        {
            // Stop all weather particles
            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null)
                {
                    kvp.Value.Stop();
                }
            }

            currentWeather = newWeather;
            targetWeather = newWeather;
            transitionProgress = 1f;

            // Start new weather particles
            if (weatherParticles.ContainsKey(currentWeather) && weatherParticles[currentWeather] != null)
            {
                weatherParticles[currentWeather].Play();
            }
        }

        /// <summary>
        /// Update weather transition.
        /// </summary>
        private void UpdateWeatherTransition()
        {
            if (transitionProgress >= 1f) return;

            transitionProgress += transitionSpeed * Time.deltaTime;
            transitionProgress = Mathf.Clamp01(transitionProgress);

            // Update particle intensities during transition
            UpdateTransitionIntensities();

            // Complete transition
            if (transitionProgress >= 1f)
            {
                CompleteWeatherTransition();
            }
        }

        /// <summary>
        /// Update particle intensities during transition.
        /// </summary>
        private void UpdateTransitionIntensities()
        {
            // Fade out current weather
            if (weatherParticles.ContainsKey(currentWeather) && weatherParticles[currentWeather] != null)
            {
                var currentEmission = weatherParticles[currentWeather].emission;
                currentEmission.rateOverTimeMultiplier = 1f - transitionProgress;
            }

            // Fade in target weather
            if (weatherParticles.ContainsKey(targetWeather) && weatherParticles[targetWeather] != null)
            {
                var targetEmission = weatherParticles[targetWeather].emission;
                targetEmission.rateOverTimeMultiplier = transitionProgress;
            }
        }

        /// <summary>
        /// Complete weather transition.
        /// </summary>
        private void CompleteWeatherTransition()
        {
            // Stop old weather
            if (weatherParticles.ContainsKey(currentWeather) && weatherParticles[currentWeather] != null)
            {
                weatherParticles[currentWeather].Stop();
            }

            // Set new weather as current
            currentWeather = targetWeather;

            // Ensure new weather is at full intensity
            if (weatherParticles.ContainsKey(currentWeather) && weatherParticles[currentWeather] != null)
            {
                var emission = weatherParticles[currentWeather].emission;
                emission.rateOverTimeMultiplier = 1f;
            }
        }
        #endregion

        #region Wind System
        /// <summary>
        /// Update wind effects.
        /// </summary>
        private void UpdateWind()
        {
            // Vary wind direction and strength over time
            float windTime = Time.time * windChangeSpeed;
            Vector3 windVariationVector = new Vector3(
                Mathf.Sin(windTime) * windVariation,
                0f,
                Mathf.Cos(windTime * 0.7f) * windVariation
            );

            currentWindDirection = (windDirection + windVariationVector).normalized;
            currentWindStrength = windStrength + Mathf.Sin(windTime * 1.3f) * windVariation;

            // Apply wind to active particle systems
            ApplyWindToParticles();
        }

        /// <summary>
        /// Apply wind effects to active particle systems.
        /// </summary>
        private void ApplyWindToParticles()
        {
            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null && kvp.Value.isPlaying)
                {
                    var velocityOverLifetime = kvp.Value.velocityOverLifetime;
                    velocityOverLifetime.enabled = true;
                    velocityOverLifetime.space = ParticleSystemSimulationSpace.World;

                    Vector3 windVelocity = currentWindDirection * currentWindStrength;
                    velocityOverLifetime.x = windVelocity.x;
                    velocityOverLifetime.y = windVelocity.y;
                    velocityOverLifetime.z = windVelocity.z;
                }
            }
        }

        /// <summary>
        /// Set wind parameters.
        /// </summary>
        /// <param name="direction">Wind direction</param>
        /// <param name="strength">Wind strength</param>
        /// <param name="variation">Wind variation amount</param>
        public void SetWind(Vector3 direction, float strength, float variation = 0.5f)
        {
            windDirection = direction.normalized;
            windStrength = strength;
            windVariation = variation;
        }
        #endregion

        #region Performance Optimization
        /// <summary>
        /// Update particle positions to follow player.
        /// </summary>
        private void UpdateParticlePositions()
        {
            if (!followPlayer || playerTransform == null) return;

            Vector3 playerPosition = playerTransform.position;

            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null)
                {
                    kvp.Value.transform.position = playerPosition;
                }
            }
        }

        /// <summary>
        /// Update performance optimizations based on distance and settings.
        /// </summary>
        private void UpdatePerformanceOptimizations()
        {
            if (!enableLOD || playerCamera == null) return;

            float distanceToCamera = Vector3.Distance(transform.position, playerCamera.transform.position);
            float lodMultiplier = Mathf.Clamp01(1f - (distanceToCamera / lodDistance));

            // Adjust particle counts based on distance
            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null && kvp.Value.isPlaying)
                {
                    var emission = kvp.Value.emission;
                    emission.rateOverTimeMultiplier = lodMultiplier;
                }
            }
        }

        /// <summary>
        /// Set performance level for weather effects.
        /// </summary>
        /// <param name="level">Performance level (0-1, where 1 is highest quality)</param>
        public void SetPerformanceLevel(float level)
        {
            level = Mathf.Clamp01(level);
            
            int adjustedMaxParticles = Mathf.RoundToInt(maxParticles * level);
            
            foreach (var kvp in weatherParticles)
            {
                if (kvp.Value != null)
                {
                    var main = kvp.Value.main;
                    main.maxParticles = adjustedMaxParticles;
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get current weather type.
        /// </summary>
        /// <returns>Current weather type</returns>
        public WeatherType GetCurrentWeather()
        {
            return currentWeather;
        }

        /// <summary>
        /// Check if weather is transitioning.
        /// </summary>
        /// <returns>True if transitioning</returns>
        public bool IsTransitioning()
        {
            return transitionProgress < 1f;
        }

        /// <summary>
        /// Get transition progress.
        /// </summary>
        /// <returns>Transition progress (0-1)</returns>
        public float GetTransitionProgress()
        {
            return transitionProgress;
        }

        /// <summary>
        /// Enable or disable weather system.
        /// </summary>
        /// <param name="enabled">True to enable</param>
        public void SetEnabled(bool enabled)
        {
            if (enabled)
            {
                if (weatherParticles.ContainsKey(currentWeather) && weatherParticles[currentWeather] != null)
                {
                    weatherParticles[currentWeather].Play();
                }
            }
            else
            {
                foreach (var kvp in weatherParticles)
                {
                    if (kvp.Value != null)
                    {
                        kvp.Value.Stop();
                    }
                }
            }
        }
        #endregion
    }
}
