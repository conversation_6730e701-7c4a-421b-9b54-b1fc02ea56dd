using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using CinderOfDarkness.Input;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Comprehensive UI Manager for Cinder of Darkness.
    /// Manages all UI panels, navigation, animations, and accessibility features.
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI Panels")]
        [SerializeField] private UIPanel[] uiPanels;
        [SerializeField] private UIPanel defaultPanel;
        [SerializeField] private UIPanel pausePanel;

        [Header("Navigation")]
        [SerializeField] private bool enableControllerNavigation = true;
        [SerializeField] private float navigationRepeatDelay = 0.5f;
        [SerializeField] private float navigationRepeatRate = 0.1f;
        [SerializeField] private AudioClip navigationSound;
        [SerializeField] private AudioClip confirmSound;
        [SerializeField] private AudioClip cancelSound;

        [Header("Animation")]
        [SerializeField] private float defaultTransitionDuration = 0.3f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private bool useScaleAnimation = true;
        [SerializeField] private bool useFadeAnimation = true;

        [Header("Accessibility")]
        [SerializeField] private bool enableHighContrast = false;
        [SerializeField] private float textScaleMultiplier = 1f;
        [SerializeField] private bool enableColorBlindSupport = false;
        [SerializeField] private ColorBlindType colorBlindType = ColorBlindType.None;

        [Header("Audio")]
        [SerializeField] private AudioSource uiAudioSource;
        [SerializeField] private float uiVolume = 1f;
        #endregion

        #region Public Properties
        public static UIManager Instance { get; private set; }
        public UIPanel CurrentPanel { get; private set; }
        public bool IsAnyPanelOpen => CurrentPanel != null && CurrentPanel != defaultPanel;
        public bool IsPaused { get; private set; }
        #endregion

        #region Private Fields
        private Dictionary<string, UIPanel> panelDictionary = new Dictionary<string, UIPanel>();
        private Stack<UIPanel> panelHistory = new Stack<UIPanel>();
        private GamepadManager gamepadManager;
        private EnhancedButtonPromptSystem buttonPromptSystem;
        private float lastNavigationTime;
        private bool isTransitioning;
        private UITheme currentTheme;
        #endregion

        #region Events
        public System.Action<UIPanel> OnPanelOpened;
        public System.Action<UIPanel> OnPanelClosed;
        public System.Action<bool> OnPauseStateChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize UI Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeUIManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Start UI systems.
        /// </summary>
        private void Start()
        {
            InitializePanels();
            InitializeInputSystems();
            InitializeAudio();
            
            if (defaultPanel != null)
            {
                ShowPanel(defaultPanel.PanelId, false);
            }
        }

        /// <summary>
        /// Update UI navigation and input.
        /// </summary>
        private void Update()
        {
            HandleUIInput();
            UpdateNavigation();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize UI Manager systems.
        /// </summary>
        private void InitializeUIManager()
        {
            // Initialize panel dictionary
            panelDictionary.Clear();
            
            if (uiPanels != null)
            {
                foreach (UIPanel panel in uiPanels)
                {
                    if (panel != null && !string.IsNullOrEmpty(panel.PanelId))
                    {
                        panelDictionary[panel.PanelId] = panel;
                    }
                }
            }
        }

        /// <summary>
        /// Initialize all UI panels.
        /// </summary>
        private void InitializePanels()
        {
            foreach (var kvp in panelDictionary)
            {
                UIPanel panel = kvp.Value;
                if (panel != null)
                {
                    panel.Initialize();
                    panel.OnPanelOpened += OnPanelOpenedHandler;
                    panel.OnPanelClosed += OnPanelClosedHandler;
                    
                    // Hide all panels initially
                    panel.Hide(false);
                }
            }
        }

        /// <summary>
        /// Initialize input systems.
        /// </summary>
        private void InitializeInputSystems()
        {
            gamepadManager = GamepadManager.Instance;
            buttonPromptSystem = EnhancedButtonPromptSystem.Instance;

            if (gamepadManager != null)
            {
                gamepadManager.OnGamepadConnected += OnGamepadConnected;
                gamepadManager.OnGamepadDisconnected += OnGamepadDisconnected;
            }
        }

        /// <summary>
        /// Initialize audio systems.
        /// </summary>
        private void InitializeAudio()
        {
            if (uiAudioSource == null)
            {
                uiAudioSource = gameObject.AddComponent<AudioSource>();
            }

            uiAudioSource.volume = uiVolume;
            uiAudioSource.playOnAwake = false;
        }
        #endregion

        #region Panel Management
        /// <summary>
        /// Show a UI panel by ID.
        /// </summary>
        /// <param name="panelId">Panel ID to show</param>
        /// <param name="addToHistory">Whether to add to navigation history</param>
        /// <param name="animate">Whether to animate the transition</param>
        public void ShowPanel(string panelId, bool addToHistory = true, bool animate = true)
        {
            if (isTransitioning) return;

            if (!panelDictionary.ContainsKey(panelId))
            {
                Debug.LogWarning($"Panel with ID '{panelId}' not found");
                return;
            }

            UIPanel targetPanel = panelDictionary[panelId];
            ShowPanel(targetPanel, addToHistory, animate);
        }

        /// <summary>
        /// Show a UI panel.
        /// </summary>
        /// <param name="panel">Panel to show</param>
        /// <param name="addToHistory">Whether to add to navigation history</param>
        /// <param name="animate">Whether to animate the transition</param>
        public void ShowPanel(UIPanel panel, bool addToHistory = true, bool animate = true)
        {
            if (panel == null || isTransitioning) return;

            StartCoroutine(ShowPanelCoroutine(panel, addToHistory, animate));
        }

        /// <summary>
        /// Hide the current panel.
        /// </summary>
        /// <param name="animate">Whether to animate the transition</param>
        public void HideCurrentPanel(bool animate = true)
        {
            if (CurrentPanel != null && CurrentPanel != defaultPanel)
            {
                StartCoroutine(HidePanelCoroutine(CurrentPanel, animate));
            }
        }

        /// <summary>
        /// Go back to the previous panel in history.
        /// </summary>
        public void GoBack()
        {
            if (panelHistory.Count > 0)
            {
                UIPanel previousPanel = panelHistory.Pop();
                ShowPanel(previousPanel, false, true);
            }
            else if (defaultPanel != null)
            {
                ShowPanel(defaultPanel, false, true);
            }
        }

        /// <summary>
        /// Show panel coroutine with animation support.
        /// </summary>
        /// <param name="panel">Panel to show</param>
        /// <param name="addToHistory">Add to history</param>
        /// <param name="animate">Animate transition</param>
        /// <returns>Coroutine</returns>
        private System.Collections.IEnumerator ShowPanelCoroutine(UIPanel panel, bool addToHistory, bool animate)
        {
            isTransitioning = true;

            // Add current panel to history
            if (addToHistory && CurrentPanel != null && CurrentPanel != panel)
            {
                panelHistory.Push(CurrentPanel);
            }

            // Hide current panel
            if (CurrentPanel != null && CurrentPanel != panel)
            {
                yield return StartCoroutine(HidePanelCoroutine(CurrentPanel, animate));
            }

            // Show new panel
            CurrentPanel = panel;
            
            if (animate)
            {
                yield return StartCoroutine(panel.ShowAnimated(defaultTransitionDuration));
            }
            else
            {
                panel.Show(false);
            }

            // Update button prompts
            UpdateButtonPrompts();

            // Play sound
            PlayUISound(confirmSound);

            OnPanelOpened?.Invoke(panel);
            isTransitioning = false;
        }

        /// <summary>
        /// Hide panel coroutine with animation support.
        /// </summary>
        /// <param name="panel">Panel to hide</param>
        /// <param name="animate">Animate transition</param>
        /// <returns>Coroutine</returns>
        private System.Collections.IEnumerator HidePanelCoroutine(UIPanel panel, bool animate)
        {
            if (animate)
            {
                yield return StartCoroutine(panel.HideAnimated(defaultTransitionDuration));
            }
            else
            {
                panel.Hide(false);
            }

            OnPanelClosed?.Invoke(panel);
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle UI input.
        /// </summary>
        private void HandleUIInput()
        {
            // Handle pause input
            if (Input.GetKeyDown(KeyCode.Escape) || 
                (gamepadManager?.CurrentGamepad?.startButton.wasPressedThisFrame ?? false))
            {
                TogglePause();
            }

            // Handle back navigation
            if (Input.GetKeyDown(KeyCode.Backspace) || 
                (gamepadManager?.CurrentGamepad?.selectButton.wasPressedThisFrame ?? false))
            {
                GoBack();
                PlayUISound(cancelSound);
            }
        }

        /// <summary>
        /// Update controller navigation.
        /// </summary>
        private void UpdateNavigation()
        {
            if (!enableControllerNavigation || gamepadManager?.CurrentGamepad == null) return;

            // Handle navigation input with repeat
            Vector2 navigationInput = gamepadManager.CurrentGamepad.leftStick.ReadValue();
            
            if (navigationInput.magnitude > 0.5f)
            {
                if (Time.time - lastNavigationTime > navigationRepeatDelay)
                {
                    HandleNavigationInput(navigationInput);
                    lastNavigationTime = Time.time;
                }
            }
        }

        /// <summary>
        /// Handle navigation input.
        /// </summary>
        /// <param name="input">Navigation input vector</param>
        private void HandleNavigationInput(Vector2 input)
        {
            if (CurrentPanel == null) return;

            // Determine navigation direction
            if (Mathf.Abs(input.x) > Mathf.Abs(input.y))
            {
                // Horizontal navigation
                if (input.x > 0)
                {
                    CurrentPanel.NavigateRight();
                }
                else
                {
                    CurrentPanel.NavigateLeft();
                }
            }
            else
            {
                // Vertical navigation
                if (input.y > 0)
                {
                    CurrentPanel.NavigateUp();
                }
                else
                {
                    CurrentPanel.NavigateDown();
                }
            }

            PlayUISound(navigationSound);
        }

        /// <summary>
        /// Handle gamepad connection.
        /// </summary>
        /// <param name="gamepadType">Connected gamepad type</param>
        private void OnGamepadConnected(GamepadType gamepadType)
        {
            UpdateButtonPrompts();
        }

        /// <summary>
        /// Handle gamepad disconnection.
        /// </summary>
        /// <param name="gamepadType">Disconnected gamepad type</param>
        private void OnGamepadDisconnected(GamepadType gamepadType)
        {
            UpdateButtonPrompts();
        }
        #endregion

        #region Pause System
        /// <summary>
        /// Toggle pause state.
        /// </summary>
        public void TogglePause()
        {
            SetPaused(!IsPaused);
        }

        /// <summary>
        /// Set pause state.
        /// </summary>
        /// <param name="paused">True to pause</param>
        public void SetPaused(bool paused)
        {
            if (IsPaused == paused) return;

            IsPaused = paused;

            if (paused)
            {
                Time.timeScale = 0f;
                if (pausePanel != null)
                {
                    ShowPanel(pausePanel, true, true);
                }
            }
            else
            {
                Time.timeScale = 1f;
                if (CurrentPanel == pausePanel)
                {
                    GoBack();
                }
            }

            OnPauseStateChanged?.Invoke(IsPaused);
        }
        #endregion

        #region Button Prompts
        /// <summary>
        /// Update button prompts based on current context.
        /// </summary>
        private void UpdateButtonPrompts()
        {
            if (buttonPromptSystem == null) return;

            // Clear existing prompts
            buttonPromptSystem.HideAllPrompts();

            // Show context-appropriate prompts
            if (CurrentPanel != null)
            {
                if (CurrentPanel.CanGoBack)
                {
                    buttonPromptSystem.ShowPrompt("back", "Back", "Backspace", "B", -1f, 5);
                }

                if (CurrentPanel.HasConfirmAction)
                {
                    buttonPromptSystem.ShowPrompt("confirm", "Confirm", "Enter", "A", -1f, 10);
                }
            }

            // Always show pause prompt in game
            if (CurrentPanel == defaultPanel)
            {
                buttonPromptSystem.ShowPrompt("pause", "Pause", "Escape", "Menu", -1f, 1);
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play UI sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlayUISound(AudioClip clip)
        {
            if (clip != null && uiAudioSource != null)
            {
                uiAudioSource.PlayOneShot(clip, uiVolume);
            }
        }

        /// <summary>
        /// Set UI volume.
        /// </summary>
        /// <param name="volume">Volume level (0-1)</param>
        public void SetUIVolume(float volume)
        {
            uiVolume = Mathf.Clamp01(volume);
            if (uiAudioSource != null)
            {
                uiAudioSource.volume = uiVolume;
            }
        }
        #endregion

        #region Accessibility
        /// <summary>
        /// Set high contrast mode.
        /// </summary>
        /// <param name="enabled">True to enable high contrast</param>
        public void SetHighContrast(bool enabled)
        {
            enableHighContrast = enabled;
            ApplyAccessibilitySettings();
        }

        /// <summary>
        /// Set text scale multiplier.
        /// </summary>
        /// <param name="scale">Text scale multiplier</param>
        public void SetTextScale(float scale)
        {
            textScaleMultiplier = Mathf.Clamp(scale, 0.5f, 2f);
            ApplyAccessibilitySettings();
        }

        /// <summary>
        /// Set color blind support.
        /// </summary>
        /// <param name="type">Color blind type</param>
        public void SetColorBlindSupport(ColorBlindType type)
        {
            colorBlindType = type;
            enableColorBlindSupport = type != ColorBlindType.None;
            ApplyAccessibilitySettings();
        }

        /// <summary>
        /// Apply accessibility settings to all UI elements.
        /// </summary>
        private void ApplyAccessibilitySettings()
        {
            foreach (var kvp in panelDictionary)
            {
                UIPanel panel = kvp.Value;
                if (panel != null)
                {
                    panel.ApplyAccessibilitySettings(enableHighContrast, textScaleMultiplier, colorBlindType);
                }
            }
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handle panel opened event.
        /// </summary>
        /// <param name="panel">Opened panel</param>
        private void OnPanelOpenedHandler(UIPanel panel)
        {
            UpdateButtonPrompts();
        }

        /// <summary>
        /// Handle panel closed event.
        /// </summary>
        /// <param name="panel">Closed panel</param>
        private void OnPanelClosedHandler(UIPanel panel)
        {
            UpdateButtonPrompts();
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get panel by ID.
        /// </summary>
        /// <param name="panelId">Panel ID</param>
        /// <returns>UI Panel or null</returns>
        public UIPanel GetPanel(string panelId)
        {
            return panelDictionary.ContainsKey(panelId) ? panelDictionary[panelId] : null;
        }

        /// <summary>
        /// Check if panel is open.
        /// </summary>
        /// <param name="panelId">Panel ID</param>
        /// <returns>True if panel is open</returns>
        public bool IsPanelOpen(string panelId)
        {
            return CurrentPanel != null && CurrentPanel.PanelId == panelId;
        }

        /// <summary>
        /// Close all panels and return to default.
        /// </summary>
        public void CloseAllPanels()
        {
            panelHistory.Clear();
            if (defaultPanel != null)
            {
                ShowPanel(defaultPanel, false, true);
            }
        }
        #endregion

        #region Cleanup
        /// <summary>
        /// Clean up UI Manager.
        /// </summary>
        private void OnDestroy()
        {
            if (gamepadManager != null)
            {
                gamepadManager.OnGamepadConnected -= OnGamepadConnected;
                gamepadManager.OnGamepadDisconnected -= OnGamepadDisconnected;
            }

            foreach (var kvp in panelDictionary)
            {
                UIPanel panel = kvp.Value;
                if (panel != null)
                {
                    panel.OnPanelOpened -= OnPanelOpenedHandler;
                    panel.OnPanelClosed -= OnPanelClosedHandler;
                }
            }
        }
        #endregion
    }

    #region Enums
    /// <summary>
    /// Color blind support types.
    /// </summary>
    public enum ColorBlindType
    {
        None,
        Protanopia,
        Deuteranopia,
        Tritanopia
    }

    /// <summary>
    /// UI Theme types.
    /// </summary>
    public enum UITheme
    {
        Default,
        HighContrast,
        Dark,
        Light
    }
    #endregion
}
