using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

/// <summary>
/// Arena Manager for Cinder of Darkness
/// Handles loading, managing, and registering custom arenas for Boss Rush and Trials
/// </summary>
public class ArenaManager : MonoBehaviour
{
    [Header("Arena Management")]
    public string arenasFolder = "Mods/Arenas";
    public string userArenasFolder = "Mods/Arenas/User";
    public string communityArenasFolder = "Mods/Arenas/Community";
    public bool enableCommunityMods = true;
    public bool enableAutoLoad = true;

    [Header("Arena Browser UI")]
    public GameObject arenaBrowserUI;
    public Transform arenaListParent;
    public GameObject arenaEntryPrefab;
    public TMPro.TMP_Dropdown filterDropdown;
    public TMPro.TMP_InputField searchField;

    // Static instance
    private static ArenaManager instance;
    public static ArenaManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaManager");
                    instance = go.AddComponent<ArenaManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Arena management
    private Dictionary<string, LoadedArena> loadedArenas = new Dictionary<string, LoadedArena>();
    private List<string> arenaCategories = new List<string>();
    private string currentFilter = "All";
    private string currentSearch = "";

    [System.Serializable]
    public class LoadedArena
    {
        public string arenaId;
        public string arenaName;
        public string author;
        public string version;
        public string description;
        public ArenaData.BiomeType biomeType;
        public ArenaData.WinConditionType winCondition;
        public string filePath;
        public ArenaData arenaData;
        public bool isUserCreated;
        public bool isCommunityMod;
        public string[] tags;
        public Texture2D thumbnail;
        public System.DateTime lastModified;
    }

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeArenaManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        CreateArenaDirectories();
        SetupUI();

        if (enableAutoLoad)
        {
            LoadAllArenas();
        }
    }

    void InitializeArenaManager()
    {
        Debug.Log("Arena Manager initialized");
    }

    void CreateArenaDirectories()
    {
        string basePath = Application.persistentDataPath;

        // Create main arenas directory
        string arenasPath = Path.Combine(basePath, arenasFolder);
        if (!Directory.Exists(arenasPath))
        {
            Directory.CreateDirectory(arenasPath);
        }

        // Create user arenas directory
        string userPath = Path.Combine(basePath, userArenasFolder);
        if (!Directory.Exists(userPath))
        {
            Directory.CreateDirectory(userPath);
        }

        // Create community arenas directory
        string communityPath = Path.Combine(basePath, communityArenasFolder);
        if (!Directory.Exists(communityPath))
        {
            Directory.CreateDirectory(communityPath);
        }

        Debug.Log($"Arena directories created at: {arenasPath}");
    }

    void SetupUI()
    {
        if (arenaBrowserUI != null)
            arenaBrowserUI.SetActive(false);

        if (filterDropdown != null)
        {
            SetupFilterDropdown();
            filterDropdown.onValueChanged.AddListener(OnFilterChanged);
        }

        if (searchField != null)
        {
            searchField.onValueChanged.AddListener(OnSearchChanged);
        }
    }

    void SetupFilterDropdown()
    {
        arenaCategories = new List<string> { "All", "User Created", "Community", "Survival", "Timed", "Combat", "Narrative" };

        filterDropdown.ClearOptions();
        filterDropdown.AddOptions(arenaCategories);
    }

    // Public API
    public static void LoadAllArenas()
    {
        Instance.LoadArenasInternal();
    }

    public static LoadedArena[] GetLoadedArenas()
    {
        return Instance.loadedArenas.Values.ToArray();
    }

    public static LoadedArena[] GetFilteredArenas(string filter = "All", string search = "")
    {
        return Instance.GetFilteredArenasInternal(filter, search);
    }

    public static LoadedArena GetArena(string arenaId)
    {
        return Instance.loadedArenas.ContainsKey(arenaId) ? Instance.loadedArenas[arenaId] : null;
    }

    public static void RegisterArenaForBossRush(string arenaId)
    {
        Instance.RegisterArenaForBossRushInternal(arenaId);
    }

    public static void OpenArenaBrowser()
    {
        Instance.OpenArenaBrowserInternal();
    }

    public static void CloseArenaBrowser()
    {
        Instance.CloseArenaBrowserInternal();
    }

    public static string ImportArenaFromFile(string filePath)
    {
        return Instance.ImportArenaFromFileInternal(filePath);
    }

    public static bool ExportArenaToFile(string arenaId, string exportPath)
    {
        return Instance.ExportArenaToFileInternal(arenaId, exportPath);
    }

    void LoadArenasInternal()
    {
        loadedArenas.Clear();

        string basePath = Application.persistentDataPath;

        // Load user arenas
        LoadArenasFromDirectory(Path.Combine(basePath, userArenasFolder), true, false);

        // Load community arenas
        if (enableCommunityMods)
        {
            LoadArenasFromDirectory(Path.Combine(basePath, communityArenasFolder), false, true);
        }

        // Load default arenas
        LoadArenasFromDirectory(Path.Combine(basePath, arenasFolder), false, false);

        // Register arenas with other systems
        RegisterArenasWithSystems();

        Debug.Log($"Loaded {loadedArenas.Count} arenas");
    }

    void LoadArenasFromDirectory(string directoryPath, bool isUserCreated, bool isCommunityMod)
    {
        if (!Directory.Exists(directoryPath)) return;

        string[] arenaFiles = Directory.GetFiles(directoryPath, "*.arenamod", SearchOption.AllDirectories);

        foreach (string file in arenaFiles)
        {
            try
            {
                LoadArenaFromFile(file, isUserCreated, isCommunityMod);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load arena from {file}: {e.Message}");
            }
        }
    }

    void LoadArenaFromFile(string filePath, bool isUserCreated, bool isCommunityMod)
    {
        string json = File.ReadAllText(filePath);
        var arenaJson = JsonConvert.DeserializeObject<ArenaDataJson>(json);

        if (arenaJson?.arenaData == null)
        {
            Debug.LogWarning($"Invalid arena file: {filePath}");
            return;
        }

        var loadedArena = new LoadedArena
        {
            arenaId = arenaJson.modId,
            arenaName = arenaJson.name,
            author = arenaJson.author,
            version = arenaJson.version,
            description = arenaJson.description,
            biomeType = arenaJson.arenaData.biomeType,
            winCondition = arenaJson.arenaData.winCondition,
            filePath = filePath,
            arenaData = arenaJson.arenaData,
            isUserCreated = isUserCreated,
            isCommunityMod = isCommunityMod,
            tags = GenerateArenaTags(arenaJson.arenaData),
            lastModified = File.GetLastWriteTime(filePath)
        };

        // Load thumbnail if exists
        LoadArenaThumbnail(loadedArena);

        loadedArenas[loadedArena.arenaId] = loadedArena;

        Debug.Log($"Loaded arena: {loadedArena.arenaName} by {loadedArena.author}");
    }

    string[] GenerateArenaTags(ArenaData arena)
    {
        List<string> tags = new List<string>();

        // Add biome tag
        tags.Add(arena.biomeType.ToString());

        // Add win condition tag
        switch (arena.winCondition)
        {
            case ArenaData.WinConditionType.SurviveTimeLimit:
                tags.Add("Survival");
                break;
            case ArenaData.WinConditionType.KillAllEnemies:
                tags.Add("Combat");
                break;
            case ArenaData.WinConditionType.ReachLocation:
                tags.Add("Navigation");
                break;
            default:
                tags.Add("Challenge");
                break;
        }

        // Add difficulty tag
        if (arena.difficultyMultiplier > 1.5f)
            tags.Add("Hard");
        else if (arena.difficultyMultiplier < 0.8f)
            tags.Add("Easy");
        else
            tags.Add("Normal");

        // Add time-based tag
        if (arena.timeLimitSeconds > 0)
            tags.Add("Timed");

        // Add feature tags
        if (arena.traps.Count > 0)
            tags.Add("Traps");

        if (arena.destructibleProps.Count > 0)
            tags.Add("Destructible");

        return tags.ToArray();
    }

    void LoadArenaThumbnail(LoadedArena arena)
    {
        string thumbnailPath = Path.ChangeExtension(arena.filePath, ".png");
        if (File.Exists(thumbnailPath))
        {
            byte[] imageData = File.ReadAllBytes(thumbnailPath);
            arena.thumbnail = new Texture2D(256, 256);
            arena.thumbnail.LoadImage(imageData);
        }
    }

    void RegisterArenasWithSystems()
    {
        // Register with Trials of the Ash
        var trialsSystem = FindObjectOfType<TrialsOfTheAsh>();
        if (trialsSystem != null)
        {
            foreach (var arena in loadedArenas.Values)
            {
                RegisterArenaWithTrials(arena, trialsSystem);
            }
        }

        // Register with Modding System
        var moddingSystem = ModdingSystem.Instance;
        if (moddingSystem != null)
        {
            foreach (var arena in loadedArenas.Values)
            {
                RegisterArenaWithModdingSystem(arena, moddingSystem);
            }
        }
    }

    void RegisterArenaWithTrials(LoadedArena arena, TrialsOfTheAsh trialsSystem)
    {
        var arenaMod = new ArenaMod
        {
            arenaId = arena.arenaId,
            name = arena.arenaName,
            description = arena.description,
            timeLimitSeconds = arena.arenaData.timeLimitSeconds,
            allowHealing = arena.arenaData.allowHealing,
            allowMagic = arena.arenaData.allowMagic,
            difficultyMultiplier = arena.arenaData.difficultyMultiplier,
            enemies = arena.arenaData.enemySpawnPoints.Select(pos => new EnemySpawn
            {
                enemyId = "arena_enemy",
                position = pos,
                count = 1,
                spawnDelay = 0f
            }).ToList()
        };

        trialsSystem.RegisterModdedArena(arenaMod);
    }

    void RegisterArenaWithModdingSystem(LoadedArena arena, ModdingSystem moddingSystem)
    {
        // Register arena as a mod
        var mod = new ModdingSystem.LoadedMod
        {
            modId = arena.arenaId,
            name = arena.arenaName,
            author = arena.author,
            version = arena.version,
            description = arena.description,
            type = ModdingSystem.ModType.Arena,
            isEnabled = true,
            isValid = true,
            filePath = arena.filePath,
            modData = arena.arenaData
        };

        // This would be added to the modding system's loaded mods
        Debug.Log($"Registered arena mod: {arena.arenaName}");
    }

    LoadedArena[] GetFilteredArenasInternal(string filter, string search)
    {
        var arenas = loadedArenas.Values.AsEnumerable();

        // Apply filter
        if (filter != "All")
        {
            switch (filter)
            {
                case "User Created":
                    arenas = arenas.Where(a => a.isUserCreated);
                    break;
                case "Community":
                    arenas = arenas.Where(a => a.isCommunityMod);
                    break;
                default:
                    arenas = arenas.Where(a => a.tags.Contains(filter));
                    break;
            }
        }

        // Apply search
        if (!string.IsNullOrEmpty(search))
        {
            search = search.ToLower();
            arenas = arenas.Where(a =>
                a.arenaName.ToLower().Contains(search) ||
                a.author.ToLower().Contains(search) ||
                a.description.ToLower().Contains(search) ||
                a.tags.Any(tag => tag.ToLower().Contains(search))
            );
        }

        return arenas.OrderBy(a => a.arenaName).ToArray();
    }

    void RegisterArenaForBossRushInternal(string arenaId)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return;

        var arena = loadedArenas[arenaId];

        // Add to boss rush queue
        var bossRushManager = FindObjectOfType<BossRushManager>();
        if (bossRushManager != null)
        {
            bossRushManager.AddCustomArena(arena.arenaData);
        }

        Debug.Log($"Registered arena for Boss Rush: {arena.arenaName}");
    }

    void OpenArenaBrowserInternal()
    {
        if (arenaBrowserUI != null)
            arenaBrowserUI.SetActive(true);

        RefreshArenaBrowser();
    }

    void CloseArenaBrowserInternal()
    {
        if (arenaBrowserUI != null)
            arenaBrowserUI.SetActive(false);
    }

    void RefreshArenaBrowser()
    {
        if (arenaListParent == null) return;

        // Clear existing entries
        foreach (Transform child in arenaListParent)
        {
            Destroy(child.gameObject);
        }

        // Get filtered arenas
        var filteredArenas = GetFilteredArenasInternal(currentFilter, currentSearch);

        // Create UI entries
        foreach (var arena in filteredArenas)
        {
            CreateArenaEntry(arena);
        }
    }

    void CreateArenaEntry(LoadedArena arena)
    {
        if (arenaEntryPrefab == null || arenaListParent == null) return;

        GameObject entry = Instantiate(arenaEntryPrefab, arenaListParent);

        // Setup arena info
        var nameText = entry.transform.Find("ArenaName")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (nameText != null)
            nameText.text = arena.arenaName;

        var authorText = entry.transform.Find("Author")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (authorText != null)
            authorText.text = $"by {arena.author}";

        var biomeText = entry.transform.Find("Biome")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (biomeText != null)
            biomeText.text = arena.biomeType.ToString();

        var winConditionText = entry.transform.Find("WinCondition")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (winConditionText != null)
            winConditionText.text = FormatWinCondition(arena.winCondition);

        var descriptionText = entry.transform.Find("Description")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (descriptionText != null)
            descriptionText.text = arena.description;

        var lastModifiedText = entry.transform.Find("LastModified")?.GetComponent<TMPro.TextMeshProUGUI>();
        if (lastModifiedText != null)
            lastModifiedText.text = $"Modified: {arena.lastModified:MM/dd/yyyy}";

        // Setup thumbnail
        var thumbnail = entry.transform.Find("Thumbnail")?.GetComponent<UnityEngine.UI.Image>();
        if (thumbnail != null)
        {
            if (arena.thumbnail != null)
            {
                thumbnail.sprite = Sprite.Create(arena.thumbnail, new Rect(0, 0, arena.thumbnail.width, arena.thumbnail.height), Vector2.one * 0.5f);
            }
            else
            {
                // Use default thumbnail based on biome
                thumbnail.sprite = GetDefaultThumbnail(arena.biomeType);
            }
        }

        // Setup tags
        var tagsParent = entry.transform.Find("Tags");
        if (tagsParent != null)
        {
            CreateTagElements(arena.tags, tagsParent);
        }

        // Setup difficulty indicator
        var difficultyIndicator = entry.transform.Find("DifficultyIndicator")?.GetComponent<UnityEngine.UI.Image>();
        if (difficultyIndicator != null)
        {
            difficultyIndicator.color = GetDifficultyColor(arena.arenaData.difficultyMultiplier);
        }

        // Setup type indicator
        var typeIndicator = entry.transform.Find("TypeIndicator")?.GetComponent<UnityEngine.UI.Image>();
        if (typeIndicator != null)
        {
            if (arena.isUserCreated)
                typeIndicator.color = Color.blue;
            else if (arena.isCommunityMod)
                typeIndicator.color = Color.green;
            else
                typeIndicator.color = Color.gray;
        }

        // Setup buttons
        var playButton = entry.transform.Find("PlayButton")?.GetComponent<UnityEngine.UI.Button>();
        if (playButton != null)
        {
            playButton.onClick.AddListener(() => PlayArenaInBossRush(arena.arenaId));
        }

        var editButton = entry.transform.Find("EditButton")?.GetComponent<UnityEngine.UI.Button>();
        if (editButton != null)
        {
            editButton.onClick.AddListener(() => EditArena(arena.arenaId));
            editButton.interactable = arena.isUserCreated; // Only allow editing user-created arenas
        }

        var deleteButton = entry.transform.Find("DeleteButton")?.GetComponent<UnityEngine.UI.Button>();
        if (deleteButton != null)
        {
            deleteButton.onClick.AddListener(() => DeleteArena(arena.arenaId));
            deleteButton.gameObject.SetActive(arena.isUserCreated); // Only show delete for user arenas
        }

        var exportButton = entry.transform.Find("ExportButton")?.GetComponent<UnityEngine.UI.Button>();
        if (exportButton != null)
        {
            exportButton.onClick.AddListener(() => ExportArena(arena.arenaId));
        }

        var screenshotButton = entry.transform.Find("ScreenshotButton")?.GetComponent<UnityEngine.UI.Button>();
        if (screenshotButton != null)
        {
            screenshotButton.onClick.AddListener(() => CaptureArenaScreenshot(arena.arenaId));
        }
    }

    string FormatWinCondition(ArenaData.WinConditionType winCondition)
    {
        switch (winCondition)
        {
            case ArenaData.WinConditionType.KillAllEnemies:
                return "Defeat All";
            case ArenaData.WinConditionType.SurviveTimeLimit:
                return "Survival";
            case ArenaData.WinConditionType.ReachLocation:
                return "Reach Goal";
            case ArenaData.WinConditionType.KillSpecificTarget:
                return "Eliminate Target";
            case ArenaData.WinConditionType.CollectItems:
                return "Collection";
            case ArenaData.WinConditionType.DefendObject:
                return "Defense";
            case ArenaData.WinConditionType.EscapeArena:
                return "Escape";
            case ArenaData.WinConditionType.ScoreThreshold:
                return "Score Attack";
            default:
                return winCondition.ToString();
        }
    }

    Sprite GetDefaultThumbnail(ArenaData.BiomeType biome)
    {
        // Load default thumbnails from Resources
        string thumbnailPath = $"UI/ArenaThumbnails/{biome}";
        return Resources.Load<Sprite>(thumbnailPath);
    }

    Color GetDifficultyColor(float difficultyMultiplier)
    {
        if (difficultyMultiplier <= 0.8f)
            return Color.green; // Easy
        else if (difficultyMultiplier <= 1.2f)
            return Color.yellow; // Normal
        else if (difficultyMultiplier <= 1.8f)
            return Color.orange; // Hard
        else
            return Color.red; // Extreme
    }

    void CreateTagElements(string[] tags, Transform tagsParent)
    {
        // Clear existing tags
        foreach (Transform child in tagsParent)
        {
            Destroy(child.gameObject);
        }

        // Create tag elements
        foreach (string tag in tags.Take(3)) // Limit to 3 tags for UI space
        {
            GameObject tagElement = new GameObject($"Tag_{tag}");
            tagElement.transform.SetParent(tagsParent, false);

            var tagText = tagElement.AddComponent<TMPro.TextMeshProUGUI>();
            tagText.text = tag;
            tagText.fontSize = 10;
            tagText.color = Color.white;

            var tagBackground = tagElement.AddComponent<UnityEngine.UI.Image>();
            tagBackground.color = GetTagColor(tag);

            var rectTransform = tagElement.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(50, 20);
        }
    }

    Color GetTagColor(string tag)
    {
        switch (tag.ToLower())
        {
            case "survival": return new Color(0.8f, 0.4f, 0.2f);
            case "combat": return new Color(0.8f, 0.2f, 0.2f);
            case "timed": return new Color(0.2f, 0.6f, 0.8f);
            case "hard": return new Color(0.6f, 0.2f, 0.8f);
            case "easy": return new Color(0.2f, 0.8f, 0.4f);
            default: return new Color(0.5f, 0.5f, 0.5f);
        }
    }

    void DeleteArena(string arenaId)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return;

        var arena = loadedArenas[arenaId];

        // Show confirmation dialog
        ShowConfirmation($"Delete arena '{arena.arenaName}'? This cannot be undone.", (confirmed) =>
        {
            if (confirmed)
            {
                try
                {
                    // Delete file
                    if (File.Exists(arena.filePath))
                    {
                        File.Delete(arena.filePath);
                    }

                    // Delete thumbnail if exists
                    string thumbnailPath = Path.ChangeExtension(arena.filePath, ".png");
                    if (File.Exists(thumbnailPath))
                    {
                        File.Delete(thumbnailPath);
                    }

                    // Remove from loaded arenas
                    loadedArenas.Remove(arenaId);

                    // Refresh UI
                    RefreshArenaBrowser();

                    Debug.Log($"Deleted arena: {arena.arenaName}");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to delete arena: {e.Message}");
                }
            }
        });
    }

    void ExportArena(string arenaId)
    {
        ArenaImportExport.ExportArena(arenaId, GetExportPath(arenaId));
    }

    string GetExportPath(string arenaId)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return "";

        var arena = loadedArenas[arenaId];
        string fileName = SanitizeFileName(arena.arenaName) + ".arenamod";
        string exportDir = Path.Combine(Application.persistentDataPath, "Exports");
        Directory.CreateDirectory(exportDir);
        return Path.Combine(exportDir, fileName);
    }

    void CaptureArenaScreenshot(string arenaId)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return;

        var arena = loadedArenas[arenaId];

        // Load arena in preview mode and capture screenshot
        StartCoroutine(CaptureArenaScreenshotCoroutine(arena));
    }

    System.Collections.IEnumerator CaptureArenaScreenshotCoroutine(LoadedArena arena)
    {
        // This would load the arena in a preview scene and capture a screenshot
        // For now, just create a placeholder
        yield return new WaitForSeconds(0.1f);

        // Create placeholder thumbnail
        Texture2D thumbnail = new Texture2D(256, 256);
        Color[] pixels = new Color[256 * 256];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = GetBiomeColor(arena.biomeType);
        }
        thumbnail.SetPixels(pixels);
        thumbnail.Apply();

        // Save thumbnail
        string thumbnailPath = Path.ChangeExtension(arena.filePath, ".png");
        byte[] pngData = thumbnail.EncodeToPNG();
        File.WriteAllBytes(thumbnailPath, pngData);

        // Update arena thumbnail
        arena.thumbnail = thumbnail;

        // Refresh UI
        RefreshArenaBrowser();

        Debug.Log($"Captured screenshot for arena: {arena.arenaName}");
    }

    Color GetBiomeColor(ArenaData.BiomeType biome)
    {
        switch (biome)
        {
            case ArenaData.BiomeType.Ashlands: return new Color(0.6f, 0.3f, 0.2f);
            case ArenaData.BiomeType.ForestOfShadows: return new Color(0.2f, 0.4f, 0.2f);
            case ArenaData.BiomeType.FrozenNorth: return new Color(0.7f, 0.8f, 1f);
            case ArenaData.BiomeType.CityOfTheSky: return new Color(1f, 0.9f, 0.7f);
            case ArenaData.BiomeType.KingdomOfSouthernBolt: return new Color(0.8f, 0.7f, 0.5f);
            default: return Color.gray;
        }
    }

    void ShowConfirmation(string message, System.Action<bool> callback)
    {
        // In a full implementation, this would show a proper confirmation dialog
        Debug.Log($"Confirmation: {message}");
        callback(true); // Auto-confirm for now
    }

    void PlayArenaInBossRush(string arenaId)
    {
        RegisterArenaForBossRushInternal(arenaId);

        // Start boss rush with this arena
        var bossRushManager = FindObjectOfType<BossRushManager>();
        if (bossRushManager != null)
        {
            bossRushManager.StartCustomArenaBossRush(arenaId);
        }

        CloseArenaBrowserInternal();
    }

    void EditArena(string arenaId)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return;

        var arena = loadedArenas[arenaId];

        // Open arena editor with this arena
        ArenaEditorManager.LoadArenaForEditing(arena.arenaName);

        CloseArenaBrowserInternal();
    }

    // Import/Export functionality
    string ImportArenaFromFileInternal(string filePath)
    {
        if (!File.Exists(filePath))
        {
            Debug.LogError($"Arena file not found: {filePath}");
            return null;
        }

        try
        {
            string json = File.ReadAllText(filePath);
            var arenaJson = JsonConvert.DeserializeObject<ArenaDataJson>(json);

            if (arenaJson?.arenaData == null)
            {
                Debug.LogError("Invalid arena file format");
                return null;
            }

            // Generate new ID to avoid conflicts
            string newArenaId = System.Guid.NewGuid().ToString();
            arenaJson.modId = newArenaId;
            arenaJson.arenaData.arenaId = newArenaId;

            // Save to user arenas folder
            string fileName = SanitizeFileName(arenaJson.name) + ".arenamod";
            string userPath = Path.Combine(Application.persistentDataPath, userArenasFolder, fileName);

            string newJson = JsonConvert.SerializeObject(arenaJson, Formatting.Indented);
            File.WriteAllText(userPath, newJson);

            // Reload arenas
            LoadArenasInternal();

            Debug.Log($"Imported arena: {arenaJson.name}");
            return newArenaId;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to import arena: {e.Message}");
            return null;
        }
    }

    bool ExportArenaToFileInternal(string arenaId, string exportPath)
    {
        if (!loadedArenas.ContainsKey(arenaId))
        {
            Debug.LogError($"Arena not found: {arenaId}");
            return false;
        }

        try
        {
            var arena = loadedArenas[arenaId];

            var exportData = new ArenaDataJson
            {
                modId = arena.arenaId,
                name = arena.arenaName,
                author = arena.author,
                version = arena.version,
                description = arena.description,
                arenaData = arena.arenaData
            };

            string json = JsonConvert.SerializeObject(exportData, Formatting.Indented);
            File.WriteAllText(exportPath, json);

            Debug.Log($"Exported arena to: {exportPath}");
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to export arena: {e.Message}");
            return false;
        }
    }

    string SanitizeFileName(string fileName)
    {
        string sanitized = fileName;
        char[] invalidChars = Path.GetInvalidFileNameChars();

        foreach (char c in invalidChars)
        {
            sanitized = sanitized.Replace(c, '_');
        }

        return sanitized;
    }

    // UI Event Handlers
    void OnFilterChanged(int filterIndex)
    {
        currentFilter = arenaCategories[filterIndex];
        RefreshArenaBrowser();
    }

    void OnSearchChanged(string search)
    {
        currentSearch = search;
        RefreshArenaBrowser();
    }

    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        switch (eventName)
        {
            case "ArenaCreated":
                LoadArenasInternal();
                break;
            case "ArenaDeleted":
                LoadArenasInternal();
                break;
        }
    }

    /// <summary>
    /// Check if arena exists
    /// </summary>
    public bool ArenaExists(string arenaId)
    {
        return loadedArenas.ContainsKey(arenaId);
    }

    /// <summary>
    /// Handle arena completion for event system integration
    /// </summary>
    public void OnArenaCompleted(string arenaId, ArenaCompletionData completionData)
    {
        // Notify community event manager
        var communityEventManager = CommunityEventManager.Instance;
        if (communityEventManager != null)
        {
            var result = new ArenaCompletionResult
            {
                IsCompleted = completionData.isCompleted,
                IsBossRush = completionData.isBossRush,
                Score = completionData.score,
                CompletionTime = completionData.completionTime,
                PlayerId = completionData.playerId,
                CustomData = completionData.customData
            };

            // This would trigger community events based on arena completion
            // communityEventManager.OnArenaCompleted(arenaId, result);
        }

        // Check for regional impact
        CheckArenaRegionalImpact(arenaId, completionData);
    }

    /// <summary>
    /// Check for regional impact from arena completion
    /// </summary>
    void CheckArenaRegionalImpact(string arenaId, ArenaCompletionData completionData)
    {
        if (!loadedArenas.ContainsKey(arenaId)) return;

        var arena = loadedArenas[arenaId];

        // Check if arena has regional impact configuration
        if (arena.arenaData.regionalImpact != null && arena.arenaData.regionalImpact.enableRegionalImpact)
        {
            ApplyArenaRegionalImpact(arena.arenaData.regionalImpact, completionData);
        }
    }

    /// <summary>
    /// Apply regional impact from arena completion
    /// </summary>
    void ApplyArenaRegionalImpact(ArenaRegionalImpact regionalImpact, ArenaCompletionData completionData)
    {
        // Apply morality shifts
        if (regionalImpact.moralityShift != null && completionData.isCompleted)
        {
            var moralitySystem = FindObjectOfType<MoralitySystem>();
            if (moralitySystem != null)
            {
                moralitySystem.ShiftMorality(
                    regionalImpact.moralityShift.targetAlignment,
                    regionalImpact.moralityShift.shiftAmount
                );
            }
        }

        // Apply faction reputation changes
        if (regionalImpact.factionChanges != null)
        {
            var worldStateManager = FindObjectOfType<WorldStateManager>();
            if (worldStateManager != null)
            {
                foreach (var factionChange in regionalImpact.factionChanges)
                {
                    worldStateManager.ModifyFactionReputation(
                        factionChange.factionId,
                        factionChange.reputationChange
                    );
                }
            }
        }

        // Trigger world events
        if (regionalImpact.triggeredEvents != null)
        {
            foreach (string eventId in regionalImpact.triggeredEvents)
            {
                CommunityEventManager.TriggerCommunityEventStatic(eventId);
            }
        }
    }

    [System.Serializable]
    public class ArenaDataJson
    {
        public string modId;
        public string name;
        public string author;
        public string version;
        public string description;
        public ArenaData arenaData;
    }

    [System.Serializable]
    public class ArenaCompletionData
    {
        public bool isCompleted;
        public bool isBossRush;
        public int score;
        public float completionTime;
        public string playerId;
        public Dictionary<string, object> customData;
    }

    [System.Serializable]
    public class ArenaRegionalImpact
    {
        public bool enableRegionalImpact;
        public MoralityShiftConfig moralityShift;
        public FactionChangeConfig[] factionChanges;
        public string[] triggeredEvents;
    }

    [System.Serializable]
    public class MoralityShiftConfig
    {
        public MoralityAlignment targetAlignment;
        public float shiftAmount;
        public string reason;
    }

    [System.Serializable]
    public class FactionChangeConfig
    {
        public string factionId;
        public float reputationChange;
        public string reason;
    }
}
