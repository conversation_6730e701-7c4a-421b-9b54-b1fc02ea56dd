using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Interface for community-driven world event mod providers in Cinder of Darkness
/// Allows external mods to define custom world events with full integration
/// </summary>
public interface IWorldEventModProvider
{
    /// <summary>
    /// Unique identifier for the mod provider
    /// </summary>
    string ModId { get; }

    /// <summary>
    /// Display name of the mod
    /// </summary>
    string ModName { get; }

    /// <summary>
    /// Mod author information
    /// </summary>
    string ModAuthor { get; }

    /// <summary>
    /// Mod version
    /// </summary>
    string ModVersion { get; }

    /// <summary>
    /// Mod description
    /// </summary>
    string ModDescription { get; }

    /// <summary>
    /// Compatibility version with base game
    /// </summary>
    string CompatibilityVersion { get; }

    /// <summary>
    /// Whether this mod is trusted (verified author)
    /// </summary>
    bool IsTrusted { get; }

    /// <summary>
    /// Gets all custom events provided by this mod.
    /// </summary>
    /// <returns>A list of custom world events that this mod provides to the game.</returns>
    /// <remarks>
    /// This method should return all events that the mod wants to register with the game.
    /// Events will be validated before being added to the active event pool.
    /// </remarks>
    List<CommunityWorldEvent> GetCustomEvents();

    /// <summary>
    /// Gets custom trigger conditions for events.
    /// </summary>
    /// <returns>A list of custom trigger providers that define when events should fire.</returns>
    /// <remarks>
    /// Custom triggers allow mods to define complex conditions for event activation
    /// beyond the standard game triggers (time, morality, reputation, etc.).
    /// </remarks>
    List<ICustomEventTrigger> GetCustomTriggers();

    /// <summary>
    /// Gets custom event effects that can be applied during events.
    /// </summary>
    /// <returns>A list of custom effect providers that define unique event behaviors.</returns>
    /// <remarks>
    /// Custom effects enable mods to create unique event outcomes that go beyond
    /// standard game effects like reputation changes or item rewards.
    /// </remarks>
    List<ICustomEventEffect> GetCustomEffects();

    /// <summary>
    /// Validates mod content for safety and compatibility with the current game version.
    /// </summary>
    /// <returns>A validation result containing any errors, warnings, and security assessment.</returns>
    /// <remarks>
    /// This method is called before the mod is loaded to ensure it won't break the game
    /// or compromise security. Mods that fail validation will not be loaded.
    /// </remarks>
    ModValidationResult ValidateMod();

    /// <summary>
    /// Initialize the mod provider
    /// </summary>
    /// <param name="gameContext">Current game context</param>
    void Initialize(GameContext gameContext);

    /// <summary>
    /// Cleanup when mod is unloaded
    /// </summary>
    void Cleanup();

    /// <summary>
    /// Handle event triggered by this mod
    /// </summary>
    /// <param name="eventId">Event identifier</param>
    /// <param name="context">Event context</param>
    void OnEventTriggered(string eventId, EventTriggerContext context);

    /// <summary>
    /// Handle event completed by this mod
    /// </summary>
    /// <param name="eventId">Event identifier</param>
    /// <param name="result">Event completion result</param>
    void OnEventCompleted(string eventId, EventCompletionResult result);
}

/// <summary>
/// Custom event trigger interface for community mods
/// </summary>
public interface ICustomEventTrigger
{
    /// <summary>
    /// Unique trigger identifier
    /// </summary>
    string TriggerId { get; }

    /// <summary>
    /// Display name for the trigger
    /// </summary>
    string TriggerName { get; }

    /// <summary>
    /// Check if trigger conditions are met
    /// </summary>
    /// <param name="context">Current game context</param>
    /// <returns>True if trigger should fire</returns>
    bool ShouldTrigger(EventTriggerContext context);

    /// <summary>
    /// Get trigger configuration parameters
    /// </summary>
    /// <returns>Configuration parameters</returns>
    Dictionary<string, object> GetTriggerParameters();

    /// <summary>
    /// Set trigger configuration parameters
    /// </summary>
    /// <param name="parameters">Configuration parameters</param>
    void SetTriggerParameters(Dictionary<string, object> parameters);
}

/// <summary>
/// Custom event effect interface for community mods
/// </summary>
public interface ICustomEventEffect
{
    /// <summary>
    /// Unique effect identifier
    /// </summary>
    string EffectId { get; }

    /// <summary>
    /// Display name for the effect
    /// </summary>
    string EffectName { get; }

    /// <summary>
    /// Apply the custom effect
    /// </summary>
    /// <param name="context">Event context</param>
    /// <param name="parameters">Effect parameters</param>
    void ApplyEffect(EventEffectContext context, Dictionary<string, object> parameters);

    /// <summary>
    /// Remove the custom effect
    /// </summary>
    /// <param name="context">Event context</param>
    /// <param name="parameters">Effect parameters</param>
    void RemoveEffect(EventEffectContext context, Dictionary<string, object> parameters);

    /// <summary>
    /// Update the effect over time
    /// </summary>
    /// <param name="context">Event context</param>
    /// <param name="deltaTime">Time since last update</param>
    void UpdateEffect(EventEffectContext context, float deltaTime);
}

/// <summary>
/// Community world event definition
/// </summary>
[System.Serializable]
public class CommunityWorldEvent
{
    public string eventId;
    public string eventName;
    public string eventDescription;
    public WorldEvent.EventType eventType;
    public WorldEvent.EventSeverity severity;
    public string modId;
    public string modAuthor;
    public float modVersion;

    // Custom trigger configuration
    public CustomTriggerConfig customTrigger;

    // Custom effects
    public CustomEffectConfig[] customEffects;

    // Arena integration
    public ArenaIntegrationConfig arenaIntegration;

    // Localization
    public LocalizationData localizationData;

    // Validation metadata
    public ValidationMetadata validationData;
}

/// <summary>
/// Custom trigger configuration
/// </summary>
[System.Serializable]
public class CustomTriggerConfig
{
    public string triggerId;
    public Dictionary<string, object> parameters;
    public string[] requiredMods;
    public string[] requiredGameFlags;
    public ReputationRequirement[] reputationRequirements;
    public AlignmentRequirement alignmentRequirement;
    public QuestRequirement[] questRequirements;
}

/// <summary>
/// Custom effect configuration
/// </summary>
[System.Serializable]
public class CustomEffectConfig
{
    public string effectId;
    public Dictionary<string, object> parameters;
    public float duration;
    public bool isPersistent;
    public string[] affectedSystems;
}

/// <summary>
/// Arena integration configuration
/// </summary>
[System.Serializable]
public class ArenaIntegrationConfig
{
    public bool enableArenaIntegration;
    public string[] triggerArenas;
    public BossRushIntegration bossRushIntegration;
    public RegionalImpactConfig regionalImpact;
    public MoralityShiftConfig moralityShift;
    public FactionOutcomeConfig[] factionOutcomes;
}

/// <summary>
/// Boss rush integration settings
/// </summary>
[System.Serializable]
public class BossRushIntegration
{
    public bool triggerOnCompletion;
    public bool triggerOnFailure;
    public int minimumScore;
    public float minimumTime;
    public string[] requiredArenas;
}

/// <summary>
/// Regional impact configuration
/// </summary>
[System.Serializable]
public class RegionalImpactConfig
{
    public string[] affectedRegions;
    public RegionalEffect[] effects;
    public float impactRadius;
    public bool spreadToNeighboringRegions;
}

/// <summary>
/// Regional effect definition
/// </summary>
[System.Serializable]
public class RegionalEffect
{
    public string effectType;
    public float intensity;
    public float duration;
    public Dictionary<string, object> parameters;
}

/// <summary>
/// Morality shift configuration
/// </summary>
[System.Serializable]
public class MoralityShiftConfig
{
    public MoralityAlignment targetAlignment;
    public float shiftAmount;
    public string reason;
    public bool affectsGlobalMorality;
}

/// <summary>
/// Faction outcome configuration
/// </summary>
[System.Serializable]
public class FactionOutcomeConfig
{
    public string factionId;
    public float reputationChange;
    public string outcomeType;
    public Dictionary<string, object> parameters;
}

/// <summary>
/// Reputation requirement
/// </summary>
[System.Serializable]
public class ReputationRequirement
{
    public string factionId;
    public float minimumReputation;
    public float maximumReputation;
}

/// <summary>
/// Alignment requirement
/// </summary>
[System.Serializable]
public class AlignmentRequirement
{
    public MoralityAlignment requiredAlignment;
    public float minimumValue;
    public float maximumValue;
}

/// <summary>
/// Quest requirement
/// </summary>
[System.Serializable]
public class QuestRequirement
{
    public string questId;
    public bool mustBeCompleted;
    public bool mustBeActive;
    public string[] requiredChoices;
}

/// <summary>
/// Localization data for community events
/// </summary>
[System.Serializable]
public class LocalizationData
{
    public Dictionary<string, string> localizedNames;
    public Dictionary<string, string> localizedDescriptions;
    public Dictionary<string, string> localizedDialogue;
    public Dictionary<string, string> localizedEffects;
}

/// <summary>
/// Validation metadata
/// </summary>
[System.Serializable]
public class ValidationMetadata
{
    public string checksum;
    public System.DateTime creationDate;
    public System.DateTime lastModified;
    public string[] dependencies;
    public string[] conflicts;
    public bool isVerified;
    public string verificationSignature;
}

/// <summary>
/// Mod validation result
/// </summary>
public class ModValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new List<string>();
    public List<string> Warnings { get; set; } = new List<string>();
    public SecurityLevel SecurityLevel { get; set; }
    public List<string> RequiredPermissions { get; set; } = new List<string>();

    public enum SecurityLevel
    {
        Safe,
        Caution,
        Unsafe,
        Blocked
    }
}

/// <summary>
/// Game context for mod initialization
/// </summary>
public class GameContext
{
    public EventManager EventManager { get; set; }
    public MoralitySystem MoralitySystem { get; set; }
    public QuestSystem QuestSystem { get; set; }
    public WorldStateManager WorldStateManager { get; set; }
    public ArenaManager ArenaManager { get; set; }
    public LocalizationManager LocalizationManager { get; set; }
    public SaveSystemManager SaveSystemManager { get; set; }
}

/// <summary>
/// Event effect context
/// </summary>
public class EventEffectContext
{
    public string EventId { get; set; }
    public EventManager.ActiveEvent ActiveEvent { get; set; }
    public EventTriggerContext TriggerContext { get; set; }
    public GameContext GameContext { get; set; }
    public Dictionary<string, object> EventVariables { get; set; }
}

/// <summary>
/// Event completion result
/// </summary>
public class EventCompletionResult
{
    public EventManager.EventState FinalState { get; set; }
    public bool PlayerParticipated { get; set; }
    public string PlayerChoice { get; set; }
    public float MoralityImpact { get; set; }
    public Dictionary<string, float> ReputationChanges { get; set; }
    public List<string> UnlockedContent { get; set; }
    public Dictionary<string, object> CustomData { get; set; }
}
