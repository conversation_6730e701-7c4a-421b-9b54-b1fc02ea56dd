using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System;
using UnityEngine.SceneManagement;

/// <summary>
/// Comprehensive save/load system for Cinder of Darkness
/// Replaces PlayerPrefs with secure file-based saving
/// </summary>
public class SaveSystemManager : MonoBehaviour
{
    [Header("Save Settings")]
    public int maxSaveSlots = 5;
    public bool enableAutosave = true;
    public float autosaveInterval = 300f; // 5 minutes
    public bool enableQuicksave = true;
    public bool compressSaveFiles = true;

    [Header("Cloud Save Integration")]
    public bool enableCloudSaveIntegration = true;
    public bool autoSyncToCloud = true;
    public float cloudSyncDelay = 5f; // Delay before syncing to cloud

    [Header("Security")]
    public bool encryptSaveFiles = true;
    public string encryptionKey = "CinderOfDarkness2024";

    // Static instance
    private static SaveSystemManager instance;
    public static SaveSystemManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<SaveSystemManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("SaveSystemManager");
                    instance = go.AddComponent<SaveSystemManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Save data structure
    [System.Serializable]
    public class SaveData
    {
        public string saveName;
        public string sceneName;
        public DateTime saveTime;
        public float playtime;
        public Vector3 playerPosition;
        public Vector3 playerRotation;
        public float playerHealth;
        public float playerMana;
        public int playerLevel;
        public float playerExperience;

        // Game systems data
        public MoralityData moralityData;
        public MagicData magicData;
        public InventoryData inventoryData;
        public QuestData questData;
        public WorldData worldData;
        public StoryData storyData;
        public EventSystemData eventData;

        // Journey statistics
        public JourneyData journeyData;

        // Settings
        public GameSettings gameSettings;

        public SaveData()
        {
            saveName = "New Save";
            saveTime = DateTime.Now;
            playtime = 0f;
            playerPosition = Vector3.zero;
            playerRotation = Vector3.zero;
            playerHealth = 100f;
            playerMana = 100f;
            playerLevel = 1;
            playerExperience = 0f;

            moralityData = new MoralityData();
            magicData = new MagicData();
            inventoryData = new InventoryData();
            questData = new QuestData();
            worldData = new WorldData();
            storyData = new StoryData();
            eventData = new EventSystemData();
            journeyData = new JourneyData();
            gameSettings = new GameSettings();
        }
    }

    // Data structures for different systems
    [System.Serializable]
    public class MoralityData
    {
        public float evilProgression;
        public float goodProgression;
        public float fearLevel;
        public float admirationLevel;
        public int evilChoicesMade;
        public int goodChoicesMade;
        public string currentPath;
    }

    [System.Serializable]
    public class MagicData
    {
        public bool hasDiscoveredWater;
        public bool hasDiscoveredWind;
        public bool hasDiscoveredEarth;
        public float fireProgression;
        public float waterProgression;
        public float windProgression;
        public float earthProgression;
        public List<string> knownSpells;
    }

    [System.Serializable]
    public class InventoryData
    {
        public int gold;
        public List<ItemData> items;
        public List<WeaponData> weapons;
        public List<ArmorData> armor;
        public string equippedWeapon;
        public string equippedArmor;
    }

    [System.Serializable]
    public class ItemData
    {
        public string itemId;
        public int quantity;
        public string rarity;
    }

    [System.Serializable]
    public class WeaponData
    {
        public string weaponId;
        public int upgradeLevel;
        public List<string> enchantments;
    }

    [System.Serializable]
    public class ArmorData
    {
        public string armorId;
        public int upgradeLevel;
        public float durability;
    }

    [System.Serializable]
    public class QuestData
    {
        public List<string> completedQuests;
        public List<string> activeQuests;
        public List<string> availableQuests;
        public Dictionary<string, int> questProgress;
    }

    [System.Serializable]
    public class WorldData
    {
        public List<string> visitedLocations;
        public List<string> unlockedFastTravelPoints;
        public Dictionary<string, bool> worldFlags;
        public float muscleGrowth;
        public float facialHairGrowth;
    }

    [System.Serializable]
    public class StoryData
    {
        public float storyProgress;
        public List<string> completedStoryBeats;
        public List<string> unlockedDialogue;
        public Dictionary<string, string> characterRelationships;
    }

    [System.Serializable]
    public class EventSystemData
    {
        public List<ActiveEventSaveData> activeEvents;
        public Dictionary<string, float> eventCooldowns;
        public List<CompletedEventSaveData> eventHistory;
        public Dictionary<string, int> eventTriggerCounts;

        public EventSystemData()
        {
            activeEvents = new List<ActiveEventSaveData>();
            eventCooldowns = new Dictionary<string, float>();
            eventHistory = new List<CompletedEventSaveData>();
            eventTriggerCounts = new Dictionary<string, int>();
        }
    }

    [System.Serializable]
    public class ActiveEventSaveData
    {
        public string eventId;
        public float startTime;
        public float duration;
        public float progress;
        public bool playerInteracted;
        public string playerChoice;
        public List<string> affectedNPCs;
        public List<string> triggeredEffects;
    }

    [System.Serializable]
    public class CompletedEventSaveData
    {
        public string eventId;
        public string eventName;
        public float startTime;
        public float endTime;
        public string finalState;
        public bool playerParticipated;
        public string playerChoice;
        public float moralityImpact;
        public List<string> consequences;
    }

    [System.Serializable]
    public class JourneyData
    {
        public int totalKills;
        public int innocentKills;
        public int villainKills;
        public int sideQuestsCompleted;
        public int childrenPlayedWith;
        public int buildingsDestroyed;
        public int rareItemsFound;
        public float totalGoldEarned;
        public float muscleGrowthAchieved;
        public float currentMoralWeight;
        public int evilChoicesMade;
        public int goodChoicesMade;
        public bool hasDiscoveredWater;
        public bool hasDiscoveredWind;
        public bool hasDiscoveredEarth;
    }

    [System.Serializable]
    public class GameSettings
    {
        public float masterVolume = 1f;
        public float musicVolume = 1f;
        public float sfxVolume = 1f;
        public int qualityLevel = 2;
        public bool fullscreen = true;
        public bool vsync = true;
        public string language = "English";
    }

    // Private fields
    private string saveDirectory;
    private float autosaveTimer;
    private SaveData currentSaveData;
    private bool isInitialized = false;

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSaveSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        if (enableAutosave)
        {
            autosaveTimer = autosaveInterval;
        }

        isInitialized = true;
    }

    void Update()
    {
        if (!isInitialized || !enableAutosave) return;

        autosaveTimer -= Time.deltaTime;
        if (autosaveTimer <= 0f)
        {
            AutoSave();
            autosaveTimer = autosaveInterval;
        }
    }

    void InitializeSaveSystem()
    {
        // Set save directory
        saveDirectory = Path.Combine(Application.persistentDataPath, "Saves");

        // Create save directory if it doesn't exist
        if (!Directory.Exists(saveDirectory))
        {
            Directory.CreateDirectory(saveDirectory);
        }

        Debug.Log($"Save system initialized. Save directory: {saveDirectory}");
    }

    // Public save/load methods
    public bool SaveGame(int slotIndex, string saveName = "")
    {
        try
        {
            SaveData saveData = CollectSaveData();

            if (!string.IsNullOrEmpty(saveName))
            {
                saveData.saveName = saveName;
            }
            else
            {
                saveData.saveName = $"Save Slot {slotIndex + 1}";
            }

            string fileName = $"save_slot_{slotIndex}.sav";
            string filePath = Path.Combine(saveDirectory, fileName);

            string jsonData = JsonUtility.ToJson(saveData, true);

            if (encryptSaveFiles)
            {
                jsonData = EncryptString(jsonData);
            }

            if (compressSaveFiles)
            {
                byte[] compressedData = CompressString(jsonData);
                File.WriteAllBytes(filePath, compressedData);
            }
            else
            {
                File.WriteAllText(filePath, jsonData);
            }

            currentSaveData = saveData;

            Debug.Log($"Game saved to slot {slotIndex}: {saveData.saveName}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save game: {e.Message}");
            return false;
        }
    }

    public bool LoadGame(int slotIndex)
    {
        try
        {
            string fileName = $"save_slot_{slotIndex}.sav";
            string filePath = Path.Combine(saveDirectory, fileName);

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"Save file not found: {filePath}");
                return false;
            }

            string jsonData;

            if (compressSaveFiles)
            {
                byte[] compressedData = File.ReadAllBytes(filePath);
                jsonData = DecompressString(compressedData);
            }
            else
            {
                jsonData = File.ReadAllText(filePath);
            }

            if (encryptSaveFiles)
            {
                jsonData = DecryptString(jsonData);
            }

            SaveData saveData = JsonUtility.FromJson<SaveData>(jsonData);

            ApplySaveData(saveData);
            currentSaveData = saveData;

            Debug.Log($"Game loaded from slot {slotIndex}: {saveData.saveName}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load game: {e.Message}");
            return false;
        }
    }

    public bool LoadGame(string fileName)
    {
        try
        {
            string filePath = Path.Combine(saveDirectory, fileName);

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"Save file not found: {filePath}");
                return false;
            }

            string jsonData;

            if (compressSaveFiles)
            {
                byte[] compressedData = File.ReadAllBytes(filePath);
                jsonData = DecompressString(compressedData);
            }
            else
            {
                jsonData = File.ReadAllText(filePath);
            }

            if (encryptSaveFiles)
            {
                jsonData = DecryptString(jsonData);
            }

            SaveData saveData = JsonUtility.FromJson<SaveData>(jsonData);

            ApplySaveData(saveData);
            currentSaveData = saveData;

            Debug.Log($"Game loaded: {saveData.saveName}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load game: {e.Message}");
            return false;
        }
    }

    public void AutoSave()
    {
        if (SceneLoader.IsInGameplay())
        {
            SaveGame(0, "Autosave");
            Debug.Log("Autosave completed");
        }
    }

    public void QuickSave()
    {
        if (enableQuicksave && SceneLoader.IsInGameplay())
        {
            SaveGame(1, "Quicksave");
            Debug.Log("Quicksave completed");
        }
    }

    public void QuickLoad()
    {
        if (enableQuicksave)
        {
            LoadGame(1);
            Debug.Log("Quickload completed");
        }
    }

    // Save data collection
    SaveData CollectSaveData()
    {
        SaveData saveData = new SaveData();

        // Basic game state
        saveData.sceneName = SceneManager.GetActiveScene().name;
        saveData.saveTime = DateTime.Now;

        // Player data
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            saveData.playerPosition = player.transform.position;
            saveData.playerRotation = player.transform.eulerAngles;

            // Get player stats
            var playerStats = player.GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                saveData.playerHealth = playerStats.currentHealth;
                saveData.playerMana = playerStats.currentMana;
                saveData.playerLevel = playerStats.level;
                saveData.playerExperience = playerStats.experience;
            }
        }

        // Collect system data
        CollectMoralityData(saveData);
        CollectMagicData(saveData);
        CollectInventoryData(saveData);
        CollectQuestData(saveData);
        CollectWorldData(saveData);
        CollectStoryData(saveData);
        CollectEventData(saveData);
        CollectJourneyData(saveData);
        CollectGameSettings(saveData);

        return saveData;
    }

    void CollectMoralityData(SaveData saveData)
    {
        var moralitySystem = FindObjectOfType<PhilosophicalMoralitySystem>();
        if (moralitySystem != null)
        {
            saveData.moralityData.evilProgression = moralitySystem.evilProgression;
            saveData.moralityData.goodProgression = moralitySystem.goodProgression;
            saveData.moralityData.fearLevel = moralitySystem.GetFearLevel();
            saveData.moralityData.admirationLevel = moralitySystem.GetAdmirationLevel();
            saveData.moralityData.currentPath = moralitySystem.GetCurrentPath().ToString();
        }
    }

    void CollectMagicData(SaveData saveData)
    {
        var magicSystem = FindObjectOfType<ElementalMagicSystem>();
        if (magicSystem != null)
        {
            saveData.magicData.hasDiscoveredWater = magicSystem.HasDiscoveredElement(ElementalMagicSystem.ElementType.Water);
            saveData.magicData.hasDiscoveredWind = magicSystem.HasDiscoveredElement(ElementalMagicSystem.ElementType.Wind);
            saveData.magicData.hasDiscoveredEarth = magicSystem.HasDiscoveredElement(ElementalMagicSystem.ElementType.Earth);
            saveData.magicData.fireProgression = magicSystem.GetElementProgression(ElementalMagicSystem.ElementType.Fire);
            saveData.magicData.waterProgression = magicSystem.GetElementProgression(ElementalMagicSystem.ElementType.Water);
            saveData.magicData.windProgression = magicSystem.GetElementProgression(ElementalMagicSystem.ElementType.Wind);
            saveData.magicData.earthProgression = magicSystem.GetElementProgression(ElementalMagicSystem.ElementType.Earth);
        }
    }

    void CollectInventoryData(SaveData saveData)
    {
        var economySystem = FindObjectOfType<EconomySystem>();
        if (economySystem != null)
        {
            saveData.inventoryData.gold = economySystem.GetCurrentGold();

            // Collect items, weapons, armor
            var inventory = economySystem.GetInventory();
            saveData.inventoryData.items = new List<ItemData>();

            foreach (var item in inventory)
            {
                saveData.inventoryData.items.Add(new ItemData
                {
                    itemId = item.itemName,
                    quantity = item.quantity,
                    rarity = item.rarity.ToString()
                });
            }
        }
    }

    void CollectQuestData(SaveData saveData)
    {
        var questSystem = FindObjectOfType<UniqueSideQuests>();
        if (questSystem != null)
        {
            saveData.questData.completedQuests = new List<string>();
            saveData.questData.activeQuests = new List<string>();
            saveData.questData.availableQuests = new List<string>();
            saveData.questData.questProgress = new Dictionary<string, int>();

            // Collect quest data from the system
            // Implementation depends on quest system structure
        }
    }

    void CollectWorldData(SaveData saveData)
    {
        var worldSystem = FindObjectOfType<WorldInteractionSystem>();
        if (worldSystem != null)
        {
            saveData.worldData.muscleGrowth = worldSystem.GetMuscleGrowth();
            saveData.worldData.visitedLocations = new List<string>();
            saveData.worldData.unlockedFastTravelPoints = new List<string>();
            saveData.worldData.worldFlags = new Dictionary<string, bool>();
        }
    }

    void CollectStoryData(SaveData saveData)
    {
        var gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            saveData.storyData.storyProgress = gameManager.currentStoryProgress;
            saveData.storyData.completedStoryBeats = new List<string>();
            saveData.storyData.unlockedDialogue = new List<string>();
            saveData.storyData.characterRelationships = new Dictionary<string, string>();
        }
    }

    void CollectJourneyData(SaveData saveData)
    {
        var gameController = FindObjectOfType<CinderOfDarknessGameController>();
        if (gameController != null)
        {
            // Copy journey data from game controller
            var journeyData = gameController.journeyData;
            saveData.journeyData = journeyData;
        }
    }

    void CollectGameSettings(SaveData saveData)
    {
        var settingsManager = FindObjectOfType<SettingsManager>();
        if (settingsManager != null)
        {
            var settings = settingsManager.GetCurrentSettings();
            saveData.gameSettings.masterVolume = settings.masterVolume;
            saveData.gameSettings.musicVolume = settings.musicVolume;
            saveData.gameSettings.sfxVolume = settings.sfxVolume;
            saveData.gameSettings.qualityLevel = settings.qualityLevel;
            saveData.gameSettings.fullscreen = settings.fullscreen;
            saveData.gameSettings.vsync = settings.vsync;
            saveData.gameSettings.language = settings.language;
        }
    }

    // Save data application
    void ApplySaveData(SaveData saveData)
    {
        // Load scene if different
        if (saveData.sceneName != SceneManager.GetActiveScene().name)
        {
            SceneLoader.LoadSceneAsync(saveData.sceneName);
            // Wait for scene to load before applying data
            StartCoroutine(ApplySaveDataAfterSceneLoad(saveData));
        }
        else
        {
            ApplySaveDataImmediate(saveData);
        }
    }

    System.Collections.IEnumerator ApplySaveDataAfterSceneLoad(SaveData saveData)
    {
        // Wait for scene to load
        yield return new WaitUntil(() => SceneManager.GetActiveScene().name == saveData.sceneName);

        // Wait one more frame for objects to initialize
        yield return null;

        ApplySaveDataImmediate(saveData);
    }

    void ApplySaveDataImmediate(SaveData saveData)
    {
        // Apply player data
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            player.transform.position = saveData.playerPosition;
            player.transform.eulerAngles = saveData.playerRotation;

            var playerStats = player.GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                playerStats.currentHealth = saveData.playerHealth;
                playerStats.currentMana = saveData.playerMana;
                playerStats.level = saveData.playerLevel;
                playerStats.experience = saveData.playerExperience;
            }
        }

        // Apply system data
        ApplyMoralityData(saveData);
        ApplyMagicData(saveData);
        ApplyInventoryData(saveData);
        ApplyQuestData(saveData);
        ApplyWorldData(saveData);
        ApplyStoryData(saveData);
        ApplyEventData(saveData);
        ApplyJourneyData(saveData);
        ApplyGameSettings(saveData);
    }

    void ApplyMoralityData(SaveData saveData)
    {
        var moralitySystem = FindObjectOfType<PhilosophicalMoralitySystem>();
        if (moralitySystem != null)
        {
            moralitySystem.evilProgression = saveData.moralityData.evilProgression;
            moralitySystem.goodProgression = saveData.moralityData.goodProgression;
            // Apply other morality data
        }
    }

    void ApplyMagicData(SaveData saveData)
    {
        var magicSystem = FindObjectOfType<ElementalMagicSystem>();
        if (magicSystem != null)
        {
            if (saveData.magicData.hasDiscoveredWater)
                magicSystem.DiscoverElement(ElementalMagicSystem.ElementType.Water);
            if (saveData.magicData.hasDiscoveredWind)
                magicSystem.DiscoverElement(ElementalMagicSystem.ElementType.Wind);
            if (saveData.magicData.hasDiscoveredEarth)
                magicSystem.DiscoverElement(ElementalMagicSystem.ElementType.Earth);
        }
    }

    void ApplyInventoryData(SaveData saveData)
    {
        var economySystem = FindObjectOfType<EconomySystem>();
        if (economySystem != null)
        {
            economySystem.SetGold(saveData.inventoryData.gold);

            // Apply inventory items
            foreach (var itemData in saveData.inventoryData.items)
            {
                // Add items to inventory
                // Implementation depends on economy system structure
            }
        }
    }

    void ApplyQuestData(SaveData saveData)
    {
        var questSystem = FindObjectOfType<UniqueSideQuests>();
        if (questSystem != null)
        {
            // Apply quest data
            // Implementation depends on quest system structure
        }
    }

    void ApplyWorldData(SaveData saveData)
    {
        var worldSystem = FindObjectOfType<WorldInteractionSystem>();
        if (worldSystem != null)
        {
            worldSystem.SetMuscleGrowth(saveData.worldData.muscleGrowth);
            // Apply other world data
        }
    }

    void ApplyStoryData(SaveData saveData)
    {
        var gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            gameManager.currentStoryProgress = saveData.storyData.storyProgress;
            // Apply other story data
        }
    }

    void ApplyJourneyData(SaveData saveData)
    {
        var gameController = FindObjectOfType<CinderOfDarknessGameController>();
        if (gameController != null)
        {
            gameController.journeyData = saveData.journeyData;
        }
    }

    void ApplyGameSettings(SaveData saveData)
    {
        var settingsManager = FindObjectOfType<SettingsManager>();
        if (settingsManager != null)
        {
            settingsManager.ApplySettings(saveData.gameSettings);
        }
    }

    // Utility methods
    public bool HasSaveFile(int slotIndex)
    {
        string fileName = $"save_slot_{slotIndex}.sav";
        string filePath = Path.Combine(saveDirectory, fileName);
        return File.Exists(filePath);
    }

    public bool HasAnySaveFile()
    {
        for (int i = 0; i < maxSaveSlots; i++)
        {
            if (HasSaveFile(i))
                return true;
        }
        return false;
    }

    public string GetMostRecentSaveFile()
    {
        DateTime mostRecentTime = DateTime.MinValue;
        string mostRecentFile = "";

        for (int i = 0; i < maxSaveSlots; i++)
        {
            string fileName = $"save_slot_{i}.sav";
            string filePath = Path.Combine(saveDirectory, fileName);

            if (File.Exists(filePath))
            {
                DateTime fileTime = File.GetLastWriteTime(filePath);
                if (fileTime > mostRecentTime)
                {
                    mostRecentTime = fileTime;
                    mostRecentFile = fileName;
                }
            }
        }

        return mostRecentFile;
    }

    public List<SaveFileInfo> GetAllSaveFiles()
    {
        List<SaveFileInfo> saveFiles = new List<SaveFileInfo>();

        for (int i = 0; i < maxSaveSlots; i++)
        {
            string fileName = $"save_slot_{i}.sav";
            string filePath = Path.Combine(saveDirectory, fileName);

            if (File.Exists(filePath))
            {
                try
                {
                    string jsonData;

                    if (compressSaveFiles)
                    {
                        byte[] compressedData = File.ReadAllBytes(filePath);
                        jsonData = DecompressString(compressedData);
                    }
                    else
                    {
                        jsonData = File.ReadAllText(filePath);
                    }

                    if (encryptSaveFiles)
                    {
                        jsonData = DecryptString(jsonData);
                    }

                    SaveData saveData = JsonUtility.FromJson<SaveData>(jsonData);

                    saveFiles.Add(new SaveFileInfo
                    {
                        slotIndex = i,
                        saveName = saveData.saveName,
                        sceneName = saveData.sceneName,
                        saveTime = saveData.saveTime,
                        playtime = saveData.playtime,
                        playerLevel = saveData.playerLevel
                    });
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to read save file {fileName}: {e.Message}");
                }
            }
        }

        return saveFiles;
    }

    public void DeleteSave(int slotIndex)
    {
        string fileName = $"save_slot_{slotIndex}.sav";
        string filePath = Path.Combine(saveDirectory, fileName);

        if (File.Exists(filePath))
        {
            File.Delete(filePath);
            Debug.Log($"Deleted save file: {fileName}");
        }
    }

    public void DeleteAllSaves()
    {
        for (int i = 0; i < maxSaveSlots; i++)
        {
            DeleteSave(i);
        }
        Debug.Log("All save files deleted");
    }

    // Encryption/Compression helpers
    string EncryptString(string text)
    {
        // Simple XOR encryption (replace with more secure method for production)
        char[] chars = text.ToCharArray();
        for (int i = 0; i < chars.Length; i++)
        {
            chars[i] = (char)(chars[i] ^ encryptionKey[i % encryptionKey.Length]);
        }
        return new string(chars);
    }

    string DecryptString(string encryptedText)
    {
        // XOR decryption (same as encryption for XOR)
        return EncryptString(encryptedText);
    }

    byte[] CompressString(string text)
    {
        // Simple compression using System.IO.Compression
        byte[] data = System.Text.Encoding.UTF8.GetBytes(text);
        using (var output = new MemoryStream())
        {
            using (var gzip = new System.IO.Compression.GZipStream(output, System.IO.Compression.CompressionMode.Compress))
            {
                gzip.Write(data, 0, data.Length);
            }
            return output.ToArray();
        }
    }

    string DecompressString(byte[] compressedData)
    {
        using (var input = new MemoryStream(compressedData))
        {
            using (var gzip = new System.IO.Compression.GZipStream(input, System.IO.Compression.CompressionMode.Decompress))
            {
                using (var output = new MemoryStream())
                {
                    gzip.CopyTo(output);
                    return System.Text.Encoding.UTF8.GetString(output.ToArray());
                }
            }
        }
    }

    [System.Serializable]
    public class SaveFileInfo
    {
        public int slotIndex;
        public string saveName;
        public string sceneName;
        public DateTime saveTime;
        public float playtime;
        public int playerLevel;
    }

    // Event system integration methods
    void CollectEventData(SaveData saveData)
    {
        // Collect event system data
        var eventManager = EventManager.Instance;
        if (eventManager != null)
        {
            var eventManagerSaveData = eventManager.GetSaveData();
            saveData.eventData = new EventSystemData();

            // Convert active events
            foreach (var activeEvent in eventManagerSaveData.activeEvents)
            {
                var activeEventSave = new ActiveEventSaveData
                {
                    eventId = activeEvent.eventId,
                    startTime = activeEvent.startTime,
                    duration = activeEvent.duration,
                    progress = activeEvent.progress,
                    playerInteracted = activeEvent.playerInteracted,
                    playerChoice = activeEvent.playerChoice,
                    affectedNPCs = activeEvent.affectedNPCs,
                    triggeredEffects = activeEvent.triggeredEffects
                };
                saveData.eventData.activeEvents.Add(activeEventSave);
            }

            // Convert event cooldowns
            saveData.eventData.eventCooldowns = eventManagerSaveData.eventCooldowns;

            // Convert event history
            foreach (var completedEvent in eventManagerSaveData.eventHistory)
            {
                var completedEventSave = new CompletedEventSaveData
                {
                    eventId = completedEvent.eventId,
                    eventName = completedEvent.eventName,
                    startTime = completedEvent.startTime,
                    endTime = completedEvent.endTime,
                    finalState = completedEvent.finalState.ToString(),
                    playerParticipated = completedEvent.playerParticipated,
                    playerChoice = completedEvent.playerChoice,
                    moralityImpact = completedEvent.moralityImpact,
                    consequences = completedEvent.consequences
                };
                saveData.eventData.eventHistory.Add(completedEventSave);
            }

            // Convert trigger counts
            saveData.eventData.eventTriggerCounts = eventManagerSaveData.eventTriggerCounts;
        }
    }

    void ApplyEventData(SaveData saveData)
    {
        // Apply event system data
        var eventManager = EventManager.Instance;
        if (eventManager != null && saveData.eventData != null)
        {
            var eventManagerSaveData = new EventManager.EventManagerSaveData();

            // Convert active events
            eventManagerSaveData.activeEvents = new List<EventManager.ActiveEvent>();
            foreach (var activeEventSave in saveData.eventData.activeEvents)
            {
                // Find the event data from registry
                var eventRegistry = FindObjectOfType<EventRegistry>();
                if (eventRegistry != null)
                {
                    var eventData = eventRegistry.GetEvent(activeEventSave.eventId);
                    if (eventData != null)
                    {
                        var activeEvent = new EventManager.ActiveEvent(eventData)
                        {
                            startTime = activeEventSave.startTime,
                            duration = activeEventSave.duration,
                            progress = activeEventSave.progress,
                            playerInteracted = activeEventSave.playerInteracted,
                            playerChoice = activeEventSave.playerChoice,
                            affectedNPCs = activeEventSave.affectedNPCs,
                            triggeredEffects = activeEventSave.triggeredEffects
                        };
                        eventManagerSaveData.activeEvents.Add(activeEvent);
                    }
                }
            }

            // Convert event cooldowns
            eventManagerSaveData.eventCooldowns = saveData.eventData.eventCooldowns ?? new Dictionary<string, float>();

            // Convert event history
            eventManagerSaveData.eventHistory = new List<EventManager.CompletedEvent>();
            foreach (var completedEventSave in saveData.eventData.eventHistory)
            {
                var completedEvent = new EventManager.CompletedEvent
                {
                    eventId = completedEventSave.eventId,
                    eventName = completedEventSave.eventName,
                    startTime = completedEventSave.startTime,
                    endTime = completedEventSave.endTime,
                    playerParticipated = completedEventSave.playerParticipated,
                    playerChoice = completedEventSave.playerChoice,
                    moralityImpact = completedEventSave.moralityImpact,
                    consequences = completedEventSave.consequences
                };

                // Parse final state
                if (System.Enum.TryParse<EventManager.EventState>(completedEventSave.finalState, out var finalState))
                {
                    completedEvent.finalState = finalState;
                }

                eventManagerSaveData.eventHistory.Add(completedEvent);
            }

            // Convert trigger counts
            eventManagerSaveData.eventTriggerCounts = saveData.eventData.eventTriggerCounts ?? new Dictionary<string, int>();

            // Load data into event manager
            eventManager.LoadSaveData(eventManagerSaveData);
        }
    }
}

// Additional data structures for save system
[System.Serializable]
public class PlayerStats : MonoBehaviour
{
    public float currentHealth = 100f;
    public float maxHealth = 100f;
    public float currentMana = 100f;
    public float maxMana = 100f;
    public int level = 1;
    public float experience = 0f;
    public float experienceToNextLevel = 100f;
}

[System.Serializable]
public class GameSettings
{
    public float masterVolume = 1f;
    public float musicVolume = 1f;
    public float sfxVolume = 1f;
    public int qualityLevel = 2;
    public bool fullscreen = true;
    public bool vsync = true;
    public string language = "English";
}

[System.Serializable]
public class SettingsManager : MonoBehaviour
{
    private GameSettings currentSettings = new GameSettings();

    public GameSettings GetCurrentSettings()
    {
        return currentSettings;
    }

    public void ApplySettings(GameSettings settings)
    {
        currentSettings = settings;
        // Apply settings to Unity systems
        QualitySettings.SetQualityLevel(settings.qualityLevel);
        Screen.fullScreen = settings.fullscreen;
        QualitySettings.vSyncCount = settings.vsync ? 1 : 0;
    }

    public void LoadSettings()
    {
        // Load from PlayerPrefs or file
        currentSettings.masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
        currentSettings.musicVolume = PlayerPrefs.GetFloat("MusicVolume", 1f);
        currentSettings.sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 1f);
        currentSettings.qualityLevel = PlayerPrefs.GetInt("QualityLevel", 2);
        currentSettings.fullscreen = PlayerPrefs.GetInt("Fullscreen", 1) == 1;
        currentSettings.vsync = PlayerPrefs.GetInt("VSync", 1) == 1;
        currentSettings.language = PlayerPrefs.GetString("Language", "English");
    }

    public void SaveSettings()
    {
        PlayerPrefs.SetFloat("MasterVolume", currentSettings.masterVolume);
        PlayerPrefs.SetFloat("MusicVolume", currentSettings.musicVolume);
        PlayerPrefs.SetFloat("SFXVolume", currentSettings.sfxVolume);
        PlayerPrefs.SetInt("QualityLevel", currentSettings.qualityLevel);
        PlayerPrefs.SetInt("Fullscreen", currentSettings.fullscreen ? 1 : 0);
        PlayerPrefs.SetInt("VSync", currentSettings.vsync ? 1 : 0);
        PlayerPrefs.SetString("Language", currentSettings.language);
        PlayerPrefs.Save();
    }

    public void ResetToDefaults()
    {
        currentSettings = new GameSettings();
        ApplySettings(currentSettings);
    }
}
