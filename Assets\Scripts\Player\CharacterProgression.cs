using UnityEngine;
using System.Collections;

public class CharacterProgression : MonoBehaviour
{
    [Header("Character Growth")]
    public int currentAge = 15;
    public int maxAge = 25;
    public float storyProgress = 0f; // 0 to 1

    [Header("Facial Hair System")]
    public FacialHairStage currentFacialHair = FacialHairStage.Clean;
    public GameObject[] facialHairObjects; // 0: Clean, 1: Stubble, 2: Mustache, 3: Beard
    public Material[] facialHairMaterials;

    [Header("Character Models")]
    public GameObject youngKaelModel;
    public GameObject matureKaelModel;
    public SkinnedMeshRenderer characterRenderer;

    [Header("Clothing System")]
    public ClothingSet[] availableClothing;
    public ClothingSet currentClothing;
    public ArmorProgression armorProgression;

    [Header("Cultural Heritage")]
    public CulturalTraits arabTraits;
    public CulturalTraits greekTraits;
    public float culturalBalance = 0.5f; // 0 = full Arab, 1 = full Greek

    [Header("Voice Evolution")]
    public AudioClip[] youngVoiceClips;
    public AudioClip[] matureVoiceClips;
    public float voicePitchMultiplier = 1f;

    private PlayerStats playerStats;
    private AudioSource voiceSource;
    private Animator characterAnimator;

    public enum FacialHairStage
    {
        Clean,      // Age 15-17
        Stubble,    // Age 18-20
        Mustache,   // Age 21-23
        Beard       // Age 24-25
    }

    [System.Serializable]
    public class ClothingSet
    {
        public string name;
        public GameObject[] clothingPieces;
        public Material[] materials;
        public CulturalOrigin origin;
        public MoralAlignment alignment;
        public int unlockLevel;
    }

    [System.Serializable]
    public class ArmorProgression
    {
        public ClothingSet raggedClothes;    // Starting outfit
        public ClothingSet lightArmor;       // Early game
        public ClothingSet culturalArmor;    // Mid game (based on choices)
        public ClothingSet legendaryArmor;   // End game
    }

    [System.Serializable]
    public class CulturalTraits
    {
        public Color skinTone;
        public Color eyeColor;
        public Color hairColor;
        public float[] facialFeatureWeights;
        public string[] culturalNames;
        public AudioClip[] culturalVoiceLines;
    }

    public enum CulturalOrigin
    {
        Arab,
        Greek,
        Mixed,
        Universal
    }

    public enum MoralAlignment
    {
        Sun,
        Moon,
        Eclipse,
        Neutral
    }

    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        voiceSource = GetComponent<AudioSource>();
        characterAnimator = GetComponent<Animator>();

        InitializeCharacter();

        // Subscribe to story progress events
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnMoralPathChanged;
        }

        GameManager.Instance.OnStoryProgressChanged += OnStoryProgressChanged;
    }

    void Update()
    {
        UpdateCharacterProgression();
    }

    void InitializeCharacter()
    {
        // Start with ragged clothes
        EquipClothing(armorProgression.raggedClothes);

        // Set initial facial hair
        UpdateFacialHair();

        // Apply cultural heritage
        ApplyCulturalTraits();

        Debug.Log($"Kael initialized: Age {currentAge}, Heritage: {culturalBalance * 100:F0}% Greek, {(1-culturalBalance) * 100:F0}% Arab");
    }

    void UpdateCharacterProgression()
    {
        // Age progression based on story progress
        int newAge = Mathf.RoundToInt(Mathf.Lerp(15, maxAge, storyProgress));
        if (newAge != currentAge)
        {
            AgeCharacter(newAge);
        }

        // Update voice pitch as character matures
        float targetPitch = Mathf.Lerp(1.2f, 0.8f, storyProgress);
        voicePitchMultiplier = Mathf.Lerp(voicePitchMultiplier, targetPitch, Time.deltaTime);

        if (voiceSource != null)
        {
            voiceSource.pitch = voicePitchMultiplier;
        }
    }

    void AgeCharacter(int newAge)
    {
        currentAge = newAge;

        // Update facial hair based on age
        FacialHairStage newStage = GetFacialHairStageForAge(currentAge);
        if (newStage != currentFacialHair)
        {
            StartCoroutine(TransitionFacialHair(newStage));
        }

        // Update character model if significant age change
        if (currentAge >= 20 && youngKaelModel.activeInHierarchy)
        {
            TransitionToMatureModel();
        }

        Debug.Log($"Kael aged to {currentAge} years old");
    }

    FacialHairStage GetFacialHairStageForAge(int age)
    {
        if (age <= 17) return FacialHairStage.Clean;
        if (age <= 20) return FacialHairStage.Stubble;
        if (age <= 23) return FacialHairStage.Mustache;
        return FacialHairStage.Beard;
    }

    IEnumerator TransitionFacialHair(FacialHairStage newStage)
    {
        // Fade out current facial hair
        if (facialHairObjects[(int)currentFacialHair] != null)
        {
            yield return StartCoroutine(FadeFacialHair(facialHairObjects[(int)currentFacialHair], false));
        }

        currentFacialHair = newStage;

        // Fade in new facial hair
        if (facialHairObjects[(int)currentFacialHair] != null)
        {
            facialHairObjects[(int)currentFacialHair].SetActive(true);
            yield return StartCoroutine(FadeFacialHair(facialHairObjects[(int)currentFacialHair], true));
        }

        Debug.Log($"Facial hair transitioned to: {currentFacialHair}");
    }

    IEnumerator FadeFacialHair(GameObject hairObject, bool fadeIn)
    {
        Renderer hairRenderer = hairObject.GetComponent<Renderer>();
        if (hairRenderer == null) yield break;

        Material hairMaterial = hairRenderer.material;
        Color startColor = hairMaterial.color;
        Color targetColor = startColor;
        targetColor.a = fadeIn ? 1f : 0f;

        float duration = 2f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            Color currentColor = Color.Lerp(startColor, targetColor, progress);
            hairMaterial.color = currentColor;

            yield return null;
        }

        if (!fadeIn)
        {
            hairObject.SetActive(false);
        }
    }

    void UpdateFacialHair()
    {
        // Deactivate all facial hair objects
        foreach (GameObject hairObj in facialHairObjects)
        {
            if (hairObj != null)
                hairObj.SetActive(false);
        }

        // Activate current facial hair
        if (facialHairObjects[(int)currentFacialHair] != null)
        {
            facialHairObjects[(int)currentFacialHair].SetActive(true);
        }
    }

    void TransitionToMatureModel()
    {
        if (matureKaelModel != null && youngKaelModel != null)
        {
            // Smooth transition between models
            StartCoroutine(TransitionCharacterModel());
        }
    }

    IEnumerator TransitionCharacterModel()
    {
        // This would involve more complex model swapping in a full implementation
        // For prototype, we'll just switch models
        youngKaelModel.SetActive(false);
        matureKaelModel.SetActive(true);

        // Re-apply current clothing to new model
        EquipClothing(currentClothing);

        yield return null;
    }

    void ApplyCulturalTraits()
    {
        if (characterRenderer == null) return;

        // Blend Arab and Greek features based on cultural balance
        Color blendedSkinTone = Color.Lerp(arabTraits.skinTone, greekTraits.skinTone, culturalBalance);
        Color blendedEyeColor = Color.Lerp(arabTraits.eyeColor, greekTraits.eyeColor, culturalBalance);
        Color blendedHairColor = Color.Lerp(arabTraits.hairColor, greekTraits.hairColor, culturalBalance);

        // Apply to character materials
        Material characterMaterial = characterRenderer.material;
        characterMaterial.SetColor("_SkinColor", blendedSkinTone);
        characterMaterial.SetColor("_EyeColor", blendedEyeColor);
        characterMaterial.SetColor("_HairColor", blendedHairColor);

        Debug.Log($"Applied cultural traits: Skin {blendedSkinTone}, Eyes {blendedEyeColor}, Hair {blendedHairColor}");
    }

    public void EquipClothing(ClothingSet newClothing)
    {
        if (newClothing == null) return;

        // Remove current clothing
        if (currentClothing != null)
        {
            foreach (GameObject piece in currentClothing.clothingPieces)
            {
                if (piece != null)
                    piece.SetActive(false);
            }
        }

        currentClothing = newClothing;

        // Equip new clothing
        foreach (GameObject piece in currentClothing.clothingPieces)
        {
            if (piece != null)
                piece.SetActive(true);
        }

        Debug.Log($"Equipped clothing set: {currentClothing.name}");
    }

    public void UnlockClothing(string clothingName)
    {
        foreach (ClothingSet clothing in availableClothing)
        {
            if (clothing.name == clothingName)
            {
                clothing.unlockLevel = 0; // Mark as unlocked
                Debug.Log($"Unlocked clothing: {clothingName}");
                break;
            }
        }
    }

    void OnMoralPathChanged(PlayerStats.MoralPath newPath)
    {
        // Unlock path-specific clothing
        MoralAlignment alignment = ConvertToMoralAlignment(newPath);

        foreach (ClothingSet clothing in availableClothing)
        {
            if (clothing.alignment == alignment && clothing.unlockLevel > 0)
            {
                UnlockClothing(clothing.name);
            }
        }
    }

    void OnStoryProgressChanged(float progress)
    {
        storyProgress = progress;

        // Unlock clothing based on story progress
        if (progress >= 0.25f && currentClothing == armorProgression.raggedClothes)
        {
            EquipClothing(armorProgression.lightArmor);
        }
        else if (progress >= 0.75f && currentClothing != armorProgression.legendaryArmor)
        {
            EquipClothing(armorProgression.legendaryArmor);
        }
    }

    MoralAlignment ConvertToMoralAlignment(PlayerStats.MoralPath path)
    {
        switch (path)
        {
            case PlayerStats.MoralPath.Sun: return MoralAlignment.Sun;
            case PlayerStats.MoralPath.Moon: return MoralAlignment.Moon;
            case PlayerStats.MoralPath.Eclipse: return MoralAlignment.Eclipse;
            default: return MoralAlignment.Neutral;
        }
    }

    public void PlayVoiceLine(string lineType)
    {
        AudioClip[] voiceClips = currentAge < 20 ? youngVoiceClips : matureVoiceClips;

        if (voiceClips.Length > 0 && voiceSource != null)
        {
            AudioClip clipToPlay = voiceClips[Random.Range(0, voiceClips.Length)];
            voiceSource.PlayOneShot(clipToPlay);
        }
    }

    // Missing enums for appearance system
    public enum PostureType
    {
        Normal,
        Hunched,
        Rigid,
        Aggressive,
        Menacing,
        Open,
        Confident,
        Noble,
        Serene
    }

    public enum EyeColor
    {
        Normal,
        Dull,
        Cold,
        Red,
        Black,
        Warm,
        Determined,
        Bright,
        Golden
    }

    // Missing methods for appearance system
    public void SetPosture(PostureType postureType)
    {
        // Implementation would modify character animator parameters
        Debug.Log($"Character posture set to: {postureType}");
    }

    public void SetEyeColor(EyeColor eyeColor)
    {
        // Implementation would modify character material properties
        Debug.Log($"Character eye color set to: {eyeColor}");
    }

    public void SetMuscleScale(float scale)
    {
        // Implementation would modify character mesh scale for muscle growth
        Debug.Log($"Character muscle scale set to: {scale}");
    }

    // Getters
    public int GetCurrentAge() => currentAge;
    public FacialHairStage GetFacialHairStage() => currentFacialHair;
    public float GetCulturalBalance() => culturalBalance;
    public ClothingSet GetCurrentClothing() => currentClothing;
}
