using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Quest Log UI for Cinder of Darkness.
    /// Displays active quests, completed quests, and quest details.
    /// </summary>
    public class QuestLogUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject questLogPanel;
        [SerializeField] private Transform questListContainer;
        [SerializeField] private GameObject questEntryPrefab;
        [SerializeField] private TextMeshProUGUI questTitleText;
        [SerializeField] private TextMeshProUG<PERSON> questDescriptionText;
        [SerializeField] private TextMeshProUGUI questObjectivesText;
        [SerializeField] private TextMeshProUGUI questRewardsText;
        [SerializeField] private Button closeButton;
        [SerializeField] private Toggle showCompletedToggle;
        [SerializeField] private Scrollbar questListScrollbar;

        [Header("Quest Categories")]
        [SerializeField] private Button mainQuestsButton;
        [SerializeField] private Button sideQuestsButton;
        [SerializeField] private Button completedQuestsButton;
        [SerializeField] private Image categoryIndicator;

        [Header("Audio")]
        [SerializeField] private AudioClip openSound;
        [SerializeField] private AudioClip closeSound;
        [SerializeField] private AudioClip selectSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private QuestSystem questSystem;
        private DynamicNarrativeSystem narrativeSystem;
        private List<GameObject> questEntries = new List<GameObject>();
        private QuestCategory currentCategory = QuestCategory.Main;
        private Quest selectedQuest;
        private bool isVisible = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            questSystem = QuestSystem.Instance;
            narrativeSystem = DynamicNarrativeSystem.Instance;
        }

        private void SetupUI()
        {
            if (questLogPanel != null)
                questLogPanel.SetActive(false);

            UpdateCategoryButtons();
            RefreshQuestList();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseQuestLog);

            if (showCompletedToggle != null)
                showCompletedToggle.onValueChanged.AddListener(OnShowCompletedChanged);

            if (mainQuestsButton != null)
                mainQuestsButton.onClick.AddListener(() => SetCategory(QuestCategory.Main));

            if (sideQuestsButton != null)
                sideQuestsButton.onClick.AddListener(() => SetCategory(QuestCategory.Side));

            if (completedQuestsButton != null)
                completedQuestsButton.onClick.AddListener(() => SetCategory(QuestCategory.Completed));

            // Subscribe to quest system events
            if (questSystem != null)
            {
                questSystem.OnQuestStarted += OnQuestStarted;
                questSystem.OnQuestCompleted += OnQuestCompleted;
                questSystem.OnQuestUpdated += OnQuestUpdated;
            }
        }
        #endregion

        #region UI Management
        public void ToggleQuestLog()
        {
            if (isVisible)
                CloseQuestLog();
            else
                OpenQuestLog();
        }

        public void OpenQuestLog()
        {
            if (questLogPanel != null)
            {
                questLogPanel.SetActive(true);
                isVisible = true;
                RefreshQuestList();
                PlaySound(openSound);
            }
        }

        public void CloseQuestLog()
        {
            if (questLogPanel != null)
            {
                questLogPanel.SetActive(false);
                isVisible = false;
                PlaySound(closeSound);
            }
        }

        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.J))
            {
                ToggleQuestLog();
            }

            if (Input.GetKeyDown(KeyCode.Escape) && isVisible)
            {
                CloseQuestLog();
            }
        }

        private void SetCategory(QuestCategory category)
        {
            currentCategory = category;
            UpdateCategoryButtons();
            RefreshQuestList();
            PlaySound(selectSound);
        }

        private void UpdateCategoryButtons()
        {
            // Update button states based on current category
            if (mainQuestsButton != null)
                mainQuestsButton.interactable = currentCategory != QuestCategory.Main;

            if (sideQuestsButton != null)
                sideQuestsButton.interactable = currentCategory != QuestCategory.Side;

            if (completedQuestsButton != null)
                completedQuestsButton.interactable = currentCategory != QuestCategory.Completed;

            // Update category indicator
            if (categoryIndicator != null)
            {
                switch (currentCategory)
                {
                    case QuestCategory.Main:
                        categoryIndicator.color = Color.yellow;
                        break;
                    case QuestCategory.Side:
                        categoryIndicator.color = Color.cyan;
                        break;
                    case QuestCategory.Completed:
                        categoryIndicator.color = Color.green;
                        break;
                }
            }
        }
        #endregion

        #region Quest Display
        private void RefreshQuestList()
        {
            ClearQuestEntries();

            if (questSystem == null) return;

            var quests = GetQuestsForCategory(currentCategory);
            foreach (var quest in quests)
            {
                CreateQuestEntry(quest);
            }

            // Select first quest if none selected
            if (selectedQuest == null && quests.Count > 0)
            {
                SelectQuest(quests[0]);
            }
        }

        private List<Quest> GetQuestsForCategory(QuestCategory category)
        {
            if (questSystem == null) return new List<Quest>();

            switch (category)
            {
                case QuestCategory.Main:
                    return questSystem.GetActiveQuests().Where(q => q.questType == QuestType.Main).ToList();
                case QuestCategory.Side:
                    return questSystem.GetActiveQuests().Where(q => q.questType == QuestType.Side).ToList();
                case QuestCategory.Completed:
                    return questSystem.GetCompletedQuests().ToList();
                default:
                    return new List<Quest>();
            }
        }

        private void CreateQuestEntry(Quest quest)
        {
            if (questEntryPrefab == null || questListContainer == null) return;

            GameObject entry = Instantiate(questEntryPrefab, questListContainer);
            var questEntry = entry.GetComponent<QuestEntryUI>();
            
            if (questEntry != null)
            {
                questEntry.Setup(quest, this);
                questEntries.Add(entry);
            }
        }

        private void ClearQuestEntries()
        {
            foreach (var entry in questEntries)
            {
                if (entry != null)
                    Destroy(entry);
            }
            questEntries.Clear();
        }

        public void SelectQuest(Quest quest)
        {
            selectedQuest = quest;
            UpdateQuestDetails();
            PlaySound(selectSound);
        }

        private void UpdateQuestDetails()
        {
            if (selectedQuest == null) return;

            if (questTitleText != null)
                questTitleText.text = selectedQuest.questName;

            if (questDescriptionText != null)
                questDescriptionText.text = selectedQuest.description;

            if (questObjectivesText != null)
            {
                var objectives = string.Join("\n", selectedQuest.objectives.Select(o => 
                    $"• {o.description} {(o.isCompleted ? "✓" : "")}"));
                questObjectivesText.text = objectives;
            }

            if (questRewardsText != null)
            {
                var rewards = string.Join(", ", selectedQuest.rewards.Select(r => $"{r.amount} {r.type}"));
                questRewardsText.text = $"Rewards: {rewards}";
            }
        }
        #endregion

        #region Event Handlers
        private void OnQuestStarted(Quest quest)
        {
            if (isVisible && (currentCategory == QuestCategory.Main || currentCategory == QuestCategory.Side))
            {
                RefreshQuestList();
            }
        }

        private void OnQuestCompleted(Quest quest)
        {
            if (isVisible)
            {
                RefreshQuestList();
            }
        }

        private void OnQuestUpdated(Quest quest)
        {
            if (isVisible && selectedQuest != null && selectedQuest.questId == quest.questId)
            {
                UpdateQuestDetails();
            }
        }

        private void OnShowCompletedChanged(bool showCompleted)
        {
            RefreshQuestList();
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class QuestEntryUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI questNameText;
        [SerializeField] private TextMeshProUGUI questProgressText;
        [SerializeField] private Image questTypeIcon;
        [SerializeField] private Button selectButton;

        private Quest quest;
        private QuestLogUI questLogUI;

        public void Setup(Quest questData, QuestLogUI questLog)
        {
            quest = questData;
            questLogUI = questLog;

            if (questNameText != null)
                questNameText.text = quest.questName;

            if (questProgressText != null)
            {
                int completed = quest.objectives.Count(o => o.isCompleted);
                int total = quest.objectives.Count;
                questProgressText.text = $"{completed}/{total}";
            }

            if (questTypeIcon != null)
            {
                questTypeIcon.color = quest.questType == QuestType.Main ? Color.yellow : Color.cyan;
            }

            if (selectButton != null)
                selectButton.onClick.AddListener(() => questLogUI.SelectQuest(quest));
        }
    }

    public enum QuestCategory
    {
        Main,
        Side,
        Completed
    }
    #endregion
}
