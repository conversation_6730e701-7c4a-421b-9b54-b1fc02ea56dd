using UnityEngine;
using System.Collections;

public class SequelHookSystem : MonoBehaviour
{
    [Header("Sequel Hook Configuration")]
    public bool mainStoryCompleted = false;
    public bool sequelHookTriggered = false;
    public bool isNewGamePlus = false;
    
    [Header("Dying Traveler Encounter")]
    public GameObject dyingTravelerPrefab;
    public Transform[] possibleSpawnLocations;
    public float encounterChance = 0.3f; // 30% chance after credits
    
    [Header("Mysterious Symbol")]
    public GameObject scorchedSymbol;
    public Texture2D symbolTexture;
    public Material symbolMaterial;
    public float symbolDiscoveryRadius = 5f;
    
    [Header("Audio Elements")]
    public AudioClip dyingWhisper;
    public AudioClip mysteriousWind;
    public AudioClip distantFire;
    public AudioClip sequelTheme;
    
    [Header("Sequel Tease Content")]
    public string[] dyingTravelerLines;
    public string[] investigationPrompts;
    public string[] symbolDescriptions;
    public string[] sequelHints;
    
    private ModifiedNewGamePlusSystem newGamePlusSystem;
    private GameManager gameManager;
    private bool travelerEncounterActive = false;
    private bool symbolRevealed = false;
    
    void Start()
    {
        newGamePlusSystem = GetComponent<ModifiedNewGamePlusSystem>();
        gameManager = GameManager.Instance;
        
        // Check if this is New Game+
        isNewGamePlus = newGamePlusSystem?.IsNewGamePlus() ?? false;
        
        // Hide sequel elements initially
        if (scorchedSymbol != null)
        {
            scorchedSymbol.SetActive(false);
        }
        
        InitializeSequelHook();
    }
    
    void Update()
    {
        CheckForSequelHookTriggers();
        CheckForSymbolInvestigation();
    }
    
    void InitializeSequelHook()
    {
        // Initialize dying traveler lines
        dyingTravelerLines = new string[]
        {
            "The cinder burns... but there are other fires still alight...",
            "Beyond the ash lands... where the sun never sets... they wait...",
            "Your flame is but one... of many that must unite...",
            "The great convergence... approaches... the fires must gather...",
            "In the land of eternal dawn... the first flame still burns...",
            "Seek the Sunward Reaches... where fire was born...",
            "The cycle... is not complete... other Cinderborn walk distant paths..."
        };
        
        investigationPrompts = new string[]
        {
            "The traveler's words echo in your mind. What did they mean?",
            "You feel compelled to search the area where the traveler died.",
            "Something about their final words suggests a deeper truth.",
            "The mention of 'other fires' stirs something within your ember-heart."
        };
        
        symbolDescriptions = new string[]
        {
            "A symbol scorched into the earth - not by any fire you recognize.",
            "The mark resembles a sun with rays extending beyond the horizon.",
            "Strange runes circle the central flame, written in no language you know.",
            "The symbol pulses with residual heat, as if recently burned.",
            "This mark speaks of distant lands and fires yet unmet."
        };
        
        sequelHints = new string[]
        {
            "The symbol points toward lands beyond the ash-covered horizon.",
            "You sense other flames burning in distant realms.",
            "This is not the end of your story, but perhaps the beginning of something greater.",
            "The convergence the traveler spoke of... it calls to you.",
            "Your journey as The Cinderborn may extend far beyond these ash lands."
        };
        
        Debug.Log("Sequel Hook System initialized - watching for story completion");
    }
    
    void CheckForSequelHookTriggers()
    {
        // Check for main story completion
        if (!mainStoryCompleted && gameManager != null)
        {
            if (gameManager.IsMainStoryComplete())
            {
                OnMainStoryCompleted();
            }
        }
        
        // Check for New Game+ sequel hook
        if (isNewGamePlus && !sequelHookTriggered)
        {
            if (ShouldTriggerNewGamePlusHook())
            {
                TriggerNewGamePlusSequelHook();
            }
        }
    }
    
    void OnMainStoryCompleted()
    {
        mainStoryCompleted = true;
        
        // Chance to trigger sequel hook after credits
        if (Random.Range(0f, 1f) < encounterChance)
        {
            StartCoroutine(PostCreditsSequelHook());
        }
        
        Debug.Log("Main story completed - sequel hook may trigger");
    }
    
    bool ShouldTriggerNewGamePlusHook()
    {
        // Trigger in New Game+ after significant progress
        if (newGamePlusSystem == null) return false;
        
        // Check if player has made significant progress in New Game+
        TearOfAshSystem tearSystem = GetComponent<TearOfAshSystem>();
        if (tearSystem != null && tearSystem.GetSagesDefeated() >= 2)
        {
            return true;
        }
        
        return false;
    }
    
    IEnumerator PostCreditsSequelHook()
    {
        // Wait for credits to finish
        yield return new WaitForSeconds(30f);
        
        ShowSequelMessage("The credits fade, but your story continues...");
        yield return new WaitForSeconds(3f);
        
        // Spawn dying traveler encounter
        yield return StartCoroutine(DyingTravelerEncounter());
    }
    
    void TriggerNewGamePlusSequelHook()
    {
        sequelHookTriggered = true;
        StartCoroutine(NewGamePlusSequelHook());
    }
    
    IEnumerator NewGamePlusSequelHook()
    {
        ShowSequelMessage("In this cycle of rebirth, new truths emerge...");
        yield return new WaitForSeconds(3f);
        
        // Enhanced sequel hook for New Game+
        yield return StartCoroutine(EnhancedDyingTravelerEncounter());
    }
    
    IEnumerator DyingTravelerEncounter()
    {
        travelerEncounterActive = true;
        
        // Choose spawn location
        Transform spawnLocation = GetRandomSpawnLocation();
        if (spawnLocation == null)
        {
            travelerEncounterActive = false;
            yield break;
        }
        
        // Spawn dying traveler
        GameObject traveler = SpawnDyingTraveler(spawnLocation);
        if (traveler == null)
        {
            travelerEncounterActive = false;
            yield break;
        }
        
        ShowSequelMessage("You hear a weak voice calling from nearby...");
        yield return new WaitForSeconds(3f);
        
        // Guide player to traveler
        ShowSequelMessage("Someone needs help. You feel compelled to investigate.");
        yield return new WaitForSeconds(2f);
        
        // Wait for player to approach
        yield return StartCoroutine(WaitForPlayerApproach(traveler));
        
        // Dying traveler dialogue
        yield return StartCoroutine(DyingTravelerDialogue());
        
        // Traveler dies
        yield return StartCoroutine(TravelerDeathSequence(traveler));
        
        // Reveal symbol
        RevealScorchedSymbol(spawnLocation);
        
        travelerEncounterActive = false;
    }
    
    IEnumerator EnhancedDyingTravelerEncounter()
    {
        // Enhanced version for New Game+
        ShowSequelMessage("A familiar scene unfolds, yet different this time...");
        yield return new WaitForSeconds(3f);
        
        ShowSequelMessage("The dying traveler's words carry new weight in this cycle...");
        yield return new WaitForSeconds(3f);
        
        // Continue with regular encounter but with enhanced dialogue
        yield return StartCoroutine(DyingTravelerEncounter());
        
        // Additional New Game+ content
        ShowSequelMessage("In this cycle, you understand more of what the traveler meant...");
        yield return new WaitForSeconds(4f);
        
        ShowSequelMessage("The convergence is not just prophecy - it is inevitability.");
        yield return new WaitForSeconds(3f);
    }
    
    Transform GetRandomSpawnLocation()
    {
        if (possibleSpawnLocations.Length == 0) return null;
        return possibleSpawnLocations[Random.Range(0, possibleSpawnLocations.Length)];
    }
    
    GameObject SpawnDyingTraveler(Transform spawnLocation)
    {
        if (dyingTravelerPrefab == null) return null;
        
        GameObject traveler = Instantiate(dyingTravelerPrefab, spawnLocation.position, spawnLocation.rotation);
        
        // Set up traveler as dying/wounded
        DyingTravelerComponent travelerComponent = traveler.GetComponent<DyingTravelerComponent>();
        if (travelerComponent == null)
        {
            travelerComponent = traveler.AddComponent<DyingTravelerComponent>();
        }
        
        travelerComponent.Initialize(dyingTravelerLines);
        
        return traveler;
    }
    
    IEnumerator WaitForPlayerApproach(GameObject traveler)
    {
        if (traveler == null) yield break;
        
        float maxWaitTime = 60f; // Wait up to 1 minute
        float elapsed = 0f;
        
        while (elapsed < maxWaitTime)
        {
            float distance = Vector3.Distance(transform.position, traveler.transform.position);
            
            if (distance < 5f)
            {
                break; // Player approached
            }
            
            elapsed += Time.deltaTime;
            yield return null;
        }
    }
    
    IEnumerator DyingTravelerDialogue()
    {
        ShowSequelMessage("You find a traveler, gravely wounded, their life ebbing away...");
        yield return new WaitForSeconds(3f);
        
        // Play dying whisper audio
        if (dyingWhisper != null)
        {
            AudioSource.PlayClipAtPoint(dyingWhisper, transform.position, 0.7f);
        }
        
        // Speak the cryptic line
        string crypticLine = dyingTravelerLines[Random.Range(0, dyingTravelerLines.Length)];
        ShowSequelMessage($"Traveler: \"{crypticLine}\"");
        yield return new WaitForSeconds(5f);
        
        ShowSequelMessage("Their eyes focus on you with desperate intensity...");
        yield return new WaitForSeconds(3f);
        
        // Additional cryptic hints
        if (isNewGamePlus)
        {
            ShowSequelMessage("Traveler: \"You... you've walked this path before... the cycle remembers...\"");
            yield return new WaitForSeconds(4f);
        }
        
        string additionalHint = dyingTravelerLines[Random.Range(0, dyingTravelerLines.Length)];
        ShowSequelMessage($"Traveler: \"{additionalHint}\"");
        yield return new WaitForSeconds(4f);
        
        ShowSequelMessage("The traveler's breathing becomes labored...");
        yield return new WaitForSeconds(2f);
    }
    
    IEnumerator TravelerDeathSequence(GameObject traveler)
    {
        ShowSequelMessage("The traveler's eyes close for the final time...");
        yield return new WaitForSeconds(3f);
        
        ShowSequelMessage("Their final breath carries the scent of distant fires...");
        yield return new WaitForSeconds(3f);
        
        // Play mysterious wind
        if (mysteriousWind != null)
        {
            AudioSource.PlayClipAtPoint(mysteriousWind, transform.position, 0.5f);
        }
        
        ShowSequelMessage("As they die, you feel a strange pull toward something nearby...");
        yield return new WaitForSeconds(4f);
        
        // Remove traveler
        if (traveler != null)
        {
            Destroy(traveler);
        }
    }
    
    void RevealScorchedSymbol(Transform location)
    {
        if (scorchedSymbol == null) return;
        
        // Position symbol near the traveler's death location
        Vector3 symbolPosition = location.position + Vector3.forward * 3f;
        scorchedSymbol.transform.position = symbolPosition;
        scorchedSymbol.SetActive(true);
        
        symbolRevealed = true;
        
        ShowSequelMessage("Something has been burned into the ground where the traveler lay...");
        
        // Apply symbol material
        if (symbolMaterial != null && symbolTexture != null)
        {
            symbolMaterial.mainTexture = symbolTexture;
        }
    }
    
    void CheckForSymbolInvestigation()
    {
        if (!symbolRevealed || scorchedSymbol == null) return;
        
        float distance = Vector3.Distance(transform.position, scorchedSymbol.transform.position);
        
        if (distance <= symbolDiscoveryRadius)
        {
            if (Input.GetKeyDown(KeyCode.E))
            {
                InvestigateScorchedSymbol();
            }
            else
            {
                // Show investigation prompt
                ShowSequelMessage("Press [E] to examine the scorched symbol");
            }
        }
    }
    
    void InvestigateScorchedSymbol()
    {
        StartCoroutine(SymbolInvestigationSequence());
    }
    
    IEnumerator SymbolInvestigationSequence()
    {
        ShowSequelMessage("You kneel to examine the mysterious symbol...");
        yield return new WaitForSeconds(2f);
        
        // Describe the symbol
        string description = symbolDescriptions[Random.Range(0, symbolDescriptions.Length)];
        ShowSequelMessage(description);
        yield return new WaitForSeconds(4f);
        
        // Play distant fire sound
        if (distantFire != null)
        {
            AudioSource.PlayClipAtPoint(distantFire, transform.position, 0.4f);
        }
        
        ShowSequelMessage("As you touch the symbol, visions flash through your mind...");
        yield return new WaitForSeconds(3f);
        
        ShowSequelMessage("Distant lands... other flames... a convergence yet to come...");
        yield return new WaitForSeconds(4f);
        
        // Sequel hint
        string hint = sequelHints[Random.Range(0, sequelHints.Length)];
        ShowSequelMessage(hint);
        yield return new WaitForSeconds(4f);
        
        // Play sequel theme
        if (sequelTheme != null)
        {
            AudioSource.PlayClipAtPoint(sequelTheme, transform.position, 0.6f);
        }
        
        ShowSequelMessage("The symbol fades, but its meaning burns bright in your memory...");
        yield return new WaitForSeconds(4f);
        
        // Mark sequel hook as discovered
        MarkSequelHookDiscovered();
        
        // Hide the symbol
        scorchedSymbol.SetActive(false);
    }
    
    void MarkSequelHookDiscovered()
    {
        // Save sequel hook discovery
        PlayerPrefs.SetInt("SequelHookDiscovered", 1);
        PlayerPrefs.SetFloat("SequelHookTime", Time.time);
        PlayerPrefs.Save();
        
        // Unlock special dialogue
        if (gameManager != null)
        {
            gameManager.UnlockDialogueOption("SequelHookWitness");
            gameManager.UnlockDialogueOption("DistantFires");
            gameManager.UnlockDialogueOption("GreatConvergence");
        }
        
        // Add to psychological system
        PsychologicalSystem psycheSystem = GetComponent<PsychologicalSystem>();
        if (psycheSystem != null)
        {
            psycheSystem.AddSequelAwareness(50f);
        }
        
        Debug.Log("Sequel hook discovered - the path to future adventures is revealed");
    }
    
    void ShowSequelMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowSequelText(message); // Special sequel text display
        }
        
        Debug.Log($"Sequel Hook: {message}");
    }
    
    // Public interface
    public void ForceSequelHook()
    {
        if (!sequelHookTriggered)
        {
            StartCoroutine(DyingTravelerEncounter());
        }
    }
    
    public bool HasSequelHookBeenDiscovered()
    {
        return PlayerPrefs.GetInt("SequelHookDiscovered", 0) == 1;
    }
    
    public void SetMainStoryCompleted(bool completed)
    {
        mainStoryCompleted = completed;
        if (completed)
        {
            OnMainStoryCompleted();
        }
    }
    
    // Getters
    public bool IsSequelHookTriggered() => sequelHookTriggered;
    public bool IsSymbolRevealed() => symbolRevealed;
    public bool IsTravelerEncounterActive() => travelerEncounterActive;
}

// Component for the dying traveler
public class DyingTravelerComponent : MonoBehaviour
{
    public string[] dyingWords;
    public bool hasSpoken = false;
    
    public void Initialize(string[] words)
    {
        dyingWords = words;
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") && !hasSpoken)
        {
            hasSpoken = true;
            // Trigger dialogue when player approaches
        }
    }
}
