using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// Asset optimization tool for Cinder of Darkness.
/// Provides automated texture compression, audio optimization, and asset analysis.
/// </summary>
public class AssetOptimizationTool : EditorWindow
{
    #region Private Fields
    private Vector2 scrollPosition;
    private bool showTextureOptimization = true;
    private bool showAudioOptimization = true;
    private bool showAssetAnalysis = true;
    
    private List<string> optimizationResults = new List<string>();
    private int totalAssetsAnalyzed = 0;
    private int assetsOptimized = 0;
    private long memorySavedMB = 0;
    #endregion

    #region Menu Item
    /// <summary>
    /// Create menu item for the asset optimization tool.
    /// </summary>
    [MenuItem("Cinder of Darkness/Asset Optimization Tool")]
    public static void ShowWindow()
    {
        AssetOptimizationTool window = GetWindow<AssetOptimizationTool>("Asset Optimization");
        window.minSize = new Vector2(500, 600);
        window.Show();
    }
    #endregion

    #region GUI
    /// <summary>
    /// Draw the optimization tool GUI.
    /// </summary>
    private void OnGUI()
    {
        GUILayout.Label("Cinder of Darkness - Asset Optimization Tool", EditorStyles.boldLabel);
        GUILayout.Space(10);

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        DrawTextureOptimization();
        GUILayout.Space(10);
        
        DrawAudioOptimization();
        GUILayout.Space(10);
        
        DrawAssetAnalysis();
        GUILayout.Space(10);
        
        DrawOptimizationResults();

        EditorGUILayout.EndScrollView();
    }

    /// <summary>
    /// Draw texture optimization section.
    /// </summary>
    private void DrawTextureOptimization()
    {
        showTextureOptimization = EditorGUILayout.Foldout(showTextureOptimization, "Texture Optimization", true);
        
        if (showTextureOptimization)
        {
            EditorGUILayout.HelpBox("Optimize textures for better performance and memory usage.", MessageType.Info);
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Analyze Textures"))
            {
                AnalyzeTextures();
            }
            if (GUILayout.Button("Optimize All Textures"))
            {
                OptimizeTextures();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Set PC Compression"))
            {
                SetTextureCompressionForPlatform(BuildTarget.StandaloneWindows64);
            }
            if (GUILayout.Button("Set Console Compression"))
            {
                SetTextureCompressionForPlatform(BuildTarget.PS4);
            }
            GUILayout.EndHorizontal();
        }
    }

    /// <summary>
    /// Draw audio optimization section.
    /// </summary>
    private void DrawAudioOptimization()
    {
        showAudioOptimization = EditorGUILayout.Foldout(showAudioOptimization, "Audio Optimization", true);
        
        if (showAudioOptimization)
        {
            EditorGUILayout.HelpBox("Optimize audio files for better performance and smaller build size.", MessageType.Info);
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Analyze Audio"))
            {
                AnalyzeAudio();
            }
            if (GUILayout.Button("Optimize Audio"))
            {
                OptimizeAudio();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Set Music Streaming"))
            {
                SetAudioStreaming(true);
            }
            if (GUILayout.Button("Set SFX Compression"))
            {
                SetAudioCompression();
            }
            GUILayout.EndHorizontal();
        }
    }

    /// <summary>
    /// Draw asset analysis section.
    /// </summary>
    private void DrawAssetAnalysis()
    {
        showAssetAnalysis = EditorGUILayout.Foldout(showAssetAnalysis, "Asset Analysis", true);
        
        if (showAssetAnalysis)
        {
            EditorGUILayout.HelpBox("Analyze project assets for optimization opportunities.", MessageType.Info);
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Full Asset Analysis"))
            {
                PerformFullAssetAnalysis();
            }
            if (GUILayout.Button("Find Unused Assets"))
            {
                FindUnusedAssets();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Analyze Memory Usage"))
            {
                AnalyzeMemoryUsage();
            }
            if (GUILayout.Button("Generate Report"))
            {
                GenerateOptimizationReport();
            }
            GUILayout.EndHorizontal();
        }
    }

    /// <summary>
    /// Draw optimization results section.
    /// </summary>
    private void DrawOptimizationResults()
    {
        GUILayout.Label("Optimization Results", EditorStyles.boldLabel);
        
        EditorGUILayout.LabelField($"Assets Analyzed: {totalAssetsAnalyzed}");
        EditorGUILayout.LabelField($"Assets Optimized: {assetsOptimized}");
        EditorGUILayout.LabelField($"Memory Saved: {memorySavedMB} MB");
        
        if (optimizationResults.Count > 0)
        {
            GUILayout.Label("Recent Results:", EditorStyles.boldLabel);
            foreach (string result in optimizationResults)
            {
                EditorGUILayout.LabelField(result, EditorStyles.wordWrappedLabel);
            }
            
            if (GUILayout.Button("Clear Results"))
            {
                optimizationResults.Clear();
            }
        }
    }
    #endregion

    #region Texture Optimization
    /// <summary>
    /// Analyze all textures in the project.
    /// </summary>
    private void AnalyzeTextures()
    {
        string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
        totalAssetsAnalyzed = textureGUIDs.Length;
        
        optimizationResults.Add($"Found {textureGUIDs.Length} textures to analyze");
        
        int uncompressedCount = 0;
        long totalSize = 0;
        
        foreach (string guid in textureGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            
            if (importer != null)
            {
                var settings = importer.GetDefaultPlatformTextureSettings();
                if (settings.format == TextureImporterFormat.RGBA32 || 
                    settings.format == TextureImporterFormat.RGB24)
                {
                    uncompressedCount++;
                }
                
                Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                if (texture != null)
                {
                    totalSize += Profiling.Profiler.GetRuntimeMemorySizeLong(texture);
                }
            }
        }
        
        optimizationResults.Add($"Uncompressed textures: {uncompressedCount}");
        optimizationResults.Add($"Total texture memory: {totalSize / (1024 * 1024)} MB");
    }

    /// <summary>
    /// Optimize all textures in the project.
    /// </summary>
    private void OptimizeTextures()
    {
        string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
        assetsOptimized = 0;
        
        foreach (string guid in textureGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            
            if (importer != null && OptimizeTextureImporter(importer))
            {
                AssetDatabase.ImportAsset(path);
                assetsOptimized++;
            }
        }
        
        optimizationResults.Add($"Optimized {assetsOptimized} textures");
        AssetDatabase.Refresh();
    }

    /// <summary>
    /// Optimize a single texture importer.
    /// </summary>
    /// <param name="importer">Texture importer to optimize</param>
    /// <returns>True if optimization was applied</returns>
    private bool OptimizeTextureImporter(TextureImporter importer)
    {
        bool changed = false;
        
        // Set appropriate compression
        var settings = importer.GetDefaultPlatformTextureSettings();
        
        if (settings.format == TextureImporterFormat.RGBA32)
        {
            settings.format = TextureImporterFormat.DXT5;
            changed = true;
        }
        else if (settings.format == TextureImporterFormat.RGB24)
        {
            settings.format = TextureImporterFormat.DXT1;
            changed = true;
        }
        
        if (changed)
        {
            importer.SetPlatformTextureSettings(settings);
        }
        
        return changed;
    }

    /// <summary>
    /// Set texture compression for specific platform.
    /// </summary>
    /// <param name="platform">Target platform</param>
    private void SetTextureCompressionForPlatform(BuildTarget platform)
    {
        string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
        int optimized = 0;
        
        foreach (string guid in textureGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            
            if (importer != null)
            {
                var settings = importer.GetPlatformTextureSettings(platform.ToString());
                settings.overridden = true;
                
                // Set platform-specific compression
                if (platform == BuildTarget.StandaloneWindows64)
                {
                    settings.format = TextureImporterFormat.DXT5;
                }
                else if (platform == BuildTarget.PS4)
                {
                    settings.format = TextureImporterFormat.DXT5;
                }
                
                importer.SetPlatformTextureSettings(settings);
                AssetDatabase.ImportAsset(path);
                optimized++;
            }
        }
        
        optimizationResults.Add($"Set {platform} compression for {optimized} textures");
    }
    #endregion

    #region Audio Optimization
    /// <summary>
    /// Analyze all audio clips in the project.
    /// </summary>
    private void AnalyzeAudio()
    {
        string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
        totalAssetsAnalyzed += audioGUIDs.Length;
        
        optimizationResults.Add($"Found {audioGUIDs.Length} audio clips to analyze");
        
        int uncompressedCount = 0;
        long totalSize = 0;
        
        foreach (string guid in audioGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
            
            if (importer != null)
            {
                var settings = importer.defaultSampleSettings;
                if (settings.compressionFormat == AudioCompressionFormat.PCM)
                {
                    uncompressedCount++;
                }
                
                AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                if (clip != null)
                {
                    totalSize += Profiling.Profiler.GetRuntimeMemorySizeLong(clip);
                }
            }
        }
        
        optimizationResults.Add($"Uncompressed audio: {uncompressedCount}");
        optimizationResults.Add($"Total audio memory: {totalSize / (1024 * 1024)} MB");
    }

    /// <summary>
    /// Optimize all audio clips in the project.
    /// </summary>
    private void OptimizeAudio()
    {
        string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
        int optimized = 0;
        
        foreach (string guid in audioGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
            
            if (importer != null && OptimizeAudioImporter(importer))
            {
                AssetDatabase.ImportAsset(path);
                optimized++;
            }
        }
        
        optimizationResults.Add($"Optimized {optimized} audio clips");
        AssetDatabase.Refresh();
    }

    /// <summary>
    /// Optimize a single audio importer.
    /// </summary>
    /// <param name="importer">Audio importer to optimize</param>
    /// <returns>True if optimization was applied</returns>
    private bool OptimizeAudioImporter(AudioImporter importer)
    {
        bool changed = false;
        var settings = importer.defaultSampleSettings;
        
        // Compress audio if uncompressed
        if (settings.compressionFormat == AudioCompressionFormat.PCM)
        {
            settings.compressionFormat = AudioCompressionFormat.Vorbis;
            settings.quality = 0.7f; // Good quality compression
            importer.defaultSampleSettings = settings;
            changed = true;
        }
        
        return changed;
    }

    /// <summary>
    /// Set audio streaming for music files.
    /// </summary>
    /// <param name="enableStreaming">Whether to enable streaming</param>
    private void SetAudioStreaming(bool enableStreaming)
    {
        string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
        int streamingSet = 0;
        
        foreach (string guid in audioGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            
            // Check if this is likely a music file (in Music folder or long duration)
            if (path.Contains("/Music/") || path.Contains("/BGM/"))
            {
                AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
                if (importer != null)
                {
                    var settings = importer.defaultSampleSettings;
                    settings.loadType = enableStreaming ? AudioClipLoadType.Streaming : AudioClipLoadType.DecompressOnLoad;
                    importer.defaultSampleSettings = settings;
                    AssetDatabase.ImportAsset(path);
                    streamingSet++;
                }
            }
        }
        
        optimizationResults.Add($"Set streaming for {streamingSet} music files");
    }

    /// <summary>
    /// Set audio compression for sound effects.
    /// </summary>
    private void SetAudioCompression()
    {
        string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
        int compressed = 0;
        
        foreach (string guid in audioGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            
            // Check if this is likely a sound effect
            if (path.Contains("/SFX/") || path.Contains("/Sounds/"))
            {
                AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
                if (importer != null)
                {
                    var settings = importer.defaultSampleSettings;
                    settings.compressionFormat = AudioCompressionFormat.Vorbis;
                    settings.quality = 0.5f; // Lower quality for SFX
                    settings.loadType = AudioClipLoadType.DecompressOnLoad;
                    importer.defaultSampleSettings = settings;
                    AssetDatabase.ImportAsset(path);
                    compressed++;
                }
            }
        }
        
        optimizationResults.Add($"Set compression for {compressed} sound effects");
    }
    #endregion

    #region Asset Analysis
    /// <summary>
    /// Perform comprehensive asset analysis.
    /// </summary>
    private void PerformFullAssetAnalysis()
    {
        optimizationResults.Clear();
        optimizationResults.Add("=== FULL ASSET ANALYSIS ===");
        
        AnalyzeTextures();
        AnalyzeAudio();
        
        // Analyze other asset types
        string[] prefabGUIDs = AssetDatabase.FindAssets("t:Prefab");
        optimizationResults.Add($"Found {prefabGUIDs.Length} prefabs");
        
        string[] materialGUIDs = AssetDatabase.FindAssets("t:Material");
        optimizationResults.Add($"Found {materialGUIDs.Length} materials");
        
        string[] meshGUIDs = AssetDatabase.FindAssets("t:Mesh");
        optimizationResults.Add($"Found {meshGUIDs.Length} meshes");
        
        optimizationResults.Add("=== ANALYSIS COMPLETE ===");
    }

    /// <summary>
    /// Find unused assets in the project.
    /// </summary>
    private void FindUnusedAssets()
    {
        optimizationResults.Add("Unused asset detection is a complex operation.");
        optimizationResults.Add("Consider using Unity's Addressables or Asset Usage Detector.");
        optimizationResults.Add("Manual review recommended for critical assets.");
    }

    /// <summary>
    /// Analyze memory usage of assets.
    /// </summary>
    private void AnalyzeMemoryUsage()
    {
        long totalMemory = 0;
        
        // This is a simplified analysis - full memory profiling requires runtime data
        string[] allAssets = AssetDatabase.FindAssets("");
        
        foreach (string guid in allAssets)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (!string.IsNullOrEmpty(path) && !AssetDatabase.IsValidFolder(path))
            {
                Object asset = AssetDatabase.LoadAssetAtPath<Object>(path);
                if (asset != null)
                {
                    totalMemory += Profiling.Profiler.GetRuntimeMemorySizeLong(asset);
                }
            }
        }
        
        memorySavedMB = totalMemory / (1024 * 1024);
        optimizationResults.Add($"Estimated total asset memory: {memorySavedMB} MB");
    }

    /// <summary>
    /// Generate comprehensive optimization report.
    /// </summary>
    private void GenerateOptimizationReport()
    {
        string reportPath = "Assets/Scripts/Documentation/AssetOptimizationReport.txt";
        
        using (StreamWriter writer = new StreamWriter(reportPath))
        {
            writer.WriteLine("CINDER OF DARKNESS - ASSET OPTIMIZATION REPORT");
            writer.WriteLine("Generated: " + System.DateTime.Now.ToString());
            writer.WriteLine("==============================================");
            writer.WriteLine();
            
            writer.WriteLine($"Total Assets Analyzed: {totalAssetsAnalyzed}");
            writer.WriteLine($"Assets Optimized: {assetsOptimized}");
            writer.WriteLine($"Estimated Memory Usage: {memorySavedMB} MB");
            writer.WriteLine();
            
            writer.WriteLine("OPTIMIZATION RESULTS:");
            foreach (string result in optimizationResults)
            {
                writer.WriteLine(result);
            }
        }
        
        AssetDatabase.Refresh();
        optimizationResults.Add($"Report generated: {reportPath}");
    }
    #endregion
}
