using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Cinder of Darkness Input System Wrapper
/// Provides unified access to input actions and device detection
/// </summary>
public class CinderInput
{
    private InputActionAsset inputAsset;
    private InputActionMap gameplayMap;
    private InputActionMap uiMap;
    
    // Gameplay Actions
    public InputAction Move { get; private set; }
    public InputAction Look { get; private set; }
    public InputAction Attack { get; private set; }
    public InputAction Block { get; private set; }
    public InputAction Dodge { get; private set; }
    public InputAction Interact { get; private set; }
    public InputAction Jump { get; private set; }
    public InputAction Run { get; private set; }
    public InputAction Pause { get; private set; }
    
    // UI Actions
    public InputAction Navigate { get; private set; }
    public InputAction Submit { get; private set; }
    public InputAction Cancel { get; private set; }
    
    public InputActionAsset Asset => inputAsset;
    
    public CinderInput()
    {
        // Load the input actions asset
        inputAsset = Resources.Load<InputActionAsset>("CinderOfDarknessInputActions");
        
        if (inputAsset == null)
        {
            Debug.LogWarning("CinderOfDarknessInputActions asset not found in Resources folder");
            return;
        }
        
        // Get action maps
        gameplayMap = inputAsset.FindActionMap("Gameplay");
        uiMap = inputAsset.FindActionMap("UI");
        
        // Initialize gameplay actions
        if (gameplayMap != null)
        {
            Move = gameplayMap.FindAction("Move");
            Look = gameplayMap.FindAction("Look");
            Attack = gameplayMap.FindAction("Attack");
            Block = gameplayMap.FindAction("Block");
            Dodge = gameplayMap.FindAction("Dodge");
            Interact = gameplayMap.FindAction("Interact");
            Jump = gameplayMap.FindAction("Jump");
            Run = gameplayMap.FindAction("Run");
            Pause = gameplayMap.FindAction("Pause");
        }
        
        // Initialize UI actions
        if (uiMap != null)
        {
            Navigate = uiMap.FindAction("Navigate");
            Submit = uiMap.FindAction("Submit");
            Cancel = uiMap.FindAction("Cancel");
        }
    }
    
    public void EnableGameplay()
    {
        gameplayMap?.Enable();
        uiMap?.Disable();
    }
    
    public void EnableUI()
    {
        gameplayMap?.Disable();
        uiMap?.Enable();
    }
    
    public void EnableAll()
    {
        inputAsset?.Enable();
    }
    
    public void DisableAll()
    {
        inputAsset?.Disable();
    }
    
    public bool IsUsingGamepad()
    {
        var gamepad = Gamepad.current;
        return gamepad != null && gamepad.wasUpdatedThisFrame;
    }
    
    public bool IsUsingKeyboardMouse()
    {
        var keyboard = Keyboard.current;
        var mouse = Mouse.current;
        
        return (keyboard != null && keyboard.anyKey.wasPressedThisFrame) ||
               (mouse != null && (mouse.leftButton.wasPressedThisFrame || 
                                 mouse.rightButton.wasPressedThisFrame ||
                                 mouse.delta.ReadValue().magnitude > 0.1f));
    }
    
    public InputDevice GetCurrentDevice()
    {
        if (IsUsingGamepad())
            return Gamepad.current;
        if (IsUsingKeyboardMouse())
            return Keyboard.current ?? (InputDevice)Mouse.current;
        
        return null;
    }
    
    public string GetDeviceName()
    {
        var device = GetCurrentDevice();
        return device?.name ?? "Unknown";
    }
    
    public bool IsDeviceConnected<T>() where T : InputDevice
    {
        return InputSystem.GetDevice<T>() != null;
    }
    
    public Vector2 GetMoveInput()
    {
        return Move?.ReadValue<Vector2>() ?? Vector2.zero;
    }
    
    public Vector2 GetLookInput()
    {
        return Look?.ReadValue<Vector2>() ?? Vector2.zero;
    }
    
    public bool GetAttackPressed()
    {
        return Attack?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetBlockPressed()
    {
        return Block?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetDodgePressed()
    {
        return Dodge?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetInteractPressed()
    {
        return Interact?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetJumpPressed()
    {
        return Jump?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetRunHeld()
    {
        return Run?.IsPressed() ?? false;
    }
    
    public bool GetPausePressed()
    {
        return Pause?.WasPressedThisFrame() ?? false;
    }
    
    // UI Input Methods
    public Vector2 GetNavigateInput()
    {
        return Navigate?.ReadValue<Vector2>() ?? Vector2.zero;
    }
    
    public bool GetSubmitPressed()
    {
        return Submit?.WasPressedThisFrame() ?? false;
    }
    
    public bool GetCancelPressed()
    {
        return Cancel?.WasPressedThisFrame() ?? false;
    }
    
    public void Dispose()
    {
        DisableAll();
        inputAsset = null;
    }
}
