using UnityEngine;
using System;
using System.IO;
using System.Collections.Generic;
using System.Text;

/// <summary>
/// Crash Reporting System for Cinder of Darkness
/// Captures and logs exceptions and crashes for post-launch debugging
/// </summary>
public class CrashReportingSystem : MonoBehaviour
{
    [Header("Crash Reporting Settings")]
    public bool enableCrashReporting = true;
    public bool enableStackTraces = true;
    public bool enableSystemInfo = true;
    public bool enableScreenshots = false; // Privacy consideration
    public int maxCrashReports = 10;
    
    [Header("Auto-Submit Settings")]
    public bool enableAutoSubmit = false; // Requires user consent
    public bool userConsentForSubmission = false;
    public string submitEndpoint = ""; // Would be set to actual endpoint
    
    [Header("Privacy")]
    public bool anonymizePaths = true;
    public bool excludePersonalData = true;
    public int dataRetentionDays = 7;
    
    // Static instance
    private static CrashReportingSystem instance;
    public static CrashReportingSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CrashReportingSystem>();
                if (instance == null)
                {
                    GameObject go = new GameObject("CrashReportingSystem");
                    instance = go.AddComponent<CrashReportingSystem>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Crash report data structure
    [System.Serializable]
    public class CrashReport
    {
        public string reportId;
        public DateTime timestamp;
        public string gameVersion;
        public string unityVersion;
        public string platform;
        public string logType;
        public string condition;
        public string stackTrace;
        public SystemInfo systemInfo;
        public GameState gameState;
        public string[] recentLogs;
        
        public CrashReport()
        {
            reportId = Guid.NewGuid().ToString();
            timestamp = DateTime.Now;
            gameVersion = Application.version;
            unityVersion = Application.unityVersion;
            platform = Application.platform.ToString();
        }
    }
    
    [System.Serializable]
    public class SystemInfo
    {
        public string operatingSystem;
        public string processorType;
        public int processorCount;
        public int systemMemorySize;
        public string graphicsDeviceName;
        public int graphicsMemorySize;
        public string graphicsDeviceVersion;
        public bool supportsInstancing;
        public bool supportsComputeShaders;
        
        public SystemInfo()
        {
            operatingSystem = SystemInfo.operatingSystem;
            processorType = SystemInfo.processorType;
            processorCount = SystemInfo.processorCount;
            systemMemorySize = SystemInfo.systemMemorySize;
            graphicsDeviceName = SystemInfo.graphicsDeviceName;
            graphicsMemorySize = SystemInfo.graphicsMemorySize;
            graphicsDeviceVersion = SystemInfo.graphicsDeviceVersion;
            supportsInstancing = SystemInfo.supportsInstancing;
            supportsComputeShaders = SystemInfo.supportsComputeShaders;
        }
    }
    
    [System.Serializable]
    public class GameState
    {
        public string currentScene;
        public float playtime;
        public int playerLevel;
        public string lastAction;
        public Vector3 playerPosition;
        public bool isInCombat;
        public bool isInMenu;
        public string activeQuest;
        
        public GameState()
        {
            currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            playtime = Time.time;
            
            // Get player information if available
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                playerPosition = player.transform.position;
            }
            
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null)
            {
                playerLevel = playerStats.level;
            }
        }
    }
    
    // State tracking
    private string crashReportsPath;
    private List<string> recentLogMessages = new List<string>();
    private const int maxRecentLogs = 50;
    private bool isHandlingCrash = false;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCrashReporting();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        LoadUserPreferences();
        CleanupOldReports();
    }
    
    void InitializeCrashReporting()
    {
        crashReportsPath = Path.Combine(Application.persistentDataPath, "CrashReports");
        Directory.CreateDirectory(crashReportsPath);
        
        if (enableCrashReporting)
        {
            // Register for Unity's log message received callback
            Application.logMessageReceived += HandleLogMessage;
            
            #if UNITY_EDITOR
            Debug.Log("Crash Reporting System initialized");
            #endif
        }
    }
    
    void LoadUserPreferences()
    {
        userConsentForSubmission = PlayerPrefs.GetInt("CrashReporting_Consent", 0) == 1;
        
        // If user hasn't made a choice about crash report submission, ask
        if (!PlayerPrefs.HasKey("CrashReporting_Consent"))
        {
            ShowConsentDialog();
        }
    }
    
    void ShowConsentDialog()
    {
        // This would show a proper UI dialog in the full implementation
        // For now, we'll default to no submission for privacy
        userConsentForSubmission = false;
        PlayerPrefs.SetInt("CrashReporting_Consent", 0);
        PlayerPrefs.Save();
        
        #if UNITY_EDITOR
        Debug.Log("Crash reporting consent dialog would be shown here");
        #endif
    }
    
    void HandleLogMessage(string condition, string stackTrace, LogType type)
    {
        // Store recent log messages for context
        string logEntry = $"[{DateTime.Now:HH:mm:ss}] {type}: {condition}";
        recentLogMessages.Add(logEntry);
        
        if (recentLogMessages.Count > maxRecentLogs)
        {
            recentLogMessages.RemoveAt(0);
        }
        
        // Handle crashes and errors
        if (type == LogType.Exception || type == LogType.Error)
        {
            HandleCrashOrError(condition, stackTrace, type);
        }
    }
    
    void HandleCrashOrError(string condition, string stackTrace, LogType logType)
    {
        if (!enableCrashReporting || isHandlingCrash) return;
        
        isHandlingCrash = true;
        
        try
        {
            CrashReport report = CreateCrashReport(condition, stackTrace, logType);
            SaveCrashReport(report);
            
            // Optionally submit report if user consented
            if (enableAutoSubmit && userConsentForSubmission)
            {
                SubmitCrashReport(report);
            }
            
            #if UNITY_EDITOR
            Debug.Log($"Crash report created: {report.reportId}");
            #endif
        }
        catch (Exception e)
        {
            // Avoid infinite recursion if crash reporting itself crashes
            #if UNITY_EDITOR
            Debug.LogError($"Failed to create crash report: {e.Message}");
            #endif
        }
        finally
        {
            isHandlingCrash = false;
        }
    }
    
    CrashReport CreateCrashReport(string condition, string stackTrace, LogType logType)
    {
        CrashReport report = new CrashReport();
        
        report.logType = logType.ToString();
        report.condition = anonymizePaths ? AnonymizePaths(condition) : condition;
        
        if (enableStackTraces)
        {
            report.stackTrace = anonymizePaths ? AnonymizePaths(stackTrace) : stackTrace;
        }
        
        if (enableSystemInfo)
        {
            report.systemInfo = new SystemInfo();
        }
        
        report.gameState = new GameState();
        report.recentLogs = recentLogMessages.ToArray();
        
        return report;
    }
    
    string AnonymizePaths(string text)
    {
        if (string.IsNullOrEmpty(text)) return text;
        
        // Replace user-specific paths with generic placeholders
        string anonymized = text;
        
        // Replace common user paths
        string userProfile = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        if (!string.IsNullOrEmpty(userProfile))
        {
            anonymized = anonymized.Replace(userProfile, "[USER_PROFILE]");
        }
        
        string appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        if (!string.IsNullOrEmpty(appData))
        {
            anonymized = anonymized.Replace(appData, "[APP_DATA]");
        }
        
        // Replace Unity project paths
        string dataPath = Application.dataPath;
        if (!string.IsNullOrEmpty(dataPath))
        {
            anonymized = anonymized.Replace(dataPath, "[PROJECT_DATA]");
        }
        
        return anonymized;
    }
    
    void SaveCrashReport(CrashReport report)
    {
        try
        {
            string fileName = $"crash_{report.reportId}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            string filePath = Path.Combine(crashReportsPath, fileName);
            
            string jsonData = JsonUtility.ToJson(report, true);
            File.WriteAllText(filePath, jsonData);
            
            // Limit number of stored crash reports
            CleanupExcessReports();
        }
        catch (Exception e)
        {
            #if UNITY_EDITOR
            Debug.LogError($"Failed to save crash report: {e.Message}");
            #endif
        }
    }
    
    void SubmitCrashReport(CrashReport report)
    {
        if (string.IsNullOrEmpty(submitEndpoint)) return;
        
        // In a full implementation, this would submit the crash report to a server
        // For now, we'll just log that it would be submitted
        #if UNITY_EDITOR
        Debug.Log($"Would submit crash report {report.reportId} to {submitEndpoint}");
        #endif
        
        // Implementation would use UnityWebRequest to POST the crash report
        // StartCoroutine(SubmitCrashReportCoroutine(report));
    }
    
    void CleanupOldReports()
    {
        try
        {
            string[] reportFiles = Directory.GetFiles(crashReportsPath, "crash_*.json");
            DateTime cutoffDate = DateTime.Now.AddDays(-dataRetentionDays);
            
            foreach (string filePath in reportFiles)
            {
                FileInfo fileInfo = new FileInfo(filePath);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    File.Delete(filePath);
                }
            }
        }
        catch (Exception e)
        {
            #if UNITY_EDITOR
            Debug.LogError($"Failed to cleanup old crash reports: {e.Message}");
            #endif
        }
    }
    
    void CleanupExcessReports()
    {
        try
        {
            string[] reportFiles = Directory.GetFiles(crashReportsPath, "crash_*.json");
            
            if (reportFiles.Length > maxCrashReports)
            {
                // Sort by creation time and delete oldest
                Array.Sort(reportFiles, (x, y) => File.GetCreationTime(x).CompareTo(File.GetCreationTime(y)));
                
                int filesToDelete = reportFiles.Length - maxCrashReports;
                for (int i = 0; i < filesToDelete; i++)
                {
                    File.Delete(reportFiles[i]);
                }
            }
        }
        catch (Exception e)
        {
            #if UNITY_EDITOR
            Debug.LogError($"Failed to cleanup excess crash reports: {e.Message}");
            #endif
        }
    }
    
    void OnDestroy()
    {
        if (enableCrashReporting)
        {
            Application.logMessageReceived -= HandleLogMessage;
        }
    }
    
    // Public API
    public static void SetUserConsent(bool consent)
    {
        Instance.userConsentForSubmission = consent;
        PlayerPrefs.SetInt("CrashReporting_Consent", consent ? 1 : 0);
        PlayerPrefs.Save();
    }
    
    public static bool GetUserConsent()
    {
        return Instance.userConsentForSubmission;
    }
    
    public static void LogCustomCrash(string description, string additionalInfo = "")
    {
        if (Instance.enableCrashReporting)
        {
            Instance.HandleCrashOrError($"Custom Crash: {description}", additionalInfo, LogType.Exception);
        }
    }
    
    public static int GetCrashReportCount()
    {
        try
        {
            string[] reportFiles = Directory.GetFiles(Instance.crashReportsPath, "crash_*.json");
            return reportFiles.Length;
        }
        catch
        {
            return 0;
        }
    }
    
    public static void ClearAllCrashReports()
    {
        try
        {
            string[] reportFiles = Directory.GetFiles(Instance.crashReportsPath, "crash_*.json");
            foreach (string filePath in reportFiles)
            {
                File.Delete(filePath);
            }
        }
        catch (Exception e)
        {
            #if UNITY_EDITOR
            Debug.LogError($"Failed to clear crash reports: {e.Message}");
            #endif
        }
    }
}
