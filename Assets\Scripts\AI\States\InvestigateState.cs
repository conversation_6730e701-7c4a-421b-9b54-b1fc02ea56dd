using UnityEngine;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Investigate state for AI enemies.
    /// AI investigates suspicious sounds or last known player positions.
    /// </summary>
    public class InvestigateState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Investigate;
        #endregion

        #region Private Fields
        private Vector3 investigationPoint;
        private float investigationStartTime;
        private float searchRadius = 3f;
        private float searchTime = 5f;
        private bool hasReachedInvestigationPoint;
        private float nextLookTime;
        private float lookInterval = 1f;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize investigate state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public InvestigateState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter investigate state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            investigationStartTime = Time.time;
            hasReachedInvestigationPoint = false;
            nextLookTime = Time.time + Random.Range(0.5f, 1.5f);
            
            // Set investigation target
            if (Blackboard.InvestigationTarget != Vector3.zero)
            {
                investigationPoint = Blackboard.InvestigationTarget;
            }
            else if (Blackboard.LastKnownPlayerPosition != Vector3.zero)
            {
                investigationPoint = Blackboard.LastKnownPlayerPosition;
            }
            else
            {
                // No specific target, investigate current area
                investigationPoint = Blackboard.Transform.position;
                hasReachedInvestigationPoint = true;
            }
            
            // Set investigation speed (between patrol and chase)
            Blackboard.SetAgentSpeed((Blackboard.PatrolSpeed + Blackboard.ChaseSpeed) * 0.5f);
            
            // Set alert animation
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, true);
            
            // Mark as investigating
            Blackboard.IsInvestigating = true;
            Blackboard.InvestigationStartTime = Time.time;
            
            // Move to investigation point
            if (!hasReachedInvestigationPoint)
            {
                Blackboard.SetDestination(investigationPoint);
            }
        }

        /// <summary>
        /// Update investigate state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check common transitions first
            AIStateType? commonTransition = CheckCommonTransitions();
            if (commonTransition.HasValue)
            {
                return commonTransition;
            }

            // Check combat transitions (highest priority)
            AIStateType? combatTransition = CheckCombatTransitions();
            if (combatTransition.HasValue)
            {
                return combatTransition;
            }

            // Handle investigation behavior
            HandleInvestigation();

            // Check if investigation is complete
            if (IsInvestigationComplete())
            {
                return AIStateType.Patrol; // Return to patrol after investigation
            }

            // Update movement animation
            UpdateMovementAnimation();

            // Update player sight information
            Blackboard.UpdatePlayerSight();

            return null; // Continue investigating
        }

        /// <summary>
        /// Exit investigate state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
            
            // Clear investigation state
            Blackboard.IsInvestigating = false;
            Blackboard.InvestigationTarget = Vector3.zero;
            
            // Reset alert level
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, false);
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Handle investigation behavior.
        /// </summary>
        private void HandleInvestigation()
        {
            if (!hasReachedInvestigationPoint)
            {
                // Check if reached investigation point
                if (Blackboard.HasReachedDestination(1f))
                {
                    hasReachedInvestigationPoint = true;
                    Blackboard.StopMovement();
                }
            }
            else
            {
                // Search around the investigation point
                PerformSearchBehavior();
            }
        }

        /// <summary>
        /// Perform search behavior at investigation point.
        /// </summary>
        private void PerformSearchBehavior()
        {
            // Look around periodically
            if (Time.time >= nextLookTime)
            {
                LookAroundSuspiciously();
                nextLookTime = Time.time + Random.Range(lookInterval * 0.5f, lookInterval * 1.5f);
            }

            // Occasionally move to nearby positions to search
            float timeSinceReached = Time.time - (investigationStartTime + (searchTime * 0.3f));
            if (timeSinceReached > 0 && Random.Range(0f, 1f) < 0.1f * Time.deltaTime)
            {
                SearchNearbyArea();
            }
        }

        /// <summary>
        /// Look around suspiciously during investigation.
        /// </summary>
        private void LookAroundSuspiciously()
        {
            if (Blackboard.Transform == null) return;

            // Generate random look direction with bias towards investigation point
            Vector3 baseDirection = (investigationPoint - Blackboard.Transform.position).normalized;
            float randomAngle = Random.Range(-120f, 120f);
            Vector3 lookDirection = Quaternion.Euler(0, randomAngle, 0) * baseDirection;
            Vector3 lookTarget = Blackboard.Transform.position + lookDirection * 8f;

            // Look at the target
            LookAtTarget(lookTarget, 2f);
        }

        /// <summary>
        /// Search nearby area around investigation point.
        /// </summary>
        private void SearchNearbyArea()
        {
            Vector3 searchPosition = GetRandomSearchPosition();
            if (searchPosition != Vector3.zero)
            {
                Blackboard.SetDestination(searchPosition);
            }
        }

        /// <summary>
        /// Get random position near investigation point for searching.
        /// </summary>
        /// <returns>Random search position</returns>
        private Vector3 GetRandomSearchPosition()
        {
            Vector2 randomCircle = Random.insideUnitCircle * searchRadius;
            Vector3 searchPosition = investigationPoint + new Vector3(randomCircle.x, 0, randomCircle.y);

            // Sample NavMesh to ensure valid position
            if (UnityEngine.AI.NavMesh.SamplePosition(searchPosition, out UnityEngine.AI.NavMeshHit hit, searchRadius, UnityEngine.AI.NavMesh.AllAreas))
            {
                return hit.position;
            }

            return Vector3.zero;
        }

        /// <summary>
        /// Check if investigation is complete.
        /// </summary>
        /// <returns>True if investigation should end</returns>
        private bool IsInvestigationComplete()
        {
            float investigationTime = Time.time - investigationStartTime;
            
            // Complete if investigated for long enough
            if (investigationTime >= searchTime)
            {
                return true;
            }

            // Complete if player hasn't been detected for a while and reached investigation point
            if (hasReachedInvestigationPoint && !Blackboard.IsPlayerDetected && 
                !Blackboard.WasPlayerSeenRecently(2f))
            {
                return investigationTime >= searchTime * 0.5f; // Minimum investigation time
            }

            return false;
        }
        #endregion
    }
}
