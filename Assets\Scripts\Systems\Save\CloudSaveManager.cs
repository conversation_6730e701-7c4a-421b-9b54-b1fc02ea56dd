using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace CinderOfDarkness.Save
{
    /// <summary>
    /// Cloud Save Manager for Cinder of Darkness.
    /// Supports Steam Cloud and Firebase cloud save synchronization.
    /// </summary>
    public class CloudSaveManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Cloud Save Settings")]
        [SerializeField] private bool enableSteamCloud = true;
        [SerializeField] private bool enableFirebase = false;
        [SerializeField] private bool autoSyncOnStart = true;
        [SerializeField] private float autoSyncInterval = 300f; // 5 minutes
        [SerializeField] private int maxSaveSlots = 10;

        [Header("Save File Settings")]
        [SerializeField] private string saveFileExtension = ".cinder";
        [SerializeField] private bool compressSaveData = true;
        [SerializeField] private bool encryptSaveData = true;

        [Header("UI References")]
        [SerializeField] private GameObject cloudSyncIndicator;
        [SerializeField] private TMPro.TextMeshProUGUI syncStatusText;
        #endregion

        #region Public Properties
        public static CloudSaveManager Instance { get; private set; }
        public bool IsSyncing { get; private set; }
        public bool IsCloudAvailable { get; private set; }
        public CloudSaveStatus CurrentStatus { get; private set; } = CloudSaveStatus.Offline;
        #endregion

        #region Private Fields
        private Dictionary<string, SaveData> localSaves = new Dictionary<string, SaveData>();
        private Dictionary<string, SaveData> cloudSaves = new Dictionary<string, SaveData>();
        private string localSavePath;
        private Coroutine autoSyncCoroutine;

        // Steam Cloud integration
        private bool steamInitialized = false;

        // Firebase integration (placeholder for Firebase SDK)
        private bool firebaseInitialized = false;
        #endregion

        #region Events
        public System.Action<CloudSaveStatus> OnSaveStatusChanged;
        public System.Action<string> OnSaveCompleted;
        public System.Action<string> OnLoadCompleted;
        public System.Action<string> OnSyncCompleted;
        public System.Action<string> OnError;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Cloud Save Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeCloudSave();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Start auto-sync if enabled.
        /// </summary>
        private void Start()
        {
            if (autoSyncOnStart)
            {
                StartCoroutine(InitializeAndSync());
            }
        }

        /// <summary>
        /// Cleanup on destroy.
        /// </summary>
        private void OnDestroy()
        {
            if (autoSyncCoroutine != null)
            {
                StopCoroutine(autoSyncCoroutine);
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize cloud save systems.
        /// </summary>
        private void InitializeCloudSave()
        {
            localSavePath = Path.Combine(Application.persistentDataPath, "Saves");

            if (!Directory.Exists(localSavePath))
            {
                Directory.CreateDirectory(localSavePath);
            }

            LoadLocalSaves();
            InitializeSteamCloud();
            InitializeFirebase();
        }

        /// <summary>
        /// Initialize Steam Cloud integration.
        /// </summary>
        private void InitializeSteamCloud()
        {
            if (!enableSteamCloud) return;

            try
            {
                // Steam integration would go here
                // For now, simulate Steam availability
                steamInitialized = Application.platform == RuntimePlatform.WindowsPlayer ||
                                 Application.platform == RuntimePlatform.WindowsEditor;

                if (steamInitialized)
                {
                    Debug.Log("Steam Cloud initialized successfully");
                    IsCloudAvailable = true;
                    SetStatus(CloudSaveStatus.Online);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to initialize Steam Cloud: {e.Message}");
                steamInitialized = false;
            }
        }

        /// <summary>
        /// Initialize Firebase integration.
        /// </summary>
        private void InitializeFirebase()
        {
            if (!enableFirebase) return;

            try
            {
                // Firebase initialization would go here
                // For now, simulate Firebase availability
                firebaseInitialized = Application.internetReachability != NetworkReachability.NotReachable;

                if (firebaseInitialized)
                {
                    Debug.Log("Firebase initialized successfully");
                    IsCloudAvailable = true;
                    SetStatus(CloudSaveStatus.Online);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to initialize Firebase: {e.Message}");
                firebaseInitialized = false;
            }
        }

        /// <summary>
        /// Initialize and perform initial sync.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private IEnumerator InitializeAndSync()
        {
            yield return new WaitForSeconds(1f); // Wait for other systems

            if (IsCloudAvailable)
            {
                yield return StartCoroutine(SyncWithCloud());
            }

            if (autoSyncInterval > 0)
            {
                autoSyncCoroutine = StartCoroutine(AutoSyncCoroutine());
            }
        }
        #endregion

        #region Save Operations
        /// <summary>
        /// Save game data to local and cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <param name="saveData">Save data to store</param>
        /// <returns>Save operation task</returns>
        public async Task<bool> SaveGame(string saveSlot, SaveData saveData)
        {
            try
            {
                SetStatus(CloudSaveStatus.Saving);

                // Save locally first
                bool localSaved = await SaveLocally(saveSlot, saveData);
                if (!localSaved)
                {
                    OnError?.Invoke("Failed to save locally");
                    SetStatus(CloudSaveStatus.Error);
                    return false;
                }

                // Save to cloud if available
                if (IsCloudAvailable)
                {
                    bool cloudSaved = await SaveToCloud(saveSlot, saveData);
                    if (!cloudSaved)
                    {
                        Debug.LogWarning("Cloud save failed, but local save succeeded");
                    }
                }

                SetStatus(CloudSaveStatus.Online);
                OnSaveCompleted?.Invoke(saveSlot);
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Save operation failed: {e.Message}");
                OnError?.Invoke($"Save failed: {e.Message}");
                SetStatus(CloudSaveStatus.Error);
                return false;
            }
        }

        /// <summary>
        /// Load game data from local or cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Loaded save data</returns>
        public async Task<SaveData> LoadGame(string saveSlot)
        {
            try
            {
                SetStatus(CloudSaveStatus.Loading);

                // Try to load from cloud first if available
                if (IsCloudAvailable)
                {
                    SaveData cloudData = await LoadFromCloud(saveSlot);
                    if (cloudData != null)
                    {
                        // Update local save with cloud data
                        await SaveLocally(saveSlot, cloudData);
                        SetStatus(CloudSaveStatus.Online);
                        OnLoadCompleted?.Invoke(saveSlot);
                        return cloudData;
                    }
                }

                // Fall back to local save
                SaveData localData = await LoadLocally(saveSlot);
                if (localData != null)
                {
                    SetStatus(CloudSaveStatus.Online);
                    OnLoadCompleted?.Invoke(saveSlot);
                    return localData;
                }

                OnError?.Invoke("No save data found");
                SetStatus(CloudSaveStatus.Error);
                return null;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Load operation failed: {e.Message}");
                OnError?.Invoke($"Load failed: {e.Message}");
                SetStatus(CloudSaveStatus.Error);
                return null;
            }
        }

        /// <summary>
        /// Delete save data from local and cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Delete operation success</returns>
        public async Task<bool> DeleteSave(string saveSlot)
        {
            try
            {
                bool success = true;

                // Delete locally
                success &= await DeleteLocally(saveSlot);

                // Delete from cloud if available
                if (IsCloudAvailable)
                {
                    success &= await DeleteFromCloud(saveSlot);
                }

                return success;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Delete operation failed: {e.Message}");
                OnError?.Invoke($"Delete failed: {e.Message}");
                return false;
            }
        }
        #endregion

        #region Local Save Operations
        /// <summary>
        /// Save data locally.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <param name="saveData">Save data to store</param>
        /// <returns>Save operation success</returns>
        private async Task<bool> SaveLocally(string saveSlot, SaveData saveData)
        {
            try
            {
                string filePath = Path.Combine(localSavePath, saveSlot + saveFileExtension);
                string jsonData = JsonUtility.ToJson(saveData, true);

                if (encryptSaveData)
                {
                    jsonData = EncryptData(jsonData);
                }

                if (compressSaveData)
                {
                    jsonData = CompressData(jsonData);
                }

                await File.WriteAllTextAsync(filePath, jsonData);
                localSaves[saveSlot] = saveData;

                Debug.Log($"Save data written locally to: {filePath}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Local save failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load data locally.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Loaded save data</returns>
        private async Task<SaveData> LoadLocally(string saveSlot)
        {
            try
            {
                string filePath = Path.Combine(localSavePath, saveSlot + saveFileExtension);

                if (!File.Exists(filePath))
                {
                    return null;
                }

                string jsonData = await File.ReadAllTextAsync(filePath);

                if (compressSaveData)
                {
                    jsonData = DecompressData(jsonData);
                }

                if (encryptSaveData)
                {
                    jsonData = DecryptData(jsonData);
                }

                SaveData saveData = JsonUtility.FromJson<SaveData>(jsonData);
                localSaves[saveSlot] = saveData;

                Debug.Log($"Save data loaded locally from: {filePath}");
                return saveData;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Local load failed: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Delete data locally.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Delete operation success</returns>
        private async Task<bool> DeleteLocally(string saveSlot)
        {
            try
            {
                string filePath = Path.Combine(localSavePath, saveSlot + saveFileExtension);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                localSaves.Remove(saveSlot);

                Debug.Log($"Save data deleted locally: {filePath}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Local delete failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load all local saves.
        /// </summary>
        private void LoadLocalSaves()
        {
            try
            {
                string[] saveFiles = Directory.GetFiles(localSavePath, "*" + saveFileExtension);

                foreach (string filePath in saveFiles)
                {
                    string fileName = Path.GetFileNameWithoutExtension(filePath);
                    // Load save data asynchronously in background
                    _ = LoadLocally(fileName);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load local saves: {e.Message}");
            }
        }
        #endregion

        #region Cloud Save Operations
        /// <summary>
        /// Save data to cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <param name="saveData">Save data to store</param>
        /// <returns>Save operation success</returns>
        private async Task<bool> SaveToCloud(string saveSlot, SaveData saveData)
        {
            try
            {
                if (steamInitialized && enableSteamCloud)
                {
                    return await SaveToSteamCloud(saveSlot, saveData);
                }

                if (firebaseInitialized && enableFirebase)
                {
                    return await SaveToFirebase(saveSlot, saveData);
                }

                return false;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Cloud save failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load data from cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Loaded save data</returns>
        private async Task<SaveData> LoadFromCloud(string saveSlot)
        {
            try
            {
                if (steamInitialized && enableSteamCloud)
                {
                    return await LoadFromSteamCloud(saveSlot);
                }

                if (firebaseInitialized && enableFirebase)
                {
                    return await LoadFromFirebase(saveSlot);
                }

                return null;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Cloud load failed: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Delete data from cloud storage.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Delete operation success</returns>
        private async Task<bool> DeleteFromCloud(string saveSlot)
        {
            try
            {
                bool success = true;

                if (steamInitialized && enableSteamCloud)
                {
                    success &= await DeleteFromSteamCloud(saveSlot);
                }

                if (firebaseInitialized && enableFirebase)
                {
                    success &= await DeleteFromFirebase(saveSlot);
                }

                return success;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Cloud delete failed: {e.Message}");
                return false;
            }
        }
        #endregion

        #region Steam Cloud Operations
        /// <summary>
        /// Save data to Steam Cloud.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <param name="saveData">Save data to store</param>
        /// <returns>Save operation success</returns>
        private async Task<bool> SaveToSteamCloud(string saveSlot, SaveData saveData)
        {
            try
            {
                // Steam Cloud implementation would go here
                // For now, simulate Steam Cloud save
                await Task.Delay(100); // Simulate network delay

                cloudSaves[saveSlot] = saveData;
                Debug.Log($"Save data uploaded to Steam Cloud: {saveSlot}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Steam Cloud save failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load data from Steam Cloud.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Loaded save data</returns>
        private async Task<SaveData> LoadFromSteamCloud(string saveSlot)
        {
            try
            {
                // Steam Cloud implementation would go here
                // For now, simulate Steam Cloud load
                await Task.Delay(100); // Simulate network delay

                if (cloudSaves.ContainsKey(saveSlot))
                {
                    Debug.Log($"Save data downloaded from Steam Cloud: {saveSlot}");
                    return cloudSaves[saveSlot];
                }

                return null;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Steam Cloud load failed: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Delete data from Steam Cloud.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Delete operation success</returns>
        private async Task<bool> DeleteFromSteamCloud(string saveSlot)
        {
            try
            {
                // Steam Cloud implementation would go here
                // For now, simulate Steam Cloud delete
                await Task.Delay(50); // Simulate network delay

                cloudSaves.Remove(saveSlot);
                Debug.Log($"Save data deleted from Steam Cloud: {saveSlot}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Steam Cloud delete failed: {e.Message}");
                return false;
            }
        }
        #endregion

        #region Firebase Operations
        /// <summary>
        /// Save data to Firebase.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <param name="saveData">Save data to store</param>
        /// <returns>Save operation success</returns>
        private async Task<bool> SaveToFirebase(string saveSlot, SaveData saveData)
        {
            try
            {
                // Firebase implementation would go here
                // For now, simulate Firebase save
                await Task.Delay(150); // Simulate network delay

                cloudSaves[saveSlot] = saveData;
                Debug.Log($"Save data uploaded to Firebase: {saveSlot}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Firebase save failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load data from Firebase.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Loaded save data</returns>
        private async Task<SaveData> LoadFromFirebase(string saveSlot)
        {
            try
            {
                // Firebase implementation would go here
                // For now, simulate Firebase load
                await Task.Delay(150); // Simulate network delay

                if (cloudSaves.ContainsKey(saveSlot))
                {
                    Debug.Log($"Save data downloaded from Firebase: {saveSlot}");
                    return cloudSaves[saveSlot];
                }

                return null;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Firebase load failed: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Delete data from Firebase.
        /// </summary>
        /// <param name="saveSlot">Save slot identifier</param>
        /// <returns>Delete operation success</returns>
        private async Task<bool> DeleteFromFirebase(string saveSlot)
        {
            try
            {
                // Firebase implementation would go here
                // For now, simulate Firebase delete
                await Task.Delay(100); // Simulate network delay

                cloudSaves.Remove(saveSlot);
                Debug.Log($"Save data deleted from Firebase: {saveSlot}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Firebase delete failed: {e.Message}");
                return false;
            }
        }
        #endregion

        #region Sync Operations
        /// <summary>
        /// Synchronize local and cloud saves.
        /// </summary>
        /// <returns>Sync coroutine</returns>
        public IEnumerator SyncWithCloud()
        {
            if (!IsCloudAvailable)
            {
                yield break;
            }

            SetStatus(CloudSaveStatus.Syncing);
            IsSyncing = true;

            try
            {
                // Get all save slots
                HashSet<string> allSlots = new HashSet<string>();
                allSlots.UnionWith(localSaves.Keys);
                allSlots.UnionWith(cloudSaves.Keys);

                foreach (string slot in allSlots)
                {
                    yield return StartCoroutine(SyncSaveSlot(slot));
                }

                SetStatus(CloudSaveStatus.Online);
                OnSyncCompleted?.Invoke("All saves synchronized");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Sync failed: {e.Message}");
                OnError?.Invoke($"Sync failed: {e.Message}");
                SetStatus(CloudSaveStatus.Error);
            }
            finally
            {
                IsSyncing = false;
            }
        }

        /// <summary>
        /// Synchronize a specific save slot.
        /// </summary>
        /// <param name="saveSlot">Save slot to sync</param>
        /// <returns>Sync coroutine</returns>
        private IEnumerator SyncSaveSlot(string saveSlot)
        {
            bool hasLocal = localSaves.ContainsKey(saveSlot);
            bool hasCloud = cloudSaves.ContainsKey(saveSlot);

            if (hasLocal && hasCloud)
            {
                // Compare timestamps and sync newer version
                SaveData localSave = localSaves[saveSlot];
                SaveData cloudSave = cloudSaves[saveSlot];

                if (localSave.saveTime > cloudSave.saveTime)
                {
                    // Local is newer, upload to cloud
                    yield return StartCoroutine(UploadToCloud(saveSlot, localSave));
                }
                else if (cloudSave.saveTime > localSave.saveTime)
                {
                    // Cloud is newer, download to local
                    yield return StartCoroutine(DownloadFromCloud(saveSlot));
                }
            }
            else if (hasLocal && !hasCloud)
            {
                // Upload local to cloud
                yield return StartCoroutine(UploadToCloud(saveSlot, localSaves[saveSlot]));
            }
            else if (!hasLocal && hasCloud)
            {
                // Download cloud to local
                yield return StartCoroutine(DownloadFromCloud(saveSlot));
            }
        }

        /// <summary>
        /// Upload save to cloud.
        /// </summary>
        /// <param name="saveSlot">Save slot</param>
        /// <param name="saveData">Save data</param>
        /// <returns>Upload coroutine</returns>
        private IEnumerator UploadToCloud(string saveSlot, SaveData saveData)
        {
            var uploadTask = SaveToCloud(saveSlot, saveData);
            yield return new WaitUntil(() => uploadTask.IsCompleted);
        }

        /// <summary>
        /// Download save from cloud.
        /// </summary>
        /// <param name="saveSlot">Save slot</param>
        /// <returns>Download coroutine</returns>
        private IEnumerator DownloadFromCloud(string saveSlot)
        {
            var downloadTask = LoadFromCloud(saveSlot);
            yield return new WaitUntil(() => downloadTask.IsCompleted);

            if (downloadTask.Result != null)
            {
                var saveTask = SaveLocally(saveSlot, downloadTask.Result);
                yield return new WaitUntil(() => saveTask.IsCompleted);
            }
        }

        /// <summary>
        /// Auto-sync coroutine.
        /// </summary>
        /// <returns>Auto-sync coroutine</returns>
        private IEnumerator AutoSyncCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(autoSyncInterval);

                if (IsCloudAvailable && !IsSyncing)
                {
                    yield return StartCoroutine(SyncWithCloud());
                }
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Set cloud save status and update UI.
        /// </summary>
        /// <param name="status">New status</param>
        private void SetStatus(CloudSaveStatus status)
        {
            CurrentStatus = status;
            OnSaveStatusChanged?.Invoke(status);
            UpdateUI();
        }

        /// <summary>
        /// Update UI elements.
        /// </summary>
        private void UpdateUI()
        {
            if (cloudSyncIndicator != null)
            {
                cloudSyncIndicator.SetActive(IsSyncing);
            }

            if (syncStatusText != null)
            {
                syncStatusText.text = GetStatusText();
            }
        }

        /// <summary>
        /// Get status text for UI.
        /// </summary>
        /// <returns>Status text</returns>
        private string GetStatusText()
        {
            switch (CurrentStatus)
            {
                case CloudSaveStatus.Offline: return "Offline";
                case CloudSaveStatus.Online: return "Online";
                case CloudSaveStatus.Syncing: return "Syncing...";
                case CloudSaveStatus.Saving: return "Saving...";
                case CloudSaveStatus.Loading: return "Loading...";
                case CloudSaveStatus.Error: return "Error";
                default: return "Unknown";
            }
        }

        /// <summary>
        /// Encrypt save data.
        /// </summary>
        /// <param name="data">Data to encrypt</param>
        /// <returns>Encrypted data</returns>
        private string EncryptData(string data)
        {
            // Simple encryption implementation
            // In production, use proper encryption
            return System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(data));
        }

        /// <summary>
        /// Decrypt save data.
        /// </summary>
        /// <param name="encryptedData">Encrypted data</param>
        /// <returns>Decrypted data</returns>
        private string DecryptData(string encryptedData)
        {
            // Simple decryption implementation
            // In production, use proper decryption
            return System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(encryptedData));
        }

        /// <summary>
        /// Compress save data.
        /// </summary>
        /// <param name="data">Data to compress</param>
        /// <returns>Compressed data</returns>
        private string CompressData(string data)
        {
            // Simple compression implementation
            // In production, use proper compression like GZip
            return data; // Placeholder
        }

        /// <summary>
        /// Decompress save data.
        /// </summary>
        /// <param name="compressedData">Compressed data</param>
        /// <returns>Decompressed data</returns>
        private string DecompressData(string compressedData)
        {
            // Simple decompression implementation
            // In production, use proper decompression
            return compressedData; // Placeholder
        }

        /// <summary>
        /// Get all available save slots.
        /// </summary>
        /// <returns>List of save slots</returns>
        public List<string> GetAvailableSaveSlots()
        {
            HashSet<string> allSlots = new HashSet<string>();
            allSlots.UnionWith(localSaves.Keys);
            allSlots.UnionWith(cloudSaves.Keys);
            return new List<string>(allSlots);
        }

        /// <summary>
        /// Check if save slot exists.
        /// </summary>
        /// <param name="saveSlot">Save slot to check</param>
        /// <returns>True if save exists</returns>
        public bool SaveExists(string saveSlot)
        {
            return localSaves.ContainsKey(saveSlot) || cloudSaves.ContainsKey(saveSlot);
        }

        /// <summary>
        /// Force sync with cloud.
        /// </summary>
        public void ForceSyncWithCloud()
        {
            if (IsCloudAvailable && !IsSyncing)
            {
                StartCoroutine(SyncWithCloud());
            }
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Cloud save status enumeration.
    /// </summary>
    public enum CloudSaveStatus
    {
        Offline,
        Online,
        Syncing,
        Saving,
        Loading,
        Error
    }

    /// <summary>
    /// Save data structure for Cinder of Darkness.
    /// </summary>
    [System.Serializable]
    public class SaveData
    {
        public string saveSlot;
        public string playerName;
        public int level;
        public float playTime;
        public Vector3 playerPosition;
        public string currentScene;
        public System.DateTime saveTime;
        public Dictionary<string, object> gameData;

        public SaveData()
        {
            gameData = new Dictionary<string, object>();
            saveTime = System.DateTime.Now;
        }
    }
    #endregion
}
