using UnityEngine;
using System.Collections;

public class MythicalCreature : MonoBehaviour
{
    [Header("Creature Identity")]
    public CreatureType creatureType = CreatureType.Oni;
    public string creatureName = "";
    public MythologyOrigin mythology = MythologyOrigin.Japanese;
    
    [Header("Mythical Abilities")]
    public MythicalAbility[] abilities;
    public float mythicalPower = 100f;
    public bool isLegendary = false;
    public ElementalAffinity elementalType = ElementalAffinity.None;
    
    [Header("Appearance")]
    public GameObject[] creatureModels;
    public Material[] mythicalMaterials;
    public ParticleSystem[] auraEffects;
    public Light creatureAura;
    
    [Header("Behavior")]
    public CreatureBehavior behavior = CreatureBehavior.Territorial;
    public float aggressionLevel = 50f;
    public bool canSpeak = false;
    public string[] ancientWisdom;
    
    [Header("Cultural Respect")]
    public string culturalSignificance = "";
    public bool requiresRitual = false;
    public string[] culturalTaboos;
    
    private EnemyAI enemyAI;
    private EnemyHealth enemyHealth;
    private AudioSource creatureVoice;
    private Animator creatureAnimator;
    private bool hasBeenAwakened = false;
    
    public enum CreatureType
    {
        // Japanese Mythology
        Oni,            // Demon ogre
        Kitsune,        // Fox spirit
        Tengu,          // Bird-like warrior
        Yokai,          // General spirit
        
        // Arabic Mythology
        Ifrit,          // Fire djinn
        Marid,          // Water djinn
        Ghul,           // Desert demon
        Roc,            // Giant bird
        
        // Greek Mythology
        Chimera,        // Lion-goat-serpent
        Minotaur,       // Bull-headed humanoid
        Harpy,          // Bird-woman
        Cyclops,        // One-eyed giant
        
        // Norse Mythology
        Draugr,         // Undead warrior
        Jotun,          // Frost giant
        Fenrir,         // Giant wolf
        Valkyrie,       // Warrior maiden
        
        // Celtic Mythology
        Banshee,        // Wailing spirit
        Selkie,         // Seal-person
        Dullahan,       // Headless horseman
        
        // Egyptian Mythology
        Sphinx,         // Lion-human hybrid
        Anubis,         // Jackal-headed god
        
        // Slavic Mythology
        Baba_Yaga,      // Witch
        Domovoi,        // House spirit
        
        // Hindu Mythology
        Rakshasa,       // Shape-shifting demon
        Naga,           // Serpent being
        
        // Aztec Mythology
        Quetzalcoatl,   // Feathered serpent
        Tzitzimimeh     // Star demon
    }
    
    public enum MythologyOrigin
    {
        Japanese,
        Arabic,
        Greek,
        Norse,
        Celtic,
        Egyptian,
        Slavic,
        Hindu,
        Aztec,
        African,
        Native_American,
        Chinese
    }
    
    public enum ElementalAffinity
    {
        None,
        Fire,
        Water,
        Earth,
        Air,
        Lightning,
        Ice,
        Shadow,
        Light,
        Spirit
    }
    
    public enum CreatureBehavior
    {
        Peaceful,       // Won't attack unless provoked
        Territorial,    // Defends specific area
        Aggressive,     // Attacks on sight
        Trickster,      // Plays tricks on player
        Guardian,       // Protects something important
        Wise,           // Offers knowledge/quests
        Cursed,         // Bound by ancient curse
        Vengeful        // Seeks revenge for past wrongs
    }
    
    [System.Serializable]
    public class MythicalAbility
    {
        public string abilityName;
        public string culturalName;
        public AbilityType type;
        public float power;
        public float cooldown;
        public GameObject effectPrefab;
        public AudioClip abilitySound;
        public string culturalDescription;
    }
    
    public enum AbilityType
    {
        ElementalAttack,
        Teleportation,
        Shapeshifting,
        Illusion,
        Curse,
        Blessing,
        Summoning,
        Regeneration,
        PhaseShift,
        TimeManipulation
    }
    
    void Start()
    {
        enemyAI = GetComponent<EnemyAI>();
        enemyHealth = GetComponent<EnemyHealth>();
        creatureVoice = GetComponent<AudioSource>();
        creatureAnimator = GetComponent<Animator>();
        
        InitializeCreature();
        SetupMythicalAppearance();
        ConfigureCreatureBehavior();
        
        // Subscribe to events
        if (enemyHealth != null)
        {
            enemyHealth.OnDeath += OnCreatureDeath;
        }
    }
    
    void Update()
    {
        UpdateMythicalEffects();
        HandleMythicalBehavior();
    }
    
    void InitializeCreature()
    {
        switch (creatureType)
        {
            case CreatureType.Oni:
                InitializeOni();
                break;
            case CreatureType.Ifrit:
                InitializeIfrit();
                break;
            case CreatureType.Chimera:
                InitializeChimera();
                break;
            case CreatureType.Draugr:
                InitializeDraugr();
                break;
            case CreatureType.Sphinx:
                InitializeSphinx();
                break;
            // Add more creature initializations
        }
        
        Debug.Log($"Initialized {mythology} {creatureType}: {creatureName}");
    }
    
    void InitializeOni()
    {
        creatureName = "Akuma no Oni";
        mythology = MythologyOrigin.Japanese;
        elementalType = ElementalAffinity.Fire;
        behavior = CreatureBehavior.Aggressive;
        aggressionLevel = 80f;
        culturalSignificance = "Demons that punish the wicked and test the virtuous";
        
        // Oni abilities
        abilities = new MythicalAbility[]
        {
            new MythicalAbility
            {
                abilityName = "Demon's Rage",
                culturalName = "Oni no Ikari",
                type = AbilityType.ElementalAttack,
                power = 150f,
                cooldown = 8f,
                culturalDescription = "The oni's fury manifests as hellfire"
            },
            new MythicalAbility
            {
                abilityName = "Iron Club Strike",
                culturalName = "Tetsubo no Uchi",
                type = AbilityType.ElementalAttack,
                power = 200f,
                cooldown = 12f,
                culturalDescription = "Devastating blow with the oni's iron club"
            }
        };
        
        ancientWisdom = new string[]
        {
            "Strength without honor is mere brutality.",
            "Even demons can find redemption through righteous battle.",
            "The path of the warrior is paved with both victory and defeat."
        };
    }
    
    void InitializeIfrit()
    {
        creatureName = "Malik al-Nar"; // King of Fire
        mythology = MythologyOrigin.Arabic;
        elementalType = ElementalAffinity.Fire;
        behavior = CreatureBehavior.Territorial;
        aggressionLevel = 60f;
        canSpeak = true;
        culturalSignificance = "Powerful fire djinn, rulers of the flame realm";
        
        abilities = new MythicalAbility[]
        {
            new MythicalAbility
            {
                abilityName = "Desert Inferno",
                culturalName = "Nar as-Sahra",
                type = AbilityType.ElementalAttack,
                power = 120f,
                cooldown = 6f,
                culturalDescription = "Summons the burning winds of the desert"
            },
            new MythicalAbility
            {
                abilityName = "Flame Teleport",
                culturalName = "Intiqal an-Nar",
                type = AbilityType.Teleportation,
                power = 0f,
                cooldown = 10f,
                culturalDescription = "Travels through flames instantaneously"
            }
        };
        
        ancientWisdom = new string[]
        {
            "Fire purifies, but it also destroys. Choose wisely.",
            "The desert teaches patience to those who would survive.",
            "Power without wisdom burns even its wielder."
        };
    }
    
    void InitializeChimera()
    {
        creatureName = "Khimaira the Threefold";
        mythology = MythologyOrigin.Greek;
        elementalType = ElementalAffinity.Fire;
        behavior = CreatureBehavior.Guardian;
        aggressionLevel = 70f;
        isLegendary = true;
        culturalSignificance = "Guardian beast with three natures: courage, cunning, and venom";
        
        abilities = new MythicalAbility[]
        {
            new MythicalAbility
            {
                abilityName = "Triple Strike",
                culturalName = "Tria Plege",
                type = AbilityType.ElementalAttack,
                power = 180f,
                cooldown = 15f,
                culturalDescription = "Lion's courage, goat's cunning, serpent's venom"
            },
            new MythicalAbility
            {
                abilityName = "Breath of Destruction",
                culturalName = "Pneuma Katastrophes",
                type = AbilityType.ElementalAttack,
                power = 250f,
                cooldown = 20f,
                culturalDescription = "Devastating breath that combines all three natures"
            }
        };
        
        ancientWisdom = new string[]
        {
            "Three paths converge in the heart of the beast.",
            "Courage without cunning is folly; cunning without courage is cowardice.",
            "The greatest monsters are born from the greatest virtues corrupted."
        };
    }
    
    void InitializeDraugr()
    {
        creatureName = "Grimvar the Deathless";
        mythology = MythologyOrigin.Norse;
        elementalType = ElementalAffinity.Ice;
        behavior = CreatureBehavior.Vengeful;
        aggressionLevel = 90f;
        culturalSignificance = "Undead warrior seeking honor in death";
        
        abilities = new MythicalAbility[]
        {
            new MythicalAbility
            {
                abilityName = "Frost Weapon",
                culturalName = "Ísvápn",
                type = AbilityType.ElementalAttack,
                power = 140f,
                cooldown = 8f,
                culturalDescription = "Weapon imbued with the cold of Helheim"
            },
            new MythicalAbility
            {
                abilityName = "Undead Resilience",
                culturalName = "Dauðadygð",
                type = AbilityType.Regeneration,
                power = 50f,
                cooldown = 25f,
                culturalDescription = "The strength of the dishonored dead"
            }
        };
        
        ancientWisdom = new string[]
        {
            "Death is not the end for those who die without honor.",
            "The cold preserves both flesh and fury.",
            "Only by facing death can one truly live."
        };
    }
    
    void InitializeSphinx()
    {
        creatureName = "Khenti-Ka the Riddler";
        mythology = MythologyOrigin.Egyptian;
        elementalType = ElementalAffinity.Light;
        behavior = CreatureBehavior.Wise;
        aggressionLevel = 30f;
        canSpeak = true;
        requiresRitual = true;
        culturalSignificance = "Guardian of ancient wisdom and divine knowledge";
        
        abilities = new MythicalAbility[]
        {
            new MythicalAbility
            {
                abilityName = "Riddle of Ages",
                culturalName = "Heka netjer",
                type = AbilityType.Curse,
                power = 0f,
                cooldown = 30f,
                culturalDescription = "Poses riddles that bind the mind"
            },
            new MythicalAbility
            {
                abilityName = "Solar Judgment",
                culturalName = "Duat Ra",
                type = AbilityType.ElementalAttack,
                power = 300f,
                cooldown = 45f,
                culturalDescription = "Calls upon the judgment of Ra"
            }
        };
        
        ancientWisdom = new string[]
        {
            "Knowledge is the greatest treasure, but wisdom is knowing how to use it.",
            "The riddle of existence has many answers, all of them true.",
            "Those who seek truth must first face themselves."
        };
        
        culturalTaboos = new string[]
        {
            "Never approach without proper respect",
            "Answer riddles truthfully or face divine wrath",
            "Do not disturb the sacred burial grounds"
        };
    }
    
    void SetupMythicalAppearance()
    {
        // Activate appropriate model
        if (creatureModels.Length > 0)
        {
            foreach (GameObject model in creatureModels)
            {
                model.SetActive(false);
            }
            creatureModels[(int)creatureType % creatureModels.Length].SetActive(true);
        }
        
        // Apply mythical materials
        if (mythicalMaterials.Length > 0)
        {
            Renderer[] renderers = GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.material = mythicalMaterials[Random.Range(0, mythicalMaterials.Length)];
            }
        }
        
        // Setup aura effects
        if (creatureAura != null)
        {
            Color auraColor = GetElementalColor(elementalType);
            creatureAura.color = auraColor;
            creatureAura.intensity = isLegendary ? 3f : 1.5f;
        }
        
        // Activate particle effects
        foreach (ParticleSystem effect in auraEffects)
        {
            if (effect != null)
            {
                var main = effect.main;
                main.startColor = GetElementalColor(elementalType);
                effect.Play();
            }
        }
    }
    
    Color GetElementalColor(ElementalAffinity element)
    {
        switch (element)
        {
            case ElementalAffinity.Fire: return Color.red;
            case ElementalAffinity.Water: return Color.blue;
            case ElementalAffinity.Earth: return Color.green;
            case ElementalAffinity.Air: return Color.white;
            case ElementalAffinity.Lightning: return Color.yellow;
            case ElementalAffinity.Ice: return Color.cyan;
            case ElementalAffinity.Shadow: return Color.black;
            case ElementalAffinity.Light: return Color.white;
            case ElementalAffinity.Spirit: return Color.magenta;
            default: return Color.gray;
        }
    }
    
    void ConfigureCreatureBehavior()
    {
        if (enemyAI != null)
        {
            // Modify AI based on creature behavior
            switch (behavior)
            {
                case CreatureBehavior.Peaceful:
                    enemyAI.currentState = EnemyAI.AIState.Patrol;
                    enemyAI.detectionRange *= 0.5f;
                    break;
                case CreatureBehavior.Territorial:
                    enemyAI.patrolRadius *= 2f;
                    enemyAI.loseTargetRange *= 0.5f;
                    break;
                case CreatureBehavior.Aggressive:
                    enemyAI.detectionRange *= 1.5f;
                    enemyAI.attackDamage *= 1.3f;
                    break;
                case CreatureBehavior.Wise:
                    enemyAI.currentState = EnemyAI.AIState.Patrol;
                    enemyAI.patrolSpeed *= 0.5f;
                    break;
            }
            
            // Apply mythical power scaling
            enemyAI.attackDamage *= (mythicalPower / 100f);
            
            if (isLegendary)
            {
                enemyAI.attackDamage *= 2f;
                enemyHealth.maxHealth *= 3f;
            }
        }
    }
    
    void UpdateMythicalEffects()
    {
        // Pulse aura based on creature's state
        if (creatureAura != null)
        {
            float pulseIntensity = Mathf.Sin(Time.time * 2f) * 0.5f + 1f;
            creatureAura.intensity = (isLegendary ? 3f : 1.5f) * pulseIntensity;
        }
        
        // Update particle effects based on health
        if (enemyHealth != null)
        {
            float healthPercentage = enemyHealth.GetHealthPercentage();
            foreach (ParticleSystem effect in auraEffects)
            {
                if (effect != null)
                {
                    var emission = effect.emission;
                    emission.rateOverTime = 50f * healthPercentage;
                }
            }
        }
    }
    
    void HandleMythicalBehavior()
    {
        switch (behavior)
        {
            case CreatureBehavior.Trickster:
                HandleTricksterBehavior();
                break;
            case CreatureBehavior.Wise:
                HandleWiseBehavior();
                break;
            case CreatureBehavior.Guardian:
                HandleGuardianBehavior();
                break;
        }
    }
    
    void HandleTricksterBehavior()
    {
        // Randomly teleport or create illusions
        if (Random.Range(0f, 1f) < 0.001f) // Very rare
        {
            PerformTricksterAbility();
        }
    }
    
    void HandleWiseBehavior()
    {
        // Offer wisdom to nearby players
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            float distance = Vector3.Distance(transform.position, player.transform.position);
            if (distance < 5f && canSpeak && Random.Range(0f, 1f) < 0.01f)
            {
                SpeakWisdom();
            }
        }
    }
    
    void HandleGuardianBehavior()
    {
        // Protect specific area or object
        // Implementation would depend on what the creature is guarding
    }
    
    void PerformTricksterAbility()
    {
        // Create illusion or teleport
        if (abilities.Length > 0)
        {
            MythicalAbility ability = abilities[Random.Range(0, abilities.Length)];
            if (ability.type == AbilityType.Teleportation || ability.type == AbilityType.Illusion)
            {
                StartCoroutine(ExecuteMythicalAbility(ability));
            }
        }
    }
    
    void SpeakWisdom()
    {
        if (ancientWisdom.Length > 0)
        {
            string wisdom = ancientWisdom[Random.Range(0, ancientWisdom.Length)];
            Debug.Log($"{creatureName} speaks: \"{wisdom}\"");
            
            // Display wisdom to player UI
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"{creatureName}: {wisdom}");
                StartCoroutine(HideWisdomAfterDelay(gameUI, 5f));
            }
        }
    }
    
    IEnumerator HideWisdomAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    IEnumerator ExecuteMythicalAbility(MythicalAbility ability)
    {
        Debug.Log($"{creatureName} uses {ability.abilityName}!");
        
        // Play ability sound
        if (ability.abilitySound != null && creatureVoice != null)
        {
            creatureVoice.PlayOneShot(ability.abilitySound);
        }
        
        // Create ability effect
        if (ability.effectPrefab != null)
        {
            Instantiate(ability.effectPrefab, transform.position, transform.rotation);
        }
        
        // Execute ability based on type
        switch (ability.type)
        {
            case AbilityType.Teleportation:
                yield return StartCoroutine(PerformTeleportation());
                break;
            case AbilityType.ElementalAttack:
                PerformElementalAttack(ability);
                break;
            case AbilityType.Regeneration:
                PerformRegeneration(ability);
                break;
        }
        
        yield return new WaitForSeconds(ability.cooldown);
    }
    
    IEnumerator PerformTeleportation()
    {
        // Fade out
        Renderer[] renderers = GetComponentsInChildren<Renderer>();
        float fadeTime = 0.5f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = 1f - (elapsed / fadeTime);
            
            foreach (Renderer renderer in renderers)
            {
                Color color = renderer.material.color;
                color.a = alpha;
                renderer.material.color = color;
            }
            
            yield return null;
        }
        
        // Teleport to new position
        Vector3 newPosition = transform.position + Random.insideUnitSphere * 10f;
        newPosition.y = transform.position.y;
        transform.position = newPosition;
        
        // Fade in
        elapsed = 0f;
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = elapsed / fadeTime;
            
            foreach (Renderer renderer in renderers)
            {
                Color color = renderer.material.color;
                color.a = alpha;
                renderer.material.color = color;
            }
            
            yield return null;
        }
    }
    
    void PerformElementalAttack(MythicalAbility ability)
    {
        // Find nearby targets
        Collider[] targets = Physics.OverlapSphere(transform.position, 5f);
        
        foreach (Collider target in targets)
        {
            PlayerStats playerStats = target.GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                playerStats.TakeDamage(ability.power);
                Debug.Log($"{creatureName}'s {ability.abilityName} hits for {ability.power} damage!");
            }
        }
    }
    
    void PerformRegeneration(MythicalAbility ability)
    {
        if (enemyHealth != null)
        {
            enemyHealth.Heal(ability.power);
            Debug.Log($"{creatureName} regenerates {ability.power} health!");
        }
    }
    
    void OnCreatureDeath()
    {
        Debug.Log($"The legendary {creatureName} has fallen!");
        
        // Create special death effects for legendary creatures
        if (isLegendary)
        {
            CreateLegendaryDeathEffect();
        }
        
        // Drop mythical loot
        DropMythicalLoot();
    }
    
    void CreateLegendaryDeathEffect()
    {
        // Create spectacular death effect
        GameObject deathEffect = new GameObject("LegendaryDeath");
        deathEffect.transform.position = transform.position;
        
        ParticleSystem particles = deathEffect.AddComponent<ParticleSystem>();
        var main = particles.main;
        main.startColor = GetElementalColor(elementalType);
        main.startSize = 5f;
        main.startLifetime = 3f;
        main.maxParticles = 1000;
        
        var emission = particles.emission;
        emission.rateOverTime = 300f;
        
        Destroy(deathEffect, 5f);
    }
    
    void DropMythicalLoot()
    {
        // Create mythical essence or artifact
        GameObject loot = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        loot.name = $"{creatureName}_Essence";
        loot.transform.position = transform.position + Vector3.up;
        loot.transform.localScale = Vector3.one * 0.5f;
        
        Renderer lootRenderer = loot.GetComponent<Renderer>();
        lootRenderer.material.color = GetElementalColor(elementalType);
        lootRenderer.material.EnableKeyword("_EMISSION");
        lootRenderer.material.SetColor("_EmissionColor", GetElementalColor(elementalType) * 2f);
        
        // Add floating animation
        loot.AddComponent<FloatingLoot>();
    }
    
    public bool CanInteract()
    {
        return behavior == CreatureBehavior.Wise || behavior == CreatureBehavior.Peaceful;
    }
    
    public string GetCulturalInfo()
    {
        return $"{mythology} mythology: {culturalSignificance}";
    }
}
