using UnityEngine;

/// <summary>
/// Test script to verify all systems are properly integrated and working
/// This script can be attached to a test GameObject to verify functionality
/// </summary>
public class SystemIntegrationTest : MonoBehaviour
{
    [Header("Test Settings")]
    public bool runTestsOnStart = true;
    public bool verboseLogging = true;
    
    private PlayerController playerController;
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private PsychologicalSystem psycheSystem;
    private MultiInputControlSystem inputSystem;
    private BrutalCombatSystem brutalCombat;
    private ElementalMagicSystem magicSystem;
    private GameManager gameManager;
    private GameUI gameUI;
    
    void Start()
    {
        if (runTestsOnStart)
        {
            StartCoroutine(RunSystemTests());
        }
    }
    
    System.Collections.IEnumerator RunSystemTests()
    {
        Log("=== CINDER OF DARKNESS SYSTEM INTEGRATION TEST ===");
        
        yield return new WaitForSeconds(1f);
        
        // Test 1: Find all core systems
        Log("Test 1: Finding Core Systems...");
        FindAllSystems();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 2: Verify system initialization
        Log("Test 2: Verifying System Initialization...");
        TestSystemInitialization();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 3: Test input system
        Log("Test 3: Testing Input System...");
        TestInputSystem();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 4: Test player stats
        Log("Test 4: Testing Player Stats...");
        TestPlayerStats();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 5: Test combat system
        Log("Test 5: Testing Combat System...");
        TestCombatSystem();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 6: Test psychological system
        Log("Test 6: Testing Psychological System...");
        TestPsychologicalSystem();
        
        yield return new WaitForSeconds(0.5f);
        
        // Test 7: Test magic system
        Log("Test 7: Testing Magic System...");
        TestMagicSystem();
        
        yield return new WaitForSeconds(0.5f);
        
        Log("=== SYSTEM INTEGRATION TEST COMPLETE ===");
        Log("All systems appear to be properly integrated!");
    }
    
    void FindAllSystems()
    {
        playerController = FindObjectOfType<PlayerController>();
        playerStats = FindObjectOfType<PlayerStats>();
        playerCombat = FindObjectOfType<PlayerCombat>();
        psycheSystem = FindObjectOfType<PsychologicalSystem>();
        inputSystem = FindObjectOfType<MultiInputControlSystem>();
        brutalCombat = FindObjectOfType<BrutalCombatSystem>();
        magicSystem = FindObjectOfType<ElementalMagicSystem>();
        gameManager = FindObjectOfType<GameManager>();
        gameUI = FindObjectOfType<GameUI>();
        
        Log($"PlayerController: {(playerController != null ? "✓" : "✗")}");
        Log($"PlayerStats: {(playerStats != null ? "✓" : "✗")}");
        Log($"PlayerCombat: {(playerCombat != null ? "✓" : "✗")}");
        Log($"PsychologicalSystem: {(psycheSystem != null ? "✓" : "✗")}");
        Log($"MultiInputControlSystem: {(inputSystem != null ? "✓" : "✗")}");
        Log($"BrutalCombatSystem: {(brutalCombat != null ? "✓" : "✗")}");
        Log($"ElementalMagicSystem: {(magicSystem != null ? "✓" : "✗")}");
        Log($"GameManager: {(gameManager != null ? "✓" : "✗")}");
        Log($"GameUI: {(gameUI != null ? "✓" : "✗")}");
    }
    
    void TestSystemInitialization()
    {
        bool allInitialized = true;
        
        if (playerController != null)
        {
            Log("PlayerController initialized ✓");
        }
        else
        {
            Log("PlayerController missing ✗");
            allInitialized = false;
        }
        
        if (playerStats != null)
        {
            Log($"PlayerStats - Health: {playerStats.GetCurrentHealth()}/{playerStats.maxHealth} ✓");
        }
        else
        {
            Log("PlayerStats missing ✗");
            allInitialized = false;
        }
        
        if (inputSystem != null)
        {
            Log($"Input System - Active Device: {inputSystem.GetActiveDeviceType()} ✓");
        }
        else
        {
            Log("Input System missing ✗");
            allInitialized = false;
        }
        
        if (gameManager != null)
        {
            Log($"GameManager - State: {gameManager.GetCurrentState()} ✓");
        }
        else
        {
            Log("GameManager missing ✗");
            allInitialized = false;
        }
        
        Log($"System Initialization: {(allInitialized ? "PASS" : "FAIL")}");
    }
    
    void TestInputSystem()
    {
        if (inputSystem == null)
        {
            Log("Input System not found - SKIP");
            return;
        }
        
        Log($"Active Device Type: {inputSystem.GetActiveDeviceType()}");
        Log($"Controller Connected: {inputSystem.IsControllerConnected()}");
        Log($"PlayStation Controller: {inputSystem.IsPlayStationController()}");
        Log($"Xbox Controller: {inputSystem.IsXboxController()}");
        Log($"Keyboard/Mouse: {inputSystem.IsKeyboardMouse()}");
        
        Log("Input System: PASS");
    }
    
    void TestPlayerStats()
    {
        if (playerStats == null)
        {
            Log("Player Stats not found - SKIP");
            return;
        }
        
        Log($"Health: {playerStats.GetHealthPercentage() * 100:F1}%");
        Log($"Mana: {playerStats.GetManaPercentage() * 100:F1}%");
        Log($"Stamina: {playerStats.GetStaminaPercentage() * 100:F1}%");
        Log($"Moral Path: {playerStats.GetCurrentPath()}");
        Log($"Sun Alignment: {playerStats.GetSunAlignment()}");
        Log($"Moon Alignment: {playerStats.GetMoonAlignment()}");
        
        Log("Player Stats: PASS");
    }
    
    void TestCombatSystem()
    {
        if (playerCombat == null)
        {
            Log("Player Combat not found - SKIP");
            return;
        }
        
        Log($"Current Weapon: {playerCombat.GetCurrentWeapon()}");
        Log($"Is Attacking: {playerCombat.IsAttacking()}");
        Log($"Is Casting Magic: {playerCombat.IsCastingMagic()}");
        
        if (brutalCombat != null)
        {
            Log("Brutal Combat System found ✓");
        }
        
        Log("Combat System: PASS");
    }
    
    void TestPsychologicalSystem()
    {
        if (psycheSystem == null)
        {
            Log("Psychological System not found - SKIP");
            return;
        }
        
        Log($"Mental Stability: {psycheSystem.GetMentalStability()}");
        Log($"Trauma Level: {psycheSystem.GetTrauma()}");
        Log($"Enlightenment: {psycheSystem.GetEnlightenment()}");
        Log($"Psychological State: {psycheSystem.GetCurrentState()}");
        
        Log("Psychological System: PASS");
    }
    
    void TestMagicSystem()
    {
        if (magicSystem == null)
        {
            Log("Magic System not found - SKIP");
            return;
        }
        
        Log($"Current Mana: {magicSystem.GetCurrentMana()}/{magicSystem.GetMaxMana()}");
        Log($"Fire Affinity: {magicSystem.GetElementAffinity(ElementalMagicSystem.ElementType.Fire)}");
        Log($"Available Spells: {magicSystem.GetAvailableSpells().Count}");
        
        Log("Magic System: PASS");
    }
    
    void Log(string message)
    {
        if (verboseLogging)
        {
            Debug.Log($"[SystemTest] {message}");
        }
    }
    
    // Public method to run tests manually
    [ContextMenu("Run System Tests")]
    public void RunTests()
    {
        StartCoroutine(RunSystemTests());
    }
}
