using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using UnityEngine.InputSystem;

public class GameUI : MonoBehaviour
{
    [Header("HUD Elements")]
    public Slider healthBar;
    public Slider manaBar;
    public Slider staminaBar;
    public Text healthText;
    public Text manaText;
    public Text staminaText;

    [Header("Character Info")]
    public Text levelText;
    public Text pathText;
    public Text weaponText;

    [Header("Interaction")]
    public GameObject interactionPrompt;
    public Text interactionText;

    [Header("Menus")]
    public GameObject pauseMenu;
    public GameObject characterMenu;
    public GameObject settingsMenu;
    public GameObject gameOverScreen;

    [Header("Dialogue UI")]
    public GameObject dialoguePanel;
    public Text speakerNameText;
    public Text dialogueText;
    public Transform choicesParent;
    public GameObject choiceButtonPrefab;

    [Header("Crosshair")]
    public GameObject crosshair;
    public Image crosshairImage;

    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private PlayerController playerController;
    private bool isPaused = false;
    private bool isCharacterMenuOpen = false;

    void Start()
    {
        // Find player components
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            playerStats = player.GetComponent<PlayerStats>();
            playerCombat = player.GetComponent<PlayerCombat>();
            playerController = player;
        }

        // Initialize UI
        InitializeUI();

        // Setup dialogue system references
        DialogueSystem dialogueSystem = FindObjectOfType<DialogueSystem>();
        if (dialogueSystem != null)
        {
            dialogueSystem.dialoguePanel = dialoguePanel;
            dialogueSystem.speakerNameText = speakerNameText;
            dialogueSystem.dialogueText = dialogueText;
            dialogueSystem.choicesParent = choicesParent;
            dialogueSystem.choiceButtonPrefab = choiceButtonPrefab;
        }
    }

    void Update()
    {
        HandleInput();
        UpdateHUD();
        UpdateCrosshair();
    }

    void HandleInput()
    {
        // Use new Input System if available, fallback to old system
        bool pausePressed = false;
        bool characterMenuPressed = false;

        var keyboard = Keyboard.current;
        if (keyboard != null)
        {
            pausePressed = keyboard.escapeKey.wasPressedThisFrame;
            characterMenuPressed = keyboard.tabKey.wasPressedThisFrame;
        }
        else
        {
            // Fallback to old input system
            pausePressed = Input.GetKeyDown(KeyCode.Escape);
            characterMenuPressed = Input.GetKeyDown(KeyCode.Tab);
        }

        // Pause menu
        if (pausePressed)
        {
            if (isPaused)
                ResumeGame();
            else
                PauseGame();
        }

        // Character menu
        if (characterMenuPressed)
        {
            ToggleCharacterMenu();
        }
    }

    void InitializeUI()
    {
        // Hide all menus initially
        if (pauseMenu != null) pauseMenu.SetActive(false);
        if (characterMenu != null) characterMenu.SetActive(false);
        if (settingsMenu != null) settingsMenu.SetActive(false);
        if (gameOverScreen != null) gameOverScreen.SetActive(false);
        if (dialoguePanel != null) dialoguePanel.SetActive(false);
        if (interactionPrompt != null) interactionPrompt.SetActive(false);

        // Show crosshair
        if (crosshair != null) crosshair.SetActive(true);
    }

    void UpdateHUD()
    {
        if (playerStats == null) return;

        // Update health bar
        if (healthBar != null)
        {
            healthBar.value = playerStats.GetHealthPercentage();
        }
        if (healthText != null)
        {
            healthText.text = $"{playerStats.GetCurrentHealth():F0}/{playerStats.maxHealth:F0}";
        }

        // Update mana bar
        if (manaBar != null)
        {
            manaBar.value = playerStats.GetManaPercentage();
        }
        if (manaText != null)
        {
            manaText.text = $"{playerStats.GetCurrentMana():F0}/{playerStats.maxMana:F0}";
        }

        // Update stamina bar
        if (staminaBar != null)
        {
            staminaBar.value = playerStats.GetStaminaPercentage();
        }
        if (staminaText != null)
        {
            staminaText.text = $"{playerStats.GetCurrentStamina():F0}/{playerStats.maxStamina:F0}";
        }

        // Update character info
        if (pathText != null)
        {
            pathText.text = $"Path: {playerStats.GetCurrentPath()}";
        }

        if (weaponText != null && playerCombat != null)
        {
            weaponText.text = $"Weapon: {playerCombat.GetCurrentWeapon()}";
        }
    }

    void UpdateCrosshair()
    {
        if (crosshairImage == null || playerController == null) return;

        // Change crosshair based on perspective
        if (playerController.IsFirstPerson())
        {
            crosshairImage.color = Color.white;
            crosshair.SetActive(true);
        }
        else
        {
            crosshair.SetActive(false);
        }
    }

    public void PauseGame()
    {
        isPaused = true;
        Time.timeScale = 0f;

        if (pauseMenu != null)
            pauseMenu.SetActive(true);

        // Unlock cursor
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }

    public void ResumeGame()
    {
        isPaused = false;
        Time.timeScale = 1f;

        if (pauseMenu != null)
            pauseMenu.SetActive(false);

        if (settingsMenu != null)
            settingsMenu.SetActive(false);

        // Lock cursor again
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    public void ToggleCharacterMenu()
    {
        isCharacterMenuOpen = !isCharacterMenuOpen;

        if (characterMenu != null)
            characterMenu.SetActive(isCharacterMenuOpen);

        if (isCharacterMenuOpen)
        {
            Time.timeScale = 0f;
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        else
        {
            Time.timeScale = 1f;
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
    }

    public void ShowInteractionPrompt(string text)
    {
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(true);
            if (interactionText != null)
                interactionText.text = text;
        }
    }

    public void HideInteractionPrompt()
    {
        if (interactionPrompt != null)
            interactionPrompt.SetActive(false);
    }

    public void ShowGameOver()
    {
        if (gameOverScreen != null)
        {
            gameOverScreen.SetActive(true);
            Time.timeScale = 0f;
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
    }

    // Button callbacks
    public void OnResumeClicked()
    {
        ResumeGame();
    }

    public void OnSettingsClicked()
    {
        if (settingsMenu != null)
        {
            settingsMenu.SetActive(true);
            pauseMenu.SetActive(false);
        }
    }

    public void OnBackToMenuClicked()
    {
        Time.timeScale = 1f;
        SceneManager.LoadScene("MainMenu");
    }

    public void OnQuitClicked()
    {
        Application.Quit();
    }

    public void OnRestartClicked()
    {
        Time.timeScale = 1f;
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    // Settings callbacks
    public void OnVolumeChanged(float volume)
    {
        AudioListener.volume = volume;
    }

    public void OnMouseSensitivityChanged(float sensitivity)
    {
        if (playerController != null)
        {
            playerController.mouseSensitivity = sensitivity;
        }
    }

    public void OnGraphicsQualityChanged(int qualityIndex)
    {
        QualitySettings.SetQualityLevel(qualityIndex);
    }
}
