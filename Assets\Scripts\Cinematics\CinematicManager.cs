using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Cinematic system for Cinder of Darkness intro and outro sequences
/// Integrates with Timeline, Cinemachine, and voice acting for professional cutscenes
/// </summary>
public class CinematicManager : MonoBehaviour
{
    [Header("Cinematic Settings")]
    public bool enableCinematics = true;
    public bool allowSkipping = true;
    public KeyCode skipKey = KeyCode.Escape;
    public float fadeInDuration = 1f;
    public float fadeOutDuration = 1f;
    
    [Header("Timeline Assets")]
    public TimelineAsset introTimeline;
    public TimelineAsset outroGoodTimeline;
    public TimelineAsset outroEvilTimeline;
    public TimelineAsset outroNeutralTimeline;
    
    [Header("Playable Directors")]
    public PlayableDirector mainDirector;
    public PlayableDirector uiDirector;
    
    [Header("Cameras")]
    public Camera cinematicCamera;
    public Camera gameplayCamera;
    
    [Header("UI Elements")]
    public GameObject cinematicUI;
    public GameObject skipPrompt;
    public CanvasGroup fadeCanvasGroup;
    
    // Static instance
    private static CinematicManager instance;
    public static CinematicManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CinematicManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("CinematicManager");
                    instance = go.AddComponent<CinematicManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Cinematic state
    private bool isPlayingCinematic = false;
    private bool canSkip = false;
    private CinematicType currentCinematic;
    private System.Action onCinematicComplete;
    
    public enum CinematicType
    {
        Intro,
        OutroGood,
        OutroEvil,
        OutroNeutral
    }
    
    // Cinematic data
    [System.Serializable]
    public class CinematicSequence
    {
        public string sequenceName;
        public TimelineAsset timeline;
        public string[] voiceLines;
        public float duration;
        public bool allowSkip;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCinematicManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupCinematicComponents();
        CreatePlaceholderTimelines();
    }
    
    void InitializeCinematicManager()
    {
        Debug.Log("Cinematic Manager initialized");
    }
    
    void SetupCinematicComponents()
    {
        // Setup PlayableDirector if not assigned
        if (mainDirector == null)
        {
            mainDirector = gameObject.AddComponent<PlayableDirector>();
        }
        
        // Setup cameras
        if (cinematicCamera == null)
        {
            GameObject camObj = new GameObject("CinematicCamera");
            cinematicCamera = camObj.AddComponent<Camera>();
            cinematicCamera.enabled = false;
            cinematicCamera.depth = 10; // Higher than gameplay camera
        }
        
        if (gameplayCamera == null)
        {
            gameplayCamera = Camera.main;
        }
        
        // Setup UI
        if (fadeCanvasGroup == null)
        {
            CreateFadeUI();
        }
        
        if (skipPrompt == null)
        {
            CreateSkipPrompt();
        }
    }
    
    void CreateFadeUI()
    {
        GameObject fadeObj = new GameObject("CinematicFade");
        Canvas canvas = fadeObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        fadeCanvasGroup = fadeObj.AddComponent<CanvasGroup>();
        fadeCanvasGroup.alpha = 0f;
        fadeCanvasGroup.blocksRaycasts = false;
        
        GameObject imageObj = new GameObject("FadeImage");
        imageObj.transform.SetParent(fadeObj.transform, false);
        
        var image = imageObj.AddComponent<UnityEngine.UI.Image>();
        image.color = Color.black;
        
        var rect = imageObj.GetComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;
        
        DontDestroyOnLoad(fadeObj);
    }
    
    void CreateSkipPrompt()
    {
        GameObject skipObj = new GameObject("SkipPrompt");
        Canvas canvas = skipObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 999;
        
        GameObject textObj = new GameObject("SkipText");
        textObj.transform.SetParent(skipObj.transform, false);
        
        var text = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        text.text = "Press ESC to skip";
        text.fontSize = 18;
        text.color = Color.white;
        text.alignment = TMPro.TextAlignmentOptions.BottomRight;
        
        var rect = textObj.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(0.7f, 0.05f);
        rect.anchorMax = new Vector2(0.95f, 0.15f);
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;
        
        skipPrompt = skipObj;
        skipPrompt.SetActive(false);
        
        DontDestroyOnLoad(skipObj);
    }
    
    void CreatePlaceholderTimelines()
    {
        // Create placeholder timeline assets
        // In production, these would be created in the Timeline window
        
        if (introTimeline == null)
        {
            introTimeline = ScriptableObject.CreateInstance<TimelineAsset>();
            introTimeline.name = "IntroTimeline";
        }
        
        if (outroGoodTimeline == null)
        {
            outroGoodTimeline = ScriptableObject.CreateInstance<TimelineAsset>();
            outroGoodTimeline.name = "OutroGoodTimeline";
        }
        
        if (outroEvilTimeline == null)
        {
            outroEvilTimeline = ScriptableObject.CreateInstance<TimelineAsset>();
            outroEvilTimeline.name = "OutroEvilTimeline";
        }
        
        if (outroNeutralTimeline == null)
        {
            outroNeutralTimeline = ScriptableObject.CreateInstance<TimelineAsset>();
            outroNeutralTimeline.name = "OutroNeutralTimeline";
        }
    }
    
    void Update()
    {
        if (isPlayingCinematic && allowSkipping && canSkip)
        {
            if (Input.GetKeyDown(skipKey) || Input.GetKeyDown(KeyCode.Space))
            {
                SkipCinematic();
            }
        }
    }
    
    // Public API
    public static void PlayIntroCinematic(System.Action onComplete = null)
    {
        Instance.PlayCinematicInternal(CinematicType.Intro, onComplete);
    }
    
    public static void PlayOutroCinematic(string moralPath, System.Action onComplete = null)
    {
        CinematicType cinematicType = CinematicType.OutroNeutral;
        
        switch (moralPath.ToLower())
        {
            case "good":
            case "light":
                cinematicType = CinematicType.OutroGood;
                break;
            case "evil":
            case "dark":
                cinematicType = CinematicType.OutroEvil;
                break;
            default:
                cinematicType = CinematicType.OutroNeutral;
                break;
        }
        
        Instance.PlayCinematicInternal(cinematicType, onComplete);
    }
    
    public static bool IsPlayingCinematic()
    {
        return Instance.isPlayingCinematic;
    }
    
    public static void SkipCurrentCinematic()
    {
        Instance.SkipCinematic();
    }
    
    void PlayCinematicInternal(CinematicType cinematicType, System.Action onComplete = null)
    {
        if (!enableCinematics)
        {
            onComplete?.Invoke();
            return;
        }
        
        if (isPlayingCinematic)
        {
            Debug.LogWarning("Cinematic already playing!");
            return;
        }
        
        currentCinematic = cinematicType;
        onCinematicComplete = onComplete;
        
        StartCoroutine(PlayCinematicSequence(cinematicType));
    }
    
    IEnumerator PlayCinematicSequence(CinematicType cinematicType)
    {
        isPlayingCinematic = true;
        canSkip = false;
        
        // Disable gameplay camera and enable cinematic camera
        if (gameplayCamera != null)
            gameplayCamera.enabled = false;
        
        if (cinematicCamera != null)
            cinematicCamera.enabled = true;
        
        // Show cinematic UI
        if (cinematicUI != null)
            cinematicUI.SetActive(true);
        
        // Fade in
        yield return StartCoroutine(FadeIn());
        
        // Get timeline for this cinematic
        TimelineAsset timeline = GetTimelineForCinematic(cinematicType);
        
        if (timeline != null && mainDirector != null)
        {
            // Setup and play timeline
            mainDirector.playableAsset = timeline;
            mainDirector.Play();
            
            // Enable skipping after a short delay
            yield return new WaitForSeconds(2f);
            canSkip = allowSkipping;
            
            if (canSkip && skipPrompt != null)
                skipPrompt.SetActive(true);
            
            // Play voice lines
            PlayCinematicVoiceLines(cinematicType);
            
            // Wait for timeline to complete or be skipped
            while (mainDirector.state == PlayState.Playing && isPlayingCinematic)
            {
                yield return null;
            }
        }
        else
        {
            // Fallback: play without timeline
            yield return StartCoroutine(PlayFallbackCinematic(cinematicType));
        }
        
        // Hide skip prompt
        if (skipPrompt != null)
            skipPrompt.SetActive(false);
        
        // Fade out
        yield return StartCoroutine(FadeOut());
        
        // Restore gameplay camera
        if (cinematicCamera != null)
            cinematicCamera.enabled = false;
        
        if (gameplayCamera != null)
            gameplayCamera.enabled = true;
        
        // Hide cinematic UI
        if (cinematicUI != null)
            cinematicUI.SetActive(false);
        
        isPlayingCinematic = false;
        canSkip = false;
        
        // Invoke completion callback
        onCinematicComplete?.Invoke();
        onCinematicComplete = null;
    }
    
    TimelineAsset GetTimelineForCinematic(CinematicType cinematicType)
    {
        switch (cinematicType)
        {
            case CinematicType.Intro:
                return introTimeline;
            case CinematicType.OutroGood:
                return outroGoodTimeline;
            case CinematicType.OutroEvil:
                return outroEvilTimeline;
            case CinematicType.OutroNeutral:
                return outroNeutralTimeline;
            default:
                return null;
        }
    }
    
    void PlayCinematicVoiceLines(CinematicType cinematicType)
    {
        switch (cinematicType)
        {
            case CinematicType.Intro:
                VoiceActingSystem.PlayVoiceLine("narrator_intro");
                break;
                
            case CinematicType.OutroGood:
                VoiceActingSystem.PlayVoiceLine("narrator_outro_good");
                break;
                
            case CinematicType.OutroEvil:
                VoiceActingSystem.PlayVoiceLine("narrator_outro_evil");
                break;
                
            case CinematicType.OutroNeutral:
                VoiceActingSystem.PlayVoiceLine("narrator_outro_good"); // Use good ending as neutral
                break;
        }
    }
    
    IEnumerator PlayFallbackCinematic(CinematicType cinematicType)
    {
        // Fallback cinematic without Timeline
        float duration = GetFallbackDuration(cinematicType);
        
        Debug.Log($"Playing fallback cinematic: {cinematicType} ({duration}s)");
        
        // Simple camera movement or static shot
        Vector3 startPos = cinematicCamera.transform.position;
        Vector3 endPos = startPos + Vector3.forward * 5f;
        
        float elapsed = 0f;
        while (elapsed < duration && isPlayingCinematic)
        {
            float progress = elapsed / duration;
            cinematicCamera.transform.position = Vector3.Lerp(startPos, endPos, progress);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
    }
    
    float GetFallbackDuration(CinematicType cinematicType)
    {
        switch (cinematicType)
        {
            case CinematicType.Intro:
                return 15f;
            case CinematicType.OutroGood:
            case CinematicType.OutroEvil:
            case CinematicType.OutroNeutral:
                return 10f;
            default:
                return 5f;
        }
    }
    
    void SkipCinematic()
    {
        if (!isPlayingCinematic || !canSkip) return;
        
        Debug.Log("Skipping cinematic");
        
        // Stop timeline
        if (mainDirector != null && mainDirector.state == PlayState.Playing)
        {
            mainDirector.Stop();
        }
        
        // Stop voice acting
        VoiceActingSystem.StopCurrentVoice();
        
        // This will cause the cinematic coroutine to exit
        isPlayingCinematic = false;
    }
    
    IEnumerator FadeIn()
    {
        if (fadeCanvasGroup == null) yield break;
        
        fadeCanvasGroup.alpha = 1f;
        fadeCanvasGroup.blocksRaycasts = true;
        
        float elapsed = 0f;
        while (elapsed < fadeInDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / fadeInDuration;
            fadeCanvasGroup.alpha = Mathf.Lerp(1f, 0f, progress);
            yield return null;
        }
        
        fadeCanvasGroup.alpha = 0f;
        fadeCanvasGroup.blocksRaycasts = false;
    }
    
    IEnumerator FadeOut()
    {
        if (fadeCanvasGroup == null) yield break;
        
        fadeCanvasGroup.alpha = 0f;
        fadeCanvasGroup.blocksRaycasts = true;
        
        float elapsed = 0f;
        while (elapsed < fadeOutDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / fadeOutDuration;
            fadeCanvasGroup.alpha = Mathf.Lerp(0f, 1f, progress);
            yield return null;
        }
        
        fadeCanvasGroup.alpha = 1f;
    }
    
    // Integration with game systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        switch (eventName)
        {
            case "GameStarted":
                // Play intro cinematic when game starts
                PlayIntroCinematic(() => {
                    Debug.Log("Intro cinematic completed");
                });
                break;
                
            case "GameCompleted":
                string moralPath = parameters?.ContainsKey("moralPath") == true ? parameters["moralPath"].ToString() : "neutral";
                PlayOutroCinematic(moralPath, () => {
                    Debug.Log($"Outro cinematic completed: {moralPath}");
                    // Return to main menu or credits
                    SceneLoader.LoadSceneAsync("MainMenu");
                });
                break;
        }
    }
    
    // Settings integration
    public void SetCinematicsEnabled(bool enabled)
    {
        enableCinematics = enabled;
    }
    
    public void SetSkippingAllowed(bool allowed)
    {
        allowSkipping = allowed;
    }
    
    // Debug methods
    public void TestIntroCinematic()
    {
        #if UNITY_EDITOR
        PlayIntroCinematic(() => Debug.Log("Test intro completed"));
        #endif
    }
    
    public void TestOutroCinematic(string moralPath)
    {
        #if UNITY_EDITOR
        PlayOutroCinematic(moralPath, () => Debug.Log($"Test outro completed: {moralPath}"));
        #endif
    }
}
