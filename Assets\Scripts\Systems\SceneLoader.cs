using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using System.Collections;
using System.Threading.Tasks;
using TMPro;

/// <summary>
/// Robust scene loading system with async operations and transition effects
/// Handles all scene transitions for Cinder of Darkness
/// </summary>
public class SceneLoader : MonoBehaviour
{
    [Header("Loading UI")]
    public GameObject loadingPanel;
    public Slider loadingProgressBar;
    public TextMeshProUGUI loadingText;
    public TextMeshProUGUI tipText;
    public Image backgroundImage;
    public CanvasGroup fadeCanvasGroup;
    
    [Header("Transition Settings")]
    public float fadeInDuration = 1f;
    public float fadeOutDuration = 1f;
    public float minimumLoadingTime = 2f;
    
    [Header("Loading Tips")]
    public string[] loadingTips = {
        "The path of darkness reveals hidden truths...",
        "Every choice echoes through the realms...",
        "Ancient magic flows through those who seek it...",
        "Honor and brutality walk hand in hand...",
        "The Cinderborn's destiny awaits...",
        "Cultural wisdom guides the worthy...",
        "Death teaches the greatest lessons...",
        "Contemplation brings inner strength..."
    };
    
    // Static instance for global access
    private static SceneLoader instance;
    public static SceneLoader Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<SceneLoader>();
                if (instance == null)
                {
                    GameObject go = new GameObject("SceneLoader");
                    instance = go.AddComponent<SceneLoader>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Scene registry
    public static class Scenes
    {
        public const string MAIN_MENU = "MainMenu";
        public const string GAME_SCENE = "GameScene";
        public const string STARTING_VILLAGE = "StartingVillage";
        public const string KINGDOM_OF_LIGHT = "KingdomOfLight";
        public const string ASHLANDS = "Ashlands";
        public const string FOREST_OF_SHADOWS = "ForestOfShadows";
        public const string FROZEN_NORTH = "FrozenNorth";
        public const string QADESH_WASTES = "QadeshWastes";
        public const string JOTUNHEIM_REACHES = "JotunheimReaches";
        public const string CERULEAN_ARCHIPELAGO = "CeruleanArchipelago";
        public const string BURNING_DEPTHS = "BurningDepths";
        public const string CELESTIAL_SPIRES = "CelestialSpires";
        public const string SUNKEN_ATLAS = "SunkenAtlas";
    }
    
    private bool isLoading = false;
    private Coroutine currentLoadingCoroutine;
    
    void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSceneLoader();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void InitializeSceneLoader()
    {
        // Create loading UI if it doesn't exist
        if (loadingPanel == null)
        {
            CreateLoadingUI();
        }
        
        // Hide loading panel initially
        if (loadingPanel != null)
        {
            loadingPanel.SetActive(false);
        }
        
        Debug.Log("SceneLoader initialized");
    }
    
    void CreateLoadingUI()
    {
        // Create loading UI programmatically if not assigned
        GameObject canvas = new GameObject("LoadingCanvas");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasComponent.sortingOrder = 1000; // Ensure it's on top
        
        CanvasScaler scaler = canvas.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        canvas.AddComponent<GraphicRaycaster>();
        
        // Create loading panel
        loadingPanel = new GameObject("LoadingPanel");
        loadingPanel.transform.SetParent(canvas.transform, false);
        
        Image panelImage = loadingPanel.AddComponent<Image>();
        panelImage.color = Color.black;
        
        RectTransform panelRect = loadingPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create fade canvas group
        fadeCanvasGroup = loadingPanel.AddComponent<CanvasGroup>();
        
        DontDestroyOnLoad(canvas);
    }
    
    // Static methods for easy access
    public static void LoadScene(string sceneName)
    {
        Instance.LoadSceneInternal(sceneName);
    }
    
    public static void LoadSceneAsync(string sceneName)
    {
        Instance.StartCoroutine(Instance.LoadSceneAsyncInternal(sceneName));
    }
    
    public static void LoadSceneWithTransition(string sceneName, float customFadeTime = -1f)
    {
        float fadeTime = customFadeTime > 0 ? customFadeTime : Instance.fadeInDuration;
        Instance.StartCoroutine(Instance.LoadSceneWithTransitionInternal(sceneName, fadeTime));
    }
    
    public static void ReloadCurrentScene()
    {
        string currentScene = SceneManager.GetActiveScene().name;
        LoadSceneAsync(currentScene);
    }
    
    public static bool IsLoading()
    {
        return Instance.isLoading;
    }
    
    // Internal loading methods
    void LoadSceneInternal(string sceneName)
    {
        if (isLoading)
        {
            Debug.LogWarning("Scene loading already in progress!");
            return;
        }
        
        Debug.Log($"Loading scene: {sceneName}");
        SceneManager.LoadScene(sceneName);
    }
    
    IEnumerator LoadSceneAsyncInternal(string sceneName)
    {
        if (isLoading)
        {
            Debug.LogWarning("Scene loading already in progress!");
            yield break;
        }
        
        isLoading = true;
        
        // Show loading UI
        ShowLoadingUI();
        
        // Start loading
        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
        asyncLoad.allowSceneActivation = false;
        
        float startTime = Time.unscaledTime;
        
        // Update progress
        while (!asyncLoad.isDone)
        {
            float progress = Mathf.Clamp01(asyncLoad.progress / 0.9f);
            UpdateLoadingProgress(progress);
            
            // Check if loading is complete and minimum time has passed
            if (asyncLoad.progress >= 0.9f && 
                Time.unscaledTime - startTime >= minimumLoadingTime)
            {
                asyncLoad.allowSceneActivation = true;
            }
            
            yield return null;
        }
        
        // Hide loading UI
        yield return StartCoroutine(HideLoadingUI());
        
        isLoading = false;
        
        Debug.Log($"Scene loaded: {sceneName}");
    }
    
    IEnumerator LoadSceneWithTransitionInternal(string sceneName, float fadeTime)
    {
        if (isLoading)
        {
            Debug.LogWarning("Scene loading already in progress!");
            yield break;
        }
        
        isLoading = true;
        
        // Fade out
        yield return StartCoroutine(FadeOut(fadeTime));
        
        // Show loading UI
        ShowLoadingUI();
        
        // Load scene
        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
        asyncLoad.allowSceneActivation = false;
        
        float startTime = Time.unscaledTime;
        
        while (!asyncLoad.isDone)
        {
            float progress = Mathf.Clamp01(asyncLoad.progress / 0.9f);
            UpdateLoadingProgress(progress);
            
            if (asyncLoad.progress >= 0.9f && 
                Time.unscaledTime - startTime >= minimumLoadingTime)
            {
                asyncLoad.allowSceneActivation = true;
            }
            
            yield return null;
        }
        
        // Hide loading UI and fade in
        yield return StartCoroutine(HideLoadingUI());
        yield return StartCoroutine(FadeIn(fadeTime));
        
        isLoading = false;
        
        Debug.Log($"Scene loaded with transition: {sceneName}");
    }
    
    // UI Management
    void ShowLoadingUI()
    {
        if (loadingPanel != null)
        {
            loadingPanel.SetActive(true);
            
            // Reset progress
            if (loadingProgressBar != null)
                loadingProgressBar.value = 0f;
            
            // Show random tip
            if (tipText != null && loadingTips.Length > 0)
            {
                string randomTip = loadingTips[Random.Range(0, loadingTips.Length)];
                tipText.text = randomTip;
            }
            
            // Set loading text
            if (loadingText != null)
                loadingText.text = "Loading...";
        }
    }
    
    IEnumerator HideLoadingUI()
    {
        if (loadingPanel != null)
        {
            // Fade out loading UI
            if (fadeCanvasGroup != null)
            {
                float elapsedTime = 0f;
                float startAlpha = fadeCanvasGroup.alpha;
                
                while (elapsedTime < fadeOutDuration)
                {
                    elapsedTime += Time.unscaledDeltaTime;
                    float alpha = Mathf.Lerp(startAlpha, 0f, elapsedTime / fadeOutDuration);
                    fadeCanvasGroup.alpha = alpha;
                    yield return null;
                }
                
                fadeCanvasGroup.alpha = 0f;
            }
            
            loadingPanel.SetActive(false);
        }
    }
    
    void UpdateLoadingProgress(float progress)
    {
        if (loadingProgressBar != null)
        {
            loadingProgressBar.value = progress;
        }
        
        if (loadingText != null)
        {
            int percentage = Mathf.RoundToInt(progress * 100f);
            loadingText.text = $"Loading... {percentage}%";
        }
    }
    
    // Fade effects
    IEnumerator FadeOut(float duration)
    {
        if (fadeCanvasGroup != null)
        {
            float elapsedTime = 0f;
            float startAlpha = fadeCanvasGroup.alpha;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(startAlpha, 1f, elapsedTime / duration);
                fadeCanvasGroup.alpha = alpha;
                yield return null;
            }
            
            fadeCanvasGroup.alpha = 1f;
        }
    }
    
    IEnumerator FadeIn(float duration)
    {
        if (fadeCanvasGroup != null)
        {
            float elapsedTime = 0f;
            float startAlpha = fadeCanvasGroup.alpha;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(startAlpha, 0f, elapsedTime / duration);
                fadeCanvasGroup.alpha = alpha;
                yield return null;
            }
            
            fadeCanvasGroup.alpha = 0f;
        }
    }
    
    // Scene validation
    public static bool IsSceneValid(string sceneName)
    {
        for (int i = 0; i < SceneManager.sceneCountInBuildSettings; i++)
        {
            string scenePath = SceneUtility.GetScenePathByBuildIndex(i);
            string sceneNameFromPath = System.IO.Path.GetFileNameWithoutExtension(scenePath);
            
            if (sceneNameFromPath == sceneName)
                return true;
        }
        
        return false;
    }
    
    public static void LoadSceneIfValid(string sceneName)
    {
        if (IsSceneValid(sceneName))
        {
            LoadSceneAsync(sceneName);
        }
        else
        {
            Debug.LogError($"Scene '{sceneName}' not found in build settings!");
        }
    }
    
    // Preloading for performance
    public static void PreloadScene(string sceneName)
    {
        Instance.StartCoroutine(Instance.PreloadSceneInternal(sceneName));
    }
    
    IEnumerator PreloadSceneInternal(string sceneName)
    {
        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
        asyncLoad.allowSceneActivation = false;
        
        while (asyncLoad.progress < 0.9f)
        {
            yield return null;
        }
        
        Debug.Log($"Scene preloaded: {sceneName}");
    }
    
    // Emergency scene loading
    public static void LoadMainMenuSafe()
    {
        try
        {
            LoadScene(Scenes.MAIN_MENU);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load main menu: {e.Message}");
            // Fallback to scene index 0
            SceneManager.LoadScene(0);
        }
    }
    
    // Scene management utilities
    public static string GetCurrentSceneName()
    {
        return SceneManager.GetActiveScene().name;
    }
    
    public static bool IsInMainMenu()
    {
        return GetCurrentSceneName() == Scenes.MAIN_MENU;
    }
    
    public static bool IsInGameplay()
    {
        string currentScene = GetCurrentSceneName();
        return currentScene != Scenes.MAIN_MENU && !string.IsNullOrEmpty(currentScene);
    }
    
    void OnDestroy()
    {
        if (currentLoadingCoroutine != null)
        {
            StopCoroutine(currentLoadingCoroutine);
        }
    }
}
