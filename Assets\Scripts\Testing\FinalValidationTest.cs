using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using CinderOfDarkness.Narrative;
using CinderOfDarkness.AI;
using CinderOfDarkness.Magic;
using CinderOfDarkness.Economy;
using CinderOfDarkness.Dialogue;
using CinderOfDarkness.Stealth;
using CinderOfDarkness.Map;
using CinderOfDarkness.Time;
using CinderOfDarkness.Modding;
using CinderOfDarkness.UI;

namespace CinderOfDarkness.Testing
{
    /// <summary>
    /// Final validation test for Cinder of Darkness.
    /// Verifies all systems are functional and integrated properly.
    /// </summary>
    public class FinalValidationTest : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Test Settings")]
        [SerializeField] private bool runOnStart = true;
        [SerializeField] private bool showDetailedLogs = true;
        [SerializeField] private bool testPerformance = true;
        [SerializeField] private float testDuration = 30f;

        [Header("Test Results")]
        [SerializeField] private int totalTests = 0;
        [SerializeField] private int passedTests = 0;
        [SerializeField] private int failedTests = 0;
        [SerializeField] private float overallScore = 0f;
        #endregion

        #region Private Fields
        private List<string> testResults = new List<string>();
        private List<string> failedTestDetails = new List<string>();
        private bool testingComplete = false;
        private float testStartTime;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (runOnStart)
            {
                StartCoroutine(RunFinalValidation());
            }
        }

        private void OnGUI()
        {
            if (showDetailedLogs && !testingComplete)
            {
                GUILayout.BeginArea(new Rect(10, 10, 500, 300));
                GUILayout.Label("🔍 FINAL VALIDATION RUNNING...");
                GUILayout.Label($"Tests Completed: {passedTests + failedTests}/{totalTests}");
                GUILayout.Label($"Passed: {passedTests}");
                GUILayout.Label($"Failed: {failedTests}");
                GUILayout.Label($"Score: {overallScore:F1}%");
                GUILayout.EndArea();
            }
            else if (testingComplete)
            {
                GUILayout.BeginArea(new Rect(10, 10, 600, 400));
                GUILayout.Label("✅ FINAL VALIDATION COMPLETE");
                GUILayout.Label($"Overall Score: {overallScore:F1}%");
                
                if (overallScore >= 95f)
                {
                    GUILayout.Label("🏆 EXCELLENT - PRODUCTION READY!");
                }
                else if (overallScore >= 85f)
                {
                    GUILayout.Label("✅ GOOD - MINOR ISSUES DETECTED");
                }
                else
                {
                    GUILayout.Label("⚠️ NEEDS ATTENTION - CRITICAL ISSUES FOUND");
                }

                GUILayout.Space(10);
                GUILayout.Label("Test Results:");
                
                foreach (string result in testResults)
                {
                    GUILayout.Label(result);
                }

                if (failedTestDetails.Count > 0)
                {
                    GUILayout.Space(10);
                    GUILayout.Label("Failed Tests:");
                    foreach (string failure in failedTestDetails)
                    {
                        GUILayout.Label(failure);
                    }
                }

                GUILayout.EndArea();
            }
        }
        #endregion

        #region Validation Tests
        private IEnumerator RunFinalValidation()
        {
            testStartTime = Time.time;
            testResults.Clear();
            failedTestDetails.Clear();
            totalTests = 25; // Total number of tests
            passedTests = 0;
            failedTests = 0;

            Debug.Log("🔍 Starting Final Validation of Cinder of Darkness...");

            // Test 1: Core Systems Existence
            yield return StartCoroutine(TestCoreSystemsExistence());

            // Test 2: UI Systems Functionality
            yield return StartCoroutine(TestUISystemsFunctionality());

            // Test 3: Save/Load System
            yield return StartCoroutine(TestSaveLoadSystem());

            // Test 4: Input System
            yield return StartCoroutine(TestInputSystem());

            // Test 5: Localization System
            yield return StartCoroutine(TestLocalizationSystem());

            // Test 6: Performance Validation
            if (testPerformance)
            {
                yield return StartCoroutine(TestPerformance());
            }

            // Calculate final score
            overallScore = (float)passedTests / totalTests * 100f;
            testingComplete = true;

            Debug.Log($"✅ Final Validation Complete! Score: {overallScore:F1}%");
            
            if (overallScore >= 95f)
            {
                Debug.Log("🏆 CINDER OF DARKNESS IS PRODUCTION READY!");
            }
        }

        private IEnumerator TestCoreSystemsExistence()
        {
            Debug.Log("Testing Core Systems Existence...");

            // Test Narrative System
            RunTest("Dynamic Narrative System", () => DynamicNarrativeSystem.Instance != null);
            yield return null;

            // Test AI System
            RunTest("Reactive AI System", () => ReactiveAISystem.Instance != null);
            yield return null;

            // Test Magic System
            RunTest("Magic Evolution System", () => MagicEvolutionSystem.Instance != null);
            yield return null;

            // Test Economy System
            RunTest("Economy System", () => EconomySystem.Instance != null);
            yield return null;

            // Test Dialogue System
            RunTest("Advanced Dialogue System", () => AdvancedDialogueSystem.Instance != null);
            yield return null;

            // Test Stealth System
            RunTest("Stealth System", () => StealthSystem.Instance != null);
            yield return null;

            // Test Map System
            RunTest("World Map System", () => WorldMapSystem.Instance != null);
            yield return null;

            // Test Time System
            RunTest("Dynamic Time System", () => DynamicTimeSystem.Instance != null);
            yield return null;

            // Test Flashback System
            RunTest("Flashback System", () => FlashbackSystem.Instance != null);
            yield return null;

            // Test Modding System
            RunTest("Modding System", () => ModdingSystem.Instance != null);
            yield return null;
        }

        private IEnumerator TestUISystemsFunctionality()
        {
            Debug.Log("Testing UI Systems Functionality...");

            // Test Quest Log UI
            var questLogUI = FindObjectOfType<QuestLogUI>();
            RunTest("Quest Log UI", () => questLogUI != null);
            yield return null;

            // Test Dialogue UI
            var dialogueUI = FindObjectOfType<DialogueUI>();
            RunTest("Dialogue UI", () => dialogueUI != null);
            yield return null;

            // Test Map UI
            var mapUI = FindObjectOfType<MapUI>();
            RunTest("Map UI", () => mapUI != null);
            yield return null;

            // Test Inventory UI
            var inventoryUI = FindObjectOfType<InventoryUI>();
            RunTest("Inventory UI", () => inventoryUI != null);
            yield return null;

            // Test Magic Tree UI
            var magicTreeUI = FindObjectOfType<MagicTreeUI>();
            RunTest("Magic Tree UI", () => magicTreeUI != null);
            yield return null;

            // Test Shop UI
            var shopUI = FindObjectOfType<ShopUI>();
            RunTest("Shop UI", () => shopUI != null);
            yield return null;

            // Test Stealth Indicator UI
            var stealthUI = FindObjectOfType<StealthIndicatorUI>();
            RunTest("Stealth Indicator UI", () => stealthUI != null);
            yield return null;

            // Test Mod Manager UI
            var modManagerUI = FindObjectOfType<ModManagerUI>();
            RunTest("Mod Manager UI", () => modManagerUI != null);
            yield return null;
        }

        private IEnumerator TestSaveLoadSystem()
        {
            Debug.Log("Testing Save/Load System...");

            var saveSystem = SaveSystem.Instance;
            RunTest("Save System Instance", () => saveSystem != null);
            yield return null;

            if (saveSystem != null)
            {
                // Test save file creation
                bool saveSuccessful = false;
                try
                {
                    saveSystem.SaveGame(0);
                    saveSuccessful = true;
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Save test failed: {e.Message}");
                }

                RunTest("Save Game Functionality", () => saveSuccessful);
                yield return null;

                // Test save file existence
                RunTest("Save File Creation", () => saveSystem.HasSaveFile(0));
                yield return null;
            }
        }

        private IEnumerator TestInputSystem()
        {
            Debug.Log("Testing Input System...");

            var inputSystem = MultiInputControlSystem.Instance;
            RunTest("Multi-Input Control System", () => inputSystem != null);
            yield return null;

            if (inputSystem != null)
            {
                RunTest("Input Device Detection", () => inputSystem.CurrentDevice != null);
                yield return null;

                RunTest("Controller Type Detection", () => inputSystem.CurrentControllerType != ControllerType.Unknown);
                yield return null;
            }
        }

        private IEnumerator TestLocalizationSystem()
        {
            Debug.Log("Testing Localization System...");

            var localizationManager = LocalizationManager.Instance;
            RunTest("Localization Manager", () => localizationManager != null);
            yield return null;

            if (localizationManager != null)
            {
                RunTest("Language Support", () => localizationManager.GetAvailableLanguages().Length > 0);
                yield return null;

                RunTest("Text Localization", () => !string.IsNullOrEmpty(localizationManager.GetLocalizedText("ui_continue")));
                yield return null;
            }
        }

        private IEnumerator TestPerformance()
        {
            Debug.Log("Testing Performance...");

            float testStart = Time.time;
            float frameCount = 0;
            float totalFrameTime = 0;

            while (Time.time - testStart < 5f) // Test for 5 seconds
            {
                frameCount++;
                totalFrameTime += Time.deltaTime;
                yield return null;
            }

            float averageFPS = frameCount / totalFrameTime;
            RunTest("Performance (60+ FPS)", () => averageFPS >= 60f);

            // Test memory usage
            long memoryUsage = System.GC.GetTotalMemory(false);
            RunTest("Memory Usage (<500MB)", () => memoryUsage < 500 * 1024 * 1024);
        }

        private void RunTest(string testName, System.Func<bool> testCondition)
        {
            try
            {
                bool result = testCondition();
                
                if (result)
                {
                    passedTests++;
                    testResults.Add($"✅ {testName}");
                    if (showDetailedLogs)
                        Debug.Log($"✅ {testName} - PASSED");
                }
                else
                {
                    failedTests++;
                    testResults.Add($"❌ {testName}");
                    failedTestDetails.Add($"❌ {testName} - Test condition failed");
                    if (showDetailedLogs)
                        Debug.LogError($"❌ {testName} - FAILED");
                }
            }
            catch (System.Exception e)
            {
                failedTests++;
                testResults.Add($"❌ {testName}");
                failedTestDetails.Add($"❌ {testName} - Exception: {e.Message}");
                if (showDetailedLogs)
                    Debug.LogError($"❌ {testName} - EXCEPTION: {e.Message}");
            }
        }
        #endregion

        #region Public Methods
        [ContextMenu("Run Validation")]
        public void RunValidationManual()
        {
            if (!testingComplete)
            {
                StartCoroutine(RunFinalValidation());
            }
        }

        public bool IsTestingComplete()
        {
            return testingComplete;
        }

        public float GetOverallScore()
        {
            return overallScore;
        }

        public List<string> GetTestResults()
        {
            return new List<string>(testResults);
        }

        public List<string> GetFailedTests()
        {
            return new List<string>(failedTestDetails);
        }
        #endregion
    }
}
