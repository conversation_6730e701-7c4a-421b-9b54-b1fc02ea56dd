using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// Production-ready Steam integration for Cinder of Darkness
/// Implements Steamworks.NET for achievements, cloud saves, and overlay support
/// </summary>
public class SteamworksIntegration : MonoBehaviour
{
    [Header("Steam Configuration")]
    public uint steamAppId = 480; // Replace with actual App ID
    public bool enableSteamIntegration = true;
    public bool enableDebugLogging = false;

    [Header("Achievement Settings")]
    public bool enableAchievements = true;
    public SteamAchievement[] steamAchievements;

    [Header("Cloud Save Settings")]
    public bool enableCloudSave = true;
    public string cloudSaveFileName = "cinder_save.dat";
    public int maxCloudFileSize = 1024 * 1024; // 1MB
    public bool autoSyncCloudSaves = true;
    public float cloudSyncInterval = 300f; // 5 minutes
    public bool enableCloudConflictResolution = true;

    [Header("Leaderboard Settings")]
    public bool enableLeaderboards = false;
    public SteamLeaderboard[] steamLeaderboards;

    // Static instance
    private static SteamworksIntegration instance;
    public static SteamworksIntegration Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<SteamworksIntegration>();
                if (instance == null)
                {
                    GameObject go = new GameObject("SteamworksIntegration");
                    instance = go.AddComponent<SteamworksIntegration>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Steam achievement data
    [System.Serializable]
    public class SteamAchievement
    {
        public string achievementId;
        public string displayName;
        public string description;
        public bool isUnlocked;
        public bool isHidden;

        [Header("Game Integration")]
        public string gameEventTrigger;
        public int requiredValue;
    }

    // Steam leaderboard data
    [System.Serializable]
    public class SteamLeaderboard
    {
        public string leaderboardName;
        public string displayName;
        public LeaderboardSortMethod sortMethod;
        public LeaderboardDisplayType displayType;
    }

    public enum LeaderboardSortMethod
    {
        Ascending,
        Descending
    }

    public enum LeaderboardDisplayType
    {
        Numeric,
        TimeSeconds,
        TimeMilliSeconds
    }

    // Steam state
    private bool steamInitialized = false;
    private bool steamAvailable = false;
    private Dictionary<string, SteamAchievement> achievementDatabase = new Dictionary<string, SteamAchievement>();

    // Events
    public System.Action OnSteamInitialized;
    public System.Action<string> OnAchievementUnlocked;
    public System.Action<bool> OnCloudSaveComplete;
    public System.Action<bool> OnCloudLoadComplete;

    // Steamworks.NET integration (placeholder for actual implementation)
    private bool useSteamworksNET = false; // Set to true when Steamworks.NET is available

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSteamworks();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        if (enableSteamIntegration)
        {
            InitializeSteam();
        }

        LoadAchievementDefinitions();
    }

    void InitializeSteamworks()
    {
        // Check if Steamworks.NET is available
        #if STEAMWORKS_NET
        useSteamworksNET = true;
        #endif

        Debug.Log($"Steamworks Integration initialized (Steamworks.NET: {useSteamworksNET})");
    }

    void InitializeSteam()
    {
        if (!enableSteamIntegration)
        {
            LogDebug("Steam integration disabled");
            return;
        }

        #if STEAMWORKS_NET
        try
        {
            // Initialize Steamworks.NET
            if (SteamAPI.RestartAppIfNecessary(new Steamworks.AppId_t(steamAppId)))
            {
                Application.Quit();
                return;
            }

            if (!SteamAPI.Init())
            {
                Debug.LogError("SteamAPI_Init() failed. Steam must be running.");
                return;
            }

            steamInitialized = true;
            steamAvailable = true;

            LogDebug("Steam API initialized successfully");

            // Setup callbacks
            SetupSteamCallbacks();

            OnSteamInitialized?.Invoke();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to initialize Steam: {e.Message}");
        }
        #else
        // Fallback for when Steamworks.NET is not available
        LogDebug("Steamworks.NET not available - using placeholder implementation");
        steamInitialized = true;
        steamAvailable = false; // Set to false to use fallback systems
        OnSteamInitialized?.Invoke();
        #endif
    }

    #if STEAMWORKS_NET
    void SetupSteamCallbacks()
    {
        // Setup achievement callbacks
        if (enableAchievements)
        {
            // Achievement callbacks would be set up here
            LogDebug("Steam achievement callbacks initialized");
        }

        // Setup cloud save callbacks
        if (enableCloudSave)
        {
            // Cloud save callbacks would be set up here
            LogDebug("Steam cloud save callbacks initialized");
        }
    }
    #endif

    void LoadAchievementDefinitions()
    {
        // Load achievement definitions
        foreach (var achievement in steamAchievements)
        {
            achievementDatabase[achievement.achievementId] = achievement;
        }

        // Define Cinder of Darkness achievements
        DefineGameAchievements();

        LogDebug($"Loaded {achievementDatabase.Count} achievement definitions");
    }

    void DefineGameAchievements()
    {
        // Add achievements that weren't defined in the inspector
        AddAchievementDefinition("FIRST_STEPS", "First Steps", "Begin your journey as the Cinderborn", "GameStarted");
        AddAchievementDefinition("ELEMENT_MASTER", "Element Master", "Discover all four elemental magics", "AllElementsDiscovered");
        AddAchievementDefinition("MORAL_CHOICE", "The Weight of Choice", "Make your first major moral decision", "MoralChoiceMade");
        AddAchievementDefinition("INNOCENT_PROTECTOR", "Protector of Innocents", "Complete the game without killing any innocents", "GameCompleted", 0);
        AddAchievementDefinition("DARK_PATH", "Embrace the Darkness", "Complete the game on the evil path", "GameCompletedEvil");
        AddAchievementDefinition("LIGHT_PATH", "Champion of Light", "Complete the game on the good path", "GameCompletedGood");
        AddAchievementDefinition("CULTURAL_EXPLORER", "Cultural Explorer", "Visit all major kingdoms and settlements", "AllLocationsVisited");
        AddAchievementDefinition("MASTER_WARRIOR", "Master Warrior", "Defeat 100 enemies in combat", "EnemyKilled", 100);
        AddAchievementDefinition("CONTEMPLATIVE_SOUL", "Contemplative Soul", "Spend time in all contemplative locations", "AllContemplativeLocationsVisited");
        AddAchievementDefinition("ANCIENT_WISDOM", "Ancient Wisdom", "Discover all ancient melodies", "AllMelodiesDiscovered");
        AddAchievementDefinition("SPIRIT_LISTENER", "Spirit Listener", "Hear all spirit whispers", "AllWhispersHeard");
        AddAchievementDefinition("MUSCLE_GROWTH", "Physical Transformation", "Achieve maximum muscle growth", "MaxMuscleGrowthReached");
        AddAchievementDefinition("FACIAL_HAIR", "Bearded Warrior", "Grow a full beard", "FullBeardGrown");
        AddAchievementDefinition("QUEST_MASTER", "Quest Master", "Complete all side quests", "AllSideQuestsCompleted");
        AddAchievementDefinition("TREASURE_HUNTER", "Treasure Hunter", "Find all rare items", "AllRareItemsFound");
        AddAchievementDefinition("DEATH_TEACHER", "Death as Teacher", "Learn from 10 deaths", "PlayerDeath", 10);
        AddAchievementDefinition("REGRET_REFLECTION", "Moment of Regret", "Reflect after killing a major character", "RegretReflection");
        AddAchievementDefinition("FORBIDDEN_WORDS", "Forbidden Knowledge", "Trigger all forbidden dialogue options", "AllForbiddenDialogueTriggered");
        AddAchievementDefinition("ALLY_LOYALTY", "Loyal Companion", "Maintain maximum ally loyalty", "MaxAllyLoyaltyReached");
        AddAchievementDefinition("COMPLETIONIST", "Master of Cinder", "Achieve 100% completion", "GameCompleted", 100);
    }

    void AddAchievementDefinition(string id, string name, string description, string trigger, int requiredValue = 1)
    {
        if (!achievementDatabase.ContainsKey(id))
        {
            SteamAchievement achievement = new SteamAchievement
            {
                achievementId = id,
                displayName = name,
                description = description,
                gameEventTrigger = trigger,
                requiredValue = requiredValue,
                isUnlocked = false,
                isHidden = false
            };

            achievementDatabase[id] = achievement;
        }
    }

    void Update()
    {
        #if STEAMWORKS_NET
        if (steamInitialized)
        {
            SteamAPI.RunCallbacks();
        }
        #endif
    }

    // Public API
    public static bool IsSteamAvailable()
    {
        return Instance.steamAvailable;
    }

    public static bool IsSteamInitialized()
    {
        return Instance.steamInitialized;
    }

    public static void UnlockAchievement(string achievementId)
    {
        Instance.UnlockAchievementInternal(achievementId);
    }

    public static void SetStat(string statName, int value)
    {
        Instance.SetStatInternal(statName, value);
    }

    public static int GetStat(string statName)
    {
        return Instance.GetStatInternal(statName);
    }

    public static void SaveToCloud(byte[] data)
    {
        Instance.SaveToCloudInternal(data);
    }

    public static void LoadFromCloud(System.Action<byte[]> callback)
    {
        Instance.LoadFromCloudInternal(callback);
    }

    public static void ShowOverlay(string dialog = "")
    {
        Instance.ShowOverlayInternal(dialog);
    }

    void UnlockAchievementInternal(string achievementId)
    {
        if (!enableAchievements || !steamInitialized)
        {
            LogDebug($"Achievement unlock skipped: {achievementId}");
            return;
        }

        if (!achievementDatabase.ContainsKey(achievementId))
        {
            Debug.LogWarning($"Achievement not found: {achievementId}");
            return;
        }

        var achievement = achievementDatabase[achievementId];
        if (achievement.isUnlocked)
        {
            LogDebug($"Achievement already unlocked: {achievementId}");
            return;
        }

        #if STEAMWORKS_NET
        if (steamAvailable)
        {
            try
            {
                bool success = SteamUserStats.SetAchievement(achievementId);
                if (success)
                {
                    SteamUserStats.StoreStats();
                    achievement.isUnlocked = true;
                    LogDebug($"Steam achievement unlocked: {achievementId}");
                }
                else
                {
                    Debug.LogError($"Failed to unlock Steam achievement: {achievementId}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error unlocking Steam achievement {achievementId}: {e.Message}");
            }
        }
        else
        #endif
        {
            // Fallback for when Steam is not available
            achievement.isUnlocked = true;
            PlayerPrefs.SetInt($"Achievement_{achievementId}", 1);
            LogDebug($"Achievement unlocked (fallback): {achievementId}");
        }

        OnAchievementUnlocked?.Invoke(achievementId);
        ShowAchievementNotification(achievement);
    }

    void ShowAchievementNotification(SteamAchievement achievement)
    {
        // Show achievement notification
        LogDebug($"🏆 Achievement Unlocked: {achievement.displayName}");

        // In a full implementation, you might show a UI notification here
        // For now, we'll use the existing notification system
        if (FindObjectOfType<NotificationSystem>() != null)
        {
            NotificationSystem.ShowNotification($"Achievement Unlocked: {achievement.displayName}", 3f);
        }
    }

    void SetStatInternal(string statName, int value)
    {
        if (!steamInitialized) return;

        #if STEAMWORKS_NET
        if (steamAvailable)
        {
            try
            {
                bool success = SteamUserStats.SetStat(statName, value);
                if (success)
                {
                    SteamUserStats.StoreStats();
                    LogDebug($"Steam stat set: {statName} = {value}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error setting Steam stat {statName}: {e.Message}");
            }
        }
        else
        #endif
        {
            // Fallback
            PlayerPrefs.SetInt($"Stat_{statName}", value);
        }
    }

    int GetStatInternal(string statName)
    {
        if (!steamInitialized)
        {
            return PlayerPrefs.GetInt($"Stat_{statName}", 0);
        }

        #if STEAMWORKS_NET
        if (steamAvailable)
        {
            try
            {
                int value;
                bool success = SteamUserStats.GetStat(statName, out value);
                return success ? value : 0;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error getting Steam stat {statName}: {e.Message}");
                return 0;
            }
        }
        else
        #endif
        {
            return PlayerPrefs.GetInt($"Stat_{statName}", 0);
        }
    }

    void SaveToCloudInternal(byte[] data)
    {
        if (!enableCloudSave || !steamInitialized)
        {
            LogDebug("Cloud save skipped");
            OnCloudSaveComplete?.Invoke(false);
            return;
        }

        if (data.Length > maxCloudFileSize)
        {
            Debug.LogError($"Cloud save data too large: {data.Length} bytes (max: {maxCloudFileSize})");
            OnCloudSaveComplete?.Invoke(false);
            return;
        }

        #if STEAMWORKS_NET
        if (steamAvailable)
        {
            try
            {
                bool success = SteamRemoteStorage.FileWrite(cloudSaveFileName, data, data.Length);
                LogDebug($"Cloud save result: {success}");
                OnCloudSaveComplete?.Invoke(success);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error saving to Steam Cloud: {e.Message}");
                OnCloudSaveComplete?.Invoke(false);
            }
        }
        else
        #endif
        {
            // Fallback to local save
            try
            {
                string localPath = Path.Combine(Application.persistentDataPath, "cloud_" + cloudSaveFileName);
                File.WriteAllBytes(localPath, data);
                LogDebug("Saved to local fallback location");
                OnCloudSaveComplete?.Invoke(true);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error saving to local fallback: {e.Message}");
                OnCloudSaveComplete?.Invoke(false);
            }
        }
    }

    void LoadFromCloudInternal(System.Action<byte[]> callback)
    {
        if (!enableCloudSave || !steamInitialized)
        {
            LogDebug("Cloud load skipped");
            callback?.Invoke(null);
            return;
        }

        #if STEAMWORKS_NET
        if (steamAvailable)
        {
            try
            {
                if (SteamRemoteStorage.FileExists(cloudSaveFileName))
                {
                    int fileSize = SteamRemoteStorage.GetFileSize(cloudSaveFileName);
                    byte[] data = new byte[fileSize];

                    int bytesRead = SteamRemoteStorage.FileRead(cloudSaveFileName, data, fileSize);

                    if (bytesRead == fileSize)
                    {
                        LogDebug($"Loaded {bytesRead} bytes from Steam Cloud");
                        callback?.Invoke(data);
                    }
                    else
                    {
                        Debug.LogError("Failed to read complete file from Steam Cloud");
                        callback?.Invoke(null);
                    }
                }
                else
                {
                    LogDebug("No cloud save file found");
                    callback?.Invoke(null);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error loading from Steam Cloud: {e.Message}");
                callback?.Invoke(null);
            }
        }
        else
        #endif
        {
            // Fallback to local load
            try
            {
                string localPath = Path.Combine(Application.persistentDataPath, "cloud_" + cloudSaveFileName);
                if (File.Exists(localPath))
                {
                    byte[] data = File.ReadAllBytes(localPath);
                    LogDebug($"Loaded {data.Length} bytes from local fallback");
                    callback?.Invoke(data);
                }
                else
                {
                    LogDebug("No local fallback save found");
                    callback?.Invoke(null);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error loading from local fallback: {e.Message}");
                callback?.Invoke(null);
            }
        }
    }

    void ShowOverlayInternal(string dialog)
    {
        if (!steamAvailable)
        {
            LogDebug("Steam overlay not available");
            return;
        }

        #if STEAMWORKS_NET
        try
        {
            if (string.IsNullOrEmpty(dialog))
            {
                SteamFriends.ActivateGameOverlay("achievements");
            }
            else
            {
                SteamFriends.ActivateGameOverlay(dialog);
            }

            LogDebug($"Steam overlay activated: {dialog}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error showing Steam overlay: {e.Message}");
        }
        #endif
    }

    // Game event integration
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Check for achievement triggers
        foreach (var achievement in achievementDatabase.Values)
        {
            if (achievement.gameEventTrigger == eventName && !achievement.isUnlocked)
            {
                if (achievement.requiredValue <= 1)
                {
                    UnlockAchievement(achievement.achievementId);
                }
                else
                {
                    // Handle progressive achievements
                    string statName = $"{achievement.achievementId}_Progress";
                    int currentValue = GetStat(statName) + 1;
                    SetStat(statName, currentValue);

                    if (currentValue >= achievement.requiredValue)
                    {
                        UnlockAchievement(achievement.achievementId);
                    }
                }
            }
        }

        // Handle specific events
        switch (eventName)
        {
            case "EnemyKilled":
                int totalKills = GetStat("TotalKills") + 1;
                SetStat("TotalKills", totalKills);
                break;

            case "GameCompleted":
                string moralPath = parameters?.ContainsKey("moralPath") == true ? parameters["moralPath"].ToString() : "";
                if (moralPath == "Good")
                    OnGameEvent("GameCompletedGood", parameters);
                else if (moralPath == "Evil")
                    OnGameEvent("GameCompletedEvil", parameters);
                break;
        }
    }

    void LogDebug(string message)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[Steam] {message}");
        }
    }

    void OnDestroy()
    {
        #if STEAMWORKS_NET
        if (steamInitialized)
        {
            SteamAPI.Shutdown();
            LogDebug("Steam API shutdown");
        }
        #endif
    }

    void OnApplicationPause(bool pauseStatus)
    {
        #if STEAMWORKS_NET
        if (steamInitialized && !pauseStatus)
        {
            // Refresh Steam status when returning from pause
            LogDebug("Refreshing Steam status after pause");
        }
        #endif
    }
}

// Simple notification system for achievement notifications
public class NotificationSystem : MonoBehaviour
{
    private static NotificationSystem instance;

    public static void ShowNotification(string message, float duration)
    {
        if (instance == null)
        {
            GameObject go = new GameObject("NotificationSystem");
            instance = go.AddComponent<NotificationSystem>();
            DontDestroyOnLoad(go);
        }

        instance.StartCoroutine(instance.ShowNotificationCoroutine(message, duration));
    }

    System.Collections.IEnumerator ShowNotificationCoroutine(string message, float duration)
    {
        Debug.Log($"[Notification] {message}");
        yield return new WaitForSeconds(duration);
    }
}
