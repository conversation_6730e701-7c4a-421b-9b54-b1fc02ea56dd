using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.UI;

namespace CinderOfDarkness
{
    /// <summary>
    /// Player Inventory System for Cinder of Darkness.
    /// Manages items, equipment, and inventory operations.
    /// </summary>
    public class PlayerInventory : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Inventory Settings")]
        [SerializeField] private int maxSlots = 50;
        [SerializeField] private float maxWeight = 100f;
        [SerializeField] private InventoryItem[] startingItems;
        #endregion

        #region Private Fields
        private List<InventoryItem> items = new List<InventoryItem>();
        private Dictionary<EquipmentSlotType, InventoryItem> equippedItems = new Dictionary<EquipmentSlotType, InventoryItem>();
        #endregion

        #region Events
        public System.Action<InventoryItem> OnItemAdded;
        public System.Action<string> OnItemRemoved;
        public System.Action<InventoryItem> OnItemEquipped;
        public System.Action<EquipmentSlotType> OnItemUnequipped;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            InitializeInventory();
        }
        #endregion

        #region Initialization
        private void InitializeInventory()
        {
            // Add starting items
            if (startingItems != null)
            {
                foreach (var item in startingItems)
                {
                    AddItem(item);
                }
            }
        }
        #endregion

        #region Item Management
        public bool AddItem(InventoryItem item)
        {
            if (items.Count >= maxSlots) return false;
            if (GetCurrentWeight() + item.weight > maxWeight) return false;

            // Check if item already exists and is stackable
            var existingItem = items.FirstOrDefault(i => i.itemId == item.itemId && i.isStackable);
            if (existingItem != null)
            {
                existingItem.quantity += item.quantity;
            }
            else
            {
                items.Add(item);
            }

            OnItemAdded?.Invoke(item);
            return true;
        }

        public bool RemoveItem(string itemId, int quantity = 1)
        {
            var item = items.FirstOrDefault(i => i.itemId == itemId);
            if (item == null) return false;

            item.quantity -= quantity;
            if (item.quantity <= 0)
            {
                items.Remove(item);
            }

            OnItemRemoved?.Invoke(itemId);
            return true;
        }

        public bool UseItem(string itemId)
        {
            var item = items.FirstOrDefault(i => i.itemId == itemId);
            if (item == null || item.category != ItemCategory.Consumable) return false;

            // Apply item effects
            ApplyItemEffects(item);

            // Remove item after use
            RemoveItem(itemId, 1);
            return true;
        }

        public bool EquipItem(string itemId)
        {
            var item = items.FirstOrDefault(i => i.itemId == itemId);
            if (item == null) return false;

            var slotType = GetSlotTypeForItem(item);
            if (slotType == EquipmentSlotType.MainHand) // Invalid slot
                return false;

            // Unequip current item in slot
            if (equippedItems.ContainsKey(slotType))
            {
                UnequipItem(slotType);
            }

            // Equip new item
            equippedItems[slotType] = item;
            items.Remove(item);

            OnItemEquipped?.Invoke(item);
            return true;
        }

        public bool UnequipItem(EquipmentSlotType slotType)
        {
            if (!equippedItems.ContainsKey(slotType)) return false;

            var item = equippedItems[slotType];
            equippedItems.Remove(slotType);

            // Add back to inventory
            AddItem(item);

            OnItemUnequipped?.Invoke(slotType);
            return true;
        }

        private void ApplyItemEffects(InventoryItem item)
        {
            var playerStats = GetComponent<PlayerStats>();
            if (playerStats == null) return;

            // Apply healing
            if (item.healAmount > 0)
            {
                playerStats.Heal(item.healAmount);
            }

            // Apply mana restoration
            if (item.manaAmount > 0)
            {
                playerStats.RestoreMana(item.manaAmount);
            }

            // Apply temporary buffs
            if (item.buffDuration > 0)
            {
                ApplyTemporaryBuff(item);
            }
        }

        private void ApplyTemporaryBuff(InventoryItem item)
        {
            // This would integrate with a buff system
            Debug.Log($"Applied buff from {item.itemName} for {item.buffDuration} seconds");
        }

        private EquipmentSlotType GetSlotTypeForItem(InventoryItem item)
        {
            switch (item.equipmentType)
            {
                case "Weapon": return EquipmentSlotType.MainHand;
                case "Shield": return EquipmentSlotType.OffHand;
                case "Helmet": return EquipmentSlotType.Head;
                case "Armor": return EquipmentSlotType.Chest;
                case "Pants": return EquipmentSlotType.Legs;
                case "Boots": return EquipmentSlotType.Feet;
                case "Ring": return EquipmentSlotType.Ring;
                case "Amulet": return EquipmentSlotType.Amulet;
                default: return EquipmentSlotType.MainHand; // Invalid
            }
        }
        #endregion

        #region Getters
        public List<InventoryItem> GetAllItems()
        {
            return new List<InventoryItem>(items);
        }

        public InventoryItem GetEquippedItem(EquipmentSlotType slotType)
        {
            return equippedItems.ContainsKey(slotType) ? equippedItems[slotType] : null;
        }

        public float GetCurrentWeight()
        {
            float weight = items.Sum(i => i.weight * i.quantity);
            weight += equippedItems.Values.Sum(i => i.weight);
            return weight;
        }

        public float GetMaxWeight()
        {
            return maxWeight;
        }

        public int GetItemCount(string itemId)
        {
            var item = items.FirstOrDefault(i => i.itemId == itemId);
            return item?.quantity ?? 0;
        }

        public bool HasItem(string itemId)
        {
            return items.Any(i => i.itemId == itemId);
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class InventoryItem
    {
        [Header("Basic Info")]
        public string itemId;
        public string itemName;
        public string description;
        public Sprite icon;
        public ItemCategory category;
        public string equipmentType;

        [Header("Properties")]
        public int quantity = 1;
        public float weight = 1f;
        public int value = 1;
        public bool isStackable = true;

        [Header("Combat Stats")]
        public int damage = 0;
        public int defense = 0;
        public int magicPower = 0;
        public int magicResistance = 0;

        [Header("Consumable Effects")]
        public int healAmount = 0;
        public int manaAmount = 0;
        public float buffDuration = 0f;
        public string[] buffEffects;

        [Header("Special Properties")]
        public bool isQuestItem = false;
        public bool isUnique = false;
        public string[] enchantments;
    }
    #endregion
}
