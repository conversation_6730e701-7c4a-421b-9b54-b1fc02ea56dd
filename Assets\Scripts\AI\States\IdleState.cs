using UnityEngine;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Idle state for AI enemies.
    /// AI stands still and looks around for threats.
    /// </summary>
    public class IdleState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Idle;
        #endregion

        #region Private Fields
        private float idleStartTime;
        private float nextLookTime;
        private float lookInterval = 2f;
        private float maxIdleTime = 10f;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize idle state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public IdleState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter idle state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            idleStartTime = Time.time;
            nextLookTime = Time.time + Random.Range(1f, 3f);
            
            // Stop movement
            Blackboard.StopMovement();
            Blackboard.SetAgentSpeed(0f);
            
            // Set idle animation
            Blackboard.SetAnimationFloat(AIBlackboard.SpeedHash, 0f);
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, false);
            
            // Reset combat state
            Blackboard.IsInCombat = false;
        }

        /// <summary>
        /// Update idle state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check common transitions first
            AIStateType? commonTransition = CheckCommonTransitions();
            if (commonTransition.HasValue)
            {
                return commonTransition;
            }

            // Check combat transitions
            AIStateType? combatTransition = CheckCombatTransitions();
            if (combatTransition.HasValue)
            {
                return combatTransition;
            }

            // Check investigation transitions
            AIStateType? investigationTransition = CheckInvestigationTransitions();
            if (investigationTransition.HasValue)
            {
                return investigationTransition;
            }

            // Perform idle behaviors
            PerformIdleBehaviors();

            // Transition to patrol after being idle for too long
            if (Time.time - idleStartTime > maxIdleTime)
            {
                return AIStateType.Patrol;
            }

            return null; // Stay in idle state
        }

        /// <summary>
        /// Exit idle state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Perform idle behaviors like looking around.
        /// </summary>
        private void PerformIdleBehaviors()
        {
            // Look around occasionally
            if (Time.time >= nextLookTime)
            {
                LookAround();
                nextLookTime = Time.time + Random.Range(lookInterval, lookInterval * 2f);
            }

            // Update player sight information
            Blackboard.UpdatePlayerSight();
        }

        /// <summary>
        /// Look around randomly.
        /// </summary>
        private void LookAround()
        {
            if (Blackboard.Transform == null) return;

            // Generate random look direction
            float randomAngle = Random.Range(-90f, 90f);
            Vector3 currentForward = Blackboard.Transform.forward;
            Vector3 lookDirection = Quaternion.Euler(0, randomAngle, 0) * currentForward;
            Vector3 lookTarget = Blackboard.Transform.position + lookDirection * 5f;

            // Look at the target smoothly
            LookAtTarget(lookTarget, 1f);
        }
        #endregion
    }

    /// <summary>
    /// Patrol state for AI enemies.
    /// AI moves between patrol points or random positions.
    /// </summary>
    public class PatrolState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Patrol;
        #endregion

        #region Private Fields
        private Vector3 currentTarget;
        private bool isWaiting;
        private float waitStartTime;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize patrol state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public PatrolState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter patrol state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            // Set patrol speed
            Blackboard.SetAgentSpeed(Blackboard.PatrolSpeed);
            
            // Set patrol animation
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, false);
            
            // Reset waiting state
            isWaiting = false;
            
            // Set initial patrol target
            SetNextPatrolTarget();
        }

        /// <summary>
        /// Update patrol state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check common transitions first
            AIStateType? commonTransition = CheckCommonTransitions();
            if (commonTransition.HasValue)
            {
                return commonTransition;
            }

            // Check combat transitions
            AIStateType? combatTransition = CheckCombatTransitions();
            if (combatTransition.HasValue)
            {
                return combatTransition;
            }

            // Check investigation transitions
            AIStateType? investigationTransition = CheckInvestigationTransitions();
            if (investigationTransition.HasValue)
            {
                return investigationTransition;
            }

            // Handle patrol behavior
            HandlePatrolMovement();

            // Update movement animation
            UpdateMovementAnimation();

            // Update player sight information
            Blackboard.UpdatePlayerSight();

            return null; // Continue patrolling
        }

        /// <summary>
        /// Exit patrol state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
            isWaiting = false;
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Handle patrol movement logic.
        /// </summary>
        private void HandlePatrolMovement()
        {
            if (isWaiting)
            {
                // Wait at patrol point
                if (Time.time - waitStartTime >= Blackboard.PatrolWaitTime)
                {
                    isWaiting = false;
                    SetNextPatrolTarget();
                }
                return;
            }

            // Check if reached current target
            if (Blackboard.HasReachedDestination())
            {
                // Start waiting at this patrol point
                isWaiting = true;
                waitStartTime = Time.time;
                Blackboard.StopMovement();
            }
        }

        /// <summary>
        /// Set the next patrol target.
        /// </summary>
        private void SetNextPatrolTarget()
        {
            if (Blackboard.PatrolPoints != null && Blackboard.PatrolPoints.Length > 0)
            {
                // Use predefined patrol points
                currentTarget = Blackboard.PatrolPoints[Blackboard.CurrentPatrolIndex];
                Blackboard.CurrentPatrolIndex = (Blackboard.CurrentPatrolIndex + 1) % Blackboard.PatrolPoints.Length;
            }
            else
            {
                // Use random patrol position
                currentTarget = Blackboard.GetRandomPatrolPosition();
            }

            // Set destination
            Blackboard.SetDestination(currentTarget);
            Blackboard.CurrentPatrolTarget = currentTarget;
        }
        #endregion
    }

    /// <summary>
    /// Chase state for AI enemies.
    /// AI pursues the detected target.
    /// </summary>
    public class ChaseState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Chase;
        #endregion

        #region Private Fields
        private float chaseStartTime;
        private float maxChaseTime = 15f;
        private float lastTargetUpdateTime;
        private float targetUpdateInterval = 0.5f;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize chase state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public ChaseState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter chase state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            chaseStartTime = Time.time;
            lastTargetUpdateTime = 0f;
            
            // Set chase speed
            Blackboard.SetAgentSpeed(Blackboard.ChaseSpeed);
            
            // Set alert animation
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, true);
            
            // Enter combat mode
            Blackboard.IsInCombat = true;
            Blackboard.CombatEngagementTime = Time.time;
        }

        /// <summary>
        /// Update chase state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check common transitions first
            AIStateType? commonTransition = CheckCommonTransitions();
            if (commonTransition.HasValue)
            {
                return commonTransition;
            }

            // Check combat transitions
            AIStateType? combatTransition = CheckCombatTransitions();
            if (combatTransition.HasValue)
            {
                return combatTransition;
            }

            // Handle chase behavior
            HandleChaseMovement();

            // Check if should stop chasing
            if (ShouldStopChasing())
            {
                return AIStateType.Investigate; // Investigate last known position
            }

            // Update movement animation
            UpdateMovementAnimation();

            // Update player sight information
            Blackboard.UpdatePlayerSight();

            return null; // Continue chasing
        }

        /// <summary>
        /// Exit chase state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Handle chase movement logic.
        /// </summary>
        private void HandleChaseMovement()
        {
            // Update target position periodically for performance
            if (Time.time - lastTargetUpdateTime >= targetUpdateInterval)
            {
                if (Blackboard.PlayerTransform != null)
                {
                    Blackboard.SetDestination(Blackboard.PlayerTransform.position);
                    LookAtTarget(Blackboard.PlayerTransform.position, 5f);
                }
                lastTargetUpdateTime = Time.time;
            }
        }

        /// <summary>
        /// Check if AI should stop chasing.
        /// </summary>
        /// <returns>True if should stop chasing</returns>
        private bool ShouldStopChasing()
        {
            // Stop if target is no longer detected and hasn't been seen recently
            if (!Blackboard.IsPlayerDetected && !Blackboard.WasPlayerSeenRecently(3f))
            {
                return true;
            }

            // Stop if chasing for too long
            if (Time.time - chaseStartTime > maxChaseTime)
            {
                return true;
            }

            // Stop if target is too far away
            if (Blackboard.DistanceToTarget > Blackboard.DetectionRange * 2f)
            {
                return true;
            }

            return false;
        }
        #endregion
    }
}
