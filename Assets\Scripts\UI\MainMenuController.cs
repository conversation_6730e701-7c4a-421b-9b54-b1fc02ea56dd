using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

/// <summary>
/// Main Menu Controller for Cinder of Darkness
/// Handles navigation, settings, and game initialization
/// </summary>
public class MainMenuController : MonoBehaviour
{
    [Header("UI Panels")]
    public GameObject mainMenuPanel;
    public GameObject settingsPanel;
    public GameObject creditsPanel;
    public GameObject loadGamePanel;
    public GameObject confirmationPanel;

    [Header("Main Menu Buttons")]
    public Button newGameButton;
    public Button continueButton;
    public Button settingsButton;
    public Button creditsButton;
    public Button exitButton;

    [Header("Settings UI")]
    public Slider masterVolumeSlider;
    public Slider musicVolumeSlider;
    public Slider sfxVolumeSlider;
    public Dropdown resolutionDropdown;
    public Dropdown qualityDropdown;
    public Toggle fullscreenToggle;
    public Toggle vsyncToggle;
    public Dropdown languageDropdown;
    public Button applySettingsButton;

    [Header("Privacy Settings")]
    public Toggle analyticsOptInToggle;
    public Toggle crashReportingToggle;
    public Toggle basicDataOnlyToggle;
    public Button resetSettingsButton;

    [Header("Load Game UI")]
    public Transform saveSlotContainer;
    public GameObject saveSlotPrefab;
    public Button deleteSaveButton;

    [Header("Background Elements")]
    public RawImage backgroundImage;
    public ParticleSystem ambientParticles;
    public Light atmosphericLight;
    public float backgroundScrollSpeed = 0.02f;
    public float lightFlickerIntensity = 0.1f;

    [Header("Audio")]
    public AudioSource menuAudioSource;
    public AudioClip menuMusic;
    public AudioClip buttonHoverSound;
    public AudioClip buttonClickSound;
    public AudioClip errorSound;

    [Header("Scene References")]
    public string gameSceneName = "GameScene";
    public string firstLevelScene = "StartingVillage";

    // Private fields
    private SaveSystemManager saveSystem;
    private AudioManager audioManager;
    private SettingsManager settingsManager;
    private bool isInitialized = false;
    private Coroutine backgroundAnimationCoroutine;

    void Start()
    {
        InitializeMainMenu();
    }

    void InitializeMainMenu()
    {
        // Get system references
        saveSystem = FindObjectOfType<SaveSystemManager>();
        audioManager = FindObjectOfType<AudioManager>();
        settingsManager = FindObjectOfType<SettingsManager>();

        // Create systems if they don't exist
        if (saveSystem == null)
        {
            GameObject saveSystemGO = new GameObject("SaveSystemManager");
            saveSystem = saveSystemGO.AddComponent<SaveSystemManager>();
            DontDestroyOnLoad(saveSystemGO);
        }

        if (audioManager == null)
        {
            GameObject audioManagerGO = new GameObject("AudioManager");
            audioManager = audioManagerGO.AddComponent<AudioManager>();
            DontDestroyOnLoad(audioManagerGO);
        }

        if (settingsManager == null)
        {
            GameObject settingsManagerGO = new GameObject("SettingsManager");
            settingsManager = settingsManagerGO.AddComponent<SettingsManager>();
            DontDestroyOnLoad(settingsManagerGO);
        }

        // Initialize UI
        SetupMainMenuUI();
        SetupSettingsUI();
        SetupBackgroundEffects();

        // Load and apply settings
        if (settingsManager != null)
        {
            settingsManager.LoadSettings();
            ApplySettingsToUI();
        }

        // Start background music
        if (audioManager != null && menuMusic != null)
        {
            audioManager.PlayMusic(menuMusic, true);
        }

        // Check for existing saves
        UpdateContinueButton();

        isInitialized = true;
        Debug.Log("Main Menu initialized successfully");
    }

    void SetupMainMenuUI()
    {
        // Show main menu, hide others
        ShowPanel(mainMenuPanel);

        // Setup button listeners
        if (newGameButton != null)
        {
            newGameButton.onClick.AddListener(OnNewGameClicked);
            AddButtonSounds(newGameButton);
        }

        if (continueButton != null)
        {
            continueButton.onClick.AddListener(OnContinueClicked);
            AddButtonSounds(continueButton);
        }

        if (settingsButton != null)
        {
            settingsButton.onClick.AddListener(OnSettingsClicked);
            AddButtonSounds(settingsButton);
        }

        if (creditsButton != null)
        {
            creditsButton.onClick.AddListener(OnCreditsClicked);
            AddButtonSounds(creditsButton);
        }

        if (exitButton != null)
        {
            exitButton.onClick.AddListener(OnExitClicked);
            AddButtonSounds(exitButton);
        }
    }

    void SetupSettingsUI()
    {
        // Volume sliders
        if (masterVolumeSlider != null)
            masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);

        if (musicVolumeSlider != null)
            musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);

        if (sfxVolumeSlider != null)
            sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);

        // Graphics settings
        if (resolutionDropdown != null)
            PopulateResolutionDropdown();

        if (qualityDropdown != null)
            PopulateQualityDropdown();

        if (languageDropdown != null)
            PopulateLanguageDropdown();

        // Settings buttons
        if (applySettingsButton != null)
        {
            applySettingsButton.onClick.AddListener(OnApplySettings);
            AddButtonSounds(applySettingsButton);
        }

        if (resetSettingsButton != null)
        {
            resetSettingsButton.onClick.AddListener(OnResetSettings);
            AddButtonSounds(resetSettingsButton);
        }

        // Privacy settings
        if (analyticsOptInToggle != null)
            analyticsOptInToggle.onValueChanged.AddListener(OnAnalyticsOptInChanged);

        if (crashReportingToggle != null)
            crashReportingToggle.onValueChanged.AddListener(OnCrashReportingChanged);

        if (basicDataOnlyToggle != null)
            basicDataOnlyToggle.onValueChanged.AddListener(OnBasicDataOnlyChanged);
    }

    void SetupBackgroundEffects()
    {
        // Start background animation
        if (backgroundImage != null)
        {
            backgroundAnimationCoroutine = StartCoroutine(AnimateBackground());
        }

        // Setup atmospheric lighting
        if (atmosphericLight != null)
        {
            StartCoroutine(AnimateAtmosphericLight());
        }

        // Configure ambient particles
        if (ambientParticles != null)
        {
            var main = ambientParticles.main;
            main.startColor = new Color(0.8f, 0.6f, 0.4f, 0.3f); // Warm ember glow
            main.startLifetime = 8f;
            main.startSpeed = 0.5f;

            var emission = ambientParticles.emission;
            emission.rateOverTime = 5f;

            ambientParticles.Play();
        }
    }

    void AddButtonSounds(Button button)
    {
        if (button == null) return;

        // Add hover sound
        var eventTrigger = button.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
        if (eventTrigger == null)
            eventTrigger = button.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();

        var hoverEntry = new UnityEngine.EventSystems.EventTrigger.Entry();
        hoverEntry.eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter;
        hoverEntry.callback.AddListener((data) => PlayButtonHover());
        eventTrigger.triggers.Add(hoverEntry);

        // Click sound is handled by individual button methods
    }

    void UpdateContinueButton()
    {
        if (continueButton != null && saveSystem != null)
        {
            bool hasSave = saveSystem.HasAnySaveFile();
            continueButton.interactable = hasSave;

            // Update button text based on save availability
            var buttonText = continueButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = hasSave ? "Continue" : "No Save Found";
                buttonText.color = hasSave ? Color.white : Color.gray;
            }
        }
    }

    void ShowPanel(GameObject panelToShow)
    {
        // Hide all panels
        if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
        if (settingsPanel != null) settingsPanel.SetActive(false);
        if (creditsPanel != null) creditsPanel.SetActive(false);
        if (loadGamePanel != null) loadGamePanel.SetActive(false);
        if (confirmationPanel != null) confirmationPanel.SetActive(false);

        // Show requested panel
        if (panelToShow != null) panelToShow.SetActive(true);
    }

    // Button Event Handlers
    public void OnNewGameClicked()
    {
        PlayButtonClick();
        Debug.Log("Starting new game...");

        // Show confirmation if save exists
        if (saveSystem != null && saveSystem.HasAnySaveFile())
        {
            ShowNewGameConfirmation();
        }
        else
        {
            StartNewGame();
        }
    }

    public void OnContinueClicked()
    {
        PlayButtonClick();

        if (saveSystem != null && saveSystem.HasAnySaveFile())
        {
            Debug.Log("Loading saved game...");
            LoadGame();
        }
        else
        {
            PlayErrorSound();
            Debug.LogWarning("No save file found!");
        }
    }

    public void OnSettingsClicked()
    {
        PlayButtonClick();
        ShowPanel(settingsPanel);
    }

    public void OnCreditsClicked()
    {
        PlayButtonClick();
        ShowPanel(creditsPanel);
    }

    public void OnExitClicked()
    {
        PlayButtonClick();
        ShowExitConfirmation();
    }

    public void OnBackToMainMenu()
    {
        PlayButtonClick();
        ShowPanel(mainMenuPanel);
    }

    // Settings Event Handlers
    public void OnMasterVolumeChanged(float value)
    {
        if (audioManager != null)
            audioManager.SetMasterVolume(value);
    }

    public void OnMusicVolumeChanged(float value)
    {
        if (audioManager != null)
            audioManager.SetMusicVolume(value);
    }

    public void OnSFXVolumeChanged(float value)
    {
        if (audioManager != null)
            audioManager.SetSFXVolume(value);
    }

    public void OnApplySettings()
    {
        PlayButtonClick();

        if (settingsManager != null)
        {
            ApplyGraphicsSettings();
            settingsManager.SaveSettings();
            Debug.Log("Settings applied and saved");
        }
    }

    public void OnResetSettings()
    {
        PlayButtonClick();

        if (settingsManager != null)
        {
            settingsManager.ResetToDefaults();
            ApplySettingsToUI();
            #if UNITY_EDITOR
            Debug.Log("Settings reset to defaults");
            #endif
        }
    }

    // Privacy Settings Event Handlers
    public void OnAnalyticsOptInChanged(bool optIn)
    {
        AnalyticsManager.SetUserOptIn(optIn);

        // Update basic data only toggle availability
        if (basicDataOnlyToggle != null)
        {
            basicDataOnlyToggle.interactable = optIn;
        }
    }

    public void OnCrashReportingChanged(bool enabled)
    {
        CrashReportingSystem.SetUserConsent(enabled);
    }

    public void OnBasicDataOnlyChanged(bool basicOnly)
    {
        // This would update analytics settings to collect only basic data
        PlayerPrefs.SetInt("Analytics_BasicOnly", basicOnly ? 1 : 0);
        PlayerPrefs.Save();
    }

    // Game Flow Methods
    void StartNewGame()
    {
        if (saveSystem != null)
        {
            saveSystem.DeleteAllSaves();
        }

        // Load first game scene
        SceneLoader.LoadSceneAsync(firstLevelScene);
    }

    void LoadGame()
    {
        if (saveSystem != null)
        {
            // Load the most recent save
            string mostRecentSave = saveSystem.GetMostRecentSaveFile();
            if (!string.IsNullOrEmpty(mostRecentSave))
            {
                saveSystem.LoadGame(mostRecentSave);
                // Scene will be loaded by the save system
            }
        }
    }

    void ShowNewGameConfirmation()
    {
        // Implementation for confirmation dialog
        Debug.Log("Show new game confirmation dialog");
        // For now, just start new game
        StartNewGame();
    }

    void ShowExitConfirmation()
    {
        // Implementation for exit confirmation
        Debug.Log("Show exit confirmation dialog");
        // For now, just exit
        ExitGame();
    }

    void ExitGame()
    {
        Debug.Log("Exiting game...");

        #if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
        #else
        Application.Quit();
        #endif
    }

    // Audio Methods
    void PlayButtonHover()
    {
        if (audioManager != null && buttonHoverSound != null)
            audioManager.PlaySFX(buttonHoverSound);
    }

    void PlayButtonClick()
    {
        if (audioManager != null && buttonClickSound != null)
            audioManager.PlaySFX(buttonClickSound);
    }

    void PlayErrorSound()
    {
        if (audioManager != null && errorSound != null)
            audioManager.PlaySFX(errorSound);
    }

    // Animation Coroutines
    IEnumerator AnimateBackground()
    {
        while (true)
        {
            if (backgroundImage != null)
            {
                Rect uvRect = backgroundImage.uvRect;
                uvRect.x += backgroundScrollSpeed * Time.deltaTime;
                if (uvRect.x >= 1f) uvRect.x = 0f;
                backgroundImage.uvRect = uvRect;
            }
            yield return null;
        }
    }

    IEnumerator AnimateAtmosphericLight()
    {
        float baseIntensity = atmosphericLight.intensity;

        while (true)
        {
            float flicker = Mathf.Sin(Time.time * 2f) * lightFlickerIntensity;
            atmosphericLight.intensity = baseIntensity + flicker;
            yield return null;
        }
    }

    // Settings Helper Methods
    void PopulateResolutionDropdown()
    {
        if (resolutionDropdown == null) return;

        resolutionDropdown.ClearOptions();
        var options = new System.Collections.Generic.List<string>();

        Resolution[] resolutions = Screen.resolutions;
        for (int i = 0; i < resolutions.Length; i++)
        {
            string option = resolutions[i].width + " x " + resolutions[i].height;
            options.Add(option);
        }

        resolutionDropdown.AddOptions(options);
        resolutionDropdown.value = resolutions.Length - 1; // Default to highest resolution
    }

    void PopulateQualityDropdown()
    {
        if (qualityDropdown == null) return;

        qualityDropdown.ClearOptions();
        var options = new System.Collections.Generic.List<string>(QualitySettings.names);
        qualityDropdown.AddOptions(options);
        qualityDropdown.value = QualitySettings.GetQualityLevel();
    }

    void PopulateLanguageDropdown()
    {
        if (languageDropdown == null) return;

        languageDropdown.ClearOptions();
        var options = new System.Collections.Generic.List<string> { "English", "العربية" };
        languageDropdown.AddOptions(options);
        languageDropdown.value = 0; // Default to English
    }

    void ApplySettingsToUI()
    {
        if (settingsManager == null) return;

        var settings = settingsManager.GetCurrentSettings();

        if (masterVolumeSlider != null)
            masterVolumeSlider.value = settings.masterVolume;

        if (musicVolumeSlider != null)
            musicVolumeSlider.value = settings.musicVolume;

        if (sfxVolumeSlider != null)
            sfxVolumeSlider.value = settings.sfxVolume;

        if (fullscreenToggle != null)
            fullscreenToggle.isOn = settings.fullscreen;

        if (vsyncToggle != null)
            vsyncToggle.isOn = settings.vsync;
    }

    void ApplyGraphicsSettings()
    {
        if (settingsManager == null) return;

        var settings = settingsManager.GetCurrentSettings();

        // Apply resolution
        if (resolutionDropdown != null)
        {
            Resolution[] resolutions = Screen.resolutions;
            if (resolutionDropdown.value < resolutions.Length)
            {
                Resolution selectedResolution = resolutions[resolutionDropdown.value];
                Screen.SetResolution(selectedResolution.width, selectedResolution.height, settings.fullscreen);
            }
        }

        // Apply quality
        if (qualityDropdown != null)
        {
            QualitySettings.SetQualityLevel(qualityDropdown.value);
        }

        // Apply fullscreen
        if (fullscreenToggle != null)
        {
            Screen.fullScreen = fullscreenToggle.isOn;
            settings.fullscreen = fullscreenToggle.isOn;
        }

        // Apply VSync
        if (vsyncToggle != null)
        {
            QualitySettings.vSyncCount = vsyncToggle.isOn ? 1 : 0;
            settings.vsync = vsyncToggle.isOn;
        }
    }

    // Mod Menu Integration
    public void OnTrialsButtonClicked()
    {
        if (TrialsOfTheAsh.AreTrialsUnlocked())
        {
            // Open trials menu
            Debug.Log("Opening Trials of the Ash");
            // Implementation would show trials selection UI
        }
        else
        {
            ShowNotification("Complete the main story to unlock Trials of the Ash");
        }
    }

    public void OnModsButtonClicked()
    {
        if (ModdingSystem.IsModdingEnabled())
        {
            // Open mods menu
            Debug.Log("Opening Mods Menu");
            ShowModsMenu();
        }
        else
        {
            ShowNotification("Modding is not enabled");
        }
    }

    public void OnArenaEditorButtonClicked()
    {
        if (ModdingSystem.IsToolUnlocked("arena_creator"))
        {
            ArenaEditorManager.OpenArenaEditor();
        }
        else
        {
            ShowNotification("Arena Creator not unlocked. Complete trials to unlock mod tools.");
        }
    }

    void ShowModsMenu()
    {
        // Create simple mods menu
        GameObject modsMenu = new GameObject("ModsMenu");
        Canvas canvas = modsMenu.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 100;

        // Add background
        GameObject background = new GameObject("Background");
        background.transform.SetParent(modsMenu.transform, false);
        var bgImage = background.AddComponent<UnityEngine.UI.Image>();
        bgImage.color = new Color(0, 0, 0, 0.8f);

        var bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;

        // Add buttons
        CreateModMenuButton(modsMenu.transform, "Sandbox Mode", () => {
            SandboxMode.EnableSandboxMode(true);
            Destroy(modsMenu);
        });

        CreateModMenuButton(modsMenu.transform, "NPC Creator", () => {
            if (ModdingSystem.IsToolUnlocked("npc_creator"))
                NPCCreationTool.OpenNPCCreator();
            else
                ShowNotification("NPC Creator not unlocked");
            Destroy(modsMenu);
        });

        CreateModMenuButton(modsMenu.transform, "Arena Editor", () => {
            OnArenaEditorButtonClicked();
            Destroy(modsMenu);
        });

        CreateModMenuButton(modsMenu.transform, "Close", () => {
            Destroy(modsMenu);
        });
    }

    void CreateModMenuButton(Transform parent, string text, System.Action onClick)
    {
        GameObject button = new GameObject($"Button_{text}");
        button.transform.SetParent(parent, false);

        var buttonComponent = button.AddComponent<UnityEngine.UI.Button>();
        var buttonImage = button.AddComponent<UnityEngine.UI.Image>();
        buttonImage.color = new Color(0.2f, 0.2f, 0.2f, 0.9f);

        var buttonRect = button.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(200, 50);

        // Add text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(button.transform, false);

        var textComponent = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        textComponent.text = text;
        textComponent.fontSize = 18;
        textComponent.color = Color.white;
        textComponent.alignment = TMPro.TextAlignmentOptions.Center;

        var textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        buttonComponent.onClick.AddListener(() => onClick());

        // Position buttons vertically
        int buttonCount = parent.childCount - 1; // Exclude background
        buttonRect.anchoredPosition = new Vector2(0, 100 - (buttonCount * 60));
    }

    void ShowNotification(string message)
    {
        Debug.Log($"Notification: {message}");
        // In a full implementation, this would show a proper notification UI
    }

    void OnDestroy()
    {
        if (backgroundAnimationCoroutine != null)
        {
            StopCoroutine(backgroundAnimationCoroutine);
        }
    }
}
