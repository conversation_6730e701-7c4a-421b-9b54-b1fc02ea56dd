using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class UniqueSideQuests : MonoBehaviour
{
    [Header("Quest System")]
    public List<SideQuest> availableQuests = new List<SideQuest>();
    public List<SideQuest> activeQuests = new List<SideQuest>();
    public List<SideQuest> completedQuests = new List<SideQuest>();
    
    [Header("Special Quest NPCs")]
    public GameObject silentMusicianPrefab;
    public GameObject guiltyPriestPrefab;
    public GameObject desperateMotherPrefab;
    public GameObject lostChildPrefab;
    
    [Header("Quest Locations")]
    public Transform[] questSpawnPoints;
    public Transform caveLocation;
    public Transform forestLocation;
    public Transform villageLocation;
    
    private PsychologicalSystem psycheSystem;
    private PhilosophicalMoralitySystem moralitySystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class SideQuest
    {
        public string questName;
        public string questDescription;
        public QuestType type;
        public QuestDifficulty difficulty;
        public Vector3 questLocation;
        public GameObject questGiver;
        public string[] objectives;
        public QuestReward reward;
        public UnlockCondition unlockCondition;
        public bool isActive;
        public bool isCompleted;
        public float questProgress;
        
        public enum QuestType
        {
            Emotional,
            Moral,
            Psychological,
            Rescue,
            Discovery,
            Redemption
        }
        
        public enum QuestDifficulty
        {
            Easy,
            Medium,
            Hard,
            Legendary
        }
    }
    
    [System.Serializable]
    public class QuestReward
    {
        public int goldReward;
        public int experienceReward;
        public float traumaReduction;
        public float enlightenmentGain;
        public string[] itemRewards;
        public string specialReward;
    }
    
    [System.Serializable]
    public class UnlockCondition
    {
        public float requiredStoryProgress;
        public int requiredLevel;
        public PhilosophicalMoralitySystem.MoralPath requiredPath;
        public string requiredEvent;
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        gameManager = GameManager.Instance;
        
        InitializeUniqueQuests();
    }
    
    void Update()
    {
        CheckQuestUnlocks();
        UpdateActiveQuests();
    }
    
    void InitializeUniqueQuests()
    {
        CreateSilentMusicianQuest();
        CreateSinOfTheSageQuest();
        CreateLostChildQuest();
        CreateMemoryVisionQuest();
        CreateRedemptionQuest();
        
        Debug.Log($"Initialized {availableQuests.Count} unique side quests");
    }
    
    void CreateSilentMusicianQuest()
    {
        SideQuest quest = new SideQuest
        {
            questName = "The Silent Musician",
            questDescription = "A mute bard sits alone in the tavern, expressing his trauma through haunting melodies. Perhaps sitting with him will unlock hidden memories.",
            type = SideQuest.QuestType.Emotional,
            difficulty = SideQuest.QuestDifficulty.Medium,
            questLocation = villageLocation.position,
            objectives = new string[]
            {
                "Find the silent musician in the tavern",
                "Sit with him and listen to his music",
                "Experience the memory visions",
                "Understand his trauma",
                "Offer comfort or wisdom"
            },
            reward = new QuestReward
            {
                goldReward = 0,
                experienceReward = 500,
                traumaReduction = 10f,
                enlightenmentGain = 15f,
                specialReward = "Memory Vision ability unlocked"
            },
            unlockCondition = new UnlockCondition
            {
                requiredStoryProgress = 0.3f,
                requiredLevel = 5
            }
        };
        
        availableQuests.Add(quest);
    }
    
    void CreateSinOfTheSageQuest()
    {
        SideQuest quest = new SideQuest
        {
            questName = "The Sin of the Sage",
            questDescription = "A guilt-ridden priest hides in a cave, tormented by past actions. Help him find peace, and he may grant you cursed blessings based on your intent.",
            type = SideQuest.QuestType.Redemption,
            difficulty = SideQuest.QuestDifficulty.Hard,
            questLocation = caveLocation.position,
            objectives = new string[]
            {
                "Find the hidden priest in the cave",
                "Learn about his past sins",
                "Choose how to help him",
                "Face the moral consequences",
                "Receive his blessing or curse"
            },
            reward = new QuestReward
            {
                goldReward = 100,
                experienceReward = 750,
                traumaReduction = 20f,
                specialReward = "Cursed Blessing based on moral choice"
            },
            unlockCondition = new UnlockCondition
            {
                requiredStoryProgress = 0.5f,
                requiredLevel = 10
            }
        };
        
        availableQuests.Add(quest);
    }
    
    void CreateLostChildQuest()
    {
        SideQuest quest = new SideQuest
        {
            questName = "A Mother's Desperation",
            questDescription = "A desperate mother seeks her lost child. Find the child surrounded by wolves, rescue them, and navigate the emotional reunion with reflective dialogue.",
            type = SideQuest.QuestType.Rescue,
            difficulty = SideQuest.QuestDifficulty.Medium,
            questLocation = forestLocation.position,
            objectives = new string[]
            {
                "Speak with the desperate mother",
                "Search the forest for the lost child",
                "Rescue the child from the wolves",
                "Safely return the child to their mother",
                "Reflect on family and loss"
            },
            reward = new QuestReward
            {
                goldReward = 75,
                experienceReward = 600,
                traumaReduction = 15f,
                enlightenmentGain = 10f,
                specialReward = "Mother's Blessing - protection from fear"
            },
            unlockCondition = new UnlockCondition
            {
                requiredStoryProgress = 0.2f,
                requiredLevel = 3
            }
        };
        
        availableQuests.Add(quest);
    }
    
    void CreateMemoryVisionQuest()
    {
        SideQuest quest = new SideQuest
        {
            questName = "Echoes of the Past",
            questDescription = "Strange visions plague your dreams. Seek out the source of these memories and confront the truth they reveal.",
            type = SideQuest.QuestType.Psychological,
            difficulty = SideQuest.QuestDifficulty.Legendary,
            objectives = new string[]
            {
                "Experience recurring visions",
                "Investigate the source of memories",
                "Confront painful truths",
                "Choose how to handle the revelation",
                "Find peace or embrace the darkness"
            },
            reward = new QuestReward
            {
                goldReward = 200,
                experienceReward = 1000,
                traumaReduction = 30f,
                enlightenmentGain = 25f,
                specialReward = "True Sight - see through illusions"
            },
            unlockCondition = new UnlockCondition
            {
                requiredStoryProgress = 0.7f,
                requiredLevel = 15
            }
        };
        
        availableQuests.Add(quest);
    }
    
    void CreateRedemptionQuest()
    {
        SideQuest quest = new SideQuest
        {
            questName = "The Weight of Ash",
            questDescription = "Confront the consequences of your choices. Face those you've wronged and seek redemption, or embrace the darkness within.",
            type = SideQuest.QuestType.Moral,
            difficulty = SideQuest.QuestDifficulty.Legendary,
            objectives = new string[]
            {
                "Confront your past victims",
                "Face their accusations",
                "Choose redemption or defiance",
                "Accept the consequences",
                "Find your true path"
            },
            reward = new QuestReward
            {
                goldReward = 0,
                experienceReward = 1500,
                traumaReduction = 50f,
                enlightenmentGain = 40f,
                specialReward = "Path of Ash - ultimate moral choice unlocked"
            },
            unlockCondition = new UnlockCondition
            {
                requiredStoryProgress = 0.9f,
                requiredLevel = 20
            }
        };
        
        availableQuests.Add(quest);
    }
    
    void CheckQuestUnlocks()
    {
        foreach (SideQuest quest in availableQuests)
        {
            if (!quest.isActive && !quest.isCompleted && IsQuestUnlocked(quest))
            {
                UnlockQuest(quest);
            }
        }
    }
    
    bool IsQuestUnlocked(SideQuest quest)
    {
        UnlockCondition condition = quest.unlockCondition;
        
        // Check story progress
        if (gameManager != null && gameManager.currentStoryProgress < condition.requiredStoryProgress)
        {
            return false;
        }
        
        // Check level requirement
        PlayerStats playerStats = GetComponent<PlayerStats>();
        if (playerStats != null && playerStats.GetCurrentLevel() < condition.requiredLevel)
        {
            return false;
        }
        
        // Check moral path requirement
        if (condition.requiredPath != PhilosophicalMoralitySystem.MoralPath.Neutral && 
            moralitySystem != null && moralitySystem.GetCurrentPath() != condition.requiredPath)
        {
            return false;
        }
        
        // Check required event
        if (!string.IsNullOrEmpty(condition.requiredEvent) && 
            gameManager != null && !gameManager.IsQuestCompleted(condition.requiredEvent))
        {
            return false;
        }
        
        return true;
    }
    
    void UnlockQuest(SideQuest quest)
    {
        quest.isActive = true;
        activeQuests.Add(quest);
        
        // Spawn quest giver if needed
        SpawnQuestGiver(quest);
        
        ShowQuestMessage($"New Quest Available: {quest.questName}");
        ShowQuestMessage(quest.questDescription);
        
        Debug.Log($"Quest unlocked: {quest.questName}");
    }
    
    void SpawnQuestGiver(SideQuest quest)
    {
        GameObject questGiverPrefab = null;
        
        switch (quest.questName)
        {
            case "The Silent Musician":
                questGiverPrefab = silentMusicianPrefab;
                break;
            case "The Sin of the Sage":
                questGiverPrefab = guiltyPriestPrefab;
                break;
            case "A Mother's Desperation":
                questGiverPrefab = desperateMotherPrefab;
                break;
        }
        
        if (questGiverPrefab != null)
        {
            quest.questGiver = Instantiate(questGiverPrefab, quest.questLocation, Quaternion.identity);
            
            // Add quest giver component
            QuestGiver questGiverComponent = quest.questGiver.AddComponent<QuestGiver>();
            questGiverComponent.Initialize(quest, this);
        }
    }
    
    void UpdateActiveQuests()
    {
        foreach (SideQuest quest in activeQuests)
        {
            UpdateQuestProgress(quest);
        }
    }
    
    void UpdateQuestProgress(SideQuest quest)
    {
        // Quest-specific progress tracking
        switch (quest.questName)
        {
            case "The Silent Musician":
                UpdateSilentMusicianProgress(quest);
                break;
            case "The Sin of the Sage":
                UpdateSinOfTheSageProgress(quest);
                break;
            case "A Mother's Desperation":
                UpdateLostChildProgress(quest);
                break;
        }
    }
    
    void UpdateSilentMusicianProgress(SideQuest quest)
    {
        // Check if player is near the musician
        if (quest.questGiver != null)
        {
            float distance = Vector3.Distance(transform.position, quest.questGiver.transform.position);
            if (distance < 3f)
            {
                // Show interaction prompt
                GameUI gameUI = FindObjectOfType<GameUI>();
                if (gameUI != null)
                {
                    gameUI.ShowInteractionPrompt("Press E to sit with the silent musician");
                }
                
                if (Input.GetKeyDown(KeyCode.E))
                {
                    StartSilentMusicianInteraction(quest);
                }
            }
        }
    }
    
    void StartSilentMusicianInteraction(SideQuest quest)
    {
        StartCoroutine(SilentMusicianSequence(quest));
    }
    
    IEnumerator SilentMusicianSequence(SideQuest quest)
    {
        ShowQuestMessage("You sit beside the silent musician...");
        yield return new WaitForSeconds(2f);
        
        ShowQuestMessage("He begins to play a haunting melody without words...");
        yield return new WaitForSeconds(3f);
        
        ShowQuestMessage("The music stirs something deep within your memory...");
        yield return new WaitForSeconds(2f);
        
        // Trigger memory vision
        TriggerMemoryVision();
        yield return new WaitForSeconds(5f);
        
        ShowQuestMessage("You understand his pain. It mirrors your own...");
        yield return new WaitForSeconds(2f);
        
        // Complete quest
        CompleteQuest(quest);
    }
    
    void TriggerMemoryVision()
    {
        // Create visual effect for memory vision
        ShowQuestMessage("MEMORY VISION: You see flashes of the musician's past...");
        
        // Add trauma reduction for understanding
        if (psycheSystem != null)
        {
            psycheSystem.ReduceTrauma(10f, "Understanding shared trauma through music");
        }
    }
    
    void UpdateSinOfTheSageProgress(SideQuest quest)
    {
        // Similar implementation for priest quest
    }
    
    void UpdateLostChildProgress(SideQuest quest)
    {
        // Check for child rescue scenario
        if (quest.questProgress == 0f)
        {
            // Look for lost child in forest
            GameObject child = GameObject.FindGameObjectWithTag("LostChild");
            if (child != null)
            {
                float distance = Vector3.Distance(transform.position, child.transform.position);
                if (distance < 5f)
                {
                    quest.questProgress = 0.5f;
                    ShowQuestMessage("You found the lost child! They're surrounded by wolves!");
                    
                    // Spawn wolves around child
                    SpawnWolvesAroundChild(child.transform.position);
                }
            }
        }
    }
    
    void SpawnWolvesAroundChild(Vector3 childPosition)
    {
        // Spawn 3-4 wolves in a circle around the child
        for (int i = 0; i < 4; i++)
        {
            float angle = i * 90f * Mathf.Deg2Rad;
            Vector3 wolfPosition = childPosition + new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle)) * 3f;
            
            // Would spawn wolf prefab here
            Debug.Log($"Wolf spawned at {wolfPosition}");
        }
    }
    
    public void CompleteQuest(SideQuest quest)
    {
        quest.isCompleted = true;
        quest.isActive = false;
        quest.questProgress = 1f;
        
        activeQuests.Remove(quest);
        completedQuests.Add(quest);
        
        // Apply rewards
        ApplyQuestRewards(quest.reward);
        
        ShowQuestMessage($"Quest Completed: {quest.questName}");
        
        if (!string.IsNullOrEmpty(quest.reward.specialReward))
        {
            ShowQuestMessage($"Special Reward: {quest.reward.specialReward}");
        }
        
        Debug.Log($"Quest completed: {quest.questName}");
    }
    
    void ApplyQuestRewards(QuestReward reward)
    {
        // Apply gold reward
        EconomySystem economy = GetComponent<EconomySystem>();
        if (economy != null && reward.goldReward > 0)
        {
            economy.EarnMoney(reward.goldReward);
        }
        
        // Apply experience reward
        PlayerStats playerStats = GetComponent<PlayerStats>();
        if (playerStats != null && reward.experienceReward > 0)
        {
            playerStats.GainExperience(reward.experienceReward);
        }
        
        // Apply psychological rewards
        if (psycheSystem != null)
        {
            if (reward.traumaReduction > 0f)
            {
                psycheSystem.ReduceTrauma(reward.traumaReduction, "Quest completion brings peace");
            }
            
            if (reward.enlightenmentGain > 0f)
            {
                psycheSystem.enlightenment += reward.enlightenmentGain;
            }
        }
        
        // Apply item rewards
        if (reward.itemRewards != null && economy != null)
        {
            foreach (string itemName in reward.itemRewards)
            {
                economy.AddItem(itemName, EconomySystem.InventoryItem.ItemType.QuestItem, 
                              EconomySystem.InventoryItem.ItemRarity.Rare, 1, 100f);
            }
        }
    }
    
    void ShowQuestMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Quest: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public List<SideQuest> GetActiveQuests() => activeQuests;
    public List<SideQuest> GetCompletedQuests() => completedQuests;
    public int GetQuestCount() => availableQuests.Count;
    public int GetCompletedQuestCount() => completedQuests.Count;
}

public class QuestGiver : MonoBehaviour
{
    private UniqueSideQuests.SideQuest quest;
    private UniqueSideQuests questSystem;
    
    public void Initialize(UniqueSideQuests.SideQuest sideQuest, UniqueSideQuests system)
    {
        quest = sideQuest;
        questSystem = system;
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"Press E to start: {quest.questName}");
            }
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.HideInteractionPrompt();
            }
        }
    }
}
