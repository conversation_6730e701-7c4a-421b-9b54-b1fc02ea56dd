using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class DynamicTitleSystem : MonoBehaviour
{
    [Header("Title System")]
    public List<EarnedTitle> earnedTitles = new List<EarnedTitle>();
    public EarnedTitle currentPrimaryTitle;
    public TitleData[] availableTitles;
    
    [Header("Title Effects")]
    public float titleInfluenceRadius = 20f;
    public Dictionary<string, float> titleReputationModifiers = new Dictionary<string, float>();
    
    private PhilosophicalMoralitySystem moralitySystem;
    private DeathConsequenceSystem deathSystem;
    private PsychologicalSystem psycheSystem;
    private HostilitySystem hostilitySystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class TitleData
    {
        [Header("Identity")]
        public string titleName;
        public string titleDescription;
        public TitleCategory category;
        public TitleRarity rarity;
        
        [Header("Requirements")]
        public TitleRequirement[] requirements;
        public bool isHidden; // Only revealed after earning
        
        [Header("Effects")]
        public float reputationModifier;
        public float priceModifier;
        public float fearModifier;
        public float respectModifier;
        public string[] specialDialogueUnlocks;
        public string[] restrictedAreas; // Areas that become hostile
        
        [Header("Visual")]
        public Color titleColor;
        public GameObject titleAura;
        public AudioClip titleAnnouncementSound;
        
        public enum TitleCategory
        {
            Heroic,      // Positive, noble titles
            Villainous,  // Dark, feared titles
            Neutral,     // Balanced or professional titles
            Mystical,    // Supernatural or mysterious titles
            Cultural     // Race or culture specific titles
        }
        
        public enum TitleRarity
        {
            Common,
            Uncommon,
            Rare,
            Legendary,
            Mythical
        }
    }
    
    [System.Serializable]
    public class TitleRequirement
    {
        public RequirementType type;
        public float requiredValue;
        public string requiredAction;
        public PhilosophicalMoralitySystem.MoralPath requiredPath;
        
        public enum RequirementType
        {
            InnocentKills,
            VillainKills,
            ChildInteractions,
            ElderInteractions,
            QuestCompletions,
            MoralAlignment,
            TraumaLevel,
            EnlightenmentLevel,
            RaceStanding,
            SpecificAction
        }
    }
    
    [System.Serializable]
    public class EarnedTitle
    {
        public TitleData titleData;
        public float earnedTimestamp;
        public bool isActive;
        public int timesEarned;
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        hostilitySystem = GetComponent<HostilitySystem>();
        gameManager = GameManager.Instance;
        
        InitializeTitleSystem();
    }
    
    void Update()
    {
        CheckTitleRequirements();
        ApplyTitleEffects();
    }
    
    void InitializeTitleSystem()
    {
        // Heroic Titles
        TitleData ashspeaker = new TitleData
        {
            titleName = "Ashspeaker",
            titleDescription = "One who brings comfort to the young and wisdom to the old. The Cinderborn's gentle words soothe troubled souls.",
            category = TitleData.TitleCategory.Heroic,
            rarity = TitleData.TitleRarity.Uncommon,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.ChildInteractions,
                    requiredValue = 10f
                },
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.ElderInteractions,
                    requiredValue = 5f
                }
            },
            reputationModifier = 25f,
            priceModifier = 0.8f,
            respectModifier = 30f,
            titleColor = new Color(0.9f, 0.8f, 0.6f),
            specialDialogueUnlocks = new string[] { "ChildComfort", "ElderWisdom", "FamilyBlessings" }
        };
        
        TitleData redeemedFlame = new TitleData
        {
            titleName = "Redeemed Flame",
            titleDescription = "A soul that has walked through darkness and chosen light. The Cinderborn's redemption inspires hope in the hopeless.",
            category = TitleData.TitleCategory.Heroic,
            rarity = TitleData.TitleRarity.Legendary,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.MoralAlignment,
                    requiredPath = PhilosophicalMoralitySystem.MoralPath.Good,
                    requiredValue = 75f
                },
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.TraumaLevel,
                    requiredValue = 50f // Must have experienced significant trauma
                }
            },
            reputationModifier = 50f,
            priceModifier = 0.6f,
            respectModifier = 60f,
            titleColor = new Color(1f, 0.9f, 0.7f),
            specialDialogueUnlocks = new string[] { "RedemptionStory", "HopeGiver", "SecondChances" }
        };
        
        // Villainous Titles
        TitleData butcherOfHollow = new TitleData
        {
            titleName = "Butcher of the Hollow",
            titleDescription = "A name whispered in terror. The Cinderborn's blade has tasted innocent blood, and the hollow echoes with their screams.",
            category = TitleData.TitleCategory.Villainous,
            rarity = TitleData.TitleRarity.Rare,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.InnocentKills,
                    requiredValue = 15f
                },
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.SpecificAction,
                    requiredAction = "MassacreEvent"
                }
            },
            reputationModifier = -75f,
            priceModifier = 1.5f,
            fearModifier = 80f,
            titleColor = new Color(0.8f, 0.2f, 0.2f),
            restrictedAreas = new string[] { "Churches", "Schools", "Orphanages" },
            specialDialogueUnlocks = new string[] { "FearResponse", "PleaForMercy", "VillainReputation" }
        };
        
        TitleData soulReaper = new TitleData
        {
            titleName = "Soul Reaper",
            titleDescription = "Death follows in the Cinderborn's wake. Even demons speak their name with grudging respect.",
            category = TitleData.TitleCategory.Villainous,
            rarity = TitleData.TitleRarity.Legendary,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.VillainKills,
                    requiredValue = 50f
                },
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.MoralAlignment,
                    requiredPath = PhilosophicalMoralitySystem.MoralPath.Evil,
                    requiredValue = 80f
                }
            },
            reputationModifier = -50f,
            priceModifier = 1.3f,
            fearModifier = 90f,
            respectModifier = 40f, // Feared respect
            titleColor = new Color(0.3f, 0.1f, 0.3f),
            specialDialogueUnlocks = new string[] { "DemonRespect", "DeathThreat", "PowerRecognition" }
        };
        
        // Neutral Titles
        TitleData wanderingAsh = new TitleData
        {
            titleName = "Wandering Ash",
            titleDescription = "Neither hero nor villain, the Cinderborn walks their own path. They are the ash between fire and void.",
            category = TitleData.TitleCategory.Neutral,
            rarity = TitleData.TitleRarity.Common,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.MoralAlignment,
                    requiredPath = PhilosophicalMoralitySystem.MoralPath.Eclipse,
                    requiredValue = 25f
                }
            },
            reputationModifier = 0f,
            priceModifier = 1f,
            titleColor = new Color(0.6f, 0.6f, 0.6f),
            specialDialogueUnlocks = new string[] { "PhilosophicalDiscussion", "NeutralWisdom" }
        };
        
        // Mystical Titles
        TitleData voidwalker = new TitleData
        {
            titleName = "Voidwalker",
            titleDescription = "One who has gazed into the abyss and returned. The Cinderborn carries the weight of forbidden knowledge.",
            category = TitleData.TitleCategory.Mystical,
            rarity = TitleData.TitleRarity.Mythical,
            requirements = new TitleRequirement[]
            {
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.SpecificAction,
                    requiredAction = "EnterVoidRealm"
                },
                new TitleRequirement
                {
                    type = TitleRequirement.RequirementType.TraumaLevel,
                    requiredValue = 80f
                }
            },
            reputationModifier = -25f,
            priceModifier = 1.2f,
            fearModifier = 60f,
            respectModifier = 70f,
            titleColor = new Color(0.2f, 0.1f, 0.4f),
            specialDialogueUnlocks = new string[] { "ForbiddenKnowledge", "VoidWhispers", "CosmicTruth" },
            isHidden = true
        };
        
        availableTitles = new TitleData[] { ashspeaker, redeemedFlame, butcherOfHollow, soulReaper, wanderingAsh, voidwalker };
        
        // Set default title
        EarnedTitle defaultTitle = new EarnedTitle
        {
            titleData = wanderingAsh,
            earnedTimestamp = 0f,
            isActive = true,
            timesEarned = 1
        };
        earnedTitles.Add(defaultTitle);
        currentPrimaryTitle = defaultTitle;
        
        Debug.Log("Dynamic Title System initialized - titles will reflect The Cinderborn's deeds");
    }
    
    void CheckTitleRequirements()
    {
        foreach (TitleData title in availableTitles)
        {
            if (!HasEarnedTitle(title.titleName) && MeetsTitleRequirements(title))
            {
                EarnTitle(title);
            }
        }
    }
    
    bool MeetsTitleRequirements(TitleData title)
    {
        foreach (TitleRequirement requirement in title.requirements)
        {
            if (!MeetsRequirement(requirement))
                return false;
        }
        return true;
    }
    
    bool MeetsRequirement(TitleRequirement requirement)
    {
        switch (requirement.type)
        {
            case TitleRequirement.RequirementType.InnocentKills:
                return deathSystem != null && deathSystem.GetInnocentsKilled() >= requirement.requiredValue;
            
            case TitleRequirement.RequirementType.VillainKills:
                return deathSystem != null && deathSystem.GetVillainsKilled() >= requirement.requiredValue;
            
            case TitleRequirement.RequirementType.ChildInteractions:
                WorldInteractionSystem worldSystem = GetComponent<WorldInteractionSystem>();
                return worldSystem != null && worldSystem.GetChildInteractionCount() >= requirement.requiredValue;
            
            case TitleRequirement.RequirementType.MoralAlignment:
                if (moralitySystem == null) return false;
                bool correctPath = moralitySystem.GetCurrentPath() == requirement.requiredPath;
                float progression = 0f;
                
                switch (requirement.requiredPath)
                {
                    case PhilosophicalMoralitySystem.MoralPath.Good:
                        progression = moralitySystem.goodProgression * 100f;
                        break;
                    case PhilosophicalMoralitySystem.MoralPath.Evil:
                        progression = moralitySystem.evilProgression * 100f;
                        break;
                    case PhilosophicalMoralitySystem.MoralPath.Eclipse:
                        progression = Mathf.Min(moralitySystem.goodProgression, moralitySystem.evilProgression) * 100f;
                        break;
                }
                
                return correctPath && progression >= requirement.requiredValue;
            
            case TitleRequirement.RequirementType.TraumaLevel:
                return psycheSystem != null && psycheSystem.GetTrauma() >= requirement.requiredValue;
            
            case TitleRequirement.RequirementType.SpecificAction:
                return gameManager != null && gameManager.HasPerformedAction(requirement.requiredAction);
            
            default:
                return false;
        }
    }
    
    void EarnTitle(TitleData title)
    {
        EarnedTitle newTitle = new EarnedTitle
        {
            titleData = title,
            earnedTimestamp = Time.time,
            isActive = false,
            timesEarned = 1
        };
        
        earnedTitles.Add(newTitle);
        
        // Automatically set as primary if it's higher rarity or first earned
        if (currentPrimaryTitle == null || title.rarity > currentPrimaryTitle.titleData.rarity)
        {
            SetPrimaryTitle(title.titleName);
        }
        
        // Show dramatic title earning sequence
        StartCoroutine(TitleEarnedSequence(title));
        
        Debug.Log($"Title earned: {title.titleName}");
    }
    
    IEnumerator TitleEarnedSequence(TitleData title)
    {
        // Dramatic pause
        Time.timeScale = 0.3f;
        
        // Play announcement sound
        if (title.titleAnnouncementSound != null)
        {
            AudioSource.PlayClipAtPoint(title.titleAnnouncementSound, transform.position);
        }
        
        // Show title earned message
        ShowTitleMessage($"TITLE EARNED");
        yield return new WaitForSecondsRealtime(1.5f);
        
        ShowTitleMessage($"{title.titleName}");
        yield return new WaitForSecondsRealtime(2f);
        
        ShowTitleMessage(title.titleDescription);
        yield return new WaitForSecondsRealtime(3f);
        
        // Return to normal time
        Time.timeScale = 1f;
        
        // Apply immediate effects
        ApplyTitleEarnedEffects(title);
    }
    
    void ApplyTitleEarnedEffects(TitleData title)
    {
        // Psychological effects based on title category
        if (psycheSystem != null)
        {
            switch (title.category)
            {
                case TitleData.TitleCategory.Heroic:
                    psycheSystem.ReduceTrauma(10f, $"Pride in earning {title.titleName}");
                    break;
                case TitleData.TitleCategory.Villainous:
                    psycheSystem.AddTrauma(5f, $"The weight of {title.titleName}");
                    break;
                case TitleData.TitleCategory.Mystical:
                    psycheSystem.AddTrauma(15f, $"Forbidden knowledge of {title.titleName}");
                    break;
            }
        }
        
        // Update reputation modifiers
        if (hostilitySystem != null)
        {
            hostilitySystem.AddReputationModifier(title.titleName, title.reputationModifier);
        }
    }
    
    void ApplyTitleEffects()
    {
        if (currentPrimaryTitle == null) return;
        
        // Apply continuous effects of current primary title
        TitleData title = currentPrimaryTitle.titleData;
        
        // Check for NPCs in range and apply title effects
        Collider[] nearbyNPCs = Physics.OverlapSphere(transform.position, titleInfluenceRadius);
        
        foreach (Collider npc in nearbyNPCs)
        {
            NPCController npcController = npc.GetComponent<NPCController>();
            if (npcController != null)
            {
                ApplyTitleEffectToNPC(npcController, title);
            }
        }
    }
    
    void ApplyTitleEffectToNPC(NPCController npc, TitleData title)
    {
        // Modify NPC behavior based on title
        switch (title.category)
        {
            case TitleData.TitleCategory.Heroic:
                npc.SetFearLevel(Mathf.Max(0f, npc.GetFearLevel() - title.respectModifier * 0.01f));
                npc.SetRespectLevel(npc.GetRespectLevel() + title.respectModifier * 0.01f);
                break;
            
            case TitleData.TitleCategory.Villainous:
                npc.SetFearLevel(npc.GetFearLevel() + title.fearModifier * 0.01f);
                npc.SetRespectLevel(Mathf.Max(0f, npc.GetRespectLevel() - title.fearModifier * 0.005f));
                break;
            
            case TitleData.TitleCategory.Mystical:
                npc.SetFearLevel(npc.GetFearLevel() + title.fearModifier * 0.005f);
                npc.SetRespectLevel(npc.GetRespectLevel() + title.respectModifier * 0.005f);
                break;
        }
        
        // Check for restricted areas
        if (title.restrictedAreas != null)
        {
            foreach (string restrictedArea in title.restrictedAreas)
            {
                if (npc.GetLocationTag() == restrictedArea)
                {
                    npc.SetHostile(true);
                    npc.SetFearLevel(100f);
                }
            }
        }
    }
    
    public void SetPrimaryTitle(string titleName)
    {
        EarnedTitle title = earnedTitles.Find(t => t.titleData.titleName == titleName);
        if (title != null)
        {
            // Deactivate current primary
            if (currentPrimaryTitle != null)
            {
                currentPrimaryTitle.isActive = false;
            }
            
            // Set new primary
            title.isActive = true;
            currentPrimaryTitle = title;
            
            ShowTitleMessage($"Primary title set: {titleName}");
            
            // Update all systems with new title effects
            UpdateAllSystemsWithTitle();
        }
    }
    
    void UpdateAllSystemsWithTitle()
    {
        if (currentPrimaryTitle == null) return;
        
        TitleData title = currentPrimaryTitle.titleData;
        
        // Update economy system
        EconomySystem economy = GetComponent<EconomySystem>();
        if (economy != null)
        {
            economy.SetTitlePriceModifier(title.priceModifier);
        }
        
        // Update hostility system
        if (hostilitySystem != null)
        {
            hostilitySystem.SetTitleModifiers(title.fearModifier, title.respectModifier);
        }
    }
    
    public string GetCurrentTitleName()
    {
        return currentPrimaryTitle != null ? currentPrimaryTitle.titleData.titleName : "The Cinderborn";
    }
    
    public string GetFullTitleName()
    {
        string baseName = "The Cinderborn";
        if (currentPrimaryTitle != null)
        {
            return $"{baseName}, {currentPrimaryTitle.titleData.titleName}";
        }
        return baseName;
    }
    
    public bool HasEarnedTitle(string titleName)
    {
        return earnedTitles.Exists(t => t.titleData.titleName == titleName);
    }
    
    public List<string> GetAvailableDialogueOptions()
    {
        List<string> options = new List<string>();
        
        if (currentPrimaryTitle != null && currentPrimaryTitle.titleData.specialDialogueUnlocks != null)
        {
            options.AddRange(currentPrimaryTitle.titleData.specialDialogueUnlocks);
        }
        
        return options;
    }
    
    public bool CanAccessArea(string areaTag)
    {
        if (currentPrimaryTitle == null) return true;
        
        TitleData title = currentPrimaryTitle.titleData;
        if (title.restrictedAreas != null)
        {
            foreach (string restrictedArea in title.restrictedAreas)
            {
                if (areaTag == restrictedArea)
                    return false;
            }
        }
        
        return true;
    }
    
    void ShowTitleMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Title System: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSecondsRealtime(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public List<EarnedTitle> GetEarnedTitles() => earnedTitles;
    public EarnedTitle GetCurrentPrimaryTitle() => currentPrimaryTitle;
    public TitleData[] GetAvailableTitles() => availableTitles;
}
