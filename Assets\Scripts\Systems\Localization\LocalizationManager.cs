using UnityEngine;
using System.Collections.Generic;
using System.IO;
using TMPro;
using UnityEngine.UI;

/// <summary>
/// Localization system for Cinder of Darkness
/// Supports Arabic and English with automatic text direction handling
/// </summary>
public class LocalizationManager : MonoBehaviour
{
    public enum Language
    {
        English,
        Arabic
    }

    [Header("Localization Settings")]
    public Language currentLanguage = Language.English;
    public bool autoDetectSystemLanguage = true;
    public string localizationFolder = "Localization";

    [Header("Font Assets")]
    public TMP_FontAsset englishFont;
    public TMP_FontAsset arabicFont;

    // Static instance
    private static LocalizationManager instance;
    public static LocalizationManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<LocalizationManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("LocalizationManager");
                    instance = go.AddComponent<LocalizationManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Localization data
    private Dictionary<string, Dictionary<Language, string>> localizedStrings = new Dictionary<string, Dictionary<Language, string>>();
    private List<LocalizedText> registeredTexts = new List<LocalizedText>();

    // Events
    public System.Action<Language> OnLanguageChanged;

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeLocalization();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        LoadLocalizationData();

        if (autoDetectSystemLanguage)
        {
            DetectSystemLanguage();
        }

        ApplyLanguage(currentLanguage);
    }

    void InitializeLocalization()
    {
        // Create default localization entries
        CreateDefaultEntries();

        Debug.Log("LocalizationManager initialized");
    }

    void CreateDefaultEntries()
    {
        // Main Menu
        AddLocalizationEntry("menu_new_game", "New Game", "لعبة جديدة");
        AddLocalizationEntry("menu_continue", "Continue", "متابعة");
        AddLocalizationEntry("menu_settings", "Settings", "الإعدادات");
        AddLocalizationEntry("menu_credits", "Credits", "الشكر والتقدير");
        AddLocalizationEntry("menu_exit", "Exit", "خروج");

        // Settings
        AddLocalizationEntry("settings_audio", "Audio", "الصوت");
        AddLocalizationEntry("settings_graphics", "Graphics", "الرسوميات");
        AddLocalizationEntry("settings_controls", "Controls", "التحكم");
        AddLocalizationEntry("settings_language", "Language", "اللغة");
        AddLocalizationEntry("settings_master_volume", "Master Volume", "مستوى الصوت الرئيسي");
        AddLocalizationEntry("settings_music_volume", "Music Volume", "مستوى الموسيقى");
        AddLocalizationEntry("settings_sfx_volume", "SFX Volume", "مستوى المؤثرات الصوتية");
        AddLocalizationEntry("settings_resolution", "Resolution", "الدقة");
        AddLocalizationEntry("settings_fullscreen", "Fullscreen", "ملء الشاشة");
        AddLocalizationEntry("settings_vsync", "VSync", "المزامنة العمودية");
        AddLocalizationEntry("settings_apply", "Apply", "تطبيق");
        AddLocalizationEntry("settings_reset", "Reset", "إعادة تعيين");
        AddLocalizationEntry("settings_back", "Back", "رجوع");

        // Gameplay UI
        AddLocalizationEntry("ui_health", "Health", "الصحة");
        AddLocalizationEntry("ui_mana", "Mana", "المانا");
        AddLocalizationEntry("ui_level", "Level", "المستوى");
        AddLocalizationEntry("ui_experience", "Experience", "الخبرة");
        AddLocalizationEntry("ui_gold", "Gold", "الذهب");
        AddLocalizationEntry("ui_inventory", "Inventory", "المخزون");
        AddLocalizationEntry("ui_quests", "Quests", "المهام");
        AddLocalizationEntry("ui_map", "Map", "الخريطة");
        AddLocalizationEntry("ui_pause", "Pause", "إيقاف مؤقت");

        // Combat
        AddLocalizationEntry("combat_attack", "Attack", "هجوم");
        AddLocalizationEntry("combat_heavy_attack", "Heavy Attack", "هجوم قوي");
        AddLocalizationEntry("combat_block", "Block", "حجب");
        AddLocalizationEntry("combat_dodge", "Dodge", "تفادي");
        AddLocalizationEntry("combat_magic", "Magic", "سحر");

        // Magic Elements
        AddLocalizationEntry("magic_fire", "Fire", "النار");
        AddLocalizationEntry("magic_water", "Water", "الماء");
        AddLocalizationEntry("magic_wind", "Wind", "الرياح");
        AddLocalizationEntry("magic_earth", "Earth", "الأرض");

        // Morality System
        AddLocalizationEntry("morality_good", "Good", "خير");
        AddLocalizationEntry("morality_evil", "Evil", "شر");
        AddLocalizationEntry("morality_neutral", "Neutral", "محايد");
        AddLocalizationEntry("morality_path_of_light", "Path of Light", "طريق النور");
        AddLocalizationEntry("morality_path_of_darkness", "Path of Darkness", "طريق الظلام");

        // Loading Tips
        AddLocalizationEntry("tip_choices", "Every choice echoes through the realms...", "كل خيار يتردد صداه عبر العوالم...");
        AddLocalizationEntry("tip_magic", "Ancient magic flows through those who seek it...", "السحر القديم يتدفق عبر أولئك الذين يسعون إليه...");
        AddLocalizationEntry("tip_honor", "Honor and brutality walk hand in hand...", "الشرف والوحشية يسيران جنباً إلى جنب...");
        AddLocalizationEntry("tip_destiny", "The Cinderborn's destiny awaits...", "مصير المولود من الرماد في انتظاره...");
        AddLocalizationEntry("tip_wisdom", "Cultural wisdom guides the worthy...", "الحكمة الثقافية ترشد المستحقين...");
        AddLocalizationEntry("tip_death", "Death teaches the greatest lessons...", "الموت يعلم أعظم الدروس...");
        AddLocalizationEntry("tip_contemplation", "Contemplation brings inner strength...", "التأمل يجلب القوة الداخلية...");

        // Common UI
        AddLocalizationEntry("ui_yes", "Yes", "نعم");
        AddLocalizationEntry("ui_no", "No", "لا");
        AddLocalizationEntry("ui_ok", "OK", "موافق");
        AddLocalizationEntry("ui_cancel", "Cancel", "إلغاء");
        AddLocalizationEntry("ui_confirm", "Confirm", "تأكيد");
        AddLocalizationEntry("ui_loading", "Loading...", "جاري التحميل...");
        AddLocalizationEntry("ui_saving", "Saving...", "جاري الحفظ...");
        AddLocalizationEntry("ui_save_complete", "Save Complete", "تم الحفظ");
        AddLocalizationEntry("ui_load_complete", "Load Complete", "تم التحميل");

        // Graphics Settings
        AddLocalizationEntry("graphics_quality", "Graphics Quality", "جودة الرسوميات");
        AddLocalizationEntry("graphics_low", "Low", "منخفضة");
        AddLocalizationEntry("graphics_medium", "Medium", "متوسطة");
        AddLocalizationEntry("graphics_high", "High", "عالية");
        AddLocalizationEntry("graphics_ultra", "Ultra", "فائقة");
        AddLocalizationEntry("graphics_shadows", "Shadow Quality", "جودة الظلال");
        AddLocalizationEntry("graphics_textures", "Texture Quality", "جودة النسيج");
        AddLocalizationEntry("graphics_antialiasing", "Anti-Aliasing", "مكافحة التشويش");
        AddLocalizationEntry("graphics_postprocessing", "Post-Processing", "المعالجة اللاحقة");
        AddLocalizationEntry("graphics_particles", "Particle Effects", "تأثيرات الجسيمات");
        AddLocalizationEntry("graphics_reflections", "Reflections", "الانعكاسات");
        AddLocalizationEntry("graphics_framerate", "Frame Rate Limit", "حد معدل الإطارات");
        AddLocalizationEntry("graphics_auto_detect", "Auto Detect", "كشف تلقائي");

        // Voice Acting
        AddLocalizationEntry("voice_enable", "Enable Voice Acting", "تمكين التمثيل الصوتي");
        AddLocalizationEntry("voice_volume", "Voice Volume", "مستوى الصوت");
        AddLocalizationEntry("voice_subtitles", "Show Subtitles", "إظهار الترجمة");
        AddLocalizationEntry("voice_narrator", "Narrator", "الراوي");
        AddLocalizationEntry("voice_character", "Character", "الشخصية");
        AddLocalizationEntry("voice_skip", "Press ESC to skip", "اضغط ESC للتخطي");

        // Cinematics
        AddLocalizationEntry("cinematic_intro", "The Fall of the Island", "سقوط الجزيرة");
        AddLocalizationEntry("cinematic_outro", "The Choice of Light or Eclipse", "خيار النور أو الكسوف");
        AddLocalizationEntry("cinematic_skip", "Skip Cinematic", "تخطي المشهد السينمائي");

        // Steam Integration
        AddLocalizationEntry("steam_achievements", "Achievements", "الإنجازات");
        AddLocalizationEntry("steam_cloud_save", "Cloud Save", "الحفظ السحابي");
        AddLocalizationEntry("steam_overlay", "Steam Overlay", "واجهة Steam");
        AddLocalizationEntry("steam_not_available", "Steam not available", "Steam غير متوفر");

        // Error Messages
        AddLocalizationEntry("error_save_failed", "Save Failed", "فشل الحفظ");
        AddLocalizationEntry("error_load_failed", "Load Failed", "فشل التحميل");
        AddLocalizationEntry("error_no_save_found", "No Save Found", "لم يتم العثور على حفظ");
        AddLocalizationEntry("error_connection_failed", "Connection Failed", "فشل الاتصال");
        AddLocalizationEntry("error_steam_init_failed", "Steam initialization failed", "فشل تهيئة Steam");
        AddLocalizationEntry("error_graphics_not_supported", "Graphics setting not supported", "إعداد الرسوميات غير مدعوم");

        // Event System Localization
        AddEventLocalization();
    }

    void AddLocalizationEntry(string key, string english, string arabic)
    {
        if (!localizedStrings.ContainsKey(key))
        {
            localizedStrings[key] = new Dictionary<Language, string>();
        }

        localizedStrings[key][Language.English] = english;
        localizedStrings[key][Language.Arabic] = arabic;
    }

    void LoadLocalizationData()
    {
        // Try to load from CSV file
        string csvPath = Path.Combine(Application.streamingAssetsPath, localizationFolder, "localization.csv");

        if (File.Exists(csvPath))
        {
            LoadFromCSV(csvPath);
        }
        else
        {
            // Try to load from Resources
            TextAsset csvAsset = Resources.Load<TextAsset>($"{localizationFolder}/localization");
            if (csvAsset != null)
            {
                ParseCSVData(csvAsset.text);
            }
        }
    }

    void LoadFromCSV(string filePath)
    {
        try
        {
            string csvData = File.ReadAllText(filePath);
            ParseCSVData(csvData);
            Debug.Log("Localization data loaded from CSV file");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load localization CSV: {e.Message}");
        }
    }

    void ParseCSVData(string csvData)
    {
        string[] lines = csvData.Split('\n');

        for (int i = 1; i < lines.Length; i++) // Skip header
        {
            string line = lines[i].Trim();
            if (string.IsNullOrEmpty(line)) continue;

            string[] values = ParseCSVLine(line);
            if (values.Length >= 3)
            {
                string key = values[0];
                string english = values[1];
                string arabic = values[2];

                AddLocalizationEntry(key, english, arabic);
            }
        }
    }

    string[] ParseCSVLine(string line)
    {
        List<string> values = new List<string>();
        bool inQuotes = false;
        string currentValue = "";

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                values.Add(currentValue);
                currentValue = "";
            }
            else
            {
                currentValue += c;
            }
        }

        values.Add(currentValue);
        return values.ToArray();
    }

    void DetectSystemLanguage()
    {
        SystemLanguage systemLang = Application.systemLanguage;

        switch (systemLang)
        {
            case SystemLanguage.Arabic:
                currentLanguage = Language.Arabic;
                break;
            default:
                currentLanguage = Language.English;
                break;
        }

        Debug.Log($"Detected system language: {systemLang}, using: {currentLanguage}");
    }

    // Public API
    public static string GetLocalizedString(string key)
    {
        return Instance.GetString(key);
    }

    public string GetString(string key)
    {
        if (localizedStrings.ContainsKey(key) && localizedStrings[key].ContainsKey(currentLanguage))
        {
            return localizedStrings[key][currentLanguage];
        }

        // Fallback to English
        if (localizedStrings.ContainsKey(key) && localizedStrings[key].ContainsKey(Language.English))
        {
            return localizedStrings[key][Language.English];
        }

        // Return key if not found
        Debug.LogWarning($"Localization key not found: {key}");
        return key;
    }

    public static void SetLanguage(Language language)
    {
        Instance.ApplyLanguage(language);
    }

    public void ApplyLanguage(Language language)
    {
        currentLanguage = language;

        // Update all registered texts
        foreach (LocalizedText localizedText in registeredTexts)
        {
            if (localizedText != null)
            {
                localizedText.UpdateText();
            }
        }

        // Apply font changes
        ApplyFontChanges();

        // Apply text direction
        ApplyTextDirection();

        // Save language preference
        PlayerPrefs.SetString("Language", language.ToString());
        PlayerPrefs.Save();

        OnLanguageChanged?.Invoke(language);

        Debug.Log($"Language changed to: {language}");
    }

    void ApplyFontChanges()
    {
        TMP_FontAsset targetFont = currentLanguage == Language.Arabic ? arabicFont : englishFont;

        if (targetFont == null) return;

        // Update all TextMeshPro components
        TextMeshProUGUI[] allTexts = FindObjectsOfType<TextMeshProUGUI>(true);
        foreach (TextMeshProUGUI text in allTexts)
        {
            LocalizedText localizedText = text.GetComponent<LocalizedText>();
            if (localizedText != null)
            {
                text.font = targetFont;
            }
        }
    }

    void ApplyTextDirection()
    {
        bool isRTL = currentLanguage == Language.Arabic;

        // Update all localized texts with direction
        foreach (LocalizedText localizedText in registeredTexts)
        {
            if (localizedText != null)
            {
                localizedText.SetTextDirection(isRTL);
            }
        }
    }

    public static void RegisterLocalizedText(LocalizedText localizedText)
    {
        if (Instance.registeredTexts.Contains(localizedText)) return;

        Instance.registeredTexts.Add(localizedText);
        localizedText.UpdateText();
    }

    public static void UnregisterLocalizedText(LocalizedText localizedText)
    {
        Instance.registeredTexts.Remove(localizedText);
    }

    public static Language GetCurrentLanguage()
    {
        return Instance.currentLanguage;
    }

    public static bool IsRightToLeft()
    {
        return Instance.currentLanguage == Language.Arabic;
    }

    public static TMP_FontAsset GetCurrentFont()
    {
        return Instance.currentLanguage == Language.Arabic ? Instance.arabicFont : Instance.englishFont;
    }

    // CSV Export for translators
    public void ExportToCSV()
    {
        string csvContent = "Key,English,Arabic\n";

        foreach (var entry in localizedStrings)
        {
            string key = entry.Key;
            string english = entry.Value.ContainsKey(Language.English) ? entry.Value[Language.English] : "";
            string arabic = entry.Value.ContainsKey(Language.Arabic) ? entry.Value[Language.Arabic] : "";

            // Escape quotes and commas
            english = EscapeCSVValue(english);
            arabic = EscapeCSVValue(arabic);

            csvContent += $"{key},{english},{arabic}\n";
        }

        string filePath = Path.Combine(Application.persistentDataPath, "localization_export.csv");
        File.WriteAllText(filePath, csvContent);

        Debug.Log($"Localization data exported to: {filePath}");
    }

    string EscapeCSVValue(string value)
    {
        if (value.Contains(",") || value.Contains("\"") || value.Contains("\n"))
        {
            value = value.Replace("\"", "\"\"");
            value = $"\"{value}\"";
        }
        return value;
    }

    /// <summary>
    /// Add event system localization entries
    /// </summary>
    void AddEventLocalization()
    {
        // Event UI
        AddLocalizationEntry("event_panel_title", "World Events", "أحداث العالم");
        AddLocalizationEntry("event_active_events", "Active Events", "الأحداث النشطة");
        AddLocalizationEntry("event_upcoming_events", "Upcoming Events", "الأحداث القادمة");
        AddLocalizationEntry("event_historical_events", "Historical Events", "الأحداث التاريخية");
        AddLocalizationEntry("event_time_remaining", "{0} days remaining", "{0} أيام متبقية");
        AddLocalizationEntry("event_upcoming", "Upcoming", "قادم");
        AddLocalizationEntry("event_affected_areas", "Affected Areas", "المناطق المتأثرة");
        AddLocalizationEntry("event_duration", "Duration: {0} days", "المدة: {0} أيام");
        AddLocalizationEntry("event_has_begun", "has begun!", "قد بدأ!");
        AddLocalizationEntry("event_has_ended", "has ended", "قد انتهى");

        // Event Types
        AddLocalizationEntry("event_type_invasion", "Invasion", "غزو");
        AddLocalizationEntry("event_type_disaster", "Natural Disaster", "كارثة طبيعية");
        AddLocalizationEntry("event_type_disease", "Disease Outbreak", "تفشي مرض");
        AddLocalizationEntry("event_type_religious", "Religious Event", "حدث ديني");
        AddLocalizationEntry("event_type_political", "Political Event", "حدث سياسي");
        AddLocalizationEntry("event_type_economic", "Economic Event", "حدث اقتصادي");
        AddLocalizationEntry("event_type_supernatural", "Supernatural Event", "حدث خارق");
        AddLocalizationEntry("event_type_cultural", "Cultural Event", "حدث ثقافي");

        // Event Status
        AddLocalizationEntry("event_status_active", "Active", "نشط");
        AddLocalizationEntry("event_status_completed", "Completed", "مكتمل");
        AddLocalizationEntry("event_status_failed", "Failed", "فاشل");
        AddLocalizationEntry("event_status_cancelled", "Cancelled", "ملغى");

        // Event Severity
        AddLocalizationEntry("event_severity_minor", "Minor", "طفيف");
        AddLocalizationEntry("event_severity_moderate", "Moderate", "متوسط");
        AddLocalizationEntry("event_severity_major", "Major", "كبير");
        AddLocalizationEntry("event_severity_critical", "Critical", "حرج");

        // Event Impact Descriptions
        AddLocalizationEntry("event_impact_areas", "Affecting: {0}", "يؤثر على: {0}");
        AddLocalizationEntry("event_impact_invasion", "Travel is dangerous. Towns have closed their gates.", "السفر خطير. أغلقت البلدات أبوابها.");
        AddLocalizationEntry("event_impact_disaster", "Visibility reduced. Movement is difficult.", "الرؤية محدودة. الحركة صعبة.");
        AddLocalizationEntry("event_impact_disease", "Shops are closed. NPCs are falling ill.", "المتاجر مغلقة. الشخصيات تمرض.");
        AddLocalizationEntry("event_impact_religious", "Religious activity increased. Pilgrims traveling.", "النشاط الديني متزايد. الحجاج يسافرون.");
        AddLocalizationEntry("event_impact_political", "Tensions high. Faction conflicts possible.", "التوترات عالية. صراعات الفصائل محتملة.");

        // Flash Briefings
        AddLocalizationEntry("event_briefing_invasion", "⚔️ INVASION ALERT", "⚔️ تنبيه غزو");
        AddLocalizationEntry("event_briefing_naturaldisaster", "🌪️ DISASTER WARNING", "🌪️ تحذير كارثة");
        AddLocalizationEntry("event_briefing_disease", "🦠 HEALTH CRISIS", "🦠 أزمة صحية");
        AddLocalizationEntry("event_briefing_religious", "🕊️ RELIGIOUS GATHERING", "🕊️ تجمع ديني");
        AddLocalizationEntry("event_briefing_political", "⚖️ POLITICAL UNREST", "⚖️ اضطراب سياسي");

        // Specific Event Briefings
        AddLocalizationEntry("event_briefing_desc_orc_invasion_01", "Orc warbands have invaded the southern territories. Seek shelter or prepare for battle!", "عصابات الأورك غزت الأراضي الجنوبية. اطلب المأوى أو استعد للمعركة!");
        AddLocalizationEntry("event_briefing_desc_ash_storm_01", "A massive ash storm approaches. Visibility will be severely limited.", "عاصفة رماد ضخمة تقترب. الرؤية ستكون محدودة جداً.");
        AddLocalizationEntry("event_briefing_desc_plague_outbreak_01", "A mysterious disease spreads through the population. Avoid crowded areas.", "مرض غامض ينتشر بين السكان. تجنب المناطق المزدحمة.");
        AddLocalizationEntry("event_briefing_desc_flame_pilgrimage_01", "Followers of the Faith of the Flame begin their sacred journey.", "أتباع إيمان اللهب يبدؤون رحلتهم المقدسة.");
        AddLocalizationEntry("event_briefing_desc_realm_conflict_01", "Border tensions escalate into open warfare between realms.", "التوترات الحدودية تتصاعد إلى حرب مفتوحة بين العوالم.");

        // NPC Dialogue Context
        AddLocalizationEntry("npc_dialogue_invasion_civilian", "I heard orcs are marching from the south...", "سمعت أن الأورك يزحفون من الجنوب...");
        AddLocalizationEntry("npc_dialogue_invasion_guard", "All units to defensive positions!", "جميع الوحدات إلى المواقع الدفاعية!");
        AddLocalizationEntry("npc_dialogue_disaster_civilian", "This ash is getting into everything...", "هذا الرماد يدخل في كل شيء...");
        AddLocalizationEntry("npc_dialogue_disaster_elder", "I've seen storms before, but nothing like this.", "رأيت عواصف من قبل، لكن لا شيء مثل هذا.");

        // Player Interaction Options
        AddLocalizationEntry("event_option_ignore", "Ignore", "تجاهل");
        AddLocalizationEntry("event_option_intervene", "Intervene", "تدخل");
        AddLocalizationEntry("event_option_manipulate", "Manipulate", "تلاعب");
        AddLocalizationEntry("event_option_assist", "Assist", "ساعد");
        AddLocalizationEntry("event_option_oppose", "Oppose", "عارض");
        AddLocalizationEntry("event_option_investigate", "Investigate", "تحقق");
        AddLocalizationEntry("event_option_negotiate", "Negotiate", "تفاوض");
        AddLocalizationEntry("event_option_fight", "Fight", "قاتل");

        // Event Completion Messages
        AddLocalizationEntry("event_resolved", "Event Resolved", "تم حل الحدث");
        AddLocalizationEntry("event_failed", "Event Failed", "فشل الحدث");
        AddLocalizationEntry("event_escalated", "Event Escalated", "تصاعد الحدث");

        Debug.Log("Event system localization added");
    }

    /// <summary>
    /// Get localized text with parameter substitution
    /// </summary>
    public string GetLocalizedText(string key, params string[] parameters)
    {
        string text = GetString(key);

        if (parameters != null && parameters.Length > 0)
        {
            for (int i = 0; i < parameters.Length; i++)
            {
                text = text.Replace($"{{{i}}}", parameters[i]);
            }
        }

        return text;
    }
}

/// <summary>
/// Component for automatically localizing text elements
/// </summary>
public class LocalizedText : MonoBehaviour
{
    [Header("Localization")]
    public string localizationKey;
    public bool autoRegister = true;
    public bool updateFontAutomatically = true;

    private TextMeshProUGUI textComponent;
    private Text legacyTextComponent;
    private RectTransform rectTransform;

    void Awake()
    {
        textComponent = GetComponent<TextMeshProUGUI>();
        legacyTextComponent = GetComponent<Text>();
        rectTransform = GetComponent<RectTransform>();
    }

    void Start()
    {
        if (autoRegister)
        {
            LocalizationManager.RegisterLocalizedText(this);
        }
    }

    void OnDestroy()
    {
        LocalizationManager.UnregisterLocalizedText(this);
    }

    public void UpdateText()
    {
        if (string.IsNullOrEmpty(localizationKey)) return;

        string localizedString = LocalizationManager.GetLocalizedString(localizationKey);

        if (textComponent != null)
        {
            textComponent.text = localizedString;

            if (updateFontAutomatically)
            {
                TMP_FontAsset currentFont = LocalizationManager.GetCurrentFont();
                if (currentFont != null)
                {
                    textComponent.font = currentFont;
                }
            }
        }
        else if (legacyTextComponent != null)
        {
            legacyTextComponent.text = localizedString;
        }
    }

    public void SetTextDirection(bool isRightToLeft)
    {
        if (textComponent != null)
        {
            if (isRightToLeft)
            {
                textComponent.alignment = TextAlignmentOptions.TopRight;
                textComponent.isRightToLeftText = true;
            }
            else
            {
                textComponent.alignment = TextAlignmentOptions.TopLeft;
                textComponent.isRightToLeftText = false;
            }
        }

        // Adjust layout for RTL
        if (rectTransform != null && isRightToLeft)
        {
            // Flip horizontal alignment
            Vector2 anchorMin = rectTransform.anchorMin;
            Vector2 anchorMax = rectTransform.anchorMax;

            float tempMin = 1f - anchorMax.x;
            float tempMax = 1f - anchorMin.x;

            anchorMin.x = tempMin;
            anchorMax.x = tempMax;

            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
        }
    }

    public void SetLocalizationKey(string key)
    {
        localizationKey = key;
        UpdateText();
    }
}
