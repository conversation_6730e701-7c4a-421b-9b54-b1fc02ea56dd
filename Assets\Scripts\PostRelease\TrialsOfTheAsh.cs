using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

/// <summary>
/// Trials of the Ash - Boss Rush and Challenge Mode System
/// Post-release content for enhanced replayability and player skill testing
/// </summary>
public class TrialsOfTheAsh : MonoBehaviour
{
    [Header("Trial Configuration")]
    public bool trialsUnlocked = false;
    public TrialDefinition[] availableTrials;
    public Transform ashMirrorSpawnPoint;
    public GameObject ashMirrorEnvironment;
    
    [Head<PERSON>("Arena Settings")]
    public Camera trialCamera;
    public Transform[] bossSpawnPoints;
    public GameObject[] environmentPresets;
    
    [Header("UI References")]
    public GameObject trialsMenuUI;
    public GameObject trialHUD;
    public TMPro.TextMeshProUGUI trialTimerText;
    public TMPro.TextMeshProUGUI trialProgressText;
    public UnityEngine.UI.Slider bossHealthBar;
    
    // Static instance
    private static TrialsOfTheAsh instance;
    public static TrialsOfTheAsh Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<TrialsOfTheAsh>();
                if (instance == null)
                {
                    GameObject go = new GameObject("TrialsOfTheAsh");
                    instance = go.AddComponent<TrialsOfTheAsh>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Trial definitions
    [System.Serializable]
    public class TrialDefinition
    {
        public string trialId;
        public string trialName;
        public string description;
        public TrialType type;
        public float timeLimit;
        public bool allowHealing;
        public bool allowMagic;
        public float difficultyMultiplier;
        public string[] requiredBosses;
        public TrialReward[] rewards;
        public bool isCompleted;
        public float bestTime;
        public int attempts;
    }
    
    public enum TrialType
    {
        Endurance,      // Survive all bosses without dying
        Timed,          // Defeat bosses under time limits
        Build,          // Use final story build
        Gauntlet,       // Custom boss sequence
        Survival,       // Endless waves
        Speedrun        // Complete as fast as possible
    }
    
    [System.Serializable]
    public class TrialReward
    {
        public RewardType type;
        public string rewardId;
        public string displayName;
        public string description;
        public Sprite icon;
    }
    
    public enum RewardType
    {
        CosmeticScar,
        WeaponAura,
        LoreFragment,
        ModUnlock,
        Title,
        Achievement
    }
    
    // Trial state
    private TrialDefinition currentTrial;
    private bool isTrialActive = false;
    private float trialStartTime;
    private int currentBossIndex = 0;
    private List<GameObject> spawnedBosses = new List<GameObject>();
    private TrialSession currentSession;
    
    [System.Serializable]
    public class TrialSession
    {
        public string trialId;
        public float startTime;
        public int bossesDefeated;
        public int totalBosses;
        public bool playerDied;
        public float completionTime;
        public List<string> unlockedRewards;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeTrials();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        LoadTrialProgress();
        SetupTrialDefinitions();
        CheckTrialUnlockStatus();
    }
    
    void InitializeTrials()
    {
        Debug.Log("Trials of the Ash initialized");
    }
    
    void SetupTrialDefinitions()
    {
        if (availableTrials == null || availableTrials.Length == 0)
        {
            CreateDefaultTrials();
        }
    }
    
    void CreateDefaultTrials()
    {
        List<TrialDefinition> trials = new List<TrialDefinition>();
        
        // Endurance Trial
        trials.Add(new TrialDefinition
        {
            trialId = "endurance_all_bosses",
            trialName = "Trial of Endurance",
            description = "Defeat all story bosses in sequence without dying",
            type = TrialType.Endurance,
            timeLimit = 0f, // No time limit
            allowHealing = true,
            allowMagic = true,
            difficultyMultiplier = 1.2f,
            requiredBosses = new string[] { "shadow_lord", "flame_titan", "ice_queen", "wind_spirit", "final_boss" },
            rewards = new TrialReward[]
            {
                new TrialReward { type = RewardType.CosmeticScar, rewardId = "endurance_scar", displayName = "Scar of Endurance" },
                new TrialReward { type = RewardType.LoreFragment, rewardId = "ancient_trials_lore", displayName = "Ancient Trials Codex" }
            }
        });
        
        // Timed Trial
        trials.Add(new TrialDefinition
        {
            trialId = "timed_speedrun",
            trialName = "Trial of Speed",
            description = "Defeat each boss within strict time limits",
            type = TrialType.Timed,
            timeLimit = 300f, // 5 minutes per boss
            allowHealing = true,
            allowMagic = true,
            difficultyMultiplier = 1.0f,
            requiredBosses = new string[] { "shadow_lord", "flame_titan", "ice_queen" },
            rewards = new TrialReward[]
            {
                new TrialReward { type = RewardType.WeaponAura, rewardId = "speed_aura", displayName = "Aura of Swiftness" },
                new TrialReward { type = RewardType.Title, rewardId = "speedrunner", displayName = "The Swift" }
            }
        });
        
        // Build Trial
        trials.Add(new TrialDefinition
        {
            trialId = "build_challenge",
            trialName = "Trial of Mastery",
            description = "Use your final story build against enhanced bosses",
            type = TrialType.Build,
            timeLimit = 0f,
            allowHealing = false,
            allowMagic = true,
            difficultyMultiplier = 1.5f,
            requiredBosses = new string[] { "enhanced_shadow_lord", "enhanced_flame_titan" },
            rewards = new TrialReward[]
            {
                new TrialReward { type = RewardType.ModUnlock, rewardId = "advanced_editor", displayName = "Advanced Mod Tools" },
                new TrialReward { type = RewardType.CosmeticScar, rewardId = "mastery_scar", displayName = "Mark of Mastery" }
            }
        });
        
        // Survival Trial
        trials.Add(new TrialDefinition
        {
            trialId = "endless_survival",
            trialName = "Trial of Survival",
            description = "Survive endless waves of enemies in the Ash Mirror",
            type = TrialType.Survival,
            timeLimit = 0f,
            allowHealing = true,
            allowMagic = true,
            difficultyMultiplier = 1.0f,
            requiredBosses = new string[] { "endless_waves" },
            rewards = new TrialReward[]
            {
                new TrialReward { type = RewardType.WeaponAura, rewardId = "survivor_aura", displayName = "Aura of the Survivor" },
                new TrialReward { type = RewardType.LoreFragment, rewardId = "ash_mirror_lore", displayName = "Secrets of the Ash Mirror" }
            }
        });
        
        availableTrials = trials.ToArray();
    }
    
    void CheckTrialUnlockStatus()
    {
        // Check if player has completed the main story
        var gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            trialsUnlocked = gameManager.HasCompletedMainStory();
        }
        
        // Fallback check using save system
        if (!trialsUnlocked)
        {
            trialsUnlocked = PlayerPrefs.GetInt("MainStoryCompleted", 0) == 1;
        }
        
        Debug.Log($"Trials unlocked: {trialsUnlocked}");
    }
    
    // Public API
    public static bool AreTrialsUnlocked()
    {
        return Instance.trialsUnlocked;
    }
    
    public static void UnlockTrials()
    {
        Instance.trialsUnlocked = true;
        Instance.SaveTrialProgress();
        Debug.Log("Trials of the Ash unlocked!");
    }
    
    public static void StartTrial(string trialId)
    {
        Instance.StartTrialInternal(trialId);
    }
    
    public static void EndTrial(bool completed)
    {
        Instance.EndTrialInternal(completed);
    }
    
    public static TrialDefinition[] GetAvailableTrials()
    {
        return Instance.availableTrials;
    }
    
    public static bool IsTrialActive()
    {
        return Instance.isTrialActive;
    }
    
    void StartTrialInternal(string trialId)
    {
        if (!trialsUnlocked)
        {
            Debug.LogWarning("Trials not unlocked yet!");
            return;
        }
        
        currentTrial = availableTrials.FirstOrDefault(t => t.trialId == trialId);
        if (currentTrial == null)
        {
            Debug.LogError($"Trial not found: {trialId}");
            return;
        }
        
        // Initialize trial session
        currentSession = new TrialSession
        {
            trialId = trialId,
            startTime = Time.time,
            bossesDefeated = 0,
            totalBosses = currentTrial.requiredBosses.Length,
            playerDied = false,
            unlockedRewards = new List<string>()
        };
        
        isTrialActive = true;
        trialStartTime = Time.time;
        currentBossIndex = 0;
        
        // Setup trial environment
        SetupTrialEnvironment();
        
        // Start first boss encounter
        SpawnNextBoss();
        
        // Show trial UI
        if (trialHUD != null)
            trialHUD.SetActive(true);
        
        // Notify other systems
        OnTrialStarted();
        
        Debug.Log($"Started trial: {currentTrial.trialName}");
    }
    
    void SetupTrialEnvironment()
    {
        // Activate Ash Mirror environment
        if (ashMirrorEnvironment != null)
            ashMirrorEnvironment.SetActive(true);
        
        // Switch to trial camera
        if (trialCamera != null)
        {
            trialCamera.enabled = true;
            Camera.main.enabled = false;
        }
        
        // Apply trial modifiers to player
        ApplyTrialModifiers();
        
        // Set appropriate lighting and atmosphere
        SetTrialAtmosphere();
    }
    
    void ApplyTrialModifiers()
    {
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player == null) return;
        
        var playerStats = player.GetComponent<PlayerStats>();
        if (playerStats != null)
        {
            // Apply trial-specific modifiers
            if (!currentTrial.allowHealing)
            {
                // Disable healing items/abilities
                var healingSystem = player.GetComponent<HealingSystem>();
                if (healingSystem != null)
                    healingSystem.SetHealingEnabled(false);
            }
            
            if (!currentTrial.allowMagic)
            {
                // Disable magic system
                var magicSystem = FindObjectOfType<ElementalMagicSystem>();
                if (magicSystem != null)
                    magicSystem.SetMagicEnabled(false);
            }
        }
    }
    
    void SetTrialAtmosphere()
    {
        // Set dramatic lighting for trials
        var mainLight = FindObjectOfType<Light>();
        if (mainLight != null)
        {
            mainLight.color = new Color(0.8f, 0.6f, 0.4f); // Ash-like color
            mainLight.intensity = 1.5f;
        }
        
        // Enable post-processing effects
        GraphicsManager.EnableChromaticAberration(true, 0.2f);
        GraphicsManager.SetBloomIntensity(0.6f);
    }
    
    void SpawnNextBoss()
    {
        if (currentBossIndex >= currentTrial.requiredBosses.Length)
        {
            // All bosses defeated - trial completed
            EndTrialInternal(true);
            return;
        }
        
        string bossId = currentTrial.requiredBosses[currentBossIndex];
        Vector3 spawnPosition = bossSpawnPoints[currentBossIndex % bossSpawnPoints.Length].position;
        
        // Spawn boss
        GameObject boss = SpawnBoss(bossId, spawnPosition);
        if (boss != null)
        {
            spawnedBosses.Add(boss);
            
            // Apply difficulty multiplier
            var bossStats = boss.GetComponent<EnemyStats>();
            if (bossStats != null)
            {
                bossStats.health *= currentTrial.difficultyMultiplier;
                bossStats.damage *= currentTrial.difficultyMultiplier;
            }
            
            // Setup boss event handlers
            var bossController = boss.GetComponent<BossController>();
            if (bossController != null)
            {
                bossController.OnBossDefeated += OnBossDefeated;
            }
        }
        
        UpdateTrialUI();
    }
    
    GameObject SpawnBoss(string bossId, Vector3 position)
    {
        // Load boss prefab from resources
        GameObject bossPrefab = Resources.Load<GameObject>($"Bosses/{bossId}");
        if (bossPrefab == null)
        {
            Debug.LogError($"Boss prefab not found: {bossId}");
            return null;
        }
        
        GameObject boss = Instantiate(bossPrefab, position, Quaternion.identity);
        return boss;
    }
    
    void OnBossDefeated()
    {
        currentSession.bossesDefeated++;
        currentBossIndex++;
        
        // Clean up current boss
        foreach (var boss in spawnedBosses)
        {
            if (boss != null)
                Destroy(boss);
        }
        spawnedBosses.Clear();
        
        // Check if trial is complete
        if (currentBossIndex >= currentTrial.requiredBosses.Length)
        {
            EndTrialInternal(true);
        }
        else
        {
            // Spawn next boss after a brief delay
            StartCoroutine(SpawnNextBossDelayed(2f));
        }
        
        UpdateTrialUI();
    }
    
    IEnumerator SpawnNextBossDelayed(float delay)
    {
        yield return new WaitForSeconds(delay);
        SpawnNextBoss();
    }
    
    void EndTrialInternal(bool completed)
    {
        if (!isTrialActive) return;
        
        isTrialActive = false;
        currentSession.completionTime = Time.time - trialStartTime;
        
        // Clean up trial environment
        CleanupTrialEnvironment();
        
        if (completed)
        {
            // Mark trial as completed
            currentTrial.isCompleted = true;
            currentTrial.attempts++;
            
            // Update best time if applicable
            if (currentTrial.type == TrialType.Timed || currentTrial.type == TrialType.Speedrun)
            {
                if (currentTrial.bestTime == 0f || currentSession.completionTime < currentTrial.bestTime)
                {
                    currentTrial.bestTime = currentSession.completionTime;
                }
            }
            
            // Award rewards
            AwardTrialRewards();
            
            // Show completion UI
            ShowTrialCompletionUI();
            
            Debug.Log($"Trial completed: {currentTrial.trialName} in {currentSession.completionTime:F2} seconds");
        }
        else
        {
            currentTrial.attempts++;
            ShowTrialFailureUI();
            Debug.Log($"Trial failed: {currentTrial.trialName}");
        }
        
        // Save progress
        SaveTrialProgress();
        
        // Notify other systems
        OnTrialEnded(completed);
    }
    
    void CleanupTrialEnvironment()
    {
        // Deactivate Ash Mirror environment
        if (ashMirrorEnvironment != null)
            ashMirrorEnvironment.SetActive(false);
        
        // Restore main camera
        if (trialCamera != null)
        {
            trialCamera.enabled = false;
            Camera.main.enabled = true;
        }
        
        // Remove trial modifiers
        RemoveTrialModifiers();
        
        // Clean up spawned bosses
        foreach (var boss in spawnedBosses)
        {
            if (boss != null)
                Destroy(boss);
        }
        spawnedBosses.Clear();
        
        // Hide trial UI
        if (trialHUD != null)
            trialHUD.SetActive(false);
        
        // Restore normal graphics settings
        GraphicsManager.EnableChromaticAberration(false);
        GraphicsManager.SetBloomIntensity(0.3f);
    }
    
    void RemoveTrialModifiers()
    {
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player == null) return;
        
        // Re-enable healing if it was disabled
        var healingSystem = player.GetComponent<HealingSystem>();
        if (healingSystem != null)
            healingSystem.SetHealingEnabled(true);
        
        // Re-enable magic if it was disabled
        var magicSystem = FindObjectOfType<ElementalMagicSystem>();
        if (magicSystem != null)
            magicSystem.SetMagicEnabled(true);
    }
    
    void AwardTrialRewards()
    {
        foreach (var reward in currentTrial.rewards)
        {
            UnlockReward(reward);
            currentSession.unlockedRewards.Add(reward.rewardId);
        }
    }
    
    void UnlockReward(TrialReward reward)
    {
        switch (reward.type)
        {
            case RewardType.CosmeticScar:
                UnlockCosmeticScar(reward.rewardId);
                break;
            case RewardType.WeaponAura:
                UnlockWeaponAura(reward.rewardId);
                break;
            case RewardType.LoreFragment:
                UnlockLoreFragment(reward.rewardId);
                break;
            case RewardType.ModUnlock:
                UnlockModTool(reward.rewardId);
                break;
            case RewardType.Title:
                UnlockTitle(reward.rewardId);
                break;
            case RewardType.Achievement:
                SteamworksIntegration.UnlockAchievement(reward.rewardId);
                break;
        }
        
        Debug.Log($"Reward unlocked: {reward.displayName}");
    }
    
    void UnlockCosmeticScar(string scarId)
    {
        PlayerPrefs.SetInt($"CosmeticScar_{scarId}", 1);
        // Notify character customization system
    }
    
    void UnlockWeaponAura(string auraId)
    {
        PlayerPrefs.SetInt($"WeaponAura_{auraId}", 1);
        // Notify weapon system
    }
    
    void UnlockLoreFragment(string loreId)
    {
        PlayerPrefs.SetInt($"LoreFragment_{loreId}", 1);
        // Add to lore collection
    }
    
    void UnlockModTool(string toolId)
    {
        PlayerPrefs.SetInt($"ModTool_{toolId}", 1);
        // Notify modding system
        var moddingSystem = FindObjectOfType<ModdingSystem>();
        if (moddingSystem != null)
        {
            moddingSystem.UnlockTool(toolId);
        }
    }
    
    void UnlockTitle(string titleId)
    {
        PlayerPrefs.SetInt($"Title_{titleId}", 1);
        // Notify player profile system
    }
    
    void ShowTrialCompletionUI()
    {
        // Show completion screen with rewards
        // Implementation would create UI showing completion time, rewards, etc.
        Debug.Log("Trial completion UI shown");
    }
    
    void ShowTrialFailureUI()
    {
        // Show failure screen with retry option
        Debug.Log("Trial failure UI shown");
    }
    
    void UpdateTrialUI()
    {
        if (!isTrialActive) return;
        
        // Update timer
        if (trialTimerText != null)
        {
            float elapsedTime = Time.time - trialStartTime;
            if (currentTrial.timeLimit > 0)
            {
                float remainingTime = currentTrial.timeLimit - elapsedTime;
                trialTimerText.text = $"Time: {remainingTime:F1}s";
                
                // Check time limit
                if (remainingTime <= 0)
                {
                    EndTrialInternal(false);
                    return;
                }
            }
            else
            {
                trialTimerText.text = $"Time: {elapsedTime:F1}s";
            }
        }
        
        // Update progress
        if (trialProgressText != null)
        {
            trialProgressText.text = $"Bosses: {currentSession.bossesDefeated}/{currentSession.totalBosses}";
        }
        
        // Update boss health bar
        if (bossHealthBar != null && spawnedBosses.Count > 0)
        {
            var currentBoss = spawnedBosses[0];
            if (currentBoss != null)
            {
                var bossStats = currentBoss.GetComponent<EnemyStats>();
                if (bossStats != null)
                {
                    bossHealthBar.value = bossStats.health / bossStats.maxHealth;
                }
            }
        }
    }
    
    void Update()
    {
        if (isTrialActive)
        {
            UpdateTrialUI();
            
            // Check for player death
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                var playerStats = player.GetComponent<PlayerStats>();
                if (playerStats != null && playerStats.currentHealth <= 0)
                {
                    OnPlayerDied();
                }
            }
        }
    }
    
    void OnPlayerDied()
    {
        currentSession.playerDied = true;
        
        // End trial if it's an endurance trial
        if (currentTrial.type == TrialType.Endurance)
        {
            EndTrialInternal(false);
        }
    }
    
    // Event handlers
    void OnTrialStarted()
    {
        // Notify other systems
        var gameController = FindObjectOfType<CinderOfDarknessGameController>();
        if (gameController != null)
        {
            gameController.OnGameEvent("TrialStarted", new Dictionary<string, object>
            {
                { "trialId", currentTrial.trialId },
                { "trialType", currentTrial.type.ToString() }
            });
        }
    }
    
    void OnTrialEnded(bool completed)
    {
        // Notify other systems
        var gameController = FindObjectOfType<CinderOfDarknessGameController>();
        if (gameController != null)
        {
            gameController.OnGameEvent("TrialEnded", new Dictionary<string, object>
            {
                { "trialId", currentTrial.trialId },
                { "completed", completed },
                { "completionTime", currentSession.completionTime }
            });
        }
        
        // Steam integration
        if (completed)
        {
            SteamworksIntegration.UnlockAchievement($"TRIAL_{currentTrial.trialId.ToUpper()}");
        }
    }
    
    // Save/Load system
    void SaveTrialProgress()
    {
        var progressData = new TrialProgressData
        {
            trialsUnlocked = trialsUnlocked,
            completedTrials = availableTrials.Where(t => t.isCompleted).Select(t => t.trialId).ToArray(),
            bestTimes = availableTrials.ToDictionary(t => t.trialId, t => t.bestTime),
            attempts = availableTrials.ToDictionary(t => t.trialId, t => t.attempts)
        };
        
        string json = JsonUtility.ToJson(progressData, true);
        PlayerPrefs.SetString("TrialProgress", json);
        PlayerPrefs.Save();
    }
    
    void LoadTrialProgress()
    {
        string json = PlayerPrefs.GetString("TrialProgress", "");
        if (!string.IsNullOrEmpty(json))
        {
            try
            {
                var progressData = JsonUtility.FromJson<TrialProgressData>(json);
                trialsUnlocked = progressData.trialsUnlocked;
                
                // Apply loaded progress to trials
                foreach (var trial in availableTrials)
                {
                    trial.isCompleted = progressData.completedTrials.Contains(trial.trialId);
                    if (progressData.bestTimes.ContainsKey(trial.trialId))
                        trial.bestTime = progressData.bestTimes[trial.trialId];
                    if (progressData.attempts.ContainsKey(trial.trialId))
                        trial.attempts = progressData.attempts[trial.trialId];
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load trial progress: {e.Message}");
            }
        }
    }
    
    [System.Serializable]
    public class TrialProgressData
    {
        public bool trialsUnlocked;
        public string[] completedTrials;
        public Dictionary<string, float> bestTimes;
        public Dictionary<string, int> attempts;
    }
    
    // Integration with main game
    public void OnMainStoryCompleted()
    {
        UnlockTrials();
    }
    
    // Debug methods
    #if UNITY_EDITOR
    [ContextMenu("Unlock All Trials")]
    public void DebugUnlockAllTrials()
    {
        trialsUnlocked = true;
        foreach (var trial in availableTrials)
        {
            trial.isCompleted = true;
        }
        SaveTrialProgress();
    }
    
    [ContextMenu("Reset Trial Progress")]
    public void DebugResetTrialProgress()
    {
        trialsUnlocked = false;
        foreach (var trial in availableTrials)
        {
            trial.isCompleted = false;
            trial.bestTime = 0f;
            trial.attempts = 0;
        }
        SaveTrialProgress();
    }
    #endif
}
