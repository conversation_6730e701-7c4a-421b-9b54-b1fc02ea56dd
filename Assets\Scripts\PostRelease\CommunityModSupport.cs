using UnityEngine;
using System.Collections.Generic;
using System.Reflection;
using System.Linq;
using Newtonsoft.Json;

/// <summary>
/// Community Mod Support System for Cinder of Darkness
/// Enables external mods to extend arena functionality with custom enemies, props, and mechanics
/// </summary>
public class CommunityModSupport : MonoBehaviour
{
    [Header("Mod Support Settings")]
    public bool modSupportEnabled = true;
    public bool allowExternalAssemblies = false;
    public bool enableReflectionAPI = true;
    public string[] trustedModAuthors = { "CinderOfDarkness", "OfficialMods" };
    
    [Header("Custom Content")]
    public Dictionary<string, ModEnemyData> customEnemies = new Dictionary<string, ModEnemyData>();
    public Dictionary<string, ModPrefabReference> customProps = new Dictionary<string, ModPrefabReference>();
    public Dictionary<string, ModEffectData> customEffects = new Dictionary<string, ModEffectData>();
    
    // Static instance
    private static CommunityModSupport instance;
    public static CommunityModSupport Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CommunityModSupport>();
                if (instance == null)
                {
                    GameObject go = new GameObject("CommunityModSupport");
                    instance = go.AddComponent<CommunityModSupport>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Mod data structures
    [System.Serializable]
    public class ModEnemyData
    {
        public string enemyId;
        public string enemyName;
        public string prefabPath;
        public float health;
        public float damage;
        public float speed;
        public string[] abilities;
        public string aiType;
        public string modAuthor;
        public bool isTrusted;
    }
    
    [System.Serializable]
    public class ModPrefabReference
    {
        public string prefabId;
        public string prefabName;
        public string prefabPath;
        public string category;
        public bool isDestructible;
        public float health;
        public string[] tags;
        public string modAuthor;
        public bool isTrusted;
    }
    
    [System.Serializable]
    public class ModEffectData
    {
        public string effectId;
        public string effectName;
        public string effectType;
        public float duration;
        public float intensity;
        public string[] parameters;
        public string modAuthor;
        public bool isTrusted;
    }
    
    [System.Serializable]
    public class ModArenaExtension
    {
        public string arenaId;
        public string[] customEnemyIds;
        public string[] customPropIds;
        public string[] customEffectIds;
        public Dictionary<string, object> customProperties;
        public string modAuthor;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCommunityModSupport();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        if (modSupportEnabled)
        {
            LoadCommunityMods();
            RegisterModAPI();
        }
    }
    
    void InitializeCommunityModSupport()
    {
        Debug.Log("Community Mod Support initialized");
    }
    
    void LoadCommunityMods()
    {
        // Load custom enemies
        LoadCustomEnemies();
        
        // Load custom props
        LoadCustomProps();
        
        // Load custom effects
        LoadCustomEffects();
        
        // Scan for external mod assemblies
        if (allowExternalAssemblies)
        {
            ScanForExternalMods();
        }
    }
    
    void LoadCustomEnemies()
    {
        // Load from Resources/Mods/Enemies
        var enemyConfigs = Resources.LoadAll<TextAsset>("Mods/Enemies");
        
        foreach (var config in enemyConfigs)
        {
            try
            {
                var enemyData = JsonConvert.DeserializeObject<ModEnemyData>(config.text);
                if (ValidateEnemyData(enemyData))
                {
                    customEnemies[enemyData.enemyId] = enemyData;
                    Debug.Log($"Loaded custom enemy: {enemyData.enemyName}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load enemy config {config.name}: {e.Message}");
            }
        }
    }
    
    void LoadCustomProps()
    {
        // Load from Resources/Mods/Props
        var propConfigs = Resources.LoadAll<TextAsset>("Mods/Props");
        
        foreach (var config in propConfigs)
        {
            try
            {
                var propData = JsonConvert.DeserializeObject<ModPrefabReference>(config.text);
                if (ValidatePropData(propData))
                {
                    customProps[propData.prefabId] = propData;
                    Debug.Log($"Loaded custom prop: {propData.prefabName}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load prop config {config.name}: {e.Message}");
            }
        }
    }
    
    void LoadCustomEffects()
    {
        // Load from Resources/Mods/Effects
        var effectConfigs = Resources.LoadAll<TextAsset>("Mods/Effects");
        
        foreach (var config in effectConfigs)
        {
            try
            {
                var effectData = JsonConvert.DeserializeObject<ModEffectData>(config.text);
                if (ValidateEffectData(effectData))
                {
                    customEffects[effectData.effectId] = effectData;
                    Debug.Log($"Loaded custom effect: {effectData.effectName}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load effect config {config.name}: {e.Message}");
            }
        }
    }
    
    void ScanForExternalMods()
    {
        if (!enableReflectionAPI) return;
        
        try
        {
            // Scan loaded assemblies for mod interfaces
            var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
            
            foreach (var assembly in assemblies)
            {
                ScanAssemblyForMods(assembly);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to scan for external mods: {e.Message}");
        }
    }
    
    void ScanAssemblyForMods(Assembly assembly)
    {
        try
        {
            var types = assembly.GetTypes();
            
            foreach (var type in types)
            {
                // Look for types implementing mod interfaces
                if (typeof(IArenaModExtension).IsAssignableFrom(type) && !type.IsInterface && !type.IsAbstract)
                {
                    RegisterModExtension(type);
                }
                
                if (typeof(ICustomEnemyProvider).IsAssignableFrom(type) && !type.IsInterface && !type.IsAbstract)
                {
                    RegisterCustomEnemyProvider(type);
                }
                
                if (typeof(ICustomPropProvider).IsAssignableFrom(type) && !type.IsInterface && !type.IsAbstract)
                {
                    RegisterCustomPropProvider(type);
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Failed to scan assembly {assembly.FullName}: {e.Message}");
        }
    }
    
    void RegisterModExtension(System.Type type)
    {
        try
        {
            var instance = System.Activator.CreateInstance(type) as IArenaModExtension;
            if (instance != null)
            {
                var modInfo = instance.GetModInfo();
                if (IsTrustedMod(modInfo.author))
                {
                    instance.Initialize();
                    Debug.Log($"Registered mod extension: {modInfo.name} by {modInfo.author}");
                }
                else
                {
                    Debug.LogWarning($"Untrusted mod extension ignored: {modInfo.name} by {modInfo.author}");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to register mod extension {type.Name}: {e.Message}");
        }
    }
    
    void RegisterCustomEnemyProvider(System.Type type)
    {
        try
        {
            var instance = System.Activator.CreateInstance(type) as ICustomEnemyProvider;
            if (instance != null)
            {
                var enemies = instance.GetCustomEnemies();
                foreach (var enemy in enemies)
                {
                    if (ValidateEnemyData(enemy) && IsTrustedMod(enemy.modAuthor))
                    {
                        customEnemies[enemy.enemyId] = enemy;
                        Debug.Log($"Registered custom enemy: {enemy.enemyName}");
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to register custom enemy provider {type.Name}: {e.Message}");
        }
    }
    
    void RegisterCustomPropProvider(System.Type type)
    {
        try
        {
            var instance = System.Activator.CreateInstance(type) as ICustomPropProvider;
            if (instance != null)
            {
                var props = instance.GetCustomProps();
                foreach (var prop in props)
                {
                    if (ValidatePropData(prop) && IsTrustedMod(prop.modAuthor))
                    {
                        customProps[prop.prefabId] = prop;
                        Debug.Log($"Registered custom prop: {prop.prefabName}");
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to register custom prop provider {type.Name}: {e.Message}");
        }
    }
    
    void RegisterModAPI()
    {
        // Register API methods that mods can call
        if (enableReflectionAPI)
        {
            // This would expose safe API methods to external mods
            Debug.Log("Mod API registered");
        }
    }
    
    // Validation methods
    bool ValidateEnemyData(ModEnemyData enemy)
    {
        if (string.IsNullOrEmpty(enemy.enemyId) || string.IsNullOrEmpty(enemy.enemyName))
            return false;
        
        if (enemy.health <= 0 || enemy.damage < 0)
            return false;
        
        return true;
    }
    
    bool ValidatePropData(ModPrefabReference prop)
    {
        if (string.IsNullOrEmpty(prop.prefabId) || string.IsNullOrEmpty(prop.prefabName))
            return false;
        
        if (prop.isDestructible && prop.health <= 0)
            return false;
        
        return true;
    }
    
    bool ValidateEffectData(ModEffectData effect)
    {
        if (string.IsNullOrEmpty(effect.effectId) || string.IsNullOrEmpty(effect.effectName))
            return false;
        
        if (effect.duration < 0 || effect.intensity < 0)
            return false;
        
        return true;
    }
    
    bool IsTrustedMod(string author)
    {
        return trustedModAuthors.Contains(author);
    }
    
    // Public API
    public static bool IsModSupportEnabled()
    {
        return Instance.modSupportEnabled;
    }
    
    public static ModEnemyData GetCustomEnemy(string enemyId)
    {
        return Instance.customEnemies.ContainsKey(enemyId) ? Instance.customEnemies[enemyId] : null;
    }
    
    public static ModPrefabReference GetCustomProp(string propId)
    {
        return Instance.customProps.ContainsKey(propId) ? Instance.customProps[propId] : null;
    }
    
    public static ModEffectData GetCustomEffect(string effectId)
    {
        return Instance.customEffects.ContainsKey(effectId) ? Instance.customEffects[effectId] : null;
    }
    
    public static string[] GetCustomEnemyIds()
    {
        return Instance.customEnemies.Keys.ToArray();
    }
    
    public static string[] GetCustomPropIds()
    {
        return Instance.customProps.Keys.ToArray();
    }
    
    public static string[] GetCustomEffectIds()
    {
        return Instance.customEffects.Keys.ToArray();
    }
    
    public static void RegisterCustomEnemy(ModEnemyData enemy)
    {
        if (Instance.ValidateEnemyData(enemy))
        {
            Instance.customEnemies[enemy.enemyId] = enemy;
            Debug.Log($"Registered custom enemy: {enemy.enemyName}");
        }
    }
    
    public static void RegisterCustomProp(ModPrefabReference prop)
    {
        if (Instance.ValidatePropData(prop))
        {
            Instance.customProps[prop.prefabId] = prop;
            Debug.Log($"Registered custom prop: {prop.prefabName}");
        }
    }
    
    public static void RegisterCustomEffect(ModEffectData effect)
    {
        if (Instance.ValidateEffectData(effect))
        {
            Instance.customEffects[effect.effectId] = effect;
            Debug.Log($"Registered custom effect: {effect.effectName}");
        }
    }
    
    public static GameObject SpawnCustomEnemy(string enemyId, Vector3 position, Quaternion rotation)
    {
        var enemyData = GetCustomEnemy(enemyId);
        if (enemyData == null) return null;
        
        GameObject prefab = Resources.Load<GameObject>(enemyData.prefabPath);
        if (prefab == null) return null;
        
        GameObject enemy = Instantiate(prefab, position, rotation);
        
        // Apply custom stats
        var stats = enemy.GetComponent<EnemyStats>();
        if (stats != null)
        {
            stats.health = enemyData.health;
            stats.damage = enemyData.damage;
            stats.speed = enemyData.speed;
        }
        
        return enemy;
    }
    
    public static GameObject SpawnCustomProp(string propId, Vector3 position, Quaternion rotation)
    {
        var propData = GetCustomProp(propId);
        if (propData == null) return null;
        
        GameObject prefab = Resources.Load<GameObject>(propData.prefabPath);
        if (prefab == null) return null;
        
        GameObject prop = Instantiate(prefab, position, rotation);
        
        // Apply custom properties
        if (propData.isDestructible)
        {
            var destructible = prop.GetComponent<DestructibleObject>();
            if (destructible == null)
                destructible = prop.AddComponent<DestructibleObject>();
            
            destructible.maxHealth = propData.health;
            destructible.currentHealth = propData.health;
        }
        
        return prop;
    }
    
    // Arena extension support
    public static void ExtendArena(ArenaData arena, ModArenaExtension extension)
    {
        if (!Instance.IsTrustedMod(extension.modAuthor))
        {
            Debug.LogWarning($"Untrusted arena extension ignored: {extension.modAuthor}");
            return;
        }
        
        // Add custom enemies to spawn points
        foreach (string enemyId in extension.customEnemyIds)
        {
            if (Instance.customEnemies.ContainsKey(enemyId))
            {
                // Add spawn point for custom enemy
                arena.enemySpawnPoints.Add(Vector3.zero); // Would be positioned properly
            }
        }
        
        // Add custom props
        foreach (string propId in extension.customPropIds)
        {
            if (Instance.customProps.ContainsKey(propId))
            {
                var propData = Instance.customProps[propId];
                arena.staticProps.Add(new ArenaData.PropPlacement
                {
                    propId = propId,
                    propName = propData.prefabName,
                    position = Vector3.zero, // Would be positioned properly
                    rotation = Vector3.zero,
                    scale = Vector3.one,
                    isDestructible = propData.isDestructible,
                    health = propData.health
                });
            }
        }
        
        Debug.Log($"Extended arena {arena.arenaName} with mod content from {extension.modAuthor}");
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Handle game events that affect mod support
    }
}

// Mod interfaces for external mods
public interface IArenaModExtension
{
    ModInfo GetModInfo();
    void Initialize();
    void OnArenaLoaded(ArenaData arena);
    void OnArenaUnloaded(ArenaData arena);
}

public interface ICustomEnemyProvider
{
    CommunityModSupport.ModEnemyData[] GetCustomEnemies();
}

public interface ICustomPropProvider
{
    CommunityModSupport.ModPrefabReference[] GetCustomProps();
}

[System.Serializable]
public class ModInfo
{
    public string name;
    public string author;
    public string version;
    public string description;
    public string[] dependencies;
}
