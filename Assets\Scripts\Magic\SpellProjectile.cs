using UnityEngine;
using System.Collections.Generic;
using CinderOfDarkness.Magic;

namespace CinderOfDarkness.Magic
{
    /// <summary>
    /// Spell Projectile component for handling spell behavior and effects.
    /// Supports various spell traits and elemental interactions.
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class SpellProjectile : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Projectile Settings")]
        [SerializeField] private float speed = 10f;
        [SerializeField] private float lifetime = 5f;
        [SerializeField] private LayerMask targetLayers = -1;
        [SerializeField] private bool destroyOnImpact = true;

        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem trailEffect;
        [SerializeField] private GameObject impactEffect;
        [SerializeField] private GameObject explosionEffect;
        [SerializeField] private Light spellLight;

        [Header("Audio")]
        [SerializeField] private AudioClip launchSound;
        [SerializeField] private AudioClip impactSound;
        [SerializeField] private AudioClip explosionSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Public Properties
        public PlayerSpell SpellData { get; private set; }
        public Vector3 TargetPosition { get; private set; }
        public bool IsActive { get; private set; } = true;
        #endregion

        #region Private Fields
        private Rigidbody rb;
        private Collider spellCollider;
        private float startTime;
        private Vector3 direction;
        
        // Trait effects
        private bool hasBurnEffect = false;
        private float burnDamage = 0f;
        private bool hasPierceEffect = false;
        private float pierceCount = 0f;
        private bool hasExplosionEffect = false;
        private float explosionRadius = 0f;
        private bool hasBounceEffect = false;
        private int bounceCount = 0;
        private int maxBounces = 0;
        private bool hasHomingEffect = false;
        private float homingStrength = 0f;
        private bool hasMultiplyEffect = false;
        private int multiplyCount = 0;
        
        // Targeting
        private Transform homingTarget;
        private List<Collider> hitTargets = new List<Collider>();
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            spellCollider = GetComponent<Collider>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
        }

        private void Start()
        {
            startTime = Time.time;
            PlaySound(launchSound);
            
            if (spellLight != null)
            {
                UpdateSpellLight();
            }
        }

        private void Update()
        {
            if (!IsActive) return;

            UpdateMovement();
            UpdateHoming();
            CheckLifetime();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (!IsActive) return;
            if (!IsValidTarget(other)) return;
            if (hitTargets.Contains(other) && !hasPierceEffect) return;

            HandleImpact(other);
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize spell projectile with data.
        /// </summary>
        /// <param name="spellData">Spell data</param>
        /// <param name="targetPosition">Target position</param>
        public void Initialize(PlayerSpell spellData, Vector3 targetPosition)
        {
            SpellData = spellData;
            TargetPosition = targetPosition;
            
            // Calculate direction
            direction = (targetPosition - transform.position).normalized;
            
            // Set initial velocity
            if (rb != null)
            {
                rb.velocity = direction * speed;
            }
            
            // Face direction
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
            
            // Update visual effects
            UpdateVisualEffects();
        }

        /// <summary>
        /// Update visual effects based on spell data.
        /// </summary>
        private void UpdateVisualEffects()
        {
            if (SpellData?.spellData == null) return;

            // Update trail color based on element
            if (trailEffect != null)
            {
                var main = trailEffect.main;
                main.startColor = GetElementColor(SpellData.spellData.element);
            }

            // Update light color
            if (spellLight != null)
            {
                spellLight.color = GetElementColor(SpellData.spellData.element);
            }
        }

        /// <summary>
        /// Get color for element type.
        /// </summary>
        /// <param name="element">Element type</param>
        /// <returns>Element color</returns>
        private Color GetElementColor(ElementType element)
        {
            switch (element)
            {
                case ElementType.Fire: return Color.red;
                case ElementType.Water: return Color.blue;
                case ElementType.Earth: return Color.green;
                case ElementType.Air: return Color.cyan;
                case ElementType.Lightning: return Color.yellow;
                case ElementType.Ice: return Color.white;
                case ElementType.Shadow: return Color.black;
                case ElementType.Light: return Color.white;
                default: return Color.magenta;
            }
        }
        #endregion

        #region Movement and Targeting
        /// <summary>
        /// Update projectile movement.
        /// </summary>
        private void UpdateMovement()
        {
            if (rb == null) return;

            // Maintain speed
            if (rb.velocity.magnitude < speed * 0.9f)
            {
                rb.velocity = rb.velocity.normalized * speed;
            }
        }

        /// <summary>
        /// Update homing behavior.
        /// </summary>
        private void UpdateHoming()
        {
            if (!hasHomingEffect || rb == null) return;

            // Find or update homing target
            if (homingTarget == null)
            {
                FindHomingTarget();
            }

            if (homingTarget != null)
            {
                Vector3 targetDirection = (homingTarget.position - transform.position).normalized;
                Vector3 currentDirection = rb.velocity.normalized;
                
                Vector3 newDirection = Vector3.Slerp(currentDirection, targetDirection, homingStrength * Time.deltaTime);
                rb.velocity = newDirection * speed;
                
                transform.rotation = Quaternion.LookRotation(newDirection);
            }
        }

        /// <summary>
        /// Find nearest valid homing target.
        /// </summary>
        private void FindHomingTarget()
        {
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, 15f, targetLayers);
            float nearestDistance = float.MaxValue;
            Transform nearestTarget = null;

            foreach (var collider in nearbyColliders)
            {
                if (!IsValidTarget(collider)) continue;
                if (hitTargets.Contains(collider)) continue;

                float distance = Vector3.Distance(transform.position, collider.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = collider.transform;
                }
            }

            homingTarget = nearestTarget;
        }

        /// <summary>
        /// Check if target is valid for this spell.
        /// </summary>
        /// <param name="target">Target collider</param>
        /// <returns>True if valid target</returns>
        private bool IsValidTarget(Collider target)
        {
            // Check layer mask
            if ((targetLayers.value & (1 << target.gameObject.layer)) == 0) return false;
            
            // Don't hit the caster
            if (target.CompareTag("Player")) return false;
            
            return true;
        }
        #endregion

        #region Impact and Effects
        /// <summary>
        /// Handle projectile impact.
        /// </summary>
        /// <param name="target">Target collider</param>
        private void HandleImpact(Collider target)
        {
            // Add to hit targets
            if (!hitTargets.Contains(target))
            {
                hitTargets.Add(target);
            }

            // Apply damage
            ApplyDamage(target);

            // Apply trait effects
            ApplyTraitEffects(target);

            // Visual and audio effects
            CreateImpactEffect();
            PlaySound(impactSound);

            // Handle pierce effect
            if (hasPierceEffect && pierceCount > 0)
            {
                pierceCount--;
                return; // Don't destroy projectile
            }

            // Handle bounce effect
            if (hasBounceEffect && bounceCount < maxBounces)
            {
                HandleBounce(target);
                return;
            }

            // Handle multiply effect
            if (hasMultiplyEffect && multiplyCount > 0)
            {
                CreateMultipleProjectiles();
            }

            // Destroy projectile
            if (destroyOnImpact)
            {
                DestroyProjectile();
            }
        }

        /// <summary>
        /// Apply damage to target.
        /// </summary>
        /// <param name="target">Target collider</param>
        private void ApplyDamage(Collider target)
        {
            if (SpellData?.spellData == null) return;

            float damage = SpellData.spellData.damage * (1f + SpellData.level * 0.1f);
            
            // Apply damage to target
            var health = target.GetComponent<Health>();
            if (health != null)
            {
                health.TakeDamage(damage);
            }

            // Apply burn damage over time
            if (hasBurnEffect)
            {
                var burnEffect = target.GetComponent<BurnEffect>();
                if (burnEffect == null)
                {
                    burnEffect = target.gameObject.AddComponent<BurnEffect>();
                }
                burnEffect.ApplyBurn(burnDamage, 3f); // 3 second burn
            }
        }

        /// <summary>
        /// Apply trait-specific effects.
        /// </summary>
        /// <param name="target">Target collider</param>
        private void ApplyTraitEffects(Collider target)
        {
            // Explosion effect
            if (hasExplosionEffect)
            {
                CreateExplosion();
            }
        }

        /// <summary>
        /// Create explosion effect.
        /// </summary>
        private void CreateExplosion()
        {
            // Visual effect
            if (explosionEffect != null)
            {
                Instantiate(explosionEffect, transform.position, Quaternion.identity);
            }

            // Audio effect
            PlaySound(explosionSound);

            // Damage nearby enemies
            Collider[] nearbyTargets = Physics.OverlapSphere(transform.position, explosionRadius, targetLayers);
            foreach (var target in nearbyTargets)
            {
                if (IsValidTarget(target))
                {
                    ApplyDamage(target);
                }
            }
        }

        /// <summary>
        /// Handle bounce effect.
        /// </summary>
        /// <param name="target">Target that was hit</param>
        private void HandleBounce(Collider target)
        {
            bounceCount++;
            
            // Find new target for bounce
            Collider[] nearbyTargets = Physics.OverlapSphere(transform.position, 10f, targetLayers);
            Transform bounceTarget = null;
            float nearestDistance = float.MaxValue;

            foreach (var nearbyTarget in nearbyTargets)
            {
                if (nearbyTarget == target) continue;
                if (!IsValidTarget(nearbyTarget)) continue;
                if (hitTargets.Contains(nearbyTarget)) continue;

                float distance = Vector3.Distance(transform.position, nearbyTarget.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    bounceTarget = nearbyTarget.transform;
                }
            }

            if (bounceTarget != null)
            {
                // Redirect projectile
                Vector3 newDirection = (bounceTarget.position - transform.position).normalized;
                rb.velocity = newDirection * speed;
                transform.rotation = Quaternion.LookRotation(newDirection);
                
                TargetPosition = bounceTarget.position;
            }
            else
            {
                // No valid bounce target, destroy projectile
                DestroyProjectile();
            }
        }

        /// <summary>
        /// Create multiple projectiles for multiply effect.
        /// </summary>
        private void CreateMultipleProjectiles()
        {
            for (int i = 0; i < multiplyCount; i++)
            {
                // Create new projectile
                GameObject newProjectile = Instantiate(gameObject, transform.position, transform.rotation);
                var newSpellProjectile = newProjectile.GetComponent<SpellProjectile>();
                
                if (newSpellProjectile != null)
                {
                    // Random direction spread
                    Vector3 randomDirection = direction + Random.insideUnitSphere * 0.3f;
                    Vector3 newTargetPosition = transform.position + randomDirection.normalized * 20f;
                    
                    newSpellProjectile.Initialize(SpellData, newTargetPosition);
                    
                    // Remove multiply effect from new projectiles to prevent infinite multiplication
                    newSpellProjectile.hasMultiplyEffect = false;
                }
            }
        }

        /// <summary>
        /// Create impact visual effect.
        /// </summary>
        private void CreateImpactEffect()
        {
            if (impactEffect != null)
            {
                Instantiate(impactEffect, transform.position, Quaternion.identity);
            }
        }
        #endregion

        #region Trait Application
        /// <summary>
        /// Add burn effect trait.
        /// </summary>
        /// <param name="damage">Burn damage per second</param>
        public void AddBurnEffect(float damage)
        {
            hasBurnEffect = true;
            burnDamage = damage;
        }

        /// <summary>
        /// Add pierce effect trait.
        /// </summary>
        /// <param name="pierceAmount">Number of targets to pierce</param>
        public void AddPierceEffect(float pierceAmount)
        {
            hasPierceEffect = true;
            pierceCount = pierceAmount;
        }

        /// <summary>
        /// Add explosion effect trait.
        /// </summary>
        /// <param name="radius">Explosion radius</param>
        public void AddExplosionEffect(float radius)
        {
            hasExplosionEffect = true;
            explosionRadius = radius;
        }

        /// <summary>
        /// Add bounce effect trait.
        /// </summary>
        /// <param name="bounces">Number of bounces</param>
        public void AddBounceEffect(int bounces)
        {
            hasBounceEffect = true;
            maxBounces = bounces;
        }

        /// <summary>
        /// Add homing effect trait.
        /// </summary>
        /// <param name="strength">Homing strength</param>
        public void AddHomingEffect(float strength)
        {
            hasHomingEffect = true;
            homingStrength = strength;
        }

        /// <summary>
        /// Add multiply effect trait.
        /// </summary>
        /// <param name="count">Number of additional projectiles</param>
        public void AddMultiplyEffect(int count)
        {
            hasMultiplyEffect = true;
            multiplyCount = count;
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Check projectile lifetime and destroy if expired.
        /// </summary>
        private void CheckLifetime()
        {
            if (Time.time - startTime > lifetime)
            {
                DestroyProjectile();
            }
        }

        /// <summary>
        /// Update spell light intensity.
        /// </summary>
        private void UpdateSpellLight()
        {
            if (spellLight == null) return;

            float intensity = Mathf.Lerp(2f, 0.5f, (Time.time - startTime) / lifetime);
            spellLight.intensity = intensity;
        }

        /// <summary>
        /// Play audio clip.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }

        /// <summary>
        /// Destroy projectile with effects.
        /// </summary>
        private void DestroyProjectile()
        {
            IsActive = false;
            
            // Stop trail effect
            if (trailEffect != null)
            {
                trailEffect.Stop();
            }

            // Destroy after a short delay to allow effects to finish
            Destroy(gameObject, 1f);
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            // Draw explosion radius
            if (hasExplosionEffect)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position, explosionRadius);
            }

            // Draw homing range
            if (hasHomingEffect)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position, 15f);
            }

            // Draw target line
            if (Application.isPlaying && homingTarget != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(transform.position, homingTarget.position);
            }
        }
        #endregion
    }
}
