using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class SymbolicOpeningSequence : MonoBehaviour
{
    [Header("Opening Dream Configuration")]
    public bool isOpeningSequence = true;
    public float sequenceDuration = 180f; // 3 minutes
    public OpeningChoice[] philosophicalChoices;
    public VisionFlash[] futureVisions;
    
    [Header("Visual Elements")]
    public GameObject voidEnvironment;
    public ParticleSystem smokeEffect;
    public ParticleSystem emberEffect;
    public Light dreamLight;
    public AudioSource mysticalAudio;
    
    [Header("Audio")]
    public AudioClip voidAmbience;
    public AudioClip mysticalVoice;
    public AudioClip[] ancientMelodies;
    public AudioClip awakeningSound;
    
    [Header("Emotional Tone Setting")]
    public EmotionalTone selectedTone = EmotionalTone.Undefined;
    public float toneInfluence = 0f;
    
    private PsychologicalSystem psycheSystem;
    private PhilosophicalMoralitySystem moralitySystem;
    private DynamicMusicalSystem musicalSystem;
    private GameManager gameManager;
    private bool sequenceCompleted = false;
    
    public enum EmotionalTone
    {
        Undefined,
        Sorrowful,      // "I burn because I have lost everything"
        Defiant,        // "I burn because the world deserves my rage"
        Hopeful,        // "I burn to light the way for others"
        Nihilistic,     // "I burn because burning is all that remains"
        Protective,     // "I burn to shield others from the cold"
        Curious         // "I burn to understand what fire truly means"
    }
    
    [System.Serializable]
    public class OpeningChoice
    {
        [Header("Choice Identity")]
        public string choiceText;
        public EmotionalTone associatedTone;
        public string symbolicMeaning;
        
        [Header("Visual Response")]
        public Color flameColor;
        public float intensityModifier;
        public ParticleSystem.MainModule particleResponse;
        
        [Header("Audio Response")]
        public AudioClip responseSound;
        public float pitchModification;
        
        [Header("Philosophical Weight")]
        public float existentialWeight;
        public string[] impliedBeliefs;
        public float futureInfluence;
    }
    
    [System.Serializable]
    public class VisionFlash
    {
        [Header("Vision Content")]
        public string visionName;
        public VisionType type;
        public float duration;
        public string symbolicDescription;
        
        [Header("Visual Elements")]
        public Texture2D visionTexture;
        public Color overlayColor;
        public float intensity;
        
        [Header("Audio")]
        public AudioClip visionSound;
        public bool useDistortedAudio;
        
        [Header("Emotional Impact")]
        public EmotionalTone resonantTone;
        public float psychologicalWeight;
        
        public enum VisionType
        {
            War,           // Flashes of battle and destruction
            Peace,         // Moments of tranquility and healing
            Abandonment,   // Solitude and rejection
            Redemption,    // Forgiveness and second chances
            Transformation, // Change and growth
            Void           // Emptiness and existential dread
        }
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        musicalSystem = GetComponent<DynamicMusicalSystem>();
        gameManager = GameManager.Instance;
        
        if (isOpeningSequence && !HasPlayedOpening())
        {
            InitializeOpeningSequence();
        }
    }
    
    bool HasPlayedOpening()
    {
        return PlayerPrefs.GetInt("OpeningSequencePlayed", 0) == 1;
    }
    
    void InitializeOpeningSequence()
    {
        // Initialize philosophical choices
        InitializePhilosophicalChoices();
        
        // Initialize future visions
        InitializeFutureVisions();
        
        // Begin the opening sequence
        StartCoroutine(OpeningSequence());
        
        Debug.Log("Symbolic Opening Sequence initialized - the dream begins");
    }
    
    void InitializePhilosophicalChoices()
    {
        philosophicalChoices = new OpeningChoice[]
        {
            new OpeningChoice
            {
                choiceText = "I burn because I have lost everything, and only fire remains to warm the void within me.",
                associatedTone = EmotionalTone.Sorrowful,
                symbolicMeaning = "The flame of grief - burning to fill the emptiness left by loss",
                flameColor = new Color(0.4f, 0.6f, 0.9f), // Cold blue flame
                intensityModifier = 0.7f,
                existentialWeight = 75f,
                impliedBeliefs = new string[] { "Loss defines us", "Pain has meaning", "Memory is sacred" },
                futureInfluence = 15f
            },
            new OpeningChoice
            {
                choiceText = "I burn because the world deserves my rage, and fire is the only truth it understands.",
                associatedTone = EmotionalTone.Defiant,
                symbolicMeaning = "The flame of wrath - burning to cleanse corruption through destruction",
                flameColor = new Color(1f, 0.3f, 0.1f), // Angry red flame
                intensityModifier = 1.3f,
                existentialWeight = 85f,
                impliedBeliefs = new string[] { "Justice through fire", "Strength conquers all", "The world must change" },
                futureInfluence = 20f
            },
            new OpeningChoice
            {
                choiceText = "I burn to light the way for others, so they need not walk in darkness as I have.",
                associatedTone = EmotionalTone.Hopeful,
                symbolicMeaning = "The flame of guidance - burning to illuminate paths for others",
                flameColor = new Color(1f, 0.9f, 0.6f), // Warm golden flame
                intensityModifier = 1.1f,
                existentialWeight = 90f,
                impliedBeliefs = new string[] { "Others matter more", "Hope endures", "Light conquers darkness" },
                futureInfluence = 25f
            },
            new OpeningChoice
            {
                choiceText = "I burn because burning is all that remains when meaning has turned to ash.",
                associatedTone = EmotionalTone.Nihilistic,
                symbolicMeaning = "The flame of emptiness - burning without purpose or direction",
                flameColor = new Color(0.3f, 0.3f, 0.3f), // Ashen gray flame
                intensityModifier = 0.5f,
                existentialWeight = 60f,
                impliedBeliefs = new string[] { "Nothing matters", "Existence is suffering", "Purpose is illusion" },
                futureInfluence = 10f
            },
            new OpeningChoice
            {
                choiceText = "I burn to shield others from the cold, even if it consumes me completely.",
                associatedTone = EmotionalTone.Protective,
                symbolicMeaning = "The flame of sacrifice - burning to protect others from harm",
                flameColor = new Color(0.9f, 0.7f, 0.4f), // Protective amber flame
                intensityModifier = 0.9f,
                existentialWeight = 95f,
                impliedBeliefs = new string[] { "Sacrifice is noble", "Others before self", "Love transcends death" },
                futureInfluence = 30f
            },
            new OpeningChoice
            {
                choiceText = "I burn to understand what fire truly means, and what it means to be flame itself.",
                associatedTone = EmotionalTone.Curious,
                symbolicMeaning = "The flame of wisdom - burning to discover truth and understanding",
                flameColor = new Color(0.8f, 0.4f, 0.9f), // Mystical purple flame
                intensityModifier = 1.0f,
                existentialWeight = 80f,
                impliedBeliefs = new string[] { "Knowledge is power", "Understanding brings peace", "Truth exists" },
                futureInfluence = 20f
            }
        };
    }
    
    void InitializeFutureVisions()
    {
        futureVisions = new VisionFlash[]
        {
            new VisionFlash
            {
                visionName = "The War Vision",
                type = VisionFlash.VisionType.War,
                duration = 2f,
                symbolicDescription = "Flashes of endless battle, blood on ash, the weight of necessary violence",
                overlayColor = new Color(0.8f, 0.2f, 0.2f, 0.7f),
                intensity = 0.9f,
                useDistortedAudio = true,
                psychologicalWeight = 25f
            },
            new VisionFlash
            {
                visionName = "The Peace Vision",
                type = VisionFlash.VisionType.Peace,
                duration = 3f,
                symbolicDescription = "Moments of tranquility, children laughing, the possibility of healing",
                overlayColor = new Color(0.6f, 0.8f, 0.4f, 0.5f),
                intensity = 0.6f,
                useDistortedAudio = false,
                psychologicalWeight = 20f
            },
            new VisionFlash
            {
                visionName = "The Abandonment Vision",
                type = VisionFlash.VisionType.Abandonment,
                duration = 2.5f,
                symbolicDescription = "Standing alone as others turn away, the weight of isolation",
                overlayColor = new Color(0.3f, 0.3f, 0.5f, 0.8f),
                intensity = 0.7f,
                useDistortedAudio = true,
                psychologicalWeight = 30f
            },
            new VisionFlash
            {
                visionName = "The Redemption Vision",
                type = VisionFlash.VisionType.Redemption,
                duration = 2.8f,
                symbolicDescription = "Forgiveness offered and received, the possibility of second chances",
                overlayColor = new Color(0.9f, 0.8f, 0.6f, 0.6f),
                intensity = 0.8f,
                useDistortedAudio = false,
                psychologicalWeight = 35f
            },
            new VisionFlash
            {
                visionName = "The Transformation Vision",
                type = VisionFlash.VisionType.Transformation,
                duration = 3.2f,
                symbolicDescription = "The self changing, growing, becoming something new and unknown",
                overlayColor = new Color(0.7f, 0.4f, 0.8f, 0.7f),
                intensity = 0.9f,
                useDistortedAudio = true,
                psychologicalWeight = 40f
            },
            new VisionFlash
            {
                visionName = "The Void Vision",
                type = VisionFlash.VisionType.Void,
                duration = 1.5f,
                symbolicDescription = "Emptiness beyond comprehension, the fear of meaninglessness",
                overlayColor = new Color(0.1f, 0.1f, 0.1f, 0.9f),
                intensity = 1f,
                useDistortedAudio = true,
                psychologicalWeight = 45f
            }
        };
    }
    
    IEnumerator OpeningSequence()
    {
        // Disable player control
        DisablePlayerControl();
        
        // Phase 1: Awakening in the Void
        yield return StartCoroutine(AwakeningPhase());
        
        // Phase 2: The Philosophical Question
        yield return StartCoroutine(PhilosophicalQuestionPhase());
        
        // Phase 3: Future Visions
        yield return StartCoroutine(FutureVisionsPhase());
        
        // Phase 4: The Desolate Land
        yield return StartCoroutine(DesolateLandPhase());
        
        // Phase 5: Awakening to Reality
        yield return StartCoroutine(AwakeningToRealityPhase());
        
        // Complete sequence
        CompleteOpeningSequence();
    }
    
    IEnumerator AwakeningPhase()
    {
        ShowOpeningMessage("In the beginning, there was only smoke and ember...");
        
        // Activate void environment
        if (voidEnvironment != null)
        {
            voidEnvironment.SetActive(true);
        }
        
        // Start ambient effects
        if (smokeEffect != null)
        {
            smokeEffect.Play();
        }
        
        if (emberEffect != null)
        {
            emberEffect.Play();
        }
        
        // Play void ambience
        if (voidAmbience != null && mysticalAudio != null)
        {
            mysticalAudio.clip = voidAmbience;
            mysticalAudio.Play();
        }
        
        yield return new WaitForSeconds(5f);
        
        ShowOpeningMessage("You feel consciousness returning, like embers stirring in cold ash...");
        yield return new WaitForSeconds(4f);
        
        ShowOpeningMessage("The void around you pulses with ancient memory...");
        yield return new WaitForSeconds(3f);
    }
    
    IEnumerator PhilosophicalQuestionPhase()
    {
        // The mystical voice speaks
        if (mysticalVoice != null && mysticalAudio != null)
        {
            mysticalAudio.clip = mysticalVoice;
            mysticalAudio.Play();
        }
        
        ShowOpeningMessage("A voice echoes from everywhere and nowhere:");
        yield return new WaitForSeconds(2f);
        
        ShowOpeningMessage("\"Why do you burn?\"");
        yield return new WaitForSeconds(3f);
        
        ShowOpeningMessage("The question hangs in the void, demanding an answer from the depths of your being...");
        yield return new WaitForSeconds(3f);
        
        // Present philosophical choices
        yield return StartCoroutine(PresentPhilosophicalChoices());
    }
    
    IEnumerator PresentPhilosophicalChoices()
    {
        ShowOpeningMessage("Choose the truth that resonates in your ember-heart:");
        yield return new WaitForSeconds(2f);
        
        // Display choices (this would integrate with the UI system)
        for (int i = 0; i < philosophicalChoices.Length; i++)
        {
            ShowOpeningMessage($"{i + 1}. {philosophicalChoices[i].choiceText}");
            yield return new WaitForSeconds(1f);
        }
        
        // Wait for player choice (simulated for now)
        yield return new WaitForSeconds(5f);
        
        // For demonstration, randomly select a choice
        int selectedChoice = Random.Range(0, philosophicalChoices.Length);
        ProcessPhilosophicalChoice(selectedChoice);
        
        yield return new WaitForSeconds(3f);
    }
    
    void ProcessPhilosophicalChoice(int choiceIndex)
    {
        if (choiceIndex < 0 || choiceIndex >= philosophicalChoices.Length) return;
        
        OpeningChoice choice = philosophicalChoices[choiceIndex];
        selectedTone = choice.associatedTone;
        toneInfluence = choice.futureInfluence;
        
        // Apply visual response
        ApplyChoiceVisualResponse(choice);
        
        // Apply audio response
        if (choice.responseSound != null && mysticalAudio != null)
        {
            mysticalAudio.clip = choice.responseSound;
            mysticalAudio.pitch = choice.pitchModification;
            mysticalAudio.Play();
        }
        
        // Set emotional tone for the game
        SetEmotionalTone(choice);
        
        ShowOpeningMessage($"Your flame burns with the essence of {choice.symbolicMeaning}...");
        
        Debug.Log($"Player chose: {choice.associatedTone} - {choice.symbolicMeaning}");
    }
    
    void ApplyChoiceVisualResponse(OpeningChoice choice)
    {
        // Modify flame color and intensity
        if (dreamLight != null)
        {
            dreamLight.color = choice.flameColor;
            dreamLight.intensity = choice.intensityModifier;
        }
        
        // Modify particle effects
        if (emberEffect != null)
        {
            var main = emberEffect.main;
            main.startColor = choice.flameColor;
        }
    }
    
    void SetEmotionalTone(OpeningChoice choice)
    {
        // Apply to psychological system
        if (psycheSystem != null)
        {
            psycheSystem.SetInitialEmotionalTone(choice.associatedTone.ToString(), choice.existentialWeight);
        }
        
        // Apply to morality system
        if (moralitySystem != null)
        {
            moralitySystem.SetPhilosophicalFoundation(choice.impliedBeliefs, choice.futureInfluence);
        }
        
        // Apply to musical system
        if (musicalSystem != null)
        {
            musicalSystem.SetEmotionalTone(choice.associatedTone.ToString());
        }
    }
    
    IEnumerator FutureVisionsPhase()
    {
        ShowOpeningMessage("The void responds to your truth, showing glimpses of what may come...");
        yield return new WaitForSeconds(3f);
        
        // Show visions based on selected tone
        VisionFlash[] relevantVisions = GetRelevantVisions();
        
        foreach (VisionFlash vision in relevantVisions)
        {
            yield return StartCoroutine(PlayVision(vision));
            yield return new WaitForSeconds(1f);
        }
        
        ShowOpeningMessage("The visions fade, but their weight remains...");
        yield return new WaitForSeconds(3f);
    }
    
    VisionFlash[] GetRelevantVisions()
    {
        List<VisionFlash> relevant = new List<VisionFlash>();
        
        // Always show 3-4 visions, selected based on emotional tone
        switch (selectedTone)
        {
            case EmotionalTone.Sorrowful:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.Abandonment),
                    GetVisionByType(VisionFlash.VisionType.Peace),
                    GetVisionByType(VisionFlash.VisionType.Redemption)
                });
                break;
            
            case EmotionalTone.Defiant:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.War),
                    GetVisionByType(VisionFlash.VisionType.Transformation),
                    GetVisionByType(VisionFlash.VisionType.Void)
                });
                break;
            
            case EmotionalTone.Hopeful:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.Peace),
                    GetVisionByType(VisionFlash.VisionType.Redemption),
                    GetVisionByType(VisionFlash.VisionType.Transformation)
                });
                break;
            
            case EmotionalTone.Nihilistic:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.Void),
                    GetVisionByType(VisionFlash.VisionType.Abandonment),
                    GetVisionByType(VisionFlash.VisionType.War)
                });
                break;
            
            case EmotionalTone.Protective:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.Peace),
                    GetVisionByType(VisionFlash.VisionType.War),
                    GetVisionByType(VisionFlash.VisionType.Redemption)
                });
                break;
            
            case EmotionalTone.Curious:
                relevant.AddRange(new VisionFlash[] { 
                    GetVisionByType(VisionFlash.VisionType.Transformation),
                    GetVisionByType(VisionFlash.VisionType.Void),
                    GetVisionByType(VisionFlash.VisionType.Peace)
                });
                break;
        }
        
        return relevant.ToArray();
    }
    
    VisionFlash GetVisionByType(VisionFlash.VisionType type)
    {
        foreach (VisionFlash vision in futureVisions)
        {
            if (vision.type == type)
                return vision;
        }
        return futureVisions[0]; // Fallback
    }
    
    IEnumerator PlayVision(VisionFlash vision)
    {
        ShowOpeningMessage($"Vision: {vision.symbolicDescription}");
        
        // Apply visual overlay
        ApplyVisionOverlay(vision);
        
        // Play vision sound
        if (vision.visionSound != null && mysticalAudio != null)
        {
            mysticalAudio.clip = vision.visionSound;
            if (vision.useDistortedAudio)
            {
                mysticalAudio.pitch = Random.Range(0.7f, 1.3f);
            }
            mysticalAudio.Play();
        }
        
        yield return new WaitForSeconds(vision.duration);
        
        // Apply psychological impact
        if (psycheSystem != null)
        {
            psycheSystem.AddVisionMemory(vision.visionName, vision.psychologicalWeight);
        }
        
        // Clear overlay
        ClearVisionOverlay();
    }
    
    void ApplyVisionOverlay(VisionFlash vision)
    {
        // Apply color overlay and visual effects
        if (dreamLight != null)
        {
            dreamLight.color = Color.Lerp(dreamLight.color, vision.overlayColor, 0.7f);
            dreamLight.intensity = vision.intensity;
        }
    }
    
    void ClearVisionOverlay()
    {
        // Return to normal lighting
        if (dreamLight != null)
        {
            dreamLight.color = Color.white;
            dreamLight.intensity = 1f;
        }
    }
    
    IEnumerator DesolateLandPhase()
    {
        ShowOpeningMessage("The visions fade, and you find yourself standing...");
        yield return new WaitForSeconds(3f);
        
        ShowOpeningMessage("Before a vast, lifeless land stretching to the horizon...");
        yield return new WaitForSeconds(4f);
        
        ShowOpeningMessage("Ash falls like snow. The air tastes of endings and beginnings.");
        yield return new WaitForSeconds(4f);
        
        ShowOpeningMessage("You are alone. You have always been alone.");
        yield return new WaitForSeconds(3f);
        
        ShowOpeningMessage("But your flame burns on...");
        yield return new WaitForSeconds(3f);
    }
    
    IEnumerator AwakeningToRealityPhase()
    {
        ShowOpeningMessage("The dream begins to fade...");
        yield return new WaitForSeconds(2f);
        
        // Play awakening sound
        if (awakeningSound != null && mysticalAudio != null)
        {
            mysticalAudio.clip = awakeningSound;
            mysticalAudio.Play();
        }
        
        ShowOpeningMessage("You awaken to the weight of ash on your skin and the taste of smoke in your lungs.");
        yield return new WaitForSeconds(4f);
        
        ShowOpeningMessage("The dream was not a dream. The choice was not a choice.");
        yield return new WaitForSeconds(3f);
        
        ShowOpeningMessage("You are The Cinderborn. Your journey begins now.");
        yield return new WaitForSeconds(3f);
        
        // Fade out void environment
        if (voidEnvironment != null)
        {
            StartCoroutine(FadeOutVoidEnvironment());
        }
    }
    
    IEnumerator FadeOutVoidEnvironment()
    {
        float fadeTime = 3f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Lerp(1f, 0f, elapsed / fadeTime);
            
            // Fade out effects
            if (smokeEffect != null)
            {
                var main = smokeEffect.main;
                var startColor = main.startColor;
                startColor.color = new Color(startColor.color.r, startColor.color.g, startColor.color.b, alpha);
                main.startColor = startColor;
            }
            
            if (dreamLight != null)
            {
                dreamLight.intensity = Mathf.Lerp(1f, 0f, elapsed / fadeTime);
            }
            
            yield return null;
        }
        
        voidEnvironment.SetActive(false);
    }
    
    void CompleteOpeningSequence()
    {
        sequenceCompleted = true;
        
        // Mark as played
        PlayerPrefs.SetInt("OpeningSequencePlayed", 1);
        PlayerPrefs.Save();
        
        // Enable player control
        EnablePlayerControl();
        
        // Apply lasting effects of the opening choice
        ApplyOpeningChoiceEffects();
        
        Debug.Log($"Opening sequence completed with tone: {selectedTone}");
    }
    
    void ApplyOpeningChoiceEffects()
    {
        // Apply subtle but lasting effects based on the chosen emotional tone
        if (gameManager != null)
        {
            gameManager.SetOpeningTone(selectedTone.ToString(), toneInfluence);
        }
        
        // Unlock special dialogue options based on tone
        UnlockToneSpecificDialogue();
        
        // Set initial psychological state
        if (psycheSystem != null)
        {
            psycheSystem.SetOpeningInfluence(selectedTone.ToString(), toneInfluence);
        }
    }
    
    void UnlockToneSpecificDialogue()
    {
        string dialogueKey = $"OpeningTone_{selectedTone}";
        gameManager?.UnlockDialogueOption(dialogueKey);
        
        // Unlock tone-specific insights
        switch (selectedTone)
        {
            case EmotionalTone.Sorrowful:
                gameManager?.UnlockDialogueOption("SorrowfulWisdom");
                break;
            case EmotionalTone.Defiant:
                gameManager?.UnlockDialogueOption("DefiantStrength");
                break;
            case EmotionalTone.Hopeful:
                gameManager?.UnlockDialogueOption("HopefulGuidance");
                break;
            case EmotionalTone.Nihilistic:
                gameManager?.UnlockDialogueOption("NihilisticTruth");
                break;
            case EmotionalTone.Protective:
                gameManager?.UnlockDialogueOption("ProtectiveInstinct");
                break;
            case EmotionalTone.Curious:
                gameManager?.UnlockDialogueOption("CuriousInquiry");
                break;
        }
    }
    
    void DisablePlayerControl()
    {
        // Disable player movement and input
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.enabled = false;
        }
        
        // Hide UI elements
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.SetUIVisibility(false);
        }
    }
    
    void EnablePlayerControl()
    {
        // Re-enable player movement and input
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.enabled = true;
        }
        
        // Show UI elements
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.SetUIVisibility(true);
        }
    }
    
    void ShowOpeningMessage(string message)
    {
        // Display message with minimal UI interference
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowNarrativeText(message);
        }
        
        Debug.Log($"Opening Sequence: {message}");
    }
    
    // Getters
    public EmotionalTone GetSelectedTone() => selectedTone;
    public float GetToneInfluence() => toneInfluence;
    public bool IsSequenceCompleted() => sequenceCompleted;
    public OpeningChoice GetSelectedChoice()
    {
        foreach (var choice in philosophicalChoices)
        {
            if (choice.associatedTone == selectedTone)
                return choice;
        }
        return null;
    }
}
