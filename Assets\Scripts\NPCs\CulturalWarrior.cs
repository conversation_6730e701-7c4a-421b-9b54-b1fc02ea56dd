using UnityEngine;
using System.Collections.Generic;

public class CulturalWarrior : MonoBehaviour
{
    [Header("Cultural Identity")]
    public WarriorCulture culture = WarriorCulture.Samurai;
    public string warriorName = "";
    public string culturalTitle = "";
    public string nativeLanguageGreeting = "";
    
    [Header("Appearance")]
    public GameObject[] culturalArmor;
    public GameObject[] culturalWeapons;
    public Material[] culturalMaterials;
    public Color[] culturalColors;
    
    [Header("Combat Style")]
    public CombatTechnique[] culturalTechniques;
    public float honorLevel = 50f; // 0-100, affects behavior
    public bool followsHonorCode = true;
    
    [Header("Voice & Language")]
    public AudioClip[] nativeLanguageClips;
    public AudioClip[] translatedClips;
    public AudioClip[] battleCries;
    public float accentStrength = 0.7f;
    
    [Header("Cultural Behavior")]
    public CulturalTradition[] traditions;
    public string[] culturalValues;
    public ReactionToPlayer playerReaction = ReactionToPlayer.Neutral;
    
    private NPCController npcController;
    private EnemyA<PERSON> enemyAI;
    private AudioSource voiceSource;
    private Animator warriorAnimator;
    
    public enum WarriorCulture
    {
        Samurai,        // Japanese honor-bound warriors
        <PERSON><PERSON><PERSON>,        // Arab knights and warriors
        Crusader,       // European holy warriors
        Viking,         // Norse raiders and warriors
        Mamluk,         // Elite Arab slave-soldiers
        Byzantine,      // Eastern Roman warriors
        Mongol,         // Steppe horse warriors
        Celtic,         // Ancient European tribal warriors
        Persian,        // Ancient Iranian warriors
        Nubian          // African kingdom warriors
    }
    
    public enum ReactionToPlayer
    {
        Hostile,        // Attacks on sight
        Suspicious,     // Wary but not immediately aggressive
        Neutral,        // Indifferent
        Curious,        // Interested in player's heritage
        Respectful,     // Honors player's mixed heritage
        Protective      // Wants to help/mentor player
    }
    
    [System.Serializable]
    public class CombatTechnique
    {
        public string techniqueName;
        public string nativeName;
        public float damage;
        public float speed;
        public bool requiresHonor;
        public AnimationClip animation;
        public AudioClip battleCry;
    }
    
    [System.Serializable]
    public class CulturalTradition
    {
        public string traditionName;
        public string description;
        public bool affectsDialogue;
        public bool affectsCombat;
        public string[] triggerConditions;
    }
    
    void Start()
    {
        npcController = GetComponent<NPCController>();
        enemyAI = GetComponent<EnemyAI>();
        voiceSource = GetComponent<AudioSource>();
        warriorAnimator = GetComponent<Animator>();
        
        InitializeCulturalIdentity();
        SetupCulturalAppearance();
        ConfigureCulturalBehavior();
        
        // Subscribe to player events
        PlayerStats playerStats = FindObjectOfType<PlayerStats>();
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }
    }
    
    void InitializeCulturalIdentity()
    {
        switch (culture)
        {
            case WarriorCulture.Samurai:
                InitializeSamurai();
                break;
            case WarriorCulture.Saracen:
                InitializeSaracen();
                break;
            case WarriorCulture.Crusader:
                InitializeCrusader();
                break;
            case WarriorCulture.Viking:
                InitializeViking();
                break;
            case WarriorCulture.Mamluk:
                InitializeMamluk();
                break;
            case WarriorCulture.Byzantine:
                InitializeByzantine();
                break;
            case WarriorCulture.Mongol:
                InitializeMongol();
                break;
            case WarriorCulture.Celtic:
                InitializeCeltic();
                break;
            case WarriorCulture.Persian:
                InitializePersian();
                break;
            case WarriorCulture.Nubian:
                InitializeNubian();
                break;
        }
        
        Debug.Log($"Initialized {culture} warrior: {warriorName} ({culturalTitle})");
    }
    
    void InitializeSamurai()
    {
        warriorName = GetRandomName(new string[] { "Takeshi", "Hiroshi", "Kenji", "Akira", "Nobunaga" });
        culturalTitle = "Samurai";
        nativeLanguageGreeting = "Konnichiwa, tabibito"; // Hello, traveler
        honorLevel = 90f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Honor", "Loyalty", "Discipline", "Respect for ancestors" };
        
        // Determine reaction based on player's behavior
        CharacterProgression playerProgression = FindObjectOfType<CharacterProgression>();
        if (playerProgression != null)
        {
            float culturalBalance = playerProgression.GetCulturalBalance();
            // Samurai respect discipline regardless of heritage
            playerReaction = ReactionToPlayer.Respectful;
        }
    }
    
    void InitializeSaracen()
    {
        warriorName = GetRandomName(new string[] { "Saladin", "Hakim", "Rashid", "Yusuf", "Tariq" });
        culturalTitle = "Faris"; // Knight
        nativeLanguageGreeting = "As-salamu alaykum, ya rajul"; // Peace be upon you, man
        honorLevel = 80f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Hospitality", "Courage", "Justice", "Protection of the innocent" };
        
        // Saracens might recognize player's Arab heritage
        CharacterProgression playerProgression = FindObjectOfType<CharacterProgression>();
        if (playerProgression != null)
        {
            float culturalBalance = playerProgression.GetCulturalBalance();
            if (culturalBalance < 0.5f) // More Arab heritage
            {
                playerReaction = ReactionToPlayer.Protective;
            }
            else
            {
                playerReaction = ReactionToPlayer.Curious;
            }
        }
    }
    
    void InitializeCrusader()
    {
        warriorName = GetRandomName(new string[] { "Richard", "Godfrey", "Baldwin", "Reynald", "Bohemond" });
        culturalTitle = "Knight Templar";
        nativeLanguageGreeting = "Deus vult, stranger"; // God wills it
        honorLevel = 75f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Faith", "Duty", "Righteousness", "Protection of pilgrims" };
        
        // Crusaders might be suspicious of Arab heritage
        CharacterProgression playerProgression = FindObjectOfType<CharacterProgression>();
        if (playerProgression != null)
        {
            float culturalBalance = playerProgression.GetCulturalBalance();
            if (culturalBalance < 0.5f) // More Arab heritage
            {
                playerReaction = ReactionToPlayer.Suspicious;
            }
            else
            {
                playerReaction = ReactionToPlayer.Neutral;
            }
        }
    }
    
    void InitializeViking()
    {
        warriorName = GetRandomName(new string[] { "Ragnar", "Bjorn", "Erik", "Olaf", "Gunnar" });
        culturalTitle = "Jarl";
        nativeLanguageGreeting = "Heil og sæl, útlendingur"; // Hale and blessed, foreigner
        honorLevel = 60f;
        followsHonorCode = false; // Vikings have different honor concepts
        
        culturalValues = new string[] { "Strength", "Courage in battle", "Loyalty to clan", "Glory in death" };
        playerReaction = ReactionToPlayer.Curious; // Vikings respect strength regardless of origin
    }
    
    void InitializeMamluk()
    {
        warriorName = GetRandomName(new string[] { "Baibars", "Qutuz", "Khalil", "Barquq", "Jaqmaq" });
        culturalTitle = "Mamluk Sultan";
        nativeLanguageGreeting = "Ahlan wa sahlan, ya walad"; // Welcome, young one
        honorLevel = 85f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Military excellence", "Learning", "Justice", "Protection of realm" };
        playerReaction = ReactionToPlayer.Respectful; // Mamluks value skill and potential
    }
    
    void InitializeByzantine()
    {
        warriorName = GetRandomName(new string[] { "Constantine", "Basil", "John", "Michael", "Alexios" });
        culturalTitle = "Strategos";
        nativeLanguageGreeting = "Chairete, xenos"; // Greetings, stranger (Greek)
        honorLevel = 70f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Imperial duty", "Orthodox faith", "Tactical brilliance", "Cultural preservation" };
        
        // Byzantines might recognize Greek heritage
        CharacterProgression playerProgression = FindObjectOfType<CharacterProgression>();
        if (playerProgression != null)
        {
            float culturalBalance = playerProgression.GetCulturalBalance();
            if (culturalBalance > 0.5f) // More Greek heritage
            {
                playerReaction = ReactionToPlayer.Protective;
            }
            else
            {
                playerReaction = ReactionToPlayer.Neutral;
            }
        }
    }
    
    void InitializeMongol()
    {
        warriorName = GetRandomName(new string[] { "Temujin", "Subutai", "Jebe", "Muqali", "Batu" });
        culturalTitle = "Khan";
        nativeLanguageGreeting = "Sain baina uu, gadaad hun"; // Hello, outsider
        honorLevel = 50f;
        followsHonorCode = false; // Pragmatic approach to warfare
        
        culturalValues = new string[] { "Adaptability", "Meritocracy", "Conquest", "Unity through strength" };
        playerReaction = ReactionToPlayer.Curious; // Mongols value ability over heritage
    }
    
    void InitializeCeltic()
    {
        warriorName = GetRandomName(new string[] { "Bran", "Cormac", "Finn", "Niall", "Conall" });
        culturalTitle = "Fianna";
        nativeLanguageGreeting = "Fáilte, coigríoch"; // Welcome, foreigner (Irish Gaelic)
        honorLevel = 65f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Clan loyalty", "Storytelling", "Connection to nature", "Warrior's oath" };
        playerReaction = ReactionToPlayer.Neutral; // Celts are wary but fair
    }
    
    void InitializePersian()
    {
        warriorName = GetRandomName(new string[] { "Darius", "Cyrus", "Xerxes", "Rostam", "Bahram" });
        culturalTitle = "Immortal";
        nativeLanguageGreeting = "Dorood bar shoma, biganeh"; // Greetings to you, stranger
        honorLevel = 80f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Truth", "Justice", "Royal service", "Cultural refinement" };
        playerReaction = ReactionToPlayer.Respectful; // Persians value nobility of spirit
    }
    
    void InitializeNubian()
    {
        warriorName = GetRandomName(new string[] { "Taharqa", "Piye", "Shabaka", "Tantamani", "Aspelta" });
        culturalTitle = "Pharaoh's Guard";
        nativeLanguageGreeting = "Senebty, khaemwaset"; // Be healthy, foreign one
        honorLevel = 75f;
        followsHonorCode = true;
        
        culturalValues = new string[] { "Divine kingship", "Protection of sacred", "Archery mastery", "River wisdom" };
        playerReaction = ReactionToPlayer.Curious; // Nubians are interested in all travelers
    }
    
    string GetRandomName(string[] names)
    {
        return names[Random.Range(0, names.Length)];
    }
    
    void SetupCulturalAppearance()
    {
        // Activate appropriate armor and weapons
        foreach (GameObject armor in culturalArmor)
        {
            if (armor != null)
                armor.SetActive(true);
        }
        
        foreach (GameObject weapon in culturalWeapons)
        {
            if (weapon != null)
                weapon.SetActive(true);
        }
        
        // Apply cultural materials and colors
        Renderer[] renderers = GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            if (culturalMaterials.Length > 0)
            {
                renderer.material = culturalMaterials[Random.Range(0, culturalMaterials.Length)];
            }
            
            if (culturalColors.Length > 0)
            {
                renderer.material.color = culturalColors[Random.Range(0, culturalColors.Length)];
            }
        }
    }
    
    void ConfigureCulturalBehavior()
    {
        // Configure NPC behavior based on culture
        if (npcController != null)
        {
            npcController.npcName = $"{warriorName} the {culturalTitle}";
            
            // Set initial relationship based on player reaction
            switch (playerReaction)
            {
                case ReactionToPlayer.Hostile:
                    npcController.currentStatus = NPCController.RelationshipStatus.Enemy;
                    npcController.isHostile = true;
                    break;
                case ReactionToPlayer.Suspicious:
                    npcController.relationshipValue = -25f;
                    break;
                case ReactionToPlayer.Protective:
                    npcController.relationshipValue = 50f;
                    break;
                case ReactionToPlayer.Respectful:
                    npcController.relationshipValue = 25f;
                    break;
            }
        }
        
        // Configure combat AI based on culture
        if (enemyAI != null)
        {
            ConfigureCulturalCombat();
        }
    }
    
    void ConfigureCulturalCombat()
    {
        switch (culture)
        {
            case WarriorCulture.Samurai:
                enemyAI.attackDamage *= 1.2f; // Precise strikes
                enemyAI.attackCooldown *= 1.3f; // Deliberate attacks
                break;
            case WarriorCulture.Viking:
                enemyAI.attackDamage *= 1.5f; // Brutal attacks
                enemyAI.attackCooldown *= 0.8f; // Aggressive
                break;
            case WarriorCulture.Mongol:
                enemyAI.chaseSpeed *= 1.3f; // Mobile warfare
                enemyAI.detectionRange *= 1.2f; // Superior scouting
                break;
            // Add more cultural combat modifications
        }
    }
    
    public void SpeakNativeLanguage()
    {
        if (nativeLanguageClips.Length > 0 && voiceSource != null)
        {
            AudioClip clip = nativeLanguageClips[Random.Range(0, nativeLanguageClips.Length)];
            voiceSource.PlayOneShot(clip);
        }
        
        Debug.Log($"{warriorName} says: \"{nativeLanguageGreeting}\"");
    }
    
    public void PerformCulturalGreeting()
    {
        SpeakNativeLanguage();
        
        // Perform cultural gesture animation
        if (warriorAnimator != null)
        {
            switch (culture)
            {
                case WarriorCulture.Samurai:
                    warriorAnimator.SetTrigger("Bow");
                    break;
                case WarriorCulture.Saracen:
                    warriorAnimator.SetTrigger("SalamGesture");
                    break;
                case WarriorCulture.Crusader:
                    warriorAnimator.SetTrigger("CrossSign");
                    break;
                // Add more cultural gestures
            }
        }
    }
    
    void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        // Some cultures react to player's moral choices
        switch (culture)
        {
            case WarriorCulture.Crusader:
                if (newPath == PlayerStats.MoralPath.Sun)
                {
                    npcController.ModifyRelationship(20f);
                }
                else if (newPath == PlayerStats.MoralPath.Moon)
                {
                    npcController.ModifyRelationship(-30f);
                }
                break;
            case WarriorCulture.Samurai:
                // Samurai respect any committed path
                npcController.ModifyRelationship(10f);
                break;
        }
    }
    
    public DialogueTree GetCulturalDialogue()
    {
        // Return culture-specific dialogue tree
        return CulturalDialogueData.GetDialogueForCulture(culture, playerReaction);
    }
    
    // Getters
    public WarriorCulture GetCulture() => culture;
    public string GetWarriorName() => warriorName;
    public string GetCulturalTitle() => culturalTitle;
    public ReactionToPlayer GetPlayerReaction() => playerReaction;
    public float GetHonorLevel() => honorLevel;
}
