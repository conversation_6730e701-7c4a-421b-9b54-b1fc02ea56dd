using UnityEngine;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;

/// <summary>
/// Automatically generates ancient melody audio clips when the project loads
/// Ensures ContemplativeContentSystem has functional ambient music assets
/// </summary>
[InitializeOnLoad]
public class AutoGenerateAncientMelodies
{
    static AutoGenerateAncientMelodies()
    {
        // Check if ancient melodies exist, generate if missing
        EditorApplication.delayCall += CheckAndGenerateMelodies;
    }
    
    static void CheckAndGenerateMelodies()
    {
        string melodyDir = "Assets/Audio/Melodies";
        string assetPath = "Assets/Audio/Melodies/AncientMelodyAsset.asset";
        
        // Check if the ancient melody asset exists
        if (!File.Exists(assetPath))
        {
            Debug.Log("Ancient melody assets not found. Generating placeholder audio...");
            GenerateAllAncientMelodies();
        }
        else
        {
            // Verify the asset has valid clips
            AncientMelodyAsset asset = AssetDatabase.LoadAssetAtPath<AncientMelodyAsset>(assetPath);
            if (asset == null || asset.ancientMelodies == null || asset.ancientMelodies.Length == 0)
            {
                Debug.Log("Ancient melody asset is empty. Regenerating audio...");
                GenerateAllAncientMelodies();
            }
        }
    }
    
    static void GenerateAllAncientMelodies()
    {
        // Create directory if it doesn't exist
        string melodyDir = "Assets/Audio/Melodies";
        if (!Directory.Exists(melodyDir))
        {
            Directory.CreateDirectory(melodyDir);
        }
        
        // Generate 12 ancient melody variations with different cultural themes
        AncientMelody[] melodies = new AncientMelody[]
        {
            new AncientMelody("lament_of_the_fallen", "Lament of the Fallen", 45f, CulturalOrigin.Ashlands, EmotionalTag.Loss, InstrumentType.Lyre),
            new AncientMelody("whispers_of_dawn", "Whispers of Dawn", 62f, CulturalOrigin.LightKingdom, EmotionalTag.Hope, InstrumentType.Flute),
            new AncientMelody("shadows_dance", "Shadows Dance", 38f, CulturalOrigin.ShadowKingdom, EmotionalTag.Mystery, InstrumentType.Oud),
            new AncientMelody("river_of_memories", "River of Memories", 71f, CulturalOrigin.Independent, EmotionalTag.Reflection, InstrumentType.Dulcimer),
            new AncientMelody("forgotten_homeland", "Forgotten Homeland", 55f, CulturalOrigin.Drenari, EmotionalTag.Longing, InstrumentType.VocalDrone),
            new AncientMelody("stars_lullaby", "Star's Lullaby", 83f, CulturalOrigin.Vaelari, EmotionalTag.Peace, InstrumentType.Harp),
            new AncientMelody("echoes_of_eternity", "Echoes of Eternity", 49f, CulturalOrigin.Ancient, EmotionalTag.Wonder, InstrumentType.Bells),
            new AncientMelody("mourning_winds", "Mourning Winds", 67f, CulturalOrigin.Ashlands, EmotionalTag.Sorrow, InstrumentType.Flute),
            new AncientMelody("temple_silence", "Temple Silence", 91f, CulturalOrigin.Monks, EmotionalTag.Serenity, InstrumentType.VocalDrone),
            new AncientMelody("lost_children_song", "Lost Children's Song", 34f, CulturalOrigin.Independent, EmotionalTag.Innocence, InstrumentType.Dulcimer),
            new AncientMelody("ember_heart", "Ember Heart", 58f, CulturalOrigin.Cinderborn, EmotionalTag.Determination, InstrumentType.Lyre),
            new AncientMelody("void_between_worlds", "Void Between Worlds", 76f, CulturalOrigin.Cosmic, EmotionalTag.Transcendence, InstrumentType.Ethereal)
        };
        
        // Generate each melody
        AudioClip[] generatedClips = new AudioClip[melodies.Length];
        for (int i = 0; i < melodies.Length; i++)
        {
            generatedClips[i] = GenerateAncientMelodyClip(melodies[i]);
        }
        
        // Create the ancient melody asset
        CreateAncientMelodyAsset(melodies, generatedClips);
        
        AssetDatabase.Refresh();
        Debug.Log("All ancient melody clips generated successfully!");
    }
    
    static AudioClip GenerateAncientMelodyClip(AncientMelody melody)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * melody.duration);
        AudioClip clip = AudioClip.Create(melody.fileName, samples, 2, sampleRate, false); // Stereo for spatial depth
        
        float[] data = new float[samples * 2]; // Stereo data
        
        // Get musical parameters for this melody
        float[] scale = GetScaleForCulture(melody.culturalOrigin);
        float tempo = GetTempoForEmotion(melody.emotionalTag);
        
        // Generate melody based on instrument type and cultural origin
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            float normalizedTime = time / melody.duration;
            
            // Generate base melody
            Vector2 stereoSample = GenerateMelodyForInstrument(time, melody, scale, tempo);
            
            // Apply cultural modulation
            stereoSample = ApplyCulturalCharacteristics(stereoSample, time, melody);
            
            // Apply emotional coloring
            stereoSample = ApplyEmotionalColoring(stereoSample, time, normalizedTime, melody);
            
            // Apply envelope
            float envelope = CalculateMelodyEnvelope(normalizedTime, melody);
            stereoSample *= envelope;
            
            // Store stereo data
            data[i * 2] = Mathf.Clamp(stereoSample.x, -1f, 1f);     // Left channel
            data[i * 2 + 1] = Mathf.Clamp(stereoSample.y, -1f, 1f); // Right channel
        }
        
        clip.SetData(data, 0);
        
        // Save the clip
        string path = $"Assets/Audio/Melodies/{melody.fileName}.asset";
        AssetDatabase.CreateAsset(clip, path);
        
        Debug.Log($"Generated ancient melody: {melody.title} ({melody.duration:F1}s, {melody.culturalOrigin}, {melody.emotionalTag})");
        
        return clip;
    }
    
    static Vector2 GenerateMelodyForInstrument(float time, AncientMelody melody, float[] scale, float tempo)
    {
        Vector2 sample = Vector2.zero;
        
        switch (melody.instrumentType)
        {
            case InstrumentType.Lyre:
                sample = GenerateLyreSound(time, scale, tempo);
                break;
            case InstrumentType.Oud:
                sample = GenerateOudSound(time, scale, tempo);
                break;
            case InstrumentType.Dulcimer:
                sample = GenerateDulcimerSound(time, scale, tempo);
                break;
            case InstrumentType.Flute:
                sample = GenerateFluteSound(time, scale, tempo);
                break;
            case InstrumentType.VocalDrone:
                sample = GenerateVocalDroneSound(time, scale[0]); // Use root note
                break;
            case InstrumentType.Harp:
                sample = GenerateHarpSound(time, scale, tempo);
                break;
            case InstrumentType.Bells:
                sample = GenerateBellSound(time, scale, tempo);
                break;
            case InstrumentType.Ethereal:
                sample = GenerateEtherealSound(time, scale[0]);
                break;
        }
        
        return sample;
    }
    
    static Vector2 GenerateLyreSound(float time, float[] scale, float tempo)
    {
        // Ancient lyre with plucked string harmonics
        int noteIndex = Mathf.FloorToInt(time * tempo) % scale.Length;
        float frequency = scale[noteIndex];
        
        // Plucked string with decay
        float noteTime = time % (1f / tempo);
        float pluck = Mathf.Sin(time * frequency * 2f * Mathf.PI) * Mathf.Exp(-noteTime * 2f);
        
        // Add harmonics
        float harmonic1 = Mathf.Sin(time * frequency * 3f * 2f * Mathf.PI) * 0.3f * Mathf.Exp(-noteTime * 3f);
        float harmonic2 = Mathf.Sin(time * frequency * 5f * 2f * Mathf.PI) * 0.15f * Mathf.Exp(-noteTime * 4f);
        
        float mono = (pluck + harmonic1 + harmonic2) * 0.4f;
        
        // Stereo spread for spatial depth
        return new Vector2(mono * 0.8f, mono * 1.2f);
    }
    
    static Vector2 GenerateOudSound(float time, float[] scale, float tempo)
    {
        // Middle Eastern oud with microtonal bending
        float baseFreq = scale[Mathf.FloorToInt(time * tempo * 0.7f) % scale.Length];
        
        // Add microtonal bending
        float bend = Mathf.Sin(time * 0.7f) * 0.05f;
        float frequency = baseFreq * (1f + bend);
        
        // Oud-like timbre with rich harmonics
        float fundamental = Mathf.Sin(time * frequency * 2f * Mathf.PI);
        float harmonic2 = Mathf.Sin(time * frequency * 2f * 2f * Mathf.PI) * 0.6f;
        float harmonic3 = Mathf.Sin(time * frequency * 3f * 2f * Mathf.PI) * 0.4f;
        
        float mono = (fundamental + harmonic2 + harmonic3) * 0.3f;
        
        // Slight stereo delay for depth
        return new Vector2(mono * 0.9f, mono * 1.1f);
    }
    
    static Vector2 GenerateDulcimerSound(float time, float[] scale, float tempo)
    {
        // Hammered dulcimer with metallic resonance
        float sample = 0f;
        
        // Multiple strings playing together
        for (int i = 0; i < 3; i++)
        {
            int noteIndex = (Mathf.FloorToInt(time * tempo * 0.8f) + i) % scale.Length;
            float frequency = scale[noteIndex];
            
            // Struck string with metallic decay
            float noteTime = (time + i * 0.1f) % (1.2f / tempo);
            float strike = Mathf.Sin(time * frequency * 2f * Mathf.PI) * Mathf.Exp(-noteTime * 1.2f);
            
            // Metallic resonance
            float resonance = Mathf.Sin(time * frequency * 1.5f * 2f * Mathf.PI) * 0.2f * Mathf.Exp(-noteTime * 0.8f);
            
            sample += (strike + resonance) * (0.3f / (i + 1));
        }
        
        // Wide stereo spread
        return new Vector2(sample * 0.7f, sample * 1.3f);
    }
    
    static Vector2 GenerateFluteSound(float time, float[] scale, float tempo)
    {
        // Wooden flute with breath texture
        int noteIndex = Mathf.FloorToInt(time * tempo * 0.6f) % scale.Length;
        float frequency = scale[noteIndex];
        
        // Flute fundamental with slight vibrato
        float vibrato = 1f + 0.02f * Mathf.Sin(time * 5f);
        float flute = Mathf.Sin(time * frequency * vibrato * 2f * Mathf.PI);
        
        // Add breath noise
        float breath = (Mathf.PerlinNoise(time * 20f, 0f) - 0.5f) * 0.1f;
        
        // Soft attack and release for each note
        float noteTime = time % (1.5f / tempo);
        float envelope = Mathf.Sin(noteTime / (1.5f / tempo) * Mathf.PI);
        
        float mono = (flute + breath) * envelope * 0.35f;
        
        // Natural stereo positioning
        return new Vector2(mono * 0.9f, mono * 1.1f);
    }
    
    static Vector2 GenerateVocalDroneSound(float time, float baseFreq)
    {
        // Human vocal drone with formants
        float fundamental = Mathf.Sin(time * baseFreq * 2f * Mathf.PI);
        
        // Vocal formants (simplified)
        float formant1 = Mathf.Sin(time * baseFreq * 2f * 2f * Mathf.PI) * 0.4f;
        float formant2 = Mathf.Sin(time * baseFreq * 3f * 2f * Mathf.PI) * 0.3f;
        float formant3 = Mathf.Sin(time * baseFreq * 4f * 2f * Mathf.PI) * 0.2f;
        
        // Breathing modulation
        float breathing = 1f + 0.1f * Mathf.Sin(time * 0.3f);
        
        float mono = (fundamental + formant1 + formant2 + formant3) * breathing * 0.25f;
        
        // Center the vocal drone
        return new Vector2(mono, mono);
    }
    
    static Vector2 GenerateHarpSound(float time, float[] scale, float tempo)
    {
        // Celtic/ancient harp with string resonance
        float sample = 0f;
        
        // Arpeggiated pattern
        for (int i = 0; i < 4; i++)
        {
            float noteTime = time - (i * 0.15f);
            if (noteTime > 0)
            {
                int noteIndex = (Mathf.FloorToInt(noteTime * tempo * 1.2f) + i) % scale.Length;
                float frequency = scale[noteIndex];
                
                // Plucked harp string
                float pluck = Mathf.Sin(noteTime * frequency * 2f * Mathf.PI) * Mathf.Exp(-noteTime * 0.8f);
                
                sample += pluck * (0.4f / (i + 1));
            }
        }
        
        // Rich stereo spread
        return new Vector2(sample * 0.6f, sample * 1.4f);
    }
    
    static Vector2 GenerateBellSound(float time, float[] scale, float tempo)
    {
        // Temple bells with long resonance
        float sample = 0f;
        
        // Use scale notes as bell frequencies
        foreach (float freq in scale)
        {
            // Bell strike with long decay
            float bellTime = time % (4f / tempo); // Slow bell strikes
            float bell = Mathf.Sin(time * freq * 2f * Mathf.PI) * Mathf.Exp(-bellTime * 0.3f);
            
            // Add metallic overtones
            float overtone = Mathf.Sin(time * freq * 2.7f * 2f * Mathf.PI) * 0.3f * Mathf.Exp(-bellTime * 0.4f);
            
            sample += (bell + overtone) * 0.15f;
        }
        
        // Wide stereo field for temple ambience
        return new Vector2(sample * 0.5f, sample * 1.5f);
    }
    
    static Vector2 GenerateEtherealSound(float time, float baseFreq)
    {
        // Otherworldly, cosmic sounds
        float wave1 = Mathf.Sin(time * baseFreq * 2f * Mathf.PI);
        float wave2 = Mathf.Sin(time * baseFreq * 1.618f * 2f * Mathf.PI) * 0.7f; // Golden ratio
        float wave3 = Mathf.Sin(time * baseFreq * 2.414f * 2f * Mathf.PI) * 0.5f; // Silver ratio
        
        // Cosmic modulation
        float modulation = 1f + 0.3f * Mathf.Sin(time * 0.1f);
        
        // Add ethereal noise
        float noise = (Mathf.PerlinNoise(time * 5f, 0f) - 0.5f) * 0.2f;
        
        float mono = (wave1 + wave2 + wave3 + noise) * modulation * 0.3f;
        
        // Phase-shifted stereo for otherworldly effect
        float phase = Mathf.PI * 0.25f;
        return new Vector2(mono, mono * Mathf.Cos(phase));
    }
    
    static Vector2 ApplyCulturalCharacteristics(Vector2 sample, float time, AncientMelody melody)
    {
        switch (melody.culturalOrigin)
        {
            case CulturalOrigin.Ashlands:
                // Add ash-like texture
                sample *= 1f + 0.1f * (Mathf.PerlinNoise(time * 10f, 0f) - 0.5f);
                break;
            case CulturalOrigin.LightKingdom:
                // Brighter, more resonant
                sample *= 1f + 0.05f * Mathf.Sin(time * 3f);
                break;
            case CulturalOrigin.ShadowKingdom:
                // Darker, more mysterious
                sample *= 0.9f + 0.1f * Mathf.Sin(time * 0.7f);
                break;
            case CulturalOrigin.Drenari:
                // Exotic scales and timbres
                sample *= 1f + 0.15f * Mathf.Sin(time * 1.3f);
                break;
            case CulturalOrigin.Vaelari:
                // Ethereal and flowing
                sample *= 1f + 0.2f * Mathf.Sin(time * 0.5f);
                break;
        }
        
        return sample;
    }
    
    static Vector2 ApplyEmotionalColoring(Vector2 sample, float time, float normalizedTime, AncientMelody melody)
    {
        switch (melody.emotionalTag)
        {
            case EmotionalTag.Loss:
                // Add melancholic tremolo
                sample *= 1f + 0.1f * Mathf.Sin(time * 4f);
                break;
            case EmotionalTag.Hope:
                // Gentle upward modulation
                sample *= 1f + 0.05f * normalizedTime;
                break;
            case EmotionalTag.Mystery:
                // Unpredictable modulation
                sample *= 1f + 0.1f * Mathf.PerlinNoise(time * 2f, 0f);
                break;
            case EmotionalTag.Peace:
                // Stable, gentle
                sample *= 1f + 0.03f * Mathf.Sin(time * 1f);
                break;
            case EmotionalTag.Sorrow:
                // Descending modulation
                sample *= 1f - 0.05f * normalizedTime;
                break;
        }
        
        return sample;
    }
    
    static float CalculateMelodyEnvelope(float normalizedTime, AncientMelody melody)
    {
        // Gentle fade in and out
        float fadeInTime = 0.1f;
        float fadeOutTime = 0.2f;
        
        float envelope = 1f;
        
        if (normalizedTime < fadeInTime)
        {
            envelope = normalizedTime / fadeInTime;
        }
        else if (normalizedTime > (1f - fadeOutTime))
        {
            float fadeOutProgress = (normalizedTime - (1f - fadeOutTime)) / fadeOutTime;
            envelope = 1f - fadeOutProgress;
        }
        
        // Apply smooth curve
        envelope = Mathf.SmoothStep(0f, 1f, envelope);
        
        return envelope;
    }
    
    static float[] GetScaleForCulture(CulturalOrigin culture)
    {
        // Different musical scales for different cultures
        switch (culture)
        {
            case CulturalOrigin.Ashlands:
                return new float[] { 220f, 247f, 277f, 311f, 330f, 370f, 415f }; // Minor pentatonic
            case CulturalOrigin.LightKingdom:
                return new float[] { 261f, 294f, 330f, 349f, 392f, 440f, 494f }; // Major scale
            case CulturalOrigin.ShadowKingdom:
                return new float[] { 220f, 233f, 262f, 277f, 294f, 311f, 349f }; // Natural minor
            case CulturalOrigin.Drenari:
                return new float[] { 220f, 246f, 277f, 293f, 330f, 369f, 415f }; // Exotic scale
            case CulturalOrigin.Vaelari:
                return new float[] { 174f, 196f, 220f, 246f, 277f, 311f, 349f }; // Ethereal scale
            case CulturalOrigin.Ancient:
                return new float[] { 110f, 123f, 138f, 155f, 174f, 196f, 220f }; // Ancient modes
            case CulturalOrigin.Monks:
                return new float[] { 196f, 220f, 246f, 262f, 294f, 330f, 370f }; // Gregorian-inspired
            case CulturalOrigin.Cosmic:
                return new float[] { 110f, 138f, 174f, 220f, 277f, 349f, 440f }; // Cosmic ratios
            default:
                return new float[] { 220f, 247f, 277f, 311f, 330f, 370f, 415f };
        }
    }
    
    static float GetTempoForEmotion(EmotionalTag emotion)
    {
        // Different tempos for different emotions (notes per second)
        switch (emotion)
        {
            case EmotionalTag.Peace:
            case EmotionalTag.Serenity:
                return 0.3f; // Very slow
            case EmotionalTag.Sorrow:
            case EmotionalTag.Loss:
                return 0.4f; // Slow
            case EmotionalTag.Reflection:
            case EmotionalTag.Longing:
                return 0.5f; // Moderate slow
            case EmotionalTag.Mystery:
            case EmotionalTag.Wonder:
                return 0.6f; // Moderate
            case EmotionalTag.Hope:
            case EmotionalTag.Determination:
                return 0.7f; // Moderate fast
            case EmotionalTag.Innocence:
                return 0.8f; // Faster
            default:
                return 0.5f; // Default moderate
        }
    }
    
    static void CreateAncientMelodyAsset(AncientMelody[] melodies, AudioClip[] clips)
    {
        // Create a ScriptableObject asset to hold the melody clips
        AncientMelodyAsset asset = ScriptableObject.CreateInstance<AncientMelodyAsset>();
        
        // Assign clips and metadata
        asset.ancientMelodies = clips;
        asset.melodyMetadata = new AncientMelodyMetadata[melodies.Length];
        
        for (int i = 0; i < melodies.Length; i++)
        {
            asset.melodyMetadata[i] = new AncientMelodyMetadata
            {
                title = melodies[i].title,
                culturalOrigin = melodies[i].culturalOrigin,
                emotionalTag = melodies[i].emotionalTag,
                instrumentType = melodies[i].instrumentType,
                duration = melodies[i].duration,
                description = $"Ancient {melodies[i].instrumentType} melody from {melodies[i].culturalOrigin} culture, evoking {melodies[i].emotionalTag}.",
                suitableForContemplation = true,
                suitableForNarrative = true,
                suitableForAmbient = true
            };
        }
        
        // Set default settings
        asset.defaultVolume = 0.4f;
        asset.spatialBlend = 0.6f;
        asset.fadeInDuration = 3f;
        asset.fadeOutDuration = 5f;
        asset.enableCulturalFiltering = true;
        asset.enableEmotionalFiltering = true;
        asset.pitchVariation = 0.1f;
        asset.volumeVariation = 0.05f;
        
        string assetPath = "Assets/Audio/Melodies/AncientMelodyAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);
        
        // Also create a copy in Resources folder for runtime loading
        string resourcesDir = "Assets/Resources";
        if (!Directory.Exists(resourcesDir))
        {
            Directory.CreateDirectory(resourcesDir);
        }
        
        string resourcesPath = "Assets/Resources/AncientMelodyAsset.asset";
        AssetDatabase.CopyAsset(assetPath, resourcesPath);
        
        AssetDatabase.SaveAssets();
        
        Debug.Log($"Created AncientMelodyAsset with {melodies.Length} clips at {assetPath}");
        Debug.Log($"Also created Resources copy at {resourcesPath} for runtime loading");
    }
}
#endif
