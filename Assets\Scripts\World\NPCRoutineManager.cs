using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using CinderOfDarkness.World;

namespace CinderOfDarkness.NPCs
{
    /// <summary>
    /// NPC Routine Manager for Cinder of Darkness.
    /// Manages complex NPC behaviors, daily routines, and cultural interactions.
    /// </summary>
    public class NPCRoutineManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Routine Management")]
        [SerializeField] private bool enableRoutines = true;
        [SerializeField] private float routineUpdateInterval = 30f;
        [SerializeField] private int maxActiveRoutines = 50;

        [Header("Cultural Behaviors")]
        [SerializeField] private bool enableCulturalBehaviors = true;
        [SerializeField] private float behaviorUpdateInterval = 10f;

        [Header("Weather and Time Integration")]
        [SerializeField] private bool respondToWeather = true;
        [SerializeField] private bool respondToTimeOfDay = true;
        [SerializeField] private bool respondToSeasons = true;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logRoutineChanges = false;
        #endregion

        #region Private Fields
        private List<ActiveNPCRoutine> activeRoutines = new List<ActiveNPCRoutine>();
        private Dictionary<string, NPCBehaviorState> npcStates = new Dictionary<string, NPCBehaviorState>();
        private ExpandedKingdomSystem kingdomSystem;
        private ComprehensiveWorldExpansion worldExpansion;
        private float lastRoutineUpdate;
        private float lastBehaviorUpdate;
        private float currentGameTime;
        private WeatherSystem.WeatherType currentWeather;
        private int currentSeason;
        #endregion

        #region Public Properties
        public bool RoutinesEnabled => enableRoutines;
        public int ActiveRoutineCount => activeRoutines.Count;
        #endregion

        #region Unity Lifecycle
        private void Update()
        {
            if (enableRoutines)
            {
                UpdateRoutines();
            }

            if (enableCulturalBehaviors)
            {
                UpdateCulturalBehaviors();
            }

            UpdateTimeAndWeather();
        }

        private void OnGUI()
        {
            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }
        #endregion

        #region Initialization
        public void Initialize(ExpandedKingdomSystem kingdomSys)
        {
            kingdomSystem = kingdomSys;
            worldExpansion = ComprehensiveWorldExpansion.Instance;
            
            InitializeTimeAndWeather();
            
            Debug.Log("NPC Routine Manager initialized");
        }

        private void InitializeTimeAndWeather()
        {
            currentGameTime = 8f; // Start at 8 AM
            currentWeather = WeatherSystem.WeatherType.ClearSkies;
            currentSeason = 0; // Spring
        }
        #endregion

        #region Routine Management
        private void UpdateRoutines()
        {
            if (Time.time - lastRoutineUpdate < routineUpdateInterval) return;
            
            lastRoutineUpdate = Time.time;
            
            // Update existing routines
            for (int i = activeRoutines.Count - 1; i >= 0; i--)
            {
                var routine = activeRoutines[i];
                UpdateActiveRoutine(routine);
                
                if (routine.isCompleted || routine.isCancelled)
                {
                    CompleteRoutine(routine);
                    activeRoutines.RemoveAt(i);
                }
            }
            
            // Start new routines based on time and conditions
            CheckForNewRoutines();
        }

        private void UpdateActiveRoutine(ActiveNPCRoutine routine)
        {
            if (routine.isPaused) return;
            
            routine.elapsedTime += routineUpdateInterval;
            
            // Check if routine should be paused due to weather/conditions
            if (ShouldPauseRoutine(routine))
            {
                PauseRoutine(routine);
                return;
            }
            
            // Update routine progress
            float progress = routine.elapsedTime / (routine.duration * 3600f); // Convert hours to seconds
            
            if (progress >= 1f)
            {
                routine.isCompleted = true;
            }
            else
            {
                // Update NPC position and behavior based on routine progress
                UpdateNPCForRoutine(routine, progress);
            }
        }

        private void UpdateNPCForRoutine(ActiveNPCRoutine routine, float progress)
        {
            if (routine.npcController == null) return;
            
            // Calculate current position in routine
            Vector3 targetPosition = Vector3.Lerp(routine.startPosition, routine.endPosition, progress);
            
            // Move NPC towards target position
            routine.npcController.SetTargetPosition(targetPosition);
            
            // Update NPC animation and behavior
            routine.npcController.SetCurrentActivity(routine.currentActivity);
            
            // Apply cultural behaviors
            ApplyCulturalBehaviors(routine.npcController, routine.culturalContext);
        }

        private bool ShouldPauseRoutine(ActiveNPCRoutine routine)
        {
            // Check weather dependencies
            if (routine.isWeatherDependent && !IsWeatherSuitable(routine.requiredWeather))
            {
                return true;
            }
            
            // Check time dependencies
            if (routine.isTimeDependent && !IsTimeAppropriate(routine.startTime, routine.duration))
            {
                return true;
            }
            
            // Check seasonal dependencies
            if (routine.isSeasonDependent && !IsSeasonAppropriate(routine.requiredSeason))
            {
                return true;
            }
            
            return false;
        }

        private void CheckForNewRoutines()
        {
            if (activeRoutines.Count >= maxActiveRoutines) return;
            
            // Check kingdom routines
            if (kingdomSystem != null && kingdomSystem.CurrentKingdom != null)
            {
                CheckKingdomRoutines(kingdomSystem.CurrentKingdom);
            }
            
            // Check village routines
            if (kingdomSystem != null && kingdomSystem.CurrentVillage != null)
            {
                CheckVillageRoutines(kingdomSystem.CurrentVillage);
            }
            
            // Check expanded village routines
            if (worldExpansion != null)
            {
                CheckExpandedVillageRoutines();
            }
        }

        private void CheckKingdomRoutines(ExpandedKingdomSystem.Kingdom kingdom)
        {
            // Implementation for kingdom-specific routines
            foreach (var npcType in kingdom.npcTypes)
            {
                if (ShouldStartNPCRoutine(npcType.ToString(), kingdom.kingdomName))
                {
                    StartKingdomNPCRoutine(npcType, kingdom);
                }
            }
        }

        private void CheckVillageRoutines(ExpandedKingdomSystem.Village village)
        {
            // Implementation for village-specific routines
            foreach (var routine in village.dailyRoutines)
            {
                if (ShouldStartDailyRoutine(routine))
                {
                    StartVillageDailyRoutine(routine, village);
                }
            }
        }

        private void CheckExpandedVillageRoutines()
        {
            var villages = worldExpansion.GetAllVillages();
            foreach (var village in villages)
            {
                foreach (var routine in village.dailyRoutines)
                {
                    if (ShouldStartExpandedRoutine(routine))
                    {
                        StartExpandedVillageRoutine(routine, village);
                    }
                }
            }
        }

        private bool ShouldStartNPCRoutine(string npcType, string location)
        {
            // Check if this NPC type should start a routine at current time
            string routineKey = $"{location}_{npcType}_{currentGameTime:F0}";
            
            // Avoid duplicate routines
            foreach (var active in activeRoutines)
            {
                if (active.routineId == routineKey)
                    return false;
            }
            
            return true;
        }

        private bool ShouldStartDailyRoutine(ExpandedKingdomSystem.Village.DailyRoutine routine)
        {
            float currentHour = currentGameTime % 24f;
            float routineStart = routine.startTime;
            float routineEnd = (routine.startTime + routine.duration) % 24f;
            
            // Check if current time is within routine window
            if (routineStart <= routineEnd)
            {
                return currentHour >= routineStart && currentHour <= routineEnd;
            }
            else // Routine crosses midnight
            {
                return currentHour >= routineStart || currentHour <= routineEnd;
            }
        }

        private bool ShouldStartExpandedRoutine(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine)
        {
            float currentHour = currentGameTime % 24f;
            
            // Check basic time window
            if (!IsTimeInWindow(currentHour, routine.startTime, routine.duration))
                return false;
            
            // Check weather dependency
            if (routine.isWeatherDependent && !IsWeatherSuitable(GetRequiredWeatherForRoutine(routine)))
                return false;
            
            // Check seasonal dependency
            if (routine.isSeasonDependent && !IsSeasonAppropriate(GetRequiredSeasonForRoutine(routine)))
                return false;
            
            // Check if routine is already active
            string routineKey = $"{routine.routineName}_{currentHour:F0}";
            foreach (var active in activeRoutines)
            {
                if (active.routineId == routineKey)
                    return false;
            }
            
            return true;
        }

        private void StartKingdomNPCRoutine(ExpandedKingdomSystem.Kingdom.NPCType npcType, ExpandedKingdomSystem.Kingdom kingdom)
        {
            var routine = CreateKingdomRoutine(npcType, kingdom);
            if (routine != null)
            {
                activeRoutines.Add(routine);
                
                if (logRoutineChanges)
                    Debug.Log($"Started kingdom routine: {npcType} in {kingdom.kingdomName}");
            }
        }

        private void StartVillageDailyRoutine(ExpandedKingdomSystem.Village.DailyRoutine routine, ExpandedKingdomSystem.Village village)
        {
            var activeRoutine = CreateVillageRoutine(routine, village);
            if (activeRoutine != null)
            {
                activeRoutines.Add(activeRoutine);
                
                if (logRoutineChanges)
                    Debug.Log($"Started village routine: {routine.routineName} in {village.villageName}");
            }
        }

        private void StartExpandedVillageRoutine(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine, ComprehensiveWorldExpansion.ExpandedVillage village)
        {
            var activeRoutine = CreateExpandedVillageRoutine(routine, village);
            if (activeRoutine != null)
            {
                activeRoutines.Add(activeRoutine);
                
                if (logRoutineChanges)
                    Debug.Log($"Started expanded village routine: {routine.routineName} in {village.villageName}");
            }
        }

        private ActiveNPCRoutine CreateKingdomRoutine(ExpandedKingdomSystem.Kingdom.NPCType npcType, ExpandedKingdomSystem.Kingdom kingdom)
        {
            // Create routine based on NPC type and kingdom
            return new ActiveNPCRoutine
            {
                routineId = $"{kingdom.kingdomName}_{npcType}_{currentGameTime:F0}",
                npcType = npcType.ToString(),
                location = kingdom.kingdomName,
                startTime = currentGameTime,
                duration = GetDefaultRoutineDuration(npcType),
                startPosition = GetKingdomNPCStartPosition(npcType, kingdom),
                endPosition = GetKingdomNPCEndPosition(npcType, kingdom),
                currentActivity = GetKingdomNPCActivity(npcType),
                culturalContext = kingdom.kingdomType.ToString(),
                isWeatherDependent = IsNPCWeatherDependent(npcType),
                isTimeDependent = true,
                isSeasonDependent = false
            };
        }

        private ActiveNPCRoutine CreateVillageRoutine(ExpandedKingdomSystem.Village.DailyRoutine routine, ExpandedKingdomSystem.Village village)
        {
            return new ActiveNPCRoutine
            {
                routineId = $"{village.villageName}_{routine.routineName}_{currentGameTime:F0}",
                npcType = "Villager",
                location = village.villageName,
                startTime = routine.startTime,
                duration = routine.duration,
                startPosition = GetVillageRoutineStartPosition(routine, village),
                endPosition = GetVillageRoutineEndPosition(routine, village),
                currentActivity = routine.routineName,
                culturalContext = village.villageType.ToString(),
                isWeatherDependent = routine.isWeatherDependent,
                isTimeDependent = true,
                isSeasonDependent = routine.isSeasonDependent
            };
        }

        private ActiveNPCRoutine CreateExpandedVillageRoutine(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine, ComprehensiveWorldExpansion.ExpandedVillage village)
        {
            return new ActiveNPCRoutine
            {
                routineId = $"{village.villageName}_{routine.routineName}_{currentGameTime:F0}",
                npcType = "ExpandedVillager",
                location = village.villageName,
                startTime = routine.startTime,
                duration = routine.duration,
                startPosition = GetExpandedVillageRoutineStartPosition(routine, village),
                endPosition = GetExpandedVillageRoutineEndPosition(routine, village),
                currentActivity = routine.routineName,
                culturalContext = village.villageType.ToString(),
                isWeatherDependent = routine.isWeatherDependent,
                isTimeDependent = true,
                isSeasonDependent = routine.isSeasonDependent,
                isRepeating = routine.isRepeating,
                repeatInterval = routine.repeatInterval
            };
        }

        private void CompleteRoutine(ActiveNPCRoutine routine)
        {
            // Handle routine completion
            if (routine.npcController != null)
            {
                routine.npcController.OnRoutineCompleted(routine);
            }
            
            // Check if routine should repeat
            if (routine.isRepeating && !routine.isCancelled)
            {
                ScheduleRoutineRepeat(routine);
            }
            
            if (logRoutineChanges)
                Debug.Log($"Completed routine: {routine.routineId}");
        }

        private void PauseRoutine(ActiveNPCRoutine routine)
        {
            routine.isPaused = true;
            
            if (routine.npcController != null)
            {
                routine.npcController.OnRoutinePaused(routine);
            }
            
            if (logRoutineChanges)
                Debug.Log($"Paused routine: {routine.routineId}");
        }

        private void ScheduleRoutineRepeat(ActiveNPCRoutine routine)
        {
            // Schedule routine to repeat after interval
            StartCoroutine(RepeatRoutineAfterDelay(routine, routine.repeatInterval * 3600f)); // Convert hours to seconds
        }

        private IEnumerator RepeatRoutineAfterDelay(ActiveNPCRoutine originalRoutine, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            // Create new instance of the routine
            var newRoutine = CloneRoutine(originalRoutine);
            newRoutine.routineId = $"{originalRoutine.routineId}_repeat_{Time.time}";
            newRoutine.elapsedTime = 0f;
            newRoutine.isCompleted = false;
            newRoutine.isPaused = false;
            newRoutine.isCancelled = false;
            
            activeRoutines.Add(newRoutine);
        }

        private ActiveNPCRoutine CloneRoutine(ActiveNPCRoutine original)
        {
            return new ActiveNPCRoutine
            {
                routineId = original.routineId,
                npcType = original.npcType,
                location = original.location,
                startTime = original.startTime,
                duration = original.duration,
                startPosition = original.startPosition,
                endPosition = original.endPosition,
                currentActivity = original.currentActivity,
                culturalContext = original.culturalContext,
                isWeatherDependent = original.isWeatherDependent,
                isTimeDependent = original.isTimeDependent,
                isSeasonDependent = original.isSeasonDependent,
                isRepeating = original.isRepeating,
                repeatInterval = original.repeatInterval,
                requiredWeather = original.requiredWeather,
                requiredSeason = original.requiredSeason
            };
        }
        #endregion

        #region Cultural Behaviors
        private void UpdateCulturalBehaviors()
        {
            if (Time.time - lastBehaviorUpdate < behaviorUpdateInterval) return;
            
            lastBehaviorUpdate = Time.time;
            
            // Update cultural behaviors for all active NPCs
            foreach (var routine in activeRoutines)
            {
                if (routine.npcController != null)
                {
                    UpdateNPCCulturalBehavior(routine.npcController, routine.culturalContext);
                }
            }
        }

        private void ApplyCulturalBehaviors(NPCController npc, string culturalContext)
        {
            if (npc == null) return;
            
            // Apply behaviors based on cultural context
            switch (culturalContext)
            {
                case "FireWorshippers":
                    ApplyFireWorshipperBehaviors(npc);
                    break;
                case "EarthBound":
                    ApplyEarthBoundBehaviors(npc);
                    break;
                case "SorrowKeepers":
                    ApplySorrowKeeperBehaviors(npc);
                    break;
                case "MemoryKeepers":
                    ApplyMemoryKeeperBehaviors(npc);
                    break;
                case "TimeKeepers":
                    ApplyTimeKeeperBehaviors(npc);
                    break;
                case "DreamWeavers":
                    ApplyDreamWeaverBehaviors(npc);
                    break;
                // Add more cultural behavior applications
            }
        }

        private void UpdateNPCCulturalBehavior(NPCController npc, string culturalContext)
        {
            // Update ongoing cultural behaviors
            ApplyCulturalBehaviors(npc, culturalContext);
        }

        private void ApplyFireWorshipperBehaviors(NPCController npc)
        {
            // Fire worshipper specific behaviors
            npc.SetBehaviorModifier("fire_reverence", 1.5f);
            npc.SetAnimationTrigger("flame_gesture");
        }

        private void ApplyEarthBoundBehaviors(NPCController npc)
        {
            // Earth bound specific behaviors
            npc.SetBehaviorModifier("stone_listening", 1.2f);
            npc.SetMovementSpeed(0.8f); // Slower, more deliberate movement
        }

        private void ApplySorrowKeeperBehaviors(NPCController npc)
        {
            // Sorrow keeper specific behaviors
            npc.SetEmotionalState("melancholic");
            npc.SetBehaviorModifier("grief_processing", 1.3f);
        }

        private void ApplyMemoryKeeperBehaviors(NPCController npc)
        {
            // Memory keeper specific behaviors
            npc.SetBehaviorModifier("name_importance", 2f);
            npc.SetDialogueStyle("formal_naming");
        }

        private void ApplyTimeKeeperBehaviors(NPCController npc)
        {
            // Time keeper specific behaviors
            npc.SetBehaviorModifier("punctuality_obsession", 2.5f);
            npc.SetMovementPrecision(1.5f);
        }

        private void ApplyDreamWeaverBehaviors(NPCController npc)
        {
            // Dream weaver specific behaviors
            npc.SetBehaviorModifier("dream_awareness", 1.8f);
            npc.SetAnimationTrigger("dreamy_movement");
        }
        #endregion

        #region Time and Weather
        private void UpdateTimeAndWeather()
        {
            // Update game time (simplified - would integrate with actual time system)
            currentGameTime += Time.deltaTime * 0.1f; // Accelerated time
            if (currentGameTime >= 24f)
            {
                currentGameTime -= 24f;
                // New day
            }
            
            // Update weather (simplified - would integrate with weather system)
            // Weather changes would be handled by actual weather system
            
            // Update season (simplified - would integrate with seasonal system)
            // Season changes would be handled by actual time progression system
        }

        private bool IsWeatherSuitable(WeatherSystem.WeatherType requiredWeather)
        {
            if (requiredWeather == WeatherSystem.WeatherType.ClearSkies)
                return currentWeather == WeatherSystem.WeatherType.ClearSkies;
            
            // Add more weather condition checks
            return true; // Default to suitable
        }

        private bool IsTimeAppropriate(float startTime, float duration)
        {
            float currentHour = currentGameTime % 24f;
            return IsTimeInWindow(currentHour, startTime, duration);
        }

        private bool IsTimeInWindow(float currentTime, float startTime, float duration)
        {
            float endTime = (startTime + duration) % 24f;
            
            if (startTime <= endTime)
            {
                return currentTime >= startTime && currentTime <= endTime;
            }
            else // Window crosses midnight
            {
                return currentTime >= startTime || currentTime <= endTime;
            }
        }

        private bool IsSeasonAppropriate(int requiredSeason)
        {
            return currentSeason == requiredSeason;
        }
        #endregion

        #region Utility Methods
        private float GetDefaultRoutineDuration(ExpandedKingdomSystem.Kingdom.NPCType npcType)
        {
            // Return default duration in hours based on NPC type
            switch (npcType)
            {
                case ExpandedKingdomSystem.Kingdom.NPCType.FlameKeeper:
                    return 2f;
                case ExpandedKingdomSystem.Kingdom.NPCType.BrassSmith:
                    return 6f;
                case ExpandedKingdomSystem.Kingdom.NPCType.FireDancer:
                    return 1f;
                default:
                    return 3f;
            }
        }

        private Vector3 GetKingdomNPCStartPosition(ExpandedKingdomSystem.Kingdom.NPCType npcType, ExpandedKingdomSystem.Kingdom kingdom)
        {
            // Return appropriate start position based on NPC type and kingdom
            return Vector3.zero; // Placeholder
        }

        private Vector3 GetKingdomNPCEndPosition(ExpandedKingdomSystem.Kingdom.NPCType npcType, ExpandedKingdomSystem.Kingdom kingdom)
        {
            // Return appropriate end position based on NPC type and kingdom
            return Vector3.forward * 10f; // Placeholder
        }

        private string GetKingdomNPCActivity(ExpandedKingdomSystem.Kingdom.NPCType npcType)
        {
            return npcType.ToString().ToLower();
        }

        private bool IsNPCWeatherDependent(ExpandedKingdomSystem.Kingdom.NPCType npcType)
        {
            // Some NPCs are more affected by weather than others
            switch (npcType)
            {
                case ExpandedKingdomSystem.Kingdom.NPCType.FireDancer:
                case ExpandedKingdomSystem.Kingdom.NPCType.AshCollector:
                    return true;
                default:
                    return false;
            }
        }

        private Vector3 GetVillageRoutineStartPosition(ExpandedKingdomSystem.Village.DailyRoutine routine, ExpandedKingdomSystem.Village village)
        {
            return Vector3.zero; // Placeholder
        }

        private Vector3 GetVillageRoutineEndPosition(ExpandedKingdomSystem.Village.DailyRoutine routine, ExpandedKingdomSystem.Village village)
        {
            return Vector3.forward * 5f; // Placeholder
        }

        private Vector3 GetExpandedVillageRoutineStartPosition(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine, ComprehensiveWorldExpansion.ExpandedVillage village)
        {
            return Vector3.zero; // Placeholder
        }

        private Vector3 GetExpandedVillageRoutineEndPosition(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine, ComprehensiveWorldExpansion.ExpandedVillage village)
        {
            return Vector3.forward * 8f; // Placeholder
        }

        private WeatherSystem.WeatherType GetRequiredWeatherForRoutine(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine)
        {
            // Determine required weather based on routine type
            if (routine.routineName.Contains("Harvest"))
                return WeatherSystem.WeatherType.ClearSkies;
            
            return WeatherSystem.WeatherType.ClearSkies; // Default
        }

        private int GetRequiredSeasonForRoutine(ComprehensiveWorldExpansion.ExpandedVillage.DailyRoutine routine)
        {
            // Determine required season based on routine type
            if (routine.routineName.Contains("Harvest"))
                return 2; // Autumn
            
            return 0; // Default to spring
        }

        private void DisplayDebugInfo()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("NPC Routine Manager Debug");
            GUILayout.Label($"Active Routines: {activeRoutines.Count}");
            GUILayout.Label($"Game Time: {currentGameTime:F1}");
            GUILayout.Label($"Weather: {currentWeather}");
            GUILayout.Label($"Season: {currentSeason}");
            
            if (activeRoutines.Count > 0)
            {
                GUILayout.Label("Recent Routines:");
                for (int i = 0; i < Mathf.Min(5, activeRoutines.Count); i++)
                {
                    var routine = activeRoutines[i];
                    GUILayout.Label($"- {routine.npcType} in {routine.location}");
                }
            }
            
            GUILayout.EndArea();
        }
        #endregion

        #region Public API
        public void StartKingdomRoutines(ExpandedKingdomSystem.Kingdom kingdom)
        {
            // Start kingdom-specific routines
            foreach (var npcType in kingdom.npcTypes)
            {
                StartKingdomNPCRoutine(npcType, kingdom);
            }
        }

        public void StartVillageRoutines(ExpandedKingdomSystem.Village village)
        {
            // Start village-specific routines
            foreach (var routine in village.dailyRoutines)
            {
                if (ShouldStartDailyRoutine(routine))
                {
                    StartVillageDailyRoutine(routine, village);
                }
            }
        }

        public void StopAllRoutines()
        {
            foreach (var routine in activeRoutines)
            {
                routine.isCancelled = true;
            }
            activeRoutines.Clear();
        }

        public void PauseAllRoutines()
        {
            foreach (var routine in activeRoutines)
            {
                routine.isPaused = true;
            }
        }

        public void ResumeAllRoutines()
        {
            foreach (var routine in activeRoutines)
            {
                routine.isPaused = false;
            }
        }

        public List<ActiveNPCRoutine> GetActiveRoutines()
        {
            return new List<ActiveNPCRoutine>(activeRoutines);
        }

        public List<ActiveNPCRoutine> GetRoutinesForLocation(string location)
        {
            return activeRoutines.FindAll(r => r.location == location);
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class ActiveNPCRoutine
    {
        public string routineId;
        public string npcType;
        public string location;
        public float startTime;
        public float duration;
        public float elapsedTime;
        public Vector3 startPosition;
        public Vector3 endPosition;
        public string currentActivity;
        public string culturalContext;
        public bool isWeatherDependent;
        public bool isTimeDependent;
        public bool isSeasonDependent;
        public bool isRepeating;
        public float repeatInterval;
        public WeatherSystem.WeatherType requiredWeather;
        public int requiredSeason;
        public bool isCompleted;
        public bool isPaused;
        public bool isCancelled;
        public NPCController npcController;
    }

    [System.Serializable]
    public class NPCBehaviorState
    {
        public string npcId;
        public string currentCulturalContext;
        public Dictionary<string, float> behaviorModifiers = new Dictionary<string, float>();
        public string currentEmotionalState;
        public float lastBehaviorUpdate;
    }

    // Placeholder for NPCController - would be implemented separately
    public class NPCController : MonoBehaviour
    {
        public void SetTargetPosition(Vector3 position) { }
        public void SetCurrentActivity(string activity) { }
        public void OnRoutineCompleted(ActiveNPCRoutine routine) { }
        public void OnRoutinePaused(ActiveNPCRoutine routine) { }
        public void SetBehaviorModifier(string modifier, float value) { }
        public void SetAnimationTrigger(string trigger) { }
        public void SetMovementSpeed(float speed) { }
        public void SetEmotionalState(string state) { }
        public void SetDialogueStyle(string style) { }
        public void SetMovementPrecision(float precision) { }
    }
    #endregion
}
