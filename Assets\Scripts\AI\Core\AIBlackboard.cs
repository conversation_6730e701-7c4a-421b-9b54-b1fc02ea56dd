using UnityEngine;
using UnityEngine.AI;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// AI Blackboard for sharing data between AI states.
    /// Contains all necessary information for AI decision making.
    /// </summary>
    [System.Serializable]
    public class AIBlackboard
    {
        #region Component References
        public NavMeshAgent Agent { get; set; }
        public EnemyHealth Health { get; set; }
        public Animator Animator { get; set; }
        public AudioSource AudioSource { get; set; }
        public Transform Transform { get; set; }
        public Transform PlayerTransform { get; set; }
        #endregion

        #region AI Configuration
        public float DetectionRange { get; set; } = 10f;
        public float AttackRange { get; set; } = 2f;
        public float FleeHealthThreshold { get; set; } = 0.2f;
        public float PatrolRadius { get; set; } = 5f;
        public float ChaseSpeed { get; set; } = 3.5f;
        public float PatrolSpeed { get; set; } = 1.5f;
        #endregion

        #region State Information
        public AIStateType CurrentStateType { get; set; } = AIStateType.Idle;
        public float StateEnterTime { get; set; }
        public float TimeSinceStateChange => Time.time - StateEnterTime;
        #endregion

        #region Target Information
        public bool IsPlayerDetected { get; set; }
        public float DistanceToTarget { get; set; }
        public Vector3 LastKnownPlayerPosition { get; set; }
        public float LastPlayerSightTime { get; set; }
        public float TimeSincePlayerSeen => Time.time - LastPlayerSightTime;
        #endregion

        #region Health and Status
        public float HealthPercentage { get; set; } = 1f;
        public bool IsDead { get; set; }
        public bool IsStunned { get; set; }
        public float StunDuration { get; set; }
        public float StunStartTime { get; set; }
        #endregion

        #region Patrol Information
        public Vector3 OriginalPosition { get; set; }
        public Vector3 CurrentPatrolTarget { get; set; }
        public int CurrentPatrolIndex { get; set; }
        public Vector3[] PatrolPoints { get; set; }
        public float PatrolWaitTime { get; set; } = 2f;
        public float PatrolWaitStartTime { get; set; }
        #endregion

        #region Investigation
        public Vector3 InvestigationTarget { get; set; }
        public bool IsInvestigating { get; set; }
        public float InvestigationStartTime { get; set; }
        public float InvestigationDuration { get; set; } = 5f;
        #endregion

        #region Combat Information
        public float LastAttackTime { get; set; }
        public float AttackCooldown { get; set; } = 2f;
        public bool CanAttack => Time.time - LastAttackTime >= AttackCooldown;
        public float CombatEngagementTime { get; set; }
        public bool IsInCombat { get; set; }
        #endregion

        #region Animation Hashes (for performance)
        public static readonly int SpeedHash = Animator.StringToHash("Speed");
        public static readonly int AttackHash = Animator.StringToHash("Attack");
        public static readonly int DeathHash = Animator.StringToHash("Death");
        public static readonly int StunnedHash = Animator.StringToHash("Stunned");
        public static readonly int AlertHash = Animator.StringToHash("Alert");
        #endregion

        #region Utility Methods
        /// <summary>
        /// Check if enough time has passed since state change.
        /// </summary>
        /// <param name="duration">Duration to check</param>
        /// <returns>True if enough time has passed</returns>
        public bool HasBeenInStateFor(float duration)
        {
            return TimeSinceStateChange >= duration;
        }

        /// <summary>
        /// Check if player was seen recently.
        /// </summary>
        /// <param name="timeThreshold">Time threshold in seconds</param>
        /// <returns>True if player was seen within threshold</returns>
        public bool WasPlayerSeenRecently(float timeThreshold = 5f)
        {
            return TimeSincePlayerSeen <= timeThreshold;
        }

        /// <summary>
        /// Update player sight information.
        /// </summary>
        public void UpdatePlayerSight()
        {
            if (IsPlayerDetected)
            {
                LastPlayerSightTime = Time.time;
                if (PlayerTransform != null)
                {
                    LastKnownPlayerPosition = PlayerTransform.position;
                }
            }
        }

        /// <summary>
        /// Check if AI should flee based on health.
        /// </summary>
        /// <returns>True if should flee</returns>
        public bool ShouldFlee()
        {
            return HealthPercentage <= FleeHealthThreshold && !IsDead;
        }

        /// <summary>
        /// Check if target is within attack range.
        /// </summary>
        /// <returns>True if in attack range</returns>
        public bool IsTargetInAttackRange()
        {
            return DistanceToTarget <= AttackRange;
        }

        /// <summary>
        /// Check if target is within detection range.
        /// </summary>
        /// <returns>True if in detection range</returns>
        public bool IsTargetInDetectionRange()
        {
            return DistanceToTarget <= DetectionRange;
        }

        /// <summary>
        /// Set animation parameter safely.
        /// </summary>
        /// <param name="hash">Parameter hash</param>
        /// <param name="value">Value to set</param>
        public void SetAnimationFloat(int hash, float value)
        {
            if (Animator != null)
            {
                Animator.SetFloat(hash, value);
            }
        }

        /// <summary>
        /// Set animation parameter safely.
        /// </summary>
        /// <param name="hash">Parameter hash</param>
        /// <param name="value">Value to set</param>
        public void SetAnimationBool(int hash, bool value)
        {
            if (Animator != null)
            {
                Animator.SetBool(hash, value);
            }
        }

        /// <summary>
        /// Trigger animation safely.
        /// </summary>
        /// <param name="hash">Trigger hash</param>
        public void TriggerAnimation(int hash)
        {
            if (Animator != null)
            {
                Animator.SetTrigger(hash);
            }
        }

        /// <summary>
        /// Play audio clip safely.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        /// <param name="volume">Volume level</param>
        public void PlayAudio(AudioClip clip, float volume = 1f)
        {
            if (AudioSource != null && clip != null)
            {
                AudioSource.PlayOneShot(clip, volume);
            }
        }

        /// <summary>
        /// Set NavMesh agent destination safely.
        /// </summary>
        /// <param name="destination">Destination position</param>
        /// <returns>True if destination was set successfully</returns>
        public bool SetDestination(Vector3 destination)
        {
            if (Agent != null && Agent.isActiveAndEnabled && Agent.isOnNavMesh)
            {
                return Agent.SetDestination(destination);
            }
            return false;
        }

        /// <summary>
        /// Stop NavMesh agent movement safely.
        /// </summary>
        public void StopMovement()
        {
            if (Agent != null && Agent.isActiveAndEnabled)
            {
                Agent.ResetPath();
                Agent.velocity = Vector3.zero;
            }
        }

        /// <summary>
        /// Set NavMesh agent speed safely.
        /// </summary>
        /// <param name="speed">New speed value</param>
        public void SetAgentSpeed(float speed)
        {
            if (Agent != null && Agent.isActiveAndEnabled)
            {
                Agent.speed = speed;
            }
        }

        /// <summary>
        /// Check if agent has reached destination.
        /// </summary>
        /// <param name="threshold">Distance threshold</param>
        /// <returns>True if destination reached</returns>
        public bool HasReachedDestination(float threshold = 0.5f)
        {
            if (Agent == null || !Agent.isActiveAndEnabled) return true;
            
            return !Agent.pathPending && Agent.remainingDistance < threshold;
        }

        /// <summary>
        /// Get random position within patrol radius.
        /// </summary>
        /// <returns>Random patrol position</returns>
        public Vector3 GetRandomPatrolPosition()
        {
            Vector2 randomCircle = Random.insideUnitCircle * PatrolRadius;
            Vector3 randomPosition = OriginalPosition + new Vector3(randomCircle.x, 0, randomCircle.y);
            
            // Sample NavMesh to ensure valid position
            if (NavMesh.SamplePosition(randomPosition, out NavMeshHit hit, PatrolRadius, NavMesh.AllAreas))
            {
                return hit.position;
            }
            
            return OriginalPosition;
        }

        /// <summary>
        /// Reset blackboard to initial state.
        /// </summary>
        public void Reset()
        {
            IsPlayerDetected = false;
            DistanceToTarget = float.MaxValue;
            LastKnownPlayerPosition = Vector3.zero;
            LastPlayerSightTime = 0f;
            IsInvestigating = false;
            IsInCombat = false;
            IsStunned = false;
            CurrentPatrolIndex = 0;
        }
        #endregion
    }
}
