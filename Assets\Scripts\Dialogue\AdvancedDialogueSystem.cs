using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Collections;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.Dialogue
{
    /// <summary>
    /// Advanced Dialogue System for Cinder of Darkness.
    /// Manages branching dialogue trees, character traits, and voice acting integration.
    /// </summary>
    public class AdvancedDialogueSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Dialogue Settings")]
        [SerializeField] private bool enableDialogue = true;
        [SerializeField] private float textSpeed = 0.05f;
        [SerializeField] private float autoAdvanceDelay = 3f;
        [SerializeField] private bool enableVoiceActing = true;

        [Header("Character Traits")]
        [SerializeField] private PlayerTrait[] playerTraits;
        [SerializeField] private float charismaThreshold = 50f;
        [SerializeField] private float moralityThreshold = 50f;

        [Header("Dialogue Database")]
        [SerializeField] private DialogueData[] dialogueDatabase;
        [SerializeField] private CharacterData[] characterDatabase;

        [Header("UI References")]
        [SerializeField] private GameObject dialogueUI;
        [SerializeField] private TMPro.TextMeshProUGUI speakerNameText;
        [SerializeField] private TMPro.TextMeshProUGUI dialogueText;
        [SerializeField] private Transform choiceContainer;
        [SerializeField] private GameObject choiceButtonPrefab;
        [SerializeField] private UnityEngine.UI.Image characterPortrait;

        [Header("Voice Acting")]
        [SerializeField] private AudioSource voiceAudioSource;
        [SerializeField] private AudioSource sfxAudioSource;
        [SerializeField] private AudioClip dialogueStartSound;
        [SerializeField] private AudioClip dialogueEndSound;
        [SerializeField] private AudioClip choiceSelectSound;

        [Header("Visual Effects")]
        [SerializeField] private Animator dialogueAnimator;
        [SerializeField] private ParticleSystem dialogueEffect;
        #endregion

        #region Public Properties
        public static AdvancedDialogueSystem Instance { get; private set; }
        public bool IsDialogueActive { get; private set; }
        public DialogueNode CurrentNode { get; private set; }
        public CharacterData CurrentSpeaker { get; private set; }
        public Dictionary<string, object> DialogueMemory { get; private set; } = new Dictionary<string, object>();
        #endregion

        #region Private Fields
        private DynamicNarrativeSystem narrativeSystem;
        private PlayerController playerController;

        // Dialogue state
        private DialogueData currentDialogue;
        private Queue<DialogueNode> dialogueQueue = new Queue<DialogueNode>();
        private List<DialogueChoice> currentChoices = new List<DialogueChoice>();

        // Text display
        private Coroutine textDisplayCoroutine;
        private bool isDisplayingText = false;
        private bool skipTextAnimation = false;

        // Voice acting
        private Dictionary<string, AudioClip[]> characterVoiceClips = new Dictionary<string, AudioClip[]>();
        private Coroutine voicePlaybackCoroutine;

        // Dialogue memory and history
        private Dictionary<string, DialogueHistory> characterDialogueHistory = new Dictionary<string, DialogueHistory>();
        private List<string> conversationHistory = new List<string>();

        // Trait system
        private Dictionary<string, float> currentTraitValues = new Dictionary<string, float>();

        // Performance optimization
        private float lastTraitUpdate;
        private const float traitUpdateInterval = 1f;
        #endregion

        #region Events
        public System.Action<DialogueData> OnDialogueStarted;
        public System.Action<DialogueData> OnDialogueEnded;
        public System.Action<DialogueNode> OnNodeDisplayed;
        public System.Action<DialogueChoice> OnChoiceSelected;
        public System.Action<string, float> OnTraitChanged;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDialogueSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            InitializeTraits();
            LoadDialogueData();
            SetupUI();
        }

        private void Update()
        {
            if (!enableDialogue) return;

            HandleDialogueInput();
            UpdateTraits();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the dialogue system.
        /// </summary>
        private void InitializeDialogueSystem()
        {
            if (voiceAudioSource == null)
            {
                voiceAudioSource = gameObject.AddComponent<AudioSource>();
                voiceAudioSource.playOnAwake = false;
            }

            if (sfxAudioSource == null)
            {
                sfxAudioSource = gameObject.AddComponent<AudioSource>();
                sfxAudioSource.playOnAwake = false;
            }

            Debug.Log("Advanced Dialogue System initialized");
        }

        /// <summary>
        /// Setup system references.
        /// </summary>
        private void SetupSystemReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            playerController = FindObjectOfType<PlayerController>();
        }

        /// <summary>
        /// Initialize player traits.
        /// </summary>
        private void InitializeTraits()
        {
            foreach (var trait in playerTraits)
            {
                currentTraitValues[trait.traitName] = trait.startingValue;
            }
        }

        /// <summary>
        /// Setup UI components.
        /// </summary>
        private void SetupUI()
        {
            if (dialogueUI != null)
            {
                dialogueUI.SetActive(false);
            }
        }
        #endregion

        #region Dialogue Management
        /// <summary>
        /// Start dialogue with character.
        /// </summary>
        /// <param name="dialogueId">Dialogue ID</param>
        /// <param name="characterId">Character ID</param>
        public void StartDialogue(string dialogueId, string characterId)
        {
            if (IsDialogueActive) return;

            var dialogue = GetDialogueData(dialogueId);
            var character = GetCharacterData(characterId);

            if (dialogue == null || character == null) return;

            currentDialogue = dialogue;
            CurrentSpeaker = character;
            IsDialogueActive = true;

            // Disable player controls
            if (playerController != null)
            {
                playerController.enabled = false;
            }

            // Show dialogue UI
            if (dialogueUI != null)
            {
                dialogueUI.SetActive(true);
            }

            // Setup character portrait
            if (characterPortrait != null && character.portrait != null)
            {
                characterPortrait.sprite = character.portrait;
            }

            // Play start sound
            PlaySFX(dialogueStartSound);

            // Start dialogue
            ProcessDialogueNode(dialogue.startNode);
            OnDialogueStarted?.Invoke(dialogue);

            Debug.Log($"Started dialogue: {dialogue.dialogueTitle} with {character.characterName}");
        }

        /// <summary>
        /// End current dialogue.
        /// </summary>
        public void EndDialogue()
        {
            if (!IsDialogueActive) return;

            // Stop any ongoing text display
            if (textDisplayCoroutine != null)
            {
                StopCoroutine(textDisplayCoroutine);
            }

            // Stop voice playback
            if (voicePlaybackCoroutine != null)
            {
                StopCoroutine(voicePlaybackCoroutine);
            }

            // Hide dialogue UI
            if (dialogueUI != null)
            {
                dialogueUI.SetActive(false);
            }

            // Re-enable player controls
            if (playerController != null)
            {
                playerController.enabled = true;
            }

            // Play end sound
            PlaySFX(dialogueEndSound);

            // Record dialogue completion
            RecordDialogueCompletion();

            OnDialogueEnded?.Invoke(currentDialogue);

            IsDialogueActive = false;
            currentDialogue = null;
            CurrentNode = null;
            CurrentSpeaker = null;
            dialogueQueue.Clear();
            currentChoices.Clear();

            Debug.Log("Dialogue ended");
        }

        /// <summary>
        /// Process dialogue node.
        /// </summary>
        /// <param name="node">Dialogue node to process</param>
        private void ProcessDialogueNode(DialogueNode node)
        {
            if (node == null) return;

            CurrentNode = node;

            // Check node conditions
            if (!EvaluateNodeConditions(node))
            {
                // Skip to next node or end dialogue
                if (node.choices.Length > 0)
                {
                    ProcessDialogueNode(node.choices[0].targetNode);
                }
                else
                {
                    EndDialogue();
                }
                return;
            }

            // Update speaker name
            if (speakerNameText != null)
            {
                speakerNameText.text = GetSpeakerName(node);
            }

            // Display dialogue text
            DisplayDialogueText(node.dialogueText);

            // Apply node effects
            ApplyNodeEffects(node);

            // Setup choices
            SetupChoices(node);

            OnNodeDisplayed?.Invoke(node);
        }

        /// <summary>
        /// Display dialogue text with typewriter effect.
        /// </summary>
        /// <param name="text">Text to display</param>
        private void DisplayDialogueText(string text)
        {
            if (textDisplayCoroutine != null)
            {
                StopCoroutine(textDisplayCoroutine);
            }

            // Process text for dynamic content
            string processedText = ProcessDialogueText(text);

            textDisplayCoroutine = StartCoroutine(TypewriterEffect(processedText));
        }

        /// <summary>
        /// Typewriter effect coroutine.
        /// </summary>
        /// <param name="text">Text to display</param>
        /// <returns>Typewriter coroutine</returns>
        private IEnumerator TypewriterEffect(string text)
        {
            isDisplayingText = true;
            skipTextAnimation = false;

            if (dialogueText != null)
            {
                dialogueText.text = "";

                // Play voice clip if available
                if (enableVoiceActing)
                {
                    PlayVoiceClip(CurrentNode);
                }

                for (int i = 0; i <= text.Length; i++)
                {
                    if (skipTextAnimation)
                    {
                        dialogueText.text = text;
                        break;
                    }

                    dialogueText.text = text.Substring(0, i);
                    yield return new WaitForSeconds(textSpeed);
                }
            }

            isDisplayingText = false;
        }

        /// <summary>
        /// Setup dialogue choices.
        /// </summary>
        /// <param name="node">Current dialogue node</param>
        private void SetupChoices(DialogueNode node)
        {
            // Clear existing choices
            ClearChoices();
            currentChoices.Clear();

            if (node.choices == null || node.choices.Length == 0)
            {
                // Auto-advance or end dialogue
                if (node.autoAdvance)
                {
                    StartCoroutine(AutoAdvanceDialogue());
                }
                else
                {
                    EndDialogue();
                }
                return;
            }

            // Create choice buttons
            foreach (var choice in node.choices)
            {
                if (EvaluateChoiceConditions(choice))
                {
                    CreateChoiceButton(choice);
                    currentChoices.Add(choice);
                }
            }
        }

        /// <summary>
        /// Create choice button UI.
        /// </summary>
        /// <param name="choice">Dialogue choice</param>
        private void CreateChoiceButton(DialogueChoice choice)
        {
            if (choiceContainer == null || choiceButtonPrefab == null) return;

            GameObject choiceObject = Instantiate(choiceButtonPrefab, choiceContainer);
            var button = choiceObject.GetComponent<UnityEngine.UI.Button>();
            var text = choiceObject.GetComponentInChildren<TMPro.TextMeshProUGUI>();

            if (text != null)
            {
                string choiceText = ProcessDialogueText(choice.choiceText);

                // Add trait indicators
                if (choice.requiredTrait != null)
                {
                    choiceText += GetTraitIndicator(choice.requiredTrait, choice.requiredTraitValue);
                }

                text.text = choiceText;
            }

            if (button != null)
            {
                button.onClick.AddListener(() => SelectChoice(choice));
            }
        }

        /// <summary>
        /// Select dialogue choice.
        /// </summary>
        /// <param name="choice">Selected choice</param>
        private void SelectChoice(DialogueChoice choice)
        {
            PlaySFX(choiceSelectSound);

            // Apply choice effects
            ApplyChoiceEffects(choice);

            // Record choice in memory
            RecordChoiceInMemory(choice);

            OnChoiceSelected?.Invoke(choice);

            // Continue to next node
            if (choice.targetNode != null)
            {
                ProcessDialogueNode(choice.targetNode);
            }
            else
            {
                EndDialogue();
            }
        }

        /// <summary>
        /// Clear choice buttons.
        /// </summary>
        private void ClearChoices()
        {
            if (choiceContainer == null) return;

            foreach (Transform child in choiceContainer)
            {
                Destroy(child.gameObject);
            }
        }

        /// <summary>
        /// Auto-advance dialogue after delay.
        /// </summary>
        /// <returns>Auto-advance coroutine</returns>
        private IEnumerator AutoAdvanceDialogue()
        {
            yield return new WaitForSeconds(autoAdvanceDelay);

            if (CurrentNode?.nextNode != null)
            {
                ProcessDialogueNode(CurrentNode.nextNode);
            }
            else
            {
                EndDialogue();
            }
        }
        #endregion

        #region Text Processing
        /// <summary>
        /// Process dialogue text for dynamic content.
        /// </summary>
        /// <param name="text">Raw dialogue text</param>
        /// <returns>Processed dialogue text</returns>
        private string ProcessDialogueText(string text)
        {
            // Replace player name
            text = text.Replace("{PLAYER_NAME}", GetPlayerName());

            // Replace trait values
            foreach (var kvp in currentTraitValues)
            {
                text = text.Replace($"{{{kvp.Key.ToUpper()}}}", kvp.Value.ToString("F0"));
            }

            // Replace dialogue memory variables
            foreach (var kvp in DialogueMemory)
            {
                text = text.Replace($"{{{kvp.Key.ToUpper()}}}", kvp.Value.ToString());
            }

            // Replace narrative flags
            if (narrativeSystem != null)
            {
                // This would integrate with the narrative system for story flags
            }

            return text;
        }

        /// <summary>
        /// Get player name for dialogue.
        /// </summary>
        /// <returns>Player name</returns>
        private string GetPlayerName()
        {
            // Would get from player data or character creation
            return "Ashen One";
        }

        /// <summary>
        /// Get speaker name for current node.
        /// </summary>
        /// <param name="node">Dialogue node</param>
        /// <returns>Speaker name</returns>
        private string GetSpeakerName(DialogueNode node)
        {
            if (!string.IsNullOrEmpty(node.speakerOverride))
            {
                return node.speakerOverride;
            }

            return CurrentSpeaker?.characterName ?? "Unknown";
        }

        /// <summary>
        /// Get trait indicator for choice.
        /// </summary>
        /// <param name="traitName">Trait name</param>
        /// <param name="requiredValue">Required value</param>
        /// <returns>Trait indicator string</returns>
        private string GetTraitIndicator(string traitName, float requiredValue)
        {
            float currentValue = GetTraitValue(traitName);

            if (currentValue >= requiredValue)
            {
                return $" <color=green>[{traitName}]</color>";
            }
            else
            {
                return $" <color=red>[{traitName} {requiredValue}]</color>";
            }
        }
        #endregion

        #region Condition Evaluation
        /// <summary>
        /// Evaluate node conditions.
        /// </summary>
        /// <param name="node">Dialogue node</param>
        /// <returns>True if conditions are met</returns>
        private bool EvaluateNodeConditions(DialogueNode node)
        {
            if (node.conditions == null || node.conditions.Length == 0) return true;

            foreach (var condition in node.conditions)
            {
                if (!EvaluateCondition(condition)) return false;
            }

            return true;
        }

        /// <summary>
        /// Evaluate choice conditions.
        /// </summary>
        /// <param name="choice">Dialogue choice</param>
        /// <returns>True if conditions are met</returns>
        private bool EvaluateChoiceConditions(DialogueChoice choice)
        {
            // Check trait requirements
            if (!string.IsNullOrEmpty(choice.requiredTrait))
            {
                float traitValue = GetTraitValue(choice.requiredTrait);
                if (traitValue < choice.requiredTraitValue) return false;
            }

            // Check other conditions
            if (choice.conditions != null)
            {
                foreach (var condition in choice.conditions)
                {
                    if (!EvaluateCondition(condition)) return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Evaluate individual condition.
        /// </summary>
        /// <param name="condition">Condition to evaluate</param>
        /// <returns>True if condition is met</returns>
        private bool EvaluateCondition(DialogueCondition condition)
        {
            switch (condition.conditionType)
            {
                case ConditionType.Trait:
                    return EvaluateTraitCondition(condition);
                case ConditionType.StoryFlag:
                    return EvaluateStoryFlagCondition(condition);
                case ConditionType.DialogueMemory:
                    return EvaluateDialogueMemoryCondition(condition);
                case ConditionType.Item:
                    return EvaluateItemCondition(condition);
                default:
                    return true;
            }
        }

        /// <summary>
        /// Evaluate trait condition.
        /// </summary>
        /// <param name="condition">Trait condition</param>
        /// <returns>True if trait condition is met</returns>
        private bool EvaluateTraitCondition(DialogueCondition condition)
        {
            float traitValue = GetTraitValue(condition.parameter);

            switch (condition.comparison)
            {
                case ComparisonType.GreaterThan:
                    return traitValue > condition.value;
                case ComparisonType.LessThan:
                    return traitValue < condition.value;
                case ComparisonType.EqualTo:
                    return Mathf.Approximately(traitValue, condition.value);
                case ComparisonType.GreaterThanOrEqual:
                    return traitValue >= condition.value;
                case ComparisonType.LessThanOrEqual:
                    return traitValue <= condition.value;
                default:
                    return true;
            }
        }

        /// <summary>
        /// Evaluate story flag condition.
        /// </summary>
        /// <param name="condition">Story flag condition</param>
        /// <returns>True if story flag condition is met</returns>
        private bool EvaluateStoryFlagCondition(DialogueCondition condition)
        {
            if (narrativeSystem == null) return false;

            bool flagValue = narrativeSystem.IsStoryFlagSet(condition.parameter);
            return flagValue == (condition.value > 0);
        }

        /// <summary>
        /// Evaluate dialogue memory condition.
        /// </summary>
        /// <param name="condition">Dialogue memory condition</param>
        /// <returns>True if dialogue memory condition is met</returns>
        private bool EvaluateDialogueMemoryCondition(DialogueCondition condition)
        {
            if (!DialogueMemory.ContainsKey(condition.parameter)) return false;

            var memoryValue = DialogueMemory[condition.parameter];

            if (memoryValue is float floatValue)
            {
                switch (condition.comparison)
                {
                    case ComparisonType.GreaterThan:
                        return floatValue > condition.value;
                    case ComparisonType.LessThan:
                        return floatValue < condition.value;
                    case ComparisonType.EqualTo:
                        return Mathf.Approximately(floatValue, condition.value);
                    default:
                        return true;
                }
            }
            else if (memoryValue is bool boolValue)
            {
                return boolValue == (condition.value > 0);
            }

            return false;
        }

        /// <summary>
        /// Evaluate item condition.
        /// </summary>
        /// <param name="condition">Item condition</param>
        /// <returns>True if item condition is met</returns>
        private bool EvaluateItemCondition(DialogueCondition condition)
        {
            // Would check player inventory
            return false; // Placeholder
        }
        #endregion

        #region Effects and Memory
        /// <summary>
        /// Apply node effects.
        /// </summary>
        /// <param name="node">Dialogue node</param>
        private void ApplyNodeEffects(DialogueNode node)
        {
            if (node.effects == null) return;

            foreach (var effect in node.effects)
            {
                ApplyEffect(effect);
            }
        }

        /// <summary>
        /// Apply choice effects.
        /// </summary>
        /// <param name="choice">Dialogue choice</param>
        private void ApplyChoiceEffects(DialogueChoice choice)
        {
            if (choice.effects == null) return;

            foreach (var effect in choice.effects)
            {
                ApplyEffect(effect);
            }
        }

        /// <summary>
        /// Apply individual effect.
        /// </summary>
        /// <param name="effect">Effect to apply</param>
        private void ApplyEffect(DialogueEffect effect)
        {
            switch (effect.effectType)
            {
                case EffectType.ModifyTrait:
                    ModifyTrait(effect.parameter, effect.value);
                    break;
                case EffectType.SetStoryFlag:
                    SetStoryFlag(effect.parameter, effect.value > 0);
                    break;
                case EffectType.SetDialogueMemory:
                    SetDialogueMemory(effect.parameter, effect.value);
                    break;
                case EffectType.GiveItem:
                    GiveItem(effect.parameter, (int)effect.value);
                    break;
                case EffectType.StartQuest:
                    StartQuest(effect.parameter);
                    break;
            }
        }

        /// <summary>
        /// Record choice in dialogue memory.
        /// </summary>
        /// <param name="choice">Selected choice</param>
        private void RecordChoiceInMemory(DialogueChoice choice)
        {
            string memoryKey = $"choice_{CurrentNode.nodeId}";
            DialogueMemory[memoryKey] = choice.choiceText;

            // Record in conversation history
            conversationHistory.Add($"{CurrentSpeaker.characterName}: {CurrentNode.dialogueText}");
            conversationHistory.Add($"Player: {choice.choiceText}");
        }

        /// <summary>
        /// Record dialogue completion.
        /// </summary>
        private void RecordDialogueCompletion()
        {
            if (currentDialogue == null || CurrentSpeaker == null) return;

            string characterId = CurrentSpeaker.characterId;

            if (!characterDialogueHistory.ContainsKey(characterId))
            {
                characterDialogueHistory[characterId] = new DialogueHistory
                {
                    characterId = characterId,
                    completedDialogues = new List<string>(),
                    characterMemory = new Dictionary<string, object>(),
                    lastInteractionTime = Time.time
                };
            }

            var history = characterDialogueHistory[characterId];
            if (!history.completedDialogues.Contains(currentDialogue.dialogueId))
            {
                history.completedDialogues.Add(currentDialogue.dialogueId);
            }

            history.lastInteractionTime = Time.time;
        }
        #endregion

        #region Trait System
        /// <summary>
        /// Update player traits.
        /// </summary>
        private void UpdateTraits()
        {
            if (Time.time - lastTraitUpdate < traitUpdateInterval) return;

            // Trait decay or natural progression could be implemented here

            lastTraitUpdate = Time.time;
        }

        /// <summary>
        /// Modify player trait.
        /// </summary>
        /// <param name="traitName">Trait name</param>
        /// <param name="value">Value to add</param>
        private void ModifyTrait(string traitName, float value)
        {
            if (!currentTraitValues.ContainsKey(traitName))
            {
                currentTraitValues[traitName] = 0f;
            }

            float oldValue = currentTraitValues[traitName];
            currentTraitValues[traitName] += value;

            // Apply trait limits
            var trait = playerTraits.FirstOrDefault(t => t.traitName == traitName);
            if (trait != null)
            {
                currentTraitValues[traitName] = Mathf.Clamp(currentTraitValues[traitName], trait.minValue, trait.maxValue);
            }

            OnTraitChanged?.Invoke(traitName, currentTraitValues[traitName]);

            Debug.Log($"Trait {traitName} changed from {oldValue} to {currentTraitValues[traitName]}");
        }

        /// <summary>
        /// Get trait value.
        /// </summary>
        /// <param name="traitName">Trait name</param>
        /// <returns>Trait value</returns>
        private float GetTraitValue(string traitName)
        {
            return currentTraitValues.ContainsKey(traitName) ? currentTraitValues[traitName] : 0f;
        }

        /// <summary>
        /// Set story flag.
        /// </summary>
        /// <param name="flagName">Flag name</param>
        /// <param name="value">Flag value</param>
        private void SetStoryFlag(string flagName, bool value)
        {
            if (narrativeSystem != null)
            {
                narrativeSystem.SetStoryFlag(flagName, value, "dialogue");
            }
        }

        /// <summary>
        /// Set dialogue memory.
        /// </summary>
        /// <param name="key">Memory key</param>
        /// <param name="value">Memory value</param>
        private void SetDialogueMemory(string key, object value)
        {
            DialogueMemory[key] = value;
        }

        /// <summary>
        /// Give item to player.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="quantity">Quantity</param>
        private void GiveItem(string itemId, int quantity)
        {
            // Would integrate with inventory system
            Debug.Log($"Giving player {quantity}x {itemId}");
        }

        /// <summary>
        /// Start quest.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        private void StartQuest(string questId)
        {
            // Would integrate with quest system
            Debug.Log($"Starting quest: {questId}");
        }
        #endregion

        #region Voice Acting
        /// <summary>
        /// Play voice clip for dialogue node.
        /// </summary>
        /// <param name="node">Dialogue node</param>
        private void PlayVoiceClip(DialogueNode node)
        {
            if (!enableVoiceActing || voiceAudioSource == null) return;

            AudioClip voiceClip = null;

            // Use node-specific voice clip if available
            if (node.voiceClip != null)
            {
                voiceClip = node.voiceClip;
            }
            else if (CurrentSpeaker != null && CurrentSpeaker.voiceClips != null && CurrentSpeaker.voiceClips.Length > 0)
            {
                // Use random voice clip from character
                voiceClip = CurrentSpeaker.voiceClips[Random.Range(0, CurrentSpeaker.voiceClips.Length)];
            }

            if (voiceClip != null)
            {
                voiceAudioSource.clip = voiceClip;
                voiceAudioSource.Play();
            }
        }

        /// <summary>
        /// Stop voice playback.
        /// </summary>
        private void StopVoicePlayback()
        {
            if (voiceAudioSource != null && voiceAudioSource.isPlaying)
            {
                voiceAudioSource.Stop();
            }
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle dialogue input.
        /// </summary>
        private void HandleDialogueInput()
        {
            if (!IsDialogueActive) return;

            // Skip text animation
            if (Input.GetKeyDown(KeyCode.Space) || Input.GetMouseButtonDown(0))
            {
                if (isDisplayingText)
                {
                    skipTextAnimation = true;
                }
                else if (currentChoices.Count == 0)
                {
                    // Auto-advance if no choices
                    if (CurrentNode?.nextNode != null)
                    {
                        ProcessDialogueNode(CurrentNode.nextNode);
                    }
                    else
                    {
                        EndDialogue();
                    }
                }
            }

            // Number key shortcuts for choices
            for (int i = 1; i <= 9 && i <= currentChoices.Count; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha0 + i))
                {
                    SelectChoice(currentChoices[i - 1]);
                    break;
                }
            }

            // Escape to end dialogue
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                EndDialogue();
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySFX(AudioClip clip)
        {
            if (clip != null && sfxAudioSource != null)
            {
                sfxAudioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Get dialogue data by ID.
        /// </summary>
        /// <param name="dialogueId">Dialogue ID</param>
        /// <returns>Dialogue data or null</returns>
        private DialogueData GetDialogueData(string dialogueId)
        {
            return dialogueDatabase?.FirstOrDefault(d => d.dialogueId == dialogueId);
        }

        /// <summary>
        /// Get character data by ID.
        /// </summary>
        /// <param name="characterId">Character ID</param>
        /// <returns>Character data or null</returns>
        private CharacterData GetCharacterData(string characterId)
        {
            return characterDatabase?.FirstOrDefault(c => c.characterId == characterId);
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save dialogue data.
        /// </summary>
        public void SaveDialogueData()
        {
            var saveData = new DialogueSaveData
            {
                currentTraitValues = currentTraitValues,
                dialogueMemory = DialogueMemory,
                characterDialogueHistory = characterDialogueHistory,
                conversationHistory = conversationHistory.ToArray()
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("AdvancedDialogueData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load dialogue data.
        /// </summary>
        private void LoadDialogueData()
        {
            string json = PlayerPrefs.GetString("AdvancedDialogueData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<DialogueSaveData>(json);

                    if (saveData.currentTraitValues != null)
                    {
                        currentTraitValues = saveData.currentTraitValues;
                    }

                    if (saveData.dialogueMemory != null)
                    {
                        DialogueMemory = saveData.dialogueMemory;
                    }

                    if (saveData.characterDialogueHistory != null)
                    {
                        characterDialogueHistory = saveData.characterDialogueHistory;
                    }

                    if (saveData.conversationHistory != null)
                    {
                        conversationHistory = saveData.conversationHistory.ToList();
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load dialogue data: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get trait value by name.
        /// </summary>
        /// <param name="traitName">Trait name</param>
        /// <returns>Trait value</returns>
        public float GetPlayerTrait(string traitName)
        {
            return GetTraitValue(traitName);
        }

        /// <summary>
        /// Set trait value.
        /// </summary>
        /// <param name="traitName">Trait name</param>
        /// <param name="value">New value</param>
        public void SetPlayerTrait(string traitName, float value)
        {
            currentTraitValues[traitName] = value;
            OnTraitChanged?.Invoke(traitName, value);
        }

        /// <summary>
        /// Check if dialogue was completed.
        /// </summary>
        /// <param name="characterId">Character ID</param>
        /// <param name="dialogueId">Dialogue ID</param>
        /// <returns>True if dialogue was completed</returns>
        public bool WasDialogueCompleted(string characterId, string dialogueId)
        {
            if (!characterDialogueHistory.ContainsKey(characterId)) return false;

            return characterDialogueHistory[characterId].completedDialogues.Contains(dialogueId);
        }

        /// <summary>
        /// Get conversation history.
        /// </summary>
        /// <returns>List of conversation entries</returns>
        public List<string> GetConversationHistory()
        {
            return new List<string>(conversationHistory);
        }

        /// <summary>
        /// Clear conversation history.
        /// </summary>
        public void ClearConversationHistory()
        {
            conversationHistory.Clear();
        }

        /// <summary>
        /// Reset dialogue system.
        /// </summary>
        public void ResetDialogueSystem()
        {
            currentTraitValues.Clear();
            DialogueMemory.Clear();
            characterDialogueHistory.Clear();
            conversationHistory.Clear();

            InitializeTraits();

            Debug.Log("Dialogue system reset");
        }

        /// <summary>
        /// Get dialogue statistics.
        /// </summary>
        /// <returns>Dialogue statistics</returns>
        public DialogueStats GetDialogueStats()
        {
            int totalDialogues = characterDialogueHistory.Values.Sum(h => h.completedDialogues.Count);
            int totalCharacters = characterDialogueHistory.Count;

            return new DialogueStats
            {
                totalDialoguesCompleted = totalDialogues,
                totalCharactersInteracted = totalCharacters,
                totalConversationEntries = conversationHistory.Count,
                averageCharisma = GetTraitValue("Charisma"),
                averageMorality = GetTraitValue("Morality")
            };
        }

        /// <summary>
        /// Force dialogue skip (for testing).
        /// </summary>
        public void ForceSkipDialogue()
        {
            if (IsDialogueActive)
            {
                EndDialogue();
            }
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class DialogueSaveData
    {
        public Dictionary<string, float> currentTraitValues;
        public Dictionary<string, object> dialogueMemory;
        public Dictionary<string, DialogueHistory> characterDialogueHistory;
        public string[] conversationHistory;
    }

    [System.Serializable]
    public class DialogueStats
    {
        public int totalDialoguesCompleted;
        public int totalCharactersInteracted;
        public int totalConversationEntries;
        public float averageCharisma;
        public float averageMorality;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class DialogueData
    {
        public string dialogueId;
        public string dialogueTitle;
        public DialogueNode startNode;
        public bool isRepeatable;
    }

    [System.Serializable]
    public class DialogueNode
    {
        public string nodeId;
        public string dialogueText;
        public string speakerOverride;
        public DialogueChoice[] choices;
        public DialogueCondition[] conditions;
        public DialogueEffect[] effects;
        public DialogueNode nextNode;
        public bool autoAdvance;
        public AudioClip voiceClip;
    }

    [System.Serializable]
    public class DialogueChoice
    {
        public string choiceText;
        public DialogueNode targetNode;
        public string requiredTrait;
        public float requiredTraitValue;
        public DialogueCondition[] conditions;
        public DialogueEffect[] effects;
    }

    [System.Serializable]
    public class DialogueCondition
    {
        public ConditionType conditionType;
        public string parameter;
        public ComparisonType comparison;
        public float value;
    }

    [System.Serializable]
    public class DialogueEffect
    {
        public EffectType effectType;
        public string parameter;
        public float value;
    }

    [System.Serializable]
    public class CharacterData
    {
        public string characterId;
        public string characterName;
        public Sprite portrait;
        public AudioClip[] voiceClips;
        public Color nameColor;
    }

    [System.Serializable]
    public class PlayerTrait
    {
        public string traitName;
        public float startingValue;
        public float minValue;
        public float maxValue;
    }

    [System.Serializable]
    public class DialogueHistory
    {
        public string characterId;
        public List<string> completedDialogues;
        public Dictionary<string, object> characterMemory;
        public float lastInteractionTime;
    }

    public enum ConditionType
    {
        Trait,
        StoryFlag,
        DialogueMemory,
        Item,
        Quest
    }

    public enum ComparisonType
    {
        GreaterThan,
        LessThan,
        EqualTo,
        GreaterThanOrEqual,
        LessThanOrEqual
    }

    public enum EffectType
    {
        ModifyTrait,
        SetStoryFlag,
        SetDialogueMemory,
        GiveItem,
        StartQuest
    }
    #endregion
}
