using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class FaithOfTheFlameSystem : MonoBehaviour
{
    [Header("Faith Core")]
    public float emberOfCreation = 100f; // Player's inner spark
    public float faithLevel = 50f;
    public List<SacredRitual> performedRituals = new List<SacredRitual>();
    public List<FlameTemple> flameTemples = new List<FlameTemple>();
    
    [Header("Spiritual Beliefs")]
    public FaithTenet[] faithTenets;
    public SacredOffering[] availableOfferings;
    public Dictionary<string, float> spiritualConnections = new Dictionary<string, float>();
    
    [Header("Ritual Effects")]
    public GameObject ashPrayerEffect;
    public GameObject smokeOathEffect;
    public AudioClip[] ritualChants;
    public AudioClip[] spiritualWhispers;
    
    private PsychologicalSystem psycheSystem;
    private PlayerStats playerStats;
    private GameManager gameManager;
    
    [System.Serializable]
    public class FaithTenet
    {
        [Header("Tenet Identity")]
        public string tenetName;
        public string description;
        public TenetType type;
        public float spiritualWeight;
        
        [Header("Belief System")]
        public string[] coreBeliefs;
        public string[] practices;
        public string[] consequences;
        
        public enum TenetType
        {
            InnerSpark,      // All beings carry an ember of creation
            SpiritualMemory, // The dead live on in ash and smoke
            FlameWisdom,     // Fire reveals truth and burns away lies
            AshRedemption,   // From destruction comes renewal
            SacredOffering,  // Sacrifice strengthens the inner flame
            EternalCycle     // Death and rebirth through flame
        }
    }
    
    [System.Serializable]
    public class SacredRitual
    {
        [Header("Ritual Identity")]
        public string ritualName;
        public RitualType type;
        public string description;
        public float spiritualCost;
        public float spiritualGain;
        
        [Header("Requirements")]
        public string[] requiredItems;
        public float minimumFaith;
        public string[] forbiddenConditions;
        
        [Header("Effects")]
        public RitualEffect[] effects;
        public string[] unlockableWisdom;
        public float performanceTime;
        
        [Header("Lore")]
        public string origin;
        public string significance;
        public string[] variations;
        
        public enum RitualType
        {
            AshPrayer,       // Burn beloved object for forgiveness
            SmokeOath,       // Inhale ashes of loved ones for strength
            FlameOffering,   // Offer something precious to sacred fire
            EmberMeditation, // Connect with inner spark
            AshCommunion,    // Share ashes with others for bonding
            SpiritSummoning  // Call upon the ash-touched dead
        }
    }
    
    [System.Serializable]
    public class RitualEffect
    {
        public EffectType type;
        public float magnitude;
        public float duration;
        public string description;
        
        public enum EffectType
        {
            HealthBoost,
            ManaBoost,
            TraumaReduction,
            FaithIncrease,
            WisdomGain,
            StrengthGain,
            SpiritualProtection,
            AncestralGuidance
        }
    }
    
    [System.Serializable]
    public class FlameTemple
    {
        [Header("Temple Identity")]
        public string templeName;
        public GameObject templeObject;
        public TempleType type;
        public float sanctity = 100f;
        
        [Header("Sacred Elements")]
        public GameObject sacredFlame;
        public SacredOffering[] acceptedOfferings;
        public string[] templeWisdom;
        public float blessingPower = 1f;
        
        [Header("Temple Guardians")]
        public string[] guardianSpirits;
        public bool hasLivingGuardian;
        public string guardianName;
        
        public enum TempleType
        {
            CreationShrine,   // Honors the Ember of Creation
            MemoryTemple,     // Preserves the ash of the dead
            WisdomSanctuary,  // Seeks truth through flame
            RedemptionAltar,  // Offers second chances
            CommunionHall     // Brings people together through shared flame
        }
    }
    
    [System.Serializable]
    public class SacredOffering
    {
        [Header("Offering Details")]
        public string offeringName;
        public OfferingType type;
        public float spiritualValue;
        public float materialValue;
        
        [Header("Requirements")]
        public string itemRequired;
        public float emotionalAttachment; // How much the item means to the player
        public bool mustBePersonal;
        
        [Header("Blessing Received")]
        public BlessingType blessing;
        public float blessingMagnitude;
        public float blessingDuration;
        public string blessingDescription;
        
        public enum OfferingType
        {
            BelovedObject,    // Something precious to the player
            MemoryItem,       // Item connected to a memory
            WeaponOffering,   // Sacrifice a weapon for peace
            BloodOffering,    // Sacrifice health for power
            KnowledgeOffering, // Share wisdom with the flame
            TimeOffering      // Spend time in meditation
        }
        
        public enum BlessingType
        {
            Forgiveness,      // Reduces trauma and guilt
            Strength,         // Increases physical power
            Wisdom,           // Grants new knowledge
            Protection,       // Spiritual armor against evil
            Guidance,         // Reveals hidden paths
            Communion         // Connects with spirits of the dead
        }
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        playerStats = GetComponent<PlayerStats>();
        gameManager = GameManager.Instance;
        
        InitializeFaithSystem();
        InitializeFaithTenets();
        InitializeFlameTemples();
    }
    
    void Update()
    {
        UpdateEmberOfCreation();
        CheckForRitualOpportunities();
        UpdateSpiritualConnections();
    }
    
    void InitializeFaithSystem()
    {
        // Initialize core faith beliefs
        spiritualConnections["Ember of Creation"] = emberOfCreation;
        spiritualConnections["Ancestral Spirits"] = 0f;
        spiritualConnections["Sacred Flame"] = faithLevel;
        
        Debug.Log("Faith of the Flame system initialized - the inner spark awaits");
    }
    
    void InitializeFaithTenets()
    {
        // Core tenet: Inner Spark
        FaithTenet innerSpark = new FaithTenet
        {
            tenetName = "The Inner Spark",
            description = "All beings carry an ember of the original creation within them. When this spark dies, they are truly forgotten.",
            type = FaithTenet.TenetType.InnerSpark,
            spiritualWeight = 100f,
            coreBeliefs = new string[]
            {
                "Every soul contains divine fire",
                "The spark can grow or diminish based on actions",
                "When the spark dies, the soul is lost forever",
                "The Cinderborn's spark burns brightest of all"
            },
            practices = new string[]
            {
                "Daily ember meditation",
                "Protecting others' sparks",
                "Feeding the inner flame through good deeds",
                "Sharing warmth with the cold-hearted"
            }
        };
        
        // Spiritual Memory tenet
        FaithTenet spiritualMemory = new FaithTenet
        {
            tenetName = "Ash Memory",
            description = "The dead live on in ash and smoke. Their wisdom can be inhaled, their strength absorbed.",
            type = FaithTenet.TenetType.SpiritualMemory,
            spiritualWeight = 75f,
            coreBeliefs = new string[]
            {
                "Death is transformation, not ending",
                "Ashes contain the essence of the departed",
                "Smoke carries messages from the dead",
                "Proper cremation ensures spiritual continuation"
            },
            practices = new string[]
            {
                "Smoke Oath rituals",
                "Ash preservation ceremonies",
                "Communion with ancestral spirits",
                "Respectful cremation of the dead"
            }
        };
        
        // Flame Wisdom tenet
        FaithTenet flameWisdom = new FaithTenet
        {
            tenetName = "Flame Wisdom",
            description = "Fire reveals truth and burns away lies. In the flame's light, all deceptions are exposed.",
            type = FaithTenet.TenetType.FlameWisdom,
            spiritualWeight = 60f,
            coreBeliefs = new string[]
            {
                "Fire cannot lie",
                "Truth burns brighter than falsehood",
                "Flame purifies corrupted thoughts",
                "Sacred fire judges all actions"
            },
            practices = new string[]
            {
                "Truth-telling by firelight",
                "Flame meditation for clarity",
                "Burning false beliefs",
                "Seeking wisdom in sacred flames"
            }
        };
        
        faithTenets = new FaithTenet[] { innerSpark, spiritualMemory, flameWisdom };
    }
    
    void InitializeFlameTemples()
    {
        // Find temple objects in the world
        GameObject[] templeObjects = GameObject.FindGameObjectsWithTag("FlameTemple");
        
        foreach (GameObject templeObj in templeObjects)
        {
            FlameTemple temple = new FlameTemple
            {
                templeName = templeObj.name,
                templeObject = templeObj,
                type = FlameTemple.TempleType.CreationShrine,
                sanctity = 100f,
                blessingPower = 1f,
                templeWisdom = new string[]
                {
                    "The flame that burns within you is eternal",
                    "From ash comes new life",
                    "Truth illuminates the darkest paths",
                    "In sacrifice, we find strength"
                },
                guardianSpirits = new string[] { "Ancient Flame Keeper", "Spirit of the First Fire" }
            };
            
            flameTemples.Add(temple);
        }
        
        Debug.Log($"Initialized {flameTemples.Count} Flame Temples");
    }
    
    void UpdateEmberOfCreation()
    {
        // Ember strength changes based on player actions and spiritual state
        float emberChange = 0f;
        
        // Positive influences
        if (psycheSystem != null)
        {
            if (psycheSystem.enlightenment > 50f)
                emberChange += 0.1f * Time.deltaTime;
            
            if (psycheSystem.GetTrauma() > 80f)
                emberChange -= 0.2f * Time.deltaTime; // Trauma dims the spark
        }
        
        // Faith influences ember strength
        emberChange += (faithLevel - 50f) * 0.001f * Time.deltaTime;
        
        emberOfCreation = Mathf.Clamp(emberOfCreation + emberChange, 0f, 200f);
        
        // Update spiritual connection
        spiritualConnections["Ember of Creation"] = emberOfCreation;
        
        // Check for ember death
        if (emberOfCreation <= 0f)
        {
            OnEmberDeath();
        }
    }
    
    void OnEmberDeath()
    {
        ShowFaithMessage("Your inner spark has died... you feel hollow, forgotten by the world itself.");
        
        // Severe consequences for losing the inner spark
        if (playerStats != null)
        {
            playerStats.ModifyMaxHealth(-50f);
        }
        
        // NPCs no longer recognize the player
        gameManager?.SetPlayerForgotten(true);
    }
    
    void CheckForRitualOpportunities()
    {
        // Check if player is near sacred flames or has ritual items
        FlameTemple nearbyTemple = GetNearbyTemple();
        if (nearbyTemple != null && Input.GetKeyDown(KeyCode.R))
        {
            ShowRitualOptions(nearbyTemple);
        }
    }
    
    FlameTemple GetNearbyTemple()
    {
        foreach (FlameTemple temple in flameTemples)
        {
            if (temple.templeObject != null)
            {
                float distance = Vector3.Distance(transform.position, temple.templeObject.transform.position);
                if (distance < 5f)
                    return temple;
            }
        }
        return null;
    }
    
    void ShowRitualOptions(FlameTemple temple)
    {
        ShowFaithMessage($"Sacred rituals available at {temple.templeName}:");
        ShowFaithMessage("1. Ash Prayer - Burn a beloved object for forgiveness");
        ShowFaithMessage("2. Smoke Oath - Inhale ancestral ashes for strength");
        ShowFaithMessage("3. Flame Offering - Offer something precious to the sacred fire");
        ShowFaithMessage("Press [1], [2], or [3] to perform ritual");
    }
    
    public void PerformAshPrayer(string belovedObject)
    {
        if (faithLevel < 25f)
        {
            ShowFaithMessage("Your faith is too weak to perform the Ash Prayer.");
            return;
        }
        
        StartCoroutine(AshPrayerSequence(belovedObject));
    }
    
    IEnumerator AshPrayerSequence(string belovedObject)
    {
        ShowFaithMessage($"You place your {belovedObject} into the sacred flame...");
        yield return new WaitForSeconds(2f);
        
        // Visual effect
        if (ashPrayerEffect != null)
        {
            GameObject effect = Instantiate(ashPrayerEffect, transform.position, Quaternion.identity);
            Destroy(effect, 5f);
        }
        
        ShowFaithMessage("The flames consume your offering, carrying your prayers to the eternal ember...");
        yield return new WaitForSeconds(3f);
        
        ShowFaithMessage("You feel a weight lifted from your soul. Forgiveness flows through you like warm ash.");
        
        // Apply ritual effects
        ApplyAshPrayerEffects();
        
        // Record ritual
        RecordRitual("Ash Prayer", SacredRitual.RitualType.AshPrayer);
    }
    
    void ApplyAshPrayerEffects()
    {
        // Reduce trauma significantly
        if (psycheSystem != null)
        {
            psycheSystem.ReduceTrauma(25f, "Forgiveness through Ash Prayer");
        }
        
        // Increase faith
        ModifyFaith(15f);
        
        // Strengthen ember
        emberOfCreation += 10f;
        
        ShowFaithMessage("Ash Prayer completed. Your inner spark burns brighter.");
    }
    
    public void PerformSmokeOath(string ancestorName)
    {
        if (faithLevel < 30f)
        {
            ShowFaithMessage("Your faith is insufficient for the Smoke Oath.");
            return;
        }
        
        StartCoroutine(SmokeOathSequence(ancestorName));
    }
    
    IEnumerator SmokeOathSequence(string ancestorName)
    {
        ShowFaithMessage($"You prepare the ashes of {ancestorName} for the Smoke Oath...");
        yield return new WaitForSeconds(2f);
        
        // Visual effect
        if (smokeOathEffect != null)
        {
            GameObject effect = Instantiate(smokeOathEffect, transform.position, Quaternion.identity);
            Destroy(effect, 8f);
        }
        
        ShowFaithMessage("You inhale the sacred smoke, feeling the strength of your ancestors flow into you...");
        yield return new WaitForSeconds(3f);
        
        ShowFaithMessage($"The wisdom of {ancestorName} mingles with your own. Their strength becomes yours.");
        
        // Apply ritual effects
        ApplySmokeOathEffects(ancestorName);
        
        // Record ritual
        RecordRitual("Smoke Oath", SacredRitual.RitualType.SmokeOath);
    }
    
    void ApplySmokeOathEffects(string ancestorName)
    {
        // Gain temporary strength boost
        if (playerStats != null)
        {
            playerStats.ModifyMaxHealth(20f);
        }
        
        // Gain ancestral wisdom
        if (psycheSystem != null)
        {
            psycheSystem.enlightenment += 10f;
        }
        
        // Increase spiritual connection
        spiritualConnections["Ancestral Spirits"] += 25f;
        
        // Unlock ancestral guidance
        gameManager?.UnlockDialogueOption($"AncestralWisdom_{ancestorName}");
        
        ShowFaithMessage($"Smoke Oath completed. {ancestorName}'s strength flows through you.");
    }
    
    public void PerformFlameOffering(SacredOffering offering, FlameTemple temple)
    {
        if (faithLevel < offering.spiritualValue)
        {
            ShowFaithMessage("Your faith is not strong enough for this offering.");
            return;
        }
        
        StartCoroutine(FlameOfferingSequence(offering, temple));
    }
    
    IEnumerator FlameOfferingSequence(SacredOffering offering, FlameTemple temple)
    {
        ShowFaithMessage($"You approach the sacred flame with your {offering.offeringName}...");
        yield return new WaitForSeconds(2f);
        
        ShowFaithMessage("The flame dances higher, as if eager to receive your offering...");
        yield return new WaitForSeconds(2f);
        
        ShowFaithMessage($"You place the {offering.offeringName} into the sacred fire...");
        yield return new WaitForSeconds(3f);
        
        // Play ritual chant
        if (ritualChants.Length > 0)
        {
            AudioSource.PlayClipAtPoint(ritualChants[Random.Range(0, ritualChants.Length)], transform.position);
        }
        
        ShowFaithMessage("The flame accepts your offering! Divine blessing flows through you...");
        
        // Apply blessing
        ApplyFlameBlessing(offering, temple);
        
        // Record offering
        RecordOffering(offering, temple);
    }
    
    void ApplyFlameBlessing(SacredOffering offering, FlameTemple temple)
    {
        float blessingPower = offering.blessingMagnitude * temple.blessingPower;
        
        switch (offering.blessing)
        {
            case SacredOffering.BlessingType.Forgiveness:
                if (psycheSystem != null)
                {
                    psycheSystem.ReduceTrauma(blessingPower, "Divine forgiveness");
                }
                break;
            
            case SacredOffering.BlessingType.Strength:
                if (playerStats != null)
                {
                    playerStats.ModifyMaxHealth(blessingPower);
                }
                break;
            
            case SacredOffering.BlessingType.Wisdom:
                if (psycheSystem != null)
                {
                    psycheSystem.enlightenment += blessingPower;
                }
                break;
            
            case SacredOffering.BlessingType.Protection:
                // Grant temporary spiritual protection
                gameManager?.AddTemporaryEffect("SpiritualProtection", blessingPower, offering.blessingDuration);
                break;
            
            case SacredOffering.BlessingType.Guidance:
                // Reveal hidden paths or secrets
                gameManager?.RevealHiddenPath();
                break;
            
            case SacredOffering.BlessingType.Communion:
                // Enable communication with spirits
                gameManager?.EnableSpiritCommunication(offering.blessingDuration);
                break;
        }
        
        ShowFaithMessage($"Blessing of {offering.blessing} received: {offering.blessingDescription}");
    }
    
    void RecordRitual(string ritualName, SacredRitual.RitualType type)
    {
        SacredRitual ritual = new SacredRitual
        {
            ritualName = ritualName,
            type = type,
            performanceTime = Time.time
        };
        
        performedRituals.Add(ritual);
        
        // Increase faith for performing rituals
        ModifyFaith(5f);
    }
    
    void RecordOffering(SacredOffering offering, FlameTemple temple)
    {
        // Increase temple sanctity
        temple.sanctity += offering.spiritualValue * 0.1f;
        
        // Increase faith
        ModifyFaith(offering.spiritualValue * 0.2f);
        
        // Strengthen ember
        emberOfCreation += offering.spiritualValue * 0.1f;
    }
    
    void UpdateSpiritualConnections()
    {
        // Update connections based on recent actions and rituals
        foreach (var connection in new Dictionary<string, float>(spiritualConnections))
        {
            // Gradual decay if not maintained
            spiritualConnections[connection.Key] = Mathf.Max(0f, connection.Value - 0.1f * Time.deltaTime);
        }
    }
    
    public void ModifyFaith(float change)
    {
        faithLevel = Mathf.Clamp(faithLevel + change, 0f, 100f);
        spiritualConnections["Sacred Flame"] = faithLevel;
        
        if (Mathf.Abs(change) > 2f)
        {
            ShowFaithMessage($"Faith: {faithLevel:F1} ({change:+F1})");
        }
    }
    
    public void OnCharacterDeath(string characterName, bool wasInnocent)
    {
        if (wasInnocent)
        {
            // Innocent death dims the ember
            emberOfCreation -= 5f;
            ModifyFaith(-3f);
            ShowFaithMessage("The death of an innocent dims your inner spark...");
        }
        else
        {
            // Justified death may strengthen resolve
            emberOfCreation += 1f;
            ShowFaithMessage("Justice served, though life is still precious...");
        }
    }
    
    public void OnActOfKindness()
    {
        emberOfCreation += 2f;
        ModifyFaith(1f);
        ShowFaithMessage("Your kindness feeds the inner flame...");
    }
    
    public void OnActOfCruelty()
    {
        emberOfCreation -= 3f;
        ModifyFaith(-2f);
        ShowFaithMessage("Cruelty dims the sacred spark within...");
    }
    
    void ShowFaithMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Faith of the Flame: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public float GetEmberOfCreation() => emberOfCreation;
    public float GetFaithLevel() => faithLevel;
    public List<SacredRitual> GetPerformedRituals() => performedRituals;
    public Dictionary<string, float> GetSpiritualConnections() => spiritualConnections;
    public FaithTenet[] GetFaithTenets() => faithTenets;
    public List<FlameTemple> GetFlameTemples() => flameTemples;
}
