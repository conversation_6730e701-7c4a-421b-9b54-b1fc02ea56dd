using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using System.Collections.Generic;

[System.Serializable]
public class DialogueChoice
{
    public string choiceText;
    public int nextDialogueIndex;
    public float sunAlignmentChange;
    public float moonAlignmentChange;
    public bool requiresSunPath;
    public bool requiresMoonPath;
    public bool requiresEclipsePath;
}

[System.Serializable]
public class DialogueNode
{
    public string speakerName;
    public string dialogueText;
    public List<DialogueChoice> choices = new List<DialogueChoice>();
    public bool isEndNode;
    public bool triggersEvent;
    public string eventName;
}

[System.Serializable]
public class DialogueTree
{
    public string treeName;
    public List<DialogueNode> nodes = new List<DialogueNode>();
    public int startingNodeIndex = 0;
}

public class DialogueSystem : MonoBehaviour
{
    [Header("UI References")]
    public GameObject dialoguePanel;
    public Text speakerNameText;
    public Text dialogueText;
    public Transform choicesParent;
    public GameObject choiceButtonPrefab;

    [Header("Settings")]
    public float textSpeed = 0.05f;

    private DialogueTree currentDialogue;
    private int currentNodeIndex;
    private PlayerStats playerStats;
    private bool isDialogueActive = false;
    private bool isTyping = false;
    private List<GameObject> currentChoiceButtons = new List<GameObject>();

    // Events
    public System.Action<string> OnDialogueEvent;
    public System.Action OnDialogueStart;
    public System.Action OnDialogueEnd;

    void Start()
    {
        playerStats = FindObjectOfType<PlayerStats>();

        if (dialoguePanel != null)
            dialoguePanel.SetActive(false);
    }

    void Update()
    {
        if (isDialogueActive && isTyping)
        {
            bool spacePressed = false;

            var keyboard = Keyboard.current;
            if (keyboard != null)
            {
                spacePressed = keyboard.spaceKey.wasPressedThisFrame;
            }
            else
            {
                // Fallback to old input system
                spacePressed = Input.GetKeyDown(KeyCode.Space);
            }

            if (spacePressed)
            {
                // Skip typing animation
                StopAllCoroutines();
                DisplayCurrentDialogue();
            }
        }
    }

    public void StartDialogue(DialogueTree dialogue)
    {
        if (dialogue == null || dialogue.nodes.Count == 0)
        {
            Debug.LogWarning("Invalid dialogue tree!");
            return;
        }

        currentDialogue = dialogue;
        currentNodeIndex = dialogue.startingNodeIndex;
        isDialogueActive = true;

        // Show dialogue UI
        if (dialoguePanel != null)
            dialoguePanel.SetActive(true);

        // Lock player movement
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
            playerController.enabled = false;

        // Unlock cursor for dialogue choices
        Cursor.lockState = CursorLockMode.None;

        OnDialogueStart?.Invoke();
        DisplayCurrentDialogue();
    }

    void DisplayCurrentDialogue()
    {
        if (currentDialogue == null || currentNodeIndex >= currentDialogue.nodes.Count)
        {
            EndDialogue();
            return;
        }

        DialogueNode currentNode = currentDialogue.nodes[currentNodeIndex];

        // Update speaker name
        if (speakerNameText != null)
            speakerNameText.text = currentNode.speakerName;

        // Clear previous choices
        ClearChoices();

        // Start typing animation
        StartCoroutine(TypeDialogue(currentNode.dialogueText));
    }

    System.Collections.IEnumerator TypeDialogue(string text)
    {
        isTyping = true;
        dialogueText.text = "";

        foreach (char letter in text.ToCharArray())
        {
            dialogueText.text += letter;
            yield return new WaitForSeconds(textSpeed);
        }

        isTyping = false;
        ShowChoices();
    }

    void ShowChoices()
    {
        DialogueNode currentNode = currentDialogue.nodes[currentNodeIndex];

        // If no choices, this is an end node
        if (currentNode.choices.Count == 0 || currentNode.isEndNode)
        {
            // Create a "Continue" button or end dialogue
            if (currentNode.isEndNode)
            {
                CreateContinueButton();
            }
            return;
        }

        // Create choice buttons
        foreach (DialogueChoice choice in currentNode.choices)
        {
            // Check if choice is available based on player's moral path
            if (!IsChoiceAvailable(choice))
                continue;

            GameObject choiceButton = Instantiate(choiceButtonPrefab, choicesParent);
            Button button = choiceButton.GetComponent<Button>();
            Text buttonText = choiceButton.GetComponentInChildren<Text>();

            if (buttonText != null)
                buttonText.text = choice.choiceText;

            if (button != null)
            {
                DialogueChoice capturedChoice = choice; // Capture for closure
                button.onClick.AddListener(() => SelectChoice(capturedChoice));
            }

            currentChoiceButtons.Add(choiceButton);
        }
    }

    bool IsChoiceAvailable(DialogueChoice choice)
    {
        if (playerStats == null) return true;

        PlayerStats.MoralPath currentPath = playerStats.GetCurrentPath();

        if (choice.requiresSunPath && currentPath != PlayerStats.MoralPath.Sun)
            return false;

        if (choice.requiresMoonPath && currentPath != PlayerStats.MoralPath.Moon)
            return false;

        if (choice.requiresEclipsePath && currentPath != PlayerStats.MoralPath.Eclipse)
            return false;

        return true;
    }

    public void SelectChoice(DialogueChoice choice)
    {
        // Apply moral alignment changes
        if (playerStats != null)
        {
            playerStats.AddMoralChoice(choice.sunAlignmentChange, choice.moonAlignmentChange);
        }

        // Move to next dialogue node
        currentNodeIndex = choice.nextDialogueIndex;

        // Check if we're ending the dialogue
        if (currentNodeIndex < 0 || currentNodeIndex >= currentDialogue.nodes.Count)
        {
            EndDialogue();
            return;
        }

        DisplayCurrentDialogue();
    }

    void CreateContinueButton()
    {
        GameObject continueButton = Instantiate(choiceButtonPrefab, choicesParent);
        Button button = continueButton.GetComponent<Button>();
        Text buttonText = continueButton.GetComponentInChildren<Text>();

        if (buttonText != null)
            buttonText.text = "Continue";

        if (button != null)
            button.onClick.AddListener(() => EndDialogue());

        currentChoiceButtons.Add(continueButton);
    }

    void ClearChoices()
    {
        foreach (GameObject choiceButton in currentChoiceButtons)
        {
            if (choiceButton != null)
                Destroy(choiceButton);
        }
        currentChoiceButtons.Clear();
    }

    public void EndDialogue()
    {
        // Trigger event if needed
        if (currentDialogue != null && currentNodeIndex < currentDialogue.nodes.Count)
        {
            DialogueNode currentNode = currentDialogue.nodes[currentNodeIndex];
            if (currentNode.triggersEvent && !string.IsNullOrEmpty(currentNode.eventName))
            {
                OnDialogueEvent?.Invoke(currentNode.eventName);
            }
        }

        isDialogueActive = false;
        currentDialogue = null;

        // Hide dialogue UI
        if (dialoguePanel != null)
            dialoguePanel.SetActive(false);

        // Clear choices
        ClearChoices();

        // Unlock player movement
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
            playerController.enabled = true;

        // Lock cursor again
        Cursor.lockState = CursorLockMode.Locked;

        OnDialogueEnd?.Invoke();
    }

    public bool IsDialogueActive()
    {
        return isDialogueActive;
    }
}
