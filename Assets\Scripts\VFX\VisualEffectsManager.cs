using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;

namespace CinderOfDarkness.VFX
{
    /// <summary>
    /// Visual Effects Manager for Cinder of Darkness.
    /// Manages stylized shaders, screen effects, and GPU-friendly weather systems.
    /// </summary>
    public class VisualEffectsManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Screen Effects")]
        [SerializeField] private Volume postProcessVolume;
        [SerializeField] private VolumeProfile defaultProfile;
        [SerializeField] private VolumeProfile lowHealthProfile;
        [SerializeField] private VolumeProfile combatProfile;
        [SerializeField] private VolumeProfile deathProfile;

        [Header("Health Effects")]
        [SerializeField] private bool enableHealthEffects = true;
        [SerializeField] private float lowHealthThreshold = 0.3f;
        [SerializeField] private float criticalHealthThreshold = 0.15f;
        [SerializeField] private float healthEffectTransitionSpeed = 2f;

        [Header("Combat Effects")]
        [SerializeField] private bool enableCombatEffects = true;
        [SerializeField] private float combatEffectDuration = 3f;
        [SerializeField] private float combatEffectIntensity = 0.5f;

        [Header("Weather Effects")]
        [SerializeField] private WeatherSystem weatherSystem;
        [SerializeField] private bool enableWeatherEffects = true;
        [SerializeField] private WeatherType currentWeather = WeatherType.Clear;

        [Header("Fire Effects")]
        [SerializeField] private Material fireMaterial;
        [SerializeField] private ParticleSystem fireParticleSystem;
        [SerializeField] private Light fireLight;
        [SerializeField] private AudioSource fireAudioSource;

        [Header("Blood Effects")]
        [SerializeField] private Material bloodMaterial;
        [SerializeField] private GameObject bloodSplatterPrefab;
        [SerializeField] private int maxBloodDecals = 50;

        [Header("Magic Effects")]
        [SerializeField] private Material[] magicMaterials;
        [SerializeField] private ParticleSystem[] magicParticleSystems;
        [SerializeField] private Light[] magicLights;
        #endregion

        #region Public Properties
        public static VisualEffectsManager Instance { get; private set; }
        public float CurrentHealthPercentage { get; private set; } = 1f;
        public bool IsInCombat { get; private set; }
        public WeatherType CurrentWeather => currentWeather;
        #endregion

        #region Private Fields
        private PlayerStats playerStats;
        private PlayerCombat playerCombat;
        private float combatEffectTimer;
        private float currentHealthEffectWeight;
        private List<GameObject> activeBloodDecals = new List<GameObject>();
        private Queue<GameObject> bloodDecalPool = new Queue<GameObject>();
        
        // Post-processing components
        private Vignette vignette;
        private ChromaticAberration chromaticAberration;
        private ColorAdjustments colorAdjustments;
        private Bloom bloom;
        private FilmGrain filmGrain;
        #endregion

        #region Events
        public System.Action<WeatherType> OnWeatherChanged;
        public System.Action<float> OnHealthEffectChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize VFX Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeVFXManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Start VFX systems.
        /// </summary>
        private void Start()
        {
            FindPlayerComponents();
            InitializePostProcessing();
            InitializeBloodDecalPool();
            InitializeWeatherSystem();
        }

        /// <summary>
        /// Update VFX systems.
        /// </summary>
        private void Update()
        {
            UpdateHealthEffects();
            UpdateCombatEffects();
            UpdateWeatherEffects();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize VFX Manager systems.
        /// </summary>
        private void InitializeVFXManager()
        {
            // Ensure we have a post-process volume
            if (postProcessVolume == null)
            {
                postProcessVolume = FindObjectOfType<Volume>();
            }

            if (postProcessVolume == null)
            {
                GameObject volumeObject = new GameObject("Post Process Volume");
                postProcessVolume = volumeObject.AddComponent<Volume>();
                postProcessVolume.isGlobal = true;
            }
        }

        /// <summary>
        /// Find player components for health and combat tracking.
        /// </summary>
        private void FindPlayerComponents()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerStats = player.GetComponent<PlayerStats>();
                playerCombat = player.GetComponent<PlayerCombat>();

                if (playerStats != null)
                {
                    playerStats.OnHealthChanged += OnPlayerHealthChanged;
                    playerStats.OnPlayerDeath += OnPlayerDeath;
                }

                if (playerCombat != null)
                {
                    playerCombat.OnCombatStart += OnCombatStart;
                    playerCombat.OnCombatEnd += OnCombatEnd;
                }
            }
        }

        /// <summary>
        /// Initialize post-processing components.
        /// </summary>
        private void InitializePostProcessing()
        {
            if (postProcessVolume?.profile != null)
            {
                postProcessVolume.profile.TryGet(out vignette);
                postProcessVolume.profile.TryGet(out chromaticAberration);
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out bloom);
                postProcessVolume.profile.TryGet(out filmGrain);
            }
        }

        /// <summary>
        /// Initialize blood decal object pool.
        /// </summary>
        private void InitializeBloodDecalPool()
        {
            if (bloodSplatterPrefab != null)
            {
                for (int i = 0; i < maxBloodDecals; i++)
                {
                    GameObject decal = Instantiate(bloodSplatterPrefab);
                    decal.SetActive(false);
                    bloodDecalPool.Enqueue(decal);
                }
            }
        }

        /// <summary>
        /// Initialize weather system.
        /// </summary>
        private void InitializeWeatherSystem()
        {
            if (weatherSystem == null)
            {
                weatherSystem = GetComponent<WeatherSystem>();
            }

            if (weatherSystem == null)
            {
                weatherSystem = gameObject.AddComponent<WeatherSystem>();
            }

            weatherSystem.Initialize();
        }
        #endregion

        #region Health Effects
        /// <summary>
        /// Update health-based visual effects.
        /// </summary>
        private void UpdateHealthEffects()
        {
            if (!enableHealthEffects || playerStats == null) return;

            CurrentHealthPercentage = playerStats.GetHealthPercentage();
            
            float targetWeight = 0f;
            
            if (CurrentHealthPercentage <= criticalHealthThreshold)
            {
                targetWeight = 1f; // Maximum effect at critical health
            }
            else if (CurrentHealthPercentage <= lowHealthThreshold)
            {
                // Interpolate between no effect and maximum effect
                float normalizedHealth = (CurrentHealthPercentage - criticalHealthThreshold) / 
                                       (lowHealthThreshold - criticalHealthThreshold);
                targetWeight = 1f - normalizedHealth;
            }

            // Smooth transition
            currentHealthEffectWeight = Mathf.Lerp(currentHealthEffectWeight, targetWeight, 
                                                  healthEffectTransitionSpeed * Time.deltaTime);

            ApplyHealthEffects(currentHealthEffectWeight);
        }

        /// <summary>
        /// Apply health-based visual effects.
        /// </summary>
        /// <param name="intensity">Effect intensity (0-1)</param>
        private void ApplyHealthEffects(float intensity)
        {
            // Vignette effect
            if (vignette != null)
            {
                vignette.intensity.value = Mathf.Lerp(0.2f, 0.6f, intensity);
                vignette.color.value = Color.Lerp(Color.black, Color.red, intensity * 0.3f);
            }

            // Chromatic aberration
            if (chromaticAberration != null)
            {
                chromaticAberration.intensity.value = Mathf.Lerp(0f, 0.3f, intensity);
            }

            // Color adjustments
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = Mathf.Lerp(0f, -20f, intensity);
                colorAdjustments.contrast.value = Mathf.Lerp(0f, 10f, intensity);
            }

            // Film grain for damage effect
            if (filmGrain != null)
            {
                filmGrain.intensity.value = Mathf.Lerp(0f, 0.4f, intensity);
            }

            OnHealthEffectChanged?.Invoke(intensity);
        }

        /// <summary>
        /// Handle player health change events.
        /// </summary>
        /// <param name="currentHealth">Current health value</param>
        /// <param name="maxHealth">Maximum health value</param>
        private void OnPlayerHealthChanged(float currentHealth, float maxHealth)
        {
            // Health effects are handled in Update for smooth transitions
        }

        /// <summary>
        /// Handle player death event.
        /// </summary>
        private void OnPlayerDeath()
        {
            ApplyDeathEffects();
        }

        /// <summary>
        /// Apply death visual effects.
        /// </summary>
        private void ApplyDeathEffects()
        {
            if (deathProfile != null)
            {
                StartCoroutine(TransitionToProfile(deathProfile, 2f));
            }
        }
        #endregion

        #region Combat Effects
        /// <summary>
        /// Update combat-based visual effects.
        /// </summary>
        private void UpdateCombatEffects()
        {
            if (!enableCombatEffects) return;

            if (IsInCombat)
            {
                combatEffectTimer += Time.deltaTime;
                
                // Apply combat effects with pulsing intensity
                float pulseIntensity = Mathf.Sin(combatEffectTimer * 2f) * 0.5f + 0.5f;
                ApplyCombatEffects(combatEffectIntensity * pulseIntensity);
            }
        }

        /// <summary>
        /// Apply combat visual effects.
        /// </summary>
        /// <param name="intensity">Effect intensity (0-1)</param>
        private void ApplyCombatEffects(float intensity)
        {
            // Enhance bloom during combat
            if (bloom != null)
            {
                bloom.intensity.value = Mathf.Lerp(0.5f, 1.2f, intensity);
            }

            // Slight color enhancement
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = Mathf.Lerp(0f, 15f, intensity);
            }
        }

        /// <summary>
        /// Handle combat start event.
        /// </summary>
        private void OnCombatStart()
        {
            IsInCombat = true;
            combatEffectTimer = 0f;

            if (combatProfile != null)
            {
                StartCoroutine(TransitionToProfile(combatProfile, 0.5f));
            }
        }

        /// <summary>
        /// Handle combat end event.
        /// </summary>
        private void OnCombatEnd()
        {
            IsInCombat = false;
            
            StartCoroutine(TransitionToProfile(defaultProfile, 1f));
        }
        #endregion

        #region Weather Effects
        /// <summary>
        /// Update weather visual effects.
        /// </summary>
        private void UpdateWeatherEffects()
        {
            if (!enableWeatherEffects || weatherSystem == null) return;

            weatherSystem.UpdateWeather();
        }

        /// <summary>
        /// Change weather type.
        /// </summary>
        /// <param name="newWeather">New weather type</param>
        /// <param name="transitionDuration">Transition duration in seconds</param>
        public void ChangeWeather(WeatherType newWeather, float transitionDuration = 5f)
        {
            if (currentWeather != newWeather)
            {
                WeatherType previousWeather = currentWeather;
                currentWeather = newWeather;
                
                if (weatherSystem != null)
                {
                    weatherSystem.TransitionToWeather(newWeather, transitionDuration);
                }

                OnWeatherChanged?.Invoke(newWeather);
            }
        }
        #endregion

        #region Fire Effects
        /// <summary>
        /// Create fire effect at position.
        /// </summary>
        /// <param name="position">World position</param>
        /// <param name="intensity">Fire intensity (0-1)</param>
        /// <param name="duration">Effect duration</param>
        public void CreateFireEffect(Vector3 position, float intensity = 1f, float duration = 5f)
        {
            if (fireParticleSystem != null)
            {
                GameObject fireEffect = Instantiate(fireParticleSystem.gameObject, position, Quaternion.identity);
                ParticleSystem particles = fireEffect.GetComponent<ParticleSystem>();
                
                if (particles != null)
                {
                    var main = particles.main;
                    main.startLifetime = duration;
                    main.startSpeed = intensity * 5f;
                    
                    var emission = particles.emission;
                    emission.rateOverTime = intensity * 50f;
                }

                // Add fire light
                if (fireLight != null)
                {
                    GameObject lightObject = Instantiate(fireLight.gameObject, position, Quaternion.identity);
                    Light light = lightObject.GetComponent<Light>();
                    if (light != null)
                    {
                        light.intensity = intensity * 2f;
                        light.range = intensity * 10f;
                    }
                    
                    Destroy(lightObject, duration);
                }

                Destroy(fireEffect, duration);
            }
        }

        /// <summary>
        /// Update fire material properties.
        /// </summary>
        /// <param name="intensity">Fire intensity</param>
        /// <param name="color">Fire color</param>
        public void UpdateFireMaterial(float intensity, Color color)
        {
            if (fireMaterial != null)
            {
                fireMaterial.SetFloat("_Intensity", intensity);
                fireMaterial.SetColor("_FireColor", color);
                fireMaterial.SetFloat("_FlameHeight", intensity * 2f);
            }
        }
        #endregion

        #region Blood Effects
        /// <summary>
        /// Create blood splatter effect.
        /// </summary>
        /// <param name="position">World position</param>
        /// <param name="normal">Surface normal</param>
        /// <param name="intensity">Blood intensity (0-1)</param>
        public void CreateBloodSplatter(Vector3 position, Vector3 normal, float intensity = 1f)
        {
            GameObject bloodDecal = GetBloodDecalFromPool();
            if (bloodDecal != null)
            {
                bloodDecal.transform.position = position;
                bloodDecal.transform.rotation = Quaternion.LookRotation(normal);
                bloodDecal.transform.localScale = Vector3.one * intensity;
                bloodDecal.SetActive(true);

                activeBloodDecals.Add(bloodDecal);

                // Remove oldest decal if we've reached the limit
                if (activeBloodDecals.Count > maxBloodDecals)
                {
                    GameObject oldestDecal = activeBloodDecals[0];
                    activeBloodDecals.RemoveAt(0);
                    ReturnBloodDecalToPool(oldestDecal);
                }
            }
        }

        /// <summary>
        /// Get blood decal from object pool.
        /// </summary>
        /// <returns>Blood decal GameObject</returns>
        private GameObject GetBloodDecalFromPool()
        {
            if (bloodDecalPool.Count > 0)
            {
                return bloodDecalPool.Dequeue();
            }
            else if (bloodSplatterPrefab != null)
            {
                return Instantiate(bloodSplatterPrefab);
            }

            return null;
        }

        /// <summary>
        /// Return blood decal to object pool.
        /// </summary>
        /// <param name="decal">Decal to return</param>
        private void ReturnBloodDecalToPool(GameObject decal)
        {
            decal.SetActive(false);
            bloodDecalPool.Enqueue(decal);
        }
        #endregion

        #region Magic Effects
        /// <summary>
        /// Create magic effect.
        /// </summary>
        /// <param name="magicType">Type of magic</param>
        /// <param name="position">World position</param>
        /// <param name="intensity">Effect intensity</param>
        public void CreateMagicEffect(MagicType magicType, Vector3 position, float intensity = 1f)
        {
            int magicIndex = (int)magicType;
            
            if (magicParticleSystems != null && magicIndex < magicParticleSystems.Length)
            {
                ParticleSystem magicParticles = magicParticleSystems[magicIndex];
                if (magicParticles != null)
                {
                    GameObject effect = Instantiate(magicParticles.gameObject, position, Quaternion.identity);
                    Destroy(effect, 3f);
                }
            }

            if (magicLights != null && magicIndex < magicLights.Length)
            {
                Light magicLight = magicLights[magicIndex];
                if (magicLight != null)
                {
                    GameObject lightEffect = Instantiate(magicLight.gameObject, position, Quaternion.identity);
                    Light light = lightEffect.GetComponent<Light>();
                    if (light != null)
                    {
                        light.intensity = intensity * 3f;
                    }
                    Destroy(lightEffect, 2f);
                }
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Transition to a different post-processing profile.
        /// </summary>
        /// <param name="targetProfile">Target profile</param>
        /// <param name="duration">Transition duration</param>
        /// <returns>Transition coroutine</returns>
        private System.Collections.IEnumerator TransitionToProfile(VolumeProfile targetProfile, float duration)
        {
            VolumeProfile originalProfile = postProcessVolume.profile;
            float elapsedTime = 0f;

            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / duration;
                
                // Blend between profiles (simplified - in practice you'd blend individual components)
                postProcessVolume.weight = Mathf.Lerp(0f, 1f, t);
                
                yield return null;
            }

            postProcessVolume.profile = targetProfile;
            postProcessVolume.weight = 1f;
        }

        /// <summary>
        /// Reset all visual effects to default state.
        /// </summary>
        public void ResetAllEffects()
        {
            if (postProcessVolume != null && defaultProfile != null)
            {
                postProcessVolume.profile = defaultProfile;
                postProcessVolume.weight = 1f;
            }

            currentHealthEffectWeight = 0f;
            IsInCombat = false;
            combatEffectTimer = 0f;
        }
        #endregion

        #region Cleanup
        /// <summary>
        /// Clean up VFX Manager.
        /// </summary>
        private void OnDestroy()
        {
            if (playerStats != null)
            {
                playerStats.OnHealthChanged -= OnPlayerHealthChanged;
                playerStats.OnPlayerDeath -= OnPlayerDeath;
            }

            if (playerCombat != null)
            {
                playerCombat.OnCombatStart -= OnCombatStart;
                playerCombat.OnCombatEnd -= OnCombatEnd;
            }
        }
        #endregion
    }

    #region Enums
    /// <summary>
    /// Weather types for visual effects.
    /// </summary>
    public enum WeatherType
    {
        Clear,
        Fog,
        AshStorm,
        Rain,
        Snow,
        Sparks
    }

    /// <summary>
    /// Magic types for visual effects.
    /// </summary>
    public enum MagicType
    {
        Fire,
        Ice,
        Lightning,
        Dark,
        Light,
        Earth
    }
    #endregion
}
