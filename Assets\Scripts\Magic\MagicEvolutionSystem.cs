using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.Magic
{
    /// <summary>
    /// Advanced Magic Evolution System for Cinder of Darkness.
    /// Manages spell evolution, elemental combinations, and magical progression.
    /// </summary>
    public class MagicEvolutionSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Magic System Settings")]
        [SerializeField] private bool enableMagicEvolution = true;
        [SerializeField] private float evolutionThreshold = 100f;
        [SerializeField] private int maxSpellLevel = 10;
        [SerializeField] private float combinationCooldown = 2f;

        [Header("Spell Database")]
        [SerializeField] private SpellData[] baseSpells;
        [SerializeField] private SpellTrait[] availableTraits;
        [SerializeField] private ElementalCombination[] elementalCombinations;

        [Header("Visual Effects")]
        [SerializeField] private GameObject spellEvolutionEffect;
        [SerializeField] private GameObject combinationEffect;
        [SerializeField] private ParticleSystem magicAuraEffect;

        [Header("Audio")]
        [SerializeField] private AudioClip spellEvolutionSound;
        [SerializeField] private AudioClip combinationSound;
        [SerializeField] private AudioClip traitUnlockSound;
        [SerializeField] private AudioSource audioSource;

        [Header("UI References")]
        [SerializeField] private GameObject magicMenuUI;
        [SerializeField] private Transform spellListContainer;
        [SerializeField] private GameObject spellSlotPrefab;
        [SerializeField] private TMPro.TextMeshProUGUI magicPointsText;
        #endregion

        #region Public Properties
        public static MagicEvolutionSystem Instance { get; private set; }
        public Dictionary<string, PlayerSpell> LearnedSpells { get; private set; } = new Dictionary<string, PlayerSpell>();
        public int MagicPoints { get; private set; } = 0;
        public List<string> UnlockedTraits { get; private set; } = new List<string>();
        #endregion

        #region Private Fields
        private DynamicNarrativeSystem narrativeSystem;
        private PlayerController playerController;
        private PlayerCombat playerCombat;

        // Spell casting tracking
        private Dictionary<string, float> spellUsageCount = new Dictionary<string, float>();
        private Dictionary<string, float> lastSpellCast = new Dictionary<string, float>();
        private List<ActiveSpell> activeSpells = new List<ActiveSpell>();

        // Combination system
        private Queue<CastSpell> recentCasts = new Queue<CastSpell>();
        private float lastCombinationAttempt;

        // Evolution tracking
        private Dictionary<string, List<string>> spellEvolutionPaths = new Dictionary<string, List<string>>();
        private Dictionary<string, int> traitUnlockProgress = new Dictionary<string, int>();

        // Performance optimization
        private float lastEvolutionCheck;
        private const float evolutionCheckInterval = 1f;
        #endregion

        #region Events
        public System.Action<PlayerSpell> OnSpellLearned;
        public System.Action<PlayerSpell, int> OnSpellEvolved;
        public System.Action<SpellTrait> OnTraitUnlocked;
        public System.Action<ElementalCombination, PlayerSpell> OnSpellCombined;
        public System.Action<int> OnMagicPointsChanged;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeMagicSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            LoadMagicData();
            InitializeBaseSpells();
            SetupUI();
        }

        private void Update()
        {
            if (!enableMagicEvolution) return;

            UpdateActiveSpells();
            CheckSpellEvolution();
            UpdateCombinationWindow();
            UpdateMagicAura();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the magic evolution system.
        /// </summary>
        private void InitializeMagicSystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }

            Debug.Log("Magic Evolution System initialized");
        }

        /// <summary>
        /// Setup system references.
        /// </summary>
        private void SetupSystemReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            playerController = FindObjectOfType<PlayerController>();
            playerCombat = FindObjectOfType<PlayerCombat>();
        }

        /// <summary>
        /// Initialize base spells for the player.
        /// </summary>
        private void InitializeBaseSpells()
        {
            // Give player basic spells to start with
            foreach (var baseSpell in baseSpells.Take(3)) // Start with 3 basic spells
            {
                LearnSpell(baseSpell.spellId);
            }
        }

        /// <summary>
        /// Setup UI components.
        /// </summary>
        private void SetupUI()
        {
            if (magicMenuUI != null)
            {
                magicMenuUI.SetActive(false);
            }

            UpdateMagicPointsDisplay();
            RefreshSpellUI();
        }
        #endregion

        #region Spell Learning and Management
        /// <summary>
        /// Learn a new spell.
        /// </summary>
        /// <param name="spellId">Spell ID to learn</param>
        public bool LearnSpell(string spellId)
        {
            if (LearnedSpells.ContainsKey(spellId)) return false;

            var spellData = GetSpellData(spellId);
            if (spellData == null) return false;

            var playerSpell = new PlayerSpell
            {
                spellData = spellData,
                level = 1,
                experience = 0f,
                unlockedTraits = new List<string>(),
                evolutionStage = 0
            };

            LearnedSpells[spellId] = playerSpell;
            spellUsageCount[spellId] = 0f;

            OnSpellLearned?.Invoke(playerSpell);
            PlaySound(traitUnlockSound);

            Debug.Log($"Learned spell: {spellData.spellName}");
            return true;
        }

        /// <summary>
        /// Cast a spell and track usage.
        /// </summary>
        /// <param name="spellId">Spell ID to cast</param>
        /// <param name="targetPosition">Target position</param>
        /// <param name="caster">Caster transform</param>
        public bool CastSpell(string spellId, Vector3 targetPosition, Transform caster)
        {
            if (!LearnedSpells.ContainsKey(spellId)) return false;

            var playerSpell = LearnedSpells[spellId];

            // Check cooldown
            if (lastSpellCast.ContainsKey(spellId) &&
                Time.time - lastSpellCast[spellId] < playerSpell.spellData.cooldown)
            {
                return false;
            }

            // Execute spell
            ExecuteSpell(playerSpell, targetPosition, caster);

            // Track usage
            TrackSpellUsage(spellId);

            // Add to recent casts for combination detection
            AddToRecentCasts(spellId, targetPosition);

            lastSpellCast[spellId] = Time.time;
            return true;
        }

        /// <summary>
        /// Execute spell effects.
        /// </summary>
        /// <param name="playerSpell">Player spell to execute</param>
        /// <param name="targetPosition">Target position</param>
        /// <param name="caster">Caster transform</param>
        private void ExecuteSpell(PlayerSpell playerSpell, Vector3 targetPosition, Transform caster)
        {
            var spellData = playerSpell.spellData;

            // Create spell projectile or effect
            if (spellData.spellPrefab != null)
            {
                GameObject spellObject = Instantiate(spellData.spellPrefab, caster.position, caster.rotation);

                // Setup spell component
                var spellComponent = spellObject.GetComponent<SpellProjectile>();
                if (spellComponent != null)
                {
                    spellComponent.Initialize(playerSpell, targetPosition);
                }

                // Apply traits
                ApplySpellTraits(spellObject, playerSpell);
            }

            // Create active spell tracking
            var activeSpell = new ActiveSpell
            {
                spellId = spellData.spellId,
                startTime = Time.time,
                duration = spellData.duration,
                casterPosition = caster.position,
                targetPosition = targetPosition
            };

            activeSpells.Add(activeSpell);
        }

        /// <summary>
        /// Apply spell traits to spell object.
        /// </summary>
        /// <param name="spellObject">Spell object</param>
        /// <param name="playerSpell">Player spell with traits</param>
        private void ApplySpellTraits(GameObject spellObject, PlayerSpell playerSpell)
        {
            foreach (var traitId in playerSpell.unlockedTraits)
            {
                var trait = GetSpellTrait(traitId);
                if (trait != null)
                {
                    ApplyTrait(spellObject, trait);
                }
            }
        }

        /// <summary>
        /// Apply individual trait to spell.
        /// </summary>
        /// <param name="spellObject">Spell object</param>
        /// <param name="trait">Trait to apply</param>
        private void ApplyTrait(GameObject spellObject, SpellTrait trait)
        {
            var spellComponent = spellObject.GetComponent<SpellProjectile>();
            if (spellComponent == null) return;

            switch (trait.traitType)
            {
                case SpellTraitType.Burn:
                    spellComponent.AddBurnEffect(trait.effectValue);
                    break;
                case SpellTraitType.Pierce:
                    spellComponent.AddPierceEffect(trait.effectValue);
                    break;
                case SpellTraitType.Explode:
                    spellComponent.AddExplosionEffect(trait.effectValue);
                    break;
                case SpellTraitType.Bounce:
                    spellComponent.AddBounceEffect((int)trait.effectValue);
                    break;
                case SpellTraitType.Homing:
                    spellComponent.AddHomingEffect(trait.effectValue);
                    break;
                case SpellTraitType.Multiply:
                    spellComponent.AddMultiplyEffect((int)trait.effectValue);
                    break;
            }
        }
        #endregion

        #region Spell Evolution
        /// <summary>
        /// Track spell usage for evolution.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        private void TrackSpellUsage(string spellId)
        {
            if (!spellUsageCount.ContainsKey(spellId))
            {
                spellUsageCount[spellId] = 0f;
            }

            spellUsageCount[spellId]++;

            if (LearnedSpells.ContainsKey(spellId))
            {
                LearnedSpells[spellId].experience++;
            }
        }

        /// <summary>
        /// Check for spell evolution opportunities.
        /// </summary>
        private void CheckSpellEvolution()
        {
            if (Time.time - lastEvolutionCheck < evolutionCheckInterval) return;

            foreach (var kvp in LearnedSpells.ToList())
            {
                CheckSpellForEvolution(kvp.Key, kvp.Value);
            }

            lastEvolutionCheck = Time.time;
        }

        /// <summary>
        /// Check individual spell for evolution.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <param name="playerSpell">Player spell</param>
        private void CheckSpellForEvolution(string spellId, PlayerSpell playerSpell)
        {
            // Check for level evolution
            float requiredExp = evolutionThreshold * (playerSpell.level + 1);
            if (playerSpell.experience >= requiredExp && playerSpell.level < maxSpellLevel)
            {
                EvolveSpellLevel(spellId, playerSpell);
            }

            // Check for trait unlocks
            CheckTraitUnlocks(spellId, playerSpell);
        }

        /// <summary>
        /// Evolve spell to next level.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <param name="playerSpell">Player spell</param>
        private void EvolveSpellLevel(string spellId, PlayerSpell playerSpell)
        {
            playerSpell.level++;
            playerSpell.evolutionStage++;

            // Award magic points
            int pointsAwarded = playerSpell.level * 10;
            AddMagicPoints(pointsAwarded);

            // Visual effect
            if (spellEvolutionEffect != null && playerController != null)
            {
                Instantiate(spellEvolutionEffect, playerController.transform.position, Quaternion.identity);
            }

            PlaySound(spellEvolutionSound);
            OnSpellEvolved?.Invoke(playerSpell, playerSpell.level);

            Debug.Log($"Spell {playerSpell.spellData.spellName} evolved to level {playerSpell.level}");
        }

        /// <summary>
        /// Check for trait unlock opportunities.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <param name="playerSpell">Player spell</param>
        private void CheckTraitUnlocks(string spellId, PlayerSpell playerSpell)
        {
            foreach (var trait in availableTraits)
            {
                if (CanUnlockTrait(trait, playerSpell))
                {
                    UnlockTrait(spellId, trait);
                }
            }
        }

        /// <summary>
        /// Check if trait can be unlocked.
        /// </summary>
        /// <param name="trait">Trait to check</param>
        /// <param name="playerSpell">Player spell</param>
        /// <returns>True if trait can be unlocked</returns>
        private bool CanUnlockTrait(SpellTrait trait, PlayerSpell playerSpell)
        {
            // Check if already unlocked
            if (playerSpell.unlockedTraits.Contains(trait.traitId)) return false;

            // Check level requirement
            if (playerSpell.level < trait.requiredLevel) return false;

            // Check element compatibility
            if (trait.requiredElement != ElementType.None &&
                playerSpell.spellData.element != trait.requiredElement) return false;

            // Check usage requirement
            string spellId = playerSpell.spellData.spellId;
            if (spellUsageCount.ContainsKey(spellId) &&
                spellUsageCount[spellId] >= trait.requiredUsage)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Unlock a trait for a spell.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <param name="trait">Trait to unlock</param>
        private void UnlockTrait(string spellId, SpellTrait trait)
        {
            var playerSpell = LearnedSpells[spellId];
            playerSpell.unlockedTraits.Add(trait.traitId);

            if (!UnlockedTraits.Contains(trait.traitId))
            {
                UnlockedTraits.Add(trait.traitId);
            }

            PlaySound(traitUnlockSound);
            OnTraitUnlocked?.Invoke(trait);

            Debug.Log($"Unlocked trait {trait.traitName} for spell {playerSpell.spellData.spellName}");
        }
        #endregion

        #region Elemental Combinations
        /// <summary>
        /// Add spell cast to recent casts for combination detection.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <param name="targetPosition">Target position</param>
        private void AddToRecentCasts(string spellId, Vector3 targetPosition)
        {
            var castSpell = new CastSpell
            {
                spellId = spellId,
                castTime = Time.time,
                targetPosition = targetPosition
            };

            recentCasts.Enqueue(castSpell);

            // Maintain queue size
            while (recentCasts.Count > 5)
            {
                recentCasts.Dequeue();
            }
        }

        /// <summary>
        /// Update combination detection window.
        /// </summary>
        private void UpdateCombinationWindow()
        {
            if (Time.time - lastCombinationAttempt < combinationCooldown) return;

            CheckForCombinations();
        }

        /// <summary>
        /// Check for elemental combinations in recent casts.
        /// </summary>
        private void CheckForCombinations()
        {
            if (recentCasts.Count < 2) return;

            var recentCastArray = recentCasts.ToArray();

            for (int i = 0; i < recentCastArray.Length - 1; i++)
            {
                for (int j = i + 1; j < recentCastArray.Length; j++)
                {
                    var combination = FindCombination(recentCastArray[i].spellId, recentCastArray[j].spellId);
                    if (combination != null && CanExecuteCombination(recentCastArray[i], recentCastArray[j]))
                    {
                        ExecuteCombination(combination, recentCastArray[i], recentCastArray[j]);
                        return;
                    }
                }
            }
        }

        /// <summary>
        /// Find combination for two spells.
        /// </summary>
        /// <param name="spellId1">First spell ID</param>
        /// <param name="spellId2">Second spell ID</param>
        /// <returns>Elemental combination or null</returns>
        private ElementalCombination FindCombination(string spellId1, string spellId2)
        {
            var spell1 = GetSpellData(spellId1);
            var spell2 = GetSpellData(spellId2);

            if (spell1 == null || spell2 == null) return null;

            return elementalCombinations.FirstOrDefault(c =>
                (c.element1 == spell1.element && c.element2 == spell2.element) ||
                (c.element1 == spell2.element && c.element2 == spell1.element));
        }

        /// <summary>
        /// Check if combination can be executed.
        /// </summary>
        /// <param name="cast1">First cast</param>
        /// <param name="cast2">Second cast</param>
        /// <returns>True if combination can execute</returns>
        private bool CanExecuteCombination(CastSpell cast1, CastSpell cast2)
        {
            // Check timing window
            float timeDifference = Mathf.Abs(cast1.castTime - cast2.castTime);
            if (timeDifference > 3f) return false;

            // Check proximity
            float distance = Vector3.Distance(cast1.targetPosition, cast2.targetPosition);
            if (distance > 10f) return false;

            return true;
        }

        /// <summary>
        /// Execute elemental combination.
        /// </summary>
        /// <param name="combination">Combination to execute</param>
        /// <param name="cast1">First cast</param>
        /// <param name="cast2">Second cast</param>
        private void ExecuteCombination(ElementalCombination combination, CastSpell cast1, CastSpell cast2)
        {
            Vector3 combinationPosition = (cast1.targetPosition + cast2.targetPosition) / 2f;

            // Create combination spell
            var combinedSpell = CreateCombinationSpell(combination);

            // Execute at combination position
            if (playerController != null)
            {
                ExecuteSpell(combinedSpell, combinationPosition, playerController.transform);
            }

            // Visual effect
            if (combinationEffect != null)
            {
                Instantiate(combinationEffect, combinationPosition, Quaternion.identity);
            }

            PlaySound(combinationSound);
            OnSpellCombined?.Invoke(combination, combinedSpell);

            lastCombinationAttempt = Time.time;
            recentCasts.Clear();

            Debug.Log($"Executed combination: {combination.combinationName}");
        }

        /// <summary>
        /// Create combination spell from elemental combination.
        /// </summary>
        /// <param name="combination">Elemental combination</param>
        /// <returns>Combined player spell</returns>
        private PlayerSpell CreateCombinationSpell(ElementalCombination combination)
        {
            return new PlayerSpell
            {
                spellData = combination.resultSpell,
                level = 1,
                experience = 0f,
                unlockedTraits = new List<string>(),
                evolutionStage = 0
            };
        }
        #endregion

        #region Active Spell Management
        /// <summary>
        /// Update active spells and remove expired ones.
        /// </summary>
        private void UpdateActiveSpells()
        {
            for (int i = activeSpells.Count - 1; i >= 0; i--)
            {
                var activeSpell = activeSpells[i];

                if (Time.time - activeSpell.startTime > activeSpell.duration)
                {
                    activeSpells.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Update magic aura effect based on magic points.
        /// </summary>
        private void UpdateMagicAura()
        {
            if (magicAuraEffect == null) return;

            // Scale aura intensity based on magic points
            float intensity = Mathf.Clamp01(MagicPoints / 1000f);

            var main = magicAuraEffect.main;
            main.startLifetime = intensity * 2f;

            var emission = magicAuraEffect.emission;
            emission.rateOverTime = intensity * 50f;
        }
        #endregion

        #region Magic Points System
        /// <summary>
        /// Add magic points to player.
        /// </summary>
        /// <param name="points">Points to add</param>
        public void AddMagicPoints(int points)
        {
            MagicPoints += points;
            OnMagicPointsChanged?.Invoke(MagicPoints);
            UpdateMagicPointsDisplay();
        }

        /// <summary>
        /// Spend magic points.
        /// </summary>
        /// <param name="points">Points to spend</param>
        /// <returns>True if points were spent</returns>
        public bool SpendMagicPoints(int points)
        {
            if (MagicPoints >= points)
            {
                MagicPoints -= points;
                OnMagicPointsChanged?.Invoke(MagicPoints);
                UpdateMagicPointsDisplay();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Update magic points display.
        /// </summary>
        private void UpdateMagicPointsDisplay()
        {
            if (magicPointsText != null)
            {
                magicPointsText.text = $"Magic Points: {MagicPoints}";
            }
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Toggle magic menu visibility.
        /// </summary>
        public void ToggleMagicMenu()
        {
            if (magicMenuUI != null)
            {
                bool isActive = magicMenuUI.activeSelf;
                magicMenuUI.SetActive(!isActive);

                if (!isActive)
                {
                    RefreshSpellUI();
                }
            }
        }

        /// <summary>
        /// Refresh spell UI display.
        /// </summary>
        private void RefreshSpellUI()
        {
            if (spellListContainer == null || spellSlotPrefab == null) return;

            // Clear existing UI
            foreach (Transform child in spellListContainer)
            {
                Destroy(child.gameObject);
            }

            // Create spell slots
            foreach (var kvp in LearnedSpells)
            {
                CreateSpellSlot(kvp.Value);
            }
        }

        /// <summary>
        /// Create UI slot for spell.
        /// </summary>
        /// <param name="playerSpell">Player spell</param>
        private void CreateSpellSlot(PlayerSpell playerSpell)
        {
            GameObject slotObject = Instantiate(spellSlotPrefab, spellListContainer);

            var spellSlot = slotObject.GetComponent<SpellSlotUI>();
            if (spellSlot != null)
            {
                spellSlot.Setup(playerSpell);
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Get spell data by ID.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <returns>Spell data or null</returns>
        private SpellData GetSpellData(string spellId)
        {
            return baseSpells?.FirstOrDefault(s => s.spellId == spellId);
        }

        /// <summary>
        /// Get spell trait by ID.
        /// </summary>
        /// <param name="traitId">Trait ID</param>
        /// <returns>Spell trait or null</returns>
        private SpellTrait GetSpellTrait(string traitId)
        {
            return availableTraits?.FirstOrDefault(t => t.traitId == traitId);
        }

        /// <summary>
        /// Play audio clip.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save magic system data.
        /// </summary>
        public void SaveMagicData()
        {
            var saveData = new MagicSaveData
            {
                magicPoints = MagicPoints,
                learnedSpells = LearnedSpells.Values.ToArray(),
                unlockedTraits = UnlockedTraits.ToArray(),
                spellUsageCount = spellUsageCount
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("MagicEvolutionData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load magic system data.
        /// </summary>
        private void LoadMagicData()
        {
            string json = PlayerPrefs.GetString("MagicEvolutionData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<MagicSaveData>(json);

                    MagicPoints = saveData.magicPoints;

                    if (saveData.learnedSpells != null)
                    {
                        LearnedSpells.Clear();
                        foreach (var spell in saveData.learnedSpells)
                        {
                            LearnedSpells[spell.spellData.spellId] = spell;
                        }
                    }

                    if (saveData.unlockedTraits != null)
                    {
                        UnlockedTraits = saveData.unlockedTraits.ToList();
                    }

                    if (saveData.spellUsageCount != null)
                    {
                        spellUsageCount = saveData.spellUsageCount;
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load magic data: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get spell by ID.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <returns>Player spell or null</returns>
        public PlayerSpell GetSpell(string spellId)
        {
            return LearnedSpells.ContainsKey(spellId) ? LearnedSpells[spellId] : null;
        }

        /// <summary>
        /// Get all learned spells.
        /// </summary>
        /// <returns>List of learned spells</returns>
        public List<PlayerSpell> GetAllSpells()
        {
            return LearnedSpells.Values.ToList();
        }

        /// <summary>
        /// Check if spell is learned.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <returns>True if spell is learned</returns>
        public bool HasSpell(string spellId)
        {
            return LearnedSpells.ContainsKey(spellId);
        }

        /// <summary>
        /// Get spell usage count.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <returns>Usage count</returns>
        public float GetSpellUsage(string spellId)
        {
            return spellUsageCount.ContainsKey(spellId) ? spellUsageCount[spellId] : 0f;
        }

        /// <summary>
        /// Force evolve spell (for testing).
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        public void ForceEvolveSpell(string spellId)
        {
            if (LearnedSpells.ContainsKey(spellId))
            {
                var playerSpell = LearnedSpells[spellId];
                EvolveSpellLevel(spellId, playerSpell);
            }
        }

        /// <summary>
        /// Reset magic system.
        /// </summary>
        public void ResetMagicSystem()
        {
            LearnedSpells.Clear();
            UnlockedTraits.Clear();
            spellUsageCount.Clear();
            lastSpellCast.Clear();
            activeSpells.Clear();
            recentCasts.Clear();
            MagicPoints = 0;

            InitializeBaseSpells();
            UpdateMagicPointsDisplay();
            RefreshSpellUI();

            Debug.Log("Magic system reset");
        }

        /// <summary>
        /// Get magic system statistics.
        /// </summary>
        /// <returns>Magic statistics</returns>
        public MagicStats GetMagicStats()
        {
            return new MagicStats
            {
                totalSpellsLearned = LearnedSpells.Count,
                totalTraitsUnlocked = UnlockedTraits.Count,
                totalMagicPoints = MagicPoints,
                totalSpellsCast = spellUsageCount.Values.Sum(),
                averageSpellLevel = LearnedSpells.Count > 0 ? LearnedSpells.Values.Average(s => s.level) : 0f
            };
        }

        /// <summary>
        /// Check if trait is unlocked globally.
        /// </summary>
        /// <param name="traitId">Trait ID</param>
        /// <returns>True if trait is unlocked</returns>
        public bool IsTraitUnlocked(string traitId)
        {
            return UnlockedTraits.Contains(traitId);
        }

        /// <summary>
        /// Get available combinations for spell.
        /// </summary>
        /// <param name="spellId">Spell ID</param>
        /// <returns>List of possible combinations</returns>
        public List<ElementalCombination> GetAvailableCombinations(string spellId)
        {
            var spellData = GetSpellData(spellId);
            if (spellData == null) return new List<ElementalCombination>();

            return elementalCombinations.Where(c =>
                c.element1 == spellData.element || c.element2 == spellData.element).ToList();
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class MagicSaveData
    {
        public int magicPoints;
        public PlayerSpell[] learnedSpells;
        public string[] unlockedTraits;
        public Dictionary<string, float> spellUsageCount;
    }

    [System.Serializable]
    public class MagicStats
    {
        public int totalSpellsLearned;
        public int totalTraitsUnlocked;
        public int totalMagicPoints;
        public float totalSpellsCast;
        public float averageSpellLevel;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class SpellData
    {
        public string spellId;
        public string spellName;
        public string description;
        public ElementType element;
        public SpellType spellType;
        public float damage;
        public float manaCost;
        public float cooldown;
        public float duration;
        public float range;
        public GameObject spellPrefab;
        public Sprite spellIcon;
    }

    [System.Serializable]
    public class PlayerSpell
    {
        public SpellData spellData;
        public int level;
        public float experience;
        public List<string> unlockedTraits;
        public int evolutionStage;
    }

    [System.Serializable]
    public class SpellTrait
    {
        public string traitId;
        public string traitName;
        public string description;
        public SpellTraitType traitType;
        public ElementType requiredElement;
        public int requiredLevel;
        public float requiredUsage;
        public float effectValue;
        public Sprite traitIcon;
    }

    [System.Serializable]
    public class ElementalCombination
    {
        public string combinationId;
        public string combinationName;
        public ElementType element1;
        public ElementType element2;
        public SpellData resultSpell;
        public float damageMultiplier;
    }

    [System.Serializable]
    public class ActiveSpell
    {
        public string spellId;
        public float startTime;
        public float duration;
        public Vector3 casterPosition;
        public Vector3 targetPosition;
    }

    [System.Serializable]
    public class CastSpell
    {
        public string spellId;
        public float castTime;
        public Vector3 targetPosition;
    }

    public enum ElementType
    {
        None,
        Fire,
        Water,
        Earth,
        Air,
        Lightning,
        Ice,
        Shadow,
        Light
    }

    public enum SpellType
    {
        Projectile,
        Area,
        Self,
        Target,
        Channel
    }

    public enum SpellTraitType
    {
        Burn,
        Pierce,
        Explode,
        Bounce,
        Homing,
        Multiply,
        Freeze,
        Stun
    }
    #endregion
}
