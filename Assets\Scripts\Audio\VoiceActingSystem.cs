using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using TMPro;

/// <summary>
/// Professional voice acting system for Cinder of Darkness
/// Integrates with AudioManager and DialogueSystem for immersive voice experience
/// </summary>
public class VoiceActingSystem : MonoBehaviour
{
    [Header("Voice Settings")]
    public bool enableVoiceActing = true;
    public bool enableSubtitles = true;
    public float subtitleDisplayDuration = 3f;
    public float voiceVolumeMultiplier = 1f;
    
    [Header("Voice Actors")]
    public VoiceActor[] voiceActors;
    
    [Header("UI References")]
    public GameObject subtitlePanel;
    public TextMeshProUGUI subtitleText;
    public TextMeshProUGUI speakerNameText;
    public CanvasGroup subtitleCanvasGroup;
    
    [Header("Audio Sources")]
    public AudioSource narratorSource;
    public AudioSource dialogueSource;
    public AudioSource ambientVoiceSource;
    
    // Static instance
    private static VoiceActingSystem instance;
    public static VoiceActingSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<VoiceActingSystem>();
                if (instance == null)
                {
                    GameObject go = new GameObject("VoiceActingSystem");
                    instance = go.AddComponent<VoiceActingSystem>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Voice actor data structure
    [System.Serializable]
    public class VoiceActor
    {
        public string actorId;
        public string actorName;
        public VoiceType voiceType;
        public AudioClip[] voiceClips;
        public float pitchVariation = 0.1f;
        public float volumeMultiplier = 1f;
        
        [Header("Character Association")]
        public string[] characterIds;
    }
    
    public enum VoiceType
    {
        Narrator,
        MainCharacter,
        Mentor,
        Child,
        Villain,
        Villager,
        FactionLeader,
        Spirit,
        Boss
    }
    
    // Voice line data
    [System.Serializable]
    public class VoiceLine
    {
        public string lineId;
        public string speakerId;
        public string text;
        public AudioClip audioClip;
        public float duration;
        public bool isImportant;
        public string emotionalTone;
    }
    
    // Private fields
    private Dictionary<string, VoiceActor> voiceActorDatabase = new Dictionary<string, VoiceActor>();
    private Dictionary<string, VoiceLine> voiceLineDatabase = new Dictionary<string, VoiceLine>();
    private Queue<VoiceLine> voiceQueue = new Queue<VoiceLine>();
    private bool isPlayingVoice = false;
    private Coroutine currentSubtitleCoroutine;
    private Coroutine currentVoiceCoroutine;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeVoiceSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        LoadVoiceActors();
        CreatePlaceholderVoiceLines();
        SetupAudioSources();
    }
    
    void InitializeVoiceSystem()
    {
        // Initialize subtitle UI if not assigned
        if (subtitlePanel == null)
        {
            CreateSubtitleUI();
        }
        
        Debug.Log("Voice Acting System initialized");
    }
    
    void CreateSubtitleUI()
    {
        // Create subtitle UI canvas
        GameObject canvas = new GameObject("SubtitleCanvas");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasComponent.sortingOrder = 100;
        
        // Add CanvasScaler
        var scaler = canvas.AddComponent<UnityEngine.UI.CanvasScaler>();
        scaler.uiScaleMode = UnityEngine.UI.CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        // Create subtitle panel
        subtitlePanel = new GameObject("SubtitlePanel");
        subtitlePanel.transform.SetParent(canvas.transform, false);
        
        var panelRect = subtitlePanel.AddComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.1f, 0.1f);
        panelRect.anchorMax = new Vector2(0.9f, 0.3f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Add background
        var panelImage = subtitlePanel.AddComponent<UnityEngine.UI.Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        // Add CanvasGroup for fading
        subtitleCanvasGroup = subtitlePanel.AddComponent<CanvasGroup>();
        subtitleCanvasGroup.alpha = 0f;
        
        // Create speaker name text
        GameObject speakerNameObj = new GameObject("SpeakerName");
        speakerNameObj.transform.SetParent(subtitlePanel.transform, false);
        
        speakerNameText = speakerNameObj.AddComponent<TextMeshProUGUI>();
        speakerNameText.text = "";
        speakerNameText.fontSize = 24;
        speakerNameText.color = Color.yellow;
        speakerNameText.fontStyle = FontStyles.Bold;
        
        var speakerRect = speakerNameObj.GetComponent<RectTransform>();
        speakerRect.anchorMin = new Vector2(0.05f, 0.7f);
        speakerRect.anchorMax = new Vector2(0.95f, 0.95f);
        speakerRect.offsetMin = Vector2.zero;
        speakerRect.offsetMax = Vector2.zero;
        
        // Create subtitle text
        GameObject subtitleTextObj = new GameObject("SubtitleText");
        subtitleTextObj.transform.SetParent(subtitlePanel.transform, false);
        
        subtitleText = subtitleTextObj.AddComponent<TextMeshProUGUI>();
        subtitleText.text = "";
        subtitleText.fontSize = 20;
        subtitleText.color = Color.white;
        subtitleText.alignment = TextAlignmentOptions.TopLeft;
        
        var subtitleRect = subtitleTextObj.GetComponent<RectTransform>();
        subtitleRect.anchorMin = new Vector2(0.05f, 0.05f);
        subtitleRect.anchorMax = new Vector2(0.95f, 0.65f);
        subtitleRect.offsetMin = Vector2.zero;
        subtitleRect.offsetMax = Vector2.zero;
        
        DontDestroyOnLoad(canvas);
    }
    
    void LoadVoiceActors()
    {
        foreach (VoiceActor actor in voiceActors)
        {
            if (!voiceActorDatabase.ContainsKey(actor.actorId))
            {
                voiceActorDatabase.Add(actor.actorId, actor);
            }
        }
        
        Debug.Log($"Loaded {voiceActorDatabase.Count} voice actors");
    }
    
    void CreatePlaceholderVoiceLines()
    {
        // Create placeholder voice lines for key characters
        CreateVoiceLine("narrator_intro", "narrator", "In the beginning, there was fire... and from fire, came the Cinderborn.", VoiceType.Narrator);
        CreateVoiceLine("narrator_outro_good", "narrator", "And so, the light prevailed, bringing hope to all realms.", VoiceType.Narrator);
        CreateVoiceLine("narrator_outro_evil", "narrator", "Darkness consumed the land, and the Cinderborn became legend.", VoiceType.Narrator);
        
        CreateVoiceLine("mentor_greeting", "elder", "Welcome, young one. Your journey begins now.", VoiceType.Mentor);
        CreateVoiceLine("mentor_wisdom", "elder", "Remember, every choice echoes through eternity.", VoiceType.Mentor);
        CreateVoiceLine("mentor_farewell", "elder", "Go forth, and may wisdom guide your path.", VoiceType.Mentor);
        
        CreateVoiceLine("child_hello", "companion", "Are you really the Cinderborn? You look different than I imagined!", VoiceType.Child);
        CreateVoiceLine("child_scared", "companion", "I'm scared... but I trust you to protect us.", VoiceType.Child);
        CreateVoiceLine("child_happy", "companion", "Thank you for playing with us! You're not scary at all!", VoiceType.Child);
        
        CreateVoiceLine("boss_taunt", "shadow_lord", "You think you can defeat me, little ember?", VoiceType.Boss);
        CreateVoiceLine("boss_defeat", "shadow_lord", "Impossible... how can light overcome such darkness?", VoiceType.Boss);
        
        CreateVoiceLine("villager_fear", "villager1", "Stay back! We know what you are!", VoiceType.Villager);
        CreateVoiceLine("villager_gratitude", "villager2", "Thank you, Cinderborn. You saved our village.", VoiceType.Villager);
        
        CreateVoiceLine("faction_light", "light_leader", "The Kingdom of Light welcomes you, champion.", VoiceType.FactionLeader);
        CreateVoiceLine("faction_shadow", "shadow_leader", "Join us, and embrace your true nature.", VoiceType.FactionLeader);
        
        CreateVoiceLine("spirit_whisper", "ancient_spirit", "The path ahead is shrouded in mystery...", VoiceType.Spirit);
        CreateVoiceLine("spirit_warning", "ancient_spirit", "Beware the choices that cannot be undone.", VoiceType.Spirit);
    }
    
    void CreateVoiceLine(string lineId, string speakerId, string text, VoiceType voiceType)
    {
        VoiceLine voiceLine = new VoiceLine
        {
            lineId = lineId,
            speakerId = speakerId,
            text = text,
            audioClip = GeneratePlaceholderAudio(text, voiceType),
            duration = CalculateAudioDuration(text),
            isImportant = voiceType == VoiceType.Narrator || voiceType == VoiceType.Boss,
            emotionalTone = GetEmotionalTone(voiceType)
        };
        
        voiceLineDatabase[lineId] = voiceLine;
    }
    
    AudioClip GeneratePlaceholderAudio(string text, VoiceType voiceType)
    {
        // Generate placeholder audio based on voice type
        // In production, this would load actual recorded audio files
        
        float duration = CalculateAudioDuration(text);
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(duration * sampleRate);
        
        float[] audioData = new float[samples];
        
        // Generate different tones based on voice type
        float frequency = GetVoiceFrequency(voiceType);
        float amplitude = 0.1f;
        
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            audioData[i] = amplitude * Mathf.Sin(2 * Mathf.PI * frequency * time) * Mathf.Exp(-time * 2f);
        }
        
        AudioClip clip = AudioClip.Create($"Voice_{voiceType}_{text.GetHashCode()}", samples, 1, sampleRate, false);
        clip.SetData(audioData, 0);
        
        return clip;
    }
    
    float GetVoiceFrequency(VoiceType voiceType)
    {
        switch (voiceType)
        {
            case VoiceType.Narrator: return 200f;
            case VoiceType.MainCharacter: return 180f;
            case VoiceType.Mentor: return 150f;
            case VoiceType.Child: return 300f;
            case VoiceType.Villain: return 120f;
            case VoiceType.Boss: return 100f;
            case VoiceType.Villager: return 220f;
            case VoiceType.FactionLeader: return 160f;
            case VoiceType.Spirit: return 250f;
            default: return 200f;
        }
    }
    
    float CalculateAudioDuration(string text)
    {
        // Estimate duration based on text length (average speaking rate)
        float wordsPerMinute = 150f;
        int wordCount = text.Split(' ').Length;
        return (wordCount / wordsPerMinute) * 60f + 0.5f; // Add 0.5s padding
    }
    
    string GetEmotionalTone(VoiceType voiceType)
    {
        switch (voiceType)
        {
            case VoiceType.Narrator: return "Epic";
            case VoiceType.MainCharacter: return "Determined";
            case VoiceType.Mentor: return "Wise";
            case VoiceType.Child: return "Innocent";
            case VoiceType.Villain: return "Menacing";
            case VoiceType.Boss: return "Threatening";
            case VoiceType.Villager: return "Fearful";
            case VoiceType.FactionLeader: return "Authoritative";
            case VoiceType.Spirit: return "Mysterious";
            default: return "Neutral";
        }
    }
    
    void SetupAudioSources()
    {
        if (narratorSource == null)
        {
            GameObject narratorObj = new GameObject("NarratorSource");
            narratorObj.transform.SetParent(transform);
            narratorSource = narratorObj.AddComponent<AudioSource>();
            narratorSource.spatialBlend = 0f; // 2D audio
            narratorSource.volume = 0.8f;
        }
        
        if (dialogueSource == null)
        {
            GameObject dialogueObj = new GameObject("DialogueSource");
            dialogueObj.transform.SetParent(transform);
            dialogueSource = dialogueObj.AddComponent<AudioSource>();
            dialogueSource.spatialBlend = 0f; // 2D audio
            dialogueSource.volume = 0.9f;
        }
        
        if (ambientVoiceSource == null)
        {
            GameObject ambientObj = new GameObject("AmbientVoiceSource");
            ambientObj.transform.SetParent(transform);
            ambientVoiceSource = ambientObj.AddComponent<AudioSource>();
            ambientVoiceSource.spatialBlend = 1f; // 3D audio
            ambientVoiceSource.volume = 0.6f;
        }
    }
    
    // Public API
    public static void PlayVoiceLine(string lineId, bool showSubtitles = true)
    {
        Instance.PlayVoiceLineInternal(lineId, showSubtitles);
    }
    
    public static void PlayVoiceLineWithDelay(string lineId, float delay, bool showSubtitles = true)
    {
        Instance.StartCoroutine(Instance.PlayVoiceLineDelayed(lineId, delay, showSubtitles));
    }
    
    public static void QueueVoiceLine(string lineId, bool showSubtitles = true)
    {
        Instance.QueueVoiceLineInternal(lineId, showSubtitles);
    }
    
    public static void StopCurrentVoice()
    {
        Instance.StopCurrentVoiceInternal();
    }
    
    public static bool IsPlayingVoice()
    {
        return Instance.isPlayingVoice;
    }
    
    void PlayVoiceLineInternal(string lineId, bool showSubtitles = true)
    {
        if (!enableVoiceActing) return;
        
        if (voiceLineDatabase.ContainsKey(lineId))
        {
            VoiceLine voiceLine = voiceLineDatabase[lineId];
            
            if (currentVoiceCoroutine != null)
            {
                StopCoroutine(currentVoiceCoroutine);
            }
            
            currentVoiceCoroutine = StartCoroutine(PlayVoiceCoroutine(voiceLine, showSubtitles));
        }
        else
        {
            Debug.LogWarning($"Voice line not found: {lineId}");
        }
    }
    
    IEnumerator PlayVoiceLineDelayed(string lineId, float delay, bool showSubtitles = true)
    {
        yield return new WaitForSeconds(delay);
        PlayVoiceLineInternal(lineId, showSubtitles);
    }
    
    void QueueVoiceLineInternal(string lineId, bool showSubtitles = true)
    {
        if (voiceLineDatabase.ContainsKey(lineId))
        {
            VoiceLine voiceLine = voiceLineDatabase[lineId];
            voiceQueue.Enqueue(voiceLine);
            
            if (!isPlayingVoice)
            {
                ProcessVoiceQueue();
            }
        }
    }
    
    void ProcessVoiceQueue()
    {
        if (voiceQueue.Count > 0 && !isPlayingVoice)
        {
            VoiceLine nextLine = voiceQueue.Dequeue();
            currentVoiceCoroutine = StartCoroutine(PlayVoiceCoroutine(nextLine, enableSubtitles));
        }
    }
    
    IEnumerator PlayVoiceCoroutine(VoiceLine voiceLine, bool showSubtitles)
    {
        isPlayingVoice = true;
        
        // Determine audio source based on voice type
        AudioSource targetSource = GetAudioSourceForVoiceLine(voiceLine);
        
        // Apply voice settings
        targetSource.clip = voiceLine.audioClip;
        targetSource.volume = voiceVolumeMultiplier * GetVolumeForVoiceType(voiceLine);
        targetSource.pitch = 1f + Random.Range(-0.05f, 0.05f); // Slight pitch variation
        
        // Show subtitles if enabled
        if (showSubtitles && enableSubtitles)
        {
            ShowSubtitles(voiceLine);
        }
        
        // Play audio
        targetSource.Play();
        
        // Wait for audio to finish
        yield return new WaitForSeconds(voiceLine.duration);
        
        // Hide subtitles
        if (showSubtitles && enableSubtitles)
        {
            HideSubtitles();
        }
        
        isPlayingVoice = false;
        currentVoiceCoroutine = null;
        
        // Process next item in queue
        ProcessVoiceQueue();
    }
    
    AudioSource GetAudioSourceForVoiceLine(VoiceLine voiceLine)
    {
        VoiceActor actor = GetVoiceActorForSpeaker(voiceLine.speakerId);
        
        if (actor != null)
        {
            switch (actor.voiceType)
            {
                case VoiceType.Narrator:
                    return narratorSource;
                case VoiceType.Spirit:
                    return ambientVoiceSource;
                default:
                    return dialogueSource;
            }
        }
        
        return dialogueSource;
    }
    
    VoiceActor GetVoiceActorForSpeaker(string speakerId)
    {
        foreach (var actor in voiceActorDatabase.Values)
        {
            if (System.Array.Exists(actor.characterIds, id => id == speakerId))
            {
                return actor;
            }
        }
        return null;
    }
    
    float GetVolumeForVoiceType(VoiceLine voiceLine)
    {
        VoiceActor actor = GetVoiceActorForSpeaker(voiceLine.speakerId);
        
        if (actor != null)
        {
            return actor.volumeMultiplier;
        }
        
        return 1f;
    }
    
    void ShowSubtitles(VoiceLine voiceLine)
    {
        if (subtitlePanel == null) return;
        
        // Set speaker name
        VoiceActor actor = GetVoiceActorForSpeaker(voiceLine.speakerId);
        string speakerName = actor != null ? actor.actorName : voiceLine.speakerId;
        
        if (speakerNameText != null)
        {
            speakerNameText.text = speakerName;
        }
        
        // Set subtitle text
        if (subtitleText != null)
        {
            subtitleText.text = voiceLine.text;
        }
        
        // Fade in subtitles
        if (currentSubtitleCoroutine != null)
        {
            StopCoroutine(currentSubtitleCoroutine);
        }
        
        currentSubtitleCoroutine = StartCoroutine(FadeSubtitles(true));
    }
    
    void HideSubtitles()
    {
        if (currentSubtitleCoroutine != null)
        {
            StopCoroutine(currentSubtitleCoroutine);
        }
        
        currentSubtitleCoroutine = StartCoroutine(FadeSubtitles(false));
    }
    
    IEnumerator FadeSubtitles(bool fadeIn)
    {
        if (subtitleCanvasGroup == null) yield break;
        
        float startAlpha = subtitleCanvasGroup.alpha;
        float targetAlpha = fadeIn ? 1f : 0f;
        float duration = 0.3f;
        
        for (float t = 0; t < duration; t += Time.deltaTime)
        {
            float progress = t / duration;
            subtitleCanvasGroup.alpha = Mathf.Lerp(startAlpha, targetAlpha, progress);
            yield return null;
        }
        
        subtitleCanvasGroup.alpha = targetAlpha;
        currentSubtitleCoroutine = null;
    }
    
    void StopCurrentVoiceInternal()
    {
        if (currentVoiceCoroutine != null)
        {
            StopCoroutine(currentVoiceCoroutine);
            currentVoiceCoroutine = null;
        }
        
        narratorSource.Stop();
        dialogueSource.Stop();
        ambientVoiceSource.Stop();
        
        HideSubtitles();
        isPlayingVoice = false;
    }
    
    // Integration with game systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        switch (eventName)
        {
            case "GameStarted":
                PlayVoiceLineWithDelay("narrator_intro", 1f);
                break;
                
            case "GameCompleted":
                string moralPath = parameters?.ContainsKey("moralPath") == true ? parameters["moralPath"].ToString() : "";
                if (moralPath == "Good")
                    PlayVoiceLine("narrator_outro_good");
                else if (moralPath == "Evil")
                    PlayVoiceLine("narrator_outro_evil");
                break;
                
            case "MentorEncounter":
                QueueVoiceLine("mentor_greeting");
                break;
                
            case "ChildInteraction":
                QueueVoiceLine("child_hello");
                break;
                
            case "BossEncounter":
                QueueVoiceLine("boss_taunt");
                break;
                
            case "BossDefeated":
                PlayVoiceLineWithDelay("boss_defeat", 0.5f);
                break;
                
            case "VillagerFear":
                QueueVoiceLine("villager_fear");
                break;
                
            case "VillagerGratitude":
                QueueVoiceLine("villager_gratitude");
                break;
                
            case "SpiritWhisper":
                PlayVoiceLine("spirit_whisper");
                break;
        }
    }
    
    // Settings integration
    public void SetVoiceActingEnabled(bool enabled)
    {
        enableVoiceActing = enabled;
        if (!enabled)
        {
            StopCurrentVoiceInternal();
        }
    }
    
    public void SetSubtitlesEnabled(bool enabled)
    {
        enableSubtitles = enabled;
        if (!enabled)
        {
            HideSubtitles();
        }
    }
    
    public void SetVoiceVolume(float volume)
    {
        voiceVolumeMultiplier = Mathf.Clamp01(volume);
    }
    
    // Debug methods
    public void TestVoiceLine(string lineId)
    {
        #if UNITY_EDITOR
        PlayVoiceLine(lineId);
        #endif
    }
    
    public void ListAvailableVoiceLines()
    {
        #if UNITY_EDITOR
        Debug.Log("Available voice lines:");
        foreach (var line in voiceLineDatabase.Keys)
        {
            Debug.Log($"- {line}");
        }
        #endif
    }
}
