using UnityEngine;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;

/// <summary>
/// Automatically generates visual material assets when the project loads
/// Ensures all systems have functional material references
/// </summary>
[InitializeOnLoad]
public class AutoGenerateMaterials
{
    static AutoGenerateMaterials()
    {
        // Check if materials exist, generate if missing
        EditorApplication.delayCall += CheckAndGenerateMaterials;
    }
    
    static void CheckAndGenerateMaterials()
    {
        string materialDir = "Assets/Materials/Effects";
        string assetPath = "Assets/Materials/Effects/CinderMaterialAsset.asset";
        
        // Check if the material asset exists
        if (!File.Exists(assetPath))
        {
            Debug.Log("Material assets not found. Generating placeholder materials...");
            GenerateAllMaterials();
        }
        else
        {
            // Verify the asset has valid materials
            CinderMaterialAsset asset = AssetDatabase.LoadAssetAtPath<CinderMaterialAsset>(assetPath);
            if (asset == null || !asset.ValidateAsset())
            {
                Debug.Log("Material asset is incomplete. Regenerating materials...");
                GenerateAllMaterials();
            }
        }
    }
    
    static void GenerateAllMaterials()
    {
        // Create directory structure
        CreateMaterialDirectories();
        
        // Generate all material types
        GenerateFireMaterials();
        GenerateAshMaterials();
        GenerateBloodMaterials();
        GenerateSpiritMaterials();
        GenerateCombatFXMaterials();
        
        // Create material asset collection
        CreateMaterialAssetCollection();
        
        AssetDatabase.Refresh();
        Debug.Log("All visual materials generated successfully!");
    }
    
    static void CreateMaterialDirectories()
    {
        string[] directories = {
            "Assets/Materials",
            "Assets/Materials/Effects",
            "Assets/Materials/Effects/Fire",
            "Assets/Materials/Effects/Ash",
            "Assets/Materials/Effects/Blood",
            "Assets/Materials/Effects/Spirit",
            "Assets/Materials/Effects/CombatFX"
        };
        
        foreach (string dir in directories)
        {
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
        }
    }
    
    static void GenerateFireMaterials()
    {
        // Soft Flame Material
        Material softFlame = CreateFireMaterial("SoftFlame", 
            new Color(1f, 0.6f, 0.2f, 0.8f), 
            new Color(1f, 0.4f, 0.1f) * 2f, 
            0.7f);
        AssetDatabase.CreateAsset(softFlame, "Assets/Materials/Effects/Fire/SoftFlame.mat");
        
        // Ember Glow Material
        Material emberGlow = CreateFireMaterial("EmberGlow", 
            new Color(1f, 0.3f, 0.1f, 0.6f), 
            new Color(1f, 0.2f, 0.05f) * 1.5f, 
            0.5f);
        AssetDatabase.CreateAsset(emberGlow, "Assets/Materials/Effects/Fire/EmberGlow.mat");
        
        // Torch Flame Material
        Material torchFlame = CreateFireMaterial("TorchFlame", 
            new Color(1f, 0.7f, 0.3f, 0.9f), 
            new Color(1f, 0.5f, 0.2f) * 3f, 
            0.8f);
        AssetDatabase.CreateAsset(torchFlame, "Assets/Materials/Effects/Fire/TorchFlame.mat");
        
        // Weapon Enchantment Material
        Material weaponEnchant = CreateFireMaterial("WeaponEnchantment", 
            new Color(0.8f, 0.4f, 1f, 0.7f), 
            new Color(0.6f, 0.2f, 1f) * 2.5f, 
            0.6f);
        AssetDatabase.CreateAsset(weaponEnchant, "Assets/Materials/Effects/Fire/WeaponEnchantment.mat");
    }
    
    static void GenerateAshMaterials()
    {
        // Floating Ash Particles Material
        Material floatingAsh = CreateAshMaterial("FloatingAsh", 
            new Color(0.7f, 0.7f, 0.7f, 0.3f), 
            0.3f);
        AssetDatabase.CreateAsset(floatingAsh, "Assets/Materials/Effects/Ash/FloatingAsh.mat");
        
        // Ash Fog Material
        Material ashFog = CreateAshMaterial("AshFog", 
            new Color(0.5f, 0.5f, 0.5f, 0.2f), 
            0.2f);
        AssetDatabase.CreateAsset(ashFog, "Assets/Materials/Effects/Ash/AshFog.mat");
        
        // Cursed Ash Material
        Material cursedAsh = CreateAshMaterial("CursedAsh", 
            new Color(0.4f, 0.3f, 0.3f, 0.4f), 
            0.4f);
        AssetDatabase.CreateAsset(cursedAsh, "Assets/Materials/Effects/Ash/CursedAsh.mat");
        
        // Dream State Ash Material
        Material dreamAsh = CreateAshMaterial("DreamAsh", 
            new Color(0.8f, 0.8f, 0.9f, 0.25f), 
            0.25f);
        AssetDatabase.CreateAsset(dreamAsh, "Assets/Materials/Effects/Ash/DreamAsh.mat");
    }
    
    static void GenerateBloodMaterials()
    {
        // Fresh Blood Material
        Material freshBlood = CreateBloodMaterial("FreshBlood", 
            new Color(0.8f, 0.1f, 0.1f, 1f), 
            0.8f, 
            true);
        AssetDatabase.CreateAsset(freshBlood, "Assets/Materials/Effects/Blood/FreshBlood.mat");
        
        // Dried Blood Material
        Material driedBlood = CreateBloodMaterial("DriedBlood", 
            new Color(0.4f, 0.1f, 0.1f, 1f), 
            0.2f, 
            false);
        AssetDatabase.CreateAsset(driedBlood, "Assets/Materials/Effects/Blood/DriedBlood.mat");
        
        // Blood Stain Material
        Material bloodStain = CreateBloodMaterial("BloodStain", 
            new Color(0.3f, 0.05f, 0.05f, 0.8f), 
            0.1f, 
            false);
        AssetDatabase.CreateAsset(bloodStain, "Assets/Materials/Effects/Blood/BloodStain.mat");
        
        // Gore Material
        Material gore = CreateBloodMaterial("Gore", 
            new Color(0.6f, 0.1f, 0.1f, 1f), 
            0.5f, 
            true);
        AssetDatabase.CreateAsset(gore, "Assets/Materials/Effects/Blood/Gore.mat");
    }
    
    static void GenerateSpiritMaterials()
    {
        // Translucent Spirit Material
        Material translucentSpirit = CreateSpiritMaterial("TranslucentSpirit", 
            new Color(0.7f, 0.9f, 1f, 0.3f), 
            new Color(0.5f, 0.8f, 1f) * 0.5f, 
            0.3f);
        AssetDatabase.CreateAsset(translucentSpirit, "Assets/Materials/Effects/Spirit/TranslucentSpirit.mat");
        
        // Spectral Glow Material
        Material spectralGlow = CreateSpiritMaterial("SpectralGlow", 
            new Color(0.6f, 0.8f, 1f, 0.4f), 
            new Color(0.4f, 0.7f, 1f) * 1f, 
            0.4f);
        AssetDatabase.CreateAsset(spectralGlow, "Assets/Materials/Effects/Spirit/SpectralGlow.mat");
        
        // Ghost Manifestation Material
        Material ghostManifestation = CreateSpiritMaterial("GhostManifestation", 
            new Color(0.8f, 0.9f, 1f, 0.25f), 
            new Color(0.6f, 0.8f, 1f) * 0.3f, 
            0.25f);
        AssetDatabase.CreateAsset(ghostManifestation, "Assets/Materials/Effects/Spirit/GhostManifestation.mat");
        
        // Dream Echo Material
        Material dreamEcho = CreateSpiritMaterial("DreamEcho", 
            new Color(0.9f, 0.8f, 1f, 0.2f), 
            new Color(0.8f, 0.6f, 1f) * 0.4f, 
            0.2f);
        AssetDatabase.CreateAsset(dreamEcho, "Assets/Materials/Effects/Spirit/DreamEcho.mat");
    }
    
    static void GenerateCombatFXMaterials()
    {
        // Slash Trail Material
        Material slashTrail = CreateCombatFXMaterial("SlashTrail", 
            new Color(1f, 1f, 0.8f, 0.6f), 
            new Color(1f, 0.9f, 0.7f) * 1.5f, 
            true);
        AssetDatabase.CreateAsset(slashTrail, "Assets/Materials/Effects/CombatFX/SlashTrail.mat");
        
        // Weapon Glow Material
        Material weaponGlow = CreateCombatFXMaterial("WeaponGlow", 
            new Color(0.9f, 0.9f, 1f, 0.8f), 
            new Color(0.8f, 0.8f, 1f) * 2f, 
            false);
        AssetDatabase.CreateAsset(weaponGlow, "Assets/Materials/Effects/CombatFX/WeaponGlow.mat");
        
        // Hit Impact Material
        Material hitImpact = CreateCombatFXMaterial("HitImpact", 
            new Color(1f, 0.7f, 0.3f, 0.9f), 
            new Color(1f, 0.5f, 0.2f) * 3f, 
            true);
        AssetDatabase.CreateAsset(hitImpact, "Assets/Materials/Effects/CombatFX/HitImpact.mat");
        
        // Strong Attack Distortion Material
        Material strongAttack = CreateCombatFXMaterial("StrongAttackDistortion", 
            new Color(1f, 0.3f, 0.3f, 0.7f), 
            new Color(1f, 0.2f, 0.2f) * 2.5f, 
            true);
        AssetDatabase.CreateAsset(strongAttack, "Assets/Materials/Effects/CombatFX/StrongAttackDistortion.mat");
        
        // Block Effect Material
        Material blockEffect = CreateCombatFXMaterial("BlockEffect", 
            new Color(0.8f, 0.8f, 1f, 0.8f), 
            new Color(0.6f, 0.6f, 1f) * 1.8f, 
            false);
        AssetDatabase.CreateAsset(blockEffect, "Assets/Materials/Effects/CombatFX/BlockEffect.mat");
    }
    
    static Material CreateFireMaterial(string name, Color baseColor, Color emissionColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.2f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    static Material CreateAshMaterial(string name, Color baseColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.1f);
        
        return material;
    }
    
    static Material CreateBloodMaterial(string name, Color baseColor, float smoothness, bool isWet)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to opaque or transparent based on alpha
        if (baseColor.a < 1f)
        {
            material.SetFloat("_Surface", 1); // Transparent
            material.SetFloat("_Blend", 0); // Alpha blend
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.renderQueue = 3000;
        }
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", isWet ? 0.1f : 0f);
        material.SetFloat("_Smoothness", smoothness);
        
        return material;
    }
    
    static Material CreateSpiritMaterial(string name, Color baseColor, Color emissionColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.8f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    static Material CreateCombatFXMaterial(string name, Color baseColor, Color emissionColor, bool hasDistortion)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", hasDistortion ? 0.9f : 0.5f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    static Shader GetURPShader(string shaderName)
    {
        // Try to find URP shader first
        Shader urpShader = Shader.Find($"Universal Render Pipeline/{shaderName}");
        if (urpShader != null)
            return urpShader;
        
        // Fallback to built-in shader
        Shader builtinShader = Shader.Find($"Standard");
        if (builtinShader != null)
            return builtinShader;
        
        // Final fallback
        return Shader.Find("Sprites/Default");
    }
    
    static void CreateMaterialAssetCollection()
    {
        // Create a ScriptableObject to hold all material references
        CinderMaterialAsset asset = ScriptableObject.CreateInstance<CinderMaterialAsset>();
        
        // Load fire materials
        asset.softFlame = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/SoftFlame.mat");
        asset.emberGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/EmberGlow.mat");
        asset.torchFlame = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/TorchFlame.mat");
        asset.weaponEnchantment = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/WeaponEnchantment.mat");
        
        // Load ash materials
        asset.floatingAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/FloatingAsh.mat");
        asset.ashFog = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/AshFog.mat");
        asset.cursedAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/CursedAsh.mat");
        asset.dreamAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/DreamAsh.mat");
        
        // Load blood materials
        asset.freshBlood = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/FreshBlood.mat");
        asset.driedBlood = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/DriedBlood.mat");
        asset.bloodStain = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/BloodStain.mat");
        asset.gore = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/Gore.mat");
        
        // Load spirit materials
        asset.translucentSpirit = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/TranslucentSpirit.mat");
        asset.spectralGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/SpectralGlow.mat");
        asset.ghostManifestation = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/GhostManifestation.mat");
        asset.dreamEcho = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/DreamEcho.mat");
        
        // Load combat FX materials
        asset.slashTrail = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/SlashTrail.mat");
        asset.weaponGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/WeaponGlow.mat");
        asset.hitImpact = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/HitImpact.mat");
        asset.strongAttackDistortion = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/StrongAttackDistortion.mat");
        asset.blockEffect = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/BlockEffect.mat");
        
        string assetPath = "Assets/Materials/Effects/CinderMaterialAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);
        
        // Also create a copy in Resources folder for runtime loading
        string resourcesDir = "Assets/Resources";
        if (!Directory.Exists(resourcesDir))
        {
            Directory.CreateDirectory(resourcesDir);
        }
        
        string resourcesPath = "Assets/Resources/CinderMaterialAsset.asset";
        AssetDatabase.CopyAsset(assetPath, resourcesPath);
        
        AssetDatabase.SaveAssets();
        
        Debug.Log($"Created CinderMaterialAsset with 20 materials at {assetPath}");
        Debug.Log($"Also created Resources copy at {resourcesPath} for runtime loading");
    }
}
#endif
