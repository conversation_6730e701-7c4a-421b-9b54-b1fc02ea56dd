using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class ArtisticWorldFeedbackSystem : MonoBehaviour
{
    [Header("Mural System")]
    public List<DynamicMural> worldMurals = new List<DynamicMural>();
    public Material[] muralMaterials;
    public Texture2D[] legendTextures;
    
    [Header("Children's Art")]
    public List<ChildArt> childrenDrawings = new List<ChildArt>();
    public GameObject[] artCanvasPrefabs;
    public Material[] childArtMaterials;
    
    [Header("Ancient Books")]
    public List<WhisperingBook> ancientBooks = new List<WhisperingBook>();
    public AudioClip[] whisperSounds;
    public GameObject[] visionEffects;
    
    [Header("World Feedback")]
    public float reputationInfluenceRadius = 20f;
    public float artUpdateFrequency = 30f; // Seconds
    private float lastArtUpdate = 0f;
    
    private DynamicTitleSystem titleSystem;
    private PhilosophicalMoralitySystem moralitySystem;
    private DeathConsequenceSystem deathSystem;
    private TimeProgressionSystem timeSystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class DynamicMural
    {
        [Header("Mural Identity")]
        public string muralName;
        public GameObject muralObject;
        public MuralType type;
        public string currentLegend;
        
        [Header("Evolution")]
        public List<MuralStage> stages;
        public int currentStage = 0;
        public float lastUpdateTime;
        public bool hasChanged = false;
        
        [Header("Cultural Context")]
        public string culturalOrigin;
        public string[] historicalEvents;
        public float culturalSignificance;
        
        public enum MuralType
        {
            LocalLegend,
            HistoricalEvent,
            ProphecyDepiction,
            HeroicTale,
            CautionaryStory,
            CinderbornChronicle
        }
    }
    
    [System.Serializable]
    public class MuralStage
    {
        public string stageDescription;
        public Texture2D stageTexture;
        public string[] stageText;
        public float requiredReputation;
        public string triggerEvent;
    }
    
    [System.Serializable]
    public class ChildArt
    {
        [Header("Art Identity")]
        public string artName;
        public GameObject artObject;
        public string childArtist;
        public ArtStyle style;
        
        [Header("Reputation Reflection")]
        public ReputationType currentReputation;
        public Texture2D artTexture;
        public string artDescription;
        public float creationTime;
        
        [Header("Evolution")]
        public bool canEvolve = true;
        public float lastUpdateTime;
        public List<ArtEvolution> evolutionStages;
        
        public enum ArtStyle
        {
            CrayonDrawing,
            CharcoalSketch,
            PaintedPortrait,
            ScratchedWall,
            SandDrawing
        }
        
        public enum ReputationType
        {
            Heroic,      // Wings, halo, bright colors
            Villainous,  // Horns, dark colors, scary features
            Mysterious,  // Shadowy, unclear features
            Neutral,     // Normal human appearance
            Monstrous    // Completely inhuman depiction
        }
    }
    
    [System.Serializable]
    public class ArtEvolution
    {
        public ReputationType targetReputation;
        public Texture2D evolutionTexture;
        public string evolutionDescription;
        public AudioClip childReaction;
    }
    
    [System.Serializable]
    public class WhisperingBook
    {
        [Header("Book Identity")]
        public string bookTitle;
        public GameObject bookObject;
        public BookType type;
        public string[] whisperTexts;
        
        [Header("Vision System")]
        public VisionSequence[] visionSequences;
        public bool hasBeenRead = false;
        public float lastReadTime;
        
        [Header("Knowledge")]
        public string[] revealedSecrets;
        public string[] unlockedDialogues;
        public float wisdomValue;
        
        public enum BookType
        {
            AncientHistory,
            PropheticVision,
            PersonalDiary,
            RitualManual,
            PhilosophicalTreatise,
            WarRecord
        }
    }
    
    [System.Serializable]
    public class VisionSequence
    {
        public string visionName;
        public string visionDescription;
        public GameObject visionEffect;
        public AudioClip visionSound;
        public float visionDuration;
        public string[] visionDialogue;
    }
    
    void Start()
    {
        titleSystem = GetComponent<DynamicTitleSystem>();
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        timeSystem = GetComponent<TimeProgressionSystem>();
        gameManager = GameManager.Instance;
        
        InitializeArtisticSystems();
    }
    
    void Update()
    {
        if (Time.time - lastArtUpdate > artUpdateFrequency)
        {
            UpdateWorldArt();
            lastArtUpdate = Time.time;
        }
        
        CheckForBookInteractions();
        UpdateMuralEvolution();
    }
    
    void InitializeArtisticSystems()
    {
        InitializeMurals();
        InitializeChildrenArt();
        InitializeAncientBooks();
        
        Debug.Log("Artistic World Feedback System initialized - the world will reflect your journey");
    }
    
    void InitializeMurals()
    {
        // Find mural objects in the world
        GameObject[] muralObjects = GameObject.FindGameObjectsWithTag("Mural");
        
        foreach (GameObject muralObj in muralObjects)
        {
            DynamicMural mural = new DynamicMural
            {
                muralName = muralObj.name,
                muralObject = muralObj,
                type = DynamicMural.MuralType.LocalLegend,
                currentLegend = "Ancient legends speak of a warrior born from ash...",
                culturalOrigin = "Local folklore",
                culturalSignificance = 50f,
                stages = CreateMuralStages()
            };
            
            worldMurals.Add(mural);
        }
        
        Debug.Log($"Initialized {worldMurals.Count} dynamic murals");
    }
    
    List<MuralStage> CreateMuralStages()
    {
        return new List<MuralStage>
        {
            new MuralStage
            {
                stageDescription = "Unknown warrior shrouded in mystery",
                requiredReputation = -100f,
                triggerEvent = "FirstEncounter"
            },
            new MuralStage
            {
                stageDescription = "Heroic figure with wings of light",
                requiredReputation = 50f,
                triggerEvent = "HeroicReputation"
            },
            new MuralStage
            {
                stageDescription = "Dark figure with horns and shadow",
                requiredReputation = -50f,
                triggerEvent = "VillainousReputation"
            },
            new MuralStage
            {
                stageDescription = "Balanced figure of ash and flame",
                requiredReputation = 0f,
                triggerEvent = "BalancedPath"
            }
        };
    }
    
    void InitializeChildrenArt()
    {
        // Find art canvas objects in public spaces
        GameObject[] canvasObjects = GameObject.FindGameObjectsWithTag("ChildArt");
        
        foreach (GameObject canvas in canvasObjects)
        {
            ChildArt art = new ChildArt
            {
                artName = $"Drawing by {GenerateChildName()}",
                artObject = canvas,
                childArtist = GenerateChildName(),
                style = (ChildArt.ArtStyle)Random.Range(0, 5),
                currentReputation = ChildArt.ReputationType.Neutral,
                artDescription = "A simple drawing of a person with ash-colored hair",
                creationTime = Time.time,
                canEvolve = true,
                evolutionStages = CreateArtEvolutionStages()
            };
            
            childrenDrawings.Add(art);
        }
        
        Debug.Log($"Initialized {childrenDrawings.Count} children's art pieces");
    }
    
    string GenerateChildName()
    {
        string[] childNames = { "Little Emma", "Young Tom", "Small Sara", "Tiny Jack", "Little Maya", "Young Alex" };
        return childNames[Random.Range(0, childNames.Length)];
    }
    
    List<ArtEvolution> CreateArtEvolutionStages()
    {
        return new List<ArtEvolution>
        {
            new ArtEvolution
            {
                targetReputation = ChildArt.ReputationType.Heroic,
                evolutionDescription = "The child has drawn you with bright wings and a golden halo"
            },
            new ArtEvolution
            {
                targetReputation = ChildArt.ReputationType.Villainous,
                evolutionDescription = "The child has drawn you with dark horns and red eyes"
            },
            new ArtEvolution
            {
                targetReputation = ChildArt.ReputationType.Monstrous,
                evolutionDescription = "The child has drawn you as a terrifying monster with claws and fangs"
            }
        };
    }
    
    void InitializeAncientBooks()
    {
        // Find book objects in the world
        GameObject[] bookObjects = GameObject.FindGameObjectsWithTag("AncientBook");
        
        foreach (GameObject bookObj in bookObjects)
        {
            WhisperingBook book = new WhisperingBook
            {
                bookTitle = GenerateBookTitle(),
                bookObject = bookObj,
                type = (WhisperingBook.BookType)Random.Range(0, 6),
                whisperTexts = GenerateWhisperTexts(),
                visionSequences = CreateVisionSequences(),
                wisdomValue = Random.Range(10f, 50f)
            };
            
            ancientBooks.Add(book);
        }
        
        Debug.Log($"Initialized {ancientBooks.Count} whispering ancient books");
    }
    
    string GenerateBookTitle()
    {
        string[] titles = {
            "Chronicles of the Ash-Born",
            "Prophecies of Fire and Shadow",
            "The Last Ember's Testament",
            "Whispers from the Void",
            "Songs of the Forgotten Flame",
            "The Cinderborn Prophecy"
        };
        return titles[Random.Range(0, titles.Length)];
    }
    
    string[] GenerateWhisperTexts()
    {
        return new string[]
        {
            "The one born of ash shall walk between light and shadow...",
            "In the final hour, the ember shall either save or consume all...",
            "Beware the child of cinders, for they carry both creation and destruction...",
            "The flame that burns within can illuminate or incinerate...",
            "From the ashes of the old world, a new one shall rise..."
        };
    }
    
    VisionSequence[] CreateVisionSequences()
    {
        return new VisionSequence[]
        {
            new VisionSequence
            {
                visionName = "The First Flame",
                visionDescription = "You see the birth of the first flame, the source of all fire magic",
                visionDuration = 10f,
                visionDialogue = new string[]
                {
                    "In the beginning, there was only darkness...",
                    "Then came the spark that would change everything...",
                    "The First Flame, eternal and pure..."
                }
            },
            new VisionSequence
            {
                visionName = "The Ash Prophecy",
                visionDescription = "Visions of possible futures flash before your eyes",
                visionDuration = 15f,
                visionDialogue = new string[]
                {
                    "You see yourself standing over a world of ash...",
                    "Or perhaps bringing new life to barren lands...",
                    "The future remains unwritten..."
                }
            }
        };
    }
    
    void UpdateWorldArt()
    {
        UpdateMurals();
        UpdateChildrenArt();
        UpdateBookAvailability();
    }
    
    void UpdateMurals()
    {
        foreach (DynamicMural mural in worldMurals)
        {
            if (ShouldUpdateMural(mural))
            {
                UpdateMuralContent(mural);
            }
        }
    }
    
    bool ShouldUpdateMural(DynamicMural mural)
    {
        // Check if enough time has passed and reputation has changed significantly
        float timeSinceUpdate = Time.time - mural.lastUpdateTime;
        return timeSinceUpdate > 60f && HasReputationChanged();
    }
    
    bool HasReputationChanged()
    {
        // Check if player's reputation or title has changed significantly
        return titleSystem != null && titleSystem.GetCurrentTitleName() != "Wandering Ash";
    }
    
    void UpdateMuralContent(DynamicMural mural)
    {
        string currentTitle = titleSystem?.GetCurrentTitleName() ?? "Unknown";
        float reputation = GetCurrentReputation();
        
        // Find appropriate stage based on reputation
        MuralStage newStage = FindAppropriateStage(mural, reputation);
        
        if (newStage != null && mural.currentStage < mural.stages.Count - 1)
        {
            mural.currentStage++;
            mural.lastUpdateTime = Time.time;
            mural.hasChanged = true;
            
            UpdateMuralVisuals(mural, newStage);
            ShowArtMessage($"The mural in {mural.muralName} has been updated to reflect your deeds");
        }
    }
    
    MuralStage FindAppropriateStage(DynamicMural mural, float reputation)
    {
        foreach (MuralStage stage in mural.stages)
        {
            if (reputation >= stage.requiredReputation)
                return stage;
        }
        return null;
    }
    
    void UpdateMuralVisuals(DynamicMural mural, MuralStage stage)
    {
        if (mural.muralObject != null && stage.stageTexture != null)
        {
            Renderer renderer = mural.muralObject.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.mainTexture = stage.stageTexture;
            }
        }
    }
    
    void UpdateChildrenArt()
    {
        foreach (ChildArt art in childrenDrawings)
        {
            if (ShouldUpdateChildArt(art))
            {
                UpdateChildArtContent(art);
            }
        }
    }
    
    bool ShouldUpdateChildArt(ChildArt art)
    {
        float timeSinceUpdate = Time.time - art.lastUpdateTime;
        return art.canEvolve && timeSinceUpdate > 45f && HasReputationChanged();
    }
    
    void UpdateChildArtContent(ChildArt art)
    {
        ChildArt.ReputationType newReputation = DetermineChildArtReputation();
        
        if (newReputation != art.currentReputation)
        {
            art.currentReputation = newReputation;
            art.lastUpdateTime = Time.time;
            
            UpdateChildArtVisuals(art, newReputation);
            ShowArtMessage($"{art.childArtist} has updated their drawing of you");
        }
    }
    
    ChildArt.ReputationType DetermineChildArtReputation()
    {
        string currentTitle = titleSystem?.GetCurrentTitleName() ?? "";
        
        if (currentTitle.Contains("Redeemed") || currentTitle.Contains("Ashspeaker"))
            return ChildArt.ReputationType.Heroic;
        else if (currentTitle.Contains("Butcher") || currentTitle.Contains("Reaper"))
            return ChildArt.ReputationType.Villainous;
        else if (currentTitle.Contains("Voidwalker"))
            return ChildArt.ReputationType.Monstrous;
        else if (currentTitle.Contains("Wandering"))
            return ChildArt.ReputationType.Mysterious;
        else
            return ChildArt.ReputationType.Neutral;
    }
    
    void UpdateChildArtVisuals(ChildArt art, ChildArt.ReputationType reputation)
    {
        // Find appropriate evolution stage
        ArtEvolution evolution = art.evolutionStages.Find(e => e.targetReputation == reputation);
        
        if (evolution != null && art.artObject != null)
        {
            Renderer renderer = art.artObject.GetComponent<Renderer>();
            if (renderer != null && evolution.evolutionTexture != null)
            {
                renderer.material.mainTexture = evolution.evolutionTexture;
            }
            
            art.artDescription = evolution.evolutionDescription;
            
            // Play child reaction sound
            if (evolution.childReaction != null)
            {
                AudioSource.PlayClipAtPoint(evolution.childReaction, art.artObject.transform.position);
            }
        }
    }
    
    void UpdateBookAvailability()
    {
        // Some books may become available or change based on player progress
        foreach (WhisperingBook book in ancientBooks)
        {
            if (!book.hasBeenRead && ShouldRevealBook(book))
            {
                RevealBook(book);
            }
        }
    }
    
    bool ShouldRevealBook(WhisperingBook book)
    {
        // Books reveal based on philosophical growth or specific achievements
        if (moralitySystem != null)
        {
            PhilosophicalMoralitySystem.MoralPath path = moralitySystem.GetCurrentPath();
            
            switch (book.type)
            {
                case WhisperingBook.BookType.PropheticVision:
                    return path != PhilosophicalMoralitySystem.MoralPath.Neutral;
                case WhisperingBook.BookType.PhilosophicalTreatise:
                    return moralitySystem.GetPhilosophicalGrowth() > 50f;
                default:
                    return true;
            }
        }
        return true;
    }
    
    void RevealBook(WhisperingBook book)
    {
        if (book.bookObject != null)
        {
            book.bookObject.SetActive(true);
            ShowArtMessage($"An ancient book, '{book.bookTitle}', has appeared...");
        }
    }
    
    void CheckForBookInteractions()
    {
        foreach (WhisperingBook book in ancientBooks)
        {
            if (book.bookObject != null && !book.hasBeenRead)
            {
                float distance = Vector3.Distance(transform.position, book.bookObject.transform.position);
                if (distance < 3f && Input.GetKeyDown(KeyCode.E))
                {
                    ReadAncientBook(book);
                }
            }
        }
    }
    
    void ReadAncientBook(WhisperingBook book)
    {
        StartCoroutine(BookReadingSequence(book));
    }
    
    IEnumerator BookReadingSequence(WhisperingBook book)
    {
        book.hasBeenRead = true;
        book.lastReadTime = Time.time;
        
        ShowArtMessage($"You approach the ancient book: '{book.bookTitle}'");
        yield return new WaitForSeconds(2f);
        
        ShowArtMessage("The book has no visible text, but as you touch it, whispers fill your mind...");
        yield return new WaitForSeconds(2f);
        
        // Play whisper sounds
        if (whisperSounds.Length > 0)
        {
            AudioSource.PlayClipAtPoint(whisperSounds[Random.Range(0, whisperSounds.Length)], transform.position);
        }
        
        // Show whispered text
        foreach (string whisper in book.whisperTexts)
        {
            ShowArtMessage($"Whisper: \"{whisper}\"");
            yield return new WaitForSeconds(3f);
        }
        
        // Trigger visions
        if (book.visionSequences.Length > 0)
        {
            VisionSequence vision = book.visionSequences[Random.Range(0, book.visionSequences.Length)];
            yield return StartCoroutine(PlayVisionSequence(vision));
        }
        
        // Grant wisdom
        GrantBookWisdom(book);
    }
    
    IEnumerator PlayVisionSequence(VisionSequence vision)
    {
        ShowArtMessage($"VISION: {vision.visionName}");
        yield return new WaitForSeconds(1f);
        
        // Create vision effect
        if (vision.visionEffect != null)
        {
            GameObject effect = Instantiate(vision.visionEffect, transform.position, Quaternion.identity);
            Destroy(effect, vision.visionDuration);
        }
        
        // Play vision sound
        if (vision.visionSound != null)
        {
            AudioSource.PlayClipAtPoint(vision.visionSound, transform.position);
        }
        
        ShowArtMessage(vision.visionDescription);
        yield return new WaitForSeconds(2f);
        
        // Show vision dialogue
        foreach (string dialogue in vision.visionDialogue)
        {
            ShowArtMessage($"Vision: {dialogue}");
            yield return new WaitForSeconds(3f);
        }
        
        ShowArtMessage("The vision fades, but its meaning lingers...");
    }
    
    void GrantBookWisdom(WhisperingBook book)
    {
        // Grant wisdom points
        PsychologicalSystem psycheSystem = GetComponent<PsychologicalSystem>();
        if (psycheSystem != null)
        {
            psycheSystem.enlightenment += book.wisdomValue;
        }
        
        // Unlock secrets
        foreach (string secret in book.revealedSecrets)
        {
            gameManager?.RevealSecret(secret);
        }
        
        // Unlock dialogue options
        foreach (string dialogue in book.unlockedDialogues)
        {
            gameManager?.UnlockDialogueOption(dialogue);
        }
        
        ShowArtMessage($"Ancient wisdom flows through you. Enlightenment increased by {book.wisdomValue}");
    }
    
    void UpdateMuralEvolution()
    {
        // Check for time-based mural evolution
        if (timeSystem != null)
        {
            float currentTime = timeSystem.GetCurrentGameTime();
            
            foreach (DynamicMural mural in worldMurals)
            {
                if (currentTime - mural.lastUpdateTime > 365f) // One year
                {
                    EvolveHistoricalMural(mural);
                }
            }
        }
    }
    
    void EvolveHistoricalMural(DynamicMural mural)
    {
        mural.type = DynamicMural.MuralType.HistoricalEvent;
        mural.currentLegend = "The chronicles of The Cinderborn, as witnessed by our ancestors";
        mural.lastUpdateTime = timeSystem.GetCurrentGameTime();
        
        ShowArtMessage($"The mural in {mural.muralName} has evolved into a historical record");
    }
    
    float GetCurrentReputation()
    {
        // Calculate overall reputation from various systems
        float reputation = 0f;
        
        if (titleSystem != null)
        {
            string title = titleSystem.GetCurrentTitleName();
            if (title.Contains("Redeemed") || title.Contains("Ashspeaker"))
                reputation += 50f;
            else if (title.Contains("Butcher") || title.Contains("Reaper"))
                reputation -= 50f;
        }
        
        if (deathSystem != null)
        {
            reputation -= deathSystem.GetInnocentsKilled() * 10f;
            reputation += deathSystem.GetVillainsKilled() * 2f;
        }
        
        return Mathf.Clamp(reputation, -100f, 100f);
    }
    
    void ShowArtMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Artistic Feedback: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public List<DynamicMural> GetWorldMurals() => worldMurals;
    public List<ChildArt> GetChildrenDrawings() => childrenDrawings;
    public List<WhisperingBook> GetAncientBooks() => ancientBooks;
}
