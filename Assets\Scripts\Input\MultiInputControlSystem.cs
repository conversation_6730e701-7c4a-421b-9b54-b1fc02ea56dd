using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using System.Collections;

public class MultiInputControlSystem : MonoBehaviour
{
    [Header("Input Detection")]
    public InputDevice currentInputDevice;
    public InputDeviceType activeDeviceType = InputDeviceType.KeyboardMouse;
    public float deviceSwitchDelay = 0.5f;
    private float lastInputTime = 0f;

    [Header("Controller Support")]
    public bool playStationControllerConnected = false;
    public bool xboxControllerConnected = false;
    public bool keyboardMouseActive = true;

    [Header("Vibration Settings")]
    public bool vibrationEnabled = true;
    public float vibrationIntensity = 1f;
    public VibrationProfile[] vibrationProfiles;

    [Header("UI Button Icons")]
    public ButtonIconSet keyboardIcons;
    public ButtonIconSet playStationIcons;
    public ButtonIconSet xboxIcons;
    public UIButtonPromptSystem buttonPromptSystem;

    [Header("Input Actions")]
    public PlayerInput playerInput;
    public InputActionAsset inputActions;

    [Header("Cinder Input System")]
    public CinderInput cinderInput;

    private Gamepad currentGamepad;
    private PsychologicalSystem psycheSystem;
    private GameUI gameUI;

    public enum InputDeviceType
    {
        KeyboardMouse,
        PlayStation,
        Xbox,
        Generic
    }

    [System.Serializable]
    public class VibrationProfile
    {
        public string profileName;
        public VibrationTrigger trigger;
        public float lowFrequency;
        public float highFrequency;
        public float duration;
        public AnimationCurve intensityCurve;

        public enum VibrationTrigger
        {
            WeaponHit,
            TakeDamage,
            BossHit,
            InnocentKilled,
            EmotionalMoment,
            HeartbeatSync,
            EnvironmentalImpact,
            MagicCast
        }
    }

    [System.Serializable]
    public class ButtonIconSet
    {
        [Header("Action Icons")]
        public Sprite interactIcon;
        public Sprite attackIcon;
        public Sprite blockIcon;
        public Sprite dodgeIcon;
        public Sprite jumpIcon;
        public Sprite menuIcon;
        public Sprite inventoryIcon;
        public Sprite mapIcon;

        [Header("Movement Icons")]
        public Sprite moveIcon;
        public Sprite runIcon;
        public Sprite crouchIcon;
        public Sprite cameraIcon;

        [Header("UI Navigation")]
        public Sprite confirmIcon;
        public Sprite cancelIcon;
        public Sprite selectIcon;
        public Sprite backIcon;
    }

    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        gameUI = FindObjectOfType<GameUI>();

        InitializeInputSystem();
        SetupInputActions();
        DetectConnectedDevices();
    }

    void Update()
    {
        DetectActiveInputDevice();
        UpdateControllerConnections();
        HandleDeviceSwitching();
    }

    void InitializeInputSystem()
    {
        // Initialize Cinder Input System
        if (cinderInput == null)
        {
            cinderInput = new CinderInput();
        }

        // Load input actions if not assigned
        if (inputActions == null && cinderInput != null)
        {
            inputActions = cinderInput.Asset;
        }

        // Initialize legacy player input
        if (playerInput == null)
        {
            playerInput = GetComponent<PlayerInput>();
        }

        // Setup vibration profiles
        InitializeVibrationProfiles();

        // Initialize button prompt system
        if (buttonPromptSystem == null)
        {
            buttonPromptSystem = FindObjectOfType<UIButtonPromptSystem>();
        }

        // Enable gameplay input by default
        if (cinderInput != null)
        {
            cinderInput.EnableGameplay();
        }

        Debug.Log("Multi-Input Control System initialized with CinderInput");
    }

    void InitializeVibrationProfiles()
    {
        vibrationProfiles = new VibrationProfile[]
        {
            new VibrationProfile
            {
                profileName = "Weapon Hit",
                trigger = VibrationProfile.VibrationTrigger.WeaponHit,
                lowFrequency = 0.3f,
                highFrequency = 0.7f,
                duration = 0.2f
            },
            new VibrationProfile
            {
                profileName = "Take Damage",
                trigger = VibrationProfile.VibrationTrigger.TakeDamage,
                lowFrequency = 0.8f,
                highFrequency = 0.4f,
                duration = 0.3f
            },
            new VibrationProfile
            {
                profileName = "Boss Hit",
                trigger = VibrationProfile.VibrationTrigger.BossHit,
                lowFrequency = 1f,
                highFrequency = 1f,
                duration = 0.5f
            },
            new VibrationProfile
            {
                profileName = "Innocent Killed",
                trigger = VibrationProfile.VibrationTrigger.InnocentKilled,
                lowFrequency = 0.6f,
                highFrequency = 0.2f,
                duration = 2f
            },
            new VibrationProfile
            {
                profileName = "Emotional Moment",
                trigger = VibrationProfile.VibrationTrigger.EmotionalMoment,
                lowFrequency = 0.4f,
                highFrequency = 0.1f,
                duration = 1.5f
            },
            new VibrationProfile
            {
                profileName = "Heartbeat Sync",
                trigger = VibrationProfile.VibrationTrigger.HeartbeatSync,
                lowFrequency = 0.5f,
                highFrequency = 0f,
                duration = 0.8f
            }
        };
    }

    void SetupInputActions()
    {
        if (inputActions == null) return;

        // Enable all action maps
        inputActions.Enable();

        // Subscribe to input events
        var gameplayMap = inputActions.FindActionMap("Gameplay");
        var uiMap = inputActions.FindActionMap("UI");

        if (gameplayMap != null)
        {
            gameplayMap.FindAction("Move")?.performed += OnMove;
            gameplayMap.FindAction("Look")?.performed += OnLook;
            gameplayMap.FindAction("Attack")?.performed += OnAttack;
            gameplayMap.FindAction("Block")?.performed += OnBlock;
            gameplayMap.FindAction("Dodge")?.performed += OnDodge;
            gameplayMap.FindAction("Interact")?.performed += OnInteract;
            gameplayMap.FindAction("Jump")?.performed += OnJump;
            gameplayMap.FindAction("Run")?.performed += OnRun;
            gameplayMap.FindAction("Crouch")?.performed += OnCrouch;
            gameplayMap.FindAction("Menu")?.performed += OnMenu;
            gameplayMap.FindAction("Inventory")?.performed += OnInventory;
            gameplayMap.FindAction("Map")?.performed += OnMap;
        }

        if (uiMap != null)
        {
            uiMap.FindAction("Navigate")?.performed += OnUINavigate;
            uiMap.FindAction("Submit")?.performed += OnUISubmit;
            uiMap.FindAction("Cancel")?.performed += OnUICancel;
            uiMap.FindAction("Point")?.performed += OnUIPoint;
            uiMap.FindAction("Click")?.performed += OnUIClick;
        }
    }

    void DetectConnectedDevices()
    {
        // Check for connected gamepads
        var gamepads = Gamepad.all;

        playStationControllerConnected = false;
        xboxControllerConnected = false;

        foreach (var gamepad in gamepads)
        {
            if (gamepad.name.ToLower().Contains("dualshock") ||
                gamepad.name.ToLower().Contains("dualsense") ||
                gamepad.name.ToLower().Contains("playstation"))
            {
                playStationControllerConnected = true;
                currentGamepad = gamepad;
            }
            else if (gamepad.name.ToLower().Contains("xbox") ||
                     gamepad.name.ToLower().Contains("xinput"))
            {
                xboxControllerConnected = true;
                currentGamepad = gamepad;
            }
        }

        // Set initial device type
        if (playStationControllerConnected)
        {
            SetActiveDevice(InputDeviceType.PlayStation);
        }
        else if (xboxControllerConnected)
        {
            SetActiveDevice(InputDeviceType.Xbox);
        }
        else
        {
            SetActiveDevice(InputDeviceType.KeyboardMouse);
        }

        Debug.Log($"Detected devices - PS: {playStationControllerConnected}, Xbox: {xboxControllerConnected}");
    }

    void DetectActiveInputDevice()
    {
        if (cinderInput == null) return;

        // Use CinderInput for device detection
        bool usingGamepad = cinderInput.IsUsingGamepad();
        bool usingKeyboardMouse = cinderInput.IsUsingKeyboardMouse();

        // Check for recent input from different devices
        var keyboard = Keyboard.current;
        var mouse = Mouse.current;
        var gamepad = Gamepad.current;

        // Keyboard/Mouse detection
        if (keyboard != null && keyboard.anyKey.wasPressedThisFrame ||
            mouse != null && (mouse.leftButton.wasPressedThisFrame ||
                             mouse.rightButton.wasPressedThisFrame ||
                             mouse.delta.ReadValue().magnitude > 0.1f))
        {
            if (activeDeviceType != InputDeviceType.KeyboardMouse)
            {
                SetActiveDevice(InputDeviceType.KeyboardMouse);
            }
        }

        // Gamepad detection
        if (gamepad != null && gamepad.wasUpdatedThisFrame)
        {
            InputDeviceType detectedType = DetectGamepadType(gamepad);
            if (activeDeviceType != detectedType)
            {
                SetActiveDevice(detectedType);
            }
        }
    }

    InputDeviceType DetectGamepadType(Gamepad gamepad)
    {
        string deviceName = gamepad.name.ToLower();

        if (deviceName.Contains("dualshock") ||
            deviceName.Contains("dualsense") ||
            deviceName.Contains("playstation"))
        {
            return InputDeviceType.PlayStation;
        }
        else if (deviceName.Contains("xbox") ||
                 deviceName.Contains("xinput"))
        {
            return InputDeviceType.Xbox;
        }

        return InputDeviceType.Generic;
    }

    void UpdateControllerConnections()
    {
        // Update connection status
        var gamepads = Gamepad.all;
        bool psConnected = false;
        bool xboxConnected = false;

        foreach (var gamepad in gamepads)
        {
            string deviceName = gamepad.name.ToLower();
            if (deviceName.Contains("dualshock") || deviceName.Contains("dualsense"))
            {
                psConnected = true;
            }
            else if (deviceName.Contains("xbox"))
            {
                xboxConnected = true;
            }
        }

        if (psConnected != playStationControllerConnected)
        {
            playStationControllerConnected = psConnected;
            OnControllerConnectionChanged();
        }

        if (xboxConnected != xboxControllerConnected)
        {
            xboxControllerConnected = xboxConnected;
            OnControllerConnectionChanged();
        }
    }

    void OnControllerConnectionChanged()
    {
        // Update UI prompts when controller connection changes
        UpdateUIButtonPrompts();

        // Show connection message
        string message = "";
        if (playStationControllerConnected)
            message = "PlayStation controller connected";
        else if (xboxControllerConnected)
            message = "Xbox controller connected";
        else
            message = "Controller disconnected";

        ShowInputMessage(message);
    }

    void HandleDeviceSwitching()
    {
        // Handle seamless switching between input devices
        if (Time.time - lastInputTime > deviceSwitchDelay)
        {
            // Device switching logic here
        }
    }

    void SetActiveDevice(InputDeviceType deviceType)
    {
        if (activeDeviceType == deviceType) return;

        activeDeviceType = deviceType;
        lastInputTime = Time.time;

        // Update UI button prompts
        UpdateUIButtonPrompts();

        // Update cursor visibility
        UpdateCursorVisibility();

        Debug.Log($"Active input device changed to: {deviceType}");
    }

    void UpdateUIButtonPrompts()
    {
        if (buttonPromptSystem == null) return;

        ButtonIconSet iconSet = GetCurrentIconSet();
        buttonPromptSystem.UpdateButtonIcons(iconSet, activeDeviceType);
    }

    ButtonIconSet GetCurrentIconSet()
    {
        switch (activeDeviceType)
        {
            case InputDeviceType.PlayStation:
                return playStationIcons;
            case InputDeviceType.Xbox:
                return xboxIcons;
            default:
                return keyboardIcons;
        }
    }

    void UpdateCursorVisibility()
    {
        switch (activeDeviceType)
        {
            case InputDeviceType.KeyboardMouse:
                Cursor.visible = true;
                Cursor.lockState = CursorLockMode.None;
                break;
            case InputDeviceType.PlayStation:
            case InputDeviceType.Xbox:
                Cursor.visible = false;
                Cursor.lockState = CursorLockMode.Locked;
                break;
        }
    }

    // Input Action Handlers
    void OnMove(InputAction.CallbackContext context)
    {
        Vector2 moveInput = context.ReadValue<Vector2>();
        // Handle movement
    }

    void OnLook(InputAction.CallbackContext context)
    {
        Vector2 lookInput = context.ReadValue<Vector2>();
        // Handle camera look
    }

    void OnAttack(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle attack
            TriggerVibration(VibrationProfile.VibrationTrigger.WeaponHit);
        }
    }

    void OnBlock(InputAction.CallbackContext context)
    {
        // Handle block
    }

    void OnDodge(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle dodge
        }
    }

    void OnInteract(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle interaction
        }
    }

    void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle jump
        }
    }

    void OnRun(InputAction.CallbackContext context)
    {
        // Handle run toggle
    }

    void OnCrouch(InputAction.CallbackContext context)
    {
        // Handle crouch
    }

    void OnMenu(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Toggle main menu
        }
    }

    void OnInventory(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Toggle inventory
        }
    }

    void OnMap(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Toggle map
        }
    }

    // UI Input Handlers
    void OnUINavigate(InputAction.CallbackContext context)
    {
        Vector2 navigation = context.ReadValue<Vector2>();
        // Handle UI navigation
    }

    void OnUISubmit(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle UI submit/confirm
        }
    }

    void OnUICancel(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle UI cancel/back
        }
    }

    void OnUIPoint(InputAction.CallbackContext context)
    {
        Vector2 pointerPosition = context.ReadValue<Vector2>();
        // Handle mouse pointer movement
    }

    void OnUIClick(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // Handle mouse clicks
        }
    }

    // Vibration System
    public void TriggerVibration(VibrationProfile.VibrationTrigger trigger)
    {
        if (!vibrationEnabled) return;

        VibrationProfile profile = GetVibrationProfile(trigger);
        if (profile != null)
        {
            // Use CinderInput vibration system if available
            if (cinderInput != null)
            {
                float adjustedLow = profile.lowFrequency * vibrationIntensity;
                float adjustedHigh = profile.highFrequency * vibrationIntensity;
                cinderInput.SetGamepadVibration(adjustedLow, adjustedHigh, profile.duration);
            }
            else if (currentGamepad != null)
            {
                // Fallback to legacy vibration
                StartCoroutine(PlayVibration(profile));
            }
        }
    }

    VibrationProfile GetVibrationProfile(VibrationProfile.VibrationTrigger trigger)
    {
        foreach (var profile in vibrationProfiles)
        {
            if (profile.trigger == trigger)
                return profile;
        }
        return null;
    }

    IEnumerator PlayVibration(VibrationProfile profile)
    {
        float elapsed = 0f;

        while (elapsed < profile.duration)
        {
            float intensity = profile.intensityCurve != null ?
                profile.intensityCurve.Evaluate(elapsed / profile.duration) : 1f;

            float lowFreq = profile.lowFrequency * intensity * vibrationIntensity;
            float highFreq = profile.highFrequency * intensity * vibrationIntensity;

            currentGamepad.SetMotorSpeeds(lowFreq, highFreq);

            elapsed += Time.deltaTime;
            yield return null;
        }

        // Stop vibration
        currentGamepad.SetMotorSpeeds(0f, 0f);
    }

    // Special vibration for emotional moments
    public void TriggerEmotionalVibration()
    {
        TriggerVibration(VibrationProfile.VibrationTrigger.EmotionalMoment);
    }

    public void TriggerHeartbeatVibration()
    {
        TriggerVibration(VibrationProfile.VibrationTrigger.HeartbeatSync);
    }

    public void OnInnocentKilled()
    {
        TriggerVibration(VibrationProfile.VibrationTrigger.InnocentKilled);
    }

    public void OnBossHit()
    {
        TriggerVibration(VibrationProfile.VibrationTrigger.BossHit);
    }

    // Settings
    public void SetVibrationEnabled(bool enabled)
    {
        vibrationEnabled = enabled;
        if (!enabled && currentGamepad != null)
        {
            currentGamepad.SetMotorSpeeds(0f, 0f);
        }
    }

    public void SetVibrationIntensity(float intensity)
    {
        vibrationIntensity = Mathf.Clamp01(intensity);
    }

    // Input remapping
    public void RemapAction(string actionName, InputBinding newBinding)
    {
        var action = inputActions.FindAction(actionName);
        if (action != null)
        {
            // Implement input remapping logic
            action.ApplyBindingOverride(newBinding);
        }
    }

    public void ResetToDefaultBindings()
    {
        inputActions.RemoveAllBindingOverrides();
    }

    void ShowInputMessage(string message)
    {
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 3f));
        }

        Debug.Log($"Input System: {message}");
    }

    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    void OnDestroy()
    {
        // Clean up input actions
        if (inputActions != null)
        {
            inputActions.Disable();
        }

        // Stop any ongoing vibration
        if (currentGamepad != null)
        {
            currentGamepad.SetMotorSpeeds(0f, 0f);
        }
    }

    // Getters
    public InputDeviceType GetActiveDeviceType() => activeDeviceType;
    public bool IsControllerConnected() => playStationControllerConnected || xboxControllerConnected;
    public bool IsPlayStationController() => activeDeviceType == InputDeviceType.PlayStation;
    public bool IsXboxController() => activeDeviceType == InputDeviceType.Xbox;
    public bool IsKeyboardMouse() => activeDeviceType == InputDeviceType.KeyboardMouse;
}
