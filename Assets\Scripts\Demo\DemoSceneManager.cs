using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using CinderOfDarkness.Narrative;
using CinderOfDarkness.AI;
using CinderOfDarkness.Magic;
using CinderOfDarkness.Economy;
using CinderOfDarkness.Dialogue;
using CinderOfDarkness.Stealth;
using CinderOfDarkness.Map;
using CinderOfDarkness.Time;
using CinderOfDarkness.Modding;
using CinderOfDarkness.UI;

namespace CinderOfDarkness.Demo
{
    /// <summary>
    /// Demo Scene Manager for Cinder of Darkness.
    /// Showcases all 10 core systems working together in a cohesive demonstration.
    /// </summary>
    public class DemoSceneManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Demo UI")]
        [SerializeField] private GameObject demoPanel;
        [SerializeField] private TextMeshProUGUI demoTitleText;
        [SerializeField] private TextMeshProUGUI demoDescriptionText;
        [SerializeField] private Button nextDemoButton;
        [SerializeField] private Button previousDemoButton;
        [SerializeField] private But<PERSON> skipDemoButton;
        [SerializeField] private Slider demoProgressSlider;

        [Header("System Indicators")]
        [SerializeField] private Transform systemIndicatorsContainer;
        [SerializeField] private GameObject systemIndicatorPrefab;

        [Header("Demo Objects")]
        [SerializeField] private GameObject[] demoObjects;
        [SerializeField] private Transform playerSpawnPoint;
        [SerializeField] private Transform[] demoWaypoints;

        [Header("Audio")]
        [SerializeField] private AudioClip demoStartSound;
        [SerializeField] private AudioClip demoCompleteSound;
        [SerializeField] private AudioClip systemActivateSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private int currentDemoIndex = 0;
        private List<DemoStep> demoSteps = new List<DemoStep>();
        private List<GameObject> systemIndicators = new List<GameObject>();
        private bool isDemoActive = false;
        private PlayerController playerController;
        
        // System references
        private DynamicNarrativeSystem narrativeSystem;
        private ReactiveAISystem aiSystem;
        private MagicEvolutionSystem magicSystem;
        private EconomySystem economySystem;
        private AdvancedDialogueSystem dialogueSystem;
        private StealthSystem stealthSystem;
        private WorldMapSystem mapSystem;
        private DynamicTimeSystem timeSystem;
        private FlashbackSystem flashbackSystem;
        private ModdingSystem moddingSystem;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            InitializeDemo();
            SetupDemoSteps();
            SetupUI();
            StartDemo();
        }

        private void Update()
        {
            HandleInput();
            UpdateDemoProgress();
        }
        #endregion

        #region Initialization
        private void InitializeDemo()
        {
            // Get system references
            narrativeSystem = DynamicNarrativeSystem.Instance;
            aiSystem = ReactiveAISystem.Instance;
            magicSystem = MagicEvolutionSystem.Instance;
            economySystem = EconomySystem.Instance;
            dialogueSystem = AdvancedDialogueSystem.Instance;
            stealthSystem = StealthSystem.Instance;
            mapSystem = WorldMapSystem.Instance;
            timeSystem = DynamicTimeSystem.Instance;
            flashbackSystem = FlashbackSystem.Instance;
            moddingSystem = ModdingSystem.Instance;

            // Get player reference
            playerController = FindObjectOfType<PlayerController>();
            if (playerController == null)
            {
                CreateDemoPlayer();
            }

            // Setup audio
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void CreateDemoPlayer()
        {
            // Create a basic player for demo purposes
            GameObject playerObj = new GameObject("DemoPlayer");
            playerController = playerObj.AddComponent<PlayerController>();
            
            // Add basic components
            var rigidbody = playerObj.AddComponent<Rigidbody>();
            var collider = playerObj.AddComponent<CapsuleCollider>();
            var inventory = playerObj.AddComponent<PlayerInventory>();
            
            // Position player
            if (playerSpawnPoint != null)
            {
                playerObj.transform.position = playerSpawnPoint.position;
                playerObj.transform.rotation = playerSpawnPoint.rotation;
            }
        }

        private void SetupDemoSteps()
        {
            demoSteps.Clear();

            // Demo Step 1: Dynamic Narrative System
            demoSteps.Add(new DemoStep
            {
                title = "Dynamic Narrative System",
                description = "Experience branching storylines with meaningful choices that affect the world around you.",
                systemName = "Narrative",
                demoAction = DemoNarrativeSystem
            });

            // Demo Step 2: Reactive AI System
            demoSteps.Add(new DemoStep
            {
                title = "Reactive AI System",
                description = "Watch as enemies adapt to your playstyle and NPCs react to your reputation.",
                systemName = "AI",
                demoAction = DemoAISystem
            });

            // Demo Step 3: Magic Evolution System
            demoSteps.Add(new DemoStep
            {
                title = "Magic Evolution System",
                description = "Cast spells that evolve and unlock new traits as you use them.",
                systemName = "Magic",
                demoAction = DemoMagicSystem
            });

            // Demo Step 4: Economy System
            demoSteps.Add(new DemoStep
            {
                title = "Economy & Trading System",
                description = "Trade with merchants, manage multiple currencies, and influence regional economies.",
                systemName = "Economy",
                demoAction = DemoEconomySystem
            });

            // Demo Step 5: Advanced Dialogue System
            demoSteps.Add(new DemoStep
            {
                title = "Advanced Dialogue System",
                description = "Engage in conversations where your character traits unlock unique dialogue options.",
                systemName = "Dialogue",
                demoAction = DemoDialogueSystem
            });

            // Demo Step 6: Stealth System
            demoSteps.Add(new DemoStep
            {
                title = "Stealth & Assassination System",
                description = "Master the shadows with advanced stealth mechanics and assassination techniques.",
                systemName = "Stealth",
                demoAction = DemoStealthSystem
            });

            // Demo Step 7: World Map System
            demoSteps.Add(new DemoStep
            {
                title = "Interactive World Map",
                description = "Explore a living world with fog of war, custom markers, and real-time tracking.",
                systemName = "Map",
                demoAction = DemoMapSystem
            });

            // Demo Step 8: Dynamic Time System
            demoSteps.Add(new DemoStep
            {
                title = "Dynamic Time System",
                description = "Experience day-night cycles with time-based events and dynamic lighting.",
                systemName = "Time",
                demoAction = DemoTimeSystem
            });

            // Demo Step 9: Flashback System
            demoSteps.Add(new DemoStep
            {
                title = "Flashback & Memory System",
                description = "Relive memories through immersive flashback sequences triggered by the environment.",
                systemName = "Flashback",
                demoAction = DemoFlashbackSystem
            });

            // Demo Step 10: Modding System
            demoSteps.Add(new DemoStep
            {
                title = "Comprehensive Modding Support",
                description = "Discover the powerful modding framework that allows unlimited community creativity.",
                systemName = "Modding",
                demoAction = DemoModdingSystem
            });
        }

        private void SetupUI()
        {
            if (nextDemoButton != null)
                nextDemoButton.onClick.AddListener(NextDemo);

            if (previousDemoButton != null)
                previousDemoButton.onClick.AddListener(PreviousDemo);

            if (skipDemoButton != null)
                skipDemoButton.onClick.AddListener(SkipDemo);

            CreateSystemIndicators();
            UpdateDemoUI();
        }

        private void CreateSystemIndicators()
        {
            if (systemIndicatorPrefab == null || systemIndicatorsContainer == null) return;

            foreach (var step in demoSteps)
            {
                GameObject indicator = Instantiate(systemIndicatorPrefab, systemIndicatorsContainer);
                var indicatorUI = indicator.GetComponent<SystemIndicatorUI>();
                
                if (indicatorUI != null)
                {
                    indicatorUI.Setup(step.systemName, false);
                }
                
                systemIndicators.Add(indicator);
            }
        }
        #endregion

        #region Demo Control
        private void StartDemo()
        {
            isDemoActive = true;
            currentDemoIndex = 0;
            
            if (demoPanel != null)
                demoPanel.SetActive(true);

            PlaySound(demoStartSound);
            ExecuteCurrentDemo();
        }

        private void NextDemo()
        {
            if (currentDemoIndex < demoSteps.Count - 1)
            {
                currentDemoIndex++;
                ExecuteCurrentDemo();
            }
            else
            {
                CompleteDemo();
            }
        }

        private void PreviousDemo()
        {
            if (currentDemoIndex > 0)
            {
                currentDemoIndex--;
                ExecuteCurrentDemo();
            }
        }

        private void SkipDemo()
        {
            CompleteDemo();
        }

        private void CompleteDemo()
        {
            isDemoActive = false;
            
            if (demoPanel != null)
                demoPanel.SetActive(false);

            PlaySound(demoCompleteSound);
            
            // Show completion message
            Debug.Log("Demo completed! All 10 systems demonstrated successfully.");
            
            // Enable all UI systems for free exploration
            EnableAllUISystems();
        }

        private void ExecuteCurrentDemo()
        {
            if (currentDemoIndex >= 0 && currentDemoIndex < demoSteps.Count)
            {
                var currentStep = demoSteps[currentDemoIndex];
                
                UpdateDemoUI();
                UpdateSystemIndicators();
                PlaySound(systemActivateSound);
                
                // Execute the demo action
                StartCoroutine(currentStep.demoAction());
            }
        }

        private void UpdateDemoUI()
        {
            if (currentDemoIndex >= 0 && currentDemoIndex < demoSteps.Count)
            {
                var currentStep = demoSteps[currentDemoIndex];
                
                if (demoTitleText != null)
                    demoTitleText.text = currentStep.title;

                if (demoDescriptionText != null)
                    demoDescriptionText.text = currentStep.description;
            }

            if (demoProgressSlider != null)
            {
                demoProgressSlider.value = (float)currentDemoIndex / (demoSteps.Count - 1);
            }

            // Update button states
            if (previousDemoButton != null)
                previousDemoButton.interactable = currentDemoIndex > 0;

            if (nextDemoButton != null)
                nextDemoButton.interactable = currentDemoIndex < demoSteps.Count - 1;
        }

        private void UpdateSystemIndicators()
        {
            for (int i = 0; i < systemIndicators.Count; i++)
            {
                var indicator = systemIndicators[i].GetComponent<SystemIndicatorUI>();
                if (indicator != null)
                {
                    indicator.SetActive(i <= currentDemoIndex);
                }
            }
        }

        private void HandleInput()
        {
            if (!isDemoActive) return;

            if (Input.GetKeyDown(KeyCode.RightArrow) || Input.GetKeyDown(KeyCode.Space))
            {
                NextDemo();
            }
            else if (Input.GetKeyDown(KeyCode.LeftArrow))
            {
                PreviousDemo();
            }
            else if (Input.GetKeyDown(KeyCode.Escape))
            {
                SkipDemo();
            }
        }

        private void UpdateDemoProgress()
        {
            // Update any ongoing demo animations or effects
        }
        #endregion

        #region Demo Actions
        private IEnumerator DemoNarrativeSystem()
        {
            Debug.Log("Demonstrating Dynamic Narrative System...");
            
            if (narrativeSystem != null)
            {
                // Make a sample choice
                narrativeSystem.MakeChoice("demo_choice_1", "Help the villager", ChoiceType.Major);
                yield return new WaitForSeconds(1f);
                
                // Show reputation change
                narrativeSystem.ModifyFactionReputation("Villagers", 25f);
                yield return new WaitForSeconds(1f);
                
                // Trigger story flag
                narrativeSystem.SetStoryFlag("demo_helped_villager", true);
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoAISystem()
        {
            Debug.Log("Demonstrating Reactive AI System...");
            
            if (aiSystem != null)
            {
                // Record player actions
                aiSystem.RecordPlayerAction(ActionType.MeleeAttack, Vector3.zero);
                yield return new WaitForSeconds(0.5f);
                
                aiSystem.RecordPlayerAction(ActionType.MagicCast, Vector3.zero);
                yield return new WaitForSeconds(0.5f);
                
                // Show adaptation
                aiSystem.UpdateDifficulty();
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoMagicSystem()
        {
            Debug.Log("Demonstrating Magic Evolution System...");
            
            if (magicSystem != null)
            {
                // Learn a spell
                magicSystem.LearnSpell("fireball");
                yield return new WaitForSeconds(1f);
                
                // Cast spell multiple times to show evolution
                for (int i = 0; i < 5; i++)
                {
                    magicSystem.CastSpell("fireball", Vector3.zero, Vector3.forward);
                    yield return new WaitForSeconds(0.3f);
                }
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoEconomySystem()
        {
            Debug.Log("Demonstrating Economy System...");
            
            if (economySystem != null)
            {
                // Add currencies
                economySystem.AddCurrency("gold", 100);
                economySystem.AddCurrency("silver", 500);
                yield return new WaitForSeconds(1f);
                
                // Simulate transaction
                economySystem.RemoveCurrency("gold", 25);
                yield return new WaitForSeconds(1f);
                
                // Update regional prices
                economySystem.UpdateRegionalPrices("demo_region");
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoDialogueSystem()
        {
            Debug.Log("Demonstrating Advanced Dialogue System...");
            
            if (dialogueSystem != null)
            {
                // Set character traits
                dialogueSystem.SetCharacterTrait("player", "charisma", 75f);
                yield return new WaitForSeconds(1f);
                
                // Start a sample conversation
                dialogueSystem.StartConversation("demo_conversation");
                yield return new WaitForSeconds(2f);
                
                // End conversation
                dialogueSystem.EndConversation();
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoStealthSystem()
        {
            Debug.Log("Demonstrating Stealth System...");
            
            if (stealthSystem != null)
            {
                // Enter stealth
                stealthSystem.EnterStealth();
                yield return new WaitForSeconds(1f);
                
                // Use stealth tools
                stealthSystem.UseSmokeBomb();
                yield return new WaitForSeconds(1f);
                
                stealthSystem.UseLockpick();
                yield return new WaitForSeconds(1f);
                
                // Exit stealth
                stealthSystem.ExitStealth();
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoMapSystem()
        {
            Debug.Log("Demonstrating World Map System...");
            
            if (mapSystem != null)
            {
                // Add markers
                mapSystem.AddMarker("demo_marker_1", Vector3.zero, MarkerType.Quest);
                yield return new WaitForSeconds(0.5f);
                
                mapSystem.AddMarker("demo_marker_2", Vector3.right * 10, MarkerType.Shop);
                yield return new WaitForSeconds(0.5f);
                
                // Reveal areas
                mapSystem.RevealArea(Vector2Int.zero);
                yield return new WaitForSeconds(1f);
                
                mapSystem.RevealArea(Vector2Int.one);
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoTimeSystem()
        {
            Debug.Log("Demonstrating Dynamic Time System...");
            
            if (timeSystem != null)
            {
                // Accelerate time
                timeSystem.SetTimeScale(10f);
                yield return new WaitForSeconds(2f);
                
                // Reset time scale
                timeSystem.SetTimeScale(1f);
                yield return new WaitForSeconds(1f);
                
                // Schedule an event
                timeSystem.ScheduleEvent("demo_event", 5f);
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoFlashbackSystem()
        {
            Debug.Log("Demonstrating Flashback System...");
            
            if (flashbackSystem != null)
            {
                // Add memory trigger
                flashbackSystem.AddMemoryTrigger("demo_memory", Vector3.zero, 5f);
                yield return new WaitForSeconds(1f);
                
                // Trigger flashback
                flashbackSystem.TriggerFlashback("demo_flashback");
                yield return new WaitForSeconds(2f);
                
                // End flashback
                flashbackSystem.EndFlashback();
            }
            
            yield return new WaitForSeconds(2f);
        }

        private IEnumerator DemoModdingSystem()
        {
            Debug.Log("Demonstrating Modding System...");
            
            if (moddingSystem != null)
            {
                // Refresh mods
                moddingSystem.RefreshAvailableMods();
                yield return new WaitForSeconds(1f);
                
                // Toggle sandbox mode
                moddingSystem.ToggleSandboxMode(true);
                yield return new WaitForSeconds(1f);
                
                moddingSystem.ToggleSandboxMode(false);
            }
            
            yield return new WaitForSeconds(2f);
        }
        #endregion

        #region Utility
        private void EnableAllUISystems()
        {
            // Enable all UI systems for exploration
            var questLogUI = FindObjectOfType<QuestLogUI>();
            var inventoryUI = FindObjectOfType<InventoryUI>();
            var magicTreeUI = FindObjectOfType<MagicTreeUI>();
            var mapUI = FindObjectOfType<MapUI>();
            var stealthUI = FindObjectOfType<StealthIndicatorUI>();
            var modManagerUI = FindObjectOfType<ModManagerUI>();
            
            Debug.Log("All UI systems enabled for free exploration!");
        }

        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    [System.Serializable]
    public class DemoStep
    {
        public string title;
        public string description;
        public string systemName;
        public System.Func<IEnumerator> demoAction;
    }

    public class SystemIndicatorUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI systemNameText;
        [SerializeField] private Image statusIcon;
        [SerializeField] private Color activeColor = Color.green;
        [SerializeField] private Color inactiveColor = Color.gray;

        public void Setup(string systemName, bool isActive)
        {
            if (systemNameText != null)
                systemNameText.text = systemName;
            
            SetActive(isActive);
        }

        public void SetActive(bool isActive)
        {
            if (statusIcon != null)
                statusIcon.color = isActive ? activeColor : inactiveColor;
        }
    }
    #endregion
}
