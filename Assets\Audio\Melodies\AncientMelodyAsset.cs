using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// ScriptableObject to hold ancient melody audio clips with metadata
/// Provides organized access to atmospheric music for contemplative systems
/// </summary>
[CreateAssetMenu(fileName = "AncientMelodyAsset", menuName = "Cinder of Darkness/Ancient Melody Asset")]
public class AncientMelodyAsset : ScriptableObject
{
    [Head<PERSON>("Ancient Melody Audio Clips")]
    [Tooltip("12 atmospheric ancient melody tracks for contemplative moments")]
    public AudioClip[] ancientMelodies = new AudioClip[12];
    
    [<PERSON>er("Melody Metadata")]
    [Tooltip("Cultural and emotional information for each melody")]
    public AncientMelodyMetadata[] melodyMetadata = new AncientMelodyMetadata[12];
    
    [Head<PERSON>("Playback Settings")]
    [Range(0f, 1f)]
    [Tooltip("Default volume for melody playback")]
    public float defaultVolume = 0.4f;
    
    [Range(0f, 1f)]
    [Tooltip("3D spatial blend (0 = 2D, 1 = 3D)")]
    public float spatialBlend = 0.6f;
    
    [Tooltip("Fade in duration when melody starts")]
    public float fadeInDuration = 3f;
    
    [Tooltip("Fade out duration when melody ends")]
    public float fadeOutDuration = 5f;
    
    [Header("Cultural Filtering")]
    [Tooltip("Enable cultural-specific melody selection")]
    public bool enableCulturalFiltering = true;
    
    [Tooltip("Enable emotional-specific melody selection")]
    public bool enableEmotionalFiltering = true;
    
    [Header("Randomization")]
    [Range(0f, 0.3f)]
    [Tooltip("Random pitch variation for each playback")]
    public float pitchVariation = 0.1f;
    
    [Range(0f, 0.2f)]
    [Tooltip("Random volume variation for each playback")]
    public float volumeVariation = 0.05f;
    
    /// <summary>
    /// Get a random ancient melody from all available clips
    /// </summary>
    public AudioClip GetRandomMelody()
    {
        if (ancientMelodies == null || ancientMelodies.Length == 0)
            return null;
        
        List<AudioClip> validClips = ancientMelodies.Where(clip => clip != null).ToList();
        if (validClips.Count == 0)
            return null;
        
        int randomIndex = Random.Range(0, validClips.Count);
        return validClips[randomIndex];
    }
    
    /// <summary>
    /// Get a melody by specific index
    /// </summary>
    public AudioClip GetMelody(int index)
    {
        if (ancientMelodies == null || index < 0 || index >= ancientMelodies.Length)
            return null;
        
        return ancientMelodies[index];
    }
    
    /// <summary>
    /// Get melodies filtered by cultural origin
    /// </summary>
    public AudioClip GetMelodyByCulture(CulturalOrigin culture)
    {
        if (!enableCulturalFiltering || melodyMetadata == null)
            return GetRandomMelody();
        
        List<int> matchingIndices = new List<int>();
        
        for (int i = 0; i < melodyMetadata.Length; i++)
        {
            if (melodyMetadata[i].culturalOrigin == culture && ancientMelodies[i] != null)
            {
                matchingIndices.Add(i);
            }
        }
        
        if (matchingIndices.Count == 0)
            return GetRandomMelody(); // Fallback to random
        
        int randomIndex = matchingIndices[Random.Range(0, matchingIndices.Count)];
        return ancientMelodies[randomIndex];
    }
    
    /// <summary>
    /// Get melodies filtered by emotional tag
    /// </summary>
    public AudioClip GetMelodyByEmotion(EmotionalTag emotion)
    {
        if (!enableEmotionalFiltering || melodyMetadata == null)
            return GetRandomMelody();
        
        List<int> matchingIndices = new List<int>();
        
        for (int i = 0; i < melodyMetadata.Length; i++)
        {
            if (melodyMetadata[i].emotionalTag == emotion && ancientMelodies[i] != null)
            {
                matchingIndices.Add(i);
            }
        }
        
        if (matchingIndices.Count == 0)
            return GetRandomMelody(); // Fallback to random
        
        int randomIndex = matchingIndices[Random.Range(0, matchingIndices.Count)];
        return ancientMelodies[randomIndex];
    }
    
    /// <summary>
    /// Get melodies filtered by both culture and emotion
    /// </summary>
    public AudioClip GetMelodyByCultureAndEmotion(CulturalOrigin culture, EmotionalTag emotion)
    {
        if (melodyMetadata == null)
            return GetRandomMelody();
        
        List<int> matchingIndices = new List<int>();
        
        for (int i = 0; i < melodyMetadata.Length; i++)
        {
            bool cultureMatch = !enableCulturalFiltering || melodyMetadata[i].culturalOrigin == culture;
            bool emotionMatch = !enableEmotionalFiltering || melodyMetadata[i].emotionalTag == emotion;
            
            if (cultureMatch && emotionMatch && ancientMelodies[i] != null)
            {
                matchingIndices.Add(i);
            }
        }
        
        if (matchingIndices.Count == 0)
        {
            // Try just culture
            AudioClip cultureClip = GetMelodyByCulture(culture);
            if (cultureClip != null) return cultureClip;
            
            // Try just emotion
            AudioClip emotionClip = GetMelodyByEmotion(emotion);
            if (emotionClip != null) return emotionClip;
            
            // Final fallback
            return GetRandomMelody();
        }
        
        int randomIndex = matchingIndices[Random.Range(0, matchingIndices.Count)];
        return ancientMelodies[randomIndex];
    }
    
    /// <summary>
    /// Get melodies suitable for contemplative moments
    /// </summary>
    public AudioClip GetContemplativeMelody()
    {
        EmotionalTag[] contemplativeTags = {
            EmotionalTag.Peace,
            EmotionalTag.Reflection,
            EmotionalTag.Serenity,
            EmotionalTag.Wonder
        };
        
        EmotionalTag randomTag = contemplativeTags[Random.Range(0, contemplativeTags.Length)];
        return GetMelodyByEmotion(randomTag);
    }
    
    /// <summary>
    /// Get melodies suitable for sorrowful moments
    /// </summary>
    public AudioClip GetSorrowfulMelody()
    {
        EmotionalTag[] sorrowfulTags = {
            EmotionalTag.Loss,
            EmotionalTag.Sorrow,
            EmotionalTag.Longing
        };
        
        EmotionalTag randomTag = sorrowfulTags[Random.Range(0, sorrowfulTags.Length)];
        return GetMelodyByEmotion(randomTag);
    }
    
    /// <summary>
    /// Get melodies suitable for hopeful moments
    /// </summary>
    public AudioClip GetHopefulMelody()
    {
        EmotionalTag[] hopefulTags = {
            EmotionalTag.Hope,
            EmotionalTag.Determination,
            EmotionalTag.Innocence
        };
        
        EmotionalTag randomTag = hopefulTags[Random.Range(0, hopefulTags.Length)];
        return GetMelodyByEmotion(randomTag);
    }
    
    /// <summary>
    /// Get melodies suitable for mysterious moments
    /// </summary>
    public AudioClip GetMysteriousMelody()
    {
        EmotionalTag[] mysteriousTags = {
            EmotionalTag.Mystery,
            EmotionalTag.Transcendence,
            EmotionalTag.Wonder
        };
        
        EmotionalTag randomTag = mysteriousTags[Random.Range(0, mysteriousTags.Length)];
        return GetMelodyByEmotion(randomTag);
    }
    
    /// <summary>
    /// Get metadata for a specific melody
    /// </summary>
    public AncientMelodyMetadata GetMelodyMetadata(int index)
    {
        if (melodyMetadata == null || index < 0 || index >= melodyMetadata.Length)
            return null;
        
        return melodyMetadata[index];
    }
    
    /// <summary>
    /// Get metadata for a specific audio clip
    /// </summary>
    public AncientMelodyMetadata GetMelodyMetadata(AudioClip clip)
    {
        if (clip == null || ancientMelodies == null || melodyMetadata == null)
            return null;
        
        for (int i = 0; i < ancientMelodies.Length; i++)
        {
            if (ancientMelodies[i] == clip)
            {
                return melodyMetadata[i];
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Get random pitch variation for playback
    /// </summary>
    public float GetRandomPitch()
    {
        return 1f + Random.Range(-pitchVariation, pitchVariation);
    }
    
    /// <summary>
    /// Get random volume variation for playback
    /// </summary>
    public float GetRandomVolume()
    {
        return defaultVolume + Random.Range(-volumeVariation, volumeVariation);
    }
    
    /// <summary>
    /// Get all melodies of a specific instrument type
    /// </summary>
    public AudioClip[] GetMelodiesByInstrument(InstrumentType instrument)
    {
        if (melodyMetadata == null)
            return new AudioClip[0];
        
        List<AudioClip> matchingClips = new List<AudioClip>();
        
        for (int i = 0; i < melodyMetadata.Length; i++)
        {
            if (melodyMetadata[i].instrumentType == instrument && ancientMelodies[i] != null)
            {
                matchingClips.Add(ancientMelodies[i]);
            }
        }
        
        return matchingClips.ToArray();
    }
    
    /// <summary>
    /// Get count of valid melody clips
    /// </summary>
    public int GetValidMelodyCount()
    {
        if (ancientMelodies == null)
            return 0;
        
        return ancientMelodies.Count(clip => clip != null);
    }
    
    /// <summary>
    /// Validate that all clips and metadata are properly assigned
    /// </summary>
    public bool ValidateAsset()
    {
        if (ancientMelodies == null || melodyMetadata == null)
            return false;
        
        if (ancientMelodies.Length != melodyMetadata.Length)
            return false;
        
        for (int i = 0; i < ancientMelodies.Length; i++)
        {
            if (ancientMelodies[i] == null || melodyMetadata[i] == null)
                return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// Get asset status for debugging
    /// </summary>
    public string GetAssetStatus()
    {
        int validClips = GetValidMelodyCount();
        int totalClips = ancientMelodies?.Length ?? 0;
        bool isValid = ValidateAsset();
        
        return $"Ancient Melody Asset Status:\n" +
               $"Valid Clips: {validClips}/{totalClips}\n" +
               $"Asset Valid: {isValid}\n" +
               $"Cultural Filtering: {enableCulturalFiltering}\n" +
               $"Emotional Filtering: {enableEmotionalFiltering}\n" +
               $"Default Volume: {defaultVolume:F2}\n" +
               $"Spatial Blend: {spatialBlend:F2}";
    }
}

/// <summary>
/// Metadata for individual ancient melody clips
/// </summary>
[System.Serializable]
public class AncientMelodyMetadata
{
    [Header("Identity")]
    public string title;
    public CulturalOrigin culturalOrigin;
    public EmotionalTag emotionalTag;
    public InstrumentType instrumentType;
    
    [Header("Properties")]
    public float duration;
    
    [TextArea(2, 4)]
    public string description;
    
    [Header("Usage Hints")]
    public bool suitableForContemplation = true;
    public bool suitableForNarrative = true;
    public bool suitableForAmbient = true;
    
    public override string ToString()
    {
        return $"{title} ({culturalOrigin}, {emotionalTag}, {instrumentType}, {duration:F1}s)";
    }
}
