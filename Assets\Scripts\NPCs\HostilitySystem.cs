using UnityEngine;
using System.Collections.Generic;

public class HostilitySystem : MonoBehaviour
{
    [Header("Reputation System")]
    public float globalReputation = 0f; // -100 to 100
    public ReputationType currentReputation = ReputationType.Unknown;
    public float fearLevel = 0f; // How much NPCs fear the player
    public float respectLevel = 0f; // How much NPCs respect the player

    [Head<PERSON>("Faction Standing")]
    public Dictionary<FactionType, float> factionStandings = new Dictionary<FactionType, float>();

    [Header("Dynamic Reactions")]
    public float reactionUpdateRange = 50f;
    public LayerMask npcLayerMask;

    private PlayerStats playerStats;
    private PsychologicalSystem psycheSystem;
    private List<NPCController> nearbyNPCs = new List<NPCController>();
    private static HostilitySystem instance;

    public enum ReputationType
    {
        Unknown,        // No reputation yet
        Savior,         // +75 to +100 - Beloved by the innocent
        Hero,           // +50 to +74 - Respected protector
        Neutral,        // -25 to +25 - Mixed reputation
        Feared,         // -50 to -26 - Feared but not hated
        Villa<PERSON>,        // -75 to -51 - Actively hunted
        Monster         // -100 to -76 - Kill on sight
    }

    public enum FactionType
    {
        Innocent,       // Civilians, children, peaceful folk
        Oppressed,      // Slaves, outcasts, downtrodden
        Merchants,      // Traders, craftsmen
        Guards,         // City guards, soldiers
        Nobles,         // Lords, ladies, aristocrats
        Criminals,      // Bandits, thieves, assassins
        Cultists,       // Dark worshippers, fanatics
        Warriors,       // Professional fighters
        Scholars,       // Mages, priests, learned folk
        Monsters        // Demons, undead, beasts
    }

    public static HostilitySystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<HostilitySystem>();
            }
            return instance;
        }
    }

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
            return;
        }
    }

    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        psycheSystem = GetComponent<PsychologicalSystem>();

        InitializeFactionStandings();

        // Subscribe to player events
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }

        InvokeRepeating(nameof(UpdateNearbyNPCReactions), 1f, 2f);
    }

    void InitializeFactionStandings()
    {
        // Initialize all faction standings to neutral
        foreach (FactionType faction in System.Enum.GetValues(typeof(FactionType)))
        {
            factionStandings[faction] = 0f;
        }
    }

    void UpdateNearbyNPCReactions()
    {
        // Find all NPCs in range
        Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, reactionUpdateRange, npcLayerMask);
        nearbyNPCs.Clear();

        foreach (Collider col in nearbyColliders)
        {
            NPCController npc = col.GetComponent<NPCController>();
            if (npc != null)
            {
                nearbyNPCs.Add(npc);
                UpdateNPCReaction(npc);
            }
        }
    }

    void UpdateNPCReaction(NPCController npc)
    {
        FactionType npcFaction = GetNPCFaction(npc);
        float factionStanding = factionStandings.ContainsKey(npcFaction) ? factionStandings[npcFaction] : 0f;

        // Calculate reaction based on reputation, faction standing, and player's psychological state
        NPCReaction reaction = CalculateNPCReaction(npcFaction, factionStanding);

        // Apply reaction to NPC
        ApplyReactionToNPC(npc, reaction);
    }

    FactionType GetNPCFaction(NPCController npc)
    {
        // Determine NPC faction based on their type and characteristics
        switch (npc.npcType)
        {
            case NPCController.NPCType.Villager:
                return npc.relationshipValue < -50f ? FactionType.Oppressed : FactionType.Innocent;
            case NPCController.NPCType.Merchant:
                return FactionType.Merchants;
            case NPCController.NPCType.Guard:
                return FactionType.Guards;
            case NPCController.NPCType.Noble:
                return FactionType.Nobles;
            case NPCController.NPCType.Outcast:
                return FactionType.Oppressed;
            case NPCController.NPCType.Sage:
                return FactionType.Scholars;
            case NPCController.NPCType.Warrior:
                return FactionType.Warriors;
            default:
                return FactionType.Innocent;
        }
    }

    NPCReaction CalculateNPCReaction(FactionType faction, float factionStanding)
    {
        NPCReaction reaction = new NPCReaction();

        // Base reaction on global reputation
        switch (currentReputation)
        {
            case ReputationType.Savior:
            case ReputationType.Hero:
                reaction = CalculateHeroReaction(faction, factionStanding);
                break;
            case ReputationType.Villain:
            case ReputationType.Monster:
                reaction = CalculateVillainReaction(faction, factionStanding);
                break;
            case ReputationType.Feared:
                reaction = CalculateFearedReaction(faction, factionStanding);
                break;
            default:
                reaction = CalculateNeutralReaction(faction, factionStanding);
                break;
        }

        // Modify based on psychological state
        if (psycheSystem != null)
        {
            ModifyReactionByPsychology(ref reaction);
        }

        return reaction;
    }

    NPCReaction CalculateHeroReaction(FactionType faction, float factionStanding)
    {
        NPCReaction reaction = new NPCReaction();

        switch (faction)
        {
            case FactionType.Innocent:
            case FactionType.Oppressed:
                reaction.disposition = NPCDisposition.Admiring;
                reaction.fearLevel = 0f;
                reaction.respectLevel = 80f + factionStanding;
                reaction.dialogueModifier = "hopeful";
                break;
            case FactionType.Criminals:
            case FactionType.Cultists:
                reaction.disposition = NPCDisposition.Fearful;
                reaction.fearLevel = 70f + respectLevel;
                reaction.respectLevel = 20f;
                reaction.dialogueModifier = "intimidated";
                break;
            case FactionType.Guards:
            case FactionType.Warriors:
                reaction.disposition = NPCDisposition.Respectful;
                reaction.fearLevel = 10f;
                reaction.respectLevel = 60f + factionStanding;
                reaction.dialogueModifier = "respectful";
                break;
            default:
                reaction.disposition = NPCDisposition.Friendly;
                reaction.fearLevel = 5f;
                reaction.respectLevel = 50f + factionStanding;
                reaction.dialogueModifier = "friendly";
                break;
        }

        return reaction;
    }

    NPCReaction CalculateVillainReaction(FactionType faction, float factionStanding)
    {
        NPCReaction reaction = new NPCReaction();

        switch (faction)
        {
            case FactionType.Innocent:
            case FactionType.Oppressed:
                reaction.disposition = NPCDisposition.Terrified;
                reaction.fearLevel = 90f + fearLevel;
                reaction.respectLevel = 0f;
                reaction.dialogueModifier = "terrified";
                break;
            case FactionType.Criminals:
            case FactionType.Cultists:
                reaction.disposition = NPCDisposition.WaryRespect;
                reaction.fearLevel = 30f;
                reaction.respectLevel = 70f + factionStanding;
                reaction.dialogueModifier = "wary_respect";
                break;
            case FactionType.Guards:
                reaction.disposition = NPCDisposition.Hostile;
                reaction.fearLevel = 50f;
                reaction.respectLevel = 20f;
                reaction.dialogueModifier = "hostile";
                break;
            default:
                reaction.disposition = NPCDisposition.Fearful;
                reaction.fearLevel = 60f + fearLevel;
                reaction.respectLevel = 10f;
                reaction.dialogueModifier = "fearful";
                break;
        }

        return reaction;
    }

    NPCReaction CalculateFearedReaction(FactionType faction, float factionStanding)
    {
        NPCReaction reaction = new NPCReaction();

        switch (faction)
        {
            case FactionType.Innocent:
                reaction.disposition = NPCDisposition.Cautious;
                reaction.fearLevel = 40f;
                reaction.respectLevel = 30f;
                reaction.dialogueModifier = "cautious";
                break;
            case FactionType.Oppressed:
                reaction.disposition = NPCDisposition.Hopeful;
                reaction.fearLevel = 20f;
                reaction.respectLevel = 50f;
                reaction.dialogueModifier = "hopeful";
                break;
            case FactionType.Criminals:
                reaction.disposition = NPCDisposition.Respectful;
                reaction.fearLevel = 30f;
                reaction.respectLevel = 60f;
                reaction.dialogueModifier = "respectful";
                break;
            default:
                reaction.disposition = NPCDisposition.Neutral;
                reaction.fearLevel = 30f;
                reaction.respectLevel = 30f;
                reaction.dialogueModifier = "neutral";
                break;
        }

        return reaction;
    }

    NPCReaction CalculateNeutralReaction(FactionType faction, float factionStanding)
    {
        NPCReaction reaction = new NPCReaction();
        reaction.disposition = NPCDisposition.Neutral;
        reaction.fearLevel = 10f;
        reaction.respectLevel = 20f + factionStanding;
        reaction.dialogueModifier = "neutral";

        return reaction;
    }

    void ModifyReactionByPsychology(ref NPCReaction reaction)
    {
        switch (psycheSystem.GetCurrentState())
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
            case PsychologicalSystem.PsychologicalState.Hollow:
                reaction.fearLevel += 20f;
                reaction.respectLevel -= 10f;
                break;
            case PsychologicalSystem.PsychologicalState.Enlightened:
                reaction.fearLevel -= 15f;
                reaction.respectLevel += 15f;
                break;
            case PsychologicalSystem.PsychologicalState.Tormented:
                reaction.fearLevel += 10f;
                break;
        }

        // Clamp values
        reaction.fearLevel = Mathf.Clamp(reaction.fearLevel, 0f, 100f);
        reaction.respectLevel = Mathf.Clamp(reaction.respectLevel, 0f, 100f);
    }

    void ApplyReactionToNPC(NPCController npc, NPCReaction reaction)
    {
        // Update NPC's relationship value based on reaction
        float targetRelationship = (reaction.respectLevel - reaction.fearLevel) / 2f;
        npc.relationshipValue = Mathf.Lerp(npc.relationshipValue, targetRelationship, Time.deltaTime * 0.5f);

        // Update NPC behavior
        switch (reaction.disposition)
        {
            case NPCDisposition.Hostile:
                npc.isHostile = true;
                break;
            case NPCDisposition.Terrified:
                // NPC might flee
                TriggerNPCFlee(npc);
                break;
            case NPCDisposition.Admiring:
                // NPC might approach or offer help
                TriggerNPCApproach(npc);
                break;
        }

        // Store reaction for dialogue system
        NPCReactionData reactionData = npc.GetComponent<NPCReactionData>();
        if (reactionData == null)
        {
            reactionData = npc.gameObject.AddComponent<NPCReactionData>();
        }
        reactionData.currentReaction = reaction;
    }

    void TriggerNPCFlee(NPCController npc)
    {
        // Make NPC flee from player
        Vector3 fleeDirection = (npc.transform.position - transform.position).normalized;
        Vector3 fleeTarget = npc.transform.position + fleeDirection * 20f;

        // This would be implemented with pathfinding
        Debug.Log($"{npc.npcName} flees in terror!");
    }

    void TriggerNPCApproach(NPCController npc)
    {
        // Make NPC approach player with gifts or information
        Debug.Log($"{npc.npcName} approaches with admiration!");
    }

    public void ModifyReputation(float amount, string reason = "")
    {
        globalReputation = Mathf.Clamp(globalReputation + amount, -100f, 100f);
        UpdateReputationType();

        if (!string.IsNullOrEmpty(reason))
        {
            Debug.Log($"Reputation changed by {amount}: {reason}");
        }
    }

    public void ModifyFactionStanding(FactionType faction, float amount, string reason = "")
    {
        if (factionStandings.ContainsKey(faction))
        {
            factionStandings[faction] = Mathf.Clamp(factionStandings[faction] + amount, -100f, 100f);
        }
        else
        {
            factionStandings[faction] = Mathf.Clamp(amount, -100f, 100f);
        }

        if (!string.IsNullOrEmpty(reason))
        {
            Debug.Log($"{faction} standing changed by {amount}: {reason}");
        }
    }

    void UpdateReputationType()
    {
        if (globalReputation >= 75f)
            currentReputation = ReputationType.Savior;
        else if (globalReputation >= 50f)
            currentReputation = ReputationType.Hero;
        else if (globalReputation >= 25f)
            currentReputation = ReputationType.Neutral;
        else if (globalReputation >= -25f)
            currentReputation = ReputationType.Neutral;
        else if (globalReputation >= -50f)
            currentReputation = ReputationType.Feared;
        else if (globalReputation >= -75f)
            currentReputation = ReputationType.Villain;
        else
            currentReputation = ReputationType.Monster;
    }

    void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        switch (newPath)
        {
            case PlayerStats.MoralPath.Sun:
                ModifyFactionStanding(FactionType.Innocent, 10f, "Choosing the path of light");
                ModifyFactionStanding(FactionType.Oppressed, 15f, "Becoming a beacon of hope");
                ModifyFactionStanding(FactionType.Criminals, -10f, "Opposing darkness");
                break;
            case PlayerStats.MoralPath.Moon:
                ModifyFactionStanding(FactionType.Criminals, 10f, "Embracing darkness");
                ModifyFactionStanding(FactionType.Cultists, 15f, "Walking the dark path");
                ModifyFactionStanding(FactionType.Innocent, -15f, "Becoming feared");
                break;
            case PlayerStats.MoralPath.Eclipse:
                ModifyFactionStanding(FactionType.Scholars, 10f, "Seeking balance");
                ModifyFactionStanding(FactionType.Warriors, 5f, "Understanding both sides");
                break;
        }
    }

    public string GetReputationTitle()
    {
        switch (currentReputation)
        {
            case ReputationType.Savior: return "The Cinderborn, Savior of the Innocent";
            case ReputationType.Hero: return "The Cinderborn, Protector of the Realm";
            case ReputationType.Feared: return "The Cinderborn, The Feared One";
            case ReputationType.Villain: return "The Cinderborn, Bringer of Darkness";
            case ReputationType.Monster: return "The Cinderborn, The Ash-Cursed";
            default: return "The Cinderborn, Walker Between Worlds";
        }
    }

    // Getters
    public ReputationType GetCurrentReputation() => currentReputation;
    public float GetGlobalReputation() => globalReputation;
    public float GetFactionStanding(FactionType faction)
    {
        return factionStandings.ContainsKey(faction) ? factionStandings[faction] : 0f;
    }
}

[System.Serializable]
public class NPCReaction
{
    public NPCDisposition disposition = NPCDisposition.Neutral;
    public float fearLevel = 0f;
    public float respectLevel = 0f;
    public string dialogueModifier = "neutral";
}

public enum NPCDisposition
{
    Hostile,        // Will attack on sight
    Terrified,      // Will flee immediately
    Fearful,        // Afraid but won't flee
    Cautious,       // Wary but willing to talk
    Neutral,        // No strong feelings
    Friendly,       // Positive but not devoted
    Respectful,     // Shows respect and deference
    Admiring,       // Looks up to player
    WaryRespect     // Respects but doesn't trust
}

public class NPCReactionData : MonoBehaviour
{
    public NPCReaction currentReaction = new NPCReaction();
}
