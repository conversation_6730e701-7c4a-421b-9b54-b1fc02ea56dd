# MOMO - 3D Souls-like Game Prototype

## Game Overview
A dark, medieval fantasy game following a 15-year-old orphaned boy rejected by society. The player navigates moral choices between the Path of the Sun (light), Path of the Moon (darkness), or Eclipse (balance).

## Core Features
- **Moral Choice System**: Three distinct paths affecting story and gameplay
- **Real-time Combat**: Dark Souls-inspired combat with melee weapons and magic
- **Dialogue Trees**: Deep narrative choices with lasting consequences
- **Dual Perspective**: Third-person with first-person toggle
- **Faction System**: NPCs that can become allies or betray the player
- **AI Boss Fight**: Endgame boss that learns from player's combat style
- **Multiple Endings**: Based on player choices - peace or war
- **Rich Lore**: Fantasy world with orcs, elves, dwarves, demons, angels (no gods, only Sages/Emperors)

## Prototype Features
- Starting village and explorable area
- Main character with basic movement and combat
- 2 NPCs with dialogue and alliance/betrayal mechanics
- Combat system with 2 weapon types and 1 magic type
- First boss encounter
- Basic UI and menu systems

## Technology Stack
- **Engine**: Unity 3D
- **Language**: C#
- **Platform**: PC (Windows)

## Project Structure
```
Assets/
├── Scripts/
│   ├── Player/
│   ├── Combat/
│   ├── NPCs/
│   ├── UI/
│   └── Managers/
├── Scenes/
├── Prefabs/
├── Materials/
├── Audio/
└── Textures/
```

## Getting Started
1. Open Unity Hub
2. Create new 3D project named "MOMO"
3. Import the scripts and set up the scenes
4. Configure input settings for combat and camera controls

## Lore & Setting
The world of Aethermoor is ruled by ancient Sages rather than gods. The protagonist discovers their heritage as the child of the Sun Sage and Moon Sage, but only through their chosen path. Themes of slavery, injustice, and the search for purpose drive the narrative.

## Development Notes
- Focus on atmospheric storytelling
- Implement choice consequences early
- Balance combat difficulty for accessibility
- Ensure moral choices feel meaningful
