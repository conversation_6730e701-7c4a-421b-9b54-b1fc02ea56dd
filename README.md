# 🏰 Cinder of Darkness – A AAA Soulslike RPG

[![Unity Version](https://img.shields.io/badge/Unity-2022.3.62f1-blue.svg)](https://unity3d.com/get-unity/download)
[![URP](https://img.shields.io/badge/Render%20Pipeline-URP-green.svg)](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest)
[![Platform](https://img.shields.io/badge/Platform-PC%20%7C%20Steam-lightgrey.svg)](https://store.steampowered.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎮 Overview

**Cinder of Darkness** is a AAA-quality soulslike RPG that follows **The Cinderborn**, a stoic warrior navigating a world torn between light and shadow. Experience brutal combat, deep moral choices, and psychological storytelling in a dark fantasy realm where every decision shapes both the world and your character's soul.

### 🌟 Core Concept
- **Brutal Soulslike Combat** - Sekiro-inspired parrying, visceral finishers, and challenging boss encounters
- **Moral Philosophy System** - Choose between <PERSON> (Light), <PERSON> (Darkness), or Eclipse (Balance) paths
- **Psychological Depth** - Your choices affect mental state, visual effects, and dialogue options
- **Cultural Diversity** - Explore kingdoms inspired by global mythologies with respectful representation
- **Dynamic Storytelling** - World events and NPC relationships evolve based on your actions

## 🚀 Key Features

### ⚔️ Combat & Gameplay
- **Advanced Combat System** - Sekiro-style parries, brutal finishers, and weapon mastery
- **Unique Weapons** - Legendary weapons with deep lore and special abilities (Cradle Fang, Ruin Harp, etc.)
- **Boss Rush Mode** - "Trials of the Ash" post-release content with challenging encounters
- **Psychological Mechanics** - Mental state affects gameplay, visuals, and character interactions
- **Dynamic Difficulty** - Adaptive challenge based on player skill and moral choices

### 🌍 World & Narrative
- **Dynamic World Events** - Living world that responds to player actions and moral alignment
- **Cultural Kingdoms** - Distinct societies with Gothic-Arabic fusion architecture
- **Faith of the Flame** - Spiritual system with ember symbolism and philosophical depth
- **Contemplative Content** - Peaceful moments for reflection and emotional balance
- **Multiple Endings** - Story branches based on moral choices and character relationships

### 🛠️ Technical Excellence
- **Multi-Input Support** - Full keyboard/mouse, Xbox, and PlayStation controller support
- **URP Rendering** - Advanced post-processing with atmospheric effects
- **Localization Ready** - Arabic/English support with RTL text rendering
- **Modding Framework** - Arena Editor with community content creation tools
- **Steam Integration** - Achievements, cloud saves, and community features

### 🎨 Audio & Visuals
- **Dynamic Musical System** - Adaptive soundtrack with ancient/folk ambient music
- **Voice Acting System** - Full voice acting support with subtitle integration
- **Atmospheric Graphics** - Dense forests, spectral elements, and dynamic lighting
- **Cultural Art Style** - Respectful representation of global warrior traditions

## 🧱 Project Structure

```
Assets/
├── 📁 Scripts/              → All gameplay code organized by feature
│   ├── Combat/             → Combat systems, bosses, and weapons
│   ├── Player/             → Player controller, stats, and progression
│   ├── NPCs/               → NPC behavior, dialogue, and cultural systems
│   ├── Narrative/          → Story systems, companions, and moral choices
│   ├── Systems/            → Core engine systems (save, localization, etc.)
│   ├── Modding/            → Arena editor and community mod support
│   ├── UI/                 → User interface and HUD systems
│   └── Testing/            → Comprehensive testing and validation
├── 📁 Scenes/              → Unity scene files
│   ├── MainMenu.unity      → Main menu and entry point
│   └── Realms/             → Game world scenes (6 unique kingdoms)
├── 📁 Art/                 → Visual assets
│   ├── Characters/         → Character models and animations
│   ├── Environments/       → World art and props
│   ├── Icons/              → UI icons and symbols
│   └── FX/                 → Visual effects and particles
├── 📁 Audio/               → Sound assets
│   ├── Music/              → Background music and ancient melodies
│   └── SFX/                → Sound effects and spirit whispers
├── 📁 Settings/            → Configuration files
│   ├── Input Actions      → Controller and keyboard mappings
│   └── URP Asset          → Render pipeline settings
├── 📁 Prefabs/             → Reusable game objects
├── 📁 Materials/           → Shader materials and textures
├── 📁 Localization/        → Multi-language support files
├── 📁 Editor/              → Custom Unity editor tools
└── 📁 Plugins/             → Third-party integrations
```

## 🔧 Unity Requirements

### Minimum Requirements
- **Unity Version**: 2022.3.62f1 LTS (Required)
- **Render Pipeline**: Universal Render Pipeline (URP)
- **Input System**: New Unity Input System package
- **Platform**: Windows 10/11, Steam compatible

### Required Packages
- Universal RP
- Input System
- TextMeshPro
- Timeline
- Cinemachine
- Post Processing Stack v2

### Recommended Hardware
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 or better
- **GPU**: GTX 1060 / RX 580 or better
- **RAM**: 16GB minimum
- **Storage**: 50GB available space

## 🛠️ Build Instructions

### Getting Started
1. **Install Unity Hub** and Unity 2022.3.62f1 LTS
2. **Clone/Download** this repository
3. **Open Unity Hub** → Add Project → Select the project folder
4. **Wait for import** (first import may take 10-15 minutes)
5. **Open MainMenu.unity** scene to start

### Building the Game
1. **File** → **Build Settings**
2. **Add Open Scenes** (MainMenu.unity should be Scene 0)
3. **Select Platform** (PC, Mac & Linux Standalone)
4. **Configure Player Settings** as needed
5. **Build** to your desired output folder

### Development Setup
1. **Install Visual Studio 2022** or **JetBrains Rider** for C# editing
2. **Configure Git** for version control (recommended)
3. **Install Unity packages** via Package Manager if missing
4. **Run ProjectIntegrityValidator** to verify setup

## 👥 Contributors

### Development Team
- **Lead Developer**: [Your Name] - Core systems, gameplay programming
- **AI Co-Pilot**: Augment + Claude Sonnet 4 - Code optimization, system design
- **Technical Consultant**: Unity Technologies - Best practices and optimization

### Special Thanks
- **Community Contributors** - Arena editor testing and feedback
- **Cultural Consultants** - Authentic representation guidance
- **Audio Team** - Ancient melody and ambient sound design

## 📦 Optional Development Tools

### Recommended IDEs
- **Visual Studio 2022** - Full C# debugging and IntelliSense
- **JetBrains Rider** - Advanced refactoring and code analysis
- **Visual Studio Code** - Lightweight editing with Unity extensions

### Version Control
- **Git** - Recommended for source control
- **Git LFS** - For large binary assets
- **Unity Collaborate** - Alternative cloud-based solution

### Asset Store Packages
- **DOTween** - Animation and tweening
- **Odin Inspector** - Enhanced Unity inspector
- **Addressables** - Advanced asset management
- **Unity Analytics** - Player behavior tracking

## 🎯 Development Status

### Current Version: Alpha 1.0
- ✅ **Core Systems** - Player controller, combat, and progression
- ✅ **World Foundation** - 6 unique realms with cultural diversity
- ✅ **Narrative Framework** - Moral choice system and dynamic titles
- ✅ **Technical Excellence** - Multi-input, localization, and modding support
- 🔄 **Content Creation** - Expanding quests, dialogue, and world events
- 🔄 **Polish Phase** - Audio implementation, visual effects, and optimization

### Upcoming Features
- **Voice Acting Integration** - Full character voice implementation
- **Steam Workshop** - Community mod sharing and discovery
- **Achievement System** - 50+ achievements for various playstyles
- **New Game+** - Enhanced replay with retained progression

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Third-Party Assets
- Unity Technologies - Unity Engine and packages
- Various open-source libraries - See CREDITS.md for full attribution

---

**"I am not a hero or villain. I am the ash, and to ash I shall return."** - The Cinderborn

*Experience the journey of The Cinderborn through realms of light and shadow, where every choice echoes through eternity.*
