using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

public class BossController : MonoBehaviour
{
    [Header("Boss Identity")]
    public string bossName = "Unknown Boss";
    public string bossTitle = "";
    public BossType type = BossType.Standard;
    public bool isHiddenBoss = false;

    [Header("Boss Stats")]
    public float maxHealth = 1000f;
    public float currentHealth;
    public float[] phaseHealthThresholds = { 0.75f, 0.5f, 0.25f };
    public int currentPhase = 0;

    [Header("Combat")]
    public float attackDamage = 50f;
    public float attackRange = 3f;
    public float attackCooldown = 2f;
    public BossAttack[] phaseAttacks;

    [Header("AI Mirror System")]
    public bool isAIMirror = false;
    public PlayerCombat playerCombat;
    public List<string> learnedPlayerMoves = new List<string>();
    public float adaptationRate = 0.1f;

    [Header("Philosophical Boss")]
    public bool isPhilosophicalBoss = false;
    public string[] philosophicalTaunts;
    public string[] phaseTransitionQuotes;

    [Header("Visual Effects")]
    public GameObject[] phaseTransitionEffects;
    public ParticleSystem auraEffect;
    public Material[] phaseMaterials;

    [Header("Audio")]
    public AudioClip[] battleMusic;
    public AudioClip[] voiceLines;
    public AudioClip[] phaseTransitionSounds;

    [Header("UI")]
    public GameObject bossHealthBarUI;
    public Slider bossHealthBar;
    public Text bossNameText;
    public Text phaseText;

    private EnemyAI enemyAI;
    private EnemyHealth enemyHealth;
    private Animator bossAnimator;
    private AudioSource audioSource;
    private PlayerStats playerStats;
    private PsychologicalSystem psycheSystem;
    private Transform player;
    private bool isInCombat = false;
    private float lastAttackTime = 0f;
    private bool hasTriggeredPhase = false;
    private bool isBossActive = false;
    private bool isPerformingSpecialAttack = false;
    private float detectionRange = 10f;

    [Header("Phase System")]
    public BossAttack[] basicAttacks;
    public BossAttack[] phaseSpecialAttacks;
    public float[] phaseSpeedMultipliers = { 1f, 1.2f, 1.5f, 2f };
    public float[] phaseDamageMultipliers = { 1f, 1.3f, 1.6f, 2f };
    public AudioClip[] phaseMusicTracks;
    public AudioClip[] attackSounds;
    public AudioClip deathSound;

    public enum BossType
    {
        Standard,           // Regular boss
        FallenMentor,       // The old mentor in the forest
        CorruptedNoble,     // Noble swordsman who transforms
        DemonLord,          // Father of possessed swordsman
        FallenAngel,        // Angel who may cut player's arm
        VillageElder,       // Betrayal boss
        RaisedMother,       // The old woman who raised player
        AIMirror,           // Final boss that mirrors player
        PhilosophicalBoss   // Boss with deep dialogue
    }

    [System.Serializable]
    public class BossAttack
    {
        public string attackName;
        public float damage;
        public float range;
        public float castTime;
        public float cooldown;
        public GameObject attackEffect;
        public bool isAOE;
        public float aoeRadius;
    }

    void Start()
    {
        enemyHealth = GetComponent<EnemyHealth>();
        enemyAI = GetComponent<EnemyAI>();
        audioSource = GetComponent<AudioSource>();

        // Find player
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            player = playerController.transform;
            playerStats = playerController.GetComponent<PlayerStats>();
        }

        // Setup health
        if (enemyHealth != null)
        {
            enemyHealth.OnHealthChanged += OnHealthChanged;
            enemyHealth.OnDeath += OnBossDeath;
        }

        // Hide boss UI initially
        if (bossHealthBarUI != null)
            bossHealthBarUI.SetActive(false);

        SetupBossType();
    }

    void Update()
    {
        if (!isBossActive) return;

        CheckPlayerDistance();
        HandleBossAI();
        UpdateUI();
    }

    void CheckPlayerDistance()
    {
        if (player == null) return;

        float distanceToPlayer = Vector3.Distance(transform.position, player.position);

        if (!isBossActive && distanceToPlayer <= detectionRange)
        {
            ActivateBoss();
        }
    }

    void ActivateBoss()
    {
        isBossActive = true;

        // Show boss UI
        if (bossHealthBarUI != null)
            bossHealthBarUI.SetActive(true);

        // Update boss name
        if (bossNameText != null)
            bossNameText.text = bossName;

        // Play boss music
        PlayPhaseMusic(currentPhase);

        // Disable regular AI and take control
        if (enemyAI != null)
            enemyAI.enabled = false;

        Debug.Log($"Boss {bossName} has been activated!");
    }

    void HandleBossAI()
    {
        if (player == null || isPerformingSpecialAttack) return;

        float distanceToPlayer = Vector3.Distance(transform.position, player.position);

        // Face the player
        Vector3 direction = (player.position - transform.position).normalized;
        direction.y = 0;
        transform.rotation = Quaternion.LookRotation(direction);

        // Attack if in range and cooldown is ready
        if (distanceToPlayer <= attackRange && Time.time >= lastAttackTime + attackCooldown)
        {
            PerformRandomAttack();
        }

        // Move towards player if too far
        if (distanceToPlayer > attackRange)
        {
            Vector3 moveDirection = direction * Time.deltaTime * 3f * phaseSpeedMultipliers[currentPhase - 1];
            transform.position += moveDirection;
        }
    }

    void PerformRandomAttack()
    {
        BossAttack[] availableAttacks = basicAttacks;

        // Use special attacks in later phases
        if (currentPhase > 1 && phaseSpecialAttacks.Length > 0)
        {
            availableAttacks = phaseSpecialAttacks;
        }

        if (availableAttacks.Length > 0)
        {
            BossAttack selectedAttack = availableAttacks[Random.Range(0, availableAttacks.Length)];
            StartCoroutine(ExecuteAttack(selectedAttack));
        }
    }

    System.Collections.IEnumerator ExecuteAttack(BossAttack attack)
    {
        isPerformingSpecialAttack = true;
        lastAttackTime = Time.time;

        Debug.Log($"Boss performing: {attack.attackName}");

        // Play attack sound
        if (attackSounds.Length > 0 && audioSource != null)
        {
            audioSource.PlayOneShot(attackSounds[Random.Range(0, attackSounds.Length)]);
        }

        // Cast time
        yield return new WaitForSeconds(attack.castTime);

        // Execute attack
        if (attack.isAOE)
        {
            PerformAOEAttack(attack);
        }
        else
        {
            PerformSingleTargetAttack(attack);
        }

        // Create attack effect
        if (attack.attackEffect != null)
        {
            Instantiate(attack.attackEffect, transform.position, transform.rotation);
        }

        // Wait for attack cooldown
        yield return new WaitForSeconds(attack.cooldown);

        isPerformingSpecialAttack = false;
    }

    void PerformSingleTargetAttack(BossAttack attack)
    {
        if (player == null || playerStats == null) return;

        float distanceToPlayer = Vector3.Distance(transform.position, player.position);
        if (distanceToPlayer <= attack.range)
        {
            float finalDamage = attack.damage * phaseDamageMultipliers[currentPhase - 1];
            playerStats.TakeDamage(finalDamage);
            Debug.Log($"Boss hits player with {attack.attackName} for {finalDamage} damage!");
        }
    }

    void PerformAOEAttack(BossAttack attack)
    {
        if (player == null || playerStats == null) return;

        float distanceToPlayer = Vector3.Distance(transform.position, player.position);
        if (distanceToPlayer <= attack.aoeRadius)
        {
            float finalDamage = attack.damage * phaseDamageMultipliers[currentPhase - 1];
            playerStats.TakeDamage(finalDamage);
            Debug.Log($"Boss hits player with AOE {attack.attackName} for {finalDamage} damage!");
        }
    }

    void OnHealthChanged(float healthPercentage)
    {
        // Check for phase transitions
        for (int i = 0; i < phaseHealthThresholds.Length; i++)
        {
            if (healthPercentage <= phaseHealthThresholds[i] && currentPhase == i + 1)
            {
                TransitionToPhase(i + 2);
                break;
            }
        }
    }

    void TransitionToPhase(int newPhase)
    {
        currentPhase = newPhase;

        Debug.Log($"Boss entering phase {currentPhase}!");

        // Play phase transition effect
        StartCoroutine(PhaseTransitionEffect());

        // Change music
        PlayPhaseMusic(currentPhase);

        // Reset attack cooldown for immediate phase attack
        lastAttackTime = 0f;
    }

    System.Collections.IEnumerator PhaseTransitionEffect()
    {
        // Create visual effect for phase transition
        GameObject phaseEffect = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        phaseEffect.transform.position = transform.position;
        phaseEffect.transform.localScale = Vector3.zero;

        Renderer effectRenderer = phaseEffect.GetComponent<Renderer>();
        Material effectMat = new Material(Shader.Find("Standard"));
        effectMat.color = Color.red;
        effectMat.EnableKeyword("_EMISSION");
        effectMat.SetColor("_EmissionColor", Color.red * 2f);
        effectRenderer.material = effectMat;

        Destroy(phaseEffect.GetComponent<Collider>());

        // Animate the effect
        float duration = 2f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            phaseEffect.transform.localScale = Vector3.one * progress * 10f;

            Color color = effectMat.color;
            color.a = 1f - progress;
            effectMat.color = color;

            yield return null;
        }

        Destroy(phaseEffect);
    }

    void PlayPhaseMusic(int phase)
    {
        if (phaseMusicTracks.Length > 0 && audioSource != null)
        {
            int trackIndex = Mathf.Min(phase - 1, phaseMusicTracks.Length - 1);
            if (phaseMusicTracks[trackIndex] != null)
            {
                audioSource.clip = phaseMusicTracks[trackIndex];
                audioSource.Play();
            }
        }
    }

    void SetupBossType()
    {
        switch (type)
        {
            case BossType.Standard:
                maxHealth = 300f;
                attackCooldown = 3f;
                break;
            case BossType.FallenMentor:
                maxHealth = 500f;
                attackCooldown = 2.5f;
                break;
            case BossType.AIMirror:
                maxHealth = 1000f;
                attackCooldown = 2f;
                SetupPlayerMirror();
                break;
            case BossType.PhilosophicalBoss:
                maxHealth = 800f;
                attackCooldown = 3f;
                break;
            default:
                maxHealth = 400f;
                attackCooldown = 2.5f;
                break;
        }

        currentHealth = maxHealth;
    }

    void SetupPlayerMirror()
    {
        // Copy player's stats and abilities
        if (playerStats != null)
        {
            maxHealth = playerStats.maxHealth * 1.5f;
            // Mirror player's combat style and moral choices
        }
    }

    void OnBossDeath()
    {
        Debug.Log($"Boss {bossName} has been defeated!");

        // Play death sound
        if (deathSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(deathSound);
        }

        // Hide boss UI
        if (bossHealthBarUI != null)
            bossHealthBarUI.SetActive(false);

        // Trigger boss defeat events
        // This could unlock new areas, give rewards, etc.
    }

    void UpdateUI()
    {
        if (bossHealthBar != null && enemyHealth != null)
        {
            bossHealthBar.value = enemyHealth.GetHealthPercentage();
        }

        if (phaseText != null)
        {
            phaseText.text = $"Phase {currentPhase}";
        }
    }
}
