using UnityEngine;
using System.Collections.Generic;

public static class CulturalDialogueData
{
    public static DialogueTree GetDialogueForCulture(CulturalWarrior.WarriorCulture culture, CulturalWarrior.ReactionToPlayer reaction)
    {
        switch (culture)
        {
            case CulturalWarrior.WarriorCulture.Samurai:
                return CreateSamuraiDialogue(reaction);
            case CulturalWarrior.WarriorCulture.Saracen:
                return CreateSaracenDialogue(reaction);
            case CulturalWarrior.WarriorCulture.Crusader:
                return CreateCrusaderDialogue(reaction);
            case CulturalWarrior.WarriorCulture.Viking:
                return CreateVikingDialogue(reaction);
            case CulturalWarrior.WarriorCulture.Mamluk:
                return CreateMamlukDialogue(reaction);
            case CulturalWarrior.WarriorCulture.Byzantine:
                return CreateByzantineDialogue(reaction);
            default:
                return CreateGenericDialogue(reaction);
        }
    }

    static DialogueTree CreateSamuraiDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Samurai Encounter";
        dialogue.nodes = new List<DialogueNode>();

        // Node 0 - Initial greeting
        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Takeshi the Samurai";

        switch (reaction)
        {
            case CulturalWarrior.ReactionToPlayer.Respectful:
                node0.dialogueText = "Konnichiwa, young warrior. I sense discipline within thee, though thy path remains unclear. The way of the sword teaches that honor is forged through trials.";
                break;
            case CulturalWarrior.ReactionToPlayer.Curious:
                node0.dialogueText = "Interesting... thou dost carry the bearing of one trained in combat, yet thy heritage speaks of distant lands. Tell me, what brings thee to these shores?";
                break;
            default:
                node0.dialogueText = "Hold, stranger. These lands are under my protection. State thy business, and speak truthfully.";
                break;
        }

        node0.choices = new List<DialogueChoice>();

        // Choice 1 - Respectful approach
        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "I seek to understand the way of honor, sensei.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 10f;
        node0.choices.Add(choice1);

        // Choice 2 - Aggressive approach
        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I need no permission to walk these lands.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 8f;
        node0.choices.Add(choice2);

        // Choice 3 - Curious approach
        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "I am The Cinderborn, a wanderer seeking my destiny.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 3f;
        choice3.moonAlignmentChange = 3f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        // Node 1 - Honor path
        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Takeshi the Samurai";
        node1.dialogueText = "Wisdom in thy words, young one. The way of honor is not learned through words alone, but through action. Wouldst thou accept a test of thy resolve?";
        node1.choices = new List<DialogueChoice>();

        DialogueChoice choice1_1 = new DialogueChoice();
        choice1_1.choiceText = "I accept thy challenge with humility.";
        choice1_1.nextDialogueIndex = 4;
        choice1_1.sunAlignmentChange = 15f;
        node1.choices.Add(choice1_1);

        DialogueChoice choice1_2 = new DialogueChoice();
        choice1_2.choiceText = "I am not ready for such trials.";
        choice1_2.nextDialogueIndex = 5;
        node1.choices.Add(choice1_2);

        dialogue.nodes.Add(node1);

        // Node 2 - Conflict path
        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Takeshi the Samurai";
        node2.dialogueText = "Thy arrogance blinds thee to wisdom, young fool. The sword teaches humility to those who refuse to learn it willingly. Draw thy blade!";
        node2.isEndNode = true;
        node2.triggersEvent = true;
        node2.eventName = "BecomeEnemy";
        dialogue.nodes.Add(node2);

        // Node 3 - Neutral path
        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Takeshi the Samurai";
        node3.dialogueText = "The Cinderborn... a title that speaks of rebirth through fire. Thy mixed heritage is written in thy stance, thy eyes. The blood of warriors flows within thee, but which path wilt thou choose?";
        node3.choices = new List<DialogueChoice>();

        DialogueChoice choice3_1 = new DialogueChoice();
        choice3_1.choiceText = "I seek to honor both sides of my heritage.";
        choice3_1.nextDialogueIndex = 6;
        choice3_1.sunAlignmentChange = 8f;
        choice3_1.moonAlignmentChange = 8f;
        node3.choices.Add(choice3_1);

        DialogueChoice choice3_2 = new DialogueChoice();
        choice3_2.choiceText = "My heritage matters not. Only strength does.";
        choice3_2.nextDialogueIndex = 7;
        choice3_2.moonAlignmentChange = 12f;
        node3.choices.Add(choice3_2);

        dialogue.nodes.Add(node3);

        // Additional nodes...
        DialogueNode node4 = new DialogueNode();
        node4.speakerName = "Takeshi the Samurai";
        node4.dialogueText = "Excellent. The test is simple: protect the innocent, even when it costs thee dearly. Return when thou hast proven thy honor.";
        node4.isEndNode = true;
        node4.triggersEvent = true;
        node4.eventName = "GiveQuest";
        dialogue.nodes.Add(node4);

        DialogueNode node5 = new DialogueNode();
        node5.speakerName = "Takeshi the Samurai";
        node5.dialogueText = "Wisdom lies in knowing one's limitations. Return when thy spirit has grown stronger, Cinderborn.";
        node5.isEndNode = true;
        dialogue.nodes.Add(node5);

        DialogueNode node6 = new DialogueNode();
        node6.speakerName = "Takeshi the Samurai";
        node6.dialogueText = "The path of balance... difficult, but perhaps the truest way. Thy ancestors would be proud of such wisdom.";
        node6.isEndNode = true;
        node6.triggersEvent = true;
        node6.eventName = "BecomeAlly";
        dialogue.nodes.Add(node6);

        DialogueNode node7 = new DialogueNode();
        node7.speakerName = "Takeshi the Samurai";
        node7.dialogueText = "Strength without honor is the way of beasts. I pray thou dost learn this truth before it destroys thee.";
        node7.isEndNode = true;
        dialogue.nodes.Add(node7);

        return dialogue;
    }

    static DialogueTree CreateSaracenDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Saracen Encounter";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Saladin ibn Yusuf";

        switch (reaction)
        {
            case CulturalWarrior.ReactionToPlayer.Protective:
                node0.dialogueText = "As-salamu alaykum, ya waladi. I see the blood of the desert in thy veins, though it mingles with that of distant shores. Thou art welcome among thy people.";
                break;
            case CulturalWarrior.ReactionToPlayer.Curious:
                node0.dialogueText = "Peace be upon thee, young traveler. Thy features speak of noble lineage - perhaps the blood of both East and West flows within thee?";
                break;
            default:
                node0.dialogueText = "Hold, stranger. These are troubled times, and trust must be earned. What brings thee to our lands?";
                break;
        }

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "Wa alaykum as-salam. I seek wisdom and purpose.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 8f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I seek power to claim what is rightfully mine.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 10f;
        node0.choices.Add(choice2);

        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "I am lost, searching for my place in this world.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 5f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        // Continue with more nodes...
        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Saladin ibn Yusuf";
        node1.dialogueText = "Mashallah! Wisdom and purpose are the greatest treasures. The desert teaches patience, and the oasis teaches generosity. Both lessons thou must learn.";
        node1.isEndNode = true;
        node1.triggersEvent = true;
        node1.eventName = "BecomeAlly";
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Saladin ibn Yusuf";
        node2.dialogueText = "Power taken by force is like sand in the wind - it slips away when thou dost need it most. True strength comes from justice and mercy.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Saladin ibn Yusuf";
        node3.dialogueText = "The lost often find the greatest treasures, ya walad. Come, share bread with us, and perhaps thy path will become clearer.";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "BecomeAlly";
        dialogue.nodes.Add(node3);

        return dialogue;
    }

    static DialogueTree CreateCrusaderDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Crusader Encounter";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Sir Richard of Acre";

        switch (reaction)
        {
            case CulturalWarrior.ReactionToPlayer.Suspicious:
                node0.dialogueText = "Hold there, stranger. Thy features... they speak of mixed blood. In these times of holy war, one must be cautious. Art thou friend or foe to the faithful?";
                break;
            case CulturalWarrior.ReactionToPlayer.Neutral:
                node0.dialogueText = "Greetings, traveler. I am Sir Richard, sworn to protect pilgrims and the innocent. What brings thee to these contested lands?";
                break;
            default:
                node0.dialogueText = "Deus vult, stranger. These lands have seen much bloodshed between Christian and Saracen. Where dost thy loyalty lie?";
                break;
        }

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "I serve no banner but justice and mercy.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 12f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I serve only myself and my own interests.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 8f;
        node0.choices.Add(choice2);

        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "My heritage is mixed, but my heart seeks peace.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 6f;
        choice3.moonAlignmentChange = 3f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Sir Richard of Acre";
        node1.dialogueText = "Noble words, and I sense truth in them. Perhaps there is hope yet for peace between our peoples. A true knight serves justice above all else.";
        node1.isEndNode = true;
        node1.triggersEvent = true;
        node1.eventName = "BecomeAlly";
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Sir Richard of Acre";
        node2.dialogueText = "Selfish ambition has brought naught but suffering to these lands. I pray thou dost find a worthier cause before it is too late.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Sir Richard of Acre";
        node3.dialogueText = "Mixed blood need not mean a divided heart. Some of the noblest souls I have known carried the heritage of both East and West. Peace be with thee.";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "BecomeAlly";
        dialogue.nodes.Add(node3);

        return dialogue;
    }

    static DialogueTree CreateVikingDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Viking Encounter";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Ragnar Ironbeard";
        node0.dialogueText = "Hah! Another warrior walks these lands. I care not for thy bloodline, stranger - only whether thou canst fight! The gods judge by deeds, not by birth.";

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "Then let us test our strength in honorable combat!";
        choice1.nextDialogueIndex = 1;
        choice1.moonAlignmentChange = 10f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I prefer to avoid unnecessary bloodshed.";
        choice2.nextDialogueIndex = 2;
        choice2.sunAlignmentChange = 8f;
        node0.choices.Add(choice2);

        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "I fight when I must, but seek wisdom first.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 5f;
        choice3.moonAlignmentChange = 5f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Ragnar Ironbeard";
        node1.dialogueText = "HAH! Now thou dost speak like a true warrior! Come then, let us see if thy blade is as sharp as thy tongue!";
        node1.isEndNode = true;
        node1.triggersEvent = true;
        node1.eventName = "BecomeAlly";
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Ragnar Ironbeard";
        node2.dialogueText = "Bah! Thou dost sound like a monk, not a warrior. The gods favor the bold, not the meek. But... perhaps there is wisdom in thy words.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Ragnar Ironbeard";
        node3.dialogueText = "Wisdom and strength together? Rare indeed! Thou wouldst make a fine addition to my warband, stranger. What say thee?";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "BecomeAlly";
        dialogue.nodes.Add(node3);

        return dialogue;
    }

    static DialogueTree CreateMamlukDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Mamluk Encounter";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Baibars al-Bunduqdari";
        node0.dialogueText = "Ahlan wa sahlan, young warrior. I am Baibars, once a slave, now a sultan through skill and determination. Thy mixed heritage speaks of strength, not weakness.";

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "How did you rise from slavery to leadership?";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 5f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "Power taken by force is the only power that matters.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 8f;
        node0.choices.Add(choice2);

        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "I seek to prove myself worthy of respect.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 8f;
        choice3.moonAlignmentChange = 3f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Baibars al-Bunduqdari";
        node1.dialogueText = "Through excellence in all things - warfare, scholarship, justice. A Mamluk must be more than a warrior; he must be a leader worthy of following.";
        node1.isEndNode = true;
        node1.triggersEvent = true;
        node1.eventName = "BecomeAlly";
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Baibars al-Bunduqdari";
        node2.dialogueText = "Force alone creates tyrants, not leaders. True power comes from earning the loyalty of those who follow thee willingly.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Baibars al-Bunduqdari";
        node3.dialogueText = "Respect is earned through deeds, not demanded through threats. Show me thy worth, and I shall show thee the path to greatness.";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "GiveQuest";
        dialogue.nodes.Add(node3);

        return dialogue;
    }

    static DialogueTree CreateByzantineDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Byzantine Encounter";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Constantine Palaiologos";

        switch (reaction)
        {
            case CulturalWarrior.ReactionToPlayer.Protective:
                node0.dialogueText = "Chairete, young one! I see the noble blood of Hellas in thy features. The Empire may have fallen, but the spirit of our ancestors lives on in thee.";
                break;
            default:
                node0.dialogueText = "Greetings, stranger. I am Constantine, last of the Palaiologos line. These are dark times for those who remember the glory of Byzantium.";
                break;
        }

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "Tell me of the old empire's glory.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 5f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "The past is dead. Only the future matters.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 6f;
        node0.choices.Add(choice2);

        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "Perhaps the old ways can be reborn in new forms.";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 8f;
        choice3.moonAlignmentChange = 5f;
        node0.choices.Add(choice3);

        dialogue.nodes.Add(node0);

        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Constantine Palaiologos";
        node1.dialogueText = "Ah, to speak of Konstantinoupolis in its prime! The greatest city in the world, where East met West, where learning and faith flourished together...";
        node1.isEndNode = true;
        node1.triggersEvent = true;
        node1.eventName = "BecomeAlly";
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Constantine Palaiologos";
        node2.dialogueText = "Perhaps thou art right. Yet those who forget the past are doomed to repeat its mistakes. Remember that, young one.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Constantine Palaiologos";
        node3.dialogueText = "Wisdom beyond thy years! Yes, the phoenix can rise from ashes, but only if someone tends the flame. Wilt thou help preserve what was best of the old world?";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "BecomeAlly";
        dialogue.nodes.Add(node3);

        return dialogue;
    }

    static DialogueTree CreateGenericDialogue(CulturalWarrior.ReactionToPlayer reaction)
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Generic Warrior";
        dialogue.nodes = new List<DialogueNode>();

        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Unknown Warrior";
        node0.dialogueText = "Greetings, traveler. These are dangerous times for those who walk alone. What brings thee to these lands?";

        node0.choices = new List<DialogueChoice>();

        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "I seek my destiny.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 3f;
        node0.choices.Add(choice1);

        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I seek power and glory.";
        choice2.nextDialogueIndex = 2;
        choice2.moonAlignmentChange = 5f;
        node0.choices.Add(choice2);

        dialogue.nodes.Add(node0);

        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Unknown Warrior";
        node1.dialogueText = "A noble quest. May thy path lead thee to wisdom as well as strength.";
        node1.isEndNode = true;
        dialogue.nodes.Add(node1);

        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Unknown Warrior";
        node2.dialogueText = "Power without purpose is a hollow victory. Consider what thou truly seekest.";
        node2.isEndNode = true;
        dialogue.nodes.Add(node2);

        return dialogue;
    }
}
