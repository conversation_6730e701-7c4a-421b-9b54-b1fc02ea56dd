using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

namespace CinderOfDarkness.Narrative
{
    /// <summary>
    /// Dynamic Narrative System for Cinder of Darkness.
    /// Manages branching storylines, multiple endings, and dynamic reputation system.
    /// </summary>
    public class DynamicNarrativeSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Narrative Settings")]
        [SerializeField] private bool enableDynamicNarrative = true;
        [SerializeField] private float reputationDecayRate = 0.1f;
        [SerializeField] private float consequenceDelayMin = 30f;
        [SerializeField] private float consequenceDelayMax = 300f;

        [Header("Story Branches")]
        [SerializeField] private StoryBranch[] storyBranches;
        [SerializeField] private NarrativeEnding[] possibleEndings;

        [Header("Reputation System")]
        [SerializeField] private ReputationFaction[] factions;
        [SerializeField] private float globalReputationWeight = 0.3f;

        [Header("Consequence System")]
        [SerializeField] private ConsequenceEvent[] consequenceEvents;
        [SerializeField] private int maxActiveConsequences = 10;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        #endregion

        #region Public Properties
        public static DynamicNarrativeSystem Instance { get; private set; }
        public NarrativeState CurrentNarrativeState { get; private set; }
        public float GlobalReputation { get; private set; }
        public Dictionary<string, float> FactionReputations { get; private set; }
        #endregion

        #region Private Fields
        private List<PlayerChoice> choiceHistory = new List<PlayerChoice>();
        private List<ActiveConsequence> activeConsequences = new List<ActiveConsequence>();
        private Dictionary<string, StoryFlag> storyFlags = new Dictionary<string, StoryFlag>();
        private Queue<DelayedConsequence> delayedConsequences = new Queue<DelayedConsequence>();

        // System references
        private GameManager gameManager;
        private PlayerStats playerStats;
        private DialogueSystem dialogueSystem;
        private QuestSystem questSystem;

        // Narrative tracking
        private string currentStoryBranch = "main";
        private int narrativeDepth = 0;
        private float lastReputationUpdate;
        #endregion

        #region Events
        public Action<PlayerChoice> OnChoiceMade;
        public Action<string, float> OnReputationChanged;
        public Action<ConsequenceEvent> OnConsequenceTriggered;
        public Action<NarrativeEnding> OnEndingUnlocked;
        public Action<string> OnStoryBranchChanged;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeNarrativeSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            LoadNarrativeState();
            InitializeReputationSystem();
        }

        private void Update()
        {
            if (!enableDynamicNarrative) return;

            UpdateDelayedConsequences();
            UpdateReputationDecay();
            CheckStoryBranchConditions();
            ProcessActiveConsequences();
        }
        #endregion

        #region Initialization
        private void InitializeNarrativeSystem()
        {
            CurrentNarrativeState = new NarrativeState();
            FactionReputations = new Dictionary<string, float>();

            // Initialize faction reputations
            if (factions != null)
            {
                foreach (var faction in factions)
                {
                    FactionReputations[faction.factionId] = faction.startingReputation;
                }
            }

            Debug.Log("Dynamic Narrative System initialized");
        }

        private void SetupSystemReferences()
        {
            gameManager = GameManager.Instance;
            playerStats = FindObjectOfType<PlayerStats>();
            dialogueSystem = FindObjectOfType<DialogueSystem>();
            questSystem = QuestSystem.Instance;
        }

        private void InitializeReputationSystem()
        {
            GlobalReputation = 0f;
            lastReputationUpdate = Time.time;
        }
        #endregion

        #region Choice System
        /// <summary>
        /// Register a player choice and trigger narrative consequences.
        /// </summary>
        public void MakeChoice(string choiceId, string choiceText, ChoiceType type, Dictionary<string, float> reputationEffects = null)
        {
            var choice = new PlayerChoice
            {
                choiceId = choiceId,
                choiceText = choiceText,
                type = type,
                timestamp = Time.time,
                storyContext = currentStoryBranch,
                reputationEffects = reputationEffects ?? new Dictionary<string, float>()
            };

            choiceHistory.Add(choice);
            ProcessChoiceConsequences(choice);

            OnChoiceMade?.Invoke(choice);

            if (showDebugInfo)
            {
                Debug.Log($"Choice made: {choiceId} - {choiceText}");
            }
        }

        /// <summary>
        /// Process immediate and delayed consequences of a choice.
        /// </summary>
        private void ProcessChoiceConsequences(PlayerChoice choice)
        {
            // Apply immediate reputation effects
            ApplyReputationEffects(choice.reputationEffects);

            // Check for immediate consequences
            var immediateConsequences = consequenceEvents.Where(c =>
                c.triggerChoiceId == choice.choiceId && c.delayTime <= 0f);

            foreach (var consequence in immediateConsequences)
            {
                TriggerConsequence(consequence);
            }

            // Queue delayed consequences
            var delayedConsequenceEvents = consequenceEvents.Where(c =>
                c.triggerChoiceId == choice.choiceId && c.delayTime > 0f);

            foreach (var consequence in delayedConsequenceEvents)
            {
                var delayedConsequence = new DelayedConsequence
                {
                    consequence = consequence,
                    triggerTime = Time.time + consequence.delayTime,
                    sourceChoice = choice
                };
                delayedConsequences.Enqueue(delayedConsequence);
            }

            // Update story flags
            UpdateStoryFlags(choice);
        }

        /// <summary>
        /// Update story flags based on player choice.
        /// </summary>
        private void UpdateStoryFlags(PlayerChoice choice)
        {
            string flagKey = $"choice_{choice.choiceId}";
            storyFlags[flagKey] = new StoryFlag
            {
                flagId = flagKey,
                value = true,
                timestamp = Time.time,
                context = choice.storyContext
            };

            // Update narrative depth based on choice significance
            if (choice.type == ChoiceType.Major)
            {
                narrativeDepth++;
            }
        }
        #endregion

        #region Reputation System
        /// <summary>
        /// Apply reputation effects from choices or events.
        /// </summary>
        public void ApplyReputationEffects(Dictionary<string, float> effects)
        {
            foreach (var effect in effects)
            {
                ModifyFactionReputation(effect.Key, effect.Value);
            }

            UpdateGlobalReputation();
        }

        /// <summary>
        /// Modify reputation with a specific faction.
        /// </summary>
        public void ModifyFactionReputation(string factionId, float change)
        {
            if (!FactionReputations.ContainsKey(factionId))
            {
                FactionReputations[factionId] = 0f;
            }

            float oldReputation = FactionReputations[factionId];
            FactionReputations[factionId] = Mathf.Clamp(oldReputation + change, -100f, 100f);

            OnReputationChanged?.Invoke(factionId, FactionReputations[factionId]);

            if (showDebugInfo)
            {
                Debug.Log($"Faction {factionId} reputation: {oldReputation:F1} -> {FactionReputations[factionId]:F1}");
            }
        }

        /// <summary>
        /// Update global reputation based on faction standings.
        /// </summary>
        private void UpdateGlobalReputation()
        {
            if (FactionReputations.Count == 0) return;

            float totalReputation = FactionReputations.Values.Sum();
            float averageReputation = totalReputation / FactionReputations.Count;

            GlobalReputation = Mathf.Lerp(GlobalReputation, averageReputation, globalReputationWeight);
        }

        /// <summary>
        /// Apply reputation decay over time.
        /// </summary>
        private void UpdateReputationDecay()
        {
            if (Time.time - lastReputationUpdate < 60f) return; // Update every minute

            foreach (var factionId in FactionReputations.Keys.ToList())
            {
                float currentRep = FactionReputations[factionId];
                float decayAmount = reputationDecayRate * Time.deltaTime;

                if (currentRep > 0)
                {
                    FactionReputations[factionId] = Mathf.Max(0f, currentRep - decayAmount);
                }
                else if (currentRep < 0)
                {
                    FactionReputations[factionId] = Mathf.Min(0f, currentRep + decayAmount);
                }
            }

            lastReputationUpdate = Time.time;
        }

        /// <summary>
        /// Get reputation level description.
        /// </summary>
        public string GetReputationLevel(string factionId)
        {
            if (!FactionReputations.ContainsKey(factionId)) return "Unknown";

            float rep = FactionReputations[factionId];

            if (rep >= 80f) return "Revered";
            if (rep >= 60f) return "Honored";
            if (rep >= 40f) return "Friendly";
            if (rep >= 20f) return "Liked";
            if (rep >= -20f) return "Neutral";
            if (rep >= -40f) return "Disliked";
            if (rep >= -60f) return "Hostile";
            if (rep >= -80f) return "Hated";
            return "Despised";
        }
        #endregion

        #region Consequence System
        /// <summary>
        /// Update and process delayed consequences.
        /// </summary>
        private void UpdateDelayedConsequences()
        {
            while (delayedConsequences.Count > 0 && delayedConsequences.Peek().triggerTime <= Time.time)
            {
                var delayedConsequence = delayedConsequences.Dequeue();
                TriggerConsequence(delayedConsequence.consequence);
            }
        }

        /// <summary>
        /// Trigger a consequence event.
        /// </summary>
        private void TriggerConsequence(ConsequenceEvent consequence)
        {
            if (activeConsequences.Count >= maxActiveConsequences) return;

            var activeConsequence = new ActiveConsequence
            {
                consequenceEvent = consequence,
                startTime = Time.time,
                isActive = true
            };

            activeConsequences.Add(activeConsequence);
            ExecuteConsequence(consequence);

            OnConsequenceTriggered?.Invoke(consequence);

            if (showDebugInfo)
            {
                Debug.Log($"Consequence triggered: {consequence.consequenceId}");
            }
        }

        /// <summary>
        /// Execute the effects of a consequence.
        /// </summary>
        private void ExecuteConsequence(ConsequenceEvent consequence)
        {
            switch (consequence.type)
            {
                case ConsequenceType.DialogueChange:
                    ModifyDialogueAvailability(consequence.targetId, consequence.effectValue > 0);
                    break;

                case ConsequenceType.QuestModification:
                    ModifyQuest(consequence.targetId, consequence.effectValue);
                    break;

                case ConsequenceType.NPCBehaviorChange:
                    ModifyNPCBehavior(consequence.targetId, consequence.effectValue);
                    break;

                case ConsequenceType.StoryBranchUnlock:
                    UnlockStoryBranch(consequence.targetId);
                    break;

                case ConsequenceType.EndingUnlock:
                    UnlockEnding(consequence.targetId);
                    break;
            }
        }

        /// <summary>
        /// Process active consequences and remove completed ones.
        /// </summary>
        private void ProcessActiveConsequences()
        {
            for (int i = activeConsequences.Count - 1; i >= 0; i--)
            {
                var consequence = activeConsequences[i];

                if (Time.time - consequence.startTime > consequence.consequenceEvent.duration)
                {
                    activeConsequences.RemoveAt(i);
                }
            }
        }
        #endregion

        #region Story Branch Management
        /// <summary>
        /// Check conditions for story branch changes.
        /// </summary>
        private void CheckStoryBranchConditions()
        {
            foreach (var branch in storyBranches)
            {
                if (branch.branchId == currentStoryBranch) continue;

                if (EvaluateBranchConditions(branch))
                {
                    SwitchStoryBranch(branch.branchId);
                    break;
                }
            }
        }

        /// <summary>
        /// Evaluate if conditions are met for a story branch.
        /// </summary>
        private bool EvaluateBranchConditions(StoryBranch branch)
        {
            // Check reputation requirements
            foreach (var repReq in branch.reputationRequirements)
            {
                if (!FactionReputations.ContainsKey(repReq.factionId) ||
                    FactionReputations[repReq.factionId] < repReq.minimumReputation)
                {
                    return false;
                }
            }

            // Check required choices
            foreach (var requiredChoice in branch.requiredChoices)
            {
                if (!choiceHistory.Any(c => c.choiceId == requiredChoice))
                {
                    return false;
                }
            }

            // Check story flags
            foreach (var flagReq in branch.requiredFlags)
            {
                if (!storyFlags.ContainsKey(flagReq.flagId) ||
                    storyFlags[flagReq.flagId].value != flagReq.requiredValue)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Switch to a new story branch.
        /// </summary>
        private void SwitchStoryBranch(string newBranchId)
        {
            string oldBranch = currentStoryBranch;
            currentStoryBranch = newBranchId;

            OnStoryBranchChanged?.Invoke(newBranchId);

            if (showDebugInfo)
            {
                Debug.Log($"Story branch changed: {oldBranch} -> {newBranchId}");
            }
        }

        /// <summary>
        /// Unlock a new story branch.
        /// </summary>
        private void UnlockStoryBranch(string branchId)
        {
            var branch = storyBranches.FirstOrDefault(b => b.branchId == branchId);
            if (branch != null)
            {
                // Add logic to unlock branch
                Debug.Log($"Story branch unlocked: {branchId}");
            }
        }

        /// <summary>
        /// Unlock a narrative ending.
        /// </summary>
        private void UnlockEnding(string endingId)
        {
            var ending = possibleEndings.FirstOrDefault(e => e.endingId == endingId);
            if (ending != null)
            {
                OnEndingUnlocked?.Invoke(ending);
                Debug.Log($"Ending unlocked: {endingId}");
            }
        }

        /// <summary>
        /// Modify dialogue availability.
        /// </summary>
        private void ModifyDialogueAvailability(string dialogueId, bool available)
        {
            if (dialogueSystem != null)
            {
                // Integration with dialogue system
                Debug.Log($"Dialogue {dialogueId} availability: {available}");
            }
        }

        /// <summary>
        /// Modify quest based on consequences.
        /// </summary>
        private void ModifyQuest(string questId, float modifier)
        {
            if (questSystem != null)
            {
                // Integration with quest system
                Debug.Log($"Quest {questId} modified by: {modifier}");
            }
        }

        /// <summary>
        /// Modify NPC behavior.
        /// </summary>
        private void ModifyNPCBehavior(string npcId, float modifier)
        {
            // Find and modify NPC behavior
            Debug.Log($"NPC {npcId} behavior modified by: {modifier}");
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save narrative state.
        /// </summary>
        public void SaveNarrativeState()
        {
            var saveData = new NarrativeState
            {
                currentBranch = currentStoryBranch,
                narrativeDepth = narrativeDepth,
                globalReputation = GlobalReputation,
                factionReputations = new Dictionary<string, float>(FactionReputations),
                unlockedEndings = possibleEndings.Where(e => IsEndingUnlocked(e)).Select(e => e.endingId).ToList()
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("NarrativeState", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load narrative state.
        /// </summary>
        private void LoadNarrativeState()
        {
            string json = PlayerPrefs.GetString("NarrativeState", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<NarrativeState>(json);
                    currentStoryBranch = saveData.currentBranch ?? "main";
                    narrativeDepth = saveData.narrativeDepth;
                    GlobalReputation = saveData.globalReputation;

                    if (saveData.factionReputations != null)
                    {
                        FactionReputations = new Dictionary<string, float>(saveData.factionReputations);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to load narrative state: {e.Message}");
                }
            }
        }

        /// <summary>
        /// Check if an ending is unlocked.
        /// </summary>
        private bool IsEndingUnlocked(NarrativeEnding ending)
        {
            return EvaluateEndingConditions(ending);
        }

        /// <summary>
        /// Evaluate conditions for an ending.
        /// </summary>
        private bool EvaluateEndingConditions(NarrativeEnding ending)
        {
            // Check reputation requirements
            foreach (var repReq in ending.reputationRequirements)
            {
                if (!FactionReputations.ContainsKey(repReq.factionId) ||
                    FactionReputations[repReq.factionId] < repReq.minimumReputation)
                {
                    return false;
                }
            }

            // Check required choices
            foreach (var requiredChoice in ending.requiredChoices)
            {
                if (!choiceHistory.Any(c => c.choiceId == requiredChoice))
                {
                    return false;
                }
            }

            // Check story flags
            foreach (var flagReq in ending.requiredFlags)
            {
                if (!storyFlags.ContainsKey(flagReq.flagId) ||
                    storyFlags[flagReq.flagId].value != flagReq.requiredValue)
                {
                    return false;
                }
            }

            return true;
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get all available endings based on current state.
        /// </summary>
        public List<NarrativeEnding> GetAvailableEndings()
        {
            return possibleEndings.Where(e => EvaluateEndingConditions(e)).ToList();
        }

        /// <summary>
        /// Get choice history.
        /// </summary>
        public List<PlayerChoice> GetChoiceHistory()
        {
            return new List<PlayerChoice>(choiceHistory);
        }

        /// <summary>
        /// Get current story branch.
        /// </summary>
        public string GetCurrentStoryBranch()
        {
            return currentStoryBranch;
        }

        /// <summary>
        /// Get narrative depth.
        /// </summary>
        public int GetNarrativeDepth()
        {
            return narrativeDepth;
        }

        /// <summary>
        /// Check if a story flag is set.
        /// </summary>
        public bool IsStoryFlagSet(string flagId)
        {
            return storyFlags.ContainsKey(flagId) && storyFlags[flagId].value;
        }

        /// <summary>
        /// Set a story flag manually.
        /// </summary>
        public void SetStoryFlag(string flagId, bool value, string context = "")
        {
            storyFlags[flagId] = new StoryFlag
            {
                flagId = flagId,
                value = value,
                timestamp = Time.time,
                context = context
            };
        }

        /// <summary>
        /// Get reputation with specific faction.
        /// </summary>
        public float GetFactionReputation(string factionId)
        {
            return FactionReputations.ContainsKey(factionId) ? FactionReputations[factionId] : 0f;
        }

        /// <summary>
        /// Reset narrative system to default state.
        /// </summary>
        public void ResetNarrativeSystem()
        {
            choiceHistory.Clear();
            activeConsequences.Clear();
            storyFlags.Clear();
            delayedConsequences.Clear();

            currentStoryBranch = "main";
            narrativeDepth = 0;
            GlobalReputation = 0f;

            InitializeReputationSystem();

            Debug.Log("Narrative system reset to default state");
        }
        #endregion
    }

    #region Data Structures
    [Serializable]
    public class NarrativeState
    {
        public string currentBranch;
        public int narrativeDepth;
        public float globalReputation;
        public List<string> unlockedEndings;
        public Dictionary<string, float> factionReputations;
    }

    [Serializable]
    public class PlayerChoice
    {
        public string choiceId;
        public string choiceText;
        public ChoiceType type;
        public float timestamp;
        public string storyContext;
        public Dictionary<string, float> reputationEffects;
    }

    [Serializable]
    public class StoryBranch
    {
        public string branchId;
        public string branchName;
        public string description;
        public ReputationRequirement[] reputationRequirements;
        public string[] requiredChoices;
        public FlagRequirement[] requiredFlags;
        public bool isEndingBranch;
    }

    [Serializable]
    public class NarrativeEnding
    {
        public string endingId;
        public string endingName;
        public string description;
        public ReputationRequirement[] reputationRequirements;
        public string[] requiredChoices;
        public FlagRequirement[] requiredFlags;
        public EndingType type;
    }

    [Serializable]
    public class ReputationFaction
    {
        public string factionId;
        public string factionName;
        public float startingReputation;
        public Color factionColor;
    }

    [Serializable]
    public class ConsequenceEvent
    {
        public string consequenceId;
        public string triggerChoiceId;
        public ConsequenceType type;
        public float delayTime;
        public float duration;
        public string targetId;
        public float effectValue;
        public string description;
    }

    [Serializable]
    public class ReputationRequirement
    {
        public string factionId;
        public float minimumReputation;
    }

    [Serializable]
    public class FlagRequirement
    {
        public string flagId;
        public bool requiredValue;
    }

    [Serializable]
    public class StoryFlag
    {
        public string flagId;
        public bool value;
        public float timestamp;
        public string context;
    }

    [Serializable]
    public class ActiveConsequence
    {
        public ConsequenceEvent consequenceEvent;
        public float startTime;
        public bool isActive;
    }

    [Serializable]
    public class DelayedConsequence
    {
        public ConsequenceEvent consequence;
        public float triggerTime;
        public PlayerChoice sourceChoice;
    }

    public enum ChoiceType
    {
        Minor,
        Major,
        Critical,
        Moral,
        Combat,
        Dialogue
    }

    public enum ConsequenceType
    {
        DialogueChange,
        QuestModification,
        NPCBehaviorChange,
        StoryBranchUnlock,
        EndingUnlock,
        ReputationChange
    }

    public enum EndingType
    {
        Good,
        Bad,
        Neutral,
        Secret,
        True
    }
    #endregion
}
