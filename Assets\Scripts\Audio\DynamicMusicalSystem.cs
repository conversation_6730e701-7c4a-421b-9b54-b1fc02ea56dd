using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class DynamicMusicalSystem : MonoBehaviour
{
    [Header("Musical Composition")]
    public MusicalTheme[] regionalThemes;
    public MusicalLayer[] orchestralLayers;
    public SilenceAmbience[] silenceAmbiences;
    
    [Header("Emotional Triggers")]
    public EmotionalCue[] emotionalCues;
    public float emotionalFadeSpeed = 2f;
    public float explorationVolume = 0.3f;
    public float storyBeatVolume = 0.8f;
    
    [Header("Dark Neoclassical Elements")]
    public AudioClip[] violinMelodies;
    public AudioClip[] oudCompositions;
    public AudioClip[] qanunArpeggios;
    public AudioClip[] sombrerChants;
    public AudioClip[] breathingSounds;
    public AudioClip[] waterDrops;
    public AudioClip[] ashShifting;
    
    [Header("Silence System")]
    public bool useHeartbeatOnly = false;
    public AudioClip heartbeatSound;
    public float heartbeatIntensity = 1f;
    
    private AudioSource primaryAudioSource;
    private AudioSource ambientAudioSource;
    private AudioSource emotionalAudioSource;
    private AudioSource silenceAudioSource;
    
    private PhilosophicalMoralitySystem moralitySystem;
    private PsychologicalSystem psycheSystem;
    private OrphanedChildCompanion childCompanion;
    private string currentRegion = "";
    private MusicalState currentState = MusicalState.Exploration;
    
    public enum MusicalState
    {
        Exploration,
        StoryBeat,
        Combat,
        Hallucination,
        MoralDecision,
        CompleteSilence,
        HeartbeatOnly
    }
    
    [System.Serializable]
    public class MusicalTheme
    {
        [Header("Regional Identity")]
        public string regionName;
        public RegionType type;
        public CulturalMood mood;
        
        [Header("Instrumental Composition")]
        public AudioClip baseTheme;
        public AudioClip[] stringVariations; // Violin, oud, qanun
        public AudioClip[] vocalChants;
        public AudioClip[] folkMelodies;
        
        [Header("Cultural Elements")]
        public float tempoModifier = 1f;
        public float melancholyLevel = 0.5f;
        public bool useTraditionalInstruments = true;
        public string[] culturalNotes;
        
        public enum RegionType
        {
            KingdomOfLight,
            KingdomOfShadow,
            IndependentVillage,
            SacredGrounds,
            Wilderness,
            Ruins
        }
        
        public enum CulturalMood
        {
            Hopeful,
            Melancholic,
            Mysterious,
            Fearful,
            Reverent,
            Hostile
        }
    }
    
    [System.Serializable]
    public class MusicalLayer
    {
        public string layerName;
        public AudioClip audioClip;
        public LayerType type;
        public float baseVolume = 0.5f;
        public float currentVolume = 0f;
        public bool isActive = false;
        
        public enum LayerType
        {
            Strings,     // Violin, oud, qanun
            Vocals,      // Human chants
            Percussion,  // Subtle drums
            Ambient,     // Environmental sounds
            Emotional    // Swells for story moments
        }
    }
    
    [System.Serializable]
    public class SilenceAmbience
    {
        public string ambienceName;
        public SilenceType type;
        public AudioClip[] soundClips;
        public float volume = 0.2f;
        public float frequency = 5f; // Seconds between sounds
        
        public enum SilenceType
        {
            Breathing,
            WaterDrops,
            AshShifting,
            WindWhisper,
            DistantEcho,
            Heartbeat
        }
    }
    
    [System.Serializable]
    public class EmotionalCue
    {
        public string cueName;
        public EmotionalTrigger trigger;
        public MusicalResponse response;
        public float duration = 5f;
        public AudioClip specificClip;
        
        public enum EmotionalTrigger
        {
            ChildKilled,
            InnocentSaved,
            Betrayal,
            Redemption,
            Hallucination,
            MoralChoice,
            CompanionLost,
            TruthRevealed
        }
        
        public enum MusicalResponse
        {
            CompleteSilence,
            HeartbeatOnly,
            SorrowfulStrings,
            HopefulChant,
            DissonantChord,
            EtherealVoices,
            DramaticSwell,
            FadeToSilence
        }
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        childCompanion = GetComponent<OrphanedChildCompanion>();
        
        InitializeAudioSources();
        InitializeMusicalThemes();
        InitializeSilenceSystem();
    }
    
    void Update()
    {
        UpdateCurrentRegion();
        UpdateMusicalState();
        UpdateLayerVolumes();
        UpdateSilenceAmbience();
    }
    
    void InitializeAudioSources()
    {
        // Create multiple audio sources for layered composition
        primaryAudioSource = gameObject.AddComponent<AudioSource>();
        primaryAudioSource.loop = true;
        primaryAudioSource.volume = explorationVolume;
        
        ambientAudioSource = gameObject.AddComponent<AudioSource>();
        ambientAudioSource.loop = true;
        ambientAudioSource.volume = 0.2f;
        
        emotionalAudioSource = gameObject.AddComponent<AudioSource>();
        emotionalAudioSource.loop = false;
        emotionalAudioSource.volume = 0f;
        
        silenceAudioSource = gameObject.AddComponent<AudioSource>();
        silenceAudioSource.loop = true;
        silenceAudioSource.volume = 0f;
    }
    
    void InitializeMusicalThemes()
    {
        // Kingdom of Light Theme
        MusicalTheme lightTheme = new MusicalTheme
        {
            regionName = "Kingdom of Light",
            type = MusicalTheme.RegionType.KingdomOfLight,
            mood = MusicalTheme.CulturalMood.Hopeful,
            tempoModifier = 1.2f,
            melancholyLevel = 0.2f,
            useTraditionalInstruments = true,
            culturalNotes = new string[]
            {
                "Golden harmonies reflect marble architecture",
                "Mirror-like clarity in musical composition",
                "Ascending melodies symbolize sun worship",
                "Ceremonial chants for truth rituals"
            }
        };
        
        // Kingdom of Shadow Theme
        MusicalTheme shadowTheme = new MusicalTheme
        {
            regionName = "Kingdom of Shadow",
            type = MusicalTheme.RegionType.KingdomOfShadow,
            mood = MusicalTheme.CulturalMood.Mysterious,
            tempoModifier = 0.7f,
            melancholyLevel = 0.8f,
            useTraditionalInstruments = true,
            culturalNotes = new string[]
            {
                "Whispered melodies echo forest dwellings",
                "Subterranean acoustics shape composition",
                "Funeral dirges for the morally changed",
                "Moon-phase rhythms guide tempo"
            }
        };
        
        // Independent Village Themes (varied)
        MusicalTheme villageTheme = new MusicalTheme
        {
            regionName = "Independent Villages",
            type = MusicalTheme.RegionType.IndependentVillage,
            mood = MusicalTheme.CulturalMood.Melancholic,
            tempoModifier = 1f,
            melancholyLevel = 0.6f,
            useTraditionalInstruments = true,
            culturalNotes = new string[]
            {
                "Folk melodies unique to each settlement",
                "Fire-rejection villages use only wind instruments",
                "Stranger-burning villages have aggressive percussion",
                "Fear-based communities use dissonant harmonies"
            }
        };
        
        regionalThemes = new MusicalTheme[] { lightTheme, shadowTheme, villageTheme };
        
        Debug.Log("Dynamic Musical System initialized with Dark Neoclassical themes");
    }
    
    void InitializeSilenceSystem()
    {
        // Create silence ambiences for quiet moments
        SilenceAmbience breathing = new SilenceAmbience
        {
            ambienceName = "Contemplative Breathing",
            type = SilenceAmbience.SilenceType.Breathing,
            soundClips = breathingSounds,
            volume = 0.15f,
            frequency = 8f
        };
        
        SilenceAmbience waterDrops = new SilenceAmbience
        {
            ambienceName = "Cave Dripping",
            type = SilenceAmbience.SilenceType.WaterDrops,
            soundClips = waterDrops,
            volume = 0.1f,
            frequency = 3f
        };
        
        SilenceAmbience ashShifting = new SilenceAmbience
        {
            ambienceName = "Shifting Ash",
            type = SilenceAmbience.SilenceType.AshShifting,
            soundClips = ashShifting,
            volume = 0.08f,
            frequency = 12f
        };
        
        silenceAmbiences = new SilenceAmbience[] { breathing, waterDrops, ashShifting };
    }
    
    void UpdateCurrentRegion()
    {
        // Detect current region based on player position
        string newRegion = DetectCurrentRegion();
        
        if (newRegion != currentRegion)
        {
            TransitionToRegionalTheme(newRegion);
            currentRegion = newRegion;
        }
    }
    
    string DetectCurrentRegion()
    {
        // This would use colliders or position-based detection
        // For now, return a placeholder
        return "Kingdom of Light"; // Placeholder
    }
    
    void TransitionToRegionalTheme(string regionName)
    {
        MusicalTheme theme = GetThemeByRegion(regionName);
        if (theme != null)
        {
            StartCoroutine(CrossfadeToTheme(theme));
        }
    }
    
    MusicalTheme GetThemeByRegion(string regionName)
    {
        foreach (MusicalTheme theme in regionalThemes)
        {
            if (theme.regionName == regionName)
                return theme;
        }
        return null;
    }
    
    IEnumerator CrossfadeToTheme(MusicalTheme newTheme)
    {
        float crossfadeTime = 3f;
        float elapsed = 0f;
        
        AudioClip oldClip = primaryAudioSource.clip;
        float oldVolume = primaryAudioSource.volume;
        
        // Start new theme at low volume
        if (newTheme.baseTheme != null)
        {
            primaryAudioSource.clip = newTheme.baseTheme;
            primaryAudioSource.volume = 0f;
            primaryAudioSource.Play();
        }
        
        // Crossfade
        while (elapsed < crossfadeTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / crossfadeTime;
            
            primaryAudioSource.volume = Mathf.Lerp(0f, explorationVolume * newTheme.tempoModifier, progress);
            
            yield return null;
        }
        
        primaryAudioSource.volume = explorationVolume * newTheme.tempoModifier;
        
        ShowMusicalMessage($"Musical theme transitions to {newTheme.regionName}");
    }
    
    void UpdateMusicalState()
    {
        MusicalState newState = DetermineMusicalState();
        
        if (newState != currentState)
        {
            TransitionToMusicalState(newState);
            currentState = newState;
        }
    }
    
    MusicalState DetermineMusicalState()
    {
        // Check for emotional triggers first
        if (useHeartbeatOnly)
            return MusicalState.HeartbeatOnly;
        
        // Check psychological state
        if (psycheSystem != null && psycheSystem.IsHallucinating())
            return MusicalState.Hallucination;
        
        // Check for moral decision moments
        if (moralitySystem != null && moralitySystem.IsInMoralChoice())
            return MusicalState.MoralDecision;
        
        // Default to exploration
        return MusicalState.Exploration;
    }
    
    void TransitionToMusicalState(MusicalState newState)
    {
        switch (newState)
        {
            case MusicalState.Exploration:
                TransitionToExploration();
                break;
            case MusicalState.StoryBeat:
                TransitionToStoryBeat();
                break;
            case MusicalState.Hallucination:
                TransitionToHallucination();
                break;
            case MusicalState.MoralDecision:
                TransitionToMoralDecision();
                break;
            case MusicalState.CompleteSilence:
                TransitionToCompleteSilence();
                break;
            case MusicalState.HeartbeatOnly:
                TransitionToHeartbeatOnly();
                break;
        }
    }
    
    void TransitionToExploration()
    {
        StartCoroutine(FadeToVolume(primaryAudioSource, explorationVolume, emotionalFadeSpeed));
        StartCoroutine(FadeToVolume(emotionalAudioSource, 0f, emotionalFadeSpeed));
        StartCoroutine(FadeToVolume(silenceAudioSource, 0.1f, emotionalFadeSpeed));
    }
    
    void TransitionToStoryBeat()
    {
        StartCoroutine(FadeToVolume(primaryAudioSource, storyBeatVolume, emotionalFadeSpeed));
        
        // Add emotional layer
        if (emotionalAudioSource.clip != null)
        {
            emotionalAudioSource.Play();
            StartCoroutine(FadeToVolume(emotionalAudioSource, 0.6f, emotionalFadeSpeed));
        }
    }
    
    void TransitionToHallucination()
    {
        // Distorted, ethereal music for hallucinations
        StartCoroutine(FadeToVolume(primaryAudioSource, 0.2f, emotionalFadeSpeed));
        
        // Play dissonant emotional layer
        if (emotionalAudioSource.clip != null)
        {
            emotionalAudioSource.pitch = 0.8f; // Lower pitch for unsettling effect
            emotionalAudioSource.Play();
            StartCoroutine(FadeToVolume(emotionalAudioSource, 0.4f, emotionalFadeSpeed));
        }
    }
    
    void TransitionToMoralDecision()
    {
        // Dramatic swell for moral choices
        StartCoroutine(FadeToVolume(primaryAudioSource, storyBeatVolume * 1.2f, emotionalFadeSpeed * 0.5f));
        
        // Add dramatic strings
        if (violinMelodies.Length > 0)
        {
            emotionalAudioSource.clip = violinMelodies[Random.Range(0, violinMelodies.Length)];
            emotionalAudioSource.pitch = 1f;
            emotionalAudioSource.Play();
            StartCoroutine(FadeToVolume(emotionalAudioSource, 0.8f, emotionalFadeSpeed * 0.5f));
        }
    }
    
    void TransitionToCompleteSilence()
    {
        StartCoroutine(FadeToVolume(primaryAudioSource, 0f, emotionalFadeSpeed * 2f));
        StartCoroutine(FadeToVolume(emotionalAudioSource, 0f, emotionalFadeSpeed * 2f));
        StartCoroutine(FadeToVolume(ambientAudioSource, 0f, emotionalFadeSpeed * 2f));
        
        // Only silence ambience remains
        StartCoroutine(FadeToVolume(silenceAudioSource, 0.3f, emotionalFadeSpeed * 3f));
    }
    
    void TransitionToHeartbeatOnly()
    {
        // Fade all music
        StartCoroutine(FadeToVolume(primaryAudioSource, 0f, emotionalFadeSpeed));
        StartCoroutine(FadeToVolume(emotionalAudioSource, 0f, emotionalFadeSpeed));
        StartCoroutine(FadeToVolume(ambientAudioSource, 0f, emotionalFadeSpeed));
        
        // Play only heartbeat
        if (heartbeatSound != null)
        {
            silenceAudioSource.clip = heartbeatSound;
            silenceAudioSource.Play();
            StartCoroutine(FadeToVolume(silenceAudioSource, heartbeatIntensity, emotionalFadeSpeed));
        }
    }
    
    IEnumerator FadeToVolume(AudioSource source, float targetVolume, float fadeSpeed)
    {
        float startVolume = source.volume;
        float elapsed = 0f;
        float fadeTime = Mathf.Abs(targetVolume - startVolume) / fadeSpeed;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / fadeTime;
            source.volume = Mathf.Lerp(startVolume, targetVolume, progress);
            yield return null;
        }
        
        source.volume = targetVolume;
        
        if (targetVolume <= 0f)
        {
            source.Stop();
        }
    }
    
    void UpdateLayerVolumes()
    {
        // Update individual musical layers based on context
        foreach (MusicalLayer layer in orchestralLayers)
        {
            UpdateLayerVolume(layer);
        }
    }
    
    void UpdateLayerVolume(MusicalLayer layer)
    {
        float targetVolume = layer.isActive ? layer.baseVolume : 0f;
        
        // Modify based on current state
        switch (currentState)
        {
            case MusicalState.Exploration:
                if (layer.type == MusicalLayer.LayerType.Ambient)
                    targetVolume *= 1.5f;
                break;
            case MusicalState.StoryBeat:
                if (layer.type == MusicalLayer.LayerType.Emotional)
                    targetVolume *= 2f;
                break;
            case MusicalState.Hallucination:
                if (layer.type == MusicalLayer.LayerType.Strings)
                    targetVolume *= 0.5f; // Muted strings for unsettling effect
                break;
        }
        
        layer.currentVolume = Mathf.Lerp(layer.currentVolume, targetVolume, Time.deltaTime * emotionalFadeSpeed);
    }
    
    void UpdateSilenceAmbience()
    {
        if (currentState == MusicalState.CompleteSilence || currentState == MusicalState.HeartbeatOnly)
        {
            // Play subtle ambient sounds during silence
            foreach (SilenceAmbience ambience in silenceAmbiences)
            {
                if (Random.Range(0f, ambience.frequency) < Time.deltaTime)
                {
                    PlaySilenceSound(ambience);
                }
            }
        }
    }
    
    void PlaySilenceSound(SilenceAmbience ambience)
    {
        if (ambience.soundClips.Length > 0)
        {
            AudioClip clip = ambience.soundClips[Random.Range(0, ambience.soundClips.Length)];
            AudioSource.PlayClipAtPoint(clip, transform.position, ambience.volume);
        }
    }
    
    public void TriggerEmotionalCue(EmotionalCue.EmotionalTrigger trigger)
    {
        EmotionalCue cue = GetEmotionalCue(trigger);
        if (cue != null)
        {
            StartCoroutine(PlayEmotionalCue(cue));
        }
    }
    
    EmotionalCue GetEmotionalCue(EmotionalCue.EmotionalTrigger trigger)
    {
        foreach (EmotionalCue cue in emotionalCues)
        {
            if (cue.trigger == trigger)
                return cue;
        }
        return null;
    }
    
    IEnumerator PlayEmotionalCue(EmotionalCue cue)
    {
        ShowMusicalMessage($"Emotional cue triggered: {cue.cueName}");
        
        switch (cue.response)
        {
            case EmotionalCue.MusicalResponse.CompleteSilence:
                TransitionToCompleteSilence();
                break;
            case EmotionalCue.MusicalResponse.HeartbeatOnly:
                useHeartbeatOnly = true;
                TransitionToHeartbeatOnly();
                break;
            case EmotionalCue.MusicalResponse.SorrowfulStrings:
                PlaySorrowfulStrings(cue);
                break;
            case EmotionalCue.MusicalResponse.HopefulChant:
                PlayHopefulChant(cue);
                break;
        }
        
        yield return new WaitForSeconds(cue.duration);
        
        // Return to normal state
        if (cue.response == EmotionalCue.MusicalResponse.HeartbeatOnly)
        {
            useHeartbeatOnly = false;
        }
        
        TransitionToExploration();
    }
    
    void PlaySorrowfulStrings(EmotionalCue cue)
    {
        if (cue.specificClip != null)
        {
            emotionalAudioSource.clip = cue.specificClip;
        }
        else if (violinMelodies.Length > 0)
        {
            emotionalAudioSource.clip = violinMelodies[Random.Range(0, violinMelodies.Length)];
        }
        
        emotionalAudioSource.pitch = 0.7f; // Lower, more sorrowful
        emotionalAudioSource.Play();
        StartCoroutine(FadeToVolume(emotionalAudioSource, 0.6f, emotionalFadeSpeed));
    }
    
    void PlayHopefulChant(EmotionalCue cue)
    {
        if (cue.specificClip != null)
        {
            emotionalAudioSource.clip = cue.specificClip;
        }
        else if (sombrerChants.Length > 0)
        {
            emotionalAudioSource.clip = sombrerChants[Random.Range(0, sombrerChants.Length)];
        }
        
        emotionalAudioSource.pitch = 1.1f; // Slightly higher, more hopeful
        emotionalAudioSource.Play();
        StartCoroutine(FadeToVolume(emotionalAudioSource, 0.5f, emotionalFadeSpeed));
    }
    
    // Public methods for external triggers
    public void OnChildKilled()
    {
        TriggerEmotionalCue(EmotionalCue.EmotionalTrigger.ChildKilled);
    }
    
    public void OnBetrayal()
    {
        TriggerEmotionalCue(EmotionalCue.EmotionalTrigger.Betrayal);
    }
    
    public void OnRedemption()
    {
        TriggerEmotionalCue(EmotionalCue.EmotionalTrigger.Redemption);
    }
    
    public void OnMoralChoice()
    {
        currentState = MusicalState.MoralDecision;
        TransitionToMoralDecision();
    }
    
    public void OnStoryBeat()
    {
        currentState = MusicalState.StoryBeat;
        TransitionToStoryBeat();
    }
    
    void ShowMusicalMessage(string message)
    {
        Debug.Log($"Musical System: {message}");
    }
    
    // Getters
    public MusicalState GetCurrentState() => currentState;
    public string GetCurrentRegion() => currentRegion;
    public MusicalTheme GetCurrentTheme() => GetThemeByRegion(currentRegion);
}
