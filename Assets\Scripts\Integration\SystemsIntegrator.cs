using UnityEngine;
using CinderOfDarkness.AI;
using CinderOfDarkness.Input;
using CinderOfDarkness.VFX;
using CinderOfDarkness.UI;
using CinderOfDarkness.Tutorial;

namespace CinderOfDarkness.Integration
{
    /// <summary>
    /// Systems Integrator for Cinder of Darkness.
    /// Coordinates all major systems and ensures proper initialization and communication.
    /// </summary>
    public class SystemsIntegrator : MonoBehaviour
    {
        #region Serialized Fields
        [Header("System References")]
        [SerializeField] private GamepadManager gamepadManager;
        [SerializeField] private VisualEffectsManager vfxManager;
        [SerializeField] private UIManager uiManager;
        [SerializeField] private TutorialManager tutorialManager;
        [SerializeField] private EnhancedButtonPromptSystem buttonPromptSystem;

        [Header("AI System")]
        [SerializeField] private bool enableAISystem = true;
        [SerializeField] private GameObject[] aiEnemyPrefabs;
        [SerializeField] private Transform[] aiSpawnPoints;

        [Header("Integration Settings")]
        [SerializeField] private bool autoInitializeSystems = true;
        [SerializeField] private bool enableSystemCommunication = true;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private float systemUpdateInterval = 0.1f;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logSystemEvents = false;
        #endregion

        #region Public Properties
        public static SystemsIntegrator Instance { get; private set; }
        public bool AllSystemsInitialized { get; private set; }
        public SystemStatus CurrentStatus { get; private set; } = SystemStatus.Initializing;
        #endregion

        #region Private Fields
        private float lastSystemUpdate;
        private PlayerController playerController;
        private PlayerCombat playerCombat;
        private PlayerStats playerStats;
        private NoiseGenerator noiseGenerator;
        private System.Collections.Generic.List<AIStateMachine> activeAI = new System.Collections.Generic.List<AIStateMachine>();
        #endregion

        #region Events
        public System.Action OnAllSystemsInitialized;
        public System.Action<SystemStatus> OnSystemStatusChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Systems Integrator singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (autoInitializeSystems)
                {
                    StartCoroutine(InitializeAllSystems());
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Update system integration.
        /// </summary>
        private void Update()
        {
            if (AllSystemsInitialized && Time.time - lastSystemUpdate >= systemUpdateInterval)
            {
                UpdateSystemIntegration();
                lastSystemUpdate = Time.time;
            }
        }

        /// <summary>
        /// Display debug information.
        /// </summary>
        private void OnGUI()
        {
            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }
        #endregion

        #region System Initialization
        /// <summary>
        /// Initialize all systems in proper order.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeAllSystems()
        {
            SetSystemStatus(SystemStatus.Initializing);
            
            if (logSystemEvents)
            {
                Debug.Log("🔧 Starting system initialization...");
            }

            // Phase 1: Core Input Systems
            yield return StartCoroutine(InitializeInputSystems());

            // Phase 2: Visual Effects Systems
            yield return StartCoroutine(InitializeVFXSystems());

            // Phase 3: UI Systems
            yield return StartCoroutine(InitializeUISystems());

            // Phase 4: AI Systems
            if (enableAISystem)
            {
                yield return StartCoroutine(InitializeAISystems());
            }

            // Phase 5: Tutorial Systems
            yield return StartCoroutine(InitializeTutorialSystems());

            // Phase 6: Player Integration
            yield return StartCoroutine(InitializePlayerIntegration());

            // Phase 7: System Communication
            if (enableSystemCommunication)
            {
                SetupSystemCommunication();
            }

            // Complete initialization
            AllSystemsInitialized = true;
            SetSystemStatus(SystemStatus.Running);
            OnAllSystemsInitialized?.Invoke();

            if (logSystemEvents)
            {
                Debug.Log("✅ All systems initialized successfully!");
            }
        }

        /// <summary>
        /// Initialize input systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeInputSystems()
        {
            if (logSystemEvents) Debug.Log("🎮 Initializing Input Systems...");

            // Initialize Gamepad Manager
            if (gamepadManager == null)
            {
                gamepadManager = GamepadManager.Instance;
            }

            if (gamepadManager == null)
            {
                GameObject gamepadManagerObject = new GameObject("GamepadManager");
                gamepadManager = gamepadManagerObject.AddComponent<GamepadManager>();
            }

            // Initialize Button Prompt System
            if (buttonPromptSystem == null)
            {
                buttonPromptSystem = EnhancedButtonPromptSystem.Instance;
            }

            yield return new WaitForSeconds(0.1f);
            
            if (logSystemEvents) Debug.Log("✅ Input Systems initialized");
        }

        /// <summary>
        /// Initialize VFX systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeVFXSystems()
        {
            if (logSystemEvents) Debug.Log("🎨 Initializing VFX Systems...");

            // Initialize Visual Effects Manager
            if (vfxManager == null)
            {
                vfxManager = VisualEffectsManager.Instance;
            }

            if (vfxManager == null)
            {
                GameObject vfxManagerObject = new GameObject("VisualEffectsManager");
                vfxManager = vfxManagerObject.AddComponent<VisualEffectsManager>();
            }

            yield return new WaitForSeconds(0.1f);
            
            if (logSystemEvents) Debug.Log("✅ VFX Systems initialized");
        }

        /// <summary>
        /// Initialize UI systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeUISystems()
        {
            if (logSystemEvents) Debug.Log("🖥️ Initializing UI Systems...");

            // Initialize UI Manager
            if (uiManager == null)
            {
                uiManager = UIManager.Instance;
            }

            if (uiManager == null)
            {
                GameObject uiManagerObject = new GameObject("UIManager");
                uiManager = uiManagerObject.AddComponent<UIManager>();
            }

            yield return new WaitForSeconds(0.1f);
            
            if (logSystemEvents) Debug.Log("✅ UI Systems initialized");
        }

        /// <summary>
        /// Initialize AI systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeAISystems()
        {
            if (logSystemEvents) Debug.Log("🤖 Initializing AI Systems...");

            // Spawn AI enemies at designated points
            if (aiEnemyPrefabs != null && aiSpawnPoints != null)
            {
                for (int i = 0; i < aiSpawnPoints.Length && i < aiEnemyPrefabs.Length; i++)
                {
                    if (aiSpawnPoints[i] != null && aiEnemyPrefabs[i] != null)
                    {
                        GameObject aiEnemy = Instantiate(aiEnemyPrefabs[i], aiSpawnPoints[i].position, aiSpawnPoints[i].rotation);
                        AIStateMachine aiStateMachine = aiEnemy.GetComponent<AIStateMachine>();
                        
                        if (aiStateMachine != null)
                        {
                            activeAI.Add(aiStateMachine);
                        }

                        // Add sensor system if not present
                        AISensorSystem sensorSystem = aiEnemy.GetComponent<AISensorSystem>();
                        if (sensorSystem == null)
                        {
                            sensorSystem = aiEnemy.AddComponent<AISensorSystem>();
                        }
                    }
                    
                    yield return new WaitForSeconds(0.05f); // Stagger spawning
                }
            }

            if (logSystemEvents) Debug.Log($"✅ AI Systems initialized - {activeAI.Count} AI entities spawned");
        }

        /// <summary>
        /// Initialize tutorial systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializeTutorialSystems()
        {
            if (logSystemEvents) Debug.Log("📚 Initializing Tutorial Systems...");

            // Initialize Tutorial Manager
            if (tutorialManager == null)
            {
                tutorialManager = TutorialManager.Instance;
            }

            if (tutorialManager == null)
            {
                GameObject tutorialManagerObject = new GameObject("TutorialManager");
                tutorialManager = tutorialManagerObject.AddComponent<TutorialManager>();
            }

            yield return new WaitForSeconds(0.1f);
            
            if (logSystemEvents) Debug.Log("✅ Tutorial Systems initialized");
        }

        /// <summary>
        /// Initialize player integration with all systems.
        /// </summary>
        /// <returns>Initialization coroutine</returns>
        private System.Collections.IEnumerator InitializePlayerIntegration()
        {
            if (logSystemEvents) Debug.Log("👤 Initializing Player Integration...");

            // Find player components
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerController = player.GetComponent<PlayerController>();
                playerCombat = player.GetComponent<PlayerCombat>();
                playerStats = player.GetComponent<PlayerStats>();

                // Add noise generator if not present
                noiseGenerator = player.GetComponent<NoiseGenerator>();
                if (noiseGenerator == null)
                {
                    noiseGenerator = player.AddComponent<NoiseGenerator>();
                }

                // Integrate with VFX system
                if (vfxManager != null && playerStats != null)
                {
                    // VFX manager will automatically find and connect to player stats
                }

                // Integrate with gamepad haptic feedback
                if (gamepadManager != null && playerCombat != null)
                {
                    playerCombat.OnAttackHit += (pos, damage, target) =>
                    {
                        gamepadManager.TriggerHapticFeedback(HapticEventType.MediumHit);
                    };
                }
            }

            yield return new WaitForSeconds(0.1f);
            
            if (logSystemEvents) Debug.Log("✅ Player Integration completed");
        }
        #endregion

        #region System Communication
        /// <summary>
        /// Setup communication between all systems.
        /// </summary>
        private void SetupSystemCommunication()
        {
            if (logSystemEvents) Debug.Log("🔗 Setting up system communication...");

            // Connect gamepad events to UI
            if (gamepadManager != null && uiManager != null)
            {
                gamepadManager.OnGamepadConnected += (type) =>
                {
                    if (buttonPromptSystem != null)
                    {
                        // Update button prompts when gamepad connects
                    }
                };
            }

            // Connect combat events to VFX
            if (playerCombat != null && vfxManager != null)
            {
                playerCombat.OnAttackHit += (pos, damage, target) =>
                {
                    vfxManager.CreateBloodSplatter(pos, Vector3.up, damage / 100f);
                };
            }

            // Connect AI events to tutorial hints
            if (tutorialManager != null)
            {
                foreach (var ai in activeAI)
                {
                    if (ai != null)
                    {
                        ai.OnTargetDetected += (target) =>
                        {
                            if (!tutorialManager.IsStepCompleted("combat_detected"))
                            {
                                tutorialManager.ShowHint("Enemy detected you! Prepare for combat!", "LMB", "X");
                            }
                        };
                    }
                }
            }

            // Connect noise generation to AI
            if (noiseGenerator != null)
            {
                // Noise generator automatically broadcasts to AI sensors
            }

            if (logSystemEvents) Debug.Log("✅ System communication established");
        }
        #endregion

        #region System Updates
        /// <summary>
        /// Update system integration and communication.
        /// </summary>
        private void UpdateSystemIntegration()
        {
            if (enablePerformanceMonitoring)
            {
                MonitorSystemPerformance();
            }

            // Update AI-Player interactions
            UpdateAIPlayerInteractions();

            // Update VFX based on game state
            UpdateVFXIntegration();

            // Update tutorial hints based on context
            UpdateTutorialIntegration();
        }

        /// <summary>
        /// Monitor system performance and adjust accordingly.
        /// </summary>
        private void MonitorSystemPerformance()
        {
            float frameRate = 1f / Time.deltaTime;
            
            // Adjust AI update rates based on performance
            if (frameRate < 30f && activeAI.Count > 0)
            {
                // Reduce AI update frequency for performance
                foreach (var ai in activeAI)
                {
                    if (ai != null)
                    {
                        // Could implement LOD system for AI
                    }
                }
            }

            // Adjust VFX quality based on performance
            if (vfxManager != null && frameRate < 45f)
            {
                // Could reduce VFX quality
            }
        }

        /// <summary>
        /// Update AI-Player interactions.
        /// </summary>
        private void UpdateAIPlayerInteractions()
        {
            if (playerTransform == null) return;

            foreach (var ai in activeAI)
            {
                if (ai != null)
                {
                    ai.SetTarget(playerTransform);
                }
            }
        }

        /// <summary>
        /// Update VFX integration with game state.
        /// </summary>
        private void UpdateVFXIntegration()
        {
            if (vfxManager == null) return;

            // Update weather based on game state or time
            // This could be expanded with more complex logic
        }

        /// <summary>
        /// Update tutorial integration with current context.
        /// </summary>
        private void UpdateTutorialIntegration()
        {
            if (tutorialManager == null || tutorialManager.HasCompletedTutorial) return;

            // Show contextual hints based on player state
            if (playerController != null && !playerController.IsMoving())
            {
                // Could show movement hint after period of inactivity
            }
        }
        #endregion

        #region Status Management
        /// <summary>
        /// Set system status and notify listeners.
        /// </summary>
        /// <param name="status">New system status</param>
        private void SetSystemStatus(SystemStatus status)
        {
            if (CurrentStatus != status)
            {
                CurrentStatus = status;
                OnSystemStatusChanged?.Invoke(status);
                
                if (logSystemEvents)
                {
                    Debug.Log($"🔄 System status changed to: {status}");
                }
            }
        }
        #endregion

        #region Debug
        /// <summary>
        /// Display debug information on screen.
        /// </summary>
        private void DisplayDebugInfo()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("=== SYSTEMS STATUS ===");
            GUILayout.Label($"Status: {CurrentStatus}");
            GUILayout.Label($"All Initialized: {AllSystemsInitialized}");
            GUILayout.Label($"Active AI: {activeAI.Count}");
            
            if (gamepadManager != null)
            {
                GUILayout.Label($"Gamepad: {gamepadManager.CurrentGamepadType}");
            }
            
            if (vfxManager != null)
            {
                GUILayout.Label($"Weather: {vfxManager.CurrentWeather}");
            }
            
            if (tutorialManager != null)
            {
                GUILayout.Label($"Tutorial: {(tutorialManager.IsTutorialActive ? "Active" : "Inactive")}");
            }
            
            GUILayout.EndArea();
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get reference to specific system.
        /// </summary>
        /// <typeparam name="T">System type</typeparam>
        /// <returns>System instance</returns>
        public T GetSystem<T>() where T : MonoBehaviour
        {
            if (typeof(T) == typeof(GamepadManager)) return gamepadManager as T;
            if (typeof(T) == typeof(VisualEffectsManager)) return vfxManager as T;
            if (typeof(T) == typeof(UIManager)) return uiManager as T;
            if (typeof(T) == typeof(TutorialManager)) return tutorialManager as T;
            if (typeof(T) == typeof(EnhancedButtonPromptSystem)) return buttonPromptSystem as T;
            
            return null;
        }

        /// <summary>
        /// Force reinitialize all systems.
        /// </summary>
        public void ReinitializeSystems()
        {
            AllSystemsInitialized = false;
            StartCoroutine(InitializeAllSystems());
        }
        #endregion

        #region Cleanup
        /// <summary>
        /// Clean up system integrator.
        /// </summary>
        private void OnDestroy()
        {
            activeAI.Clear();
        }
        #endregion
    }

    #region Enums
    /// <summary>
    /// System status types.
    /// </summary>
    public enum SystemStatus
    {
        Initializing,
        Running,
        Error,
        Paused
    }
    #endregion
}
