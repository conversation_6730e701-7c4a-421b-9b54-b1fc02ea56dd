using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using TMPro;

public class PsychologicalUI : MonoBehaviour
{
    [<PERSON><PERSON>("Mental State Display")]
    public Slider mentalStabilityBar;
    public Slider traumaBar;
    public Slider enlightenmentBar;
    public TextMeshProUGUI psychologicalStateText;
    public Image stateIndicator;
    
    [Header("Meditation UI")]
    public GameObject meditationPanel;
    public TextMeshProUGUI meditationText;
    public Image meditationBackground;
    public ParticleSystem meditationParticles;
    public AudioSource meditationAudio;
    
    [Header("Psychological Effects UI")]
    public GameObject whisperPanel;
    public TextMeshProUGUI whisperText;
    public GameObject hopefulPanel;
    public TextMeshProUGUI hopefulText;
    public GameObject conflictPanel;
    public TextMeshProUGUI conflictText;
    
    [Header("Visual Effects")]
    public Image screenOverlay;
    public Color darkOverlayColor = new Color(0.5f, 0.2f, 0.2f, 0.3f);
    public Color lightOverlayColor = new Color(0.9f, 0.9f, 0.7f, 0.2f);
    public Color neutralOverlayColor = new Color(0.5f, 0.5f, 0.5f, 0.1f);
    
    [Header("State Colors")]
    public Color enlightenedColor = new Color(0.9f, 0.9f, 0.7f);
    public Color balancedColor = new Color(0.7f, 0.7f, 0.7f);
    public Color tormentedColor = new Color(0.8f, 0.6f, 0.4f);
    public Color corruptedColor = new Color(0.8f, 0.3f, 0.3f);
    public Color hollowColor = new Color(0.4f, 0.4f, 0.4f);
    
    [Header("Dialogue Color Modification")]
    public TextMeshProUGUI[] dialogueTexts;
    public Color defaultDialogueColor = Color.white;
    public Color darkDialogueColor = new Color(0.8f, 0.2f, 0.2f);
    public Color lightDialogueColor = new Color(0.9f, 0.9f, 0.7f);
    
    private PsychologicalSystem psycheSystem;
    private bool isMeditating = false;
    private Coroutine currentEffectCoroutine;
    
    void Start()
    {
        psycheSystem = FindObjectOfType<PsychologicalSystem>();
        
        // Initialize UI elements
        if (meditationPanel != null)
            meditationPanel.SetActive(false);
        
        if (whisperPanel != null)
            whisperPanel.SetActive(false);
        
        if (hopefulPanel != null)
            hopefulPanel.SetActive(false);
        
        if (conflictPanel != null)
            conflictPanel.SetActive(false);
    }
    
    void Update()
    {
        if (psycheSystem != null)
        {
            UpdateMentalStateDisplay();
            UpdateScreenOverlay();
            UpdateDialogueColors();
        }
    }
    
    void UpdateMentalStateDisplay()
    {
        // Update bars
        if (mentalStabilityBar != null)
        {
            mentalStabilityBar.value = psycheSystem.GetMentalStability() / 100f;
        }
        
        if (traumaBar != null)
        {
            traumaBar.value = psycheSystem.GetTrauma() / 100f;
        }
        
        if (enlightenmentBar != null)
        {
            enlightenmentBar.value = psycheSystem.enlightenment / 100f;
        }
        
        // Update state text and indicator
        PsychologicalSystem.PsychologicalState currentState = psycheSystem.GetCurrentState();
        
        if (psychologicalStateText != null)
        {
            psychologicalStateText.text = GetStateDisplayText(currentState);
        }
        
        if (stateIndicator != null)
        {
            stateIndicator.color = GetStateColor(currentState);
        }
    }
    
    string GetStateDisplayText(PsychologicalSystem.PsychologicalState state)
    {
        switch (state)
        {
            case PsychologicalSystem.PsychologicalState.Enlightened:
                return "Enlightened - The Cinderborn has found inner peace";
            case PsychologicalSystem.PsychologicalState.Balanced:
                return "Balanced - Walking the middle path";
            case PsychologicalSystem.PsychologicalState.Tormented:
                return "Tormented - Struggling with inner conflict";
            case PsychologicalSystem.PsychologicalState.Corrupted:
                return "Corrupted - Darkness has taken hold";
            case PsychologicalSystem.PsychologicalState.Hollow:
                return "Hollow - Emptied by trauma and loss";
            default:
                return "Unknown State";
        }
    }
    
    Color GetStateColor(PsychologicalSystem.PsychologicalState state)
    {
        switch (state)
        {
            case PsychologicalSystem.PsychologicalState.Enlightened:
                return enlightenedColor;
            case PsychologicalSystem.PsychologicalState.Balanced:
                return balancedColor;
            case PsychologicalSystem.PsychologicalState.Tormented:
                return tormentedColor;
            case PsychologicalSystem.PsychologicalState.Corrupted:
                return corruptedColor;
            case PsychologicalSystem.PsychologicalState.Hollow:
                return hollowColor;
            default:
                return Color.white;
        }
    }
    
    void UpdateScreenOverlay()
    {
        if (screenOverlay == null) return;
        
        Color targetColor = neutralOverlayColor;
        
        switch (psycheSystem.GetCurrentState())
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
            case PsychologicalSystem.PsychologicalState.Hollow:
                targetColor = darkOverlayColor;
                break;
            case PsychologicalSystem.PsychologicalState.Enlightened:
                targetColor = lightOverlayColor;
                break;
            case PsychologicalSystem.PsychologicalState.Tormented:
                // Flickering overlay for tormented state
                float flicker = Mathf.Sin(Time.time * 3f) * 0.1f + 0.2f;
                targetColor = new Color(0.6f, 0.4f, 0.4f, flicker);
                break;
        }
        
        screenOverlay.color = Color.Lerp(screenOverlay.color, targetColor, Time.deltaTime * 0.5f);
    }
    
    void UpdateDialogueColors()
    {
        Color targetColor = defaultDialogueColor;
        
        switch (psycheSystem.GetCurrentState())
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
            case PsychologicalSystem.PsychologicalState.Hollow:
                targetColor = darkDialogueColor;
                break;
            case PsychologicalSystem.PsychologicalState.Enlightened:
                targetColor = lightDialogueColor;
                break;
        }
        
        foreach (TextMeshProUGUI dialogueText in dialogueTexts)
        {
            if (dialogueText != null)
            {
                dialogueText.color = Color.Lerp(dialogueText.color, targetColor, Time.deltaTime * 2f);
            }
        }
    }
    
    public void ShowMeditationUI()
    {
        if (meditationPanel != null)
        {
            meditationPanel.SetActive(true);
            isMeditating = true;
            
            // Start meditation visual effects
            if (meditationParticles != null)
            {
                meditationParticles.Play();
            }
            
            // Fade in meditation background
            if (meditationBackground != null)
            {
                StartCoroutine(FadeInMeditationBackground());
            }
        }
    }
    
    public void HideMeditationUI()
    {
        if (meditationPanel != null)
        {
            StartCoroutine(FadeOutMeditation());
        }
    }
    
    IEnumerator FadeInMeditationBackground()
    {
        float duration = 2f;
        float elapsed = 0f;
        Color startColor = meditationBackground.color;
        startColor.a = 0f;
        Color endColor = startColor;
        endColor.a = 0.8f;
        
        while (elapsed < duration)
        {
            elapsed += Time.unscaledDeltaTime;
            float progress = elapsed / duration;
            meditationBackground.color = Color.Lerp(startColor, endColor, progress);
            yield return null;
        }
    }
    
    IEnumerator FadeOutMeditation()
    {
        float duration = 1f;
        float elapsed = 0f;
        Color startColor = meditationBackground.color;
        Color endColor = startColor;
        endColor.a = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.unscaledDeltaTime;
            float progress = elapsed / duration;
            meditationBackground.color = Color.Lerp(startColor, endColor, progress);
            yield return null;
        }
        
        meditationPanel.SetActive(false);
        isMeditating = false;
        
        if (meditationParticles != null)
        {
            meditationParticles.Stop();
        }
    }
    
    public void ShowMeditationText(string text)
    {
        if (meditationText != null)
        {
            meditationText.text = text;
            StartCoroutine(TypewriterEffect(meditationText, text, 0.05f));
        }
    }
    
    public void ShowWhisperText(string text)
    {
        if (currentEffectCoroutine != null)
        {
            StopCoroutine(currentEffectCoroutine);
        }
        
        currentEffectCoroutine = StartCoroutine(ShowTemporaryText(whisperPanel, whisperText, text, 3f, darkDialogueColor));
    }
    
    public void ShowHopefulText(string text)
    {
        if (currentEffectCoroutine != null)
        {
            StopCoroutine(currentEffectCoroutine);
        }
        
        currentEffectCoroutine = StartCoroutine(ShowTemporaryText(hopefulPanel, hopefulText, text, 4f, lightDialogueColor));
    }
    
    public void ShowConflictedText(string text)
    {
        if (currentEffectCoroutine != null)
        {
            StopCoroutine(currentEffectCoroutine);
        }
        
        currentEffectCoroutine = StartCoroutine(ShowTemporaryText(conflictPanel, conflictText, text, 3.5f, tormentedColor));
    }
    
    IEnumerator ShowTemporaryText(GameObject panel, TextMeshProUGUI textComponent, string text, float duration, Color textColor)
    {
        if (panel != null && textComponent != null)
        {
            panel.SetActive(true);
            textComponent.text = text;
            textComponent.color = textColor;
            
            // Fade in
            yield return StartCoroutine(FadeTextAlpha(textComponent, 0f, 1f, 0.5f));
            
            // Wait
            yield return new WaitForSeconds(duration - 1f);
            
            // Fade out
            yield return StartCoroutine(FadeTextAlpha(textComponent, 1f, 0f, 0.5f));
            
            panel.SetActive(false);
        }
    }
    
    IEnumerator FadeTextAlpha(TextMeshProUGUI textComponent, float startAlpha, float endAlpha, float duration)
    {
        float elapsed = 0f;
        Color color = textComponent.color;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            color.a = Mathf.Lerp(startAlpha, endAlpha, progress);
            textComponent.color = color;
            yield return null;
        }
        
        color.a = endAlpha;
        textComponent.color = color;
    }
    
    IEnumerator TypewriterEffect(TextMeshProUGUI textComponent, string fullText, float delay)
    {
        textComponent.text = "";
        
        for (int i = 0; i < fullText.Length; i++)
        {
            textComponent.text += fullText[i];
            yield return new WaitForSecondsRealtime(delay);
        }
    }
    
    public void TriggerVisionBlur()
    {
        StartCoroutine(VisionBlurEffect());
    }
    
    IEnumerator VisionBlurEffect()
    {
        // Create temporary blur overlay
        GameObject blurOverlay = new GameObject("VisionBlur");
        Canvas canvas = blurOverlay.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        Image blurImage = blurOverlay.AddComponent<Image>();
        blurImage.color = new Color(0f, 0f, 0f, 0f);
        
        // Blur in
        float duration = 0.5f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Sin(elapsed / duration * Mathf.PI) * 0.4f;
            blurImage.color = new Color(0f, 0f, 0f, alpha);
            yield return null;
        }
        
        // Hold blur
        yield return new WaitForSeconds(Random.Range(1f, 3f));
        
        // Blur out
        elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Sin((1f - elapsed / duration) * Mathf.PI) * 0.4f;
            blurImage.color = new Color(0f, 0f, 0f, alpha);
            yield return null;
        }
        
        Destroy(blurOverlay);
    }
    
    public void SetDialogueColor(Color color)
    {
        foreach (TextMeshProUGUI dialogueText in dialogueTexts)
        {
            if (dialogueText != null)
            {
                dialogueText.color = color;
            }
        }
    }
    
    // Public methods for external systems
    public bool IsMeditating() => isMeditating;
    
    public void RegisterDialogueText(TextMeshProUGUI dialogueText)
    {
        if (dialogueText != null)
        {
            System.Array.Resize(ref dialogueTexts, dialogueTexts.Length + 1);
            dialogueTexts[dialogueTexts.Length - 1] = dialogueText;
        }
    }
}
