using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class LegendaryWeapon
{
    [Header("Basic Info")]
    public string weaponName;
    public string culturalName;
    public WeaponType type;
    public WeaponOrigin origin;
    
    [Header("Lore")]
    public string forgeHistory;
    public string previousOwners;
    public string legendaryDeeds;
    public string curseOrBlessing;
    
    [<PERSON><PERSON>("Stats")]
    public float baseDamage;
    public float criticalChance;
    public float attackSpeed;
    public float reach;
    public ElementalType elementalAffinity;
    
    [Header("Special Abilities")]
    public WeaponSkill[] uniqueSkills;
    public PassiveEffect[] passiveEffects;
    
    [Header("Unlock Requirements")]
    public QuestRequirement[] unlockQuests;
    public MoralRequirement moralRequirement;
    public int minimumLevel;
    
    public enum WeaponType
    {
        Sword,
        Greatsword,
        <PERSON><PERSON>,
        <PERSON>im<PERSON>r,
        <PERSON><PERSON>,
        <PERSON>pear,
        <PERSON>xe,
        <PERSON>,
        Bow,
        Crossbow
    }
    
    public enum WeaponOrigin
    {
        Damascus,
        Japanese,
        European,
        Arabic,
        Chinese,
        Norse,
        Celtic,
        Persian,
        Indian,
        Legendary
    }
    
    public enum ElementalType
    {
        None,
        Fire,
        Ice,
        Lightning,
        Shadow,
        Light,
        Poison,
        Blood
    }
}

[System.Serializable]
public class WeaponSkill
{
    public string skillName;
    public string culturalName;
    public string description;
    public float manaCost;
    public float staminaCost;
    public float cooldown;
    public SkillType type;
    public AnimationClip skillAnimation;
    public GameObject effectPrefab;
    public AudioClip skillSound;
    
    public enum SkillType
    {
        Attack,
        Combo,
        Defensive,
        Utility,
        Ultimate
    }
}

[System.Serializable]
public class PassiveEffect
{
    public string effectName;
    public EffectType type;
    public float value;
    public string description;
    
    public enum EffectType
    {
        DamageBonus,
        CriticalBonus,
        SpeedBonus,
        HealthRegen,
        ManaRegen,
        StaminaRegen,
        ElementalResistance,
        StatusImmunity
    }
}

[System.Serializable]
public class QuestRequirement
{
    public string questName;
    public bool mustComplete;
    public string description;
}

[System.Serializable]
public class MoralRequirement
{
    public PlayerStats.MoralPath requiredPath;
    public float minimumAlignment;
    public bool allowNeutral;
}

public class WeaponLoreSystem : MonoBehaviour
{
    [Header("Legendary Weapons Database")]
    public LegendaryWeapon[] legendaryWeapons;
    
    [Header("Current Equipment")]
    public LegendaryWeapon equippedMainHand;
    public LegendaryWeapon equippedOffHand;
    public MagicRing[] equippedRings;
    
    [Header("Discovered Weapons")]
    public List<string> discoveredWeapons = new List<string>();
    public List<string> unlockedWeapons = new List<string>();
    
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private SkillTreeSystem skillTree;
    
    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        playerCombat = GetComponent<PlayerCombat>();
        skillTree = GetComponent<SkillTreeSystem>();
        
        InitializeLegendaryWeapons();
        LoadPlayerProgress();
    }
    
    void InitializeLegendaryWeapons()
    {
        // Damascus Hellblade
        LegendaryWeapon damascusHellblade = new LegendaryWeapon
        {
            weaponName = "Damascus Hellblade",
            culturalName = "Sayf al-Jahannam",
            type = LegendaryWeapon.WeaponType.Sword,
            origin = LegendaryWeapon.WeaponOrigin.Damascus,
            forgeHistory = "Forged in the fires of Mount Qasyoun by the master smith Ibn Firnas, using steel folded a thousand times and quenched in the tears of djinn.",
            previousOwners = "Wielded by Khalid ibn al-Walid, the Sword of Allah, in his conquest of the Sassanid Empire. Later carried by Saladin during the Crusades.",
            legendaryDeeds = "Cut through the armor of a hundred knights at Hattin. Said to have parted the Red Sea when its wielder was in mortal peril.",
            curseOrBlessing = "Blessed by the desert winds, but cursed to thirst for the blood of the innocent. The blade grows heavier with each evil deed.",
            baseDamage = 85f,
            criticalChance = 15f,
            attackSpeed = 1.2f,
            reach = 1.5f,
            elementalAffinity = LegendaryWeapon.ElementalType.Fire
        };
        
        // Guts' Dragonslayer (Hidden Berserk Reference)
        LegendaryWeapon dragonslayer = new LegendaryWeapon
        {
            weaponName = "The Dragonslayer",
            culturalName = "Ryū-goroshi",
            type = LegendaryWeapon.WeaponType.Greatsword,
            origin = LegendaryWeapon.WeaponOrigin.Legendary,
            forgeHistory = "Too big to be called a sword. Massive, thick, heavy, and far too rough. Indeed, it was a heap of raw iron.",
            previousOwners = "Forged by Godot for a king to kill a dragon, but wielded by a man who became more monster than the beasts he slayed.",
            legendaryDeeds = "Slain countless apostles and demons. Each kill adds to its weight in both iron and souls.",
            curseOrBlessing = "Cursed to attract demons and evil spirits, but blessed with the power to cut through any supernatural defense.",
            baseDamage = 150f,
            criticalChance = 5f,
            attackSpeed = 0.6f,
            reach = 2.5f,
            elementalAffinity = LegendaryWeapon.ElementalType.Shadow
        };
        
        // Masamune (Japanese)
        LegendaryWeapon masamune = new LegendaryWeapon
        {
            weaponName = "Masamune",
            culturalName = "正宗",
            type = LegendaryWeapon.WeaponType.Katana,
            origin = LegendaryWeapon.WeaponOrigin.Japanese,
            forgeHistory = "Forged by the legendary swordsmith Gorō Nyūdō Masamune during the Kamakura period, using tamahagane steel and the soul of a benevolent kami.",
            previousOwners = "Carried by countless samurai who died with honor. Each death purified the blade further.",
            legendaryDeeds = "Never drew blood from an innocent. Said to refuse to cut those pure of heart.",
            curseOrBlessing = "Blessed by the kami of justice. Grows stronger when defending the innocent, weaker when used for evil.",
            baseDamage = 75f,
            criticalChance = 25f,
            attackSpeed = 1.8f,
            reach = 1.3f,
            elementalAffinity = LegendaryWeapon.ElementalType.Light
        };
        
        // Add to array (in full implementation, this would be loaded from data files)
        legendaryWeapons = new LegendaryWeapon[] { damascusHellblade, dragonslayer, masamune };
    }
    
    void LoadPlayerProgress()
    {
        // Load discovered and unlocked weapons from save data
        string discoveredData = PlayerPrefs.GetString("DiscoveredWeapons", "");
        if (!string.IsNullOrEmpty(discoveredData))
        {
            discoveredWeapons = new List<string>(discoveredData.Split(','));
        }
        
        string unlockedData = PlayerPrefs.GetString("UnlockedWeapons", "");
        if (!string.IsNullOrEmpty(unlockedData))
        {
            unlockedWeapons = new List<string>(unlockedData.Split(','));
        }
    }
    
    public void DiscoverWeapon(string weaponName)
    {
        if (!discoveredWeapons.Contains(weaponName))
        {
            discoveredWeapons.Add(weaponName);
            SaveProgress();
            
            // Show discovery notification
            ShowWeaponDiscovery(weaponName);
        }
    }
    
    public void UnlockWeapon(string weaponName)
    {
        LegendaryWeapon weapon = GetWeaponByName(weaponName);
        if (weapon != null && CanUnlockWeapon(weapon))
        {
            if (!unlockedWeapons.Contains(weaponName))
            {
                unlockedWeapons.Add(weaponName);
                SaveProgress();
                
                // Show unlock notification
                ShowWeaponUnlock(weapon);
            }
        }
    }
    
    bool CanUnlockWeapon(LegendaryWeapon weapon)
    {
        // Check level requirement
        if (playerStats.GetCurrentLevel() < weapon.minimumLevel)
            return false;
        
        // Check moral requirement
        if (!CheckMoralRequirement(weapon.moralRequirement))
            return false;
        
        // Check quest requirements
        foreach (QuestRequirement quest in weapon.unlockQuests)
        {
            if (quest.mustComplete && !IsQuestCompleted(quest.questName))
                return false;
        }
        
        return true;
    }
    
    bool CheckMoralRequirement(MoralRequirement requirement)
    {
        if (requirement.allowNeutral && playerStats.GetCurrentPath() == PlayerStats.MoralPath.Eclipse)
            return true;
        
        if (requirement.requiredPath != playerStats.GetCurrentPath())
            return false;
        
        float currentAlignment = 0f;
        switch (requirement.requiredPath)
        {
            case PlayerStats.MoralPath.Sun:
                currentAlignment = playerStats.GetSunAlignment();
                break;
            case PlayerStats.MoralPath.Moon:
                currentAlignment = playerStats.GetMoonAlignment();
                break;
        }
        
        return currentAlignment >= requirement.minimumAlignment;
    }
    
    bool IsQuestCompleted(string questName)
    {
        GameManager gameManager = GameManager.Instance;
        return gameManager != null && gameManager.IsQuestCompleted(questName);
    }
    
    public LegendaryWeapon GetWeaponByName(string weaponName)
    {
        foreach (LegendaryWeapon weapon in legendaryWeapons)
        {
            if (weapon.weaponName == weaponName)
                return weapon;
        }
        return null;
    }
    
    public void EquipWeapon(string weaponName, bool isMainHand = true)
    {
        if (!unlockedWeapons.Contains(weaponName))
        {
            Debug.LogWarning($"Weapon {weaponName} is not unlocked!");
            return;
        }
        
        LegendaryWeapon weapon = GetWeaponByName(weaponName);
        if (weapon != null)
        {
            if (isMainHand)
            {
                equippedMainHand = weapon;
            }
            else
            {
                equippedOffHand = weapon;
            }
            
            // Update combat system
            if (playerCombat != null)
            {
                playerCombat.UpdateEquippedWeapon(weapon);
            }
            
            // Apply passive effects
            ApplyWeaponPassives(weapon);
            
            Debug.Log($"Equipped {weapon.weaponName} ({weapon.culturalName})");
        }
    }
    
    void ApplyWeaponPassives(LegendaryWeapon weapon)
    {
        foreach (PassiveEffect effect in weapon.passiveEffects)
        {
            switch (effect.type)
            {
                case PassiveEffect.EffectType.DamageBonus:
                    // Apply damage bonus to combat system
                    break;
                case PassiveEffect.EffectType.CriticalBonus:
                    // Apply critical bonus
                    break;
                case PassiveEffect.EffectType.HealthRegen:
                    // Apply health regeneration
                    break;
                // Add more passive effects
            }
        }
    }
    
    public void UseWeaponSkill(string skillName)
    {
        if (equippedMainHand == null) return;
        
        WeaponSkill skill = GetWeaponSkill(equippedMainHand, skillName);
        if (skill != null && CanUseSkill(skill))
        {
            ExecuteWeaponSkill(skill);
        }
    }
    
    WeaponSkill GetWeaponSkill(LegendaryWeapon weapon, string skillName)
    {
        foreach (WeaponSkill skill in weapon.uniqueSkills)
        {
            if (skill.skillName == skillName)
                return skill;
        }
        return null;
    }
    
    bool CanUseSkill(WeaponSkill skill)
    {
        // Check mana and stamina costs
        if (playerStats.GetCurrentMana() < skill.manaCost)
            return false;
        
        if (playerStats.GetCurrentStamina() < skill.staminaCost)
            return false;
        
        // Check cooldown (would need cooldown tracking system)
        return true;
    }
    
    void ExecuteWeaponSkill(WeaponSkill skill)
    {
        // Consume resources
        playerStats.ConsumeMana(skill.manaCost);
        playerStats.ConsumeStamina(skill.staminaCost);
        
        // Play animation
        Animator animator = GetComponent<Animator>();
        if (animator != null && skill.skillAnimation != null)
        {
            animator.Play(skill.skillAnimation.name);
        }
        
        // Create effect
        if (skill.effectPrefab != null)
        {
            Instantiate(skill.effectPrefab, transform.position, transform.rotation);
        }
        
        // Play sound
        if (skill.skillSound != null)
        {
            AudioSource.PlayClipAtPoint(skill.skillSound, transform.position);
        }
        
        // Execute skill logic based on type
        switch (skill.type)
        {
            case WeaponSkill.SkillType.Attack:
                ExecuteAttackSkill(skill);
                break;
            case WeaponSkill.SkillType.Defensive:
                ExecuteDefensiveSkill(skill);
                break;
            case WeaponSkill.SkillType.Ultimate:
                ExecuteUltimateSkill(skill);
                break;
        }
        
        Debug.Log($"Used weapon skill: {skill.skillName} ({skill.culturalName})");
    }
    
    void ExecuteAttackSkill(WeaponSkill skill)
    {
        // Enhanced attack with weapon-specific effects
        if (playerCombat != null)
        {
            playerCombat.PerformSpecialAttack(skill);
        }
    }
    
    void ExecuteDefensiveSkill(WeaponSkill skill)
    {
        // Defensive abilities like parry, block, or dodge
        // Implementation would depend on combat system
    }
    
    void ExecuteUltimateSkill(WeaponSkill skill)
    {
        // Powerful ultimate abilities
        // These would have dramatic effects and long cooldowns
    }
    
    void ShowWeaponDiscovery(string weaponName)
    {
        LegendaryWeapon weapon = GetWeaponByName(weaponName);
        if (weapon != null)
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                string message = $"Legendary Weapon Discovered: {weapon.weaponName}\n\"{weapon.forgeHistory}\"";
                gameUI.ShowInteractionPrompt(message);
                StartCoroutine(HideMessageAfterDelay(gameUI, 8f));
            }
        }
    }
    
    void ShowWeaponUnlock(LegendaryWeapon weapon)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = $"Weapon Unlocked: {weapon.weaponName} ({weapon.culturalName})\n\"{weapon.legendaryDeeds}\"";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 10f));
        }
    }
    
    System.Collections.IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    void SaveProgress()
    {
        PlayerPrefs.SetString("DiscoveredWeapons", string.Join(",", discoveredWeapons));
        PlayerPrefs.SetString("UnlockedWeapons", string.Join(",", unlockedWeapons));
        PlayerPrefs.Save();
    }
    
    public string GetWeaponLore(string weaponName)
    {
        LegendaryWeapon weapon = GetWeaponByName(weaponName);
        if (weapon != null)
        {
            return $"{weapon.weaponName} ({weapon.culturalName})\n\n" +
                   $"Origin: {weapon.forgeHistory}\n\n" +
                   $"Previous Owners: {weapon.previousOwners}\n\n" +
                   $"Legendary Deeds: {weapon.legendaryDeeds}\n\n" +
                   $"Blessing/Curse: {weapon.curseOrBlessing}";
        }
        return "Unknown weapon.";
    }
    
    // Getters
    public LegendaryWeapon GetEquippedMainHand() => equippedMainHand;
    public LegendaryWeapon GetEquippedOffHand() => equippedOffHand;
    public List<string> GetDiscoveredWeapons() => discoveredWeapons;
    public List<string> GetUnlockedWeapons() => unlockedWeapons;
}

[System.Serializable]
public class MagicRing
{
    public string ringName;
    public string culturalName;
    public RingType type;
    public float magicBonus;
    public float physicalBonus;
    public string specialEffect;
    
    public enum RingType
    {
        Power,      // Increases damage
        Wisdom,     // Increases mana
        Vitality,   // Increases health
        Agility,    // Increases speed
        Balance,    // Balanced bonuses
        Cursed      // Powerful but with drawbacks
    }
}
