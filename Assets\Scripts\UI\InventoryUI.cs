using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Inventory UI for Cinder of Darkness.
    /// Displays items, equipment, and inventory management.
    /// </summary>
    public class InventoryUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject inventoryPanel;
        [SerializeField] private Transform itemsContainer;
        [SerializeField] private GameObject itemSlotPrefab;
        [SerializeField] private Button closeButton;
        [SerializeField] private TextMeshProUGUI weightText;
        [SerializeField] private Slider weightSlider;

        [Header("Item Details")]
        [SerializeField] private GameObject itemDetailsPanel;
        [SerializeField] private TextMeshProUGUI itemNameText;
        [SerializeField] private TextMeshProUGUI itemDescriptionText;
        [SerializeField] private TextMeshProUGUI itemStatsText;
        [SerializeField] private Image itemIcon;
        [SerializeField] private Button useButton;
        [SerializeField] private But<PERSON> dropButton;
        [SerializeField] private Button equipButton;

        [Header("Categories")]
        [SerializeField] private Button allItemsButton;
        [SerializeField] private Button weaponsButton;
        [SerializeField] private Button armorButton;
        [SerializeField] private Button consumablesButton;
        [SerializeField] private Button miscButton;
        [SerializeField] private Image categoryIndicator;

        [Header("Equipment Slots")]
        [SerializeField] private Transform equipmentContainer;
        [SerializeField] private EquipmentSlot[] equipmentSlots;

        [Header("Audio")]
        [SerializeField] private AudioClip openSound;
        [SerializeField] private AudioClip closeSound;
        [SerializeField] private AudioClip itemSelectSound;
        [SerializeField] private AudioClip itemUseSound;
        [SerializeField] private AudioClip equipSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private PlayerInventory playerInventory;
        private List<GameObject> itemSlots = new List<GameObject>();
        private ItemCategory currentCategory = ItemCategory.All;
        private InventoryItem selectedItem;
        private bool isVisible = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
            UpdateWeightDisplay();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            playerInventory = FindObjectOfType<PlayerInventory>();
            if (playerInventory == null)
            {
                // Create a basic inventory system if none exists
                var player = FindObjectOfType<PlayerController>();
                if (player != null)
                {
                    playerInventory = player.gameObject.AddComponent<PlayerInventory>();
                }
            }
        }

        private void SetupUI()
        {
            if (inventoryPanel != null)
                inventoryPanel.SetActive(false);

            if (itemDetailsPanel != null)
                itemDetailsPanel.SetActive(false);

            UpdateCategoryButtons();
            RefreshInventory();
            SetupEquipmentSlots();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseInventory);

            if (useButton != null)
                useButton.onClick.AddListener(UseSelectedItem);

            if (dropButton != null)
                dropButton.onClick.AddListener(DropSelectedItem);

            if (equipButton != null)
                equipButton.onClick.AddListener(EquipSelectedItem);

            // Category buttons
            if (allItemsButton != null)
                allItemsButton.onClick.AddListener(() => SetCategory(ItemCategory.All));

            if (weaponsButton != null)
                weaponsButton.onClick.AddListener(() => SetCategory(ItemCategory.Weapon));

            if (armorButton != null)
                armorButton.onClick.AddListener(() => SetCategory(ItemCategory.Armor));

            if (consumablesButton != null)
                consumablesButton.onClick.AddListener(() => SetCategory(ItemCategory.Consumable));

            if (miscButton != null)
                miscButton.onClick.AddListener(() => SetCategory(ItemCategory.Misc));

            // Subscribe to inventory events
            if (playerInventory != null)
            {
                playerInventory.OnItemAdded += OnItemAdded;
                playerInventory.OnItemRemoved += OnItemRemoved;
                playerInventory.OnItemEquipped += OnItemEquipped;
            }
        }

        private void SetupEquipmentSlots()
        {
            if (equipmentSlots == null) return;

            foreach (var slot in equipmentSlots)
            {
                if (slot.slotButton != null)
                {
                    var slotType = slot.slotType;
                    slot.slotButton.onClick.AddListener(() => OnEquipmentSlotClicked(slotType));
                }
            }
        }
        #endregion

        #region UI Management
        public void ToggleInventory()
        {
            if (isVisible)
                CloseInventory();
            else
                OpenInventory();
        }

        public void OpenInventory()
        {
            if (inventoryPanel != null)
            {
                inventoryPanel.SetActive(true);
                isVisible = true;
                RefreshInventory();
                PlaySound(openSound);
            }
        }

        public void CloseInventory()
        {
            if (inventoryPanel != null)
            {
                inventoryPanel.SetActive(false);
                isVisible = false;
                PlaySound(closeSound);
            }

            if (itemDetailsPanel != null)
                itemDetailsPanel.SetActive(false);
        }

        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.I))
            {
                ToggleInventory();
            }

            if (Input.GetKeyDown(KeyCode.Escape) && isVisible)
            {
                CloseInventory();
            }
        }

        private void SetCategory(ItemCategory category)
        {
            currentCategory = category;
            UpdateCategoryButtons();
            RefreshInventory();
        }

        private void UpdateCategoryButtons()
        {
            // Update button states
            if (allItemsButton != null)
                allItemsButton.interactable = currentCategory != ItemCategory.All;

            if (weaponsButton != null)
                weaponsButton.interactable = currentCategory != ItemCategory.Weapon;

            if (armorButton != null)
                armorButton.interactable = currentCategory != ItemCategory.Armor;

            if (consumablesButton != null)
                consumablesButton.interactable = currentCategory != ItemCategory.Consumable;

            if (miscButton != null)
                miscButton.interactable = currentCategory != ItemCategory.Misc;

            // Update category indicator
            if (categoryIndicator != null)
            {
                switch (currentCategory)
                {
                    case ItemCategory.All:
                        categoryIndicator.color = Color.white;
                        break;
                    case ItemCategory.Weapon:
                        categoryIndicator.color = Color.red;
                        break;
                    case ItemCategory.Armor:
                        categoryIndicator.color = Color.blue;
                        break;
                    case ItemCategory.Consumable:
                        categoryIndicator.color = Color.green;
                        break;
                    case ItemCategory.Misc:
                        categoryIndicator.color = Color.yellow;
                        break;
                }
            }
        }
        #endregion

        #region Inventory Display
        private void RefreshInventory()
        {
            ClearItemSlots();

            if (playerInventory == null) return;

            var items = GetItemsForCategory(currentCategory);
            foreach (var item in items)
            {
                CreateItemSlot(item);
            }

            UpdateEquipmentDisplay();
        }

        private List<InventoryItem> GetItemsForCategory(ItemCategory category)
        {
            if (playerInventory == null) return new List<InventoryItem>();

            var allItems = playerInventory.GetAllItems();

            if (category == ItemCategory.All)
                return allItems;

            return allItems.Where(item => item.category == category).ToList();
        }

        private void CreateItemSlot(InventoryItem item)
        {
            if (itemSlotPrefab == null || itemsContainer == null) return;

            GameObject slot = Instantiate(itemSlotPrefab, itemsContainer);
            var itemSlot = slot.GetComponent<ItemSlotUI>();

            if (itemSlot != null)
            {
                itemSlot.Setup(item, this);
                itemSlots.Add(slot);
            }
        }

        private void ClearItemSlots()
        {
            foreach (var slot in itemSlots)
            {
                if (slot != null)
                    Destroy(slot);
            }
            itemSlots.Clear();
        }

        public void SelectItem(InventoryItem item)
        {
            selectedItem = item;
            UpdateItemDetails();
            PlaySound(itemSelectSound);
        }

        private void UpdateItemDetails()
        {
            if (selectedItem == null)
            {
                if (itemDetailsPanel != null)
                    itemDetailsPanel.SetActive(false);
                return;
            }

            if (itemDetailsPanel != null)
                itemDetailsPanel.SetActive(true);

            if (itemNameText != null)
                itemNameText.text = selectedItem.itemName;

            if (itemDescriptionText != null)
                itemDescriptionText.text = selectedItem.description;

            if (itemStatsText != null)
                itemStatsText.text = GetItemStatsText(selectedItem);

            if (itemIcon != null)
                itemIcon.sprite = selectedItem.icon;

            UpdateActionButtons();
        }

        private void UpdateActionButtons()
        {
            if (selectedItem == null) return;

            if (useButton != null)
                useButton.gameObject.SetActive(selectedItem.category == ItemCategory.Consumable);

            if (equipButton != null)
            {
                bool canEquip = selectedItem.category == ItemCategory.Weapon || 
                               selectedItem.category == ItemCategory.Armor;
                equipButton.gameObject.SetActive(canEquip);
            }

            if (dropButton != null)
                dropButton.gameObject.SetActive(true);
        }

        private string GetItemStatsText(InventoryItem item)
        {
            var stats = new List<string>();

            if (item.damage > 0)
                stats.Add($"Damage: {item.damage}");

            if (item.defense > 0)
                stats.Add($"Defense: {item.defense}");

            if (item.weight > 0)
                stats.Add($"Weight: {item.weight}");

            if (item.value > 0)
                stats.Add($"Value: {item.value}");

            return string.Join("\n", stats);
        }

        private void UpdateEquipmentDisplay()
        {
            if (equipmentSlots == null || playerInventory == null) return;

            foreach (var slot in equipmentSlots)
            {
                var equippedItem = playerInventory.GetEquippedItem(slot.slotType);
                
                if (slot.itemIcon != null)
                {
                    if (equippedItem != null)
                    {
                        slot.itemIcon.sprite = equippedItem.icon;
                        slot.itemIcon.color = Color.white;
                    }
                    else
                    {
                        slot.itemIcon.sprite = null;
                        slot.itemIcon.color = Color.clear;
                    }
                }
            }
        }

        private void UpdateWeightDisplay()
        {
            if (playerInventory == null) return;

            float currentWeight = playerInventory.GetCurrentWeight();
            float maxWeight = playerInventory.GetMaxWeight();

            if (weightText != null)
                weightText.text = $"{currentWeight:F1}/{maxWeight:F1}";

            if (weightSlider != null)
            {
                weightSlider.value = currentWeight / maxWeight;
                
                // Change color based on weight
                var fillImage = weightSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    float ratio = currentWeight / maxWeight;
                    if (ratio > 0.9f)
                        fillImage.color = Color.red;
                    else if (ratio > 0.7f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.green;
                }
            }
        }
        #endregion

        #region Item Actions
        private void UseSelectedItem()
        {
            if (selectedItem == null || playerInventory == null) return;

            playerInventory.UseItem(selectedItem.itemId);
            PlaySound(itemUseSound);
        }

        private void DropSelectedItem()
        {
            if (selectedItem == null || playerInventory == null) return;

            playerInventory.RemoveItem(selectedItem.itemId, 1);
            selectedItem = null;
            UpdateItemDetails();
        }

        private void EquipSelectedItem()
        {
            if (selectedItem == null || playerInventory == null) return;

            playerInventory.EquipItem(selectedItem.itemId);
            PlaySound(equipSound);
        }

        private void OnEquipmentSlotClicked(EquipmentSlotType slotType)
        {
            if (playerInventory == null) return;

            var equippedItem = playerInventory.GetEquippedItem(slotType);
            if (equippedItem != null)
            {
                playerInventory.UnequipItem(slotType);
            }
        }
        #endregion

        #region Event Handlers
        private void OnItemAdded(InventoryItem item)
        {
            if (isVisible)
                RefreshInventory();
        }

        private void OnItemRemoved(string itemId)
        {
            if (isVisible)
                RefreshInventory();
        }

        private void OnItemEquipped(InventoryItem item)
        {
            UpdateEquipmentDisplay();
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class ItemSlotUI : MonoBehaviour
    {
        [SerializeField] private Image itemIcon;
        [SerializeField] private TextMeshProUGUI quantityText;
        [SerializeField] private Button selectButton;

        private InventoryItem item;
        private InventoryUI inventoryUI;

        public void Setup(InventoryItem itemData, InventoryUI inventory)
        {
            item = itemData;
            inventoryUI = inventory;

            if (itemIcon != null)
                itemIcon.sprite = item.icon;

            if (quantityText != null)
                quantityText.text = item.quantity > 1 ? item.quantity.ToString() : "";

            if (selectButton != null)
                selectButton.onClick.AddListener(() => inventoryUI.SelectItem(item));
        }
    }

    [System.Serializable]
    public class EquipmentSlot
    {
        public EquipmentSlotType slotType;
        public Button slotButton;
        public Image itemIcon;
    }

    public enum ItemCategory
    {
        All,
        Weapon,
        Armor,
        Consumable,
        Misc
    }

    public enum EquipmentSlotType
    {
        MainHand,
        OffHand,
        Head,
        Chest,
        Legs,
        Feet,
        Ring,
        Amulet
    }
    #endregion
}
