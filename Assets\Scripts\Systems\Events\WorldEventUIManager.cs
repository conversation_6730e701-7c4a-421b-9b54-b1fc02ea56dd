using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Collections;
using TMPro;
using System.Linq;

/// <summary>
/// World Event UI Manager for immersive event display in Cinder of Darkness
/// Handles comprehensive event UI including active, upcoming, and historical events
/// </summary>
public class WorldEventUIManager : MonoBehaviour
{
    [Header("Main Event Panel")]
    public GameObject worldEventPanel;
    public Button eventPanelToggleButton;
    public CanvasGroup eventPanelCanvasGroup;
    public float panelFadeSpeed = 2f;
    
    [Header("Event Sections")]
    public Transform activeEventsContainer;
    public Transform upcomingEventsContainer;
    public Transform historicalEventsContainer;
    public GameObject eventEntryPrefab;
    
    [Header("Event Type Icons")]
    public EventTypeIconSet eventTypeIcons;
    
    [Header("Event Impact Display")]
    public GameObject eventImpactPanel;
    public TextMeshProUGUI eventImpactTitle;
    public TextMeshProUGUI eventImpactDescription;
    public Image eventImpactIcon;
    public Image eventImpactBackground;
    
    [Header("HUD Notifications")]
    public GameObject hudNotificationPrefab;
    public Transform hudNotificationParent;
    public float notificationDuration = 4f;
    public int maxHudNotifications = 3;
    
    [Header("Flash Briefings")]
    public GameObject flashBriefingPanel;
    public TextMeshProUGUI flashBriefingTitle;
    public TextMeshProUGUI flashBriefingText;
    public Image flashBriefingIcon;
    public Button flashBriefingCloseButton;
    public float briefingAutoCloseTime = 8f;
    
    [Header("Audio")]
    public AudioSource eventAudioSource;
    public EventAudioSet eventAudioSet;
    
    [Header("Localization")]
    public LocalizationManager localizationManager;
    
    // Static instance
    private static WorldEventUIManager instance;
    public static WorldEventUIManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<WorldEventUIManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("WorldEventUIManager");
                    instance = go.AddComponent<WorldEventUIManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // UI state management
    private bool isPanelOpen = false;
    private List<GameObject> activeHudNotifications = new List<GameObject>();
    private Dictionary<string, GameObject> activeEventEntries = new Dictionary<string, GameObject>();
    private Coroutine currentBriefingCoroutine;
    
    /// <summary>
    /// Event type icon configuration
    /// </summary>
    [System.Serializable]
    public class EventTypeIconSet
    {
        public Sprite invasionIcon;
        public Sprite naturalDisasterIcon;
        public Sprite diseaseIcon;
        public Sprite religiousIcon;
        public Sprite politicalIcon;
        public Sprite economicIcon;
        public Sprite supernaturalIcon;
        public Sprite culturalIcon;
        public Sprite defaultIcon;
        
        public Sprite GetIcon(WorldEvent.EventType eventType)
        {
            switch (eventType)
            {
                case WorldEvent.EventType.Invasion:
                    return invasionIcon ?? defaultIcon;
                case WorldEvent.EventType.NaturalDisaster:
                    return naturalDisasterIcon ?? defaultIcon;
                case WorldEvent.EventType.Disease:
                    return diseaseIcon ?? defaultIcon;
                case WorldEvent.EventType.Religious:
                    return religiousIcon ?? defaultIcon;
                case WorldEvent.EventType.Political:
                    return politicalIcon ?? defaultIcon;
                case WorldEvent.EventType.Economic:
                    return economicIcon ?? defaultIcon;
                case WorldEvent.EventType.Supernatural:
                    return supernaturalIcon ?? defaultIcon;
                case WorldEvent.EventType.Cultural:
                    return culturalIcon ?? defaultIcon;
                default:
                    return defaultIcon;
            }
        }
    }
    
    /// <summary>
    /// Event audio configuration
    /// </summary>
    [System.Serializable]
    public class EventAudioSet
    {
        public AudioClip invasionSound;      // War drums, battle horns
        public AudioClip disasterSound;     // Wind howling, thunder
        public AudioClip diseaseSound;      // Eerie silence, coughing
        public AudioClip religiousSound;    // Chanting, bells
        public AudioClip politicalSound;    // Crowd murmurs, tension
        public AudioClip economicSound;     // Market bustle, coins
        public AudioClip supernaturalSound; // Mystical ambience
        public AudioClip culturalSound;     // Festive music, celebration
        public AudioClip eventStartSound;   // General event start
        public AudioClip eventEndSound;     // General event end
        
        public AudioClip GetEventSound(WorldEvent.EventType eventType)
        {
            switch (eventType)
            {
                case WorldEvent.EventType.Invasion:
                    return invasionSound;
                case WorldEvent.EventType.NaturalDisaster:
                    return disasterSound;
                case WorldEvent.EventType.Disease:
                    return diseaseSound;
                case WorldEvent.EventType.Religious:
                    return religiousSound;
                case WorldEvent.EventType.Political:
                    return politicalSound;
                case WorldEvent.EventType.Economic:
                    return economicSound;
                case WorldEvent.EventType.Supernatural:
                    return supernaturalSound;
                case WorldEvent.EventType.Cultural:
                    return culturalSound;
                default:
                    return eventStartSound;
            }
        }
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeEventUI();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        StartCoroutine(UpdateEventDisplay());
    }
    
    void InitializeEventUI()
    {
        // Find required components if not assigned
        if (localizationManager == null)
            localizationManager = FindObjectOfType<LocalizationManager>();
        
        if (eventAudioSource == null)
            eventAudioSource = GetComponent<AudioSource>();
        
        if (eventAudioSource == null)
            eventAudioSource = gameObject.AddComponent<AudioSource>();
        
        Debug.Log("World Event UI Manager initialized");
    }
    
    void SetupUI()
    {
        // Hide panels initially
        if (worldEventPanel != null)
            worldEventPanel.SetActive(false);
        
        if (eventImpactPanel != null)
            eventImpactPanel.SetActive(false);
        
        if (flashBriefingPanel != null)
            flashBriefingPanel.SetActive(false);
        
        // Setup toggle button
        if (eventPanelToggleButton != null)
            eventPanelToggleButton.onClick.AddListener(ToggleEventPanel);
        
        // Setup flash briefing close button
        if (flashBriefingCloseButton != null)
            flashBriefingCloseButton.onClick.AddListener(CloseFlashBriefing);
        
        // Subscribe to event manager events
        SubscribeToEventManager();
    }
    
    void SubscribeToEventManager()
    {
        // Subscribe to event notifications (would be implemented with proper event system)
        // EventManager.OnEventStarted += OnEventStarted;
        // EventManager.OnEventCompleted += OnEventCompleted;
        // EventManager.OnEventEscalated += OnEventEscalated;
    }
    
    /// <summary>
    /// Update event display continuously
    /// </summary>
    IEnumerator UpdateEventDisplay()
    {
        while (true)
        {
            if (isPanelOpen)
            {
                RefreshEventDisplay();
            }
            
            yield return new WaitForSeconds(1f);
        }
    }
    
    /// <summary>
    /// Toggle main event panel
    /// </summary>
    public void ToggleEventPanel()
    {
        isPanelOpen = !isPanelOpen;
        
        if (isPanelOpen)
        {
            OpenEventPanel();
        }
        else
        {
            CloseEventPanel();
        }
    }
    
    /// <summary>
    /// Open event panel with fade animation
    /// </summary>
    void OpenEventPanel()
    {
        if (worldEventPanel != null)
        {
            worldEventPanel.SetActive(true);
            RefreshEventDisplay();
            
            if (eventPanelCanvasGroup != null)
            {
                StartCoroutine(FadeCanvasGroup(eventPanelCanvasGroup, 0f, 1f, panelFadeSpeed));
            }
        }
        
        // Pause game time (optional)
        Time.timeScale = 0f;
    }
    
    /// <summary>
    /// Close event panel with fade animation
    /// </summary>
    void CloseEventPanel()
    {
        if (eventPanelCanvasGroup != null)
        {
            StartCoroutine(FadeCanvasGroup(eventPanelCanvasGroup, 1f, 0f, panelFadeSpeed, () =>
            {
                if (worldEventPanel != null)
                    worldEventPanel.SetActive(false);
            }));
        }
        else if (worldEventPanel != null)
        {
            worldEventPanel.SetActive(false);
        }
        
        // Resume game time
        Time.timeScale = 1f;
    }
    
    /// <summary>
    /// Fade canvas group animation
    /// </summary>
    IEnumerator FadeCanvasGroup(CanvasGroup canvasGroup, float startAlpha, float endAlpha, float speed, System.Action onComplete = null)
    {
        float elapsed = 0f;
        float duration = Mathf.Abs(endAlpha - startAlpha) / speed;
        
        while (elapsed < duration)
        {
            canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, elapsed / duration);
            elapsed += Time.unscaledDeltaTime;
            yield return null;
        }
        
        canvasGroup.alpha = endAlpha;
        onComplete?.Invoke();
    }
    
    /// <summary>
    /// Refresh all event displays
    /// </summary>
    void RefreshEventDisplay()
    {
        RefreshActiveEvents();
        RefreshUpcomingEvents();
        RefreshHistoricalEvents();
        UpdateEventImpactDisplay();
    }
    
    /// <summary>
    /// Refresh active events section
    /// </summary>
    void RefreshActiveEvents()
    {
        if (activeEventsContainer == null) return;
        
        // Clear existing entries
        foreach (Transform child in activeEventsContainer)
        {
            if (child.gameObject != eventEntryPrefab)
                Destroy(child.gameObject);
        }
        
        activeEventEntries.Clear();
        
        // Get active events from EventManager
        var activeEvents = EventManager.GetActiveEvents();
        
        foreach (var activeEvent in activeEvents)
        {
            CreateEventEntry(activeEvent, activeEventsContainer, EventEntryType.Active);
        }
    }
    
    /// <summary>
    /// Refresh upcoming events section
    /// </summary>
    void RefreshUpcomingEvents()
    {
        if (upcomingEventsContainer == null) return;
        
        // Clear existing entries
        foreach (Transform child in upcomingEventsContainer)
        {
            if (child.gameObject != eventEntryPrefab)
                Destroy(child.gameObject);
        }
        
        // Get upcoming events (would be implemented based on event prediction system)
        var upcomingEvents = GetPredictedUpcomingEvents();
        
        foreach (var upcomingEvent in upcomingEvents)
        {
            CreateEventEntry(upcomingEvent, upcomingEventsContainer, EventEntryType.Upcoming);
        }
    }
    
    /// <summary>
    /// Refresh historical events section
    /// </summary>
    void RefreshHistoricalEvents()
    {
        if (historicalEventsContainer == null) return;
        
        // Clear existing entries
        foreach (Transform child in historicalEventsContainer)
        {
            if (child.gameObject != eventEntryPrefab)
                Destroy(child.gameObject);
        }
        
        // Get recent historical events
        var eventHistory = EventManager.GetEventHistory();
        var recentEvents = eventHistory.TakeLast(10).Reverse();
        
        foreach (var historicalEvent in recentEvents)
        {
            CreateHistoricalEventEntry(historicalEvent, historicalEventsContainer);
        }
    }
    
    /// <summary>
    /// Event entry types
    /// </summary>
    public enum EventEntryType
    {
        Active,
        Upcoming,
        Historical
    }
    
    /// <summary>
    /// Create event entry UI element
    /// </summary>
    void CreateEventEntry(EventManager.ActiveEvent activeEvent, Transform parent, EventEntryType entryType)
    {
        if (eventEntryPrefab == null) return;
        
        GameObject entry = Instantiate(eventEntryPrefab, parent);
        
        // Setup event icon
        var iconImage = entry.transform.Find("EventIcon")?.GetComponent<Image>();
        if (iconImage != null && eventTypeIcons != null)
        {
            iconImage.sprite = eventTypeIcons.GetIcon(activeEvent.eventData.eventType);
        }
        
        // Setup event title
        var titleText = entry.transform.Find("EventTitle")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
        {
            titleText.text = GetLocalizedText(activeEvent.eventData.eventName);
        }
        
        // Setup event description
        var descText = entry.transform.Find("EventDescription")?.GetComponent<TextMeshProUGUI>();
        if (descText != null)
        {
            descText.text = GetLocalizedText(activeEvent.eventData.eventDescription);
        }
        
        // Setup progress/time info
        var progressText = entry.transform.Find("EventProgress")?.GetComponent<TextMeshProUGUI>();
        if (progressText != null)
        {
            switch (entryType)
            {
                case EventEntryType.Active:
                    float remainingTime = activeEvent.GetRemainingTime();
                    int days = Mathf.FloorToInt(remainingTime);
                    progressText.text = GetLocalizedText($"event_time_remaining", days.ToString());
                    break;
                    
                case EventEntryType.Upcoming:
                    progressText.text = GetLocalizedText("event_upcoming");
                    break;
            }
        }
        
        // Setup severity indicator
        var severityImage = entry.transform.Find("SeverityIndicator")?.GetComponent<Image>();
        if (severityImage != null)
        {
            severityImage.color = GetSeverityColor(activeEvent.eventData.severity);
        }
        
        // Setup affected areas
        var areasText = entry.transform.Find("AffectedAreas")?.GetComponent<TextMeshProUGUI>();
        if (areasText != null)
        {
            string areas = string.Join(", ", activeEvent.eventData.affectedRealms);
            areasText.text = GetLocalizedText("event_affected_areas") + ": " + areas;
        }
        
        // Store entry reference
        if (entryType == EventEntryType.Active)
        {
            activeEventEntries[activeEvent.eventId] = entry;
        }
    }
    
    /// <summary>
    /// Create historical event entry
    /// </summary>
    void CreateHistoricalEventEntry(EventManager.CompletedEvent completedEvent, Transform parent)
    {
        if (eventEntryPrefab == null) return;
        
        GameObject entry = Instantiate(eventEntryPrefab, parent);
        
        // Setup event title
        var titleText = entry.transform.Find("EventTitle")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
        {
            titleText.text = GetLocalizedText(completedEvent.eventName);
        }
        
        // Setup completion status
        var statusText = entry.transform.Find("EventStatus")?.GetComponent<TextMeshProUGUI>();
        if (statusText != null)
        {
            string statusKey = $"event_status_{completedEvent.finalState.ToString().ToLower()}";
            statusText.text = GetLocalizedText(statusKey);
            statusText.color = GetStatusColor(completedEvent.finalState);
        }
        
        // Setup duration
        var durationText = entry.transform.Find("EventDuration")?.GetComponent<TextMeshProUGUI>();
        if (durationText != null)
        {
            float duration = completedEvent.endTime - completedEvent.startTime;
            int days = Mathf.FloorToInt(duration / 24f);
            durationText.text = GetLocalizedText("event_duration", days.ToString());
        }
        
        // Setup participation indicator
        var participationIcon = entry.transform.Find("ParticipationIcon")?.GetComponent<Image>();
        if (participationIcon != null)
        {
            participationIcon.gameObject.SetActive(completedEvent.playerParticipated);
        }
    }
    
    /// <summary>
    /// Update event impact display
    /// </summary>
    void UpdateEventImpactDisplay()
    {
        var activeEvents = EventManager.GetActiveEvents();
        
        if (activeEvents.Count > 0)
        {
            // Show impact of most severe active event
            var mostSevereEvent = activeEvents.OrderByDescending(e => (int)e.eventData.severity).First();
            ShowEventImpact(mostSevereEvent);
        }
        else
        {
            HideEventImpact();
        }
    }
    
    /// <summary>
    /// Show event impact panel
    /// </summary>
    void ShowEventImpact(EventManager.ActiveEvent activeEvent)
    {
        if (eventImpactPanel == null) return;
        
        eventImpactPanel.SetActive(true);
        
        if (eventImpactTitle != null)
        {
            eventImpactTitle.text = GetLocalizedText(activeEvent.eventData.eventName);
        }
        
        if (eventImpactDescription != null)
        {
            string impactText = GenerateEventImpactDescription(activeEvent);
            eventImpactDescription.text = impactText;
        }
        
        if (eventImpactIcon != null && eventTypeIcons != null)
        {
            eventImpactIcon.sprite = eventTypeIcons.GetIcon(activeEvent.eventData.eventType);
        }
        
        if (eventImpactBackground != null)
        {
            eventImpactBackground.color = GetSeverityColor(activeEvent.eventData.severity);
        }
    }
    
    /// <summary>
    /// Hide event impact panel
    /// </summary>
    void HideEventImpact()
    {
        if (eventImpactPanel != null)
            eventImpactPanel.SetActive(false);
    }
    
    /// <summary>
    /// Generate event impact description
    /// </summary>
    string GenerateEventImpactDescription(EventManager.ActiveEvent activeEvent)
    {
        var eventData = activeEvent.eventData;
        string description = "";
        
        // Add affected areas
        if (eventData.affectedRealms.Length > 0)
        {
            string areas = string.Join(", ", eventData.affectedRealms);
            description += GetLocalizedText("event_impact_areas", areas) + "\n";
        }
        
        // Add specific impacts based on event type
        switch (eventData.eventType)
        {
            case WorldEvent.EventType.Invasion:
                description += GetLocalizedText("event_impact_invasion");
                break;
            case WorldEvent.EventType.NaturalDisaster:
                description += GetLocalizedText("event_impact_disaster");
                break;
            case WorldEvent.EventType.Disease:
                description += GetLocalizedText("event_impact_disease");
                break;
            case WorldEvent.EventType.Religious:
                description += GetLocalizedText("event_impact_religious");
                break;
            case WorldEvent.EventType.Political:
                description += GetLocalizedText("event_impact_political");
                break;
        }
        
        return description;
    }
    
    /// <summary>
    /// Get predicted upcoming events
    /// </summary>
    List<EventManager.ActiveEvent> GetPredictedUpcomingEvents()
    {
        // This would implement event prediction logic
        // For now, return empty list
        return new List<EventManager.ActiveEvent>();
    }
    
    /// <summary>
    /// Get severity color
    /// </summary>
    Color GetSeverityColor(WorldEvent.EventSeverity severity)
    {
        switch (severity)
        {
            case WorldEvent.EventSeverity.Minor:
                return new Color(0.2f, 0.8f, 0.2f, 0.8f); // Green
            case WorldEvent.EventSeverity.Moderate:
                return new Color(0.8f, 0.8f, 0.2f, 0.8f); // Yellow
            case WorldEvent.EventSeverity.Major:
                return new Color(0.8f, 0.4f, 0.2f, 0.8f); // Orange
            case WorldEvent.EventSeverity.Critical:
                return new Color(0.8f, 0.2f, 0.2f, 0.8f); // Red
            default:
                return Color.white;
        }
    }
    
    /// <summary>
    /// Get status color
    /// </summary>
    Color GetStatusColor(EventManager.EventState state)
    {
        switch (state)
        {
            case EventManager.EventState.Completed:
                return Color.green;
            case EventManager.EventState.Failed:
                return Color.red;
            case EventManager.EventState.Cancelled:
                return Color.yellow;
            default:
                return Color.white;
        }
    }
    
    /// <summary>
    /// Get localized text
    /// </summary>
    string GetLocalizedText(string key, params string[] parameters)
    {
        if (localizationManager != null)
        {
            return localizationManager.GetLocalizedText(key, parameters);
        }
        
        // Fallback to key if no localization manager
        return key;
    }
    
    // Public API for event notifications
    public static void ShowEventStarted(EventManager.ActiveEvent activeEvent)
    {
        Instance.OnEventStarted(activeEvent);
    }
    
    public static void ShowEventCompleted(EventManager.CompletedEvent completedEvent)
    {
        Instance.OnEventCompleted(completedEvent);
    }
    
    public static void ShowFlashBriefing(string title, string description, WorldEvent.EventType eventType)
    {
        Instance.DisplayFlashBriefing(title, description, eventType);
    }
    
    /// <summary>
    /// Handle event started notification
    /// </summary>
    void OnEventStarted(EventManager.ActiveEvent activeEvent)
    {
        // Show HUD notification
        ShowHudNotification(activeEvent.eventData.eventName, activeEvent.eventData.eventType, true);
        
        // Play event sound
        PlayEventSound(activeEvent.eventData.eventType, true);
        
        // Refresh display if panel is open
        if (isPanelOpen)
        {
            RefreshEventDisplay();
        }
    }
    
    /// <summary>
    /// Handle event completed notification
    /// </summary>
    void OnEventCompleted(EventManager.CompletedEvent completedEvent)
    {
        // Show HUD notification
        ShowHudNotification(completedEvent.eventName + " " + GetLocalizedText("event_ended"), WorldEvent.EventType.Political, false);
        
        // Play event end sound
        if (eventAudioSource != null && eventAudioSet != null && eventAudioSet.eventEndSound != null)
        {
            eventAudioSource.PlayOneShot(eventAudioSet.eventEndSound);
        }
        
        // Refresh display if panel is open
        if (isPanelOpen)
        {
            RefreshEventDisplay();
        }
    }
    
    /// <summary>
    /// Show HUD notification
    /// </summary>
    void ShowHudNotification(string eventName, WorldEvent.EventType eventType, bool isStart)
    {
        if (hudNotificationPrefab == null || hudNotificationParent == null) return;
        
        // Remove oldest notification if at max capacity
        if (activeHudNotifications.Count >= maxHudNotifications)
        {
            var oldestNotification = activeHudNotifications[0];
            activeHudNotifications.RemoveAt(0);
            Destroy(oldestNotification);
        }
        
        // Create notification
        GameObject notification = Instantiate(hudNotificationPrefab, hudNotificationParent);
        activeHudNotifications.Add(notification);
        
        // Setup notification content
        var iconImage = notification.transform.Find("Icon")?.GetComponent<Image>();
        if (iconImage != null && eventTypeIcons != null)
        {
            iconImage.sprite = eventTypeIcons.GetIcon(eventType);
        }
        
        var titleText = notification.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
        {
            string prefix = isStart ? "⚔️" : "✓";
            titleText.text = $"{prefix} {GetLocalizedText(eventName)}";
        }
        
        var statusText = notification.transform.Find("Status")?.GetComponent<TextMeshProUGUI>();
        if (statusText != null)
        {
            string statusKey = isStart ? "event_has_begun" : "event_has_ended";
            statusText.text = GetLocalizedText(statusKey);
        }
        
        // Auto-remove after duration
        StartCoroutine(RemoveHudNotificationAfterDelay(notification, notificationDuration));
    }
    
    /// <summary>
    /// Remove HUD notification after delay
    /// </summary>
    IEnumerator RemoveHudNotificationAfterDelay(GameObject notification, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (notification != null)
        {
            activeHudNotifications.Remove(notification);
            
            // Fade out animation
            var canvasGroup = notification.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = notification.AddComponent<CanvasGroup>();
            
            yield return StartCoroutine(FadeCanvasGroup(canvasGroup, 1f, 0f, 2f));
            
            Destroy(notification);
        }
    }
    
    /// <summary>
    /// Play event sound
    /// </summary>
    void PlayEventSound(WorldEvent.EventType eventType, bool isStart)
    {
        if (eventAudioSource == null || eventAudioSet == null) return;
        
        AudioClip soundToPlay = isStart ? eventAudioSet.GetEventSound(eventType) : eventAudioSet.eventEndSound;
        
        if (soundToPlay != null)
        {
            eventAudioSource.PlayOneShot(soundToPlay);
        }
    }
    
    /// <summary>
    /// Display flash briefing
    /// </summary>
    void DisplayFlashBriefing(string title, string description, WorldEvent.EventType eventType)
    {
        if (flashBriefingPanel == null) return;
        
        // Stop any existing briefing
        if (currentBriefingCoroutine != null)
        {
            StopCoroutine(currentBriefingCoroutine);
        }
        
        // Setup briefing content
        if (flashBriefingTitle != null)
            flashBriefingTitle.text = GetLocalizedText(title);
        
        if (flashBriefingText != null)
            flashBriefingText.text = GetLocalizedText(description);
        
        if (flashBriefingIcon != null && eventTypeIcons != null)
            flashBriefingIcon.sprite = eventTypeIcons.GetIcon(eventType);
        
        // Show briefing
        flashBriefingPanel.SetActive(true);
        
        // Auto-close after time
        currentBriefingCoroutine = StartCoroutine(AutoCloseFlashBriefing());
        
        // Play briefing sound
        PlayEventSound(eventType, true);
    }
    
    /// <summary>
    /// Auto-close flash briefing
    /// </summary>
    IEnumerator AutoCloseFlashBriefing()
    {
        yield return new WaitForSeconds(briefingAutoCloseTime);
        CloseFlashBriefing();
    }
    
    /// <summary>
    /// Close flash briefing
    /// </summary>
    void CloseFlashBriefing()
    {
        if (flashBriefingPanel != null)
            flashBriefingPanel.SetActive(false);
        
        if (currentBriefingCoroutine != null)
        {
            StopCoroutine(currentBriefingCoroutine);
            currentBriefingCoroutine = null;
        }
    }
}
