using UnityEngine;
using T<PERSON>ro;

namespace CinderOfDarkness.Localization
{
    /// <summary>
    /// Localized Text component for automatic text localization.
    /// Automatically updates text content based on current language.
    /// </summary>
    [RequireComponent(typeof(TextMeshProUGUI))]
    public class LocalizedText : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Localization Settings")]
        [SerializeField] private string localizationKey;
        [SerializeField] private string defaultText;
        [SerializeField] private bool autoRegister = true;
        [SerializeField] private bool useFormatting = false;
        [SerializeField] private object[] formatParameters;

        [Header("RTL Settings")]
        [SerializeField] private bool autoDetectRTL = true;
        [SerializeField] private bool forceRTL = false;
        [SerializeField] private bool forceLTR = false;
        #endregion

        #region Private Fields
        private TextMeshProUGUI textComponent;
        private TMP_FontAsset originalFont;
        private TextAlignmentOptions originalAlignment;
        private bool isRegistered = false;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize component.
        /// </summary>
        private void Awake()
        {
            textComponent = GetComponent<TextMeshProUGUI>();
            
            if (textComponent != null)
            {
                originalFont = textComponent.font;
                originalAlignment = textComponent.alignment;
                
                // Use current text as default if not set
                if (string.IsNullOrEmpty(defaultText))
                {
                    defaultText = textComponent.text;
                }
            }
        }

        /// <summary>
        /// Register with localization manager.
        /// </summary>
        private void Start()
        {
            if (autoRegister && LocalizationManager.Instance != null)
            {
                RegisterWithManager();
            }
        }

        /// <summary>
        /// Unregister when destroyed.
        /// </summary>
        private void OnDestroy()
        {
            UnregisterFromManager();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Set localization key and update text.
        /// </summary>
        /// <param name="key">Localization key</param>
        public void SetLocalizationKey(string key)
        {
            localizationKey = key;
            UpdateText();
        }

        /// <summary>
        /// Set format parameters for formatted text.
        /// </summary>
        /// <param name="parameters">Format parameters</param>
        public void SetFormatParameters(params object[] parameters)
        {
            formatParameters = parameters;
            useFormatting = parameters != null && parameters.Length > 0;
            UpdateText();
        }

        /// <summary>
        /// Update text with current language settings.
        /// </summary>
        public void UpdateText()
        {
            if (LocalizationManager.Instance != null)
            {
                string languageCode = LocalizationManager.Instance.GetLanguageCode(LocalizationManager.Instance.CurrentLanguage);
                bool isRTL = LocalizationManager.Instance.IsRTLLanguage;
                UpdateText(languageCode, isRTL);
            }
        }

        /// <summary>
        /// Update text with specific language and RTL settings.
        /// </summary>
        /// <param name="languageCode">Language code</param>
        /// <param name="isRTL">Whether language is RTL</param>
        public void UpdateText(string languageCode, bool isRTL)
        {
            if (textComponent == null || string.IsNullOrEmpty(localizationKey))
                return;

            // Get localized text
            string localizedText;
            if (useFormatting && formatParameters != null && formatParameters.Length > 0)
            {
                localizedText = LocalizationManager.Instance.GetLocalizedText(localizationKey, formatParameters);
            }
            else
            {
                localizedText = LocalizationManager.Instance.GetLocalizedText(localizationKey, defaultText);
            }

            // Apply text
            textComponent.text = localizedText;

            // Update font and alignment for RTL
            UpdateFontAndAlignment(isRTL);
        }

        /// <summary>
        /// Register with localization manager.
        /// </summary>
        public void RegisterWithManager()
        {
            if (!isRegistered && LocalizationManager.Instance != null)
            {
                LocalizationManager.Instance.RegisterLocalizedText(this);
                isRegistered = true;
            }
        }

        /// <summary>
        /// Unregister from localization manager.
        /// </summary>
        public void UnregisterFromManager()
        {
            if (isRegistered && LocalizationManager.Instance != null)
            {
                LocalizationManager.Instance.UnregisterLocalizedText(this);
                isRegistered = false;
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Update font and text alignment based on RTL settings.
        /// </summary>
        /// <param name="isRTL">Whether current language is RTL</param>
        private void UpdateFontAndAlignment(bool isRTL)
        {
            if (textComponent == null)
                return;

            // Determine if we should use RTL layout
            bool useRTL = DetermineRTLUsage(isRTL);

            // Update font
            UpdateFont(useRTL);

            // Update alignment
            UpdateAlignment(useRTL);
        }

        /// <summary>
        /// Determine whether to use RTL layout.
        /// </summary>
        /// <param name="languageIsRTL">Whether current language is RTL</param>
        /// <returns>True if RTL should be used</returns>
        private bool DetermineRTLUsage(bool languageIsRTL)
        {
            if (forceRTL)
                return true;
            
            if (forceLTR)
                return false;
            
            if (autoDetectRTL)
                return languageIsRTL;
            
            return false;
        }

        /// <summary>
        /// Update font based on RTL usage.
        /// </summary>
        /// <param name="useRTL">Whether to use RTL font</param>
        private void UpdateFont(bool useRTL)
        {
            if (LocalizationManager.Instance != null)
            {
                TMP_FontAsset newFont = LocalizationManager.Instance.GetCurrentFont();
                if (newFont != null)
                {
                    textComponent.font = newFont;
                }
            }
        }

        /// <summary>
        /// Update text alignment based on RTL usage.
        /// </summary>
        /// <param name="useRTL">Whether to use RTL alignment</param>
        private void UpdateAlignment(bool useRTL)
        {
            if (useRTL)
            {
                // Convert LTR alignment to RTL
                switch (originalAlignment)
                {
                    case TextAlignmentOptions.Left:
                    case TextAlignmentOptions.TopLeft:
                    case TextAlignmentOptions.BottomLeft:
                        textComponent.alignment = GetRTLAlignment(originalAlignment);
                        break;
                    case TextAlignmentOptions.Right:
                    case TextAlignmentOptions.TopRight:
                    case TextAlignmentOptions.BottomRight:
                        textComponent.alignment = GetLTRAlignment(originalAlignment);
                        break;
                    default:
                        // Keep center alignments as-is
                        textComponent.alignment = originalAlignment;
                        break;
                }
            }
            else
            {
                // Use original alignment for LTR
                textComponent.alignment = originalAlignment;
            }
        }

        /// <summary>
        /// Get RTL equivalent of LTR alignment.
        /// </summary>
        /// <param name="ltrAlignment">LTR alignment</param>
        /// <returns>RTL alignment</returns>
        private TextAlignmentOptions GetRTLAlignment(TextAlignmentOptions ltrAlignment)
        {
            switch (ltrAlignment)
            {
                case TextAlignmentOptions.Left:
                    return TextAlignmentOptions.Right;
                case TextAlignmentOptions.TopLeft:
                    return TextAlignmentOptions.TopRight;
                case TextAlignmentOptions.BottomLeft:
                    return TextAlignmentOptions.BottomRight;
                default:
                    return ltrAlignment;
            }
        }

        /// <summary>
        /// Get LTR equivalent of RTL alignment.
        /// </summary>
        /// <param name="rtlAlignment">RTL alignment</param>
        /// <returns>LTR alignment</returns>
        private TextAlignmentOptions GetLTRAlignment(TextAlignmentOptions rtlAlignment)
        {
            switch (rtlAlignment)
            {
                case TextAlignmentOptions.Right:
                    return TextAlignmentOptions.Left;
                case TextAlignmentOptions.TopRight:
                    return TextAlignmentOptions.TopLeft;
                case TextAlignmentOptions.BottomRight:
                    return TextAlignmentOptions.BottomLeft;
                default:
                    return rtlAlignment;
            }
        }
        #endregion

        #region Editor Support
#if UNITY_EDITOR
        /// <summary>
        /// Validate component in editor.
        /// </summary>
        private void OnValidate()
        {
            if (textComponent == null)
            {
                textComponent = GetComponent<TextMeshProUGUI>();
            }

            // Update text in editor if localization key is set
            if (!string.IsNullOrEmpty(localizationKey) && Application.isPlaying)
            {
                UpdateText();
            }
        }

        /// <summary>
        /// Context menu for manual text update.
        /// </summary>
        [ContextMenu("Update Localized Text")]
        private void UpdateLocalizedTextContextMenu()
        {
            UpdateText();
        }

        /// <summary>
        /// Context menu for registering with manager.
        /// </summary>
        [ContextMenu("Register with Localization Manager")]
        private void RegisterContextMenu()
        {
            RegisterWithManager();
        }
#endif
        #endregion

        #region Properties
        /// <summary>
        /// Get or set the localization key.
        /// </summary>
        public string LocalizationKey
        {
            get { return localizationKey; }
            set { SetLocalizationKey(value); }
        }

        /// <summary>
        /// Get or set the default text.
        /// </summary>
        public string DefaultText
        {
            get { return defaultText; }
            set 
            { 
                defaultText = value;
                UpdateText();
            }
        }

        /// <summary>
        /// Get whether component is registered with manager.
        /// </summary>
        public bool IsRegistered
        {
            get { return isRegistered; }
        }

        /// <summary>
        /// Get the text component.
        /// </summary>
        public TextMeshProUGUI TextComponent
        {
            get { return textComponent; }
        }
        #endregion
    }
}
