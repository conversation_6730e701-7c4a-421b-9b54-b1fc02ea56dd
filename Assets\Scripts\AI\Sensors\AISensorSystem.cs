using UnityEngine;
using System.Collections.Generic;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// AI Sensor System for detecting player and environmental stimuli.
    /// Handles vision, hearing, and other sensory inputs for AI enemies.
    /// </summary>
    public class AISensorSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Vision Settings")]
        [SerializeField] private float visionRange = 10f;
        [SerializeField] private float visionAngle = 60f;
        [SerializeField] private LayerMask visionObstacleMask = -1;
        [SerializeField] private LayerMask targetMask = 1;
        [SerializeField] private float visionUpdateRate = 0.2f;
        
        [Header("Hearing Settings")]
        [SerializeField] private float hearingRange = 15f;
        [SerializeField] private float hearingSensitivity = 1f;
        [SerializeField] private float noiseDecayRate = 2f;
        
        [Header("Debug")]
        [SerializeField] private bool showVisionCone = true;
        [SerializeField] private bool showHearingRange = true;
        [SerializeField] private Color visionColor = Color.yellow;
        [SerializeField] private Color hearingColor = Color.blue;
        #endregion

        #region Public Properties
        public bool CanSeeTarget { get; private set; }
        public Transform DetectedTarget { get; private set; }
        public Vector3 LastSeenPosition { get; private set; }
        public float LastSeenTime { get; private set; }
        public float NoiseLevel { get; private set; }
        public Vector3 LastNoisePosition { get; private set; }
        public float LastNoiseTime { get; private set; }
        #endregion

        #region Private Fields
        private AIStateMachine aiStateMachine;
        private float lastVisionUpdate;
        private List<Transform> potentialTargets = new List<Transform>();
        private static List<AISensorSystem> allSensors = new List<AISensorSystem>();
        #endregion

        #region Events
        public System.Action<Transform> OnTargetDetected;
        public System.Action OnTargetLost;
        public System.Action<Vector3, float> OnNoiseDetected;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize sensor system.
        /// </summary>
        private void Awake()
        {
            aiStateMachine = GetComponent<AIStateMachine>();
            allSensors.Add(this);
        }

        /// <summary>
        /// Start sensor updates.
        /// </summary>
        private void Start()
        {
            FindPotentialTargets();
        }

        /// <summary>
        /// Update sensors.
        /// </summary>
        private void Update()
        {
            UpdateVision();
            UpdateHearing();
        }

        /// <summary>
        /// Clean up sensor references.
        /// </summary>
        private void OnDestroy()
        {
            allSensors.Remove(this);
        }

        /// <summary>
        /// Draw sensor debug gizmos.
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (showVisionCone)
            {
                DrawVisionCone();
            }

            if (showHearingRange)
            {
                DrawHearingRange();
            }
        }
        #endregion

        #region Vision System
        /// <summary>
        /// Update vision detection.
        /// </summary>
        private void UpdateVision()
        {
            // Update at specified rate for performance
            if (Time.time - lastVisionUpdate < visionUpdateRate) return;
            lastVisionUpdate = Time.time;

            bool previousCanSee = CanSeeTarget;
            Transform previousTarget = DetectedTarget;

            CanSeeTarget = false;
            DetectedTarget = null;

            // Check each potential target
            foreach (Transform target in potentialTargets)
            {
                if (target == null) continue;

                if (IsTargetInVision(target))
                {
                    CanSeeTarget = true;
                    DetectedTarget = target;
                    LastSeenPosition = target.position;
                    LastSeenTime = Time.time;
                    break; // Take first detected target
                }
            }

            // Trigger events
            if (CanSeeTarget && !previousCanSee)
            {
                OnTargetDetected?.Invoke(DetectedTarget);
            }
            else if (!CanSeeTarget && previousCanSee)
            {
                OnTargetLost?.Invoke();
            }
        }

        /// <summary>
        /// Check if target is within vision cone and line of sight.
        /// </summary>
        /// <param name="target">Target to check</param>
        /// <returns>True if target is visible</returns>
        private bool IsTargetInVision(Transform target)
        {
            Vector3 directionToTarget = (target.position - transform.position).normalized;
            float distanceToTarget = Vector3.Distance(transform.position, target.position);

            // Check distance
            if (distanceToTarget > visionRange) return false;

            // Check angle
            float angleToTarget = Vector3.Angle(transform.forward, directionToTarget);
            if (angleToTarget > visionAngle * 0.5f) return false;

            // Check line of sight
            Vector3 rayOrigin = transform.position + Vector3.up * 0.5f;
            Vector3 rayDirection = (target.position + Vector3.up * 1f - rayOrigin).normalized;

            if (Physics.Raycast(rayOrigin, rayDirection, out RaycastHit hit, distanceToTarget, visionObstacleMask))
            {
                // Check if we hit the target or something else
                return hit.transform == target || hit.transform.IsChildOf(target);
            }

            return true; // No obstacles in the way
        }

        /// <summary>
        /// Find all potential targets in the scene.
        /// </summary>
        private void FindPotentialTargets()
        {
            potentialTargets.Clear();

            // Find all objects with target mask
            Collider[] targets = Physics.OverlapSphere(transform.position, visionRange * 2f, targetMask);
            foreach (Collider target in targets)
            {
                if (target.transform != transform) // Don't detect self
                {
                    potentialTargets.Add(target.transform);
                }
            }

            // Also add player specifically
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null && !potentialTargets.Contains(player.transform))
            {
                potentialTargets.Add(player.transform);
            }
        }
        #endregion

        #region Hearing System
        /// <summary>
        /// Update hearing detection.
        /// </summary>
        private void UpdateHearing()
        {
            // Decay noise level over time
            if (NoiseLevel > 0)
            {
                NoiseLevel = Mathf.Max(0, NoiseLevel - noiseDecayRate * Time.deltaTime);
            }
        }

        /// <summary>
        /// Process noise at a specific position.
        /// </summary>
        /// <param name="noisePosition">Position where noise occurred</param>
        /// <param name="noiseIntensity">Intensity of the noise (0-1)</param>
        public void ProcessNoise(Vector3 noisePosition, float noiseIntensity)
        {
            float distanceToNoise = Vector3.Distance(transform.position, noisePosition);
            
            // Calculate effective noise level based on distance and sensitivity
            float effectiveRange = hearingRange * hearingSensitivity;
            if (distanceToNoise <= effectiveRange)
            {
                float noiseLevel = noiseIntensity * (1f - (distanceToNoise / effectiveRange));
                
                if (noiseLevel > NoiseLevel)
                {
                    NoiseLevel = noiseLevel;
                    LastNoisePosition = noisePosition;
                    LastNoiseTime = Time.time;
                    
                    OnNoiseDetected?.Invoke(noisePosition, noiseLevel);
                    
                    // Notify AI state machine
                    if (aiStateMachine != null)
                    {
                        aiStateMachine.OnNoiseDetected(noisePosition, noiseLevel);
                    }
                }
            }
        }
        #endregion

        #region Static Noise Broadcasting
        /// <summary>
        /// Broadcast noise to all AI sensors in range.
        /// </summary>
        /// <param name="noisePosition">Position where noise occurred</param>
        /// <param name="noiseIntensity">Intensity of the noise (0-1)</param>
        /// <param name="maxRange">Maximum range for noise propagation</param>
        public static void BroadcastNoise(Vector3 noisePosition, float noiseIntensity, float maxRange = 50f)
        {
            foreach (AISensorSystem sensor in allSensors)
            {
                if (sensor != null)
                {
                    float distance = Vector3.Distance(sensor.transform.position, noisePosition);
                    if (distance <= maxRange)
                    {
                        sensor.ProcessNoise(noisePosition, noiseIntensity);
                    }
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set vision parameters.
        /// </summary>
        /// <param name="range">Vision range</param>
        /// <param name="angle">Vision angle in degrees</param>
        public void SetVisionParameters(float range, float angle)
        {
            visionRange = range;
            visionAngle = angle;
        }

        /// <summary>
        /// Set hearing parameters.
        /// </summary>
        /// <param name="range">Hearing range</param>
        /// <param name="sensitivity">Hearing sensitivity</param>
        public void SetHearingParameters(float range, float sensitivity)
        {
            hearingRange = range;
            hearingSensitivity = sensitivity;
        }

        /// <summary>
        /// Check if position was heard recently.
        /// </summary>
        /// <param name="timeThreshold">Time threshold in seconds</param>
        /// <returns>True if noise was heard recently</returns>
        public bool WasNoiseHeardRecently(float timeThreshold = 5f)
        {
            return Time.time - LastNoiseTime <= timeThreshold;
        }

        /// <summary>
        /// Check if target was seen recently.
        /// </summary>
        /// <param name="timeThreshold">Time threshold in seconds</param>
        /// <returns>True if target was seen recently</returns>
        public bool WasTargetSeenRecently(float timeThreshold = 5f)
        {
            return Time.time - LastSeenTime <= timeThreshold;
        }

        /// <summary>
        /// Force refresh of potential targets.
        /// </summary>
        public void RefreshTargets()
        {
            FindPotentialTargets();
        }
        #endregion

        #region Debug Visualization
        /// <summary>
        /// Draw vision cone for debugging.
        /// </summary>
        private void DrawVisionCone()
        {
            Gizmos.color = CanSeeTarget ? Color.red : visionColor;
            
            // Draw vision range
            Gizmos.DrawWireSphere(transform.position, visionRange);
            
            // Draw vision cone
            Vector3 leftBoundary = Quaternion.Euler(0, -visionAngle * 0.5f, 0) * transform.forward * visionRange;
            Vector3 rightBoundary = Quaternion.Euler(0, visionAngle * 0.5f, 0) * transform.forward * visionRange;
            
            Gizmos.DrawLine(transform.position, transform.position + leftBoundary);
            Gizmos.DrawLine(transform.position, transform.position + rightBoundary);
            
            // Draw arc
            Vector3 previousPoint = transform.position + leftBoundary;
            for (int i = 1; i <= 20; i++)
            {
                float angle = Mathf.Lerp(-visionAngle * 0.5f, visionAngle * 0.5f, i / 20f);
                Vector3 point = transform.position + Quaternion.Euler(0, angle, 0) * transform.forward * visionRange;
                Gizmos.DrawLine(previousPoint, point);
                previousPoint = point;
            }
            
            // Draw line to detected target
            if (CanSeeTarget && DetectedTarget != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, DetectedTarget.position);
            }
        }

        /// <summary>
        /// Draw hearing range for debugging.
        /// </summary>
        private void DrawHearingRange()
        {
            Gizmos.color = hearingColor;
            Gizmos.DrawWireSphere(transform.position, hearingRange);
            
            // Draw line to last noise position
            if (NoiseLevel > 0 && LastNoisePosition != Vector3.zero)
            {
                Gizmos.color = Color.Lerp(hearingColor, Color.red, NoiseLevel);
                Gizmos.DrawLine(transform.position, LastNoisePosition);
                Gizmos.DrawWireSphere(LastNoisePosition, 0.5f);
            }
        }
        #endregion
    }
}
