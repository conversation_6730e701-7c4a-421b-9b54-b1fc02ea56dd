using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using CinderOfDarkness.Input;
using CinderOfDarkness.UI;

namespace CinderOfDarkness.Tutorial
{
    /// <summary>
    /// Comprehensive Tutorial Manager for Cinder of Darkness.
    /// Provides interactive tutorials, dynamic hints, and replay functionality.
    /// </summary>
    public class TutorialManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Tutorial Settings")]
        [SerializeField] private bool enableTutorials = true;
        [SerializeField] private bool showTutorialOnFirstPlay = true;
        [SerializeField] private bool allowSkipping = true;
        [SerializeField] private float hintDisplayDuration = 5f;
        [SerializeField] private float hintFadeDelay = 1f;

        [Header("Tutorial UI")]
        [SerializeField] private Canvas tutorialCanvas;
        [SerializeField] private GameObject tutorialPanel;
        [SerializeField] private TextMeshProUGUI tutorialTitle;
        [SerializeField] private TextMeshProUGUI tutorialDescription;
        [SerializeField] private Image tutorialImage;
        [SerializeField] private Button nextButton;
        [SerializeField] private Button previousButton;
        [SerializeField] private Button skipButton;
        [SerializeField] private Slider progressSlider;

        [Header("Hint System")]
        [SerializeField] private GameObject hintPanel;
        [SerializeField] private TextMeshProUGUI hintText;
        [SerializeField] private Image hintIcon;
        [SerializeField] private CanvasGroup hintCanvasGroup;

        [Header("Tutorial Steps")]
        [SerializeField] private TutorialStep[] tutorialSteps;

        [Header("Audio")]
        [SerializeField] private AudioSource tutorialAudioSource;
        [SerializeField] private AudioClip tutorialStartSound;
        [SerializeField] private AudioClip tutorialCompleteSound;
        [SerializeField] private AudioClip stepCompleteSound;
        #endregion

        #region Public Properties
        public static TutorialManager Instance { get; private set; }
        public bool IsTutorialActive { get; private set; }
        public bool HasCompletedTutorial { get; private set; }
        public int CurrentStepIndex { get; private set; }
        public TutorialStep CurrentStep => CurrentStepIndex >= 0 && CurrentStepIndex < tutorialSteps.Length ? 
                                          tutorialSteps[CurrentStepIndex] : null;
        #endregion

        #region Private Fields
        private GamepadManager gamepadManager;
        private EnhancedButtonPromptSystem buttonPromptSystem;
        private PlayerController playerController;
        private PlayerCombat playerCombat;
        private Dictionary<string, bool> completedSteps = new Dictionary<string, bool>();
        private Queue<TutorialHint> pendingHints = new Queue<TutorialHint>();
        private bool isShowingHint = false;
        private System.Coroutine currentHintCoroutine;
        #endregion

        #region Events
        public System.Action OnTutorialStarted;
        public System.Action OnTutorialCompleted;
        public System.Action<TutorialStep> OnStepStarted;
        public System.Action<TutorialStep> OnStepCompleted;
        public System.Action<TutorialHint> OnHintShown;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Tutorial Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeTutorialManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Start tutorial systems.
        /// </summary>
        private void Start()
        {
            InitializeComponents();
            LoadTutorialProgress();
            
            if (showTutorialOnFirstPlay && !HasCompletedTutorial)
            {
                StartTutorial();
            }
        }

        /// <summary>
        /// Update tutorial system.
        /// </summary>
        private void Update()
        {
            if (IsTutorialActive)
            {
                UpdateTutorialInput();
                UpdateCurrentStep();
            }

            ProcessPendingHints();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize tutorial manager systems.
        /// </summary>
        private void InitializeTutorialManager()
        {
            // Ensure tutorial canvas is set up
            if (tutorialCanvas == null)
            {
                tutorialCanvas = GetComponentInChildren<Canvas>();
            }

            if (tutorialCanvas != null)
            {
                tutorialCanvas.sortingOrder = 1000; // Ensure tutorial appears on top
            }

            // Initialize tutorial steps
            for (int i = 0; i < tutorialSteps.Length; i++)
            {
                if (tutorialSteps[i] != null)
                {
                    tutorialSteps[i].Initialize(i);
                }
            }
        }

        /// <summary>
        /// Initialize component references.
        /// </summary>
        private void InitializeComponents()
        {
            gamepadManager = GamepadManager.Instance;
            buttonPromptSystem = EnhancedButtonPromptSystem.Instance;

            // Find player components
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerController = player.GetComponent<PlayerController>();
                playerCombat = player.GetComponent<PlayerCombat>();
            }

            // Setup UI event listeners
            if (nextButton != null)
                nextButton.onClick.AddListener(NextStep);

            if (previousButton != null)
                previousButton.onClick.AddListener(PreviousStep);

            if (skipButton != null)
                skipButton.onClick.AddListener(SkipTutorial);

            // Setup audio
            if (tutorialAudioSource == null)
                tutorialAudioSource = gameObject.AddComponent<AudioSource>();
        }

        /// <summary>
        /// Load tutorial progress from PlayerPrefs.
        /// </summary>
        private void LoadTutorialProgress()
        {
            HasCompletedTutorial = PlayerPrefs.GetInt("TutorialCompleted", 0) == 1;
            
            // Load completed steps
            for (int i = 0; i < tutorialSteps.Length; i++)
            {
                if (tutorialSteps[i] != null)
                {
                    string stepKey = $"TutorialStep_{tutorialSteps[i].StepId}";
                    completedSteps[tutorialSteps[i].StepId] = PlayerPrefs.GetInt(stepKey, 0) == 1;
                }
            }
        }

        /// <summary>
        /// Save tutorial progress to PlayerPrefs.
        /// </summary>
        private void SaveTutorialProgress()
        {
            PlayerPrefs.SetInt("TutorialCompleted", HasCompletedTutorial ? 1 : 0);
            
            foreach (var kvp in completedSteps)
            {
                string stepKey = $"TutorialStep_{kvp.Key}";
                PlayerPrefs.SetInt(stepKey, kvp.Value ? 1 : 0);
            }
            
            PlayerPrefs.Save();
        }
        #endregion

        #region Tutorial Control
        /// <summary>
        /// Start the tutorial from the beginning.
        /// </summary>
        public void StartTutorial()
        {
            if (!enableTutorials || IsTutorialActive) return;

            IsTutorialActive = true;
            CurrentStepIndex = 0;
            
            // Pause the game
            Time.timeScale = 0f;
            
            // Show tutorial UI
            if (tutorialPanel != null)
                tutorialPanel.SetActive(true);

            // Play start sound
            PlayTutorialSound(tutorialStartSound);

            // Start first step
            StartCurrentStep();

            OnTutorialStarted?.Invoke();
        }

        /// <summary>
        /// Complete the tutorial.
        /// </summary>
        public void CompleteTutorial()
        {
            if (!IsTutorialActive) return;

            IsTutorialActive = false;
            HasCompletedTutorial = true;
            
            // Resume the game
            Time.timeScale = 1f;
            
            // Hide tutorial UI
            if (tutorialPanel != null)
                tutorialPanel.SetActive(false);

            // Play completion sound
            PlayTutorialSound(tutorialCompleteSound);

            // Save progress
            SaveTutorialProgress();

            OnTutorialCompleted?.Invoke();
        }

        /// <summary>
        /// Skip the tutorial.
        /// </summary>
        public void SkipTutorial()
        {
            if (!allowSkipping || !IsTutorialActive) return;

            // Mark all steps as completed
            foreach (var step in tutorialSteps)
            {
                if (step != null)
                {
                    completedSteps[step.StepId] = true;
                }
            }

            CompleteTutorial();
        }

        /// <summary>
        /// Restart the tutorial from the beginning.
        /// </summary>
        public void RestartTutorial()
        {
            // Reset progress
            HasCompletedTutorial = false;
            completedSteps.Clear();
            
            // Clear saved progress
            PlayerPrefs.DeleteKey("TutorialCompleted");
            for (int i = 0; i < tutorialSteps.Length; i++)
            {
                if (tutorialSteps[i] != null)
                {
                    string stepKey = $"TutorialStep_{tutorialSteps[i].StepId}";
                    PlayerPrefs.DeleteKey(stepKey);
                }
            }
            
            // Start tutorial
            StartTutorial();
        }
        #endregion

        #region Step Management
        /// <summary>
        /// Move to the next tutorial step.
        /// </summary>
        public void NextStep()
        {
            if (!IsTutorialActive) return;

            // Complete current step
            if (CurrentStep != null)
            {
                CompleteCurrentStep();
            }

            // Move to next step
            CurrentStepIndex++;

            if (CurrentStepIndex >= tutorialSteps.Length)
            {
                CompleteTutorial();
            }
            else
            {
                StartCurrentStep();
            }
        }

        /// <summary>
        /// Move to the previous tutorial step.
        /// </summary>
        public void PreviousStep()
        {
            if (!IsTutorialActive || CurrentStepIndex <= 0) return;

            CurrentStepIndex--;
            StartCurrentStep();
        }

        /// <summary>
        /// Start the current tutorial step.
        /// </summary>
        private void StartCurrentStep()
        {
            if (CurrentStep == null) return;

            // Update UI
            UpdateTutorialUI();
            
            // Start step logic
            CurrentStep.StartStep();
            
            // Show step-specific button prompts
            ShowStepButtonPrompts();

            OnStepStarted?.Invoke(CurrentStep);
        }

        /// <summary>
        /// Complete the current tutorial step.
        /// </summary>
        private void CompleteCurrentStep()
        {
            if (CurrentStep == null) return;

            // Mark step as completed
            completedSteps[CurrentStep.StepId] = true;
            
            // Complete step logic
            CurrentStep.CompleteStep();
            
            // Play step complete sound
            PlayTutorialSound(stepCompleteSound);

            OnStepCompleted?.Invoke(CurrentStep);
        }

        /// <summary>
        /// Update the current tutorial step.
        /// </summary>
        private void UpdateCurrentStep()
        {
            if (CurrentStep == null) return;

            // Check if step completion conditions are met
            if (CurrentStep.IsCompleted())
            {
                NextStep();
            }
            else
            {
                CurrentStep.UpdateStep();
            }
        }

        /// <summary>
        /// Update tutorial UI elements.
        /// </summary>
        private void UpdateTutorialUI()
        {
            if (CurrentStep == null) return;

            // Update text elements
            if (tutorialTitle != null)
                tutorialTitle.text = CurrentStep.Title;

            if (tutorialDescription != null)
                tutorialDescription.text = GetLocalizedDescription();

            // Update image
            if (tutorialImage != null && CurrentStep.StepImage != null)
                tutorialImage.sprite = CurrentStep.StepImage;

            // Update progress
            if (progressSlider != null)
                progressSlider.value = (float)CurrentStepIndex / tutorialSteps.Length;

            // Update button states
            if (previousButton != null)
                previousButton.interactable = CurrentStepIndex > 0;

            if (skipButton != null)
                skipButton.gameObject.SetActive(allowSkipping);
        }

        /// <summary>
        /// Get localized description based on input device.
        /// </summary>
        /// <returns>Localized description text</returns>
        private string GetLocalizedDescription()
        {
            if (CurrentStep == null) return "";

            bool isGamepad = gamepadManager?.IsGamepadConnected ?? false;
            return isGamepad ? CurrentStep.GamepadDescription : CurrentStep.KeyboardDescription;
        }

        /// <summary>
        /// Show button prompts for current step.
        /// </summary>
        private void ShowStepButtonPrompts()
        {
            if (buttonPromptSystem == null || CurrentStep == null) return;

            // Clear existing prompts
            buttonPromptSystem.HideAllPrompts();

            // Show step-specific prompts
            foreach (var prompt in CurrentStep.ButtonPrompts)
            {
                buttonPromptSystem.ShowPrompt(
                    prompt.PromptId,
                    prompt.Action,
                    prompt.KeyboardKey,
                    prompt.GamepadButton,
                    -1f,
                    prompt.Priority
                );
            }

            // Show navigation prompts
            buttonPromptSystem.ShowPrompt("next", "Next", "Enter", "A", -1f, 5);
            
            if (CurrentStepIndex > 0)
            {
                buttonPromptSystem.ShowPrompt("previous", "Previous", "Backspace", "B", -1f, 3);
            }

            if (allowSkipping)
            {
                buttonPromptSystem.ShowPrompt("skip", "Skip Tutorial", "Escape", "Menu", -1f, 1);
            }
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Update tutorial input handling.
        /// </summary>
        private void UpdateTutorialInput()
        {
            // Handle keyboard input
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.Space))
            {
                NextStep();
            }
            else if (Input.GetKeyDown(KeyCode.Backspace))
            {
                PreviousStep();
            }
            else if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (allowSkipping)
                {
                    SkipTutorial();
                }
            }

            // Handle gamepad input
            if (gamepadManager?.CurrentGamepad != null)
            {
                if (gamepadManager.CurrentGamepad.buttonSouth.wasPressedThisFrame) // A button
                {
                    NextStep();
                }
                else if (gamepadManager.CurrentGamepad.buttonEast.wasPressedThisFrame) // B button
                {
                    PreviousStep();
                }
                else if (gamepadManager.CurrentGamepad.startButton.wasPressedThisFrame) // Menu button
                {
                    if (allowSkipping)
                    {
                        SkipTutorial();
                    }
                }
            }
        }
        #endregion

        #region Hint System
        /// <summary>
        /// Show a tutorial hint.
        /// </summary>
        /// <param name="hint">Hint to show</param>
        public void ShowHint(TutorialHint hint)
        {
            if (!enableTutorials || hint == null) return;

            pendingHints.Enqueue(hint);
        }

        /// <summary>
        /// Show a tutorial hint with text.
        /// </summary>
        /// <param name="hintText">Hint text</param>
        /// <param name="keyboardKey">Keyboard key</param>
        /// <param name="gamepadButton">Gamepad button</param>
        /// <param name="icon">Hint icon</param>
        public void ShowHint(string hintText, string keyboardKey = "", string gamepadButton = "", Sprite icon = null)
        {
            TutorialHint hint = new TutorialHint
            {
                HintText = hintText,
                KeyboardKey = keyboardKey,
                GamepadButton = gamepadButton,
                Icon = icon,
                Duration = hintDisplayDuration
            };

            ShowHint(hint);
        }

        /// <summary>
        /// Process pending hints.
        /// </summary>
        private void ProcessPendingHints()
        {
            if (isShowingHint || pendingHints.Count == 0) return;

            TutorialHint hint = pendingHints.Dequeue();
            if (currentHintCoroutine != null)
            {
                StopCoroutine(currentHintCoroutine);
            }

            currentHintCoroutine = StartCoroutine(ShowHintCoroutine(hint));
        }

        /// <summary>
        /// Show hint coroutine with animation.
        /// </summary>
        /// <param name="hint">Hint to show</param>
        /// <returns>Hint coroutine</returns>
        private System.Collections.IEnumerator ShowHintCoroutine(TutorialHint hint)
        {
            isShowingHint = true;

            // Update hint UI
            if (hintText != null)
            {
                bool isGamepad = gamepadManager?.IsGamepadConnected ?? false;
                string buttonText = isGamepad ? hint.GamepadButton : hint.KeyboardKey;
                
                if (!string.IsNullOrEmpty(buttonText))
                {
                    hintText.text = $"{hint.HintText} [{buttonText}]";
                }
                else
                {
                    hintText.text = hint.HintText;
                }
            }

            if (hintIcon != null && hint.Icon != null)
            {
                hintIcon.sprite = hint.Icon;
                hintIcon.gameObject.SetActive(true);
            }
            else if (hintIcon != null)
            {
                hintIcon.gameObject.SetActive(false);
            }

            // Show hint panel
            if (hintPanel != null)
                hintPanel.SetActive(true);

            // Fade in
            if (hintCanvasGroup != null)
            {
                yield return StartCoroutine(FadeHint(0f, 1f, 0.3f));
            }

            // Wait for duration
            yield return new WaitForSecondsRealtime(hint.Duration);

            // Fade out
            if (hintCanvasGroup != null)
            {
                yield return StartCoroutine(FadeHint(1f, 0f, 0.3f));
            }

            // Hide hint panel
            if (hintPanel != null)
                hintPanel.SetActive(false);

            OnHintShown?.Invoke(hint);
            isShowingHint = false;
        }

        /// <summary>
        /// Fade hint in or out.
        /// </summary>
        /// <param name="startAlpha">Start alpha</param>
        /// <param name="endAlpha">End alpha</param>
        /// <param name="duration">Fade duration</param>
        /// <returns>Fade coroutine</returns>
        private System.Collections.IEnumerator FadeHint(float startAlpha, float endAlpha, float duration)
        {
            float elapsedTime = 0f;

            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(startAlpha, endAlpha, elapsedTime / duration);
                
                if (hintCanvasGroup != null)
                {
                    hintCanvasGroup.alpha = alpha;
                }

                yield return null;
            }

            if (hintCanvasGroup != null)
            {
                hintCanvasGroup.alpha = endAlpha;
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play tutorial sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlayTutorialSound(AudioClip clip)
        {
            if (clip != null && tutorialAudioSource != null)
            {
                tutorialAudioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Check if a specific step has been completed.
        /// </summary>
        /// <param name="stepId">Step ID to check</param>
        /// <returns>True if step is completed</returns>
        public bool IsStepCompleted(string stepId)
        {
            return completedSteps.ContainsKey(stepId) && completedSteps[stepId];
        }

        /// <summary>
        /// Set tutorial enabled state.
        /// </summary>
        /// <param name="enabled">True to enable tutorials</param>
        public void SetTutorialsEnabled(bool enabled)
        {
            enableTutorials = enabled;
            
            if (!enabled && IsTutorialActive)
            {
                SkipTutorial();
            }
        }

        /// <summary>
        /// Get tutorial completion percentage.
        /// </summary>
        /// <returns>Completion percentage (0-1)</returns>
        public float GetCompletionPercentage()
        {
            if (tutorialSteps.Length == 0) return 1f;

            int completedCount = 0;
            foreach (var step in tutorialSteps)
            {
                if (step != null && IsStepCompleted(step.StepId))
                {
                    completedCount++;
                }
            }

            return (float)completedCount / tutorialSteps.Length;
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Tutorial hint data structure.
    /// </summary>
    [System.Serializable]
    public class TutorialHint
    {
        public string HintText;
        public string KeyboardKey;
        public string GamepadButton;
        public Sprite Icon;
        public float Duration = 3f;
    }

    /// <summary>
    /// Button prompt for tutorials.
    /// </summary>
    [System.Serializable]
    public class TutorialButtonPrompt
    {
        public string PromptId;
        public string Action;
        public string KeyboardKey;
        public string GamepadButton;
        public int Priority = 5;
    }
    #endregion
}
