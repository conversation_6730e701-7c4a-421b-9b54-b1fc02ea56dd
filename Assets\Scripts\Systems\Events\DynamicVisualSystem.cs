using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Dynamic Visual System for World Events in Cinder of Darkness
/// Handles visual changes, atmospheric effects, and environmental modifications during events
/// </summary>
public class DynamicVisualSystem : MonoBehaviour
{
    [Header("Rendering References")]
    public Camera mainCamera;
    public Light directionalLight;
    public Volume globalVolume;
    public ParticleSystem[] globalParticleSystems;
    
    [<PERSON><PERSON>("Skybox Management")]
    public Material defaultSkybox;
    public Material[] eventSkyboxes;
    
    [Header("Lighting Presets")]
    public LightingPreset defaultLighting;
    public LightingPreset[] eventLightingPresets;
    
    [Header("Post-Processing Profiles")]
    public VolumeProfile defaultProfile;
    public VolumeProfile[] eventProfiles;
    
    [Header("Particle Effect Prefabs")]
    public GameObject[] weatherEffectPrefabs;
    public GameObject[] atmosphericEffectPrefabs;
    public GameObject[] magicalEffectPrefabs;
    
    [Header("Screen Effect Settings")]
    public float screenEffectIntensity = 1f;
    public bool enableScreenShake = true;
    public bool enableColorGrading = true;
    
    // Static instance
    private static DynamicVisualSystem instance;
    public static DynamicVisualSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<DynamicVisualSystem>();
                if (instance == null)
                {
                    GameObject go = new GameObject("DynamicVisualSystem");
                    instance = go.AddComponent<DynamicVisualSystem>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Visual state management
    private Dictionary<string, ActiveVisualEffect> activeEffects = new Dictionary<string, ActiveVisualEffect>();
    private LightingPreset originalLighting;
    private Material originalSkybox;
    private VolumeProfile originalProfile;
    private bool isInitialized = false;
    
    /// <summary>
    /// Active visual effect tracking
    /// </summary>
    [System.Serializable]
    public class ActiveVisualEffect
    {
        public string effectId;
        public string eventId;
        public VisualEffectType effectType;
        public float intensity;
        public Color effectColor;
        public GameObject effectObject;
        public ParticleSystem particleSystem;
        public float startTime;
        public float duration;
        public bool isPersistent;
        
        public bool IsExpired()
        {
            return !isPersistent && Time.time >= startTime + duration;
        }
    }
    
    /// <summary>
    /// Types of visual effects
    /// </summary>
    public enum VisualEffectType
    {
        Skybox,
        Lighting,
        PostProcessing,
        ParticleEffect,
        ScreenEffect,
        Fog,
        Weather,
        Atmospheric
    }
    
    /// <summary>
    /// Lighting preset configuration
    /// </summary>
    [System.Serializable]
    public class LightingPreset
    {
        public string presetName;
        public Color ambientColor = Color.gray;
        public float ambientIntensity = 1f;
        public Color directionalLightColor = Color.white;
        public float directionalLightIntensity = 1f;
        public Vector3 directionalLightRotation = new Vector3(50f, -30f, 0f);
        public bool enableFog = false;
        public Color fogColor = Color.gray;
        public float fogDensity = 0.01f;
        public FogMode fogMode = FogMode.ExponentialSquared;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeVisualSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        if (!isInitialized)
        {
            InitializeVisualSystem();
        }
        
        StartCoroutine(UpdateVisualEffects());
    }
    
    void InitializeVisualSystem()
    {
        // Find required components if not assigned
        if (mainCamera == null)
            mainCamera = Camera.main;
        
        if (directionalLight == null)
            directionalLight = FindObjectOfType<Light>();
        
        if (globalVolume == null)
            globalVolume = FindObjectOfType<Volume>();
        
        // Store original settings
        StoreOriginalSettings();
        
        isInitialized = true;
        Debug.Log("Dynamic Visual System initialized");
    }
    
    void StoreOriginalSettings()
    {
        // Store original skybox
        originalSkybox = RenderSettings.skybox;
        
        // Store original lighting
        originalLighting = new LightingPreset
        {
            presetName = "Original",
            ambientColor = RenderSettings.ambientLight,
            ambientIntensity = RenderSettings.ambientIntensity,
            enableFog = RenderSettings.fog,
            fogColor = RenderSettings.fogColor,
            fogDensity = RenderSettings.fogDensity,
            fogMode = RenderSettings.fogMode
        };
        
        if (directionalLight != null)
        {
            originalLighting.directionalLightColor = directionalLight.color;
            originalLighting.directionalLightIntensity = directionalLight.intensity;
            originalLighting.directionalLightRotation = directionalLight.transform.eulerAngles;
        }
        
        // Store original post-processing profile
        if (globalVolume != null)
        {
            originalProfile = globalVolume.profile;
        }
    }
    
    /// <summary>
    /// Update loop for visual effects
    /// </summary>
    IEnumerator UpdateVisualEffects()
    {
        while (true)
        {
            // Update active effects
            var expiredEffects = new List<string>();
            
            foreach (var kvp in activeEffects)
            {
                var effect = kvp.Value;
                
                if (effect.IsExpired())
                {
                    expiredEffects.Add(kvp.Key);
                }
                else
                {
                    UpdateVisualEffect(effect);
                }
            }
            
            // Remove expired effects
            foreach (var effectId in expiredEffects)
            {
                RemoveVisualEffect(effectId);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    /// <summary>
    /// Apply event visuals
    /// </summary>
    public string ApplyEventVisuals(string eventId, WorldEvent.VisualChanges visualChanges)
    {
        if (visualChanges == null) return null;
        
        string effectId = $"event_{eventId}_{System.Guid.NewGuid()}";
        
        // Apply skybox changes
        if (visualChanges.changeSkybox && visualChanges.eventSkybox != null)
        {
            ApplySkyboxChange(effectId, eventId, visualChanges.eventSkybox);
        }
        
        // Apply lighting changes
        if (visualChanges.changeLighting)
        {
            ApplyLightingChange(effectId, eventId, visualChanges);
        }
        
        // Apply post-processing changes
        if (visualChanges.changePostProcessing && !string.IsNullOrEmpty(visualChanges.postProcessingProfile))
        {
            ApplyPostProcessingChange(effectId, eventId, visualChanges.postProcessingProfile);
        }
        
        // Apply particle effects
        if (visualChanges.addParticleEffects && visualChanges.particleEffectPrefabs != null)
        {
            foreach (var prefabName in visualChanges.particleEffectPrefabs)
            {
                ApplyParticleEffect(effectId, eventId, prefabName);
            }
        }
        
        // Apply screen effects
        if (visualChanges.addScreenEffects && visualChanges.screenEffects != null)
        {
            foreach (var screenEffect in visualChanges.screenEffects)
            {
                ApplyScreenEffect(effectId, eventId, screenEffect);
            }
        }
        
        return effectId;
    }
    
    /// <summary>
    /// Apply skybox change
    /// </summary>
    void ApplySkyboxChange(string effectId, string eventId, Material skyboxMaterial)
    {
        RenderSettings.skybox = skyboxMaterial;
        
        var effect = new ActiveVisualEffect
        {
            effectId = effectId,
            eventId = eventId,
            effectType = VisualEffectType.Skybox,
            startTime = Time.time,
            isPersistent = true
        };
        
        activeEffects[effectId] = effect;
        
        Debug.Log($"Applied skybox change for event {eventId}");
    }
    
    /// <summary>
    /// Apply lighting change
    /// </summary>
    void ApplyLightingChange(string effectId, string eventId, WorldEvent.VisualChanges visualChanges)
    {
        // Apply ambient lighting
        RenderSettings.ambientLight = visualChanges.ambientLightColor;
        RenderSettings.ambientIntensity = visualChanges.lightIntensityMultiplier;
        
        // Apply directional light changes
        if (directionalLight != null)
        {
            directionalLight.color = visualChanges.ambientLightColor;
            directionalLight.intensity *= visualChanges.lightIntensityMultiplier;
        }
        
        var effect = new ActiveVisualEffect
        {
            effectId = effectId,
            eventId = eventId,
            effectType = VisualEffectType.Lighting,
            intensity = visualChanges.lightIntensityMultiplier,
            effectColor = visualChanges.ambientLightColor,
            startTime = Time.time,
            isPersistent = true
        };
        
        activeEffects[effectId] = effect;
        
        Debug.Log($"Applied lighting change for event {eventId}");
    }
    
    /// <summary>
    /// Apply post-processing change
    /// </summary>
    void ApplyPostProcessingChange(string effectId, string eventId, string profileName)
    {
        if (globalVolume == null) return;
        
        // Find profile by name
        var profile = System.Array.Find(eventProfiles, p => p.name == profileName);
        if (profile != null)
        {
            globalVolume.profile = profile;
            
            var effect = new ActiveVisualEffect
            {
                effectId = effectId,
                eventId = eventId,
                effectType = VisualEffectType.PostProcessing,
                startTime = Time.time,
                isPersistent = true
            };
            
            activeEffects[effectId] = effect;
            
            Debug.Log($"Applied post-processing change for event {eventId}");
        }
    }
    
    /// <summary>
    /// Apply particle effect
    /// </summary>
    void ApplyParticleEffect(string effectId, string eventId, string prefabName)
    {
        // Find prefab by name
        GameObject prefab = null;
        
        foreach (var weatherPrefab in weatherEffectPrefabs)
        {
            if (weatherPrefab.name == prefabName)
            {
                prefab = weatherPrefab;
                break;
            }
        }
        
        if (prefab == null)
        {
            foreach (var atmosphericPrefab in atmosphericEffectPrefabs)
            {
                if (atmosphericPrefab.name == prefabName)
                {
                    prefab = atmosphericPrefab;
                    break;
                }
            }
        }
        
        if (prefab == null)
        {
            foreach (var magicalPrefab in magicalEffectPrefabs)
            {
                if (magicalPrefab.name == prefabName)
                {
                    prefab = magicalPrefab;
                    break;
                }
            }
        }
        
        if (prefab != null)
        {
            GameObject effectObject = Instantiate(prefab);
            var particleSystem = effectObject.GetComponent<ParticleSystem>();
            
            var effect = new ActiveVisualEffect
            {
                effectId = effectId + "_particle",
                eventId = eventId,
                effectType = VisualEffectType.ParticleEffect,
                effectObject = effectObject,
                particleSystem = particleSystem,
                startTime = Time.time,
                isPersistent = true
            };
            
            activeEffects[effect.effectId] = effect;
            
            Debug.Log($"Applied particle effect {prefabName} for event {eventId}");
        }
    }
    
    /// <summary>
    /// Apply screen effect
    /// </summary>
    void ApplyScreenEffect(string effectId, string eventId, WorldEvent.ScreenEffect screenEffect)
    {
        switch (screenEffect.effectType)
        {
            case WorldEvent.ScreenEffectType.Shake:
                if (enableScreenShake)
                {
                    StartCoroutine(ScreenShakeEffect(screenEffect.intensity, screenEffect.duration));
                }
                break;
                
            case WorldEvent.ScreenEffectType.Flash:
                StartCoroutine(ScreenFlashEffect(screenEffect.effectColor, screenEffect.intensity, screenEffect.duration));
                break;
                
            case WorldEvent.ScreenEffectType.Fade:
                StartCoroutine(ScreenFadeEffect(screenEffect.effectColor, screenEffect.intensity, screenEffect.duration));
                break;
                
            case WorldEvent.ScreenEffectType.Vignette:
                ApplyVignetteEffect(effectId, eventId, screenEffect);
                break;
                
            case WorldEvent.ScreenEffectType.ColorGrading:
                if (enableColorGrading)
                {
                    ApplyColorGradingEffect(effectId, eventId, screenEffect);
                }
                break;
        }
    }
    
    /// <summary>
    /// Screen shake effect
    /// </summary>
    IEnumerator ScreenShakeEffect(float intensity, float duration)
    {
        if (mainCamera == null) yield break;
        
        Vector3 originalPosition = mainCamera.transform.localPosition;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            float x = Random.Range(-1f, 1f) * intensity;
            float y = Random.Range(-1f, 1f) * intensity;
            
            mainCamera.transform.localPosition = originalPosition + new Vector3(x, y, 0);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        mainCamera.transform.localPosition = originalPosition;
    }
    
    /// <summary>
    /// Screen flash effect
    /// </summary>
    IEnumerator ScreenFlashEffect(Color flashColor, float intensity, float duration)
    {
        // Create flash overlay
        GameObject flashOverlay = new GameObject("FlashOverlay");
        var canvas = flashOverlay.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        var image = flashOverlay.AddComponent<UnityEngine.UI.Image>();
        image.color = new Color(flashColor.r, flashColor.g, flashColor.b, 0f);
        
        var rectTransform = flashOverlay.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        
        // Flash animation
        float elapsed = 0f;
        while (elapsed < duration)
        {
            float alpha = Mathf.PingPong(elapsed * 4f, 1f) * intensity;
            image.color = new Color(flashColor.r, flashColor.g, flashColor.b, alpha);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Fade out
        while (image.color.a > 0f)
        {
            float alpha = Mathf.MoveTowards(image.color.a, 0f, Time.deltaTime * 2f);
            image.color = new Color(flashColor.r, flashColor.g, flashColor.b, alpha);
            yield return null;
        }
        
        Destroy(flashOverlay);
    }
    
    /// <summary>
    /// Screen fade effect
    /// </summary>
    IEnumerator ScreenFadeEffect(Color fadeColor, float intensity, float duration)
    {
        // Similar to flash but with smooth fade in/out
        GameObject fadeOverlay = new GameObject("FadeOverlay");
        var canvas = fadeOverlay.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        var image = fadeOverlay.AddComponent<UnityEngine.UI.Image>();
        image.color = new Color(fadeColor.r, fadeColor.g, fadeColor.b, 0f);
        
        var rectTransform = fadeOverlay.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        
        // Fade in
        float elapsed = 0f;
        float halfDuration = duration * 0.5f;
        
        while (elapsed < halfDuration)
        {
            float alpha = Mathf.Lerp(0f, intensity, elapsed / halfDuration);
            image.color = new Color(fadeColor.r, fadeColor.g, fadeColor.b, alpha);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Fade out
        elapsed = 0f;
        while (elapsed < halfDuration)
        {
            float alpha = Mathf.Lerp(intensity, 0f, elapsed / halfDuration);
            image.color = new Color(fadeColor.r, fadeColor.g, fadeColor.b, alpha);
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        Destroy(fadeOverlay);
    }
    
    /// <summary>
    /// Apply vignette effect
    /// </summary>
    void ApplyVignetteEffect(string effectId, string eventId, WorldEvent.ScreenEffect screenEffect)
    {
        // Implementation would modify post-processing vignette settings
        Debug.Log($"Applied vignette effect for event {eventId}");
    }
    
    /// <summary>
    /// Apply color grading effect
    /// </summary>
    void ApplyColorGradingEffect(string effectId, string eventId, WorldEvent.ScreenEffect screenEffect)
    {
        // Implementation would modify post-processing color grading settings
        Debug.Log($"Applied color grading effect for event {eventId}");
    }
    
    /// <summary>
    /// Update visual effect over time
    /// </summary>
    void UpdateVisualEffect(ActiveVisualEffect effect)
    {
        // Update effect properties based on time or other factors
        if (effect.particleSystem != null)
        {
            // Update particle system properties if needed
        }
    }
    
    /// <summary>
    /// Remove visual effect
    /// </summary>
    void RemoveVisualEffect(string effectId)
    {
        if (!activeEffects.ContainsKey(effectId)) return;
        
        var effect = activeEffects[effectId];
        
        // Clean up effect based on type
        switch (effect.effectType)
        {
            case VisualEffectType.ParticleEffect:
                if (effect.effectObject != null)
                {
                    if (effect.particleSystem != null)
                    {
                        effect.particleSystem.Stop();
                        Destroy(effect.effectObject, 2f); // Allow particles to finish
                    }
                    else
                    {
                        Destroy(effect.effectObject);
                    }
                }
                break;
        }
        
        activeEffects.Remove(effectId);
    }
    
    /// <summary>
    /// Remove all event visuals
    /// </summary>
    public void RemoveEventVisuals(string eventId)
    {
        var effectsToRemove = new List<string>();
        
        foreach (var kvp in activeEffects)
        {
            if (kvp.Value.eventId == eventId)
            {
                effectsToRemove.Add(kvp.Key);
            }
        }
        
        foreach (var effectId in effectsToRemove)
        {
            RemoveVisualEffect(effectId);
        }
        
        // Check if we need to restore original settings
        if (activeEffects.Count == 0)
        {
            RestoreOriginalSettings();
        }
    }
    
    /// <summary>
    /// Restore original visual settings
    /// </summary>
    void RestoreOriginalSettings()
    {
        // Restore skybox
        if (originalSkybox != null)
        {
            RenderSettings.skybox = originalSkybox;
        }
        
        // Restore lighting
        if (originalLighting != null)
        {
            RenderSettings.ambientLight = originalLighting.ambientColor;
            RenderSettings.ambientIntensity = originalLighting.ambientIntensity;
            RenderSettings.fog = originalLighting.enableFog;
            RenderSettings.fogColor = originalLighting.fogColor;
            RenderSettings.fogDensity = originalLighting.fogDensity;
            RenderSettings.fogMode = originalLighting.fogMode;
            
            if (directionalLight != null)
            {
                directionalLight.color = originalLighting.directionalLightColor;
                directionalLight.intensity = originalLighting.directionalLightIntensity;
                directionalLight.transform.eulerAngles = originalLighting.directionalLightRotation;
            }
        }
        
        // Restore post-processing
        if (globalVolume != null && originalProfile != null)
        {
            globalVolume.profile = originalProfile;
        }
        
        Debug.Log("Restored original visual settings");
    }
    
    // Public API
    public static void ApplyEventVisualChanges(string eventId, WorldEvent.VisualChanges visualChanges)
    {
        Instance.ApplyEventVisuals(eventId, visualChanges);
    }
    
    public static void RemoveEventVisualChanges(string eventId)
    {
        Instance.RemoveEventVisuals(eventId);
    }
    
    public static bool HasActiveVisualEffects()
    {
        return Instance.activeEffects.Count > 0;
    }
    
    public static List<ActiveVisualEffect> GetActiveEffects()
    {
        return new List<ActiveVisualEffect>(Instance.activeEffects.Values);
    }
}
