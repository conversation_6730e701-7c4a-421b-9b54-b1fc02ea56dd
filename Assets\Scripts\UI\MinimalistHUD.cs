using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class MinimalistHUD : MonoBehaviour
{
    [Header("Core HUD Elements")]
    public Slider healthBar;
    public Slider manaBar;
    public Slider rageFlameBar;
    public Image weaponIcon;
    public Image shieldIcon;
    
    [Header("Rage Flame System")]
    public float maxRageFlame = 100f;
    public float currentRageFlame = 0f;
    public float rageDecayRate = 10f;
    public float rageBuildRate = 15f;
    public Color rageFlameColor = new Color(1f, 0.3f, 0f);
    public ParticleSystem rageFlameEffect;
    
    [Header("HUD Positioning")]
    public RectTransform hudContainer;
    public Vector2 hudOffset = new Vector2(50f, 50f);
    public bool autoHide = true;
    public float autoHideDelay = 3f;
    
    [Header("Visual Effects")]
    public AnimationCurve healthPulse;
    public AnimationCurve ragePulse;
    public float pulseSpeed = 2f;
    
    [Header("Combat State")]
    public bool isInCombat = false;
    public float combatDetectionRange = 15f;
    public LayerMask enemyLayers = -1;
    
    private PlayerStats playerStats;
    private ElementalMagicSystem magicSystem;
    private PlayerCombat playerCombat;
    private CanvasGroup hudCanvasGroup;
    private float lastCombatTime = 0f;
    private bool isRageFlameActive = false;
    private Coroutine autoHideCoroutine;
    
    void Start()
    {
        InitializeHUD();
        FindPlayerSystems();
        SetupHUDElements();
    }
    
    void Update()
    {
        UpdateCombatState();
        UpdateHUDValues();
        UpdateRageFlame();
        UpdateHUDVisibility();
        UpdateVisualEffects();
    }
    
    void InitializeHUD()
    {
        hudCanvasGroup = hudContainer.GetComponent<CanvasGroup>();
        if (hudCanvasGroup == null)
        {
            hudCanvasGroup = hudContainer.gameObject.AddComponent<CanvasGroup>();
        }
        
        // Position HUD elements
        if (hudContainer != null)
        {
            hudContainer.anchoredPosition = hudOffset;
        }
        
        Debug.Log("Minimalist HUD initialized - weapon, shield, health, mana, rage flame");
    }
    
    void FindPlayerSystems()
    {
        playerStats = FindObjectOfType<PlayerStats>();
        magicSystem = FindObjectOfType<ElementalMagicSystem>();
        playerCombat = FindObjectOfType<PlayerCombat>();
        
        if (playerStats == null)
        {
            Debug.LogWarning("PlayerStats not found for HUD");
        }
    }
    
    void SetupHUDElements()
    {
        // Setup health bar
        if (healthBar != null)
        {
            healthBar.minValue = 0f;
            healthBar.maxValue = 1f;
            healthBar.value = 1f;
        }
        
        // Setup mana bar
        if (manaBar != null)
        {
            manaBar.minValue = 0f;
            manaBar.maxValue = 1f;
            manaBar.value = 1f;
        }
        
        // Setup rage flame bar
        if (rageFlameBar != null)
        {
            rageFlameBar.minValue = 0f;
            rageFlameBar.maxValue = 1f;
            rageFlameBar.value = 0f;
            
            // Set rage flame color
            Image rageFill = rageFlameBar.fillRect.GetComponent<Image>();
            if (rageFill != null)
            {
                rageFill.color = rageFlameColor;
            }
        }
    }
    
    void UpdateCombatState()
    {
        // Check for nearby enemies
        Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, combatDetectionRange, enemyLayers);
        
        bool wasInCombat = isInCombat;
        isInCombat = nearbyEnemies.Length > 0;
        
        if (isInCombat)
        {
            lastCombatTime = Time.time;
        }
        
        // Combat state changed
        if (wasInCombat != isInCombat)
        {
            OnCombatStateChanged(isInCombat);
        }
    }
    
    void OnCombatStateChanged(bool inCombat)
    {
        if (inCombat)
        {
            // Show HUD immediately when combat starts
            ShowHUD();
            
            // Start building rage flame
            StartCoroutine(BuildRageFlame());
        }
        else
        {
            // Start auto-hide timer when combat ends
            if (autoHide)
            {
                StartAutoHideTimer();
            }
            
            // Start rage flame decay
            StartCoroutine(DecayRageFlame());
        }
    }
    
    void UpdateHUDValues()
    {
        if (playerStats != null)
        {
            // Update health bar
            if (healthBar != null)
            {
                float healthPercent = playerStats.GetCurrentHealth() / playerStats.maxHealth;
                healthBar.value = healthPercent;
            }
        }
        
        if (magicSystem != null)
        {
            // Update mana bar
            if (manaBar != null)
            {
                float manaPercent = magicSystem.GetCurrentMana() / magicSystem.GetMaxMana();
                manaBar.value = manaPercent;
            }
        }
        
        // Update rage flame bar
        if (rageFlameBar != null)
        {
            float ragePercent = currentRageFlame / maxRageFlame;
            rageFlameBar.value = ragePercent;
        }
        
        // Update weapon and shield icons
        UpdateEquipmentIcons();
    }
    
    void UpdateEquipmentIcons()
    {
        if (playerCombat != null)
        {
            // Update weapon icon
            if (weaponIcon != null)
            {
                Weapon currentWeapon = playerCombat.GetCurrentWeapon();
                if (currentWeapon != null && currentWeapon.weaponIcon != null)
                {
                    weaponIcon.sprite = currentWeapon.weaponIcon;
                    weaponIcon.gameObject.SetActive(true);
                }
                else
                {
                    weaponIcon.gameObject.SetActive(false);
                }
            }
            
            // Update shield icon
            if (shieldIcon != null)
            {
                Shield currentShield = playerCombat.GetCurrentShield();
                if (currentShield != null && currentShield.shieldIcon != null)
                {
                    shieldIcon.sprite = currentShield.shieldIcon;
                    shieldIcon.gameObject.SetActive(true);
                }
                else
                {
                    shieldIcon.gameObject.SetActive(false);
                }
            }
        }
    }
    
    void UpdateRageFlame()
    {
        // Rage flame builds during combat and decays outside combat
        if (isInCombat)
        {
            // Build rage when taking damage or dealing damage
            if (playerCombat != null && playerCombat.IsAttacking())
            {
                BuildRageFlameValue(rageBuildRate * Time.deltaTime);
            }
        }
        else
        {
            // Decay rage when not in combat
            DecayRageFlameValue(rageDecayRate * Time.deltaTime);
        }
        
        // Check if rage flame is active
        bool wasActive = isRageFlameActive;
        isRageFlameActive = currentRageFlame >= maxRageFlame;
        
        if (isRageFlameActive && !wasActive)
        {
            ActivateRageFlame();
        }
        else if (!isRageFlameActive && wasActive)
        {
            DeactivateRageFlame();
        }
    }
    
    void BuildRageFlameValue(float amount)
    {
        currentRageFlame = Mathf.Min(currentRageFlame + amount, maxRageFlame);
    }
    
    void DecayRageFlameValue(float amount)
    {
        currentRageFlame = Mathf.Max(currentRageFlame - amount, 0f);
    }
    
    IEnumerator BuildRageFlame()
    {
        while (isInCombat)
        {
            BuildRageFlameValue(rageBuildRate * Time.deltaTime);
            yield return null;
        }
    }
    
    IEnumerator DecayRageFlame()
    {
        while (!isInCombat && currentRageFlame > 0f)
        {
            DecayRageFlameValue(rageDecayRate * Time.deltaTime);
            yield return null;
        }
    }
    
    void ActivateRageFlame()
    {
        Debug.Log("Rage Flame activated! The Cinderborn's fury burns bright!");
        
        // Visual effects
        if (rageFlameEffect != null)
        {
            rageFlameEffect.Play();
        }
        
        // Apply rage bonuses to player
        if (playerStats != null)
        {
            playerStats.AddTemporaryStatBonus("RageFlame", 20f, 0f, 20f); // +20 strength, +20 stamina
        }
        
        // Show activation message
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt("RAGE FLAME IGNITED! The Cinderborn's fury knows no bounds!");
            StartCoroutine(HideMessageAfterDelay(gameUI, 3f));
        }
    }
    
    void DeactivateRageFlame()
    {
        Debug.Log("Rage Flame extinguished. The fury subsides...");
        
        // Stop visual effects
        if (rageFlameEffect != null)
        {
            rageFlameEffect.Stop();
        }
        
        // Remove rage bonuses
        if (playerStats != null)
        {
            playerStats.RemoveTemporaryStatBonus("RageFlame");
        }
    }
    
    void UpdateHUDVisibility()
    {
        // HUD is always visible during combat
        if (isInCombat)
        {
            ShowHUD();
        }
    }
    
    void UpdateVisualEffects()
    {
        // Health bar pulse when low
        if (healthBar != null && playerStats != null)
        {
            float healthPercent = playerStats.GetCurrentHealth() / playerStats.maxHealth;
            if (healthPercent < 0.3f)
            {
                float pulse = healthPulse.Evaluate(Time.time * pulseSpeed);
                Color healthColor = Color.Lerp(Color.white, Color.red, pulse);
                healthBar.fillRect.GetComponent<Image>().color = healthColor;
            }
            else
            {
                healthBar.fillRect.GetComponent<Image>().color = Color.white;
            }
        }
        
        // Rage flame pulse when active
        if (rageFlameBar != null && isRageFlameActive)
        {
            float pulse = ragePulse.Evaluate(Time.time * pulseSpeed * 2f);
            Color rageColor = Color.Lerp(rageFlameColor, Color.yellow, pulse);
            rageFlameBar.fillRect.GetComponent<Image>().color = rageColor;
        }
        else if (rageFlameBar != null)
        {
            rageFlameBar.fillRect.GetComponent<Image>().color = rageFlameColor;
        }
    }
    
    void ShowHUD()
    {
        if (autoHideCoroutine != null)
        {
            StopCoroutine(autoHideCoroutine);
            autoHideCoroutine = null;
        }
        
        if (hudCanvasGroup != null)
        {
            StartCoroutine(FadeHUD(1f, 0.3f));
        }
    }
    
    void HideHUD()
    {
        if (hudCanvasGroup != null && !isInCombat)
        {
            StartCoroutine(FadeHUD(0f, 1f));
        }
    }
    
    void StartAutoHideTimer()
    {
        if (autoHideCoroutine != null)
        {
            StopCoroutine(autoHideCoroutine);
        }
        
        autoHideCoroutine = StartCoroutine(AutoHideTimer());
    }
    
    IEnumerator AutoHideTimer()
    {
        yield return new WaitForSeconds(autoHideDelay);
        
        if (!isInCombat)
        {
            HideHUD();
        }
        
        autoHideCoroutine = null;
    }
    
    IEnumerator FadeHUD(float targetAlpha, float duration)
    {
        if (hudCanvasGroup == null) yield break;
        
        float startAlpha = hudCanvasGroup.alpha;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            hudCanvasGroup.alpha = Mathf.Lerp(startAlpha, targetAlpha, progress);
            yield return null;
        }
        
        hudCanvasGroup.alpha = targetAlpha;
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Public methods for external systems
    public void OnPlayerTakeDamage(float damage)
    {
        // Build rage when taking damage
        BuildRageFlameValue(damage * 0.5f);
        
        // Show HUD when taking damage
        ShowHUD();
        StartAutoHideTimer();
    }
    
    public void OnPlayerDealDamage(float damage)
    {
        // Build rage when dealing damage
        BuildRageFlameValue(damage * 0.3f);
    }
    
    public void ForceShowHUD(float duration = 0f)
    {
        ShowHUD();
        
        if (duration > 0f)
        {
            StartCoroutine(ForceShowTimer(duration));
        }
    }
    
    IEnumerator ForceShowTimer(float duration)
    {
        yield return new WaitForSeconds(duration);
        
        if (!isInCombat)
        {
            StartAutoHideTimer();
        }
    }
    
    // Getters
    public float GetRageFlamePercent() => currentRageFlame / maxRageFlame;
    public bool IsRageFlameActive() => isRageFlameActive;
    public bool IsInCombat() => isInCombat;
}

// Extension for PlayerCombat to support HUD
public static class PlayerCombatExtensions
{
    public static Weapon GetCurrentWeapon(this PlayerCombat combat)
    {
        // This would return the currently equipped weapon
        return null; // Placeholder
    }
    
    public static Shield GetCurrentShield(this PlayerCombat combat)
    {
        // This would return the currently equipped shield
        return null; // Placeholder
    }
    
    public static bool IsAttacking(this PlayerCombat combat)
    {
        // This would check if the player is currently attacking
        return false; // Placeholder
    }
}

[System.Serializable]
public class Weapon
{
    public string weaponName;
    public Sprite weaponIcon;
    public float damage;
    public float speed;
}

[System.Serializable]
public class Shield
{
    public string shieldName;
    public Sprite shieldIcon;
    public float defense;
    public float blockChance;
}
