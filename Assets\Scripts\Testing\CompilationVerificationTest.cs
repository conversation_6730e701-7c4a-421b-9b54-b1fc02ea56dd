using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Comprehensive compilation verification test for Cinder of Darkness
/// This script verifies that all major systems compile and can be instantiated
/// </summary>
public class CompilationVerificationTest : MonoBehaviour
{
    [Header("Test Results")]
    public bool allSystemsCompiled = false;
    public List<string> compilationErrors = new List<string>();
    public List<string> successfulSystems = new List<string>();
    
    void Start()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunCompilationTests());
        }
    }
    
    System.Collections.IEnumerator RunCompilationTests()
    {
        Debug.Log("=== CINDER OF DARKNESS COMPILATION VERIFICATION ===");
        
        yield return new WaitForSeconds(1f);
        
        // Test all major systems
        TestSystemCompilation();
        
        // Report results
        ReportResults();
    }
    
    void TestSystemCompilation()
    {
        // Test Core Player Systems
        TestSystem<PlayerController>("PlayerController");
        TestSystem<PlayerStats>("PlayerStats");
        TestSystem<PlayerCombat>("PlayerCombat");
        TestSystem<PsychologicalSystem>("PsychologicalSystem");
        TestSystem<CharacterProgression>("CharacterProgression");
        
        // Test Combat Systems
        TestSystem<BrutalCombatSystem>("BrutalCombatSystem");
        TestSystem<DeathConsequenceSystem>("DeathConsequenceSystem");
        TestSystem<EnemyAI>("EnemyAI");
        TestSystem<EnemyHealth>("EnemyHealth");
        
        // Test Magic Systems
        TestSystem<ElementalMagicSystem>("ElementalMagicSystem");
        
        // Test Input Systems
        TestSystem<MultiInputControlSystem>("MultiInputControlSystem");
        TestSystem<UIButtonPromptSystem>("UIButtonPromptSystem");
        
        // Test UI Systems
        TestSystem<GameUI>("GameUI");
        TestSystem<DialogueSystem>("DialogueSystem");
        
        // Test Management Systems
        TestSystem<GameManager>("GameManager");
        TestSystem<HostilitySystem>("HostilitySystem");
        TestSystem<EconomySystem>("EconomySystem");
        
        // Test World Systems
        TestSystem<WorldInteractionSystem>("WorldInteractionSystem");
        TestSystem<DynamicMusicalSystem>("DynamicMusicalSystem");
        
        // Test Narrative Systems
        TestSystem<PhilosophicalMoralitySystem>("PhilosophicalMoralitySystem");
        TestSystem<DynamicTitleSystem>("DynamicTitleSystem");
        TestSystem<OrphanedChildCompanion>("OrphanedChildCompanion");
        
        // Test NPC Systems
        TestSystem<NPCController>("NPCController");
        
        // Test Input Wrapper
        TestInputWrapper();
    }
    
    void TestSystem<T>(string systemName) where T : MonoBehaviour
    {
        try
        {
            // Try to find existing component
            T existingComponent = FindObjectOfType<T>();
            
            if (existingComponent != null)
            {
                successfulSystems.Add($"{systemName} - Found existing instance");
                Debug.Log($"✓ {systemName} - Found and verified existing instance");
            }
            else
            {
                // Try to create a test instance
                GameObject testObject = new GameObject($"Test_{systemName}");
                T component = testObject.AddComponent<T>();
                
                if (component != null)
                {
                    successfulSystems.Add($"{systemName} - Created test instance");
                    Debug.Log($"✓ {systemName} - Successfully created test instance");
                    
                    // Clean up test object
                    Destroy(testObject);
                }
                else
                {
                    compilationErrors.Add($"{systemName} - Failed to create component");
                    Debug.LogError($"✗ {systemName} - Failed to create component");
                }
            }
        }
        catch (System.Exception e)
        {
            compilationErrors.Add($"{systemName} - Exception: {e.Message}");
            Debug.LogError($"✗ {systemName} - Exception: {e.Message}");
        }
    }
    
    void TestInputWrapper()
    {
        try
        {
            CinderInput inputWrapper = new CinderInput();
            if (inputWrapper != null)
            {
                successfulSystems.Add("CinderInput - Input wrapper created successfully");
                Debug.Log("✓ CinderInput - Input wrapper created successfully");
            }
            else
            {
                compilationErrors.Add("CinderInput - Failed to create input wrapper");
                Debug.LogError("✗ CinderInput - Failed to create input wrapper");
            }
        }
        catch (System.Exception e)
        {
            compilationErrors.Add($"CinderInput - Exception: {e.Message}");
            Debug.LogError($"✗ CinderInput - Exception: {e.Message}");
        }
    }
    
    void ReportResults()
    {
        Debug.Log("=== COMPILATION VERIFICATION RESULTS ===");
        
        Debug.Log($"Successful Systems: {successfulSystems.Count}");
        foreach (string success in successfulSystems)
        {
            Debug.Log($"✓ {success}");
        }
        
        if (compilationErrors.Count > 0)
        {
            Debug.Log($"Compilation Errors: {compilationErrors.Count}");
            foreach (string error in compilationErrors)
            {
                Debug.LogError($"✗ {error}");
            }
            allSystemsCompiled = false;
        }
        else
        {
            allSystemsCompiled = true;
            Debug.Log("🎉 ALL SYSTEMS COMPILED SUCCESSFULLY! 🎉");
            Debug.Log("Cinder of Darkness is ready for development!");
        }
        
        Debug.Log("=== END COMPILATION VERIFICATION ===");
    }
    
    [ContextMenu("Run Compilation Test")]
    public void RunTestManually()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunCompilationTests());
        }
        else
        {
            TestSystemCompilation();
            ReportResults();
        }
    }
    
    // Public method to check specific system
    public bool IsSystemCompiled(string systemName)
    {
        return successfulSystems.Exists(s => s.Contains(systemName));
    }
    
    // Public method to get compilation status
    public string GetCompilationStatus()
    {
        if (allSystemsCompiled)
        {
            return $"✅ All {successfulSystems.Count} systems compiled successfully!";
        }
        else
        {
            return $"❌ {compilationErrors.Count} compilation errors found. {successfulSystems.Count} systems successful.";
        }
    }
}
