#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;

/// <summary>
/// Unity 2022.3.62f1 Compatibility Checker for Cinder of Darkness
/// Validates project compatibility and identifies potential issues
/// </summary>
public class Unity2022_3_62_CompatibilityChecker : EditorWindow
{
    [MenuItem("Cinder of Darkness/Build Tools/Unity 2022.3.62f1 Compatibility Check")]
    public static void ShowWindow()
    {
        GetWindow<Unity2022_3_62_CompatibilityChecker>("Unity 2022.3.62f1 Compatibility");
    }

    private Vector2 scrollPosition;
    private List<CompatibilityIssue> issues = new List<CompatibilityIssue>();
    private bool scanCompleted = false;
    private int totalFiles = 0;
    private int scannedFiles = 0;

    public class CompatibilityIssue
    {
        public string file;
        public int line;
        public string issue;
        public string severity;
        public string suggestion;
    }

    void OnGUI()
    {
        GUILayout.Label("Unity 2022.3.62f1 Compatibility Checker", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("Run Compatibility Scan", GUILayout.Height(30)))
        {
            RunCompatibilityScan();
        }

        GUILayout.Space(10);

        if (scanCompleted)
        {
            GUILayout.Label($"Scan Results: {issues.Count} issues found in {totalFiles} files", EditorStyles.helpBox);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var issue in issues)
            {
                EditorGUILayout.BeginVertical("box");
                
                GUIStyle severityStyle = EditorStyles.label;
                if (issue.severity == "Error") severityStyle.normal.textColor = Color.red;
                else if (issue.severity == "Warning") severityStyle.normal.textColor = Color.yellow;
                
                GUILayout.Label($"[{issue.severity}] {issue.file}:{issue.line}", severityStyle);
                GUILayout.Label($"Issue: {issue.issue}", EditorStyles.wordWrappedLabel);
                if (!string.IsNullOrEmpty(issue.suggestion))
                {
                    GUILayout.Label($"Suggestion: {issue.suggestion}", EditorStyles.wordWrappedMiniLabel);
                }
                
                EditorGUILayout.EndVertical();
                GUILayout.Space(5);
            }
            
            EditorGUILayout.EndScrollView();
        }
        else if (scannedFiles > 0)
        {
            float progress = (float)scannedFiles / totalFiles;
            EditorGUI.ProgressBar(GUILayoutUtility.GetRect(200, 20), progress, $"Scanning... {scannedFiles}/{totalFiles}");
        }
    }

    void RunCompatibilityScan()
    {
        issues.Clear();
        scanCompleted = false;
        scannedFiles = 0;

        // Get all C# files
        string[] scriptPaths = Directory.GetFiles("Assets", "*.cs", SearchOption.AllDirectories);
        totalFiles = scriptPaths.Length;

        foreach (string scriptPath in scriptPaths)
        {
            ScanFile(scriptPath);
            scannedFiles++;
        }

        // Check project settings
        CheckProjectSettings();
        
        // Check package compatibility
        CheckPackageCompatibility();

        scanCompleted = true;
        Debug.Log($"Unity 2022.3.62f1 compatibility scan completed. Found {issues.Count} issues.");
    }

    void ScanFile(string filePath)
    {
        string content = File.ReadAllText(filePath);
        string[] lines = content.Split('\n');

        for (int i = 0; i < lines.Length; i++)
        {
            string line = lines[i];
            
            // Check for deprecated APIs
            CheckDeprecatedAPIs(filePath, i + 1, line);
            
            // Check for obsolete methods
            CheckObsoleteMethods(filePath, i + 1, line);
            
            // Check for Input System compatibility
            CheckInputSystemUsage(filePath, i + 1, line);
            
            // Check for URP compatibility
            CheckURPCompatibility(filePath, i + 1, line);
        }
    }

    void CheckDeprecatedAPIs(string file, int line, string content)
    {
        // Check for deprecated Unity APIs
        var deprecatedPatterns = new Dictionary<string, string>
        {
            { @"Application\.LoadLevel", "Use SceneManager.LoadScene instead" },
            { @"Application\.loadedLevel", "Use SceneManager.GetActiveScene().buildIndex instead" },
            { @"Application\.loadedLevelName", "Use SceneManager.GetActiveScene().name instead" },
            { @"WWW\s+", "Use UnityWebRequest instead of WWW" },
            { @"\.renderer\.", "Use GetComponent<Renderer>() instead" },
            { @"\.rigidbody\.", "Use GetComponent<Rigidbody>() instead" },
            { @"\.collider\.", "Use GetComponent<Collider>() instead" },
            { @"\.animation\.", "Use GetComponent<Animation>() instead" },
            { @"\.audio\.", "Use GetComponent<AudioSource>() instead" },
            { @"\.camera\.", "Use GetComponent<Camera>() instead" },
            { @"\.light\.", "Use GetComponent<Light>() instead" }
        };

        foreach (var pattern in deprecatedPatterns)
        {
            if (Regex.IsMatch(content, pattern.Key))
            {
                issues.Add(new CompatibilityIssue
                {
                    file = file,
                    line = line,
                    issue = $"Deprecated API usage: {pattern.Key}",
                    severity = "Warning",
                    suggestion = pattern.Value
                });
            }
        }
    }

    void CheckObsoleteMethods(string file, int line, string content)
    {
        // Check for methods that became obsolete in Unity 2022.3
        var obsoletePatterns = new Dictionary<string, string>
        {
            { @"QualitySettings\.masterTextureLimit", "Use QualitySettings.globalTextureMipmapLimit instead" },
            { @"Texture\.GetPixels32\(\)", "Consider using Texture2D.GetPixelData for better performance" },
            { @"Mesh\.vertices\s*=", "Use Mesh.SetVertices for better performance" },
            { @"Mesh\.triangles\s*=", "Use Mesh.SetTriangles for better performance" },
            { @"Mesh\.normals\s*=", "Use Mesh.SetNormals for better performance" },
            { @"Mesh\.uv\s*=", "Use Mesh.SetUVs for better performance" }
        };

        foreach (var pattern in obsoletePatterns)
        {
            if (Regex.IsMatch(content, pattern.Key))
            {
                issues.Add(new CompatibilityIssue
                {
                    file = file,
                    line = line,
                    issue = $"Obsolete method usage: {pattern.Key}",
                    severity = "Warning",
                    suggestion = pattern.Value
                });
            }
        }
    }

    void CheckInputSystemUsage(string file, int line, string content)
    {
        // Check for old Input Manager usage
        if (Regex.IsMatch(content, @"Input\.Get"))
        {
            if (!content.Contains("using UnityEngine.InputSystem"))
            {
                issues.Add(new CompatibilityIssue
                {
                    file = file,
                    line = line,
                    issue = "Old Input Manager usage detected",
                    severity = "Info",
                    suggestion = "Consider migrating to new Input System for better device support"
                });
            }
        }
    }

    void CheckURPCompatibility(string file, int line, string content)
    {
        // Check for Built-in RP specific code
        var urpIncompatible = new Dictionary<string, string>
        {
            { @"OnRenderImage", "Use Volume and Post-processing with URP instead" },
            { @"Camera\.SetReplacementShader", "Use Renderer Features in URP instead" },
            { @"Graphics\.Blit", "Use CommandBuffer.Blit or URP Renderer Features instead" }
        };

        foreach (var pattern in urpIncompatible)
        {
            if (Regex.IsMatch(content, pattern.Key))
            {
                issues.Add(new CompatibilityIssue
                {
                    file = file,
                    line = line,
                    issue = $"URP incompatible code: {pattern.Key}",
                    severity = "Warning",
                    suggestion = pattern.Value
                });
            }
        }
    }

    void CheckProjectSettings()
    {
        // Check Unity version
        string versionPath = "ProjectSettings/ProjectVersion.txt";
        if (File.Exists(versionPath))
        {
            string versionContent = File.ReadAllText(versionPath);
            if (!versionContent.Contains("2022.3.62f1"))
            {
                issues.Add(new CompatibilityIssue
                {
                    file = versionPath,
                    line = 1,
                    issue = "Project not set to Unity 2022.3.62f1",
                    severity = "Error",
                    suggestion = "Update ProjectVersion.txt to Unity 2022.3.62f1"
                });
            }
        }

        // Check scripting define symbols
        if (!PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Standalone).Contains("UNITY_2022_3_62"))
        {
            issues.Add(new CompatibilityIssue
            {
                file = "ProjectSettings",
                line = 0,
                issue = "Missing Unity version define symbol",
                severity = "Info",
                suggestion = "Add UNITY_2022_3_62 to scripting define symbols"
            });
        }
    }

    void CheckPackageCompatibility()
    {
        string manifestPath = "Packages/manifest.json";
        if (File.Exists(manifestPath))
        {
            string manifestContent = File.ReadAllText(manifestPath);
            
            // Check for updated package versions
            var requiredVersions = new Dictionary<string, string>
            {
                { "com.unity.inputsystem", "1.7.0" },
                { "com.unity.render-pipelines.universal", "14.0.11" },
                { "com.unity.localization", "1.4.5" },
                { "com.unity.timeline", "1.7.6" },
                { "com.unity.visualscripting", "1.9.4" }
            };

            foreach (var package in requiredVersions)
            {
                if (!manifestContent.Contains($"\"{package.Key}\": \"{package.Value}\""))
                {
                    issues.Add(new CompatibilityIssue
                    {
                        file = manifestPath,
                        line = 0,
                        issue = $"Package {package.Key} may need updating",
                        severity = "Info",
                        suggestion = $"Update to version {package.Value} for Unity 2022.3.62f1 compatibility"
                    });
                }
            }
        }
    }
}
#endif
