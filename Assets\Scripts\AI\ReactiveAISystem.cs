using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.AI;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Reactive AI System for Cinder of Darkness.
    /// Adapts enemy behavior based on player actions and implements dynamic difficulty.
    /// </summary>
    public class ReactiveAISystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("AI Adaptation Settings")]
        [SerializeField] private bool enableAdaptiveAI = true;
        [SerializeField] private float adaptationRate = 0.1f;
        [SerializeField] private float adaptationDecayRate = 0.05f;
        [SerializeField] private int behaviorSampleSize = 20;

        [Header("Difficulty Scaling")]
        [SerializeField] private bool enableDynamicDifficulty = true;
        [SerializeField] private float difficultyAdjustmentRate = 0.02f;
        [SerializeField] private float targetWinRate = 0.7f;
        [SerializeField] private Vector2 difficultyRange = new Vector2(0.5f, 2.0f);

        [Header("NPC Reaction System")]
        [SerializeField] private bool enableNPCReactions = true;
        [SerializeField] private float reputationInfluenceRange = 50f;
        [SerializeField] private NPCReactionProfile[] npcReactionProfiles;

        [Header("Behavior Tracking")]
        [SerializeField] private PlayerBehaviorWeight[] behaviorWeights;
        [SerializeField] private float behaviorTrackingDuration = 300f; // 5 minutes

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        #endregion

        #region Public Properties
        public static ReactiveAISystem Instance { get; private set; }
        public PlayerBehaviorProfile CurrentBehaviorProfile { get; private set; }
        public float CurrentDifficultyMultiplier { get; private set; } = 1.0f;
        public Dictionary<string, float> AdaptationLevels { get; private set; }
        #endregion

        #region Private Fields
        private Queue<PlayerAction> recentActions = new Queue<PlayerAction>();
        private Dictionary<string, AIAdaptationData> aiAdaptations = new Dictionary<string, AIAdaptationData>();
        private List<CombatEncounter> recentEncounters = new List<CombatEncounter>();

        // System references
        private DynamicNarrativeSystem narrativeSystem;
        private PlayerStats playerStats;
        private PlayerController playerController;
        private PlayerCombat playerCombat;

        // Behavior analysis
        private float lastBehaviorUpdate;
        private float lastDifficultyUpdate;
        private Dictionary<BehaviorType, float> behaviorScores = new Dictionary<BehaviorType, float>();

        // NPC reaction tracking
        private Dictionary<string, NPCReactionState> npcReactionStates = new Dictionary<string, NPCReactionState>();
        #endregion

        #region Events
        public System.Action<PlayerBehaviorProfile> OnBehaviorProfileChanged;
        public System.Action<float> OnDifficultyChanged;
        public System.Action<string, AIAdaptationType> OnAIAdaptation;
        public System.Action<string, NPCReactionType> OnNPCReactionChanged;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeReactiveAI();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            InitializeBehaviorTracking();
            SetupEventListeners();
        }

        private void Update()
        {
            if (!enableAdaptiveAI) return;

            UpdateBehaviorAnalysis();
            UpdateAIAdaptations();
            UpdateDynamicDifficulty();
            UpdateNPCReactions();
        }
        #endregion

        #region Initialization
        private void InitializeReactiveAI()
        {
            CurrentBehaviorProfile = new PlayerBehaviorProfile();
            AdaptationLevels = new Dictionary<string, float>();

            // Initialize behavior scores
            foreach (BehaviorType behaviorType in System.Enum.GetValues(typeof(BehaviorType)))
            {
                behaviorScores[behaviorType] = 0f;
            }

            Debug.Log("Reactive AI System initialized");
        }

        private void SetupSystemReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            playerStats = FindObjectOfType<PlayerStats>();
            playerController = FindObjectOfType<PlayerController>();
            playerCombat = FindObjectOfType<PlayerCombat>();
        }

        private void InitializeBehaviorTracking()
        {
            // Initialize behavior weights if not set
            if (behaviorWeights == null || behaviorWeights.Length == 0)
            {
                behaviorWeights = new PlayerBehaviorWeight[]
                {
                    new PlayerBehaviorWeight { behaviorType = BehaviorType.Aggressive, weight = 1.0f },
                    new PlayerBehaviorWeight { behaviorType = BehaviorType.Defensive, weight = 1.0f },
                    new PlayerBehaviorWeight { behaviorType = BehaviorType.Ranged, weight = 1.0f },
                    new PlayerBehaviorWeight { behaviorType = BehaviorType.Stealth, weight = 1.0f },
                    new PlayerBehaviorWeight { behaviorType = BehaviorType.Magic, weight = 1.0f }
                };
            }
        }

        private void SetupEventListeners()
        {
            if (playerCombat != null)
            {
                // Subscribe to combat events
                playerCombat.OnAttackPerformed += OnPlayerAttack;
                playerCombat.OnDamageTaken += OnPlayerDamageTaken;
                playerCombat.OnEnemyDefeated += OnEnemyDefeated;
            }

            if (playerController != null)
            {
                // Subscribe to movement events
                // playerController.OnMovementChanged += OnPlayerMovement;
            }
        }
        #endregion

        #region Behavior Analysis
        /// <summary>
        /// Record a player action for behavior analysis.
        /// </summary>
        public void RecordPlayerAction(ActionType actionType, Vector3 position, string context = "")
        {
            var action = new PlayerAction
            {
                actionType = actionType,
                timestamp = Time.time,
                position = position,
                context = context
            };

            recentActions.Enqueue(action);

            // Maintain queue size
            while (recentActions.Count > behaviorSampleSize)
            {
                recentActions.Dequeue();
            }

            UpdateBehaviorScores(action);
        }

        /// <summary>
        /// Update behavior analysis based on recent actions.
        /// </summary>
        private void UpdateBehaviorAnalysis()
        {
            if (Time.time - lastBehaviorUpdate < 1f) return; // Update every second

            AnalyzeBehaviorPatterns();
            UpdateBehaviorProfile();

            lastBehaviorUpdate = Time.time;
        }

        /// <summary>
        /// Analyze patterns in player behavior.
        /// </summary>
        private void AnalyzeBehaviorPatterns()
        {
            if (recentActions.Count < 5) return;

            var actions = recentActions.ToArray();

            // Analyze action frequency
            var actionCounts = new Dictionary<ActionType, int>();
            foreach (var action in actions)
            {
                if (!actionCounts.ContainsKey(action.actionType))
                    actionCounts[action.actionType] = 0;
                actionCounts[action.actionType]++;
            }

            // Update behavior scores based on action patterns
            foreach (var actionCount in actionCounts)
            {
                float frequency = (float)actionCount.Value / actions.Length;
                UpdateBehaviorScoreFromAction(actionCount.Key, frequency);
            }

            // Apply decay to behavior scores
            ApplyBehaviorDecay();
        }

        /// <summary>
        /// Update behavior scores based on specific actions.
        /// </summary>
        private void UpdateBehaviorScores(PlayerAction action)
        {
            switch (action.actionType)
            {
                case ActionType.MeleeAttack:
                    ModifyBehaviorScore(BehaviorType.Aggressive, 0.1f);
                    break;
                case ActionType.RangedAttack:
                    ModifyBehaviorScore(BehaviorType.Ranged, 0.1f);
                    break;
                case ActionType.Block:
                case ActionType.Dodge:
                    ModifyBehaviorScore(BehaviorType.Defensive, 0.1f);
                    break;
                case ActionType.Stealth:
                case ActionType.Hide:
                    ModifyBehaviorScore(BehaviorType.Stealth, 0.1f);
                    break;
                case ActionType.CastSpell:
                    ModifyBehaviorScore(BehaviorType.Magic, 0.1f);
                    break;
            }
        }

        /// <summary>
        /// Update behavior score from action frequency.
        /// </summary>
        private void UpdateBehaviorScoreFromAction(ActionType actionType, float frequency)
        {
            switch (actionType)
            {
                case ActionType.MeleeAttack:
                    ModifyBehaviorScore(BehaviorType.Aggressive, frequency * 0.5f);
                    break;
                case ActionType.RangedAttack:
                    ModifyBehaviorScore(BehaviorType.Ranged, frequency * 0.5f);
                    break;
                case ActionType.Block:
                case ActionType.Dodge:
                    ModifyBehaviorScore(BehaviorType.Defensive, frequency * 0.5f);
                    break;
                case ActionType.Stealth:
                    ModifyBehaviorScore(BehaviorType.Stealth, frequency * 0.5f);
                    break;
                case ActionType.CastSpell:
                    ModifyBehaviorScore(BehaviorType.Magic, frequency * 0.5f);
                    break;
            }
        }

        /// <summary>
        /// Modify behavior score with bounds checking.
        /// </summary>
        private void ModifyBehaviorScore(BehaviorType behaviorType, float change)
        {
            if (!behaviorScores.ContainsKey(behaviorType))
                behaviorScores[behaviorType] = 0f;

            behaviorScores[behaviorType] = Mathf.Clamp01(behaviorScores[behaviorType] + change);
        }

        /// <summary>
        /// Apply decay to behavior scores over time.
        /// </summary>
        private void ApplyBehaviorDecay()
        {
            var keys = behaviorScores.Keys.ToList();
            foreach (var key in keys)
            {
                behaviorScores[key] = Mathf.Max(0f, behaviorScores[key] - adaptationDecayRate * Time.deltaTime);
            }
        }

        /// <summary>
        /// Update the current behavior profile.
        /// </summary>
        private void UpdateBehaviorProfile()
        {
            var newProfile = new PlayerBehaviorProfile
            {
                aggressiveness = behaviorScores.GetValueOrDefault(BehaviorType.Aggressive, 0f),
                defensiveness = behaviorScores.GetValueOrDefault(BehaviorType.Defensive, 0f),
                rangedPreference = behaviorScores.GetValueOrDefault(BehaviorType.Ranged, 0f),
                stealthiness = behaviorScores.GetValueOrDefault(BehaviorType.Stealth, 0f),
                magicUsage = behaviorScores.GetValueOrDefault(BehaviorType.Magic, 0f),
                dominantBehavior = GetDominantBehavior()
            };

            if (!CurrentBehaviorProfile.Equals(newProfile))
            {
                CurrentBehaviorProfile = newProfile;
                OnBehaviorProfileChanged?.Invoke(CurrentBehaviorProfile);

                if (showDebugInfo)
                {
                    Debug.Log($"Behavior profile updated: {CurrentBehaviorProfile.dominantBehavior}");
                }
            }
        }

        /// <summary>
        /// Get the dominant behavior type.
        /// </summary>
        private BehaviorType GetDominantBehavior()
        {
            if (behaviorScores.Count == 0) return BehaviorType.Aggressive;

            return behaviorScores.OrderByDescending(kvp => kvp.Value).First().Key;
        }
        #endregion

        #region AI Adaptation
        /// <summary>
        /// Update AI adaptations based on player behavior.
        /// </summary>
        private void UpdateAIAdaptations()
        {
            foreach (var enemyAI in FindObjectsOfType<EnemyAI>())
            {
                if (enemyAI == null) continue;

                string enemyId = enemyAI.GetInstanceID().ToString();

                if (!aiAdaptations.ContainsKey(enemyId))
                {
                    aiAdaptations[enemyId] = new AIAdaptationData();
                }

                UpdateEnemyAdaptation(enemyAI, aiAdaptations[enemyId]);
            }
        }

        /// <summary>
        /// Update adaptation for a specific enemy.
        /// </summary>
        private void UpdateEnemyAdaptation(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Adapt based on player's dominant behavior
            switch (CurrentBehaviorProfile.dominantBehavior)
            {
                case BehaviorType.Aggressive:
                    AdaptToAggressivePlayer(enemy, adaptationData);
                    break;
                case BehaviorType.Defensive:
                    AdaptToDefensivePlayer(enemy, adaptationData);
                    break;
                case BehaviorType.Ranged:
                    AdaptToRangedPlayer(enemy, adaptationData);
                    break;
                case BehaviorType.Stealth:
                    AdaptToStealthyPlayer(enemy, adaptationData);
                    break;
                case BehaviorType.Magic:
                    AdaptToMagicPlayer(enemy, adaptationData);
                    break;
            }
        }

        /// <summary>
        /// Adapt AI to aggressive player behavior.
        /// </summary>
        private void AdaptToAggressivePlayer(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Increase defensive behaviors
            adaptationData.blockChance = Mathf.Min(0.8f, adaptationData.blockChance + adaptationRate);
            adaptationData.dodgeChance = Mathf.Min(0.6f, adaptationData.dodgeChance + adaptationRate);
            adaptationData.counterAttackChance = Mathf.Min(0.7f, adaptationData.counterAttackChance + adaptationRate);

            ApplyAdaptationToEnemy(enemy, adaptationData);
            OnAIAdaptation?.Invoke(enemy.name, AIAdaptationType.DefensiveIncrease);
        }

        /// <summary>
        /// Adapt AI to defensive player behavior.
        /// </summary>
        private void AdaptToDefensivePlayer(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Increase aggressive behaviors
            adaptationData.aggressionLevel = Mathf.Min(2.0f, adaptationData.aggressionLevel + adaptationRate);
            adaptationData.attackFrequency = Mathf.Min(2.0f, adaptationData.attackFrequency + adaptationRate);
            adaptationData.feintChance = Mathf.Min(0.5f, adaptationData.feintChance + adaptationRate);

            ApplyAdaptationToEnemy(enemy, adaptationData);
            OnAIAdaptation?.Invoke(enemy.name, AIAdaptationType.AggressionIncrease);
        }

        /// <summary>
        /// Adapt AI to ranged player behavior.
        /// </summary>
        private void AdaptToRangedPlayer(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Increase mobility and closing distance
            adaptationData.movementSpeed = Mathf.Min(2.0f, adaptationData.movementSpeed + adaptationRate);
            adaptationData.zigzagMovement = Mathf.Min(1.0f, adaptationData.zigzagMovement + adaptationRate);
            adaptationData.coverSeekingChance = Mathf.Min(0.8f, adaptationData.coverSeekingChance + adaptationRate);

            ApplyAdaptationToEnemy(enemy, adaptationData);
            OnAIAdaptation?.Invoke(enemy.name, AIAdaptationType.MobilityIncrease);
        }

        /// <summary>
        /// Adapt AI to stealthy player behavior.
        /// </summary>
        private void AdaptToStealthyPlayer(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Increase detection and alertness
            adaptationData.detectionRange = Mathf.Min(2.0f, adaptationData.detectionRange + adaptationRate);
            adaptationData.alertnessLevel = Mathf.Min(2.0f, adaptationData.alertnessLevel + adaptationRate);
            adaptationData.patrolFrequency = Mathf.Min(2.0f, adaptationData.patrolFrequency + adaptationRate);

            ApplyAdaptationToEnemy(enemy, adaptationData);
            OnAIAdaptation?.Invoke(enemy.name, AIAdaptationType.DetectionIncrease);
        }

        /// <summary>
        /// Adapt AI to magic-using player behavior.
        /// </summary>
        private void AdaptToMagicPlayer(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Increase magic resistance and interruption
            adaptationData.magicResistance = Mathf.Min(0.8f, adaptationData.magicResistance + adaptationRate);
            adaptationData.spellInterruptChance = Mathf.Min(0.6f, adaptationData.spellInterruptChance + adaptationRate);
            adaptationData.dispelChance = Mathf.Min(0.4f, adaptationData.dispelChance + adaptationRate);

            ApplyAdaptationToEnemy(enemy, adaptationData);
            OnAIAdaptation?.Invoke(enemy.name, AIAdaptationType.MagicCounterIncrease);
        }

        /// <summary>
        /// Apply adaptation data to enemy AI.
        /// </summary>
        private void ApplyAdaptationToEnemy(EnemyAI enemy, AIAdaptationData adaptationData)
        {
            // Apply adaptations to enemy AI
            // This would integrate with the existing EnemyAI system
            if (showDebugInfo)
            {
                Debug.Log($"Applied adaptations to {enemy.name}");
            }
        }
        #endregion

        #region Dynamic Difficulty
        /// <summary>
        /// Update dynamic difficulty based on player performance.
        /// </summary>
        private void UpdateDynamicDifficulty()
        {
            if (!enableDynamicDifficulty) return;
            if (Time.time - lastDifficultyUpdate < 30f) return; // Update every 30 seconds

            float currentWinRate = CalculateRecentWinRate();
            float targetDifference = currentWinRate - targetWinRate;

            if (Mathf.Abs(targetDifference) > 0.1f)
            {
                float adjustment = -targetDifference * difficultyAdjustmentRate;
                CurrentDifficultyMultiplier = Mathf.Clamp(
                    CurrentDifficultyMultiplier + adjustment,
                    difficultyRange.x,
                    difficultyRange.y
                );

                OnDifficultyChanged?.Invoke(CurrentDifficultyMultiplier);

                if (showDebugInfo)
                {
                    Debug.Log($"Difficulty adjusted to: {CurrentDifficultyMultiplier:F2} (Win rate: {currentWinRate:F2})");
                }
            }

            lastDifficultyUpdate = Time.time;
        }

        /// <summary>
        /// Calculate recent win rate for difficulty adjustment.
        /// </summary>
        private float CalculateRecentWinRate()
        {
            if (recentEncounters.Count == 0) return targetWinRate;

            var recentEncountersInTimeframe = recentEncounters
                .Where(e => Time.time - e.timestamp < 300f) // Last 5 minutes
                .ToList();

            if (recentEncountersInTimeframe.Count == 0) return targetWinRate;

            int wins = recentEncountersInTimeframe.Count(e => e.playerWon);
            return (float)wins / recentEncountersInTimeframe.Count;
        }

        /// <summary>
        /// Record a combat encounter for difficulty tracking.
        /// </summary>
        public void RecordCombatEncounter(string enemyType, bool playerWon, float duration)
        {
            var encounter = new CombatEncounter
            {
                enemyType = enemyType,
                playerWon = playerWon,
                duration = duration,
                timestamp = Time.time,
                playerBehavior = CurrentBehaviorProfile.dominantBehavior
            };

            recentEncounters.Add(encounter);

            // Maintain list size
            while (recentEncounters.Count > 50)
            {
                recentEncounters.RemoveAt(0);
            }
        }
        #endregion

        #region NPC Reactions
        /// <summary>
        /// Update NPC reactions based on reputation and player actions.
        /// </summary>
        private void UpdateNPCReactions()
        {
            if (!enableNPCReactions || narrativeSystem == null) return;

            var nearbyNPCs = FindNearbyNPCs();
            foreach (var npc in nearbyNPCs)
            {
                UpdateNPCReaction(npc);
            }
        }

        /// <summary>
        /// Find NPCs within reaction range.
        /// </summary>
        private NPCController[] FindNearbyNPCs()
        {
            var allNPCs = FindObjectsOfType<NPCController>();
            var playerPosition = playerController != null ? playerController.transform.position : Vector3.zero;

            return allNPCs.Where(npc =>
                Vector3.Distance(npc.transform.position, playerPosition) <= reputationInfluenceRange
            ).ToArray();
        }

        /// <summary>
        /// Update reaction for a specific NPC.
        /// </summary>
        private void UpdateNPCReaction(NPCController npc)
        {
            string npcId = npc.GetInstanceID().ToString();

            if (!npcReactionStates.ContainsKey(npcId))
            {
                npcReactionStates[npcId] = new NPCReactionState
                {
                    npcId = npcId,
                    currentReaction = NPCReactionType.Neutral,
                    lastUpdateTime = Time.time
                };
            }

            var reactionState = npcReactionStates[npcId];
            var newReaction = CalculateNPCReaction(npc, reactionState);

            if (newReaction != reactionState.currentReaction)
            {
                reactionState.currentReaction = newReaction;
                reactionState.lastUpdateTime = Time.time;

                ApplyNPCReaction(npc, newReaction);
                OnNPCReactionChanged?.Invoke(npcId, newReaction);

                if (showDebugInfo)
                {
                    Debug.Log($"NPC {npc.name} reaction changed to: {newReaction}");
                }
            }
        }

        /// <summary>
        /// Calculate appropriate NPC reaction.
        /// </summary>
        private NPCReactionType CalculateNPCReaction(NPCController npc, NPCReactionState currentState)
        {
            float globalReputation = narrativeSystem.GlobalReputation;

            // Get faction-specific reputation if applicable
            string npcFaction = GetNPCFaction(npc);
            float factionReputation = !string.IsNullOrEmpty(npcFaction) ?
                narrativeSystem.GetFactionReputation(npcFaction) : globalReputation;

            // Calculate reaction based on reputation
            if (factionReputation >= 60f) return NPCReactionType.Respectful;
            if (factionReputation >= 30f) return NPCReactionType.Friendly;
            if (factionReputation >= -30f) return NPCReactionType.Neutral;
            if (factionReputation >= -60f) return NPCReactionType.Suspicious;
            if (factionReputation >= -80f) return NPCReactionType.Hostile;
            return NPCReactionType.Fearful;
        }

        /// <summary>
        /// Get NPC faction identifier.
        /// </summary>
        private string GetNPCFaction(NPCController npc)
        {
            // This would integrate with the NPC system to get faction info
            return ""; // Placeholder
        }

        /// <summary>
        /// Apply reaction to NPC behavior.
        /// </summary>
        private void ApplyNPCReaction(NPCController npc, NPCReactionType reaction)
        {
            // This would integrate with the NPC system to modify behavior
            // For example, changing dialogue options, movement patterns, etc.
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handle player attack events.
        /// </summary>
        private void OnPlayerAttack(string attackType, Vector3 position)
        {
            ActionType actionType = attackType.Contains("Ranged") ? ActionType.RangedAttack : ActionType.MeleeAttack;
            RecordPlayerAction(actionType, position, attackType);
        }

        /// <summary>
        /// Handle player damage taken events.
        /// </summary>
        private void OnPlayerDamageTaken(float damage, Vector3 position)
        {
            // Could indicate defensive failure or aggressive playstyle
            RecordPlayerAction(ActionType.Move, position, "damage_taken");
        }

        /// <summary>
        /// Handle enemy defeated events.
        /// </summary>
        private void OnEnemyDefeated(string enemyType, float combatDuration)
        {
            RecordCombatEncounter(enemyType, true, combatDuration);
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save AI adaptation data.
        /// </summary>
        public void SaveAdaptationData()
        {
            var saveData = new ReactiveAISaveData
            {
                currentDifficultyMultiplier = CurrentDifficultyMultiplier,
                behaviorScores = new Dictionary<BehaviorType, float>(behaviorScores),
                adaptationLevels = new Dictionary<string, float>(AdaptationLevels)
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("ReactiveAIData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load AI adaptation data.
        /// </summary>
        private void LoadAdaptationData()
        {
            string json = PlayerPrefs.GetString("ReactiveAIData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<ReactiveAISaveData>(json);
                    CurrentDifficultyMultiplier = saveData.currentDifficultyMultiplier;

                    if (saveData.behaviorScores != null)
                    {
                        behaviorScores = new Dictionary<BehaviorType, float>(saveData.behaviorScores);
                    }

                    if (saveData.adaptationLevels != null)
                    {
                        AdaptationLevels = new Dictionary<string, float>(saveData.adaptationLevels);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load reactive AI data: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get current difficulty multiplier.
        /// </summary>
        public float GetDifficultyMultiplier()
        {
            return CurrentDifficultyMultiplier;
        }

        /// <summary>
        /// Set difficulty multiplier manually.
        /// </summary>
        public void SetDifficultyMultiplier(float multiplier)
        {
            CurrentDifficultyMultiplier = Mathf.Clamp(multiplier, difficultyRange.x, difficultyRange.y);
            OnDifficultyChanged?.Invoke(CurrentDifficultyMultiplier);
        }

        /// <summary>
        /// Get adaptation level for specific AI type.
        /// </summary>
        public float GetAdaptationLevel(string aiType)
        {
            return AdaptationLevels.GetValueOrDefault(aiType, 0f);
        }

        /// <summary>
        /// Reset all adaptations.
        /// </summary>
        public void ResetAdaptations()
        {
            aiAdaptations.Clear();
            AdaptationLevels.Clear();
            behaviorScores.Clear();
            recentActions.Clear();
            recentEncounters.Clear();
            npcReactionStates.Clear();

            CurrentDifficultyMultiplier = 1.0f;
            InitializeBehaviorTracking();

            Debug.Log("Reactive AI system reset");
        }

        /// <summary>
        /// Get recent combat statistics.
        /// </summary>
        public CombatStats GetCombatStats()
        {
            var recentCombats = recentEncounters.Where(e => Time.time - e.timestamp < 300f).ToList();

            return new CombatStats
            {
                totalEncounters = recentCombats.Count,
                wins = recentCombats.Count(e => e.playerWon),
                losses = recentCombats.Count(e => !e.playerWon),
                winRate = recentCombats.Count > 0 ? (float)recentCombats.Count(e => e.playerWon) / recentCombats.Count : 0f,
                averageCombatDuration = recentCombats.Count > 0 ? recentCombats.Average(e => e.duration) : 0f
            };
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class ReactiveAISaveData
    {
        public float currentDifficultyMultiplier;
        public Dictionary<BehaviorType, float> behaviorScores;
        public Dictionary<string, float> adaptationLevels;
    }

    [System.Serializable]
    public class CombatStats
    {
        public int totalEncounters;
        public int wins;
        public int losses;
        public float winRate;
        public float averageCombatDuration;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class PlayerBehaviorProfile
    {
        public float aggressiveness;
        public float defensiveness;
        public float rangedPreference;
        public float stealthiness;
        public float magicUsage;
        public BehaviorType dominantBehavior;

        public bool Equals(PlayerBehaviorProfile other)
        {
            if (other == null) return false;
            return Mathf.Approximately(aggressiveness, other.aggressiveness) &&
                   Mathf.Approximately(defensiveness, other.defensiveness) &&
                   Mathf.Approximately(rangedPreference, other.rangedPreference) &&
                   Mathf.Approximately(stealthiness, other.stealthiness) &&
                   Mathf.Approximately(magicUsage, other.magicUsage) &&
                   dominantBehavior == other.dominantBehavior;
        }
    }

    [System.Serializable]
    public class PlayerAction
    {
        public ActionType actionType;
        public float timestamp;
        public Vector3 position;
        public string context;
    }

    [System.Serializable]
    public class AIAdaptationData
    {
        [Header("Combat Adaptations")]
        public float aggressionLevel = 1.0f;
        public float attackFrequency = 1.0f;
        public float blockChance = 0.3f;
        public float dodgeChance = 0.2f;
        public float counterAttackChance = 0.1f;
        public float feintChance = 0.1f;

        [Header("Movement Adaptations")]
        public float movementSpeed = 1.0f;
        public float zigzagMovement = 0.0f;
        public float coverSeekingChance = 0.2f;

        [Header("Detection Adaptations")]
        public float detectionRange = 1.0f;
        public float alertnessLevel = 1.0f;
        public float patrolFrequency = 1.0f;

        [Header("Magic Adaptations")]
        public float magicResistance = 0.0f;
        public float spellInterruptChance = 0.1f;
        public float dispelChance = 0.0f;
    }

    [System.Serializable]
    public class PlayerBehaviorWeight
    {
        public BehaviorType behaviorType;
        public float weight = 1.0f;
    }

    [System.Serializable]
    public class NPCReactionProfile
    {
        public string npcId;
        public float reputationSensitivity = 1.0f;
        public NPCReactionType[] possibleReactions;
    }

    [System.Serializable]
    public class NPCReactionState
    {
        public string npcId;
        public NPCReactionType currentReaction;
        public float lastUpdateTime;
    }

    [System.Serializable]
    public class CombatEncounter
    {
        public string enemyType;
        public bool playerWon;
        public float duration;
        public float timestamp;
        public BehaviorType playerBehavior;
    }

    public enum BehaviorType
    {
        Aggressive,
        Defensive,
        Ranged,
        Stealth,
        Magic
    }

    public enum ActionType
    {
        MeleeAttack,
        RangedAttack,
        Block,
        Dodge,
        Stealth,
        Hide,
        CastSpell,
        UseItem,
        Move,
        Interact
    }

    public enum AIAdaptationType
    {
        AggressionIncrease,
        DefensiveIncrease,
        MobilityIncrease,
        DetectionIncrease,
        MagicCounterIncrease
    }

    public enum NPCReactionType
    {
        Friendly,
        Neutral,
        Suspicious,
        Hostile,
        Fearful,
        Respectful
    }
    #endregion
}
