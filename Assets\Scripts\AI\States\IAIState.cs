using UnityEngine;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Interface for AI states in the state machine.
    /// Defines the contract for all AI behavior states.
    /// </summary>
    public interface IAIState
    {
        /// <summary>
        /// Type of this AI state.
        /// </summary>
        AIStateType StateType { get; }

        /// <summary>
        /// Called when entering this state.
        /// </summary>
        void OnEnter();

        /// <summary>
        /// Called every frame while in this state.
        /// </summary>
        /// <returns>Next state to transition to, or null to stay in current state</returns>
        AIStateType? OnUpdate();

        /// <summary>
        /// Called when exiting this state.
        /// </summary>
        void OnExit();
    }

    /// <summary>
    /// Base implementation for AI states with common functionality.
    /// </summary>
    public abstract class BaseAIState : IAIState
    {
        #region Properties
        public abstract AIStateType StateType { get; }
        protected AIBlackboard Blackboard { get; private set; }
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize base AI state with blackboard reference.
        /// </summary>
        /// <param name="blackboard">Shared AI blackboard</param>
        protected BaseAIState(AIBlackboard blackboard)
        {
            Blackboard = blackboard;
        }
        #endregion

        #region State Interface
        /// <summary>
        /// Called when entering this state.
        /// </summary>
        public virtual void OnEnter()
        {
            Blackboard.StateEnterTime = Time.time;
            Blackboard.CurrentStateType = StateType;
        }

        /// <summary>
        /// Called every frame while in this state.
        /// </summary>
        /// <returns>Next state to transition to, or null to stay in current state</returns>
        public abstract AIStateType? OnUpdate();

        /// <summary>
        /// Called when exiting this state.
        /// </summary>
        public virtual void OnExit()
        {
            // Base implementation - can be overridden
        }
        #endregion

        #region Common State Transitions
        /// <summary>
        /// Check for common state transitions that apply to most states.
        /// </summary>
        /// <returns>State to transition to, or null if no transition needed</returns>
        protected virtual AIStateType? CheckCommonTransitions()
        {
            // Death check (highest priority)
            if (Blackboard.IsDead)
            {
                return AIStateType.Dead;
            }

            // Stun check
            if (Blackboard.IsStunned)
            {
                return AIStateType.Stunned;
            }

            // Flee check (if health is low and not already fleeing)
            if (Blackboard.ShouldFlee() && StateType != AIStateType.Flee)
            {
                return AIStateType.Flee;
            }

            return null;
        }

        /// <summary>
        /// Check for combat-related state transitions.
        /// </summary>
        /// <returns>Combat state to transition to, or null</returns>
        protected virtual AIStateType? CheckCombatTransitions()
        {
            // Skip combat transitions if dead, stunned, or fleeing
            if (Blackboard.IsDead || Blackboard.IsStunned || Blackboard.ShouldFlee())
            {
                return null;
            }

            // Attack if target is in range and can attack
            if (Blackboard.IsPlayerDetected && Blackboard.IsTargetInAttackRange() && Blackboard.CanAttack)
            {
                return AIStateType.Attack;
            }

            // Chase if target is detected but not in attack range
            if (Blackboard.IsPlayerDetected && !Blackboard.IsTargetInAttackRange())
            {
                return AIStateType.Chase;
            }

            return null;
        }

        /// <summary>
        /// Check for investigation state transitions.
        /// </summary>
        /// <returns>Investigation state to transition to, or null</returns>
        protected virtual AIStateType? CheckInvestigationTransitions()
        {
            // Investigate if there's something to investigate and not in combat
            if (!Blackboard.IsPlayerDetected && !Blackboard.IsInCombat && 
                Blackboard.InvestigationTarget != Vector3.zero && !Blackboard.IsInvestigating)
            {
                return AIStateType.Investigate;
            }

            return null;
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Look at target smoothly.
        /// </summary>
        /// <param name="target">Target to look at</param>
        /// <param name="rotationSpeed">Rotation speed</param>
        protected void LookAtTarget(Vector3 target, float rotationSpeed = 2f)
        {
            if (Blackboard.Transform == null) return;

            Vector3 direction = (target - Blackboard.Transform.position).normalized;
            direction.y = 0; // Keep rotation on Y-axis only

            if (direction != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(direction);
                Blackboard.Transform.rotation = Quaternion.Slerp(
                    Blackboard.Transform.rotation, 
                    targetRotation, 
                    rotationSpeed * Time.deltaTime
                );
            }
        }

        /// <summary>
        /// Update movement animation based on agent velocity.
        /// </summary>
        protected void UpdateMovementAnimation()
        {
            if (Blackboard.Agent != null && Blackboard.Animator != null)
            {
                float speed = Blackboard.Agent.velocity.magnitude;
                Blackboard.SetAnimationFloat(AIBlackboard.SpeedHash, speed);
            }
        }

        /// <summary>
        /// Check if state has been active for minimum duration.
        /// </summary>
        /// <param name="minDuration">Minimum duration in seconds</param>
        /// <returns>True if state has been active long enough</returns>
        protected bool HasBeenActiveFor(float minDuration)
        {
            return Blackboard.HasBeenInStateFor(minDuration);
        }

        /// <summary>
        /// Get distance to a position.
        /// </summary>
        /// <param name="position">Target position</param>
        /// <returns>Distance to position</returns>
        protected float GetDistanceTo(Vector3 position)
        {
            if (Blackboard.Transform == null) return float.MaxValue;
            return Vector3.Distance(Blackboard.Transform.position, position);
        }

        /// <summary>
        /// Check if position is reachable via NavMesh.
        /// </summary>
        /// <param name="position">Position to check</param>
        /// <returns>True if position is reachable</returns>
        protected bool IsPositionReachable(Vector3 position)
        {
            if (Blackboard.Agent == null) return false;

            UnityEngine.AI.NavMeshPath path = new UnityEngine.AI.NavMeshPath();
            return Blackboard.Agent.CalculatePath(position, path) && path.status == UnityEngine.AI.NavMeshPathStatus.PathComplete;
        }

        /// <summary>
        /// Get random position around current location.
        /// </summary>
        /// <param name="radius">Search radius</param>
        /// <returns>Random valid position</returns>
        protected Vector3 GetRandomPositionAround(float radius)
        {
            if (Blackboard.Transform == null) return Vector3.zero;

            Vector2 randomCircle = Random.insideUnitCircle * radius;
            Vector3 randomPosition = Blackboard.Transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);

            // Sample NavMesh to ensure valid position
            if (UnityEngine.AI.NavMesh.SamplePosition(randomPosition, out UnityEngine.AI.NavMeshHit hit, radius, UnityEngine.AI.NavMesh.AllAreas))
            {
                return hit.position;
            }

            return Blackboard.Transform.position;
        }
        #endregion
    }
}
