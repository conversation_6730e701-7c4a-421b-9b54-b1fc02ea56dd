using UnityEngine;

/// <summary>
/// Core arena editor functionality for Cinder of Darkness
/// Centralizes arena editing operations and provides a clean API
/// </summary>
public class ArenaEditorCore : MonoBehaviour
{
    [Header("Editor Components")]
    public ArenaEditorManager editorManager;
    public ArenaObjectPalette objectPalette;
    public ArenaTester arenaTester;
    
    [Header("Editor Settings")]
    public bool enableGizmos = true;
    public bool enableGridSnap = true;
    public float gridSize = 1f;
    public KeyCode toggleEditorKey = KeyCode.F4;
    
    // Static instance for easy access
    private static ArenaEditorCore instance;
    public static ArenaEditorCore Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaEditorCore>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaEditorCore");
                    instance = go.AddComponent<ArenaEditorCore>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeArenaEditor();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupComponents();
    }
    
    void InitializeArenaEditor()
    {
        Debug.Log("Arena Editor Core initialized");
    }
    
    void SetupComponents()
    {
        // Find components if not assigned
        if (editorManager == null)
            editorManager = FindObjectOfType<ArenaEditorManager>();
        
        if (objectPalette == null)
            objectPalette = FindObjectOfType<ArenaObjectPalette>();
        
        if (arenaTester == null)
            arenaTester = FindObjectOfType<ArenaTester>();
        
        // Apply settings
        if (editorManager != null)
        {
            editorManager.enableGridSnap = enableGridSnap;
            editorManager.gridSnapSize = gridSize;
        }
    }
    
    void Update()
    {
        // Handle global arena editor input
        if (Input.GetKeyDown(toggleEditorKey))
        {
            ToggleArenaEditor();
        }
    }
    
    // Public API
    public static void OpenArenaEditor()
    {
        if (Instance.editorManager != null)
        {
            Instance.editorManager.OpenArenaEditor();
        }
    }
    
    public static void CloseArenaEditor()
    {
        if (Instance.editorManager != null)
        {
            Instance.editorManager.CloseArenaEditor();
        }
    }
    
    public static void ToggleArenaEditor()
    {
        if (Instance.editorManager != null)
        {
            if (Instance.editorManager.IsArenaEditorOpen())
                Instance.editorManager.CloseArenaEditor();
            else
                Instance.editorManager.OpenArenaEditor();
        }
    }
    
    public static void TestCurrentArena()
    {
        if (Instance.editorManager != null)
        {
            var currentArena = Instance.editorManager.GetCurrentArena();
            if (currentArena != null && Instance.arenaTester != null)
            {
                ArenaTester.TestArena(currentArena);
            }
        }
    }
    
    public static bool IsArenaEditorActive()
    {
        return Instance.editorManager != null && Instance.editorManager.IsArenaEditorOpen();
    }
    
    public static bool IsArenaTestingActive()
    {
        return Instance.arenaTester != null && ArenaTester.IsTestingActive();
    }
    
    // Settings
    public static void SetGridSnap(bool enabled)
    {
        Instance.enableGridSnap = enabled;
        if (Instance.editorManager != null)
        {
            Instance.editorManager.enableGridSnap = enabled;
        }
    }
    
    public static void SetGridSize(float size)
    {
        Instance.gridSize = size;
        if (Instance.editorManager != null)
        {
            Instance.editorManager.gridSnapSize = size;
        }
    }
    
    public static void SetGizmosEnabled(bool enabled)
    {
        Instance.enableGizmos = enabled;
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, System.Collections.Generic.Dictionary<string, object> parameters = null)
    {
        // Handle game events that affect arena editing
        switch (eventName)
        {
            case "ModToolUnlocked":
                if (parameters != null && parameters.ContainsKey("toolId"))
                {
                    string toolId = parameters["toolId"].ToString();
                    if (toolId == "arena_creator")
                    {
                        Debug.Log("Arena Creator tool unlocked!");
                    }
                }
                break;
        }
    }
}
