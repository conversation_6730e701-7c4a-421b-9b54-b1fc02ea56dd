using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Input
{
    /// <summary>
    /// Input Rebinding System for Cinder of Darkness.
    /// Supports keyboard and controller input remapping with UI auto-update.
    /// </summary>
    public class InputRebindingSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Input Settings")]
        [SerializeField] private InputActionAsset inputActions;
        [SerializeField] private bool saveBindingsAutomatically = true;
        [SerializeField] private string bindingsSaveKey = "CinderInputBindings";

        [Header("UI References")]
        [SerializeField] private Transform rebindingContainer;
        [SerializeField] private GameObject rebindingButtonPrefab;
        [SerializeField] private GameObject rebindingOverlay;
        [SerializeField] private TextMeshProUGUI rebindingPromptText;
        [SerializeField] private Button cancelRebindButton;

        [Header("Audio")]
        [SerializeField] private AudioClip rebindSuccessSound;
        [SerializeField] private AudioClip rebindCancelSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Public Properties
        public static InputRebindingSystem Instance { get; private set; }
        public bool IsRebinding { get; private set; }
        public InputActionMap CurrentActionMap { get; private set; }
        #endregion

        #region Private Fields
        private Dictionary<string, RebindingButton> rebindingButtons = new Dictionary<string, RebindingButton>();
        private InputActionRebindingExtensions.RebindingOperation currentRebindOperation;
        private string currentActionName;
        private int currentBindingIndex;
        private GamepadManager gamepadManager;
        private EnhancedButtonPromptSystem buttonPromptSystem;

        // Excluded bindings that shouldn't be rebindable
        private readonly string[] excludedActions = { "UI/Navigate", "UI/Submit", "UI/Cancel" };
        #endregion

        #region Events
        public System.Action<string, string> OnBindingChanged;
        public System.Action OnRebindingStarted;
        public System.Action OnRebindingCompleted;
        public System.Action OnRebindingCancelled;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Input Rebinding System singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeRebindingSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup UI and load saved bindings.
        /// </summary>
        private void Start()
        {
            LoadSavedBindings();
            SetupRebindingUI();
            SetupEventListeners();
        }

        /// <summary>
        /// Cleanup on destroy.
        /// </summary>
        private void OnDestroy()
        {
            CleanupRebindOperation();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize rebinding system components.
        /// </summary>
        private void InitializeRebindingSystem()
        {
            if (inputActions == null)
            {
                Debug.LogError("Input Actions asset not assigned to InputRebindingSystem!");
                return;
            }

            gamepadManager = GamepadManager.Instance;
            buttonPromptSystem = EnhancedButtonPromptSystem.Instance;

            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Enable input actions
            inputActions.Enable();
        }

        /// <summary>
        /// Setup rebinding UI elements.
        /// </summary>
        private void SetupRebindingUI()
        {
            if (rebindingContainer == null || rebindingButtonPrefab == null)
            {
                Debug.LogWarning("Rebinding UI components not assigned!");
                return;
            }

            CreateRebindingButtons();
            UpdateAllButtonDisplays();
        }

        /// <summary>
        /// Setup event listeners.
        /// </summary>
        private void SetupEventListeners()
        {
            if (cancelRebindButton != null)
            {
                cancelRebindButton.onClick.AddListener(CancelRebinding);
            }

            // Listen for gamepad connection changes
            if (gamepadManager != null)
            {
                gamepadManager.OnGamepadConnected += OnGamepadConnectionChanged;
                gamepadManager.OnGamepadDisconnected += OnGamepadConnectionChanged;
            }
        }
        #endregion

        #region UI Creation
        /// <summary>
        /// Create rebinding buttons for all actions.
        /// </summary>
        private void CreateRebindingButtons()
        {
            // Clear existing buttons
            foreach (Transform child in rebindingContainer)
            {
                Destroy(child.gameObject);
            }
            rebindingButtons.Clear();

            // Create buttons for each action map
            foreach (var actionMap in inputActions.actionMaps)
            {
                if (actionMap.name == "UI") continue; // Skip UI actions

                CurrentActionMap = actionMap;
                CreateActionMapSection(actionMap);
            }
        }

        /// <summary>
        /// Create UI section for an action map.
        /// </summary>
        /// <param name="actionMap">Action map to create section for</param>
        private void CreateActionMapSection(InputActionMap actionMap)
        {
            // Create section header
            GameObject headerObject = new GameObject($"{actionMap.name}_Header");
            headerObject.transform.SetParent(rebindingContainer);

            TextMeshProUGUI headerText = headerObject.AddComponent<TextMeshProUGUI>();
            headerText.text = FormatActionMapName(actionMap.name);
            headerText.fontSize = 24;
            headerText.fontStyle = FontStyles.Bold;

            // Create buttons for each action
            foreach (var action in actionMap.actions)
            {
                if (IsActionExcluded(action.name)) continue;

                CreateRebindingButtonsForAction(action);
            }
        }

        /// <summary>
        /// Create rebinding buttons for a specific action.
        /// </summary>
        /// <param name="action">Input action</param>
        private void CreateRebindingButtonsForAction(InputAction action)
        {
            for (int i = 0; i < action.bindings.Count; i++)
            {
                var binding = action.bindings[i];

                // Skip composite bindings
                if (binding.isComposite) continue;

                CreateRebindingButton(action, i);
            }
        }

        /// <summary>
        /// Create a single rebinding button.
        /// </summary>
        /// <param name="action">Input action</param>
        /// <param name="bindingIndex">Binding index</param>
        private void CreateRebindingButton(InputAction action, int bindingIndex)
        {
            GameObject buttonObject = Instantiate(rebindingButtonPrefab, rebindingContainer);
            RebindingButton rebindButton = buttonObject.GetComponent<RebindingButton>();

            if (rebindButton == null)
            {
                rebindButton = buttonObject.AddComponent<RebindingButton>();
            }

            string buttonKey = $"{action.name}_{bindingIndex}";
            rebindButton.Initialize(action, bindingIndex, this);
            rebindingButtons[buttonKey] = rebindButton;

            // Setup button click event
            Button button = buttonObject.GetComponent<Button>();
            if (button != null)
            {
                button.onClick.AddListener(() => StartRebinding(action.name, bindingIndex));
            }
        }
        #endregion

        #region Rebinding Operations
        /// <summary>
        /// Start rebinding process for an action.
        /// </summary>
        /// <param name="actionName">Name of the action to rebind</param>
        /// <param name="bindingIndex">Index of the binding to rebind</param>
        public void StartRebinding(string actionName, int bindingIndex)
        {
            if (IsRebinding) return;

            var action = inputActions.FindAction(actionName);
            if (action == null)
            {
                Debug.LogError($"Action '{actionName}' not found!");
                return;
            }

            IsRebinding = true;
            currentActionName = actionName;
            currentBindingIndex = bindingIndex;

            // Show rebinding overlay
            if (rebindingOverlay != null)
            {
                rebindingOverlay.SetActive(true);
            }

            // Update prompt text
            UpdateRebindingPrompt(action, bindingIndex);

            // Disable the action temporarily
            action.Disable();

            // Start rebinding operation
            currentRebindOperation = action.PerformInteractiveRebinding(bindingIndex)
                .WithCancelingThrough("<Keyboard>/escape")
                .WithCancelingThrough("<Gamepad>/start")
                .OnMatchWaitForAnother(0.1f)
                .OnComplete(operation => OnRebindComplete())
                .OnCancel(operation => OnRebindCancel())
                .Start();

            OnRebindingStarted?.Invoke();
        }

        /// <summary>
        /// Complete rebinding operation.
        /// </summary>
        private void OnRebindComplete()
        {
            CleanupRebindOperation();

            // Update button display
            string buttonKey = $"{currentActionName}_{currentBindingIndex}";
            if (rebindingButtons.ContainsKey(buttonKey))
            {
                rebindingButtons[buttonKey].UpdateDisplay();
            }

            // Save bindings
            if (saveBindingsAutomatically)
            {
                SaveBindings();
            }

            // Play success sound
            PlaySound(rebindSuccessSound);

            // Update button prompts
            if (buttonPromptSystem != null)
            {
                buttonPromptSystem.RefreshAllPrompts();
            }

            OnBindingChanged?.Invoke(currentActionName, GetBindingDisplayString(currentActionName, currentBindingIndex));
            OnRebindingCompleted?.Invoke();

            Debug.Log($"Rebinding completed for {currentActionName}");
        }

        /// <summary>
        /// Cancel rebinding operation.
        /// </summary>
        private void OnRebindCancel()
        {
            CleanupRebindOperation();
            PlaySound(rebindCancelSound);
            OnRebindingCancelled?.Invoke();
            Debug.Log("Rebinding cancelled");
        }

        /// <summary>
        /// Cancel current rebinding operation.
        /// </summary>
        public void CancelRebinding()
        {
            if (currentRebindOperation != null)
            {
                currentRebindOperation.Cancel();
            }
        }

        /// <summary>
        /// Cleanup rebinding operation.
        /// </summary>
        private void CleanupRebindOperation()
        {
            if (currentRebindOperation != null)
            {
                currentRebindOperation.Dispose();
                currentRebindOperation = null;
            }

            // Re-enable the action
            if (!string.IsNullOrEmpty(currentActionName))
            {
                var action = inputActions.FindAction(currentActionName);
                action?.Enable();
            }

            // Hide rebinding overlay
            if (rebindingOverlay != null)
            {
                rebindingOverlay.SetActive(false);
            }

            IsRebinding = false;
            currentActionName = null;
            currentBindingIndex = -1;
        }
        #endregion

        #region Save/Load Bindings
        /// <summary>
        /// Save current bindings to PlayerPrefs.
        /// </summary>
        public void SaveBindings()
        {
            try
            {
                string bindingsJson = inputActions.SaveBindingOverridesAsJson();
                PlayerPrefs.SetString(bindingsSaveKey, bindingsJson);
                PlayerPrefs.Save();
                Debug.Log("Input bindings saved successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save input bindings: {e.Message}");
            }
        }

        /// <summary>
        /// Load saved bindings from PlayerPrefs.
        /// </summary>
        public void LoadSavedBindings()
        {
            try
            {
                string bindingsJson = PlayerPrefs.GetString(bindingsSaveKey, "");
                if (!string.IsNullOrEmpty(bindingsJson))
                {
                    inputActions.LoadBindingOverridesFromJson(bindingsJson);
                    Debug.Log("Input bindings loaded successfully");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load input bindings: {e.Message}");
            }
        }

        /// <summary>
        /// Reset all bindings to defaults.
        /// </summary>
        public void ResetToDefaults()
        {
            inputActions.RemoveAllBindingOverrides();
            UpdateAllButtonDisplays();

            if (saveBindingsAutomatically)
            {
                SaveBindings();
            }

            // Update button prompts
            if (buttonPromptSystem != null)
            {
                buttonPromptSystem.RefreshAllPrompts();
            }

            Debug.Log("Input bindings reset to defaults");
        }

        /// <summary>
        /// Reset bindings for a specific action map.
        /// </summary>
        /// <param name="actionMapName">Name of the action map to reset</param>
        public void ResetActionMapToDefaults(string actionMapName)
        {
            var actionMap = inputActions.FindActionMap(actionMapName);
            if (actionMap != null)
            {
                foreach (var action in actionMap.actions)
                {
                    action.RemoveAllBindingOverrides();
                }

                UpdateActionMapButtonDisplays(actionMapName);

                if (saveBindingsAutomatically)
                {
                    SaveBindings();
                }

                Debug.Log($"Action map '{actionMapName}' reset to defaults");
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Update all button displays.
        /// </summary>
        private void UpdateAllButtonDisplays()
        {
            foreach (var button in rebindingButtons.Values)
            {
                button.UpdateDisplay();
            }
        }

        /// <summary>
        /// Update button displays for a specific action map.
        /// </summary>
        /// <param name="actionMapName">Action map name</param>
        private void UpdateActionMapButtonDisplays(string actionMapName)
        {
            foreach (var kvp in rebindingButtons)
            {
                if (kvp.Key.StartsWith(actionMapName))
                {
                    kvp.Value.UpdateDisplay();
                }
            }
        }

        /// <summary>
        /// Update rebinding prompt text.
        /// </summary>
        /// <param name="action">Target action</param>
        /// <param name="bindingIndex">Binding index</param>
        private void UpdateRebindingPrompt(InputAction action, int bindingIndex)
        {
            if (rebindingPromptText != null)
            {
                string actionName = FormatActionName(action.name);
                string deviceType = GetExpectedDeviceType(action, bindingIndex);
                rebindingPromptText.text = $"Press a {deviceType} button for '{actionName}'...";
            }
        }

        /// <summary>
        /// Get expected device type for binding.
        /// </summary>
        /// <param name="action">Input action</param>
        /// <param name="bindingIndex">Binding index</param>
        /// <returns>Device type string</returns>
        private string GetExpectedDeviceType(InputAction action, int bindingIndex)
        {
            var binding = action.bindings[bindingIndex];

            if (binding.path.Contains("Keyboard"))
                return "keyboard";
            else if (binding.path.Contains("Gamepad"))
                return "gamepad";
            else if (binding.path.Contains("Mouse"))
                return "mouse";

            return "input";
        }

        /// <summary>
        /// Format action map name for display.
        /// </summary>
        /// <param name="actionMapName">Raw action map name</param>
        /// <returns>Formatted action map name</returns>
        private string FormatActionMapName(string actionMapName)
        {
            return actionMapName.Replace("_", " ");
        }

        /// <summary>
        /// Format action name for display.
        /// </summary>
        /// <param name="actionName">Raw action name</param>
        /// <returns>Formatted action name</returns>
        private string FormatActionName(string actionName)
        {
            return actionName.Replace("_", " ").Replace("/", " - ");
        }

        /// <summary>
        /// Check if action should be excluded from rebinding.
        /// </summary>
        /// <param name="actionName">Action name to check</param>
        /// <returns>True if action should be excluded</returns>
        private bool IsActionExcluded(string actionName)
        {
            return excludedActions.Any(excluded => actionName.Contains(excluded));
        }

        /// <summary>
        /// Get binding display string for an action.
        /// </summary>
        /// <param name="actionName">Action name</param>
        /// <param name="bindingIndex">Binding index</param>
        /// <returns>Binding display string</returns>
        public string GetBindingDisplayString(string actionName, int bindingIndex)
        {
            var action = inputActions.FindAction(actionName);
            if (action != null && bindingIndex >= 0 && bindingIndex < action.bindings.Count)
            {
                return action.GetBindingDisplayString(bindingIndex);
            }
            return "Unbound";
        }

        /// <summary>
        /// Play audio feedback.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }

        /// <summary>
        /// Handle gamepad connection changes.
        /// </summary>
        /// <param name="gamepadType">Connected gamepad type</param>
        private void OnGamepadConnectionChanged(GamepadType gamepadType)
        {
            // Update button displays when gamepad connection changes
            UpdateAllButtonDisplays();

            // Refresh button prompts
            if (buttonPromptSystem != null)
            {
                buttonPromptSystem.RefreshAllPrompts();
            }
        }

        /// <summary>
        /// Get all rebindable actions.
        /// </summary>
        /// <returns>List of rebindable actions</returns>
        public List<InputAction> GetRebindableActions()
        {
            List<InputAction> actions = new List<InputAction>();

            foreach (var actionMap in inputActions.actionMaps)
            {
                if (actionMap.name == "UI") continue;

                foreach (var action in actionMap.actions)
                {
                    if (!IsActionExcluded(action.name))
                    {
                        actions.Add(action);
                    }
                }
            }

            return actions;
        }

        /// <summary>
        /// Check for binding conflicts.
        /// </summary>
        /// <param name="actionName">Action name</param>
        /// <param name="bindingIndex">Binding index</param>
        /// <returns>List of conflicting actions</returns>
        public List<string> CheckForConflicts(string actionName, int bindingIndex)
        {
            List<string> conflicts = new List<string>();
            var targetAction = inputActions.FindAction(actionName);

            if (targetAction == null) return conflicts;

            var targetBinding = targetAction.bindings[bindingIndex];

            foreach (var action in GetRebindableActions())
            {
                if (action.name == actionName) continue;

                for (int i = 0; i < action.bindings.Count; i++)
                {
                    var binding = action.bindings[i];
                    if (binding.effectivePath == targetBinding.effectivePath)
                    {
                        conflicts.Add($"{action.name} (Binding {i})");
                    }
                }
            }

            return conflicts;
        }

        /// <summary>
        /// Resolve binding conflicts by removing conflicting bindings.
        /// </summary>
        /// <param name="actionName">Action name</param>
        /// <param name="bindingIndex">Binding index</param>
        public void ResolveConflicts(string actionName, int bindingIndex)
        {
            var conflicts = CheckForConflicts(actionName, bindingIndex);

            foreach (string conflict in conflicts)
            {
                string[] parts = conflict.Split(' ');
                if (parts.Length >= 3)
                {
                    string conflictActionName = parts[0];
                    if (int.TryParse(parts[2].Trim('(', ')'), out int conflictBindingIndex))
                    {
                        var conflictAction = inputActions.FindAction(conflictActionName);
                        if (conflictAction != null)
                        {
                            conflictAction.RemoveBindingOverride(conflictBindingIndex);
                        }
                    }
                }
            }

            UpdateAllButtonDisplays();
        }
        #endregion

        #region Public API
        /// <summary>
        /// Enable or disable the rebinding system.
        /// </summary>
        /// <param name="enabled">True to enable, false to disable</param>
        public void SetRebindingEnabled(bool enabled)
        {
            gameObject.SetActive(enabled);
        }

        /// <summary>
        /// Get current binding for an action.
        /// </summary>
        /// <param name="actionName">Action name</param>
        /// <param name="bindingIndex">Binding index</param>
        /// <returns>Current binding path</returns>
        public string GetCurrentBinding(string actionName, int bindingIndex)
        {
            var action = inputActions.FindAction(actionName);
            if (action != null && bindingIndex >= 0 && bindingIndex < action.bindings.Count)
            {
                return action.bindings[bindingIndex].effectivePath;
            }
            return "";
        }

        /// <summary>
        /// Set a specific binding programmatically.
        /// </summary>
        /// <param name="actionName">Action name</param>
        /// <param name="bindingIndex">Binding index</param>
        /// <param name="bindingPath">New binding path</param>
        public void SetBinding(string actionName, int bindingIndex, string bindingPath)
        {
            var action = inputActions.FindAction(actionName);
            if (action != null)
            {
                action.ApplyBindingOverride(bindingIndex, bindingPath);

                string buttonKey = $"{actionName}_{bindingIndex}";
                if (rebindingButtons.ContainsKey(buttonKey))
                {
                    rebindingButtons[buttonKey].UpdateDisplay();
                }

                if (saveBindingsAutomatically)
                {
                    SaveBindings();
                }
            }
        }

        /// <summary>
        /// Export current bindings to JSON.
        /// </summary>
        /// <returns>JSON string of current bindings</returns>
        public string ExportBindings()
        {
            return inputActions.SaveBindingOverridesAsJson();
        }

        /// <summary>
        /// Import bindings from JSON.
        /// </summary>
        /// <param name="bindingsJson">JSON string of bindings</param>
        public void ImportBindings(string bindingsJson)
        {
            try
            {
                inputActions.LoadBindingOverridesFromJson(bindingsJson);
                UpdateAllButtonDisplays();

                if (saveBindingsAutomatically)
                {
                    SaveBindings();
                }

                Debug.Log("Bindings imported successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to import bindings: {e.Message}");
            }
        }
        #endregion
    }

    /// <summary>
    /// Individual rebinding button component.
    /// </summary>
    public class RebindingButton : MonoBehaviour
    {
        [Header("UI Components")]
        public TextMeshProUGUI actionNameText;
        public TextMeshProUGUI bindingText;
        public Image deviceIcon;

        private InputAction targetAction;
        private int bindingIndex;
        private InputRebindingSystem rebindingSystem;

        /// <summary>
        /// Initialize the rebinding button.
        /// </summary>
        /// <param name="action">Target input action</param>
        /// <param name="index">Binding index</param>
        /// <param name="system">Rebinding system reference</param>
        public void Initialize(InputAction action, int index, InputRebindingSystem system)
        {
            targetAction = action;
            bindingIndex = index;
            rebindingSystem = system;

            UpdateDisplay();
        }

        /// <summary>
        /// Update button display with current binding.
        /// </summary>
        public void UpdateDisplay()
        {
            if (targetAction == null) return;

            // Update action name
            if (actionNameText != null)
            {
                actionNameText.text = FormatActionName(targetAction.name);
            }

            // Update binding text
            if (bindingText != null)
            {
                bindingText.text = GetBindingDisplayString();
            }

            // Update device icon
            if (deviceIcon != null)
            {
                UpdateDeviceIcon();
            }
        }

        /// <summary>
        /// Get display string for current binding.
        /// </summary>
        /// <returns>Binding display string</returns>
        private string GetBindingDisplayString()
        {
            if (bindingIndex >= 0 && bindingIndex < targetAction.bindings.Count)
            {
                return targetAction.GetBindingDisplayString(bindingIndex);
            }
            return "Unbound";
        }

        /// <summary>
        /// Update device icon based on binding.
        /// </summary>
        private void UpdateDeviceIcon()
        {
            // Implementation for device icon updates
            // This would load appropriate keyboard/gamepad icons
        }

        /// <summary>
        /// Format action name for display.
        /// </summary>
        /// <param name="actionName">Raw action name</param>
        /// <returns>Formatted action name</returns>
        private string FormatActionName(string actionName)
        {
            return actionName.Replace("_", " ").Replace("/", " - ");
        }
    }
}
