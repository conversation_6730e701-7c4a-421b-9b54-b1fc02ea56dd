using UnityEngine;

public class NPCController : MonoBehaviour
{
    [Header("NPC Info")]
    public string npcName = "Unknown";
    public NPCType npcType = NPCType.Neutral;
    public string description = "";
    
    [Header("Dialogue")]
    public DialogueTree initialDialogue;
    public DialogueTree allyDialogue;
    public DialogueTree enemyDialogue;
    public DialogueTree betrayalDialogue;
    
    [Header("Relationship")]
    public float relationshipValue = 0f; // -100 to 100
    public RelationshipStatus currentStatus = RelationshipStatus.Neutral;
    public bool canBeAlly = true;
    public bool canBetray = true;
    
    [Header("Visual Settings")]
    public GameObject interactionPrompt;
    public float interactionRange = 3f;
    public Material neutralMaterial;
    public Material allyMaterial;
    public Material enemyMaterial;
    
    [Header("Combat")]
    public bool isHostile = false;
    public float attackDamage = 20f;
    public float attackRange = 2f;
    public float attackCooldown = 2f;
    
    private DialogueSystem dialogueSystem;
    private PlayerStats playerStats;
    private Transform player;
    private Renderer npcRenderer;
    private bool playerInRange = false;
    private bool hasBetrayed = false;
    private float lastAttackTime;
    
    public enum NPCType
    {
        Villager,
        Merchant,
        Guard,
        Noble,
        Outcast,
        Sage,
        Warrior
    }
    
    public enum RelationshipStatus
    {
        Enemy,
        Hostile,
        Neutral,
        Friendly,
        Ally
    }
    
    void Start()
    {
        dialogueSystem = FindObjectOfType<DialogueSystem>();
        playerStats = FindObjectOfType<PlayerStats>();
        player = FindObjectOfType<PlayerController>().transform;
        npcRenderer = GetComponent<Renderer>();
        
        if (interactionPrompt != null)
            interactionPrompt.SetActive(false);
        
        UpdateVisualAppearance();
        
        // Subscribe to dialogue events
        if (dialogueSystem != null)
        {
            dialogueSystem.OnDialogueEvent += HandleDialogueEvent;
        }
    }
    
    void Update()
    {
        CheckPlayerDistance();
        HandleInteraction();
        
        if (isHostile && playerInRange)
        {
            AttackPlayer();
        }
    }
    
    void CheckPlayerDistance()
    {
        if (player == null) return;
        
        float distance = Vector3.Distance(transform.position, player.position);
        bool wasInRange = playerInRange;
        playerInRange = distance <= interactionRange;
        
        // Show/hide interaction prompt
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(playerInRange && !isHostile);
        }
        
        // Face the player when they're nearby
        if (playerInRange && !isHostile)
        {
            Vector3 direction = (player.position - transform.position).normalized;
            direction.y = 0; // Keep NPC upright
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
        }
    }
    
    void HandleInteraction()
    {
        if (playerInRange && Input.GetKeyDown(KeyCode.E) && !isHostile)
        {
            StartDialogue();
        }
    }
    
    void StartDialogue()
    {
        if (dialogueSystem == null)
        {
            Debug.LogWarning("No dialogue system found!");
            return;
        }
        
        DialogueTree dialogueToUse = GetAppropriateDialogue();
        
        if (dialogueToUse != null)
        {
            dialogueSystem.StartDialogue(dialogueToUse);
        }
        else
        {
            Debug.LogWarning($"No dialogue available for {npcName}");
        }
    }
    
    DialogueTree GetAppropriateDialogue()
    {
        switch (currentStatus)
        {
            case RelationshipStatus.Ally:
                return allyDialogue ?? initialDialogue;
            case RelationshipStatus.Enemy:
            case RelationshipStatus.Hostile:
                return enemyDialogue ?? initialDialogue;
            default:
                return initialDialogue;
        }
    }
    
    void HandleDialogueEvent(string eventName)
    {
        switch (eventName)
        {
            case "BecomeAlly":
                BecomeAlly();
                break;
            case "BecomeEnemy":
                BecomeEnemy();
                break;
            case "Betray":
                Betray();
                break;
            case "GiveQuest":
                GiveQuest();
                break;
            case "TradeItems":
                OpenTrade();
                break;
        }
    }
    
    public void BecomeAlly()
    {
        if (!canBeAlly) return;
        
        currentStatus = RelationshipStatus.Ally;
        relationshipValue = Mathf.Max(relationshipValue, 75f);
        isHostile = false;
        
        Debug.Log($"{npcName} has become your ally!");
        UpdateVisualAppearance();
    }
    
    public void BecomeEnemy()
    {
        currentStatus = RelationshipStatus.Enemy;
        relationshipValue = Mathf.Min(relationshipValue, -75f);
        isHostile = true;
        
        Debug.Log($"{npcName} has become your enemy!");
        UpdateVisualAppearance();
    }
    
    public void Betray()
    {
        if (!canBetray || hasBetrayed) return;
        
        hasBetrayed = true;
        currentStatus = RelationshipStatus.Enemy;
        relationshipValue = -100f;
        isHostile = true;
        
        Debug.Log($"{npcName} has betrayed you!");
        
        // Start betrayal dialogue if available
        if (betrayalDialogue != null && dialogueSystem != null)
        {
            dialogueSystem.StartDialogue(betrayalDialogue);
        }
        
        UpdateVisualAppearance();
    }
    
    void GiveQuest()
    {
        Debug.Log($"{npcName} gives you a quest!");
        // Implement quest system here
    }
    
    void OpenTrade()
    {
        Debug.Log($"Opening trade with {npcName}");
        // Implement trading system here
    }
    
    void AttackPlayer()
    {
        if (Time.time < lastAttackTime + attackCooldown) return;
        
        float distanceToPlayer = Vector3.Distance(transform.position, player.position);
        if (distanceToPlayer <= attackRange)
        {
            lastAttackTime = Time.time;
            
            // Deal damage to player
            if (playerStats != null)
            {
                playerStats.TakeDamage(attackDamage);
                Debug.Log($"{npcName} attacks you for {attackDamage} damage!");
            }
        }
    }
    
    void UpdateVisualAppearance()
    {
        if (npcRenderer == null) return;
        
        Material materialToUse = neutralMaterial;
        
        switch (currentStatus)
        {
            case RelationshipStatus.Ally:
                materialToUse = allyMaterial;
                break;
            case RelationshipStatus.Enemy:
            case RelationshipStatus.Hostile:
                materialToUse = enemyMaterial;
                break;
            default:
                materialToUse = neutralMaterial;
                break;
        }
        
        if (materialToUse != null)
        {
            npcRenderer.material = materialToUse;
        }
    }
    
    public void ModifyRelationship(float amount)
    {
        relationshipValue = Mathf.Clamp(relationshipValue + amount, -100f, 100f);
        
        // Update status based on relationship value
        if (relationshipValue >= 75f)
            currentStatus = RelationshipStatus.Ally;
        else if (relationshipValue >= 25f)
            currentStatus = RelationshipStatus.Friendly;
        else if (relationshipValue <= -75f)
            currentStatus = RelationshipStatus.Enemy;
        else if (relationshipValue <= -25f)
            currentStatus = RelationshipStatus.Hostile;
        else
            currentStatus = RelationshipStatus.Neutral;
        
        UpdateVisualAppearance();
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw interaction range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionRange);
        
        // Draw attack range if hostile
        if (isHostile)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
        }
    }
}
