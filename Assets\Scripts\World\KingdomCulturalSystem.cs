using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class KingdomCulturalSystem : MonoBehaviour
{
    [Header("Cultural Kingdoms")]
    public KingdomData[] kingdoms;
    public VillageData[] independentVillages;
    public string currentKingdom = "";
    
    [Header("Cultural Interactions")]
    public float culturalAdaptationRate = 1f;
    public Dictionary<string, float> culturalStandings = new Dictionary<string, float>();
    public List<CulturalViolation> violations = new List<CulturalViolation>();
    
    [Header("Visual Systems")]
    public GameObject[] architecturalPrefabs;
    public Material[] culturalMaterials;
    public Light[] illuminationSystems;
    
    private DynamicMusicalSystem musicalSystem;
    private HostilitySystem hostilitySystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class KingdomData
    {
        [Header("Kingdom Identity")]
        public string kingdomName;
        public KingdomType type;
        public string culturalPhilosophy;
        public string[] coreBeliefs;
        
        [Header("Architecture & Design")]
        public ArchitecturalStyle architecture;
        public IlluminationType illumination;
        public Color primaryColor;
        public Color secondaryColor;
        public GameObject[] buildingPrefabs;
        
        [Header("Social Structure")]
        public SocialSystem socialSystem;
        public string[] socialRituals;
        public string[] punishments;
        public string[] rewards;
        
        [Header("Communication & Greetings")]
        public GreetingStyle greetingStyle;
        public string greetingDescription;
        public CommunicationMethod communication;
        public string[] commonPhrases;
        
        [Header("Cultural Rules")]
        public CulturalRule[] culturalRules;
        public string[] taboos;
        public string[] virtues;
        
        [Header("Relationship with Cinderborn")]
        public CinderbornPerception perception;
        public float defaultStanding;
        public string[] specialDialogues;
        
        public enum KingdomType
        {
            KingdomOfLight,
            KingdomOfShadow,
            IndependentVillage
        }
        
        public enum ArchitecturalStyle
        {
            MarbleAndGold,
            WoodAndAsh,
            StoneAndIron,
            LivingWood,
            CrystalAndLight
        }
        
        public enum IlluminationType
        {
            MirrorReflection,
            NaturalFire,
            BioluminescentFungi,
            CrystalLight,
            NoIllumination
        }
        
        public enum SocialSystem
        {
            TruthBasedHierarchy,
            WhisperCouncil,
            ElderDemocracy,
            FearBasedRule,
            AnarchicFreedom
        }
        
        public enum GreetingStyle
        {
            HandToChestThenSun,
            TouchForeheadThenGround,
            SilentBow,
            FireOffering,
            BloodPact
        }
        
        public enum CommunicationMethod
        {
            OpenSpeech,
            WhisperOnly,
            SignLanguage,
            RitualChanting,
            SilentGestures
        }
        
        public enum CinderbornPerception
        {
            Savior,
            Curse,
            TestFromGods,
            NeutralStranger,
            ProphecyFulfillment
        }
    }
    
    [System.Serializable]
    public class VillageData
    {
        [Header("Village Identity")]
        public string villageName;
        public VillageType type;
        public string uniqueBelief;
        public float isolationLevel;
        
        [Header("Fire Relationship")]
        public FireAttitude fireAttitude;
        public string fireRitual;
        public string[] fireTaboos;
        
        [Header("Stranger Policy")]
        public StrangerPolicy strangerPolicy;
        public string strangerRitual;
        public float suspicionLevel;
        
        public enum VillageType
        {
            FireRejecters,
            StrangerBurners,
            CinderbornWorshippers,
            CinderbornFearers,
            NeutralTraders,
            HermitSettlement
        }
        
        public enum FireAttitude
        {
            CompleteRejection,
            SacredOffering,
            FearfulAvoidance,
            WorshipfulReverence,
            PracticalUse
        }
        
        public enum StrangerPolicy
        {
            BurnAsOffering,
            WelcomeWithTests,
            FearfulAvoidance,
            HostileRejection,
            CautiousAcceptance
        }
    }
    
    [System.Serializable]
    public class CulturalRule
    {
        public string ruleName;
        public string ruleDescription;
        public RuleType type;
        public float violationPenalty;
        public string[] acceptableActions;
        public string[] forbiddenActions;
        
        public enum RuleType
        {
            SocialEtiquette,
            ReligiousObservance,
            CommunicationProtocol,
            HierarchyRespect,
            SacredRitual
        }
    }
    
    [System.Serializable]
    public class CulturalViolation
    {
        public string kingdomName;
        public string violationType;
        public float severity;
        public float timestamp;
        public string consequence;
    }
    
    void Start()
    {
        musicalSystem = GetComponent<DynamicMusicalSystem>();
        hostilitySystem = GetComponent<HostilitySystem>();
        gameManager = GameManager.Instance;
        
        InitializeKingdoms();
        InitializeVillages();
    }
    
    void Update()
    {
        UpdateCurrentKingdom();
        ProcessCulturalAdaptation();
        CheckCulturalViolations();
    }
    
    void InitializeKingdoms()
    {
        // Kingdom of Light - Sun Followers
        KingdomData lightKingdom = new KingdomData
        {
            kingdomName = "Kingdom of Light",
            type = KingdomData.KingdomType.KingdomOfLight,
            culturalPhilosophy = "Truth illuminates all darkness. The sun's light reveals what shadows would hide.",
            coreBeliefs = new string[]
            {
                "Truth above all else",
                "Light banishes deception",
                "Public accountability prevents corruption",
                "The sun judges all actions"
            },
            architecture = KingdomData.ArchitecturalStyle.MarbleAndGold,
            illumination = KingdomData.IlluminationType.MirrorReflection,
            primaryColor = new Color(1f, 0.9f, 0.7f), // Golden
            secondaryColor = new Color(0.9f, 0.9f, 0.9f), // Marble white
            socialSystem = KingdomData.SocialSystem.TruthBasedHierarchy,
            socialRituals = new string[]
            {
                "Daily Truth Confession at dawn",
                "Mirror Reflection Meditation",
                "Public Shame Ritual for liars",
                "Tongue Breaking Ceremony for repeated lies"
            },
            greetingStyle = KingdomData.GreetingStyle.HandToChestThenSun,
            greetingDescription = "Place hand on chest (honesty), then gesture toward the sun (truth)",
            communication = KingdomData.CommunicationMethod.OpenSpeech,
            commonPhrases = new string[]
            {
                "May the sun illuminate your words",
                "Truth be your shield and sword",
                "In light, we trust",
                "Shadows hide only lies"
            },
            perception = KingdomData.CinderbornPerception.TestFromGods,
            defaultStanding = 0f, // Neutral until proven
            specialDialogues = new string[]
            {
                "The sun's light reveals your true nature, Cinderborn",
                "Your flames... do they burn away lies or create new shadows?",
                "We have heard whispers of your deeds. Speak truth of them."
            }
        };
        
        // Kingdom of Shadow - Moon Followers
        KingdomData shadowKingdom = new KingdomData
        {
            kingdomName = "Kingdom of Shadow",
            type = KingdomData.KingdomType.KingdomOfShadow,
            culturalPhilosophy = "In darkness, we find peace. The moon guides those who have lost their way.",
            coreBeliefs = new string[]
            {
                "Silence preserves wisdom",
                "Darkness offers sanctuary",
                "Change is death and rebirth",
                "The moon sees all transformations"
            },
            architecture = KingdomData.ArchitecturalStyle.WoodAndAsh,
            illumination = KingdomData.IlluminationType.BioluminescentFungi,
            primaryColor = new Color(0.2f, 0.3f, 0.2f), // Dark forest green
            secondaryColor = new Color(0.4f, 0.4f, 0.4f), // Ash gray
            socialSystem = KingdomData.SocialSystem.WhisperCouncil,
            socialRituals = new string[]
            {
                "Whisper Councils at midnight",
                "Ceremonial Funeral for the Morally Changed",
                "Moon Phase Meditation",
                "Ash Blessing for the Transformed"
            },
            greetingStyle = KingdomData.GreetingStyle.TouchForeheadThenGround,
            greetingDescription = "Touch forehead (wisdom), then touch ground (humility)",
            communication = KingdomData.CommunicationMethod.WhisperOnly,
            commonPhrases = new string[]
            {
                "In shadow, we find truth",
                "The moon guides the lost",
                "Change is the only constant",
                "Whispers carry wisdom"
            },
            perception = KingdomData.CinderbornPerception.ProphecyFulfillment,
            defaultStanding = 25f, // Sympathetic to transformation
            specialDialogues = new string[]
            {
                "You carry the scent of transformation, Cinderborn",
                "The moon has whispered of your coming",
                "In you, we see the cycle of death and rebirth"
            }
        };
        
        kingdoms = new KingdomData[] { lightKingdom, shadowKingdom };
        
        // Initialize cultural standings
        foreach (KingdomData kingdom in kingdoms)
        {
            culturalStandings[kingdom.kingdomName] = kingdom.defaultStanding;
        }
        
        Debug.Log("Kingdom Cultural System initialized with distinct societies");
    }
    
    void InitializeVillages()
    {
        // Fire Rejector Village
        VillageData fireRejecters = new VillageData
        {
            villageName = "Coldhearth Village",
            type = VillageData.VillageType.FireRejecters,
            uniqueBelief = "Fire is the root of all violence and destruction",
            isolationLevel = 0.8f,
            fireAttitude = VillageData.FireAttitude.CompleteRejection,
            fireRitual = "Water Blessing Ceremony to cleanse fire's influence",
            fireTaboos = new string[] { "No flames allowed", "Fire magic forbidden", "Ash must be buried" },
            strangerPolicy = VillageData.StrangerPolicy.CautiousAcceptance,
            strangerRitual = "Water purification before entry",
            suspicionLevel = 0.9f // Very suspicious of The Cinderborn
        };
        
        // Stranger Burner Village
        VillageData strangerBurners = new VillageData
        {
            villageName = "Pyrewatch Settlement",
            type = VillageData.VillageType.StrangerBurners,
            uniqueBelief = "Strangers carry foreign curses that must be burned away",
            isolationLevel = 0.9f,
            fireAttitude = VillageData.FireAttitude.SacredOffering,
            fireRitual = "Stranger Burning Ceremony for purification",
            fireTaboos = new string[] { "Sacred fire must not be extinguished", "Only pure-born may tend flames" },
            strangerPolicy = VillageData.StrangerPolicy.BurnAsOffering,
            strangerRitual = "Immediate capture and burning",
            suspicionLevel = 1f // Extremely hostile
        };
        
        // Cinderborn Worshippers
        VillageData cinderbornWorshippers = new VillageData
        {
            villageName = "Ashfall Sanctuary",
            type = VillageData.VillageType.CinderbornWorshippers,
            uniqueBelief = "The Cinderborn is the prophesied savior of the ash-touched",
            isolationLevel = 0.3f,
            fireAttitude = VillageData.FireAttitude.WorshipfulReverence,
            fireRitual = "Ash Communion to honor The Cinderborn",
            fireTaboos = new string[] { "Never extinguish sacred flames", "Ash is holy" },
            strangerPolicy = VillageData.StrangerPolicy.WelcomeWithTests,
            strangerRitual = "Test of fire resistance to prove worthiness",
            suspicionLevel = 0.1f // Very welcoming to The Cinderborn
        };
        
        // Cinderborn Fearers
        VillageData cinderbornFearers = new VillageData
        {
            villageName = "Dreadmoor Hamlet",
            type = VillageData.VillageType.CinderbornFearers,
            uniqueBelief = "The Cinderborn brings the end times and must be avoided",
            isolationLevel = 0.95f,
            fireAttitude = VillageData.FireAttitude.FearfulAvoidance,
            fireRitual = "Ward Burning to keep The Cinderborn away",
            fireTaboos = new string[] { "No large fires", "Ash must be scattered immediately" },
            strangerPolicy = VillageData.StrangerPolicy.FearfulAvoidance,
            strangerRitual = "Immediate evacuation and hiding",
            suspicionLevel = 1f // Terrified
        };
        
        independentVillages = new VillageData[] { fireRejecters, strangerBurners, cinderbornWorshippers, cinderbornFearers };
        
        Debug.Log($"Initialized {independentVillages.Length} independent villages with unique beliefs");
    }
    
    void UpdateCurrentKingdom()
    {
        string newKingdom = DetectCurrentKingdom();
        
        if (newKingdom != currentKingdom)
        {
            OnKingdomEntered(newKingdom);
            currentKingdom = newKingdom;
        }
    }
    
    string DetectCurrentKingdom()
    {
        // This would use colliders or position-based detection
        // For now, return a placeholder
        return "Kingdom of Light"; // Placeholder
    }
    
    void OnKingdomEntered(string kingdomName)
    {
        KingdomData kingdom = GetKingdomData(kingdomName);
        if (kingdom != null)
        {
            ShowCulturalMessage($"Entering {kingdom.kingdomName}");
            ShowCulturalMessage($"Philosophy: {kingdom.culturalPhilosophy}");
            
            // Update musical theme
            if (musicalSystem != null)
            {
                musicalSystem.OnStoryBeat(); // Trigger musical transition
            }
            
            // Apply cultural effects
            ApplyCulturalEffects(kingdom);
            
            // Show greeting instructions
            ShowGreetingInstructions(kingdom);
        }
    }
    
    void ApplyCulturalEffects(KingdomData kingdom)
    {
        // Apply visual effects based on kingdom
        switch (kingdom.illumination)
        {
            case KingdomData.IlluminationType.MirrorReflection:
                ApplyMirrorIllumination();
                break;
            case KingdomData.IlluminationType.BioluminescentFungi:
                ApplyFungiIllumination();
                break;
        }
        
        // Apply architectural changes
        UpdateArchitecture(kingdom);
    }
    
    void ApplyMirrorIllumination()
    {
        // Bright, reflective lighting
        RenderSettings.ambientLight = new Color(1f, 0.95f, 0.8f);
        
        // Add mirror reflection effects
        foreach (Light light in illuminationSystems)
        {
            if (light != null)
            {
                light.intensity = 1.5f;
                light.color = new Color(1f, 0.9f, 0.7f);
            }
        }
    }
    
    void ApplyFungiIllumination()
    {
        // Dim, eerie bioluminescent lighting
        RenderSettings.ambientLight = new Color(0.3f, 0.4f, 0.3f);
        
        foreach (Light light in illuminationSystems)
        {
            if (light != null)
            {
                light.intensity = 0.5f;
                light.color = new Color(0.4f, 0.6f, 0.4f);
            }
        }
    }
    
    void UpdateArchitecture(KingdomData kingdom)
    {
        // This would update building materials and styles
        // For now, just show a message
        ShowCulturalMessage($"Architecture: {kingdom.architecture}");
    }
    
    void ShowGreetingInstructions(KingdomData kingdom)
    {
        ShowCulturalMessage($"Cultural Greeting: {kingdom.greetingDescription}");
        ShowCulturalMessage("Press [G] to perform proper greeting");
    }
    
    void ProcessCulturalAdaptation()
    {
        // Player gradually adapts to local customs
        if (!string.IsNullOrEmpty(currentKingdom))
        {
            float adaptationGain = culturalAdaptationRate * Time.deltaTime;
            ModifyCulturalStanding(currentKingdom, adaptationGain);
        }
    }
    
    void CheckCulturalViolations()
    {
        // Check for cultural rule violations
        // This would integrate with player actions
    }
    
    public void PerformGreeting()
    {
        KingdomData kingdom = GetKingdomData(currentKingdom);
        if (kingdom != null)
        {
            ProcessGreeting(kingdom);
        }
    }
    
    void ProcessGreeting(KingdomData kingdom)
    {
        switch (kingdom.greetingStyle)
        {
            case KingdomData.GreetingStyle.HandToChestThenSun:
                ProcessLightKingdomGreeting(kingdom);
                break;
            case KingdomData.GreetingStyle.TouchForeheadThenGround:
                ProcessShadowKingdomGreeting(kingdom);
                break;
        }
    }
    
    void ProcessLightKingdomGreeting(KingdomData kingdom)
    {
        ShowCulturalMessage("You place your hand on your chest, then gesture toward the sun.");
        ModifyCulturalStanding(kingdom.kingdomName, 5f);
        
        // NPCs react positively
        ShowCulturalMessage("The citizens nod approvingly at your proper greeting.");
    }
    
    void ProcessShadowKingdomGreeting(KingdomData kingdom)
    {
        ShowCulturalMessage("You touch your forehead, then place your hand on the ground.");
        ModifyCulturalStanding(kingdom.kingdomName, 5f);
        
        // NPCs react with whispered approval
        ShowCulturalMessage("Whispered approval echoes from the shadows.");
    }
    
    public void CommitCulturalViolation(string violationType, float severity)
    {
        if (string.IsNullOrEmpty(currentKingdom)) return;
        
        CulturalViolation violation = new CulturalViolation
        {
            kingdomName = currentKingdom,
            violationType = violationType,
            severity = severity,
            timestamp = Time.time,
            consequence = DetermineCulturalConsequence(violationType, severity)
        };
        
        violations.Add(violation);
        
        // Apply immediate consequences
        ApplyCulturalConsequence(violation);
        
        // Reduce cultural standing
        ModifyCulturalStanding(currentKingdom, -severity);
    }
    
    string DetermineCulturalConsequence(string violationType, float severity)
    {
        KingdomData kingdom = GetKingdomData(currentKingdom);
        if (kingdom == null) return "Unknown consequence";
        
        if (kingdom.type == KingdomData.KingdomType.KingdomOfLight && violationType == "Lying")
        {
            if (severity > 50f)
                return "Tongue Breaking Ceremony";
            else
                return "Public Shame Ritual";
        }
        else if (kingdom.type == KingdomData.KingdomType.KingdomOfShadow && violationType == "LoudSpeaking")
        {
            return "Ceremonial Funeral for Moral Change";
        }
        
        return "Cultural disapproval";
    }
    
    void ApplyCulturalConsequence(CulturalViolation violation)
    {
        ShowCulturalMessage($"Cultural Violation: {violation.violationType}");
        ShowCulturalMessage($"Consequence: {violation.consequence}");
        
        // Apply specific consequences
        switch (violation.consequence)
        {
            case "Public Shame Ritual":
                ApplyPublicShame();
                break;
            case "Tongue Breaking Ceremony":
                ApplyTongueBreaking();
                break;
            case "Ceremonial Funeral for Moral Change":
                ApplyCeremonialFuneral();
                break;
        }
    }
    
    void ApplyPublicShame()
    {
        ShowCulturalMessage("You are subjected to public shame. Citizens point and whisper.");
        
        // Reduce reputation
        if (hostilitySystem != null)
        {
            hostilitySystem.ModifyGlobalReputation(-25f);
        }
    }
    
    void ApplyTongueBreaking()
    {
        ShowCulturalMessage("The severe punishment of tongue breaking is threatened. You must leave immediately or face consequences.");
        
        // Force player to leave or face combat
        if (hostilitySystem != null)
        {
            hostilitySystem.SetGlobalHostility(true);
        }
    }
    
    void ApplyCeremonialFuneral()
    {
        ShowCulturalMessage("A ceremonial funeral is held for your 'moral death'. You are treated as spiritually deceased.");
        
        // NPCs ignore player as if they don't exist
        ModifyCulturalStanding(currentKingdom, -50f);
    }
    
    public void ModifyCulturalStanding(string kingdomName, float change)
    {
        if (culturalStandings.ContainsKey(kingdomName))
        {
            culturalStandings[kingdomName] = Mathf.Clamp(culturalStandings[kingdomName] + change, -100f, 100f);
            
            if (Mathf.Abs(change) > 2f)
            {
                ShowCulturalMessage($"Standing with {kingdomName}: {culturalStandings[kingdomName]:F1} ({change:+F1})");
            }
        }
    }
    
    KingdomData GetKingdomData(string kingdomName)
    {
        foreach (KingdomData kingdom in kingdoms)
        {
            if (kingdom.kingdomName == kingdomName)
                return kingdom;
        }
        return null;
    }
    
    public float GetCulturalStanding(string kingdomName)
    {
        return culturalStandings.ContainsKey(kingdomName) ? culturalStandings[kingdomName] : 0f;
    }
    
    public bool IsInKingdom(string kingdomName)
    {
        return currentKingdom == kingdomName;
    }
    
    public KingdomData GetCurrentKingdomData()
    {
        return GetKingdomData(currentKingdom);
    }
    
    void ShowCulturalMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Cultural System: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public KingdomData[] GetKingdoms() => kingdoms;
    public VillageData[] GetIndependentVillages() => independentVillages;
    public string GetCurrentKingdom() => currentKingdom;
    public Dictionary<string, float> GetCulturalStandings() => culturalStandings;
    public List<CulturalViolation> GetViolations() => violations;
}
