using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.Tutorial
{
    /// <summary>
    /// Integrated Tutorial System for Cinder of Darkness.
    /// Seamlessly integrates tutorial elements with the opening storyline and quest system.
    /// </summary>
    public class IntegratedTutorialSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Tutorial Integration")]
        [SerializeField] private bool enableTutorialIntegration = true;
        [SerializeField] private bool skipTutorialIfExperienced = true;
        [SerializeField] private float tutorialStepDelay = 2f;

        [Header("Tutorial UI")]
        [SerializeField] private GameObject tutorialOverlay;
        [SerializeField] private TMPro.TextMeshProUGUI tutorialText;
        [SerializeField] private GameObject tutorialHighlight;
        [SerializeField] private GameObject tutorialArrow;

        [Header("Input Prompts")]
        [SerializeField] private GameObject keyboardPrompts;
        [SerializeField] private GameObject controllerPrompts;
        [SerializeField] private bool autoDetectInputDevice = true;

        [Header("Audio")]
        [SerializeField] private AudioSource tutorialAudioSource;
        [SerializeField] private AudioClip tutorialStartSound;
        [SerializeField] private AudioClip tutorialCompleteSound;
        [SerializeField] private AudioClip tutorialStepSound;
        #endregion

        #region Private Fields
        private EnhancedOpeningStoryline openingStoryline;
        private QuestSystem questSystem;
        private PlayerController playerController;
        private List<TutorialStep> activeTutorialSteps = new List<TutorialStep>();
        private TutorialStep currentStep;
        private bool isTutorialActive = false;
        private int currentStepIndex = 0;
        private InputDeviceType currentInputDevice = InputDeviceType.KeyboardMouse;
        #endregion

        #region Public Properties
        public static IntegratedTutorialSystem Instance { get; private set; }
        public bool IsTutorialActive => isTutorialActive;
        public bool HasCompletedTutorial => PlayerPrefs.GetInt("TutorialCompleted", 0) == 1;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeTutorialSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupTutorialIntegration();
        }

        private void Update()
        {
            if (isTutorialActive)
            {
                UpdateCurrentTutorialStep();
            }

            if (autoDetectInputDevice)
            {
                DetectInputDevice();
            }
        }
        #endregion

        #region Initialization
        private void InitializeTutorialSystem()
        {
            openingStoryline = FindObjectOfType<EnhancedOpeningStoryline>();
            questSystem = FindObjectOfType<QuestSystem>();
            playerController = FindObjectOfType<PlayerController>();

            if (tutorialAudioSource == null)
                tutorialAudioSource = gameObject.AddComponent<AudioSource>();

            Debug.Log("Integrated Tutorial System initialized");
        }

        private void SetupTutorialIntegration()
        {
            // Check if tutorial should be skipped
            if (skipTutorialIfExperienced && HasCompletedTutorial)
            {
                Debug.Log("Tutorial skipped - player has completed it before");
                return;
            }

            // Setup tutorial steps based on opening storyline
            SetupOpeningTutorialSteps();
        }

        private void SetupOpeningTutorialSteps()
        {
            activeTutorialSteps = new List<TutorialStep>
            {
                CreateMovementTutorialStep(),
                CreateCameraTutorialStep(),
                CreateFireAbilityTutorialStep(),
                CreateInteractionTutorialStep(),
                CreateQuestLogTutorialStep(),
                CreateInventoryTutorialStep(),
                CreateDialogueTutorialStep(),
                CreateCombatBasicsTutorialStep(),
                CreateMagicSystemTutorialStep(),
                CreateWorldNavigationTutorialStep()
            };

            Debug.Log($"Setup {activeTutorialSteps.Count} tutorial steps");
        }
        #endregion

        #region Tutorial Step Creation
        private TutorialStep CreateMovementTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "movement_basics",
                stepName = "Movement Basics",
                description = "Learn to move as the Cinderborn",
                keyboardInstructions = "Use WASD keys to move around. Feel the weight of your transformed body.",
                controllerInstructions = "Use the left stick to move around. Feel the weight of your transformed body.",
                triggerCondition = TutorialStep.TriggerCondition.OnPlayerControlEnabled,
                completionCondition = TutorialStep.CompletionCondition.PlayerMoved,
                requiredMovementDistance = 5f,
                isSkippable = false,
                priority = 1,

                onStepStart = () => {
                    ShowTutorialPrompt("Movement", GetCurrentMovementInstructions());
                    HighlightPlayerCharacter();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    PlayTutorialStepSound();
                    Debug.Log("Movement tutorial completed");
                }
            };
        }

        private TutorialStep CreateCameraTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "camera_control",
                stepName = "Camera Control",
                description = "Learn to look around and observe your surroundings",
                keyboardInstructions = "Move your mouse to look around. Observe the world that has changed around you.",
                controllerInstructions = "Use the right stick to look around. Observe the world that has changed around you.",
                triggerCondition = TutorialStep.TriggerCondition.AfterPreviousStep,
                completionCondition = TutorialStep.CompletionCondition.CameraMoved,
                requiredCameraMovement = 90f, // degrees
                isSkippable = false,
                priority = 2,

                onStepStart = () => {
                    ShowTutorialPrompt("Camera Control", GetCurrentCameraInstructions());
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    PlayTutorialStepSound();
                    Debug.Log("Camera tutorial completed");
                }
            };
        }

        private TutorialStep CreateFireAbilityTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "fire_ability_basic",
                stepName = "Fire Ability Basics",
                description = "Learn to channel your inner fire",
                keyboardInstructions = "Left click to channel your fire. Right click for focused flame. Feel the power within you.",
                controllerInstructions = "Press RT to channel your fire. Press RB for focused flame. Feel the power within you.",
                triggerCondition = TutorialStep.TriggerCondition.AfterPreviousStep,
                completionCondition = TutorialStep.CompletionCondition.AbilityUsed,
                requiredAbilityUses = 3,
                isSkippable = false,
                priority = 3,

                onStepStart = () => {
                    ShowTutorialPrompt("Fire Abilities", GetCurrentFireAbilityInstructions());
                    SpawnTutorialTargets();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveTutorialTargets();
                    PlayTutorialStepSound();
                    Debug.Log("Fire ability tutorial completed");
                }
            };
        }

        private TutorialStep CreateInteractionTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "interaction_basics",
                stepName = "Interaction Basics",
                description = "Learn to interact with the world around you",
                keyboardInstructions = "Press E when near objects to interact with them. The world responds to your presence.",
                controllerInstructions = "Press A when near objects to interact with them. The world responds to your presence.",
                triggerCondition = TutorialStep.TriggerCondition.NearInteractable,
                completionCondition = TutorialStep.CompletionCondition.InteractionPerformed,
                requiredInteractions = 1,
                isSkippable = true,
                priority = 4,

                onStepStart = () => {
                    ShowTutorialPrompt("Interaction", GetCurrentInteractionInstructions());
                    HighlightNearbyInteractables();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveInteractableHighlights();
                    PlayTutorialStepSound();
                    Debug.Log("Interaction tutorial completed");
                }
            };
        }

        private TutorialStep CreateQuestLogTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "quest_log_basics",
                stepName = "Quest Log",
                description = "Learn to track your journey and objectives",
                keyboardInstructions = "Press J to open your Quest Log. Here you can track your objectives and progress.",
                controllerInstructions = "Press Back/Select to open your Quest Log. Here you can track your objectives and progress.",
                triggerCondition = TutorialStep.TriggerCondition.QuestReceived,
                completionCondition = TutorialStep.CompletionCondition.UIOpened,
                requiredUIPanel = "QuestLog",
                isSkippable = true,
                priority = 5,

                onStepStart = () => {
                    ShowTutorialPrompt("Quest Log", GetCurrentQuestLogInstructions());
                    HighlightQuestLogButton();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveQuestLogHighlight();
                    PlayTutorialStepSound();
                    Debug.Log("Quest log tutorial completed");
                }
            };
        }

        private TutorialStep CreateInventoryTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "inventory_basics",
                stepName = "Inventory Management",
                description = "Learn to manage your belongings and equipment",
                keyboardInstructions = "Press I to open your Inventory. Manage your items and equipment here.",
                controllerInstructions = "Press Y to open your Inventory. Manage your items and equipment here.",
                triggerCondition = TutorialStep.TriggerCondition.ItemReceived,
                completionCondition = TutorialStep.CompletionCondition.UIOpened,
                requiredUIPanel = "Inventory",
                isSkippable = true,
                priority = 6,

                onStepStart = () => {
                    ShowTutorialPrompt("Inventory", GetCurrentInventoryInstructions());
                    HighlightInventoryButton();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveInventoryHighlight();
                    PlayTutorialStepSound();
                    Debug.Log("Inventory tutorial completed");
                }
            };
        }

        private TutorialStep CreateDialogueTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "dialogue_basics",
                stepName = "Dialogue System",
                description = "Learn to communicate with others",
                keyboardInstructions = "Use number keys or click to select dialogue options. Your choices matter.",
                controllerInstructions = "Use the D-pad or face buttons to select dialogue options. Your choices matter.",
                triggerCondition = TutorialStep.TriggerCondition.DialogueStarted,
                completionCondition = TutorialStep.CompletionCondition.DialogueChoiceMade,
                requiredDialogueChoices = 1,
                isSkippable = true,
                priority = 7,

                onStepStart = () => {
                    ShowTutorialPrompt("Dialogue", GetCurrentDialogueInstructions());
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    PlayTutorialStepSound();
                    Debug.Log("Dialogue tutorial completed");
                }
            };
        }

        private TutorialStep CreateCombatBasicsTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "combat_basics",
                stepName = "Combat Basics",
                description = "Learn the fundamentals of combat",
                keyboardInstructions = "Left click to attack, Right click to block. Space to dodge. Use your fire abilities strategically.",
                controllerInstructions = "RT to attack, LT to block. B to dodge. Use your fire abilities strategically.",
                triggerCondition = TutorialStep.TriggerCondition.EnemyEncountered,
                completionCondition = TutorialStep.CompletionCondition.EnemyDefeated,
                requiredEnemyDefeats = 1,
                isSkippable = false,
                priority = 8,

                onStepStart = () => {
                    ShowTutorialPrompt("Combat", GetCurrentCombatInstructions());
                    SpawnTutorialEnemy();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    PlayTutorialStepSound();
                    Debug.Log("Combat tutorial completed");
                }
            };
        }

        private TutorialStep CreateMagicSystemTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "magic_system_basics",
                stepName = "Magic System",
                description = "Learn to harness your magical abilities",
                keyboardInstructions = "Hold Shift and click to cast spells. Press M to open the Magic Tree.",
                controllerInstructions = "Hold LB and press face buttons to cast spells. Press Start to open the Magic Tree.",
                triggerCondition = TutorialStep.TriggerCondition.MagicUnlocked,
                completionCondition = TutorialStep.CompletionCondition.SpellCast,
                requiredSpellCasts = 2,
                isSkippable = true,
                priority = 9,

                onStepStart = () => {
                    ShowTutorialPrompt("Magic System", GetCurrentMagicInstructions());
                    HighlightMagicUI();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveMagicHighlight();
                    PlayTutorialStepSound();
                    Debug.Log("Magic system tutorial completed");
                }
            };
        }

        private TutorialStep CreateWorldNavigationTutorialStep()
        {
            return new TutorialStep
            {
                stepId = "world_navigation",
                stepName = "World Navigation",
                description = "Learn to navigate the world and fast travel",
                keyboardInstructions = "Press Tab to open the World Map. Discover locations to unlock fast travel.",
                controllerInstructions = "Press the Map button to open the World Map. Discover locations to unlock fast travel.",
                triggerCondition = TutorialStep.TriggerCondition.LocationDiscovered,
                completionCondition = TutorialStep.CompletionCondition.MapOpened,
                isSkippable = true,
                priority = 10,

                onStepStart = () => {
                    ShowTutorialPrompt("World Navigation", GetCurrentNavigationInstructions());
                    HighlightMapButton();
                },

                onStepComplete = () => {
                    HideTutorialPrompt();
                    RemoveMapHighlight();
                    PlayTutorialStepSound();
                    CompleteTutorial();
                    Debug.Log("World navigation tutorial completed");
                }
            };
        }
        #endregion

        #region Tutorial Management
        public void StartTutorial()
        {
            if (!enableTutorialIntegration) return;

            isTutorialActive = true;
            currentStepIndex = 0;

            if (tutorialStartSound != null && tutorialAudioSource != null)
                tutorialAudioSource.PlayOneShot(tutorialStartSound);

            Debug.Log("Tutorial started");
        }

        public void StartTutorialStep(string stepId)
        {
            var step = activeTutorialSteps.Find(s => s.stepId == stepId);
            if (step != null)
            {
                StartTutorialStep(step);
            }
        }

        public void StartTutorialStep(TutorialStep step)
        {
            if (currentStep != null)
            {
                CompleteTutorialStep(currentStep);
            }

            currentStep = step;
            step.isActive = true;
            step.startTime = Time.time;

            step.onStepStart?.Invoke();

            Debug.Log($"Tutorial step started: {step.stepName}");
        }

        public void CompleteTutorialStep(TutorialStep step)
        {
            if (step == null) return;

            step.isActive = false;
            step.isCompleted = true;
            step.completionTime = Time.time;

            step.onStepComplete?.Invoke();

            // Move to next step
            currentStepIndex++;
            if (currentStepIndex < activeTutorialSteps.Count)
            {
                var nextStep = activeTutorialSteps[currentStepIndex];
                if (nextStep.triggerCondition == TutorialStep.TriggerCondition.AfterPreviousStep)
                {
                    StartCoroutine(StartNextStepAfterDelay(nextStep));
                }
            }

            Debug.Log($"Tutorial step completed: {step.stepName}");
        }

        private IEnumerator StartNextStepAfterDelay(TutorialStep step)
        {
            yield return new WaitForSeconds(tutorialStepDelay);
            StartTutorialStep(step);
        }

        public void CompleteTutorial()
        {
            isTutorialActive = false;
            currentStep = null;

            PlayerPrefs.SetInt("TutorialCompleted", 1);
            PlayerPrefs.Save();

            if (tutorialCompleteSound != null && tutorialAudioSource != null)
                tutorialAudioSource.PlayOneShot(tutorialCompleteSound);

            HideTutorialPrompt();

            Debug.Log("Tutorial completed!");
        }

        public void SkipTutorial()
        {
            if (currentStep != null && currentStep.isSkippable)
            {
                CompleteTutorialStep(currentStep);
            }
        }

        private void UpdateCurrentTutorialStep()
        {
            if (currentStep == null || !currentStep.isActive) return;

            // Check completion conditions
            bool isCompleted = CheckStepCompletionCondition(currentStep);
            if (isCompleted)
            {
                CompleteTutorialStep(currentStep);
            }
        }

        private bool CheckStepCompletionCondition(TutorialStep step)
        {
            switch (step.completionCondition)
            {
                case TutorialStep.CompletionCondition.PlayerMoved:
                    return step.currentMovementDistance >= step.requiredMovementDistance;

                case TutorialStep.CompletionCondition.CameraMoved:
                    return step.currentCameraMovement >= step.requiredCameraMovement;

                case TutorialStep.CompletionCondition.AbilityUsed:
                    return step.currentAbilityUses >= step.requiredAbilityUses;

                case TutorialStep.CompletionCondition.InteractionPerformed:
                    return step.currentInteractions >= step.requiredInteractions;

                case TutorialStep.CompletionCondition.UIOpened:
                    return step.hasOpenedRequiredUI;

                case TutorialStep.CompletionCondition.DialogueChoiceMade:
                    return step.currentDialogueChoices >= step.requiredDialogueChoices;

                case TutorialStep.CompletionCondition.EnemyDefeated:
                    return step.currentEnemyDefeats >= step.requiredEnemyDefeats;

                case TutorialStep.CompletionCondition.SpellCast:
                    return step.currentSpellCasts >= step.requiredSpellCasts;

                case TutorialStep.CompletionCondition.MapOpened:
                    return step.hasOpenedMap;

                default:
                    return false;
            }
        }
        #endregion

        #region UI Management
        private void ShowTutorialPrompt(string title, string instructions)
        {
            if (tutorialOverlay != null)
                tutorialOverlay.SetActive(true);

            if (tutorialText != null)
                tutorialText.text = $"<b>{title}</b>\n\n{instructions}";
        }

        private void HideTutorialPrompt()
        {
            if (tutorialOverlay != null)
                tutorialOverlay.SetActive(false);
        }

        private void HighlightPlayerCharacter()
        {
            // Highlight player character for movement tutorial
        }

        private void HighlightNearbyInteractables()
        {
            // Highlight nearby interactable objects
        }

        private void RemoveInteractableHighlights()
        {
            // Remove interactable highlights
        }

        private void HighlightQuestLogButton()
        {
            // Highlight quest log UI button
        }

        private void RemoveQuestLogHighlight()
        {
            // Remove quest log highlight
        }

        private void HighlightInventoryButton()
        {
            // Highlight inventory UI button
        }

        private void RemoveInventoryHighlight()
        {
            // Remove inventory highlight
        }

        private void HighlightMagicUI()
        {
            // Highlight magic system UI
        }

        private void RemoveMagicHighlight()
        {
            // Remove magic UI highlight
        }

        private void HighlightMapButton()
        {
            // Highlight map UI button
        }

        private void RemoveMapHighlight()
        {
            // Remove map highlight
        }

        private void SpawnTutorialTargets()
        {
            // Spawn targets for fire ability practice
        }

        private void RemoveTutorialTargets()
        {
            // Remove tutorial targets
        }

        private void SpawnTutorialEnemy()
        {
            // Spawn a tutorial enemy for combat practice
        }
        #endregion

        #region Input Detection
        private void DetectInputDevice()
        {
            // Detect current input device and update prompts accordingly
            if (Input.anyKey)
            {
                if (Input.inputString.Length > 0)
                {
                    currentInputDevice = InputDeviceType.KeyboardMouse;
                }
            }

            // Check for controller input
            string[] joystickNames = Input.GetJoystickNames();
            if (joystickNames.Length > 0 && !string.IsNullOrEmpty(joystickNames[0]))
            {
                for (int i = 0; i < 20; i++) // Check common controller buttons
                {
                    if (Input.GetKey(KeyCode.Joystick1Button0 + i))
                    {
                        currentInputDevice = InputDeviceType.Controller;
                        break;
                    }
                }
            }

            UpdateInputPrompts();
        }

        private void UpdateInputPrompts()
        {
            if (keyboardPrompts != null)
                keyboardPrompts.SetActive(currentInputDevice == InputDeviceType.KeyboardMouse);

            if (controllerPrompts != null)
                controllerPrompts.SetActive(currentInputDevice == InputDeviceType.Controller);
        }

        private string GetCurrentMovementInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[0].keyboardInstructions :
                activeTutorialSteps[0].controllerInstructions;
        }

        private string GetCurrentCameraInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[1].keyboardInstructions :
                activeTutorialSteps[1].controllerInstructions;
        }

        private string GetCurrentFireAbilityInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[2].keyboardInstructions :
                activeTutorialSteps[2].controllerInstructions;
        }

        private string GetCurrentInteractionInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[3].keyboardInstructions :
                activeTutorialSteps[3].controllerInstructions;
        }

        private string GetCurrentQuestLogInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[4].keyboardInstructions :
                activeTutorialSteps[4].controllerInstructions;
        }

        private string GetCurrentInventoryInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[5].keyboardInstructions :
                activeTutorialSteps[5].controllerInstructions;
        }

        private string GetCurrentDialogueInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[6].keyboardInstructions :
                activeTutorialSteps[6].controllerInstructions;
        }

        private string GetCurrentCombatInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[7].keyboardInstructions :
                activeTutorialSteps[7].controllerInstructions;
        }

        private string GetCurrentMagicInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[8].keyboardInstructions :
                activeTutorialSteps[8].controllerInstructions;
        }

        private string GetCurrentNavigationInstructions()
        {
            return currentInputDevice == InputDeviceType.KeyboardMouse ?
                activeTutorialSteps[9].keyboardInstructions :
                activeTutorialSteps[9].controllerInstructions;
        }

        private void PlayTutorialStepSound()
        {
            if (tutorialStepSound != null && tutorialAudioSource != null)
                tutorialAudioSource.PlayOneShot(tutorialStepSound);
        }
        #endregion

        #region Public API
        public void TriggerTutorialStep(string stepId)
        {
            if (!isTutorialActive) return;

            var step = activeTutorialSteps.Find(s => s.stepId == stepId);
            if (step != null && !step.isCompleted)
            {
                StartTutorialStep(step);
            }
        }

        public void UpdateStepProgress(string stepId, string progressType, float amount)
        {
            if (currentStep == null || currentStep.stepId != stepId) return;

            switch (progressType)
            {
                case "movement":
                    currentStep.currentMovementDistance += amount;
                    break;
                case "camera":
                    currentStep.currentCameraMovement += amount;
                    break;
                case "ability":
                    currentStep.currentAbilityUses++;
                    break;
                case "interaction":
                    currentStep.currentInteractions++;
                    break;
                case "dialogue":
                    currentStep.currentDialogueChoices++;
                    break;
                case "enemy":
                    currentStep.currentEnemyDefeats++;
                    break;
                case "spell":
                    currentStep.currentSpellCasts++;
                    break;
            }
        }

        public void MarkUIOpened(string uiPanel)
        {
            if (currentStep != null && currentStep.requiredUIPanel == uiPanel)
            {
                currentStep.hasOpenedRequiredUI = true;
            }

            if (uiPanel == "Map" && currentStep != null)
            {
                currentStep.hasOpenedMap = true;
            }
        }

        public bool IsTutorialStepActive(string stepId)
        {
            return currentStep != null && currentStep.stepId == stepId && currentStep.isActive;
        }

        public List<TutorialStep> GetActiveTutorialSteps()
        {
            return new List<TutorialStep>(activeTutorialSteps);
        }
        #endregion

        #region Enums
        public enum InputDeviceType
        {
            KeyboardMouse,
            Controller
        }
        #endregion
    }

    #region Tutorial Data Structures
    [System.Serializable]
    public class TutorialStep
    {
        [Header("Step Information")]
        public string stepId;
        public string stepName;
        [TextArea(2, 3)]
        public string description;

        [Header("Instructions")]
        [TextArea(2, 4)]
        public string keyboardInstructions;
        [TextArea(2, 4)]
        public string controllerInstructions;

        [Header("Trigger and Completion")]
        public TriggerCondition triggerCondition;
        public CompletionCondition completionCondition;

        [Header("Requirements")]
        public float requiredMovementDistance;
        public float requiredCameraMovement;
        public int requiredAbilityUses;
        public int requiredInteractions;
        public int requiredDialogueChoices;
        public int requiredEnemyDefeats;
        public int requiredSpellCasts;
        public string requiredUIPanel;

        [Header("Settings")]
        public bool isSkippable = true;
        public int priority = 0;

        [Header("Progress Tracking")]
        public float currentMovementDistance;
        public float currentCameraMovement;
        public int currentAbilityUses;
        public int currentInteractions;
        public int currentDialogueChoices;
        public int currentEnemyDefeats;
        public int currentSpellCasts;
        public bool hasOpenedRequiredUI;
        public bool hasOpenedMap;

        [Header("State")]
        public bool isActive;
        public bool isCompleted;
        public float startTime;
        public float completionTime;

        [Header("Events")]
        public System.Action onStepStart;
        public System.Action onStepComplete;
        public System.Action onStepSkipped;

        public enum TriggerCondition
        {
            OnPlayerControlEnabled,
            AfterPreviousStep,
            NearInteractable,
            QuestReceived,
            ItemReceived,
            DialogueStarted,
            EnemyEncountered,
            MagicUnlocked,
            LocationDiscovered,
            HealthLow,
            LevelUp,
            Custom
        }

        public enum CompletionCondition
        {
            PlayerMoved,
            CameraMoved,
            AbilityUsed,
            InteractionPerformed,
            UIOpened,
            DialogueChoiceMade,
            EnemyDefeated,
            SpellCast,
            MapOpened,
            TimeElapsed,
            Custom
        }

        public float GetCompletionPercentage()
        {
            switch (completionCondition)
            {
                case CompletionCondition.PlayerMoved:
                    return Mathf.Clamp01(currentMovementDistance / requiredMovementDistance) * 100f;

                case CompletionCondition.CameraMoved:
                    return Mathf.Clamp01(currentCameraMovement / requiredCameraMovement) * 100f;

                case CompletionCondition.AbilityUsed:
                    return Mathf.Clamp01((float)currentAbilityUses / requiredAbilityUses) * 100f;

                case CompletionCondition.InteractionPerformed:
                    return Mathf.Clamp01((float)currentInteractions / requiredInteractions) * 100f;

                case CompletionCondition.DialogueChoiceMade:
                    return Mathf.Clamp01((float)currentDialogueChoices / requiredDialogueChoices) * 100f;

                case CompletionCondition.EnemyDefeated:
                    return Mathf.Clamp01((float)currentEnemyDefeats / requiredEnemyDefeats) * 100f;

                case CompletionCondition.SpellCast:
                    return Mathf.Clamp01((float)currentSpellCasts / requiredSpellCasts) * 100f;

                case CompletionCondition.UIOpened:
                case CompletionCondition.MapOpened:
                    return (hasOpenedRequiredUI || hasOpenedMap) ? 100f : 0f;

                default:
                    return isCompleted ? 100f : 0f;
            }
        }

        public string GetProgressText()
        {
            switch (completionCondition)
            {
                case CompletionCondition.PlayerMoved:
                    return $"Distance moved: {currentMovementDistance:F1}/{requiredMovementDistance:F1}m";

                case CompletionCondition.CameraMoved:
                    return $"Camera movement: {currentCameraMovement:F0}/{requiredCameraMovement:F0}°";

                case CompletionCondition.AbilityUsed:
                    return $"Abilities used: {currentAbilityUses}/{requiredAbilityUses}";

                case CompletionCondition.InteractionPerformed:
                    return $"Interactions: {currentInteractions}/{requiredInteractions}";

                case CompletionCondition.DialogueChoiceMade:
                    return $"Dialogue choices: {currentDialogueChoices}/{requiredDialogueChoices}";

                case CompletionCondition.EnemyDefeated:
                    return $"Enemies defeated: {currentEnemyDefeats}/{requiredEnemyDefeats}";

                case CompletionCondition.SpellCast:
                    return $"Spells cast: {currentSpellCasts}/{requiredSpellCasts}";

                case CompletionCondition.UIOpened:
                    return hasOpenedRequiredUI ? "UI opened" : $"Open {requiredUIPanel}";

                case CompletionCondition.MapOpened:
                    return hasOpenedMap ? "Map opened" : "Open the map";

                default:
                    return isCompleted ? "Completed" : "In progress";
            }
        }
    }
    #endregion
}
