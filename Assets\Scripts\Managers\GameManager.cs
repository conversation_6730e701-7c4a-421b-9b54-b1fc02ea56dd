using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public GameState currentState = GameState.Playing;
    public string playerName = "Kael";
    public int currentLevel = 1;

    [Header("Save System")]
    public bool autoSave = true;
    public float autoSaveInterval = 300f; // 5 minutes

    [Header("World Settings")]
    public string currentArea = "Starting Village";
    public List<string> unlockedAreas = new List<string>();
    public List<string> completedQuests = new List<string>();

    [Header("Story Progress")]
    public StoryProgress storyProgress = new StoryProgress();
    public float currentStoryProgress = 0f; // 0 to 1

    [Header("The Cinderborn's Journey")]
    public bool hasMetMysteriousStranger = false;
    public int mysteriousStrangerEncounters = 0;
    public bool cinderbornsHeritageRevealed = false;
    public bool finalBossUnlocked = false;
    public float cinderbornsAge = 15f; // Starts at 15, grows to 25
    public PsychologicalJourneyData psychologicalJourney = new PsychologicalJourneyData();

    private PlayerStats playerStats;
    private GameUI gameUI;
    private float lastSaveTime;
    private static GameManager instance;

    // Events
    public System.Action<float> OnStoryProgressChanged;
    public System.Action<PsychologicalSystem.PsychologicalState> OnPsychologicalStateChanged;

    [System.Serializable]
    public class PsychologicalJourneyData
    {
        public int totalMeditations = 0;
        public int darkChoicesMade = 0;
        public int lightChoicesMade = 0;
        public int hallucinationsExperienced = 0;
        public int hopefulMomentsExperienced = 0;
        public float highestTrauma = 0f;
        public float highestEnlightenment = 0f;
        public bool hasReachedHollowState = false;
        public bool hasReachedEnlightenedState = false;
        public string[] significantPsychologicalEvents = new string[0];
    }

    public enum GameState
    {
        MainMenu,
        Playing,
        Paused,
        Dialogue,
        GameOver,
        Victory
    }

    [System.Serializable]
    public class StoryProgress
    {
        public bool hasMetSunSage = false;
        public bool hasMetMoonSage = false;
        public bool knowsParentage = false;
        public bool hasChosenFinalPath = false;
        public List<string> majorChoicesMade = new List<string>();
        public Dictionary<string, float> npcRelationships = new Dictionary<string, float>();
    }

    public static GameManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<GameManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("GameManager");
                    instance = go.AddComponent<GameManager>();
                }
            }
            return instance;
        }
    }

    void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
            return;
        }

        InitializeGame();
    }

    void Start()
    {
        FindGameComponents();
    }

    void Update()
    {
        HandleAutoSave();
        UpdateGameState();
    }

    void InitializeGame()
    {
        // Initialize default unlocked areas
        if (unlockedAreas.Count == 0)
        {
            unlockedAreas.Add("Starting Village");
        }

        // Set initial game state
        currentState = GameState.Playing;

        Debug.Log("Game Manager initialized");
    }

    void FindGameComponents()
    {
        playerStats = FindObjectOfType<PlayerStats>();
        gameUI = FindObjectOfType<GameUI>();

        // Subscribe to player events
        if (playerStats != null)
        {
            playerStats.OnPlayerDeath += OnPlayerDeath;
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }
    }

    void HandleAutoSave()
    {
        if (autoSave && Time.time - lastSaveTime >= autoSaveInterval)
        {
            SaveGame();
            lastSaveTime = Time.time;
        }
    }

    void UpdateGameState()
    {
        // Update game state based on current conditions
        DialogueSystem dialogueSystem = FindObjectOfType<DialogueSystem>();
        if (dialogueSystem != null && dialogueSystem.IsDialogueActive())
        {
            currentState = GameState.Dialogue;
        }
        else if (Time.timeScale == 0f)
        {
            currentState = GameState.Paused;
        }
        else
        {
            currentState = GameState.Playing;
        }
    }

    public void SaveGame()
    {
        GameSaveData saveData = new GameSaveData();

        // Player data
        if (playerStats != null)
        {
            saveData.playerName = playerName;
            saveData.currentLevel = currentLevel;
            saveData.currentHealth = playerStats.GetCurrentHealth();
            saveData.currentMana = playerStats.GetCurrentMana();
            saveData.currentStamina = playerStats.GetCurrentStamina();
            saveData.sunAlignment = playerStats.GetSunAlignment();
            saveData.moonAlignment = playerStats.GetMoonAlignment();
            saveData.currentPath = playerStats.GetCurrentPath();
        }

        // World data
        saveData.currentArea = currentArea;
        saveData.unlockedAreas = new List<string>(unlockedAreas);
        saveData.completedQuests = new List<string>(completedQuests);

        // Story progress
        saveData.storyProgress = storyProgress;

        // Save to PlayerPrefs (for prototype - in full game would use file system)
        string saveJson = JsonUtility.ToJson(saveData);
        PlayerPrefs.SetString("GameSave", saveJson);
        PlayerPrefs.Save();

        Debug.Log("Game saved successfully");
    }

    public void LoadGame()
    {
        if (PlayerPrefs.HasKey("GameSave"))
        {
            string saveJson = PlayerPrefs.GetString("GameSave");
            GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(saveJson);

            // Load player data
            playerName = saveData.playerName;
            currentLevel = saveData.currentLevel;

            if (playerStats != null)
            {
                playerStats.currentHealth = saveData.currentHealth;
                playerStats.currentMana = saveData.currentMana;
                playerStats.currentStamina = saveData.currentStamina;
                playerStats.sunAlignment = saveData.sunAlignment;
                playerStats.moonAlignment = saveData.moonAlignment;
            }

            // Load world data
            currentArea = saveData.currentArea;
            unlockedAreas = saveData.unlockedAreas;
            completedQuests = saveData.completedQuests;

            // Load story progress
            storyProgress = saveData.storyProgress;

            Debug.Log("Game loaded successfully");
        }
        else
        {
            Debug.Log("No save file found");
        }
    }

    public void OnPlayerDeath()
    {
        currentState = GameState.GameOver;

        if (gameUI != null)
        {
            gameUI.ShowGameOver();
        }

        Debug.Log("Player has died - Game Over");
    }

    public void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        Debug.Log($"Player's moral path changed to: {newPath}");

        // Trigger story events based on path
        switch (newPath)
        {
            case PlayerStats.MoralPath.Sun:
                TriggerStoryEvent("PlayerChooseSunPath");
                break;
            case PlayerStats.MoralPath.Moon:
                TriggerStoryEvent("PlayerChooseMoonPath");
                break;
            case PlayerStats.MoralPath.Eclipse:
                TriggerStoryEvent("PlayerChooseEclipsePath");
                break;
        }
    }

    public void TriggerStoryEvent(string eventName)
    {
        Debug.Log($"Story event triggered: {eventName}");

        switch (eventName)
        {
            case "PlayerChooseSunPath":
                if (!storyProgress.majorChoicesMade.Contains("SunPath"))
                {
                    storyProgress.majorChoicesMade.Add("SunPath");
                    // Unlock Sun Sage meeting
                }
                break;

            case "PlayerChooseMoonPath":
                if (!storyProgress.majorChoicesMade.Contains("MoonPath"))
                {
                    storyProgress.majorChoicesMade.Add("MoonPath");
                    // Unlock Moon Sage meeting
                }
                break;

            case "PlayerChooseEclipsePath":
                if (!storyProgress.majorChoicesMade.Contains("EclipsePath"))
                {
                    storyProgress.majorChoicesMade.Add("EclipsePath");
                    // Unlock both Sage meetings
                }
                break;

            case "MeetSunSage":
                storyProgress.hasMetSunSage = true;
                CheckParentageReveal();
                break;

            case "MeetMoonSage":
                storyProgress.hasMetMoonSage = true;
                CheckParentageReveal();
                break;
        }
    }

    void CheckParentageReveal()
    {
        // Reveal parentage based on which Sages the player has met
        if ((storyProgress.hasMetSunSage || storyProgress.hasMetMoonSage) && !storyProgress.knowsParentage)
        {
            storyProgress.knowsParentage = true;
            Debug.Log("Player discovers their true parentage!");
            // Trigger parentage reveal cutscene/dialogue
        }
    }

    public void UnlockArea(string areaName)
    {
        if (!unlockedAreas.Contains(areaName))
        {
            unlockedAreas.Add(areaName);
            Debug.Log($"New area unlocked: {areaName}");
        }
    }

    public void CompleteQuest(string questName)
    {
        if (!completedQuests.Contains(questName))
        {
            completedQuests.Add(questName);
            Debug.Log($"Quest completed: {questName}");
        }
    }

    public void RestartGame()
    {
        // Clear save data
        PlayerPrefs.DeleteKey("GameSave");

        // Reset game state
        currentState = GameState.Playing;
        Time.timeScale = 1f;

        // Reload current scene
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    public void QuitToMainMenu()
    {
        Time.timeScale = 1f;
        SceneManager.LoadScene("MainMenu");
    }

    // Missing methods for companion and ending systems
    public void AddCompanion(string companionName)
    {
        Debug.Log($"Companion '{companionName}' added to party");
        // Implementation would add to companions list
    }

    public void SetFutureAntagonist(string name, string type)
    {
        Debug.Log($"Future antagonist set: {name} ({type})");
        // Implementation would store for future encounters
    }

    public void UnlockEnding(string endingName)
    {
        Debug.Log($"Ending unlocked: {endingName}");
        // Implementation would add to unlocked endings list
    }

    public float GetPlayTime()
    {
        return Time.time; // Simplified implementation
    }

    public int GetCurrentLevel()
    {
        return currentLevel;
    }

    public bool IsGamePaused()
    {
        return currentState == GameState.Paused;
    }

    public bool HasPerformedAction(string actionName)
    {
        // Check if player has performed a specific action
        return storyProgress.majorChoicesMade.Contains(actionName);
    }

    // Getters
    public bool IsAreaUnlocked(string areaName) => unlockedAreas.Contains(areaName);
    public bool IsQuestCompleted(string questName) => completedQuests.Contains(questName);
    public GameState GetCurrentState() => currentState;
}

[System.Serializable]
public class GameSaveData
{
    public string playerName;
    public int currentLevel;
    public float currentHealth;
    public float currentMana;
    public float currentStamina;
    public float sunAlignment;
    public float moonAlignment;
    public PlayerStats.MoralPath currentPath;
    public string currentArea;
    public List<string> unlockedAreas;
    public List<string> completedQuests;
    public GameManager.StoryProgress storyProgress;
}
