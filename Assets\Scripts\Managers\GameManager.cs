using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// Central game manager handling save/load, story progression, and game state management.
/// Implements singleton pattern and manages <PERSON> Cinderborn's journey through the game world.
/// </summary>
public class GameManager : MonoBehaviour
{
    #region Serialized Fields
    [Header("Game Settings")]
    [SerializeField] private GameState currentState = GameState.Playing;
    [SerializeField] private string playerName = "Kael";
    [SerializeField] private int currentLevel = 1;

    [Header("Save System")]
    [SerializeField] private bool autoSave = true;
    [SerializeField] private float autoSaveInterval = 300f; // 5 minutes

    [Header("World Settings")]
    [SerializeField] private string currentArea = "Starting Village";
    [SerializeField] private List<string> unlockedAreas = new List<string>();
    [SerializeField] private List<string> completedQuests = new List<string>();

    [Header("Story Progress")]
    [SerializeField] private StoryProgress storyProgress = new StoryProgress();
    [SerializeField] private float currentStoryProgress; // 0 to 1

    [Header("The Cinderborn's Journey")]
    [SerializeField] private bool hasMetMysteriousStranger;
    [SerializeField] private int mysteriousStrangerEncounters;
    [SerializeField] private bool cinderbornsHeritageRevealed;
    [SerializeField] private bool finalBossUnlocked;
    [SerializeField] private float cinderbornsAge = 15f; // Starts at 15, grows to 25
    [SerializeField] private PsychologicalJourneyData psychologicalJourney = new PsychologicalJourneyData();
    #endregion

    #region Private Fields
    // Cached components
    private PlayerStats playerStats;
    private GameUI gameUI;
    private DialogueSystem dialogueSystem;

    // State tracking
    private float lastSaveTime;
    private static GameManager instance;
    #endregion

    #region Constants
    private const string SaveKey = "GameSave";
    private const string MainMenuSceneName = "MainMenu";
    #endregion

    #region Events
    /// <summary>
    /// Invoked when story progress changes.
    /// </summary>
    public event Action<float> OnStoryProgressChanged;

    /// <summary>
    /// Invoked when psychological state changes.
    /// </summary>
    public event Action<PsychologicalSystem.PsychologicalState> OnPsychologicalStateChanged;
    #endregion

    #region Data Structures
    /// <summary>
    /// Tracks the player's psychological journey throughout the game.
    /// </summary>
    [Serializable]
    public class PsychologicalJourneyData
    {
        public int totalMeditations;
        public int darkChoicesMade;
        public int lightChoicesMade;
        public int hallucinationsExperienced;
        public int hopefulMomentsExperienced;
        public float highestTrauma;
        public float highestEnlightenment;
        public bool hasReachedHollowState;
        public bool hasReachedEnlightenedState;
        public string[] significantPsychologicalEvents = new string[0];
    }

    /// <summary>
    /// Represents the current state of the game.
    /// </summary>
    public enum GameState
    {
        MainMenu,
        Playing,
        Paused,
        Dialogue,
        GameOver,
        Victory
    }

    /// <summary>
    /// Tracks story progression and major narrative choices.
    /// </summary>
    [Serializable]
    public class StoryProgress
    {
        public bool hasMetSunSage;
        public bool hasMetMoonSage;
        public bool knowsParentage;
        public bool hasChosenFinalPath;
        public List<string> majorChoicesMade = new List<string>();
        public Dictionary<string, float> npcRelationships = new Dictionary<string, float>();
    }
    #endregion

    #region Singleton Pattern
    /// <summary>
    /// Singleton instance of the GameManager.
    /// </summary>
    public static GameManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<GameManager>();
                if (instance == null)
                {
                    GameObject gameManagerObject = new GameObject("GameManager");
                    instance = gameManagerObject.AddComponent<GameManager>();
                }
            }
            return instance;
        }
    }
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize singleton pattern and game systems.
    /// </summary>
    private void Awake()
    {
        // Enforce singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeGame();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
            return;
        }
    }

    /// <summary>
    /// Find and cache game components after scene load.
    /// </summary>
    private void Start()
    {
        CacheGameComponents();
        SubscribeToEvents();
    }

    /// <summary>
    /// Handle auto-save and game state updates.
    /// </summary>
    private void Update()
    {
        HandleAutoSave();
        UpdateGameState();
    }

    /// <summary>
    /// Clean up event subscriptions when destroyed.
    /// </summary>
    private void OnDestroy()
    {
        UnsubscribeFromEvents();
    }
    #endregion

    #region Initialization
    /// <summary>
    /// Initialize default game settings and state.
    /// </summary>
    private void InitializeGame()
    {
        // Initialize default unlocked areas
        if (unlockedAreas.Count == 0)
        {
            unlockedAreas.Add("Starting Village");
        }

        // Set initial game state
        currentState = GameState.Playing;

        Debug.Log("Game Manager initialized");
    }

    /// <summary>
    /// Cache frequently used game components for performance.
    /// </summary>
    private void CacheGameComponents()
    {
        playerStats = FindObjectOfType<PlayerStats>();
        gameUI = FindObjectOfType<GameUI>();
        dialogueSystem = FindObjectOfType<DialogueSystem>();
    }

    /// <summary>
    /// Subscribe to player and system events.
    /// </summary>
    private void SubscribeToEvents()
    {
        if (playerStats != null)
        {
            playerStats.OnPlayerDeath += OnPlayerDeath;
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }
    }

    /// <summary>
    /// Unsubscribe from events to prevent memory leaks.
    /// </summary>
    private void UnsubscribeFromEvents()
    {
        if (playerStats != null)
        {
            playerStats.OnPlayerDeath -= OnPlayerDeath;
            playerStats.OnPathChanged -= OnPlayerPathChanged;
        }
    }
    #endregion

    #region Game State Management
    /// <summary>
    /// Handle automatic saving at specified intervals.
    /// </summary>
    private void HandleAutoSave()
    {
        if (autoSave && Time.time - lastSaveTime >= autoSaveInterval)
        {
            SaveGame();
            lastSaveTime = Time.time;
        }
    }

    /// <summary>
    /// Update game state based on current conditions.
    /// </summary>
    private void UpdateGameState()
    {
        // Check dialogue state first (cached for performance)
        if (dialogueSystem?.IsDialogueActive() == true)
        {
            currentState = GameState.Dialogue;
        }
        else if (Time.timeScale == 0f)
        {
            currentState = GameState.Paused;
        }
        else
        {
            currentState = GameState.Playing;
        }
    }
    #endregion

    #region Save System
    /// <summary>
    /// Save current game state to persistent storage.
    /// </summary>
    public void SaveGame()
    {
        try
        {
            GameSaveData saveData = CreateSaveData();
            string saveJson = JsonUtility.ToJson(saveData);
            PlayerPrefs.SetString(SaveKey, saveJson);
            PlayerPrefs.Save();

            Debug.Log("Game saved successfully");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save game: {e.Message}");
        }
    }

    /// <summary>
    /// Create save data from current game state.
    /// </summary>
    /// <returns>Populated save data object</returns>
    private GameSaveData CreateSaveData()
    {
        GameSaveData saveData = new GameSaveData
        {
            playerName = playerName,
            currentLevel = currentLevel,
            currentArea = currentArea,
            unlockedAreas = new List<string>(unlockedAreas),
            completedQuests = new List<string>(completedQuests),
            storyProgress = storyProgress
        };

        // Add player stats if available
        if (playerStats != null)
        {
            saveData.currentHealth = playerStats.GetCurrentHealth();
            saveData.currentMana = playerStats.GetCurrentMana();
            saveData.currentStamina = playerStats.GetCurrentStamina();
            saveData.sunAlignment = playerStats.GetSunAlignment();
            saveData.moonAlignment = playerStats.GetMoonAlignment();
            saveData.currentPath = playerStats.GetCurrentPath();
        }

        return saveData;
    }
    #endregion

    /// <summary>
    /// Load game state from persistent storage.
    /// </summary>
    public void LoadGame()
    {
        try
        {
            if (PlayerPrefs.HasKey(SaveKey))
            {
                string saveJson = PlayerPrefs.GetString(SaveKey);
                GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(saveJson);

                LoadSaveData(saveData);
                Debug.Log("Game loaded successfully");
            }
            else
            {
                Debug.Log("No save file found");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load game: {e.Message}");
        }
    }

    /// <summary>
    /// Apply loaded save data to current game state.
    /// </summary>
    /// <param name="saveData">Save data to load</param>
    private void LoadSaveData(GameSaveData saveData)
    {
        // Load basic game data
        playerName = saveData.playerName;
        currentLevel = saveData.currentLevel;
        currentArea = saveData.currentArea;
        unlockedAreas = saveData.unlockedAreas ?? new List<string>();
        completedQuests = saveData.completedQuests ?? new List<string>();
        storyProgress = saveData.storyProgress ?? new StoryProgress();

        // Load player stats if available
        if (playerStats != null)
        {
            // Note: Direct field access should be replaced with proper methods
            // This is a temporary implementation for the prototype
            Debug.Log("Loading player stats from save data");
        }
    }
    #endregion

    #region Event Handlers
    /// <summary>
    /// Handle player death event.
    /// </summary>
    public void OnPlayerDeath()
    {
        currentState = GameState.GameOver;

        if (gameUI != null)
        {
            gameUI.ShowGameOver();
        }

        Debug.Log("Player has died - Game Over");
    }

    /// <summary>
    /// Handle player moral path changes and trigger related story events.
    /// </summary>
    /// <param name="newPath">The new moral path</param>
    public void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        Debug.Log($"Player's moral path changed to: {newPath}");

        // Trigger story events based on path
        switch (newPath)
        {
            case PlayerStats.MoralPath.Sun:
                TriggerStoryEvent("PlayerChooseSunPath");
                break;
            case PlayerStats.MoralPath.Moon:
                TriggerStoryEvent("PlayerChooseMoonPath");
                break;
            case PlayerStats.MoralPath.Eclipse:
                TriggerStoryEvent("PlayerChooseEclipsePath");
                break;
        }
    }
    #endregion

    #region Story Management
    /// <summary>
    /// Trigger a story event and handle its consequences.
    /// </summary>
    /// <param name="eventName">Name of the story event to trigger</param>
    public void TriggerStoryEvent(string eventName)
    {
        Debug.Log($"Story event triggered: {eventName}");

        switch (eventName)
        {
            case "PlayerChooseSunPath":
                HandlePathChoice("SunPath");
                break;

            case "PlayerChooseMoonPath":
                HandlePathChoice("MoonPath");
                break;

            case "PlayerChooseEclipsePath":
                HandlePathChoice("EclipsePath");
                break;

            case "MeetSunSage":
                storyProgress.hasMetSunSage = true;
                CheckParentageReveal();
                break;

            case "MeetMoonSage":
                storyProgress.hasMetMoonSage = true;
                CheckParentageReveal();
                break;

            default:
                Debug.LogWarning($"Unknown story event: {eventName}");
                break;
        }
    }

    /// <summary>
    /// Handle moral path choice events.
    /// </summary>
    /// <param name="pathName">Name of the path chosen</param>
    private void HandlePathChoice(string pathName)
    {
        if (!storyProgress.majorChoicesMade.Contains(pathName))
        {
            storyProgress.majorChoicesMade.Add(pathName);
            Debug.Log($"Player chose {pathName} - unlocking related content");
        }
    }

    /// <summary>
    /// Check if parentage should be revealed based on sage meetings.
    /// </summary>
    private void CheckParentageReveal()
    {
        if ((storyProgress.hasMetSunSage || storyProgress.hasMetMoonSage) && !storyProgress.knowsParentage)
        {
            storyProgress.knowsParentage = true;
            Debug.Log("Player discovers their true parentage!");
            // TODO: Trigger parentage reveal cutscene/dialogue
        }
    }
    #endregion

    #region World Management
    /// <summary>
    /// Unlock a new area for exploration.
    /// </summary>
    /// <param name="areaName">Name of the area to unlock</param>
    public void UnlockArea(string areaName)
    {
        if (!unlockedAreas.Contains(areaName))
        {
            unlockedAreas.Add(areaName);
            Debug.Log($"New area unlocked: {areaName}");
        }
    }

    /// <summary>
    /// Mark a quest as completed.
    /// </summary>
    /// <param name="questName">Name of the completed quest</param>
    public void CompleteQuest(string questName)
    {
        if (!completedQuests.Contains(questName))
        {
            completedQuests.Add(questName);
            Debug.Log($"Quest completed: {questName}");
        }
    }
    #endregion

    #region Game Control
    /// <summary>
    /// Restart the game from the beginning.
    /// </summary>
    public void RestartGame()
    {
        // Clear save data
        PlayerPrefs.DeleteKey(SaveKey);

        // Reset game state
        currentState = GameState.Playing;
        Time.timeScale = 1f;

        // Reload current scene
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    /// <summary>
    /// Quit to main menu.
    /// </summary>
    public void QuitToMainMenu()
    {
        Time.timeScale = 1f;
        SceneManager.LoadScene(MainMenuSceneName);
    }
    #endregion

    #region Companion and Ending Systems
    /// <summary>
    /// Add a companion to the player's party.
    /// </summary>
    /// <param name="companionName">Name of the companion to add</param>
    public void AddCompanion(string companionName)
    {
        Debug.Log($"Companion '{companionName}' added to party");
        // TODO: Implementation would add to companions list
    }

    /// <summary>
    /// Set a future antagonist based on player choices.
    /// </summary>
    /// <param name="name">Name of the future antagonist</param>
    /// <param name="type">Type of antagonist</param>
    public void SetFutureAntagonist(string name, string type)
    {
        Debug.Log($"Future antagonist set: {name} ({type})");
        // TODO: Implementation would store for future encounters
    }

    /// <summary>
    /// Unlock a specific ending based on player choices.
    /// </summary>
    /// <param name="endingName">Name of the ending to unlock</param>
    public void UnlockEnding(string endingName)
    {
        Debug.Log($"Ending unlocked: {endingName}");
        // TODO: Implementation would add to unlocked endings list
    }
    #endregion

    #region Public API - Getters
    /// <summary>
    /// Get total play time (simplified implementation).
    /// </summary>
    /// <returns>Play time in seconds</returns>
    public float GetPlayTime() => Time.time;

    /// <summary>
    /// Get the player's current level.
    /// </summary>
    /// <returns>Current level</returns>
    public int GetCurrentLevel() => currentLevel;

    /// <summary>
    /// Check if the game is currently paused.
    /// </summary>
    /// <returns>True if game is paused</returns>
    public bool IsGamePaused() => currentState == GameState.Paused;

    /// <summary>
    /// Check if the player has performed a specific action.
    /// </summary>
    /// <param name="actionName">Name of the action to check</param>
    /// <returns>True if action has been performed</returns>
    public bool HasPerformedAction(string actionName) => storyProgress.majorChoicesMade.Contains(actionName);

    /// <summary>
    /// Check if an area is unlocked.
    /// </summary>
    /// <param name="areaName">Name of the area to check</param>
    /// <returns>True if area is unlocked</returns>
    public bool IsAreaUnlocked(string areaName) => unlockedAreas.Contains(areaName);

    /// <summary>
    /// Check if a quest is completed.
    /// </summary>
    /// <param name="questName">Name of the quest to check</param>
    /// <returns>True if quest is completed</returns>
    public bool IsQuestCompleted(string questName) => completedQuests.Contains(questName);

    /// <summary>
    /// Get the current game state.
    /// </summary>
    /// <returns>Current game state</returns>
    public GameState GetCurrentState() => currentState;

    /// <summary>
    /// Get the player's name.
    /// </summary>
    /// <returns>Player name</returns>
    public string GetPlayerName() => playerName;

    /// <summary>
    /// Get the current area name.
    /// </summary>
    /// <returns>Current area name</returns>
    public string GetCurrentArea() => currentArea;
    #endregion
}

/// <summary>
/// Serializable data structure for saving and loading game state.
/// Contains all necessary information to restore a player's progress.
/// </summary>
[Serializable]
public class GameSaveData
{
    [Header("Player Information")]
    public string playerName;
    public int currentLevel;

    [Header("Player Stats")]
    public float currentHealth;
    public float currentMana;
    public float currentStamina;
    public float sunAlignment;
    public float moonAlignment;
    public PlayerStats.MoralPath currentPath;

    [Header("World State")]
    public string currentArea;
    public List<string> unlockedAreas;
    public List<string> completedQuests;

    [Header("Story Progress")]
    public GameManager.StoryProgress storyProgress;
}
