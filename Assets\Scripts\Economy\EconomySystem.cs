using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.Economy
{
    /// <summary>
    /// Economy and Trading System for Cinder of Darkness.
    /// Manages multiple currencies, shops, merchants, and trading mechanics.
    /// </summary>
    public class EconomySystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Economy Settings")]
        [SerializeField] private bool enableEconomy = true;
        [SerializeField] private float inflationRate = 0.01f;
        [SerializeField] private float demandDecayRate = 0.1f;
        [SerializeField] private int maxTransactionHistory = 100;

        [Header("Currency Settings")]
        [SerializeField] private CurrencyType[] availableCurrencies;
        [SerializeField] private float[] exchangeRates;

        [Header("Shop System")]
        [SerializeField] private ShopData[] permanentShops;
        [SerializeField] private MerchantData[] travelingMerchants;
        [SerializeField] private float merchantSpawnInterval = 300f; // 5 minutes

        [Header("Item Database")]
        [SerializeField] private ItemData[] allItems;
        [SerializeField] private RegionalPricing[] regionalPricing;

        [Header("UI References")]
        [SerializeField] private GameObject shopUI;
        [SerializeField] private GameObject inventoryUI;
        [SerializeField] private Transform shopItemContainer;
        [SerializeField] private GameObject shopItemPrefab;
        [SerializeField] private TMPro.TextMeshProUGUI[] currencyDisplays;

        [Header("Audio")]
        [SerializeField] private AudioClip purchaseSound;
        [SerializeField] private AudioClip sellSound;
        [SerializeField] private AudioClip insufficientFundsSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Public Properties
        public static EconomySystem Instance { get; private set; }
        public Dictionary<CurrencyType, int> PlayerCurrencies { get; private set; } = new Dictionary<CurrencyType, int>();
        public List<ShopInstance> ActiveShops { get; private set; } = new List<ShopInstance>();
        public List<MerchantInstance> ActiveMerchants { get; private set; } = new List<MerchantInstance>();
        public ShopInstance CurrentShop { get; private set; }
        #endregion

        #region Private Fields
        private DynamicNarrativeSystem narrativeSystem;
        private PlayerInventory playerInventory;

        // Economy tracking
        private Dictionary<string, float> itemDemand = new Dictionary<string, float>();
        private Dictionary<string, float> itemSupply = new Dictionary<string, float>();
        private List<Transaction> transactionHistory = new List<Transaction>();

        // Merchant system
        private float lastMerchantSpawn;
        private Dictionary<string, float> merchantCooldowns = new Dictionary<string, float>();

        // Regional economy
        private string currentRegion = "default";
        private Dictionary<string, RegionalEconomy> regionalEconomies = new Dictionary<string, RegionalEconomy>();

        // Performance optimization
        private float lastEconomyUpdate;
        private const float economyUpdateInterval = 10f;
        #endregion

        #region Events
        public System.Action<Transaction> OnTransactionCompleted;
        public System.Action<CurrencyType, int> OnCurrencyChanged;
        public System.Action<ShopInstance> OnShopOpened;
        public System.Action<ShopInstance> OnShopClosed;
        public System.Action<MerchantInstance> OnMerchantArrived;
        public System.Action<MerchantInstance> OnMerchantDeparted;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeEconomySystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            InitializeCurrencies();
            InitializeShops();
            LoadEconomyData();
            UpdateCurrencyDisplay();
        }

        private void Update()
        {
            if (!enableEconomy) return;

            UpdateEconomy();
            UpdateMerchants();
            CheckMerchantSpawns();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the economy system.
        /// </summary>
        private void InitializeEconomySystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }

            Debug.Log("Economy System initialized");
        }

        /// <summary>
        /// Setup system references.
        /// </summary>
        private void SetupSystemReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            playerInventory = FindObjectOfType<PlayerInventory>();
        }

        /// <summary>
        /// Initialize player currencies.
        /// </summary>
        private void InitializeCurrencies()
        {
            foreach (var currency in availableCurrencies)
            {
                if (!PlayerCurrencies.ContainsKey(currency.currencyType))
                {
                    PlayerCurrencies[currency.currencyType] = currency.startingAmount;
                }
            }
        }

        /// <summary>
        /// Initialize permanent shops.
        /// </summary>
        private void InitializeShops()
        {
            foreach (var shopData in permanentShops)
            {
                var shopInstance = new ShopInstance
                {
                    shopData = shopData,
                    currentInventory = GenerateShopInventory(shopData),
                    lastRestockTime = Time.time,
                    isOpen = true
                };

                ActiveShops.Add(shopInstance);
            }
        }
        #endregion

        #region Currency Management
        /// <summary>
        /// Add currency to player.
        /// </summary>
        /// <param name="currencyType">Currency type</param>
        /// <param name="amount">Amount to add</param>
        public void AddCurrency(CurrencyType currencyType, int amount)
        {
            if (!PlayerCurrencies.ContainsKey(currencyType))
            {
                PlayerCurrencies[currencyType] = 0;
            }

            PlayerCurrencies[currencyType] += amount;
            OnCurrencyChanged?.Invoke(currencyType, PlayerCurrencies[currencyType]);
            UpdateCurrencyDisplay();
        }

        /// <summary>
        /// Spend currency.
        /// </summary>
        /// <param name="currencyType">Currency type</param>
        /// <param name="amount">Amount to spend</param>
        /// <returns>True if currency was spent</returns>
        public bool SpendCurrency(CurrencyType currencyType, int amount)
        {
            if (!HasCurrency(currencyType, amount)) return false;

            PlayerCurrencies[currencyType] -= amount;
            OnCurrencyChanged?.Invoke(currencyType, PlayerCurrencies[currencyType]);
            UpdateCurrencyDisplay();
            return true;
        }

        /// <summary>
        /// Check if player has enough currency.
        /// </summary>
        /// <param name="currencyType">Currency type</param>
        /// <param name="amount">Amount to check</param>
        /// <returns>True if player has enough currency</returns>
        public bool HasCurrency(CurrencyType currencyType, int amount)
        {
            return PlayerCurrencies.ContainsKey(currencyType) && PlayerCurrencies[currencyType] >= amount;
        }

        /// <summary>
        /// Get player currency amount.
        /// </summary>
        /// <param name="currencyType">Currency type</param>
        /// <returns>Currency amount</returns>
        public int GetCurrency(CurrencyType currencyType)
        {
            return PlayerCurrencies.ContainsKey(currencyType) ? PlayerCurrencies[currencyType] : 0;
        }

        /// <summary>
        /// Exchange currency between types.
        /// </summary>
        /// <param name="fromCurrency">Source currency</param>
        /// <param name="toCurrency">Target currency</param>
        /// <param name="amount">Amount to exchange</param>
        /// <returns>True if exchange was successful</returns>
        public bool ExchangeCurrency(CurrencyType fromCurrency, CurrencyType toCurrency, int amount)
        {
            if (!HasCurrency(fromCurrency, amount)) return false;

            float exchangeRate = GetExchangeRate(fromCurrency, toCurrency);
            int convertedAmount = Mathf.FloorToInt(amount * exchangeRate);

            SpendCurrency(fromCurrency, amount);
            AddCurrency(toCurrency, convertedAmount);

            return true;
        }

        /// <summary>
        /// Get exchange rate between currencies.
        /// </summary>
        /// <param name="fromCurrency">Source currency</param>
        /// <param name="toCurrency">Target currency</param>
        /// <returns>Exchange rate</returns>
        private float GetExchangeRate(CurrencyType fromCurrency, CurrencyType toCurrency)
        {
            // Simplified exchange rate calculation
            int fromIndex = System.Array.FindIndex(availableCurrencies, c => c.currencyType == fromCurrency);
            int toIndex = System.Array.FindIndex(availableCurrencies, c => c.currencyType == toCurrency);

            if (fromIndex >= 0 && toIndex >= 0 && fromIndex < exchangeRates.Length && toIndex < exchangeRates.Length)
            {
                return exchangeRates[toIndex] / exchangeRates[fromIndex];
            }

            return 1f;
        }
        #endregion

        #region Shop Management
        /// <summary>
        /// Open shop interface.
        /// </summary>
        /// <param name="shopId">Shop ID</param>
        public void OpenShop(string shopId)
        {
            var shop = ActiveShops.FirstOrDefault(s => s.shopData.shopId == shopId);
            if (shop == null) return;

            CurrentShop = shop;

            if (shopUI != null)
            {
                shopUI.SetActive(true);
            }

            RefreshShopUI();
            OnShopOpened?.Invoke(shop);
        }

        /// <summary>
        /// Close current shop.
        /// </summary>
        public void CloseShop()
        {
            if (CurrentShop == null) return;

            if (shopUI != null)
            {
                shopUI.SetActive(false);
            }

            OnShopClosed?.Invoke(CurrentShop);
            CurrentShop = null;
        }

        /// <summary>
        /// Purchase item from shop.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="quantity">Quantity to purchase</param>
        /// <returns>True if purchase was successful</returns>
        public bool PurchaseItem(string itemId, int quantity = 1)
        {
            if (CurrentShop == null) return false;

            var shopItem = CurrentShop.currentInventory.FirstOrDefault(i => i.itemData.itemId == itemId);
            if (shopItem == null || shopItem.quantity < quantity) return false;

            int totalCost = CalculateItemPrice(shopItem.itemData, quantity, true);
            var currencyType = shopItem.itemData.currencyType;

            if (!HasCurrency(currencyType, totalCost))
            {
                PlaySound(insufficientFundsSound);
                return false;
            }

            // Complete transaction
            SpendCurrency(currencyType, totalCost);
            shopItem.quantity -= quantity;

            // Add to player inventory
            if (playerInventory != null)
            {
                playerInventory.AddItem(itemId, quantity);
            }

            // Record transaction
            RecordTransaction(new Transaction
            {
                itemId = itemId,
                quantity = quantity,
                price = totalCost,
                currencyType = currencyType,
                transactionType = TransactionType.Purchase,
                shopId = CurrentShop.shopData.shopId,
                timestamp = Time.time
            });

            // Update demand
            UpdateItemDemand(itemId, quantity);

            PlaySound(purchaseSound);
            RefreshShopUI();
            return true;
        }

        /// <summary>
        /// Sell item to shop.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="quantity">Quantity to sell</param>
        /// <returns>True if sale was successful</returns>
        public bool SellItem(string itemId, int quantity = 1)
        {
            if (CurrentShop == null || playerInventory == null) return false;
            if (!playerInventory.HasItem(itemId, quantity)) return false;

            var itemData = GetItemData(itemId);
            if (itemData == null) return false;

            int sellPrice = CalculateItemPrice(itemData, quantity, false);
            var currencyType = itemData.currencyType;

            // Complete transaction
            playerInventory.RemoveItem(itemId, quantity);
            AddCurrency(currencyType, sellPrice);

            // Add to shop inventory
            var shopItem = CurrentShop.currentInventory.FirstOrDefault(i => i.itemData.itemId == itemId);
            if (shopItem != null)
            {
                shopItem.quantity += quantity;
            }
            else
            {
                CurrentShop.currentInventory.Add(new ShopItem
                {
                    itemData = itemData,
                    quantity = quantity,
                    basePrice = itemData.basePrice
                });
            }

            // Record transaction
            RecordTransaction(new Transaction
            {
                itemId = itemId,
                quantity = quantity,
                price = sellPrice,
                currencyType = currencyType,
                transactionType = TransactionType.Sale,
                shopId = CurrentShop.shopData.shopId,
                timestamp = Time.time
            });

            // Update supply
            UpdateItemSupply(itemId, quantity);

            PlaySound(sellSound);
            RefreshShopUI();
            return true;
        }

        /// <summary>
        /// Calculate item price with modifiers.
        /// </summary>
        /// <param name="itemData">Item data</param>
        /// <param name="quantity">Quantity</param>
        /// <param name="buying">True if buying, false if selling</param>
        /// <returns>Total price</returns>
        private int CalculateItemPrice(ItemData itemData, int quantity, bool buying)
        {
            float basePrice = itemData.basePrice;

            // Apply regional pricing
            basePrice *= GetRegionalPriceModifier(itemData.itemId);

            // Apply demand/supply modifiers
            float demandModifier = GetDemandModifier(itemData.itemId);
            float supplyModifier = GetSupplyModifier(itemData.itemId);

            if (buying)
            {
                basePrice *= (1f + demandModifier - supplyModifier);
            }
            else
            {
                basePrice *= 0.6f; // Sell for 60% of buy price
                basePrice *= (1f - demandModifier + supplyModifier);
            }

            // Apply reputation modifier
            if (narrativeSystem != null)
            {
                float reputationModifier = narrativeSystem.GetReputationModifier("merchant");
                basePrice *= (1f - reputationModifier * 0.1f); // Up to 10% discount
            }

            return Mathf.Max(1, Mathf.RoundToInt(basePrice * quantity));
        }

        /// <summary>
        /// Generate shop inventory based on shop data.
        /// </summary>
        /// <param name="shopData">Shop data</param>
        /// <returns>List of shop items</returns>
        private List<ShopItem> GenerateShopInventory(ShopData shopData)
        {
            var inventory = new List<ShopItem>();

            foreach (var itemCategory in shopData.itemCategories)
            {
                var categoryItems = allItems.Where(i => i.category == itemCategory).ToArray();
                int itemCount = Random.Range(shopData.minItems, shopData.maxItems + 1);

                for (int i = 0; i < itemCount && i < categoryItems.Length; i++)
                {
                    var itemData = categoryItems[Random.Range(0, categoryItems.Length)];
                    var existingItem = inventory.FirstOrDefault(item => item.itemData.itemId == itemData.itemId);

                    if (existingItem != null)
                    {
                        existingItem.quantity += Random.Range(1, 5);
                    }
                    else
                    {
                        inventory.Add(new ShopItem
                        {
                            itemData = itemData,
                            quantity = Random.Range(1, 10),
                            basePrice = itemData.basePrice
                        });
                    }
                }
            }

            return inventory;
        }
        #endregion

        #region Merchant System
        /// <summary>
        /// Check for merchant spawn opportunities.
        /// </summary>
        private void CheckMerchantSpawns()
        {
            if (Time.time - lastMerchantSpawn < merchantSpawnInterval) return;

            foreach (var merchantData in travelingMerchants)
            {
                if (ShouldSpawnMerchant(merchantData))
                {
                    SpawnMerchant(merchantData);
                    break; // Only spawn one merchant at a time
                }
            }

            lastMerchantSpawn = Time.time;
        }

        /// <summary>
        /// Check if merchant should spawn.
        /// </summary>
        /// <param name="merchantData">Merchant data</param>
        /// <returns>True if merchant should spawn</returns>
        private bool ShouldSpawnMerchant(MerchantData merchantData)
        {
            // Check cooldown
            if (merchantCooldowns.ContainsKey(merchantData.merchantId) &&
                Time.time - merchantCooldowns[merchantData.merchantId] < merchantData.cooldownTime)
            {
                return false;
            }

            // Check spawn chance
            return Random.Range(0f, 1f) < merchantData.spawnChance;
        }

        /// <summary>
        /// Spawn traveling merchant.
        /// </summary>
        /// <param name="merchantData">Merchant data</param>
        private void SpawnMerchant(MerchantData merchantData)
        {
            var merchantInstance = new MerchantInstance
            {
                merchantData = merchantData,
                currentInventory = GenerateMerchantInventory(merchantData),
                arrivalTime = Time.time,
                departureTime = Time.time + merchantData.stayDuration
            };

            ActiveMerchants.Add(merchantInstance);
            merchantCooldowns[merchantData.merchantId] = Time.time;

            OnMerchantArrived?.Invoke(merchantInstance);
            Debug.Log($"Traveling merchant {merchantData.merchantName} has arrived!");
        }

        /// <summary>
        /// Generate merchant inventory.
        /// </summary>
        /// <param name="merchantData">Merchant data</param>
        /// <returns>List of shop items</returns>
        private List<ShopItem> GenerateMerchantInventory(MerchantData merchantData)
        {
            var inventory = new List<ShopItem>();

            // Add rare items
            foreach (var rareItemId in merchantData.rareItems)
            {
                var itemData = GetItemData(rareItemId);
                if (itemData != null)
                {
                    inventory.Add(new ShopItem
                    {
                        itemData = itemData,
                        quantity = 1,
                        basePrice = itemData.basePrice * 2 // Rare items cost more
                    });
                }
            }

            return inventory;
        }

        /// <summary>
        /// Update active merchants.
        /// </summary>
        private void UpdateMerchants()
        {
            for (int i = ActiveMerchants.Count - 1; i >= 0; i--)
            {
                var merchant = ActiveMerchants[i];

                if (Time.time >= merchant.departureTime)
                {
                    OnMerchantDeparted?.Invoke(merchant);
                    ActiveMerchants.RemoveAt(i);
                    Debug.Log($"Traveling merchant {merchant.merchantData.merchantName} has departed!");
                }
            }
        }
        #endregion

        #region Economy Updates
        /// <summary>
        /// Update economy simulation.
        /// </summary>
        private void UpdateEconomy()
        {
            if (Time.time - lastEconomyUpdate < economyUpdateInterval) return;

            UpdateDemandAndSupply();
            ApplyInflation();
            UpdateRegionalEconomies();

            lastEconomyUpdate = Time.time;
        }

        /// <summary>
        /// Update item demand and supply.
        /// </summary>
        private void UpdateDemandAndSupply()
        {
            // Decay demand and supply over time
            var demandKeys = itemDemand.Keys.ToList();
            foreach (var itemId in demandKeys)
            {
                itemDemand[itemId] = Mathf.Max(0f, itemDemand[itemId] - demandDecayRate * Time.deltaTime);
            }

            var supplyKeys = itemSupply.Keys.ToList();
            foreach (var itemId in supplyKeys)
            {
                itemSupply[itemId] = Mathf.Max(0f, itemSupply[itemId] - demandDecayRate * Time.deltaTime);
            }
        }

        /// <summary>
        /// Apply inflation to prices.
        /// </summary>
        private void ApplyInflation()
        {
            // Simple inflation simulation
            foreach (var shop in ActiveShops)
            {
                foreach (var item in shop.currentInventory)
                {
                    item.basePrice *= (1f + inflationRate * Time.deltaTime);
                }
            }
        }

        /// <summary>
        /// Update regional economies.
        /// </summary>
        private void UpdateRegionalEconomies()
        {
            foreach (var kvp in regionalEconomies)
            {
                var economy = kvp.Value;

                // Update economic health based on trade activity
                float tradeActivity = GetRecentTradeActivity(kvp.Key);
                economy.economicHealth = Mathf.Lerp(economy.economicHealth, tradeActivity, 0.1f);
            }
        }

        /// <summary>
        /// Get recent trade activity for region.
        /// </summary>
        /// <param name="regionId">Region ID</param>
        /// <returns>Trade activity level</returns>
        private float GetRecentTradeActivity(string regionId)
        {
            var recentTransactions = transactionHistory.Where(t =>
                Time.time - t.timestamp < 300f).ToList(); // Last 5 minutes

            return recentTransactions.Count / 10f; // Normalize
        }

        /// <summary>
        /// Update item demand.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="quantity">Quantity purchased</param>
        private void UpdateItemDemand(string itemId, int quantity)
        {
            if (!itemDemand.ContainsKey(itemId))
            {
                itemDemand[itemId] = 0f;
            }

            itemDemand[itemId] += quantity * 0.1f;
        }

        /// <summary>
        /// Update item supply.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="quantity">Quantity sold</param>
        private void UpdateItemSupply(string itemId, int quantity)
        {
            if (!itemSupply.ContainsKey(itemId))
            {
                itemSupply[itemId] = 0f;
            }

            itemSupply[itemId] += quantity * 0.1f;
        }

        /// <summary>
        /// Get demand modifier for item.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Demand modifier</returns>
        private float GetDemandModifier(string itemId)
        {
            return itemDemand.ContainsKey(itemId) ? Mathf.Clamp(itemDemand[itemId], 0f, 1f) : 0f;
        }

        /// <summary>
        /// Get supply modifier for item.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Supply modifier</returns>
        private float GetSupplyModifier(string itemId)
        {
            return itemSupply.ContainsKey(itemId) ? Mathf.Clamp(itemSupply[itemId], 0f, 1f) : 0f;
        }

        /// <summary>
        /// Get regional price modifier.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Regional price modifier</returns>
        private float GetRegionalPriceModifier(string itemId)
        {
            var pricing = regionalPricing.FirstOrDefault(p => p.regionId == currentRegion && p.itemId == itemId);
            return pricing?.priceModifier ?? 1f;
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Update currency display.
        /// </summary>
        private void UpdateCurrencyDisplay()
        {
            if (currencyDisplays == null) return;

            for (int i = 0; i < currencyDisplays.Length && i < availableCurrencies.Length; i++)
            {
                if (currencyDisplays[i] != null)
                {
                    var currencyType = availableCurrencies[i].currencyType;
                    int amount = GetCurrency(currencyType);
                    currencyDisplays[i].text = $"{availableCurrencies[i].currencyName}: {amount}";
                }
            }
        }

        /// <summary>
        /// Refresh shop UI display.
        /// </summary>
        private void RefreshShopUI()
        {
            if (CurrentShop == null || shopItemContainer == null || shopItemPrefab == null) return;

            // Clear existing items
            foreach (Transform child in shopItemContainer)
            {
                Destroy(child.gameObject);
            }

            // Create shop item UI elements
            foreach (var shopItem in CurrentShop.currentInventory)
            {
                if (shopItem.quantity > 0)
                {
                    CreateShopItemUI(shopItem);
                }
            }
        }

        /// <summary>
        /// Create UI element for shop item.
        /// </summary>
        /// <param name="shopItem">Shop item</param>
        private void CreateShopItemUI(ShopItem shopItem)
        {
            GameObject itemObject = Instantiate(shopItemPrefab, shopItemContainer);

            var itemUI = itemObject.GetComponent<ShopItemUI>();
            if (itemUI != null)
            {
                int price = CalculateItemPrice(shopItem.itemData, 1, true);
                itemUI.Setup(shopItem, price);
            }
        }
        #endregion

        #region Transaction Management
        /// <summary>
        /// Record transaction in history.
        /// </summary>
        /// <param name="transaction">Transaction to record</param>
        private void RecordTransaction(Transaction transaction)
        {
            transactionHistory.Add(transaction);

            // Maintain history size
            while (transactionHistory.Count > maxTransactionHistory)
            {
                transactionHistory.RemoveAt(0);
            }

            OnTransactionCompleted?.Invoke(transaction);
        }

        /// <summary>
        /// Get transaction history.
        /// </summary>
        /// <returns>List of transactions</returns>
        public List<Transaction> GetTransactionHistory()
        {
            return new List<Transaction>(transactionHistory);
        }

        /// <summary>
        /// Get transaction statistics.
        /// </summary>
        /// <returns>Transaction statistics</returns>
        public TransactionStats GetTransactionStats()
        {
            var recentTransactions = transactionHistory.Where(t => Time.time - t.timestamp < 3600f).ToList(); // Last hour

            return new TransactionStats
            {
                totalTransactions = transactionHistory.Count,
                recentTransactions = recentTransactions.Count,
                totalSpent = transactionHistory.Where(t => t.transactionType == TransactionType.Purchase).Sum(t => t.price),
                totalEarned = transactionHistory.Where(t => t.transactionType == TransactionType.Sale).Sum(t => t.price),
                mostTradedItem = GetMostTradedItem()
            };
        }

        /// <summary>
        /// Get most traded item.
        /// </summary>
        /// <returns>Most traded item ID</returns>
        private string GetMostTradedItem()
        {
            var itemCounts = transactionHistory.GroupBy(t => t.itemId)
                .ToDictionary(g => g.Key, g => g.Sum(t => t.quantity));

            return itemCounts.Count > 0 ? itemCounts.OrderByDescending(kvp => kvp.Value).First().Key : "";
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Get item data by ID.
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <returns>Item data or null</returns>
        private ItemData GetItemData(string itemId)
        {
            return allItems?.FirstOrDefault(i => i.itemId == itemId);
        }

        /// <summary>
        /// Play audio clip.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save economy data.
        /// </summary>
        public void SaveEconomyData()
        {
            var saveData = new EconomySaveData
            {
                playerCurrencies = PlayerCurrencies,
                itemDemand = itemDemand,
                itemSupply = itemSupply,
                transactionHistory = transactionHistory.ToArray(),
                currentRegion = currentRegion,
                merchantCooldowns = merchantCooldowns
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("EconomySystemData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load economy data.
        /// </summary>
        private void LoadEconomyData()
        {
            string json = PlayerPrefs.GetString("EconomySystemData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<EconomySaveData>(json);

                    if (saveData.playerCurrencies != null)
                    {
                        PlayerCurrencies = saveData.playerCurrencies;
                    }

                    if (saveData.itemDemand != null)
                    {
                        itemDemand = saveData.itemDemand;
                    }

                    if (saveData.itemSupply != null)
                    {
                        itemSupply = saveData.itemSupply;
                    }

                    if (saveData.transactionHistory != null)
                    {
                        transactionHistory = saveData.transactionHistory.ToList();
                    }

                    currentRegion = saveData.currentRegion ?? "default";

                    if (saveData.merchantCooldowns != null)
                    {
                        merchantCooldowns = saveData.merchantCooldowns;
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load economy data: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set current region.
        /// </summary>
        /// <param name="regionId">Region ID</param>
        public void SetCurrentRegion(string regionId)
        {
            currentRegion = regionId;

            if (!regionalEconomies.ContainsKey(regionId))
            {
                regionalEconomies[regionId] = new RegionalEconomy
                {
                    regionId = regionId,
                    itemDemand = new Dictionary<string, float>(),
                    itemSupply = new Dictionary<string, float>(),
                    economicHealth = 0.5f
                };
            }
        }

        /// <summary>
        /// Get shop by ID.
        /// </summary>
        /// <param name="shopId">Shop ID</param>
        /// <returns>Shop instance or null</returns>
        public ShopInstance GetShop(string shopId)
        {
            return ActiveShops.FirstOrDefault(s => s.shopData.shopId == shopId);
        }

        /// <summary>
        /// Get merchant by ID.
        /// </summary>
        /// <param name="merchantId">Merchant ID</param>
        /// <returns>Merchant instance or null</returns>
        public MerchantInstance GetMerchant(string merchantId)
        {
            return ActiveMerchants.FirstOrDefault(m => m.merchantData.merchantId == merchantId);
        }

        /// <summary>
        /// Force merchant spawn (for testing).
        /// </summary>
        /// <param name="merchantId">Merchant ID</param>
        public void ForceSpawnMerchant(string merchantId)
        {
            var merchantData = travelingMerchants.FirstOrDefault(m => m.merchantId == merchantId);
            if (merchantData != null)
            {
                SpawnMerchant(merchantData);
            }
        }

        /// <summary>
        /// Reset economy system.
        /// </summary>
        public void ResetEconomy()
        {
            PlayerCurrencies.Clear();
            itemDemand.Clear();
            itemSupply.Clear();
            transactionHistory.Clear();
            merchantCooldowns.Clear();
            ActiveMerchants.Clear();

            InitializeCurrencies();
            currentRegion = "default";

            Debug.Log("Economy system reset");
        }

        /// <summary>
        /// Get economy statistics.
        /// </summary>
        /// <returns>Economy statistics</returns>
        public EconomyStats GetEconomyStats()
        {
            return new EconomyStats
            {
                totalCurrencies = PlayerCurrencies.Values.Sum(),
                activeShops = ActiveShops.Count,
                activeMerchants = ActiveMerchants.Count,
                totalTransactions = transactionHistory.Count,
                economicHealth = regionalEconomies.ContainsKey(currentRegion) ?
                    regionalEconomies[currentRegion].economicHealth : 0.5f
            };
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class EconomySaveData
    {
        public Dictionary<CurrencyEnum, int> playerCurrencies;
        public Dictionary<string, float> itemDemand;
        public Dictionary<string, float> itemSupply;
        public Transaction[] transactionHistory;
        public string currentRegion;
        public Dictionary<string, float> merchantCooldowns;
    }

    [System.Serializable]
    public class TransactionStats
    {
        public int totalTransactions;
        public int recentTransactions;
        public int totalSpent;
        public int totalEarned;
        public string mostTradedItem;
    }

    [System.Serializable]
    public class EconomyStats
    {
        public int totalCurrencies;
        public int activeShops;
        public int activeMerchants;
        public int totalTransactions;
        public float economicHealth;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class CurrencyType
    {
        public string currencyName;
        public CurrencyEnum currencyType;
        public int startingAmount;
        public Sprite currencyIcon;
    }

    [System.Serializable]
    public class ItemData
    {
        public string itemId;
        public string itemName;
        public string description;
        public ItemCategory category;
        public int basePrice;
        public CurrencyEnum currencyType;
        public Sprite itemIcon;
        public GameObject itemPrefab;
    }

    [System.Serializable]
    public class ShopData
    {
        public string shopId;
        public string shopName;
        public ItemCategory[] itemCategories;
        public int minItems;
        public int maxItems;
        public float restockInterval;
        public Vector3 shopPosition;
    }

    [System.Serializable]
    public class MerchantData
    {
        public string merchantId;
        public string merchantName;
        public string[] rareItems;
        public float spawnChance;
        public float stayDuration;
        public float cooldownTime;
    }

    [System.Serializable]
    public class ShopItem
    {
        public ItemData itemData;
        public int quantity;
        public float basePrice;
    }

    [System.Serializable]
    public class ShopInstance
    {
        public ShopData shopData;
        public List<ShopItem> currentInventory;
        public float lastRestockTime;
        public bool isOpen;
    }

    [System.Serializable]
    public class MerchantInstance
    {
        public MerchantData merchantData;
        public List<ShopItem> currentInventory;
        public float arrivalTime;
        public float departureTime;
    }

    [System.Serializable]
    public class Transaction
    {
        public string itemId;
        public int quantity;
        public int price;
        public CurrencyEnum currencyType;
        public TransactionType transactionType;
        public string shopId;
        public float timestamp;
    }

    [System.Serializable]
    public class RegionalPricing
    {
        public string regionId;
        public string itemId;
        public float priceModifier;
    }

    [System.Serializable]
    public class RegionalEconomy
    {
        public string regionId;
        public Dictionary<string, float> itemDemand;
        public Dictionary<string, float> itemSupply;
        public float economicHealth;
    }

    public enum CurrencyEnum
    {
        Gold,
        Silver,
        Souls,
        Gems
    }

    public enum ItemCategory
    {
        Weapon,
        Armor,
        Consumable,
        Material,
        Quest,
        Rare,
        Artifact
    }

    public enum TransactionType
    {
        Purchase,
        Sale,
        Trade,
        Quest
    }
    #endregion
}
