using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
using System.IO;
using System.Reflection;

/// <summary>
/// Community Event Manager for Dynamic World Events in Cinder of Darkness
/// Handles loading, validation, and execution of community-created world events
/// </summary>
public class CommunityEventManager : MonoBehaviour
{
    [Header("Community Event Settings")]
    public bool enableCommunityEvents = true;
    public bool enableUntrustedMods = false;
    public int maxConcurrentCommunityEvents = 2;
    public float communityEventFrequencyMultiplier = 0.5f;
    
    [Header("File Paths")]
    public string communityEventsPath = "/Mods/Events/Community/";
    public string userEventsPath = "/Mods/Events/User/";
    public string trustedAuthorsPath = "/Mods/Events/TrustedAuthors.json";
    
    [Header("Validation Settings")]
    public bool strictValidation = true;
    public bool requireSignedMods = false;
    public int maxEventDuration = 30; // days
    public int maxCustomEffects = 10;
    
    // Static instance
    private static CommunityEventManager instance;
    public static CommunityEventManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CommunityEventManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("CommunityEventManager");
                    instance = go.AddComponent<CommunityEventManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Community event management
    private List<IWorldEventModProvider> loadedModProviders = new List<IWorldEventModProvider>();
    private Dictionary<string, CommunityWorldEvent> communityEvents = new Dictionary<string, CommunityWorldEvent>();
    private Dictionary<string, ICustomEventTrigger> customTriggers = new Dictionary<string, ICustomEventTrigger>();
    private Dictionary<string, ICustomEventEffect> customEffects = new Dictionary<string, ICustomEventEffect>();
    private HashSet<string> trustedAuthors = new HashSet<string>();
    private List<string> activeCommunityEvents = new List<string>();
    
    // Integration references
    private EventManager eventManager;
    private ArenaManager arenaManager;
    private GameContext gameContext;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCommunityEventManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        StartCoroutine(InitializeAfterGameSystems());
    }
    
    void InitializeCommunityEventManager()
    {
        // Create directories if they don't exist
        CreateDirectories();
        
        // Load trusted authors
        LoadTrustedAuthors();
        
        Debug.Log("Community Event Manager initialized");
    }
    
    /// <summary>
    /// Initialize after game systems are ready
    /// </summary>
    IEnumerator InitializeAfterGameSystems()
    {
        // Wait for core systems to initialize
        yield return new WaitForSeconds(1f);
        
        // Get system references
        eventManager = EventManager.Instance;
        arenaManager = ArenaManager.Instance;
        
        // Setup game context
        SetupGameContext();
        
        // Load community events
        if (enableCommunityEvents)
        {
            LoadCommunityEvents();
        }
        
        // Subscribe to arena events
        SubscribeToArenaEvents();
    }
    
    /// <summary>
    /// Create necessary directories
    /// </summary>
    void CreateDirectories()
    {
        string basePath = Application.persistentDataPath;
        
        Directory.CreateDirectory(basePath + communityEventsPath);
        Directory.CreateDirectory(basePath + userEventsPath);
        
        // Create subdirectories
        Directory.CreateDirectory(basePath + communityEventsPath + "Verified/");
        Directory.CreateDirectory(basePath + communityEventsPath + "Unverified/");
        Directory.CreateDirectory(basePath + userEventsPath + "Exports/");
    }
    
    /// <summary>
    /// Load trusted authors list
    /// </summary>
    void LoadTrustedAuthors()
    {
        string filePath = Application.persistentDataPath + trustedAuthorsPath;
        
        if (File.Exists(filePath))
        {
            try
            {
                string json = File.ReadAllText(filePath);
                var authorsData = JsonUtility.FromJson<TrustedAuthorsData>(json);
                
                trustedAuthors = new HashSet<string>(authorsData.trustedAuthors);
                Debug.Log($"Loaded {trustedAuthors.Count} trusted authors");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load trusted authors: {e.Message}");
            }
        }
        else
        {
            // Create default trusted authors file
            CreateDefaultTrustedAuthors();
        }
    }
    
    /// <summary>
    /// Create default trusted authors file
    /// </summary>
    void CreateDefaultTrustedAuthors()
    {
        var defaultAuthors = new TrustedAuthorsData
        {
            trustedAuthors = new string[]
            {
                "AugmentCode", // Official developer
                "CinderOfDarknessTeam",
                "VerifiedModder1",
                "CommunityManager"
            }
        };
        
        string json = JsonUtility.ToJson(defaultAuthors, true);
        string filePath = Application.persistentDataPath + trustedAuthorsPath;
        
        try
        {
            File.WriteAllText(filePath, json);
            trustedAuthors = new HashSet<string>(defaultAuthors.trustedAuthors);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to create trusted authors file: {e.Message}");
        }
    }
    
    /// <summary>
    /// Setup game context for mods
    /// </summary>
    void SetupGameContext()
    {
        gameContext = new GameContext
        {
            EventManager = eventManager,
            MoralitySystem = FindObjectOfType<MoralitySystem>(),
            QuestSystem = FindObjectOfType<QuestSystem>(),
            WorldStateManager = FindObjectOfType<WorldStateManager>(),
            ArenaManager = arenaManager,
            LocalizationManager = FindObjectOfType<LocalizationManager>(),
            SaveSystemManager = FindObjectOfType<SaveSystemManager>()
        };
    }
    
    /// <summary>
    /// Load community events from files
    /// </summary>
    void LoadCommunityEvents()
    {
        // Load from community directory
        LoadEventsFromDirectory(Application.persistentDataPath + communityEventsPath);
        
        // Load from user directory
        LoadEventsFromDirectory(Application.persistentDataPath + userEventsPath);
        
        Debug.Log($"Loaded {communityEvents.Count} community events from {loadedModProviders.Count} mod providers");
    }
    
    /// <summary>
    /// Load events from specific directory
    /// </summary>
    void LoadEventsFromDirectory(string directoryPath)
    {
        if (!Directory.Exists(directoryPath)) return;
        
        // Load .eventmod files
        string[] eventModFiles = Directory.GetFiles(directoryPath, "*.eventmod", SearchOption.AllDirectories);
        
        foreach (string filePath in eventModFiles)
        {
            LoadEventModFile(filePath);
        }
        
        // Load .dll mod providers
        string[] dllFiles = Directory.GetFiles(directoryPath, "*.dll", SearchOption.AllDirectories);
        
        foreach (string filePath in dllFiles)
        {
            LoadModProviderDll(filePath);
        }
    }
    
    /// <summary>
    /// Load event mod file
    /// </summary>
    void LoadEventModFile(string filePath)
    {
        try
        {
            string json = File.ReadAllText(filePath);
            var communityEvent = JsonUtility.FromJson<CommunityWorldEvent>(json);
            
            // Validate event
            var validationResult = ValidateCommunityEvent(communityEvent);
            
            if (validationResult.IsValid)
            {
                communityEvents[communityEvent.eventId] = communityEvent;
                Debug.Log($"Loaded community event: {communityEvent.eventName}");
            }
            else
            {
                Debug.LogWarning($"Failed to validate community event {communityEvent.eventName}: {string.Join(", ", validationResult.Errors)}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load event mod file {filePath}: {e.Message}");
        }
    }
    
    /// <summary>
    /// Load mod provider DLL
    /// </summary>
    void LoadModProviderDll(string filePath)
    {
        try
        {
            // Load assembly
            Assembly assembly = Assembly.LoadFrom(filePath);
            
            // Find mod provider types
            var providerTypes = assembly.GetTypes().Where(t => typeof(IWorldEventModProvider).IsAssignableFrom(t) && !t.IsInterface);
            
            foreach (var providerType in providerTypes)
            {
                // Create instance
                var provider = (IWorldEventModProvider)System.Activator.CreateInstance(providerType);
                
                // Validate mod
                var validationResult = provider.ValidateMod();
                
                if (validationResult.IsValid && (provider.IsTrusted || enableUntrustedMods))
                {
                    // Initialize provider
                    provider.Initialize(gameContext);
                    
                    // Load events from provider
                    var events = provider.GetCustomEvents();
                    foreach (var communityEvent in events)
                    {
                        communityEvents[communityEvent.eventId] = communityEvent;
                    }
                    
                    // Load custom triggers
                    var triggers = provider.GetCustomTriggers();
                    foreach (var trigger in triggers)
                    {
                        customTriggers[trigger.TriggerId] = trigger;
                    }
                    
                    // Load custom effects
                    var effects = provider.GetCustomEffects();
                    foreach (var effect in effects)
                    {
                        customEffects[effect.EffectId] = effect;
                    }
                    
                    loadedModProviders.Add(provider);
                    Debug.Log($"Loaded mod provider: {provider.ModName} by {provider.ModAuthor}");
                }
                else
                {
                    Debug.LogWarning($"Failed to validate mod provider {provider.ModName}: {string.Join(", ", validationResult.Errors)}");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load mod provider DLL {filePath}: {e.Message}");
        }
    }
    
    /// <summary>
    /// Validate community event
    /// </summary>
    ModValidationResult ValidateCommunityEvent(CommunityWorldEvent communityEvent)
    {
        var result = new ModValidationResult();
        
        // Basic validation
        if (string.IsNullOrEmpty(communityEvent.eventId))
        {
            result.Errors.Add("Event ID is required");
        }
        
        if (string.IsNullOrEmpty(communityEvent.eventName))
        {
            result.Errors.Add("Event name is required");
        }
        
        if (string.IsNullOrEmpty(communityEvent.modAuthor))
        {
            result.Errors.Add("Mod author is required");
        }
        
        // Trust validation
        bool isTrusted = trustedAuthors.Contains(communityEvent.modAuthor);
        if (!isTrusted && !enableUntrustedMods)
        {
            result.Errors.Add("Untrusted author and untrusted mods are disabled");
        }
        
        // Duration validation
        if (communityEvent.customTrigger != null)
        {
            // Validate custom trigger
            if (string.IsNullOrEmpty(communityEvent.customTrigger.triggerId))
            {
                result.Warnings.Add("Custom trigger ID is missing");
            }
        }
        
        // Effects validation
        if (communityEvent.customEffects != null && communityEvent.customEffects.Length > maxCustomEffects)
        {
            result.Errors.Add($"Too many custom effects (max: {maxCustomEffects})");
        }
        
        // Arena integration validation
        if (communityEvent.arenaIntegration != null && communityEvent.arenaIntegration.enableArenaIntegration)
        {
            ValidateArenaIntegration(communityEvent.arenaIntegration, result);
        }
        
        // Set security level
        if (isTrusted)
        {
            result.SecurityLevel = ModValidationResult.SecurityLevel.Safe;
        }
        else if (result.Errors.Count == 0)
        {
            result.SecurityLevel = ModValidationResult.SecurityLevel.Caution;
        }
        else
        {
            result.SecurityLevel = ModValidationResult.SecurityLevel.Unsafe;
        }
        
        result.IsValid = result.Errors.Count == 0;
        return result;
    }
    
    /// <summary>
    /// Validate arena integration
    /// </summary>
    void ValidateArenaIntegration(ArenaIntegrationConfig arenaIntegration, ModValidationResult result)
    {
        if (arenaIntegration.triggerArenas != null)
        {
            foreach (string arenaId in arenaIntegration.triggerArenas)
            {
                // Check if arena exists (would integrate with ArenaManager)
                if (!ArenaExists(arenaId))
                {
                    result.Warnings.Add($"Referenced arena '{arenaId}' not found");
                }
            }
        }
        
        if (arenaIntegration.bossRushIntegration != null)
        {
            var bossRush = arenaIntegration.bossRushIntegration;
            if (bossRush.minimumScore < 0 || bossRush.minimumScore > 1000000)
            {
                result.Warnings.Add("Boss rush minimum score seems unrealistic");
            }
        }
    }
    
    /// <summary>
    /// Check if arena exists
    /// </summary>
    bool ArenaExists(string arenaId)
    {
        // This would integrate with the ArenaManager to check if arena exists
        return arenaManager != null && arenaManager.ArenaExists(arenaId);
    }
    
    /// <summary>
    /// Subscribe to arena events for integration
    /// </summary>
    void SubscribeToArenaEvents()
    {
        if (arenaManager != null)
        {
            // Subscribe to arena completion events
            // arenaManager.OnArenaCompleted += OnArenaCompleted;
            // arenaManager.OnBossRushCompleted += OnBossRushCompleted;
        }
    }
    
    /// <summary>
    /// Handle arena completion for event triggering
    /// </summary>
    void OnArenaCompleted(string arenaId, ArenaCompletionResult result)
    {
        // Check for community events that should trigger based on arena completion
        foreach (var kvp in communityEvents)
        {
            var communityEvent = kvp.Value;
            
            if (communityEvent.arenaIntegration != null && 
                communityEvent.arenaIntegration.enableArenaIntegration &&
                communityEvent.arenaIntegration.triggerArenas != null &&
                communityEvent.arenaIntegration.triggerArenas.Contains(arenaId))
            {
                // Check if conditions are met
                if (ShouldTriggerFromArena(communityEvent, result))
                {
                    TriggerCommunityEvent(communityEvent);
                }
            }
        }
    }
    
    /// <summary>
    /// Check if community event should trigger from arena result
    /// </summary>
    bool ShouldTriggerFromArena(CommunityWorldEvent communityEvent, ArenaCompletionResult result)
    {
        var arenaIntegration = communityEvent.arenaIntegration;
        
        // Check boss rush integration
        if (arenaIntegration.bossRushIntegration != null)
        {
            var bossRush = arenaIntegration.bossRushIntegration;
            
            if (result.IsBossRush)
            {
                if (bossRush.triggerOnCompletion && result.IsCompleted)
                {
                    return result.Score >= bossRush.minimumScore && result.CompletionTime <= bossRush.minimumTime;
                }
                
                if (bossRush.triggerOnFailure && !result.IsCompleted)
                {
                    return true;
                }
            }
        }
        
        // Check morality shift requirements
        if (arenaIntegration.moralityShift != null)
        {
            var moralitySystem = gameContext.MoralitySystem;
            if (moralitySystem != null)
            {
                var currentAlignment = moralitySystem.GetCurrentAlignment();
                return currentAlignment == arenaIntegration.moralityShift.targetAlignment;
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// Trigger community event
    /// </summary>
    void TriggerCommunityEvent(CommunityWorldEvent communityEvent)
    {
        if (activeCommunityEvents.Count >= maxConcurrentCommunityEvents)
        {
            Debug.LogWarning("Maximum concurrent community events reached");
            return;
        }
        
        // Convert to standard WorldEvent
        var worldEvent = ConvertToWorldEvent(communityEvent);
        
        if (worldEvent != null)
        {
            // Trigger through event manager
            eventManager.TriggerEvent(worldEvent);
            activeCommunityEvents.Add(communityEvent.eventId);
            
            // Notify mod provider
            var provider = loadedModProviders.FirstOrDefault(p => p.ModId == communityEvent.modId);
            if (provider != null)
            {
                var context = eventManager.GetCurrentContext();
                provider.OnEventTriggered(communityEvent.eventId, context);
            }
            
            Debug.Log($"Triggered community event: {communityEvent.eventName}");
        }
    }
    
    /// <summary>
    /// Convert community event to standard WorldEvent
    /// </summary>
    WorldEvent ConvertToWorldEvent(CommunityWorldEvent communityEvent)
    {
        // Create a new WorldEvent ScriptableObject
        var worldEvent = ScriptableObject.CreateInstance<WorldEvent>();
        
        worldEvent.eventId = communityEvent.eventId;
        worldEvent.eventName = communityEvent.eventName;
        worldEvent.eventDescription = communityEvent.eventDescription;
        worldEvent.eventType = communityEvent.eventType;
        worldEvent.severity = communityEvent.severity;
        
        // Set default values for required fields
        worldEvent.triggerType = WorldEvent.EventTriggerType.ChainedEvent;
        worldEvent.baseDurationDays = 7f;
        worldEvent.canRecur = false;
        worldEvent.allowsPlayerIntervention = true;
        
        // Apply custom configurations
        ApplyCustomConfigurations(worldEvent, communityEvent);
        
        return worldEvent;
    }
    
    /// <summary>
    /// Apply custom configurations to world event
    /// </summary>
    void ApplyCustomConfigurations(WorldEvent worldEvent, CommunityWorldEvent communityEvent)
    {
        // Apply custom trigger configuration
        if (communityEvent.customTrigger != null)
        {
            ApplyCustomTrigger(worldEvent, communityEvent.customTrigger);
        }
        
        // Apply custom effects
        if (communityEvent.customEffects != null)
        {
            ApplyCustomEffects(worldEvent, communityEvent.customEffects);
        }
        
        // Apply arena integration
        if (communityEvent.arenaIntegration != null)
        {
            ApplyArenaIntegration(worldEvent, communityEvent.arenaIntegration);
        }
        
        // Apply localization
        if (communityEvent.localizationData != null)
        {
            ApplyLocalization(communityEvent.localizationData);
        }
    }
    
    /// <summary>
    /// Apply custom trigger configuration
    /// </summary>
    void ApplyCustomTrigger(WorldEvent worldEvent, CustomTriggerConfig triggerConfig)
    {
        // Set trigger conditions based on custom configuration
        if (worldEvent.triggerConditions == null)
        {
            worldEvent.triggerConditions = new WorldEvent.EventTriggerConditions();
        }
        
        // Apply reputation requirements
        if (triggerConfig.reputationRequirements != null)
        {
            // This would be implemented based on the specific trigger system
        }
        
        // Apply alignment requirements
        if (triggerConfig.alignmentRequirement != null)
        {
            worldEvent.triggerConditions.requiresMoralityAlignment = true;
            worldEvent.triggerConditions.requiredAlignment = triggerConfig.alignmentRequirement.requiredAlignment;
            worldEvent.triggerConditions.minMoralityValue = triggerConfig.alignmentRequirement.minimumValue;
            worldEvent.triggerConditions.maxMoralityValue = triggerConfig.alignmentRequirement.maximumValue;
        }
        
        // Apply quest requirements
        if (triggerConfig.questRequirements != null)
        {
            var requiredQuests = triggerConfig.questRequirements
                .Where(q => q.mustBeCompleted)
                .Select(q => q.questId)
                .ToArray();
            
            worldEvent.triggerConditions.requiredCompletedQuests = requiredQuests;
        }
    }
    
    /// <summary>
    /// Apply custom effects configuration
    /// </summary>
    void ApplyCustomEffects(WorldEvent worldEvent, CustomEffectConfig[] effectConfigs)
    {
        // This would apply custom effects to the world event
        // Implementation would depend on the specific effect system
        foreach (var effectConfig in effectConfigs)
        {
            if (customEffects.ContainsKey(effectConfig.effectId))
            {
                var customEffect = customEffects[effectConfig.effectId];
                // Apply the custom effect
            }
        }
    }
    
    /// <summary>
    /// Apply arena integration configuration
    /// </summary>
    void ApplyArenaIntegration(WorldEvent worldEvent, ArenaIntegrationConfig arenaConfig)
    {
        // Apply regional impact
        if (arenaConfig.regionalImpact != null)
        {
            worldEvent.affectedRealms = arenaConfig.regionalImpact.affectedRegions;
        }
        
        // Apply morality shift
        if (arenaConfig.moralityShift != null)
        {
            var moralityShift = new WorldEvent.MoralityShift
            {
                alignment = arenaConfig.moralityShift.targetAlignment,
                shiftAmount = arenaConfig.moralityShift.shiftAmount,
                reason = arenaConfig.moralityShift.reason
            };
            
            worldEvent.moralityConsequences = new WorldEvent.MoralityShift[] { moralityShift };
        }
        
        // Apply faction outcomes
        if (arenaConfig.factionOutcomes != null)
        {
            var reputationChanges = arenaConfig.factionOutcomes.Select(f => new WorldEvent.ReputationChange
            {
                factionId = f.factionId,
                reputationDelta = f.reputationChange,
                reason = "Arena event outcome"
            }).ToArray();
            
            worldEvent.reputationChanges = reputationChanges;
        }
    }
    
    /// <summary>
    /// Apply localization data
    /// </summary>
    void ApplyLocalization(LocalizationData localizationData)
    {
        var localizationManager = gameContext.LocalizationManager;
        if (localizationManager == null) return;
        
        // Add localized strings to the localization manager
        foreach (var kvp in localizationData.localizedNames)
        {
            localizationManager.AddLocalizationEntry(kvp.Key, kvp.Value, kvp.Value);
        }
        
        foreach (var kvp in localizationData.localizedDescriptions)
        {
            localizationManager.AddLocalizationEntry(kvp.Key, kvp.Value, kvp.Value);
        }
        
        foreach (var kvp in localizationData.localizedDialogue)
        {
            localizationManager.AddLocalizationEntry(kvp.Key, kvp.Value, kvp.Value);
        }
    }
    
    /// <summary>
    /// Handle community event completion
    /// </summary>
    public void OnCommunityEventCompleted(string eventId, EventManager.EventState finalState)
    {
        activeCommunityEvents.Remove(eventId);
        
        // Notify mod provider
        if (communityEvents.ContainsKey(eventId))
        {
            var communityEvent = communityEvents[eventId];
            var provider = loadedModProviders.FirstOrDefault(p => p.ModId == communityEvent.modId);
            
            if (provider != null)
            {
                var result = new EventCompletionResult
                {
                    FinalState = finalState,
                    // Add other completion data
                };
                
                provider.OnEventCompleted(eventId, result);
            }
        }
    }
    
    /// <summary>
    /// Get all community events
    /// </summary>
    public List<CommunityWorldEvent> GetAllCommunityEvents()
    {
        return communityEvents.Values.ToList();
    }
    
    /// <summary>
    /// Get community events by author
    /// </summary>
    public List<CommunityWorldEvent> GetEventsByAuthor(string author)
    {
        return communityEvents.Values.Where(e => e.modAuthor == author).ToList();
    }
    
    /// <summary>
    /// Check if author is trusted
    /// </summary>
    public bool IsAuthorTrusted(string author)
    {
        return trustedAuthors.Contains(author);
    }
    
    /// <summary>
    /// Get loaded mod providers
    /// </summary>
    public List<IWorldEventModProvider> GetLoadedModProviders()
    {
        return loadedModProviders.ToList();
    }
    
    // Public API
    public static void TriggerCommunityEventStatic(string eventId)
    {
        if (Instance.communityEvents.ContainsKey(eventId))
        {
            Instance.TriggerCommunityEvent(Instance.communityEvents[eventId]);
        }
    }
    
    public static List<CommunityWorldEvent> GetCommunityEventsStatic()
    {
        return Instance.GetAllCommunityEvents();
    }
    
    public static bool IsAuthorTrustedStatic(string author)
    {
        return Instance.IsAuthorTrusted(author);
    }
}

/// <summary>
/// Trusted authors data structure
/// </summary>
[System.Serializable]
public class TrustedAuthorsData
{
    public string[] trustedAuthors;
}

/// <summary>
/// Arena completion result for event triggering
/// </summary>
public class ArenaCompletionResult
{
    public bool IsCompleted { get; set; }
    public bool IsBossRush { get; set; }
    public int Score { get; set; }
    public float CompletionTime { get; set; }
    public string PlayerId { get; set; }
    public Dictionary<string, object> CustomData { get; set; }
}
