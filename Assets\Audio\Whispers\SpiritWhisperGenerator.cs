using UnityEngine;
using UnityEditor;
using System.IO;

#if UNITY_EDITOR
/// <summary>
/// Generates spirit whisper audio clips for Cinder of Darkness
/// Creates eerie, spectral whisper sounds for the RegretAfterKillingSystem
/// </summary>
public class SpiritWhisperGenerator : EditorWindow
{
    [MenuItem("Cinder of Darkness/Generate Spirit Whispers")]
    public static void ShowWindow()
    {
        GetWindow<SpiritWhisperGenerator>("Spirit Whisper Generator");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Spirit Whisper Audio Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("Generates 10 eerie spirit whisper clips for RegretAfterKillingSystem", EditorStyles.helpBox);
        GUILayout.Space(10);
        
        if (GUILayout.Button("Generate All Spirit Whispers"))
        {
            GenerateAllSpiritWhispers();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.But<PERSON>("Assign to RegretAfterKillingSystem"))
        {
            AssignWhispersToSystem();
        }
    }
    
    void GenerateAllSpiritWhispers()
    {
        Debug.Log("Generating spirit whisper audio clips...");
        
        // Create directory if it doesn't exist
        string whisperDir = "Assets/Audio/Whispers";
        if (!Directory.Exists(whisperDir))
        {
            Directory.CreateDirectory(whisperDir);
        }
        
        // Generate 10 spirit whisper variations
        GenerateSpiritWhisper("spirit_whisper_01", 3.2f, 0.8f, 180f);
        GenerateSpiritWhisper("spirit_whisper_02", 4.1f, 0.6f, 220f);
        GenerateSpiritWhisper("spirit_whisper_03", 5.3f, 0.7f, 160f);
        GenerateSpiritWhisper("spirit_whisper_04", 3.8f, 0.9f, 200f);
        GenerateSpiritWhisper("spirit_whisper_05", 6.2f, 0.5f, 140f);
        GenerateSpiritWhisper("spirit_whisper_06", 4.7f, 0.8f, 190f);
        GenerateSpiritWhisper("spirit_whisper_07", 3.5f, 0.6f, 170f);
        GenerateSpiritWhisper("spirit_whisper_08", 5.8f, 0.7f, 210f);
        GenerateSpiritWhisper("spirit_whisper_09", 4.4f, 0.9f, 150f);
        GenerateSpiritWhisper("spirit_whisper_10", 6.7f, 0.5f, 230f);
        
        AssetDatabase.Refresh();
        Debug.Log("All spirit whisper clips generated successfully!");
    }
    
    void GenerateSpiritWhisper(string name, float duration, float intensity, float baseFreq)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * duration);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            float normalizedTime = time / duration;
            
            // Create ethereal whisper base
            float whisperBase = GenerateWhisperTone(time, baseFreq);
            
            // Add spectral harmonics
            float harmonics = GenerateSpectralHarmonics(time, baseFreq);
            
            // Add breath-like modulation
            float breathMod = GenerateBreathModulation(time, duration);
            
            // Add eerie reverb-like effect
            float reverb = GenerateEerieReverb(time, normalizedTime);
            
            // Combine all elements
            float sample = (whisperBase + harmonics * 0.3f) * breathMod * reverb * intensity;
            
            // Apply fade in/out envelope
            float envelope = CalculateEnvelope(normalizedTime);
            sample *= envelope;
            
            // Clamp to prevent clipping
            data[i] = Mathf.Clamp(sample, -1f, 1f);
        }
        
        clip.SetData(data, 0);
        
        // Save the clip
        string path = $"Assets/Audio/Whispers/{name}.asset";
        AssetDatabase.CreateAsset(clip, path);
        
        Debug.Log($"Generated spirit whisper: {name} ({duration:F1}s)");
    }
    
    float GenerateWhisperTone(float time, float baseFreq)
    {
        // Create a low-frequency whisper-like tone
        float primary = Mathf.Sin(time * baseFreq * 2f * Mathf.PI) * 0.4f;
        
        // Add subtle frequency modulation for organic feel
        float freqMod = 1f + 0.1f * Mathf.Sin(time * 3f);
        float modulated = Mathf.Sin(time * baseFreq * freqMod * 2f * Mathf.PI) * 0.3f;
        
        // Add noise component for breath texture
        float noise = (Mathf.PerlinNoise(time * 50f, 0f) - 0.5f) * 0.2f;
        
        return primary + modulated + noise;
    }
    
    float GenerateSpectralHarmonics(float time, float baseFreq)
    {
        // Add ethereal harmonics at different frequencies
        float harmonic1 = Mathf.Sin(time * baseFreq * 1.5f * 2f * Mathf.PI) * 0.15f;
        float harmonic2 = Mathf.Sin(time * baseFreq * 2.3f * 2f * Mathf.PI) * 0.1f;
        float harmonic3 = Mathf.Sin(time * baseFreq * 3.7f * 2f * Mathf.PI) * 0.05f;
        
        // Modulate harmonics with slow oscillation
        float modulation = 1f + 0.3f * Mathf.Sin(time * 0.7f);
        
        return (harmonic1 + harmonic2 + harmonic3) * modulation;
    }
    
    float GenerateBreathModulation(float time, float duration)
    {
        // Create breath-like amplitude modulation
        float breathRate = 2f / duration; // Breathing rate based on clip duration
        float breath = 0.7f + 0.3f * Mathf.Sin(time * breathRate * 2f * Mathf.PI);
        
        // Add subtle tremolo for ghostly effect
        float tremolo = 1f + 0.1f * Mathf.Sin(time * 8f);
        
        return breath * tremolo;
    }
    
    float GenerateEerieReverb(float time, float normalizedTime)
    {
        // Simulate reverb tail with multiple delayed echoes
        float reverb = 1f;
        
        // Add delayed echoes with decreasing amplitude
        for (int i = 1; i <= 3; i++)
        {
            float delay = 0.1f * i;
            float delayedTime = time - delay;
            
            if (delayedTime > 0)
            {
                float echo = Mathf.Exp(-delayedTime * 2f) * (0.3f / i);
                reverb += echo * Mathf.Sin(delayedTime * 100f * 2f * Mathf.PI);
            }
        }
        
        // Add spatial depth with low-pass filtering effect
        float depth = 1f - 0.2f * normalizedTime;
        
        return reverb * depth;
    }
    
    float CalculateEnvelope(float normalizedTime)
    {
        // Create smooth fade in/out envelope
        float fadeInTime = 0.1f;  // 10% of clip for fade in
        float fadeOutTime = 0.3f; // 30% of clip for fade out
        
        float envelope = 1f;
        
        // Fade in
        if (normalizedTime < fadeInTime)
        {
            envelope = normalizedTime / fadeInTime;
        }
        // Fade out
        else if (normalizedTime > (1f - fadeOutTime))
        {
            float fadeOutProgress = (normalizedTime - (1f - fadeOutTime)) / fadeOutTime;
            envelope = 1f - fadeOutProgress;
        }
        
        // Apply smooth curve
        envelope = Mathf.SmoothStep(0f, 1f, envelope);
        
        return envelope;
    }
    
    void AssignWhispersToSystem()
    {
        Debug.Log("Assigning spirit whispers to RegretAfterKillingSystem...");
        
        // Find all RegretAfterKillingSystem instances in the project
        string[] guids = AssetDatabase.FindAssets("t:MonoScript RegretAfterKillingSystem");
        
        if (guids.Length == 0)
        {
            Debug.LogWarning("RegretAfterKillingSystem script not found!");
            return;
        }
        
        // Load the whisper audio clips
        AudioClip[] whisperClips = new AudioClip[10];
        for (int i = 0; i < 10; i++)
        {
            string clipName = $"spirit_whisper_{(i + 1):D2}";
            string clipPath = $"Assets/Audio/Whispers/{clipName}.asset";
            whisperClips[i] = AssetDatabase.LoadAssetAtPath<AudioClip>(clipPath);
            
            if (whisperClips[i] == null)
            {
                Debug.LogWarning($"Could not load whisper clip: {clipName}");
            }
        }
        
        Debug.Log($"Loaded {whisperClips.Length} spirit whisper clips for assignment");
        
        // Create a ScriptableObject to hold the audio references
        CreateSpiritWhisperAsset(whisperClips);
    }
    
    void CreateSpiritWhisperAsset(AudioClip[] clips)
    {
        // Create a ScriptableObject asset to hold the whisper clips
        SpiritWhisperAsset asset = ScriptableObject.CreateInstance<SpiritWhisperAsset>();
        asset.spiritWhispers = clips;
        
        string assetPath = "Assets/Audio/Whispers/SpiritWhisperAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);
        AssetDatabase.SaveAssets();
        
        Debug.Log($"Created SpiritWhisperAsset at {assetPath}");
        Debug.Log("Spirit whispers are ready for assignment to RegretAfterKillingSystem!");
    }
}

/// <summary>
/// ScriptableObject to hold spirit whisper audio clips
/// </summary>
[CreateAssetMenu(fileName = "SpiritWhisperAsset", menuName = "Cinder of Darkness/Spirit Whisper Asset")]
public class SpiritWhisperAsset : ScriptableObject
{
    [Header("Spirit Whisper Audio Clips")]
    [Tooltip("Eerie spirit whisper sounds for regret and haunting moments")]
    public AudioClip[] spiritWhispers = new AudioClip[10];
    
    [Header("Usage Settings")]
    [Range(0f, 1f)]
    public float defaultVolume = 0.3f;
    
    [Range(0.5f, 2f)]
    public float pitchVariation = 0.2f;
    
    [Tooltip("Minimum time between whisper plays")]
    public float cooldownTime = 2f;
    
    /// <summary>
    /// Get a random spirit whisper clip
    /// </summary>
    public AudioClip GetRandomWhisper()
    {
        if (spiritWhispers == null || spiritWhispers.Length == 0)
            return null;
        
        int randomIndex = Random.Range(0, spiritWhispers.Length);
        return spiritWhispers[randomIndex];
    }
    
    /// <summary>
    /// Get a specific whisper by index
    /// </summary>
    public AudioClip GetWhisper(int index)
    {
        if (spiritWhispers == null || index < 0 || index >= spiritWhispers.Length)
            return null;
        
        return spiritWhispers[index];
    }
    
    /// <summary>
    /// Get random pitch variation for whisper playback
    /// </summary>
    public float GetRandomPitch()
    {
        return 1f + Random.Range(-pitchVariation, pitchVariation);
    }
}
#endif
