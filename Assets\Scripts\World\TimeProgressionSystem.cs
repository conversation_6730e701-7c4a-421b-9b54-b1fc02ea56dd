using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class TimeProgressionSystem : MonoBehaviour
{
    [Header("Time Flow")]
    public float currentGameTime = 0f; // In game days
    public float timeMultiplier = 1f;
    public Season currentSeason = Season.Spring;
    public int currentYear = 1;

    [Header("World Changes")]
    public List<NPCAgingData> agingNPCs = new List<NPCAgingData>();
    public List<TownEvolution> evolvingTowns = new List<TownEvolution>();
    public List<TimedEvent> scheduledEvents = new List<TimedEvent>();

    [Header("Narrative Integration")]
    public bool timeAffectsStory = true;
    public float storyProgressionRate = 1f;
    public Dictionary<string, float> questTimeWindows = new Dictionary<string, float>();

    private SmartAllySystem allySystem;
    private OrphanedChildCompanion childCompanion;
    private NewRacesSystem raceSystem;
    private GameManager gameManager;

    public enum Season
    {
        Spring,
        Summer,
        Autumn,
        Winter
    }

    [System.Serializable]
    public class NPCAgingData
    {
        public string npcName;
        public GameObject npcObject;
        public NPCLifeStage currentStage;
        public float ageInDays;
        public float agingRate = 1f;
        public bool hasAged = false;
        public string[] ageTransitionDialogues;

        public enum NPCLifeStage
        {
            Child,
            YoungAdult,
            Adult,
            MiddleAged,
            Elder,
            Deceased
        }
    }

    [System.Serializable]
    public class TownEvolution
    {
        public string townName;
        public GameObject townObject;
        public TownStage currentStage;
        public float developmentProgress = 0f;
        public float developmentRate = 1f;
        public string[] evolutionTriggers;
        public GameObject[] stageModels;

        public enum TownStage
        {
            Settlement,
            Village,
            Town,
            City,
            Metropolis,
            Ruins
        }
    }

    [System.Serializable]
    public class TimedEvent
    {
        public string eventName;
        public float triggerTime;
        public bool hasTriggered = false;
        public EventType type;
        public string eventDescription;
        public string[] consequences;

        public enum EventType
        {
            NPCDeath,
            TownChange,
            QuestExpiration,
            SeasonalEvent,
            PoliticalChange,
            NaturalDisaster,
            CulturalShift
        }
    }

    void Start()
    {
        allySystem = GetComponent<SmartAllySystem>();
        childCompanion = GetComponent<OrphanedChildCompanion>();
        raceSystem = GetComponent<NewRacesSystem>();
        gameManager = GameManager.Instance;

        InitializeTimeSystem();
    }

    // Performance optimization fields
    private float lastUpdateTime = 0f;
    private float lastNPCAgingUpdate = 0f;
    private float lastTownEvolutionUpdate = 0f;
    private float lastTimedEventCheck = 0f;
    private float lastQuestUpdate = 0f;

    // Performance constants
    private const float UPDATE_INTERVAL = 0.1f; // Update every 100ms
    private const float NPC_AGING_INTERVAL = 1f; // Check NPC aging every second
    private const float TOWN_EVOLUTION_INTERVAL = 2f; // Check town evolution every 2 seconds
    private const float TIMED_EVENT_INTERVAL = 5f; // Check timed events every 5 seconds
    private const float QUEST_UPDATE_INTERVAL = 10f; // Check quest availability every 10 seconds

    void Update()
    {
        float currentTime = Time.time;

        // Always progress time for smooth gameplay
        ProgressTime();

        // Update seasons at intervals
        if (currentTime - lastUpdateTime >= UPDATE_INTERVAL)
        {
            UpdateSeasons();
            lastUpdateTime = currentTime;
        }

        // Process NPC aging at intervals
        if (currentTime - lastNPCAgingUpdate >= NPC_AGING_INTERVAL)
        {
            ProcessNPCAging();
            lastNPCAgingUpdate = currentTime;
        }

        // Process town evolution at intervals
        if (currentTime - lastTownEvolutionUpdate >= TOWN_EVOLUTION_INTERVAL)
        {
            ProcessTownEvolution();
            lastTownEvolutionUpdate = currentTime;
        }

        // Check timed events at intervals
        if (currentTime - lastTimedEventCheck >= TIMED_EVENT_INTERVAL)
        {
            CheckTimedEvents();
            lastTimedEventCheck = currentTime;
        }

        // Update quest availability at intervals
        if (currentTime - lastQuestUpdate >= QUEST_UPDATE_INTERVAL)
        {
            UpdateQuestAvailability();
            lastQuestUpdate = currentTime;
        }
    }

    void InitializeTimeSystem()
    {
        // Initialize NPCs for aging
        InitializeAgingNPCs();

        // Initialize town evolution
        InitializeTownEvolution();

        // Schedule major events
        ScheduleTimedEvents();

        Debug.Log("Time Progression System initialized - the world will change as time passes");
    }

    void InitializeAgingNPCs()
    {
        // Find all NPCs in the world and set up aging data (cached for performance)
        NPCController[] allNPCs = FindObjectsOfType<NPCController>();

        // Pre-allocate list capacity for performance
        agingNPCs = new List<NPCAgingData>(allNPCs.Length);

        foreach (NPCController npc in allNPCs)
        {
            if (npc == null) continue; // Skip null NPCs

            NPCAgingData agingData = new NPCAgingData
            {
                npcName = npc.npcName,
                npcObject = npc.gameObject,
                currentStage = DetermineInitialLifeStage(npc),
                ageInDays = Random.Range(0f, 365f * 30f), // Random age up to 30 years
                agingRate = Random.Range(0.8f, 1.2f), // Slight variation in aging
                ageTransitionDialogues = GenerateAgeTransitionDialogues(npc.npcName)
            };

            agingNPCs.Add(agingData);
        }

        Debug.Log($"Initialized aging for {agingNPCs.Count} NPCs");
    }

    NPCAgingData.NPCLifeStage DetermineInitialLifeStage(NPCController npc)
    {
        // Determine based on NPC type or random assignment
        switch (npc.npcType)
        {
            case NPCController.NPCType.Child:
                return NPCAgingData.NPCLifeStage.Child;
            case NPCController.NPCType.Elder:
                return NPCAgingData.NPCLifeStage.Elder;
            default:
                // Random adult stage
                return (NPCAgingData.NPCLifeStage)Random.Range(1, 4);
        }
    }

    string[] GenerateAgeTransitionDialogues(string npcName)
    {
        return new string[]
        {
            $"{npcName}: \"Time has been kind to me, though I feel its weight.\"",
            $"{npcName}: \"The years pass so quickly... I remember when you first arrived.\"",
            $"{npcName}: \"Age brings wisdom, but also weariness.\"",
            $"{npcName}: \"I may not have much time left, but I'm grateful for what I've seen.\""
        };
    }

    void InitializeTownEvolution()
    {
        // Find towns and set up evolution tracking (cached for performance)
        GameObject[] towns = GameObject.FindGameObjectsWithTag("Town");

        // Pre-allocate list capacity for performance
        evolvingTowns = new List<TownEvolution>(towns.Length);

        foreach (GameObject town in towns)
        {
            if (town == null) continue; // Skip null towns

            TownEvolution evolution = new TownEvolution
            {
                townName = town.name,
                townObject = town,
                currentStage = TownEvolution.TownStage.Village,
                developmentProgress = Random.Range(0f, 50f),
                developmentRate = Random.Range(0.5f, 2f),
                evolutionTriggers = new string[] { "PlayerActions", "TimePassage", "TradeRoutes" }
            };

            evolvingTowns.Add(evolution);
        }

        Debug.Log($"Initialized evolution tracking for {evolvingTowns.Count} towns");
    }

    void ScheduleTimedEvents()
    {
        // Schedule various events throughout the game timeline
        scheduledEvents.Add(new TimedEvent
        {
            eventName = "The Great Harvest",
            triggerTime = 90f, // 90 days
            type = TimedEvent.EventType.SeasonalEvent,
            eventDescription = "A bountiful harvest brings prosperity to the land",
            consequences = new string[] { "Increased trade", "Lower food prices", "Festive atmosphere" }
        });

        scheduledEvents.Add(new TimedEvent
        {
            eventName = "Political Upheaval",
            triggerTime = 180f, // 180 days
            type = TimedEvent.EventType.PoliticalChange,
            eventDescription = "The old order crumbles, new powers rise",
            consequences = new string[] { "Changed faction relations", "New quest opportunities", "Altered dialogue" }
        });

        scheduledEvents.Add(new TimedEvent
        {
            eventName = "The Long Winter",
            triggerTime = 270f, // 270 days
            type = TimedEvent.EventType.NaturalDisaster,
            eventDescription = "An unusually harsh winter tests everyone's resolve",
            consequences = new string[] { "Increased difficulty", "Resource scarcity", "NPC deaths" }
        });
    }

    void ProgressTime()
    {
        float timeIncrement = Time.deltaTime * timeMultiplier;
        currentGameTime += timeIncrement;

        // Update game manager with time progression
        if (gameManager != null)
        {
            gameManager.SetGameTime(currentGameTime);
        }
    }

    void UpdateSeasons()
    {
        // Calculate season based on game time
        float dayOfYear = currentGameTime % 365f;
        Season newSeason;

        if (dayOfYear < 91f)
            newSeason = Season.Spring;
        else if (dayOfYear < 182f)
            newSeason = Season.Summer;
        else if (dayOfYear < 273f)
            newSeason = Season.Autumn;
        else
            newSeason = Season.Winter;

        if (newSeason != currentSeason)
        {
            OnSeasonChanged(currentSeason, newSeason);
            currentSeason = newSeason;
        }

        // Update year
        int newYear = Mathf.FloorToInt(currentGameTime / 365f) + 1;
        if (newYear != currentYear)
        {
            currentYear = newYear;
            OnYearChanged(newYear);
        }
    }

    void OnSeasonChanged(Season oldSeason, Season newSeason)
    {
        ShowTimeMessage($"The season changes from {oldSeason} to {newSeason}");

        // Apply seasonal effects
        ApplySeasonalEffects(newSeason);

        // Update world visuals
        UpdateSeasonalVisuals(newSeason);
    }

    void ApplySeasonalEffects(Season season)
    {
        switch (season)
        {
            case Season.Spring:
                // Renewal, growth, new opportunities
                ShowTimeMessage("Spring brings new life and fresh opportunities");
                break;
            case Season.Summer:
                // Abundance, activity, travel
                ShowTimeMessage("Summer's warmth encourages travel and trade");
                break;
            case Season.Autumn:
                // Harvest, preparation, reflection
                ShowTimeMessage("Autumn calls for harvest and preparation");
                break;
            case Season.Winter:
                // Hardship, scarcity, introspection
                ShowTimeMessage("Winter's cold tests resolve and brings introspection");
                break;
        }
    }

    void UpdateSeasonalVisuals(Season season)
    {
        // Update lighting and environmental effects
        switch (season)
        {
            case Season.Spring:
                RenderSettings.ambientLight = new Color(0.8f, 0.9f, 0.7f);
                break;
            case Season.Summer:
                RenderSettings.ambientLight = new Color(1f, 0.95f, 0.8f);
                break;
            case Season.Autumn:
                RenderSettings.ambientLight = new Color(0.9f, 0.7f, 0.5f);
                break;
            case Season.Winter:
                RenderSettings.ambientLight = new Color(0.7f, 0.8f, 0.9f);
                break;
        }
    }

    void OnYearChanged(int newYear)
    {
        ShowTimeMessage($"A new year begins: Year {newYear}");

        // Major world changes every year
        ProcessAnnualChanges();
    }

    void ProcessAnnualChanges()
    {
        // Significant changes that happen yearly
        ShowTimeMessage("The passage of time brings change to the world...");

        // Some NPCs may die of old age
        ProcessNaturalDeaths();

        // Towns may evolve significantly
        ProcessMajorTownChanges();

        // Political and cultural shifts
        ProcessCulturalShifts();
    }

    void ProcessNPCAging()
    {
        foreach (NPCAgingData npcData in agingNPCs)
        {
            if (npcData.npcObject == null) continue;

            // Age the NPC
            npcData.ageInDays += Time.deltaTime * timeMultiplier * npcData.agingRate;

            // Check for life stage transitions
            NPCAgingData.NPCLifeStage newStage = CalculateLifeStage(npcData.ageInDays);

            if (newStage != npcData.currentStage)
            {
                ProcessLifeStageTransition(npcData, newStage);
            }
        }
    }

    NPCAgingData.NPCLifeStage CalculateLifeStage(float ageInDays)
    {
        float ageInYears = ageInDays / 365f;

        if (ageInYears < 16f)
            return NPCAgingData.NPCLifeStage.Child;
        else if (ageInYears < 25f)
            return NPCAgingData.NPCLifeStage.YoungAdult;
        else if (ageInYears < 40f)
            return NPCAgingData.NPCLifeStage.Adult;
        else if (ageInYears < 60f)
            return NPCAgingData.NPCLifeStage.MiddleAged;
        else if (ageInYears < 80f)
            return NPCAgingData.NPCLifeStage.Elder;
        else
            return NPCAgingData.NPCLifeStage.Deceased;
    }

    void ProcessLifeStageTransition(NPCAgingData npcData, NPCAgingData.NPCLifeStage newStage)
    {
        NPCAgingData.NPCLifeStage oldStage = npcData.currentStage;
        npcData.currentStage = newStage;

        ShowTimeMessage($"{npcData.npcName} has aged from {oldStage} to {newStage}");

        // Apply visual changes
        UpdateNPCAppearance(npcData, newStage);

        // Update dialogue
        UpdateNPCDialogue(npcData, newStage);

        // Handle death
        if (newStage == NPCAgingData.NPCLifeStage.Deceased)
        {
            ProcessNPCDeath(npcData);
        }
    }

    void UpdateNPCAppearance(NPCAgingData npcData, NPCAgingData.NPCLifeStage stage)
    {
        NPCController npcController = npcData.npcObject.GetComponent<NPCController>();
        if (npcController != null)
        {
            npcController.UpdateAppearanceForAge(stage);
        }
    }

    void UpdateNPCDialogue(NPCAgingData npcData, NPCAgingData.NPCLifeStage stage)
    {
        // Add age-appropriate dialogue
        if (npcData.ageTransitionDialogues.Length > 0)
        {
            string dialogue = npcData.ageTransitionDialogues[Random.Range(0, npcData.ageTransitionDialogues.Length)];
            ShowTimeMessage(dialogue);
        }
    }

    void ProcessNPCDeath(NPCAgingData npcData)
    {
        ShowTimeMessage($"{npcData.npcName} has passed away from old age. Their memory lives on.");

        // Create memorial or grave
        CreateMemorial(npcData);

        // Remove from world but keep in memory
        if (npcData.npcObject != null)
        {
            npcData.npcObject.SetActive(false);
        }

        // Affect other NPCs emotionally
        ProcessCommunityGrief(npcData.npcName);
    }

    void CreateMemorial(NPCAgingData npcData)
    {
        // Create a simple memorial marker
        GameObject memorial = GameObject.CreatePrimitive(PrimitiveType.Cube);
        memorial.transform.position = npcData.npcObject.transform.position;
        memorial.transform.localScale = new Vector3(0.5f, 1f, 0.5f);
        memorial.name = $"Memorial_{npcData.npcName}";

        // Add memorial component
        Memorial memorialComponent = memorial.AddComponent<Memorial>();
        memorialComponent.Initialize(npcData.npcName, currentGameTime);
    }

    void ProcessCommunityGrief(string deceasedName)
    {
        // Other NPCs react to the death
        foreach (NPCAgingData npc in agingNPCs)
        {
            if (npc.npcObject != null && npc.npcObject.activeInHierarchy)
            {
                NPCController controller = npc.npcObject.GetComponent<NPCController>();
                if (controller != null)
                {
                    controller.ReactToNPCDeath(deceasedName);
                }
            }
        }
    }

    void ProcessTownEvolution()
    {
        foreach (TownEvolution town in evolvingTowns)
        {
            if (town.townObject == null) continue;

            // Progress development
            town.developmentProgress += Time.deltaTime * timeMultiplier * town.developmentRate;

            // Check for stage evolution
            TownEvolution.TownStage newStage = CalculateTownStage(town.developmentProgress);

            if (newStage != town.currentStage)
            {
                ProcessTownStageTransition(town, newStage);
            }
        }
    }

    TownEvolution.TownStage CalculateTownStage(float progress)
    {
        if (progress < 100f)
            return TownEvolution.TownStage.Settlement;
        else if (progress < 300f)
            return TownEvolution.TownStage.Village;
        else if (progress < 600f)
            return TownEvolution.TownStage.Town;
        else if (progress < 1000f)
            return TownEvolution.TownStage.City;
        else
            return TownEvolution.TownStage.Metropolis;
    }

    void ProcessTownStageTransition(TownEvolution town, TownEvolution.TownStage newStage)
    {
        TownEvolution.TownStage oldStage = town.currentStage;
        town.currentStage = newStage;

        ShowTimeMessage($"{town.townName} has evolved from {oldStage} to {newStage}");

        // Update town visuals
        UpdateTownAppearance(town, newStage);

        // Unlock new services and NPCs
        UnlockTownFeatures(town, newStage);
    }

    void UpdateTownAppearance(TownEvolution town, TownEvolution.TownStage stage)
    {
        // Switch to appropriate town model
        if (town.stageModels != null && town.stageModels.Length > (int)stage)
        {
            // Deactivate old model
            town.townObject.SetActive(false);

            // Activate new model
            if (town.stageModels[(int)stage] != null)
            {
                town.stageModels[(int)stage].SetActive(true);
                town.townObject = town.stageModels[(int)stage];
            }
        }
    }

    void UnlockTownFeatures(TownEvolution town, TownEvolution.TownStage stage)
    {
        switch (stage)
        {
            case TownEvolution.TownStage.Town:
                ShowTimeMessage($"{town.townName} now has a marketplace and inn");
                break;
            case TownEvolution.TownStage.City:
                ShowTimeMessage($"{town.townName} now has a guild hall and library");
                break;
            case TownEvolution.TownStage.Metropolis:
                ShowTimeMessage($"{town.townName} has become a great metropolis with advanced services");
                break;
        }
    }

    void CheckTimedEvents()
    {
        foreach (TimedEvent timedEvent in scheduledEvents)
        {
            if (!timedEvent.hasTriggered && currentGameTime >= timedEvent.triggerTime)
            {
                TriggerTimedEvent(timedEvent);
            }
        }
    }

    void TriggerTimedEvent(TimedEvent timedEvent)
    {
        timedEvent.hasTriggered = true;

        ShowTimeMessage($"WORLD EVENT: {timedEvent.eventName}");
        ShowTimeMessage(timedEvent.eventDescription);

        // Apply consequences
        foreach (string consequence in timedEvent.consequences)
        {
            ApplyEventConsequence(consequence);
        }

        Debug.Log($"Timed event triggered: {timedEvent.eventName}");
    }

    void ApplyEventConsequence(string consequence)
    {
        // Apply the consequence to the world
        ShowTimeMessage($"Consequence: {consequence}");

        // This would integrate with other systems to apply actual changes
    }

    void UpdateQuestAvailability()
    {
        // Some quests may expire or become available based on time
        foreach (var questWindow in questTimeWindows)
        {
            if (currentGameTime > questWindow.Value)
            {
                // Quest has expired
                gameManager?.ExpireQuest(questWindow.Key);
            }
        }
    }

    void ProcessNaturalDeaths()
    {
        // Process deaths due to old age or events
        foreach (NPCAgingData npc in agingNPCs)
        {
            if (npc.currentStage == NPCAgingData.NPCLifeStage.Elder && Random.Range(0f, 1f) < 0.1f)
            {
                ProcessNPCDeath(npc);
            }
        }
    }

    void ProcessMajorTownChanges()
    {
        // Major changes to towns each year
        foreach (TownEvolution town in evolvingTowns)
        {
            town.developmentProgress += Random.Range(50f, 150f); // Yearly boost
        }
    }

    void ProcessCulturalShifts()
    {
        // Cultural and political changes over time
        ShowTimeMessage("Cultural shifts reshape the political landscape...");

        // This would affect race relations, faction standings, etc.
        if (raceSystem != null)
        {
            // Modify race relationships over time
        }
    }

    void ShowTimeMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }

        Debug.Log($"Time System: {message}");
    }

    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    // Getters
    public float GetCurrentGameTime() => currentGameTime;
    public Season GetCurrentSeason() => currentSeason;
    public int GetCurrentYear() => currentYear;
    public List<NPCAgingData> GetAgingNPCs() => agingNPCs;
    public List<TownEvolution> GetEvolvingTowns() => evolvingTowns;
}

public class Memorial : MonoBehaviour
{
    public string deceasedName;
    public float deathTime;

    public void Initialize(string name, float time)
    {
        deceasedName = name;
        deathTime = time;
    }

    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"In memory of {deceasedName} - May they find peace in the ash");
            }
        }
    }
}
