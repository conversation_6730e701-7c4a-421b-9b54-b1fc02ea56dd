using UnityEngine;
using UnityEngine.AI;

public class SceneSetup : MonoBehaviour
{
    [Header("Scene Generation")]
    public bool generateOnStart = true;
    public GameObject playerPrefab;
    public GameObject[] npcPrefabs;
    public GameObject[] enemyPrefabs;
    public GameObject[] environmentPrefabs;
    
    [Header("World Settings")]
    public Vector3 worldSize = new Vector3(100, 10, 100);
    public int numberOfNPCs = 2;
    public int numberOfEnemies = 5;
    public int numberOfEnvironmentObjects = 20;
    
    [Header("Spawn Areas")]
    public Transform playerSpawnPoint;
    public Transform[] npcSpawnPoints;
    public Transform[] enemySpawnPoints;
    
    void Start()
    {
        if (generateOnStart)
        {
            GenerateScene();
        }
    }
    
    public void GenerateScene()
    {
        Debug.Log("Generating MOMO prototype scene...");
        
        CreateTerrain();
        SpawnPlayer();
        SpawnNPCs();
        SpawnEnemies();
        SpawnEnvironment();
        SetupLighting();
        SetupAudio();
        
        Debug.Log("Scene generation complete!");
    }
    
    void CreateTerrain()
    {
        // Create basic ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.localScale = new Vector3(worldSize.x / 10f, 1f, worldSize.z / 10f);
        ground.transform.position = Vector3.zero;
        
        // Add ground material
        Renderer groundRenderer = ground.GetComponent<Renderer>();
        Material groundMaterial = new Material(Shader.Find("Standard"));
        groundMaterial.color = new Color(0.3f, 0.5f, 0.2f); // Dark green
        groundRenderer.material = groundMaterial;
        
        // Add NavMesh surface
        NavMeshSurface navMeshSurface = ground.AddComponent<NavMeshSurface>();
        navMeshSurface.BuildNavMesh();
        
        // Create some hills and obstacles
        CreateHills();
        CreateWalls();
    }
    
    void CreateHills()
    {
        for (int i = 0; i < 5; i++)
        {
            GameObject hill = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            hill.name = $"Hill_{i}";
            hill.transform.position = new Vector3(
                Random.Range(-worldSize.x / 2, worldSize.x / 2),
                Random.Range(1f, 3f),
                Random.Range(-worldSize.z / 2, worldSize.z / 2)
            );
            hill.transform.localScale = new Vector3(
                Random.Range(5f, 15f),
                Random.Range(2f, 5f),
                Random.Range(5f, 15f)
            );
            
            // Hill material
            Renderer hillRenderer = hill.GetComponent<Renderer>();
            Material hillMaterial = new Material(Shader.Find("Standard"));
            hillMaterial.color = new Color(0.4f, 0.3f, 0.2f); // Brown
            hillRenderer.material = hillMaterial;
        }
    }
    
    void CreateWalls()
    {
        // Create boundary walls
        for (int i = 0; i < 4; i++)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = $"BoundaryWall_{i}";
            
            switch (i)
            {
                case 0: // North wall
                    wall.transform.position = new Vector3(0, 2.5f, worldSize.z / 2);
                    wall.transform.localScale = new Vector3(worldSize.x, 5f, 1f);
                    break;
                case 1: // South wall
                    wall.transform.position = new Vector3(0, 2.5f, -worldSize.z / 2);
                    wall.transform.localScale = new Vector3(worldSize.x, 5f, 1f);
                    break;
                case 2: // East wall
                    wall.transform.position = new Vector3(worldSize.x / 2, 2.5f, 0);
                    wall.transform.localScale = new Vector3(1f, 5f, worldSize.z);
                    break;
                case 3: // West wall
                    wall.transform.position = new Vector3(-worldSize.x / 2, 2.5f, 0);
                    wall.transform.localScale = new Vector3(1f, 5f, worldSize.z);
                    break;
            }
            
            // Wall material
            Renderer wallRenderer = wall.GetComponent<Renderer>();
            Material wallMaterial = new Material(Shader.Find("Standard"));
            wallMaterial.color = new Color(0.5f, 0.5f, 0.5f); // Gray
            wallRenderer.material = wallMaterial;
        }
    }
    
    void SpawnPlayer()
    {
        Vector3 spawnPosition = playerSpawnPoint != null ? playerSpawnPoint.position : Vector3.up * 2f;
        
        if (playerPrefab != null)
        {
            GameObject player = Instantiate(playerPrefab, spawnPosition, Quaternion.identity);
            player.name = "Player";
        }
        else
        {
            // Create basic player if no prefab
            CreateBasicPlayer(spawnPosition);
        }
    }
    
    void CreateBasicPlayer(Vector3 position)
    {
        GameObject player = new GameObject("Player");
        player.transform.position = position;
        player.tag = "Player";
        
        // Add basic components
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        
        // Add camera
        GameObject cameraHolder = new GameObject("CameraHolder");
        cameraHolder.transform.SetParent(player.transform);
        cameraHolder.transform.localPosition = Vector3.up * 1.8f;
        
        Camera playerCamera = cameraHolder.AddComponent<Camera>();
        playerCamera.tag = "MainCamera";
        
        // Add audio listener
        cameraHolder.AddComponent<AudioListener>();
        
        // Add player scripts
        player.AddComponent<PlayerController>();
        player.AddComponent<PlayerStats>();
        player.AddComponent<PlayerCombat>();
        
        // Set camera reference
        PlayerController playerController = player.GetComponent<PlayerController>();
        playerController.playerCamera = playerCamera;
        playerController.cameraHolder = cameraHolder.transform;
    }
    
    void SpawnNPCs()
    {
        for (int i = 0; i < numberOfNPCs; i++)
        {
            Vector3 spawnPosition;
            
            if (npcSpawnPoints != null && i < npcSpawnPoints.Length)
            {
                spawnPosition = npcSpawnPoints[i].position;
            }
            else
            {
                spawnPosition = GetRandomSpawnPosition();
            }
            
            GameObject npc = CreateBasicNPC(spawnPosition, i);
            SetupNPCDialogue(npc, i);
        }
    }
    
    GameObject CreateBasicNPC(Vector3 position, int index)
    {
        GameObject npc = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        npc.name = $"NPC_{index}";
        npc.transform.position = position;
        npc.tag = "NPC";
        
        // Add NPC components
        npc.AddComponent<NPCController>();
        
        // Set NPC color based on type
        Renderer npcRenderer = npc.GetComponent<Renderer>();
        Material npcMaterial = new Material(Shader.Find("Standard"));
        
        if (index == 0)
        {
            npcMaterial.color = Color.blue; // Elder
            NPCController npcController = npc.GetComponent<NPCController>();
            npcController.npcName = "Elder Theron";
            npcController.npcType = NPCController.NPCType.Sage;
        }
        else
        {
            npcMaterial.color = Color.yellow; // Merchant
            NPCController npcController = npc.GetComponent<NPCController>();
            npcController.npcName = "Gareth the Trader";
            npcController.npcType = NPCController.NPCType.Merchant;
        }
        
        npcRenderer.material = npcMaterial;
        
        return npc;
    }
    
    void SetupNPCDialogue(GameObject npc, int index)
    {
        NPCController npcController = npc.GetComponent<NPCController>();
        
        if (index == 0)
        {
            // Elder dialogue
            npcController.initialDialogue = DialogueData.CreateVillagerDialogue();
        }
        else
        {
            // Merchant dialogue
            npcController.initialDialogue = DialogueData.CreateMerchantDialogue();
        }
    }
    
    void SpawnEnemies()
    {
        for (int i = 0; i < numberOfEnemies; i++)
        {
            Vector3 spawnPosition;
            
            if (enemySpawnPoints != null && i < enemySpawnPoints.Length)
            {
                spawnPosition = enemySpawnPoints[i].position;
            }
            else
            {
                spawnPosition = GetRandomSpawnPosition();
            }
            
            CreateBasicEnemy(spawnPosition, i);
        }
    }
    
    void CreateBasicEnemy(Vector3 position, int index)
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Cube);
        enemy.name = $"Enemy_{index}";
        enemy.transform.position = position;
        enemy.tag = "Enemy";
        
        // Add enemy components
        enemy.AddComponent<NavMeshAgent>();
        enemy.AddComponent<EnemyHealth>();
        enemy.AddComponent<EnemyAI>();
        
        // Set enemy color
        Renderer enemyRenderer = enemy.GetComponent<Renderer>();
        Material enemyMaterial = new Material(Shader.Find("Standard"));
        enemyMaterial.color = Color.red;
        enemyRenderer.material = enemyMaterial;
        
        // Configure enemy type
        EnemyAI enemyAI = enemy.GetComponent<EnemyAI>();
        enemyAI.enemyType = (EnemyAI.EnemyType)(index % System.Enum.GetValues(typeof(EnemyAI.EnemyType)).Length);
    }
    
    void SpawnEnvironment()
    {
        // Create some trees and rocks
        for (int i = 0; i < numberOfEnvironmentObjects; i++)
        {
            Vector3 spawnPosition = GetRandomSpawnPosition();
            
            GameObject envObject;
            if (i % 2 == 0)
            {
                // Tree
                envObject = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                envObject.name = $"Tree_{i}";
                envObject.transform.localScale = new Vector3(1f, 3f, 1f);
                envObject.GetComponent<Renderer>().material.color = new Color(0.4f, 0.2f, 0.1f); // Brown
            }
            else
            {
                // Rock
                envObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                envObject.name = $"Rock_{i}";
                envObject.transform.localScale = Vector3.one * Random.Range(0.5f, 2f);
                envObject.GetComponent<Renderer>().material.color = Color.gray;
            }
            
            envObject.transform.position = spawnPosition;
            envObject.tag = "Environment";
        }
    }
    
    Vector3 GetRandomSpawnPosition()
    {
        return new Vector3(
            Random.Range(-worldSize.x / 3, worldSize.x / 3),
            1f,
            Random.Range(-worldSize.z / 3, worldSize.z / 3)
        );
    }
    
    void SetupLighting()
    {
        // Create directional light (sun)
        GameObject sunLight = new GameObject("Sun Light");
        Light sun = sunLight.AddComponent<Light>();
        sun.type = LightType.Directional;
        sun.color = new Color(1f, 0.9f, 0.7f); // Warm sunlight
        sun.intensity = 1.2f;
        sunLight.transform.rotation = Quaternion.Euler(45f, 30f, 0f);
        
        // Set ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f); // Blue sky
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f); // Gray horizon
        RenderSettings.ambientGroundColor = new Color(0.2f, 0.3f, 0.1f); // Dark green ground
    }
    
    void SetupAudio()
    {
        // Create ambient audio source
        GameObject audioManager = new GameObject("Audio Manager");
        AudioSource ambientAudio = audioManager.AddComponent<AudioSource>();
        ambientAudio.loop = true;
        ambientAudio.volume = 0.3f;
        ambientAudio.playOnAwake = true;
        
        // Note: In a full implementation, you would assign ambient sound clips here
    }
}
