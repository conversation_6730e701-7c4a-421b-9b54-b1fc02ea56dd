using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class ContemplativeContentSystem : MonoBehaviour
{
    [Header("Contemplative Locations")]
    public List<ContemplativeLocation> peacefulSites = new List<ContemplativeLocation>();
    public float interactionRadius = 3f;
    public bool showContemplativePrompts = true;
    
    [Header("Reflection Mechanics")]
    public float contemplationDuration = 30f;
    public float mentalRestoration = 25f;
    public float wisdomGain = 5f;
    
    [Header("Audio Elements")]
    public AudioClip[] ancientMelodies;
    public AudioClip[] natureSounds;
    public AudioClip[] monkWisdom;
    public AudioClip[] childQuestions;
    
    private PsychologicalSystem psycheSystem;
    private DynamicMusicalSystem musicalSystem;
    private TimeProgressionSystem timeSystem;
    private ContemplativeLocation currentLocation;
    private bool isContemplating = false;
    
    [System.Serializable]
    public class ContemplativeLocation
    {
        [Header("Location Identity")]
        public string locationName;
        public GameObject locationObject;
        public ContemplationType type;
        public string atmosphericDescription;
        
        [Header("Interaction")]
        public string interactionPrompt;
        public string[] contemplativeTexts;
        public float restorationMultiplier = 1f;
        
        [Header("Environmental Elements")]
        public AudioClip ambientSound;
        public ParticleSystem atmosphericEffect;
        public Light environmentalLighting;
        public Color lightingColor;
        
        [Header("Philosophical Content")]
        public PhilosophicalTheme theme;
        public string[] reflectiveQuestions;
        public string[] wisdomFragments;
        public float philosophicalDepth;
        
        [Header("Time-Based Changes")]
        public bool changesWithTime = true;
        public SeasonalVariation[] seasonalVariations;
        public TimeOfDayVariation[] timeVariations;
        
        public enum ContemplationType
        {
            CliffsidePerch,    // Watching sunrise/sunset
            RiversideRest,     // Humming ancient melodies
            ForgottenTemple,   // Silent monks and riddles
            ChildrenGathering, // Innocent philosophical questions
            AncientGrove,      // Natural wisdom
            MemorialSite,      // Honoring the dead
            StargazingPoint,   // Cosmic contemplation
            MeditationCircle   // Inner reflection
        }
        
        public enum PhilosophicalTheme
        {
            TimeAndMortality,
            NatureOfExistence,
            MeaningOfSuffering,
            PowerOfChoice,
            ConnectionToOthers,
            InnerPeace,
            CycleOfLife,
            WisdomThroughAge
        }
    }
    
    [System.Serializable]
    public class SeasonalVariation
    {
        public TimeProgressionSystem.Season season;
        public string seasonalDescription;
        public AudioClip seasonalAmbient;
        public Color seasonalLighting;
        public string[] seasonalReflections;
    }
    
    [System.Serializable]
    public class TimeOfDayVariation
    {
        public TimeOfDay timeOfDay;
        public string timeDescription;
        public Color lightingColor;
        public float lightIntensity;
        public string[] timeSpecificReflections;
        
        public enum TimeOfDay
        {
            Dawn,
            Morning,
            Midday,
            Afternoon,
            Dusk,
            Night,
            Midnight
        }
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        musicalSystem = GetComponent<DynamicMusicalSystem>();
        timeSystem = GetComponent<TimeProgressionSystem>();
        
        InitializeContemplativeLocations();
    }
    
    void Update()
    {
        CheckForContemplativeInteractions();
        UpdateLocationAtmospheres();
    }
    
    void InitializeContemplativeLocations()
    {
        // Cliffside Perch for sunrise/sunset watching
        ContemplativeLocation cliffsidePerch = new ContemplativeLocation
        {
            locationName = "The Watcher's Perch",
            type = ContemplativeLocation.ContemplationType.CliffsidePerch,
            atmosphericDescription = "A weathered stone outcropping overlooking the endless expanse, where the sky meets the earth in eternal conversation.",
            interactionPrompt = "Sit and watch the horizon",
            contemplativeTexts = new string[]
            {
                "The sun rises as it always has, indifferent to the struggles below.",
                "In the vastness of the sky, your troubles seem both infinite and insignificant.",
                "The horizon promises nothing, yet offers everything.",
                "Time moves like light across the land - inevitable, beautiful, and beyond your control."
            },
            theme = ContemplativeLocation.PhilosophicalTheme.TimeAndMortality,
            reflectiveQuestions = new string[]
            {
                "What will remain when you are gone?",
                "Does the sun remember the faces it has illuminated?",
                "In the grand scope of time, what truly matters?"
            },
            wisdomFragments = new string[]
            {
                "Perspective is the gift of distance.",
                "The horizon exists only because we cannot see beyond it.",
                "Every ending is a beginning viewed from another angle."
            },
            philosophicalDepth = 75f,
            restorationMultiplier = 1.2f,
            changesWithTime = true
        };
        
        // Riverside Rest for humming melodies
        ContemplativeLocation riversideRest = new ContemplativeLocation
        {
            locationName = "The Singing Waters",
            type = ContemplativeLocation.ContemplationType.RiversideRest,
            atmosphericDescription = "A gentle bend in the river where the water speaks in ancient tongues, and the stones remember every song ever sung.",
            interactionPrompt = "Sit by the water and hum",
            contemplativeTexts = new string[]
            {
                "The river carries melodies from distant lands and forgotten times.",
                "Your voice joins the eternal chorus of water over stone.",
                "Ancient songs flow through you like the river flows to the sea.",
                "In humming, you remember what words cannot express."
            },
            theme = ContemplativeLocation.PhilosophicalTheme.ConnectionToOthers,
            reflectiveQuestions = new string[]
            {
                "What songs did your ancestors sing?",
                "Can music heal what words cannot touch?",
                "Who else has sat here and hummed the same melody?"
            },
            wisdomFragments = new string[]
            {
                "Music is the language that predates words.",
                "Rivers remember every song sung beside them.",
                "In melody, we find the rhythm of existence."
            },
            philosophicalDepth = 60f,
            restorationMultiplier = 1.5f
        };
        
        // Forgotten Temple with silent monks
        ContemplativeLocation forgottenTemple = new ContemplativeLocation
        {
            locationName = "The Temple of Quiet Wisdom",
            type = ContemplativeLocation.ContemplationType.ForgottenTemple,
            atmosphericDescription = "Ancient stones hold the silence of centuries, where monks who have forgotten speech offer wisdom through presence alone.",
            interactionPrompt = "Approach the silent monks",
            contemplativeTexts = new string[]
            {
                "The monks speak without words, their silence more eloquent than any sermon.",
                "In their eyes, you see the weight of understanding that comes only through letting go.",
                "They offer riddles not to confuse, but to illuminate the questions you didn't know you carried.",
                "Their presence is a mirror, reflecting your own capacity for peace."
            },
            theme = ContemplativeLocation.PhilosophicalTheme.InnerPeace,
            reflectiveQuestions = new string[]
            {
                "What would you say if you could never speak again?",
                "Is wisdom found in knowing, or in unknowing?",
                "What questions are you afraid to ask yourself?"
            },
            wisdomFragments = new string[]
            {
                "The deepest truths cannot be spoken, only lived.",
                "Silence is not the absence of sound, but the presence of understanding.",
                "The wise know that every answer births new questions."
            },
            philosophicalDepth = 90f,
            restorationMultiplier = 2f
        };
        
        // Children's Gathering for innocent questions
        ContemplativeLocation childrenGathering = new ContemplativeLocation
        {
            locationName = "The Circle of Wonder",
            type = ContemplativeLocation.ContemplationType.ChildrenGathering,
            atmosphericDescription = "A small clearing where children gather to ask the questions adults have forgotten how to voice.",
            interactionPrompt = "Listen to the children",
            contemplativeTexts = new string[]
            {
                "A child asks why the sky is blue, and you realize you've forgotten how to wonder.",
                "Their questions cut through complexity to reach the heart of mystery.",
                "In their innocence, they see truths that experience has taught you to ignore.",
                "They remind you that not all questions need answers - some need only to be asked."
            },
            theme = ContemplativeLocation.PhilosophicalTheme.WisdomThroughAge,
            reflectiveQuestions = new string[]
            {
                "What did you know as a child that you've forgotten as an adult?",
                "When did you stop asking 'why' about everything?",
                "What would you tell your younger self?"
            },
            wisdomFragments = new string[]
            {
                "Children are philosophers who haven't learned to doubt their questions.",
                "Wonder is the beginning of wisdom.",
                "The most profound truths are often the simplest."
            },
            philosophicalDepth = 70f,
            restorationMultiplier = 1.8f
        };
        
        peacefulSites.Add(cliffsidePerch);
        peacefulSites.Add(riversideRest);
        peacefulSites.Add(forgottenTemple);
        peacefulSites.Add(childrenGathering);
        
        Debug.Log($"Initialized {peacefulSites.Count} contemplative locations");
    }
    
    void CheckForContemplativeInteractions()
    {
        if (isContemplating) return;
        
        foreach (ContemplativeLocation location in peacefulSites)
        {
            if (location.locationObject != null)
            {
                float distance = Vector3.Distance(transform.position, location.locationObject.transform.position);
                
                if (distance <= interactionRadius)
                {
                    if (currentLocation != location)
                    {
                        OnEnterContemplativeArea(location);
                        currentLocation = location;
                    }
                    
                    if (Input.GetKeyDown(KeyCode.E) && showContemplativePrompts)
                    {
                        StartContemplation(location);
                    }
                }
                else if (currentLocation == location)
                {
                    OnExitContemplativeArea(location);
                    currentLocation = null;
                }
            }
        }
    }
    
    void OnEnterContemplativeArea(ContemplativeLocation location)
    {
        ShowContemplativeMessage($"You sense a place of peace: {location.locationName}");
        ShowContemplativeMessage($"{location.atmosphericDescription}");
        
        if (showContemplativePrompts)
        {
            ShowContemplativeMessage($"Press [E] to {location.interactionPrompt}");
        }
        
        // Apply atmospheric lighting
        ApplyLocationAtmosphere(location);
    }
    
    void OnExitContemplativeArea(ContemplativeLocation location)
    {
        // Restore normal atmosphere
        RestoreNormalAtmosphere();
    }
    
    void ApplyLocationAtmosphere(ContemplativeLocation location)
    {
        // Apply environmental lighting
        if (location.environmentalLighting != null)
        {
            location.environmentalLighting.color = location.lightingColor;
            location.environmentalLighting.enabled = true;
        }
        
        // Start atmospheric effects
        if (location.atmosphericEffect != null)
        {
            location.atmosphericEffect.Play();
        }
        
        // Play ambient sound
        if (location.ambientSound != null)
        {
            AudioSource.PlayClipAtPoint(location.ambientSound, location.locationObject.transform.position, 0.3f);
        }
    }
    
    void RestoreNormalAtmosphere()
    {
        // Restore normal lighting and effects
        if (currentLocation?.environmentalLighting != null)
        {
            currentLocation.environmentalLighting.enabled = false;
        }
        
        if (currentLocation?.atmosphericEffect != null)
        {
            currentLocation.atmosphericEffect.Stop();
        }
    }
    
    void StartContemplation(ContemplativeLocation location)
    {
        if (isContemplating) return;
        
        StartCoroutine(ContemplationSequence(location));
    }
    
    IEnumerator ContemplationSequence(ContemplativeLocation location)
    {
        isContemplating = true;
        
        // Disable player movement
        DisablePlayerMovement();
        
        // Begin contemplation
        ShowContemplativeMessage($"You settle into contemplation at {location.locationName}...");
        yield return new WaitForSeconds(2f);
        
        // Apply location-specific contemplation
        yield return StartCoroutine(LocationSpecificContemplation(location));
        
        // Apply benefits
        ApplyContemplationBenefits(location);
        
        // End contemplation
        ShowContemplativeMessage("You feel refreshed and at peace. The weight on your soul has lightened.");
        yield return new WaitForSeconds(2f);
        
        // Re-enable player movement
        EnablePlayerMovement();
        
        isContemplating = false;
    }
    
    IEnumerator LocationSpecificContemplation(ContemplativeLocation location)
    {
        switch (location.type)
        {
            case ContemplativeLocation.ContemplationType.CliffsidePerch:
                yield return StartCoroutine(CliffsideContemplation(location));
                break;
            case ContemplativeLocation.ContemplationType.RiversideRest:
                yield return StartCoroutine(RiversideContemplation(location));
                break;
            case ContemplativeLocation.ContemplationType.ForgottenTemple:
                yield return StartCoroutine(TempleContemplation(location));
                break;
            case ContemplativeLocation.ContemplationType.ChildrenGathering:
                yield return StartCoroutine(ChildrenContemplation(location));
                break;
        }
    }
    
    IEnumerator CliffsideContemplation(ContemplativeLocation location)
    {
        ShowContemplativeMessage("You gaze out at the horizon, watching the eternal dance of light and shadow...");
        yield return new WaitForSeconds(3f);
        
        // Show time-appropriate contemplation
        string timeBasedReflection = GetTimeBasedReflection(location);
        ShowContemplativeMessage(timeBasedReflection);
        yield return new WaitForSeconds(4f);
        
        // Present philosophical question
        string question = location.reflectiveQuestions[Random.Range(0, location.reflectiveQuestions.Length)];
        ShowContemplativeMessage($"A question arises in your mind: {question}");
        yield return new WaitForSeconds(5f);
        
        // Wisdom fragment
        string wisdom = location.wisdomFragments[Random.Range(0, location.wisdomFragments.Length)];
        ShowContemplativeMessage($"Understanding dawns: {wisdom}");
        yield return new WaitForSeconds(3f);
    }
    
    IEnumerator RiversideContemplation(ContemplativeLocation location)
    {
        ShowContemplativeMessage("You sit by the flowing water and begin to hum an ancient melody...");
        yield return new WaitForSeconds(2f);
        
        // Play ancient melody
        if (ancientMelodies.Length > 0)
        {
            AudioClip melody = ancientMelodies[Random.Range(0, ancientMelodies.Length)];
            AudioSource.PlayClipAtPoint(melody, transform.position, 0.5f);
        }
        
        ShowContemplativeMessage("The melody flows through you like the river flows to the sea...");
        yield return new WaitForSeconds(4f);
        
        ShowContemplativeMessage("In the harmony of voice and water, you find a peace that words cannot describe.");
        yield return new WaitForSeconds(3f);
        
        // Musical system integration
        if (musicalSystem != null)
        {
            musicalSystem.OnStoryBeat(); // Trigger peaceful musical response
        }
    }
    
    IEnumerator TempleContemplation(ContemplativeLocation location)
    {
        ShowContemplativeMessage("The silent monks acknowledge your presence with gentle nods...");
        yield return new WaitForSeconds(3f);
        
        ShowContemplativeMessage("One monk approaches and offers you a riddle written on ancient parchment...");
        yield return new WaitForSeconds(3f);
        
        // Present riddle
        string riddle = GenerateMonkRiddle();
        ShowContemplativeMessage($"The riddle reads: '{riddle}'");
        yield return new WaitForSeconds(5f);
        
        ShowContemplativeMessage("You contemplate the riddle in silence, feeling wisdom settle into your bones...");
        yield return new WaitForSeconds(4f);
        
        ShowContemplativeMessage("The monk smiles knowingly. Some answers can only be lived, not spoken.");
        yield return new WaitForSeconds(3f);
    }
    
    IEnumerator ChildrenContemplation(ContemplativeLocation location)
    {
        ShowContemplativeMessage("Children gather around you, their eyes bright with curiosity...");
        yield return new WaitForSeconds(2f);
        
        // Generate innocent philosophical question
        string childQuestion = GenerateChildQuestion();
        ShowContemplativeMessage($"A small voice asks: '{childQuestion}'");
        yield return new WaitForSeconds(4f);
        
        ShowContemplativeMessage("Their innocent question cuts through layers of complexity to touch something fundamental...");
        yield return new WaitForSeconds(3f);
        
        ShowContemplativeMessage("In trying to answer simply, you rediscover truths you had forgotten.");
        yield return new WaitForSeconds(3f);
        
        // Play child laughter
        if (childQuestions.Length > 0)
        {
            AudioClip laughter = childQuestions[Random.Range(0, childQuestions.Length)];
            AudioSource.PlayClipAtPoint(laughter, transform.position, 0.4f);
        }
    }
    
    string GetTimeBasedReflection(ContemplativeLocation location)
    {
        // Get current time of day and return appropriate reflection
        if (timeSystem != null)
        {
            float timeOfDay = timeSystem.GetCurrentGameTime() % 1f; // Get fractional day
            
            if (timeOfDay < 0.25f) // Dawn
                return "The dawn light reminds you that every day is a chance to begin again.";
            else if (timeOfDay < 0.5f) // Morning
                return "The morning sun climbs steadily, indifferent to your struggles yet somehow encouraging.";
            else if (timeOfDay < 0.75f) // Afternoon
                return "The afternoon light casts long shadows, each one a reminder of time's passage.";
            else // Evening/Night
                return "The setting sun paints the sky in colors that exist nowhere else, beautiful because they are fleeting.";
        }
        
        return location.contemplativeTexts[Random.Range(0, location.contemplativeTexts.Length)];
    }
    
    string GenerateMonkRiddle()
    {
        string[] riddles = {
            "What burns without consuming, illuminates without blinding, and warms without scorching?",
            "The river that flows upward, the mountain that grows downward, the flame that cools - what am I?",
            "I am the space between thoughts, the pause between breaths, the silence between words. What am I?",
            "What is heavier when empty than when full, darker when lit than when dim?",
            "The path that leads nowhere goes everywhere. The door that opens to nothing reveals everything. Why?"
        };
        
        return riddles[Random.Range(0, riddles.Length)];
    }
    
    string GenerateChildQuestion()
    {
        string[] questions = {
            "Why do grown-ups forget how to play?",
            "If fire is hot, why does it make people feel warm inside?",
            "Do trees get lonely when all their leaves fall off?",
            "Why do people cry when they're happy?",
            "If you could give your sadness to someone else, would you?",
            "What color is kindness?",
            "Do you think the stars remember us when we look at them?"
        };
        
        return questions[Random.Range(0, questions.Length)];
    }
    
    void ApplyContemplationBenefits(ContemplativeLocation location)
    {
        float restorationAmount = mentalRestoration * location.restorationMultiplier;
        float wisdomAmount = wisdomGain * (location.philosophicalDepth / 100f);
        
        // Apply psychological benefits
        if (psycheSystem != null)
        {
            psycheSystem.ReduceTrauma(restorationAmount, $"Peaceful contemplation at {location.locationName}");
            psycheSystem.enlightenment += wisdomAmount;
        }
        
        // Apply philosophical growth
        PhilosophicalMoralitySystem moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        if (moralitySystem != null)
        {
            moralitySystem.AddPhilosophicalGrowth(wisdomAmount, location.theme.ToString());
        }
        
        Debug.Log($"Contemplation benefits applied: {restorationAmount} trauma reduction, {wisdomAmount} wisdom gain");
    }
    
    void UpdateLocationAtmospheres()
    {
        // Update locations based on time and season
        foreach (ContemplativeLocation location in peacefulSites)
        {
            if (location.changesWithTime)
            {
                UpdateLocationForTime(location);
            }
        }
    }
    
    void UpdateLocationForTime(ContemplativeLocation location)
    {
        if (timeSystem == null) return;
        
        // Update based on season
        TimeProgressionSystem.Season currentSeason = timeSystem.GetCurrentSeason();
        
        foreach (SeasonalVariation variation in location.seasonalVariations)
        {
            if (variation.season == currentSeason)
            {
                ApplySeasonalVariation(location, variation);
                break;
            }
        }
    }
    
    void ApplySeasonalVariation(ContemplativeLocation location, SeasonalVariation variation)
    {
        if (location.environmentalLighting != null)
        {
            location.environmentalLighting.color = variation.seasonalLighting;
        }
        
        // Update atmospheric description
        location.atmosphericDescription = variation.seasonalDescription;
    }
    
    void DisablePlayerMovement()
    {
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.SetMovementEnabled(false);
        }
    }
    
    void EnablePlayerMovement()
    {
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.SetMovementEnabled(true);
        }
    }
    
    void ShowContemplativeMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowNarrativeText(message);
        }
        
        Debug.Log($"Contemplative: {message}");
    }
    
    // Getters
    public bool IsContemplating() => isContemplating;
    public ContemplativeLocation GetCurrentLocation() => currentLocation;
    public List<ContemplativeLocation> GetPeacefulSites() => peacefulSites;
}
