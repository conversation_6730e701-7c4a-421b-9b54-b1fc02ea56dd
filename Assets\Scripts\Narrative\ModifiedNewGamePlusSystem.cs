using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class ModifiedNewGamePlusSystem : MonoBehaviour
{
    [Header("New Game+ State")]
    public bool isNewGamePlus = false;
    public bool falseEndingTriggered = false;
    public bool dreamSequenceCompleted = false;
    public NewGamePlusData previousPlaythrough;
    
    [Header("False Ending")]
    public float falseEndingDuration = 120f; // 2 minutes
    public GameObject dreamEnvironment;
    public AudioClip dreamMusic;
    public AudioClip awakeningSound;
    
    [Header("World Changes")]
    public List<WorldEcho> worldEchoes = new List<WorldEcho>();
    public List<NPCMemoryFragment> npcMemories = new List<NPCMemoryFragment>();
    public float enemyDifficultyMultiplier = 1.3f;
    
    [Header("Retained Elements")]
    public bool retainWeapons = true;
    public bool retainGear = true;
    public bool retainKnowledge = true;
    public bool retainTitles = true;
    public bool retainPhilosophicalGrowth = true;
    
    private PhilosophicalMoralitySystem moralitySystem;
    private TearOfAshSystem tearSystem;
    private OrphanedChildCompanion childCompanion;
    private DynamicTitleSystem titleSystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class NewGamePlusData
    {
        [Header("Previous Journey")]
        public float completionTime;
        public int totalChoicesMade;
        public PhilosophicalMoralitySystem.MoralPath finalPath;
        public OrphanedChildCompanion.FinalChoice childChoice;
        public bool tearOfAshObtained;
        public int sagesDefeated;
        
        [Header("Retained Power")]
        public List<string> unlockedWeapons;
        public List<string> earnedTitles;
        public float philosophicalWisdom;
        public float psychologicalGrowth;
        public Dictionary<string, float> raceStandings;
        
        [Header("World Memory")]
        public List<string> significantActions;
        public List<string> metNPCs;
        public List<string> visitedLocations;
        public float karmaScore;
    }
    
    [System.Serializable]
    public class WorldEcho
    {
        public string echoName;
        public string echoDescription;
        public EchoType type;
        public Vector3 location;
        public string triggerCondition;
        public bool hasTriggered = false;
        
        public enum EchoType
        {
            VisualMemory,    // Ghostly images of past events
            AudioEcho,       // Sounds from previous playthrough
            DialogueFragment, // NPCs remember vaguely
            EnvironmentalChange, // Subtle world differences
            SecretDialogue   // New dialogue options
        }
    }
    
    [System.Serializable]
    public class NPCMemoryFragment
    {
        public string npcName;
        public string memoryText;
        public float memoryStrength; // How clearly they remember
        public string[] vagueRecollections;
        public bool remembersPlayer;
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        tearSystem = GetComponent<TearOfAshSystem>();
        childCompanion = GetComponent<OrphanedChildCompanion>();
        titleSystem = GetComponent<DynamicTitleSystem>();
        gameManager = GameManager.Instance;
        
        InitializeNewGamePlusSystem();
    }
    
    void Update()
    {
        if (isNewGamePlus)
        {
            UpdateWorldEchoes();
            CheckForMemoryTriggers();
        }
    }
    
    void InitializeNewGamePlusSystem()
    {
        // Check if this is a New Game+ session
        isNewGamePlus = PlayerPrefs.GetInt("IsNewGamePlus", 0) == 1;
        
        if (isNewGamePlus)
        {
            LoadPreviousPlaythroughData();
            SetupWorldEchoes();
            SetupNPCMemories();
            ApplyNewGamePlusChanges();
        }
        
        Debug.Log($"New Game+ System initialized - Is New Game+: {isNewGamePlus}");
    }
    
    public void TriggerMainStoryCompletion()
    {
        // Called when the main story is completed
        StartCoroutine(MainStoryCompletionSequence());
    }
    
    IEnumerator MainStoryCompletionSequence()
    {
        ShowNewGameMessage("The final battle ends... The Cinderborn stands victorious...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("But something feels... wrong...");
        yield return new WaitForSeconds(2f);
        
        // Trigger false ending
        yield return StartCoroutine(FalseEndingSequence());
        
        // After false ending, present New Game+ option
        PresentNewGamePlusOption();
    }
    
    IEnumerator FalseEndingSequence()
    {
        falseEndingTriggered = true;
        
        // Fade to black
        ShowNewGameMessage("Everything fades to black...");
        yield return new WaitForSeconds(2f);
        
        // Dream environment
        if (dreamEnvironment != null)
        {
            dreamEnvironment.SetActive(true);
        }
        
        // Play dream music
        if (dreamMusic != null)
        {
            AudioSource.PlayClipAtPoint(dreamMusic, transform.position);
        }
        
        // False ending dialogue
        yield return StartCoroutine(FalseEndingDialogue());
        
        // The awakening
        yield return StartCoroutine(AwakeningSequence());
        
        dreamSequenceCompleted = true;
    }
    
    IEnumerator FalseEndingDialogue()
    {
        ShowNewGameMessage("The Cinderborn awakens in a field of ash...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("\"Was it... all a dream?\"");
        yield return new WaitForSeconds(2f);
        
        ShowNewGameMessage("The memories feel so real... the battles, the choices, the pain...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("But here you stand, in the aftermath of some great battle...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("Your weapons are real. Your scars are real. Your knowledge is real.");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("\"Perhaps... perhaps it was a vision of what could be...\"");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("\"Or what must be...\"");
        yield return new WaitForSeconds(2f);
    }
    
    IEnumerator AwakeningSequence()
    {
        ShowNewGameMessage("The vision fades, but the wisdom remains...");
        yield return new WaitForSeconds(2f);
        
        // Play awakening sound
        if (awakeningSound != null)
        {
            AudioSource.PlayClipAtPoint(awakeningSound, transform.position);
        }
        
        ShowNewGameMessage("You retain all your weapons, all your knowledge, all your growth...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("But the world... the world is as it was before...");
        yield return new WaitForSeconds(3f);
        
        ShowNewGameMessage("Yet something is different. Echoes of the vision linger...");
        yield return new WaitForSeconds(3f);
        
        // Deactivate dream environment
        if (dreamEnvironment != null)
        {
            dreamEnvironment.SetActive(false);
        }
    }
    
    void PresentNewGamePlusOption()
    {
        ShowNewGameMessage("NEW GAME+ UNLOCKED");
        ShowNewGameMessage("Begin the journey anew with all your power and wisdom intact?");
        
        // This would integrate with the game's menu system
        // For now, automatically start New Game+
        StartNewGamePlus();
    }
    
    void StartNewGamePlus()
    {
        // Save current playthrough data
        SaveCurrentPlaythroughData();
        
        // Set New Game+ flag
        PlayerPrefs.SetInt("IsNewGamePlus", 1);
        PlayerPrefs.Save();
        
        // Reset world state but retain player progress
        ResetWorldForNewGamePlus();
        
        ShowNewGameMessage("The cycle begins anew... but you are not the same person who started this journey.");
    }
    
    void SaveCurrentPlaythroughData()
    {
        previousPlaythrough = new NewGamePlusData
        {
            completionTime = Time.time,
            totalChoicesMade = gameManager?.GetTotalChoicesMade() ?? 0,
            finalPath = moralitySystem?.GetCurrentPath() ?? PhilosophicalMoralitySystem.MoralPath.Neutral,
            childChoice = childCompanion?.GetFinalChoice() ?? OrphanedChildCompanion.FinalChoice.None,
            tearOfAshObtained = tearSystem?.IsTearOfAshObtained() ?? false,
            sagesDefeated = tearSystem?.GetSagesDefeated() ?? 0,
            philosophicalWisdom = tearSystem?.GetPhilosophicalGrowth() ?? 0f,
            karmaScore = CalculateKarmaScore()
        };
        
        // Save to PlayerPrefs for persistence
        SaveToPlayerPrefs();
    }
    
    void SaveToPlayerPrefs()
    {
        PlayerPrefs.SetFloat("NGP_CompletionTime", previousPlaythrough.completionTime);
        PlayerPrefs.SetInt("NGP_TotalChoices", previousPlaythrough.totalChoicesMade);
        PlayerPrefs.SetInt("NGP_FinalPath", (int)previousPlaythrough.finalPath);
        PlayerPrefs.SetInt("NGP_ChildChoice", (int)previousPlaythrough.childChoice);
        PlayerPrefs.SetInt("NGP_TearObtained", previousPlaythrough.tearOfAshObtained ? 1 : 0);
        PlayerPrefs.SetInt("NGP_SagesDefeated", previousPlaythrough.sagesDefeated);
        PlayerPrefs.SetFloat("NGP_PhilosophicalWisdom", previousPlaythrough.philosophicalWisdom);
        PlayerPrefs.SetFloat("NGP_KarmaScore", previousPlaythrough.karmaScore);
        PlayerPrefs.Save();
    }
    
    void LoadPreviousPlaythroughData()
    {
        previousPlaythrough = new NewGamePlusData
        {
            completionTime = PlayerPrefs.GetFloat("NGP_CompletionTime", 0f),
            totalChoicesMade = PlayerPrefs.GetInt("NGP_TotalChoices", 0),
            finalPath = (PhilosophicalMoralitySystem.MoralPath)PlayerPrefs.GetInt("NGP_FinalPath", 0),
            childChoice = (OrphanedChildCompanion.FinalChoice)PlayerPrefs.GetInt("NGP_ChildChoice", 0),
            tearOfAshObtained = PlayerPrefs.GetInt("NGP_TearObtained", 0) == 1,
            sagesDefeated = PlayerPrefs.GetInt("NGP_SagesDefeated", 0),
            philosophicalWisdom = PlayerPrefs.GetFloat("NGP_PhilosophicalWisdom", 0f),
            karmaScore = PlayerPrefs.GetFloat("NGP_KarmaScore", 0f)
        };
    }
    
    void ResetWorldForNewGamePlus()
    {
        // Reset story progress but keep player power
        if (gameManager != null)
        {
            gameManager.ResetStoryProgress();
        }
        
        // Reset NPC states but add memory fragments
        ResetNPCsWithMemories();
        
        // Reset world events but add echoes
        ResetWorldWithEchoes();
        
        // Increase enemy difficulty
        IncreaseEnemyDifficulty();
    }
    
    void SetupWorldEchoes()
    {
        // Create echoes based on previous playthrough
        if (previousPlaythrough.tearOfAshObtained)
        {
            worldEchoes.Add(new WorldEcho
            {
                echoName = "Tear of Ash Echo",
                echoDescription = "A faint shimmer in the air where the Tear of Ash once manifested",
                type = WorldEcho.EchoType.VisualMemory,
                location = Vector3.zero, // Summit location
                triggerCondition = "ReachSummit"
            });
        }
        
        if (previousPlaythrough.childChoice == OrphanedChildCompanion.FinalChoice.AbandonChild)
        {
            worldEchoes.Add(new WorldEcho
            {
                echoName = "Child's Sorrow Echo",
                echoDescription = "The faint sound of a child crying echoes in the wind",
                type = WorldEcho.EchoType.AudioEcho,
                location = Vector3.zero, // Where the choice was made
                triggerCondition = "MeetChild"
            });
        }
        
        // Add more echoes based on previous choices
        AddMoralPathEchoes();
    }
    
    void AddMoralPathEchoes()
    {
        switch (previousPlaythrough.finalPath)
        {
            case PhilosophicalMoralitySystem.MoralPath.Evil:
                worldEchoes.Add(new WorldEcho
                {
                    echoName = "Dark Path Echo",
                    echoDescription = "Shadows seem deeper here, as if remembering great darkness",
                    type = WorldEcho.EchoType.EnvironmentalChange,
                    triggerCondition = "EvilChoice"
                });
                break;
            
            case PhilosophicalMoralitySystem.MoralPath.Good:
                worldEchoes.Add(new WorldEcho
                {
                    echoName = "Light Path Echo",
                    echoDescription = "A warm feeling lingers here, as if touched by great kindness",
                    type = WorldEcho.EchoType.EnvironmentalChange,
                    triggerCondition = "GoodChoice"
                });
                break;
        }
    }
    
    void SetupNPCMemories()
    {
        // NPCs have vague memories of the previous playthrough
        NPCController[] allNPCs = FindObjectsOfType<NPCController>();
        
        foreach (NPCController npc in allNPCs)
        {
            NPCMemoryFragment memory = new NPCMemoryFragment
            {
                npcName = npc.npcName,
                memoryStrength = Random.Range(0.1f, 0.7f),
                remembersPlayer = Random.Range(0f, 1f) < 0.3f, // 30% chance
                vagueRecollections = GenerateVagueRecollections(npc.npcName)
            };
            
            npcMemories.Add(memory);
        }
    }
    
    string[] GenerateVagueRecollections(string npcName)
    {
        return new string[]
        {
            "You seem... familiar somehow. Have we met before?",
            "I had the strangest dream about someone like you...",
            "There's something about you that reminds me of... something I can't quite remember.",
            "I feel like I should know you, but I can't place where we might have met.",
            "You have the look of someone who's seen much, though you seem young..."
        };
    }
    
    void ApplyNewGamePlusChanges()
    {
        // Apply retained elements
        if (retainWeapons)
        {
            RestoreWeapons();
        }
        
        if (retainGear)
        {
            RestoreGear();
        }
        
        if (retainKnowledge)
        {
            RestoreKnowledge();
        }
        
        if (retainTitles)
        {
            RestoreTitles();
        }
        
        if (retainPhilosophicalGrowth)
        {
            RestorePhilosophicalGrowth();
        }
        
        ShowNewGameMessage("Your previous journey's wisdom flows through you...");
    }
    
    void RestoreWeapons()
    {
        // Restore all unlocked weapons from previous playthrough
        UniqueWeaponsSystem weaponSystem = GetComponent<UniqueWeaponsSystem>();
        if (weaponSystem != null && previousPlaythrough.unlockedWeapons != null)
        {
            foreach (string weaponName in previousPlaythrough.unlockedWeapons)
            {
                // Unlock weapon without requirements
                weaponSystem.ForceUnlockWeapon(weaponName);
            }
        }
    }
    
    void RestoreGear()
    {
        // Restore armor and equipment
        EconomySystem economy = GetComponent<EconomySystem>();
        if (economy != null)
        {
            // Add high-quality starting gear
            economy.AddItem("Veteran's Cloak", EconomySystem.InventoryItem.ItemType.Armor, 
                          EconomySystem.InventoryItem.ItemRarity.Epic, 1, 500f);
        }
    }
    
    void RestoreKnowledge()
    {
        // Unlock special dialogue options and knowledge
        if (gameManager != null)
        {
            gameManager.UnlockDialogueOption("VisionaryKnowledge");
            gameManager.UnlockDialogueOption("ProphenticWisdom");
            gameManager.UnlockDialogueOption("CyclicalUnderstanding");
        }
    }
    
    void RestoreTitles()
    {
        // Restore earned titles with special New Game+ variants
        if (titleSystem != null)
        {
            titleSystem.AddSpecialTitle("Visionary", "One who has seen the cycle of fate");
            titleSystem.AddSpecialTitle("Echo Walker", "One who walks between what was and what will be");
        }
    }
    
    void RestorePhilosophicalGrowth()
    {
        // Apply philosophical wisdom from previous playthrough
        if (tearSystem != null)
        {
            tearSystem.ApplyRetainedWisdom(previousPlaythrough.philosophicalWisdom);
        }
    }
    
    void UpdateWorldEchoes()
    {
        foreach (WorldEcho echo in worldEchoes)
        {
            if (!echo.hasTriggered && ShouldTriggerEcho(echo))
            {
                TriggerWorldEcho(echo);
            }
        }
    }
    
    bool ShouldTriggerEcho(WorldEcho echo)
    {
        // Check if conditions are met to trigger this echo
        switch (echo.triggerCondition)
        {
            case "ReachSummit":
                return Vector3.Distance(transform.position, echo.location) < 10f;
            case "MeetChild":
                return childCompanion != null && childCompanion.GetBondLevel() > 0f;
            case "EvilChoice":
                return moralitySystem != null && moralitySystem.GetCurrentPath() == PhilosophicalMoralitySystem.MoralPath.Evil;
            case "GoodChoice":
                return moralitySystem != null && moralitySystem.GetCurrentPath() == PhilosophicalMoralitySystem.MoralPath.Good;
            default:
                return false;
        }
    }
    
    void TriggerWorldEcho(WorldEcho echo)
    {
        echo.hasTriggered = true;
        
        ShowNewGameMessage($"ECHO: {echo.echoDescription}");
        
        // Apply echo effects based on type
        switch (echo.type)
        {
            case WorldEcho.EchoType.VisualMemory:
                CreateVisualEcho(echo);
                break;
            case WorldEcho.EchoType.AudioEcho:
                CreateAudioEcho(echo);
                break;
            case WorldEcho.EchoType.SecretDialogue:
                UnlockSecretDialogue(echo);
                break;
        }
    }
    
    void CreateVisualEcho(WorldEcho echo)
    {
        // Create a ghostly visual effect
        GameObject echoEffect = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        echoEffect.transform.position = echo.location;
        echoEffect.GetComponent<Renderer>().material.color = new Color(1f, 1f, 1f, 0.3f);
        
        // Fade out over time
        StartCoroutine(FadeEchoEffect(echoEffect, 5f));
    }
    
    void CreateAudioEcho(WorldEcho echo)
    {
        // Play an audio echo
        // This would play appropriate sound based on the echo
    }
    
    void UnlockSecretDialogue(WorldEcho echo)
    {
        // Unlock special dialogue options
        gameManager?.UnlockDialogueOption($"Echo_{echo.echoName}");
    }
    
    IEnumerator FadeEchoEffect(GameObject effect, float duration)
    {
        float elapsed = 0f;
        Renderer renderer = effect.GetComponent<Renderer>();
        Color startColor = renderer.material.color;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Lerp(startColor.a, 0f, elapsed / duration);
            renderer.material.color = new Color(startColor.r, startColor.g, startColor.b, alpha);
            yield return null;
        }
        
        Destroy(effect);
    }
    
    void CheckForMemoryTriggers()
    {
        // Check if NPCs should trigger memory fragments
        foreach (NPCMemoryFragment memory in npcMemories)
        {
            if (memory.remembersPlayer && Random.Range(0f, 1f) < 0.001f) // Very small chance per frame
            {
                TriggerNPCMemory(memory);
            }
        }
    }
    
    void TriggerNPCMemory(NPCMemoryFragment memory)
    {
        if (memory.vagueRecollections.Length > 0)
        {
            string recollection = memory.vagueRecollections[Random.Range(0, memory.vagueRecollections.Length)];
            ShowNewGameMessage($"{memory.npcName}: \"{recollection}\"");
        }
    }
    
    void ResetNPCsWithMemories()
    {
        // Reset NPC states but add memory components
        NPCController[] allNPCs = FindObjectsOfType<NPCController>();
        
        foreach (NPCController npc in allNPCs)
        {
            npc.ResetForNewGamePlus();
            
            // Add memory component
            NPCMemoryComponent memoryComponent = npc.gameObject.AddComponent<NPCMemoryComponent>();
            NPCMemoryFragment memory = npcMemories.Find(m => m.npcName == npc.npcName);
            if (memory != null)
            {
                memoryComponent.Initialize(memory);
            }
        }
    }
    
    void ResetWorldWithEchoes()
    {
        // Reset world state but add echo triggers
        // This would reset quest states, world events, etc.
    }
    
    void IncreaseEnemyDifficulty()
    {
        // Increase enemy stats for New Game+
        EnemyHealth[] allEnemies = FindObjectsOfType<EnemyHealth>();
        
        foreach (EnemyHealth enemy in allEnemies)
        {
            enemy.maxHealth *= enemyDifficultyMultiplier;
            enemy.currentHealth = enemy.maxHealth;
        }
    }
    
    float CalculateKarmaScore()
    {
        float score = 0f;
        
        // Calculate based on various factors
        if (moralitySystem != null)
        {
            switch (moralitySystem.GetCurrentPath())
            {
                case PhilosophicalMoralitySystem.MoralPath.Good:
                    score += 100f;
                    break;
                case PhilosophicalMoralitySystem.MoralPath.Evil:
                    score -= 50f;
                    break;
            }
        }
        
        if (childCompanion != null)
        {
            switch (childCompanion.GetFinalChoice())
            {
                case OrphanedChildCompanion.FinalChoice.AdoptChild:
                    score += 75f;
                    break;
                case OrphanedChildCompanion.FinalChoice.AbandonChild:
                    score -= 100f;
                    break;
            }
        }
        
        return score;
    }
    
    void ShowNewGameMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 5f));
        }
        
        Debug.Log($"New Game+: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public bool IsNewGamePlus() => isNewGamePlus;
    public bool IsFalseEndingTriggered() => falseEndingTriggered;
    public NewGamePlusData GetPreviousPlaythrough() => previousPlaythrough;
    public List<WorldEcho> GetWorldEchoes() => worldEchoes;
    public List<NPCMemoryFragment> GetNPCMemories() => npcMemories;
}

public class NPCMemoryComponent : MonoBehaviour
{
    public ModifiedNewGamePlusSystem.NPCMemoryFragment memoryData;
    
    public void Initialize(ModifiedNewGamePlusSystem.NPCMemoryFragment memory)
    {
        memoryData = memory;
    }
    
    public string GetMemoryDialogue()
    {
        if (memoryData.vagueRecollections.Length > 0)
        {
            return memoryData.vagueRecollections[Random.Range(0, memoryData.vagueRecollections.Length)];
        }
        return "";
    }
}
