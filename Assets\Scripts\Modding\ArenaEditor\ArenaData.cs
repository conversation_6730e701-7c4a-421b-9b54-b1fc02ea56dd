using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// ScriptableObject data structure for Arena Creation Tool
/// Defines all arena properties for Cinder of Darkness modding system
/// </summary>
[CreateAssetMenu(fileName = "NewArena", menuName = "Cinder of Darkness/Arena Data")]
public class ArenaData : ScriptableObject
{
    [Header("Basic Arena Information")]
    public string arenaName = "New Arena";
    public string arenaId = "";
    public string description = "";
    public string author = "Unknown";
    public string version = "1.0";
    
    [Header("Biome Settings")]
    public BiomeType biomeType = BiomeType.Ashlands;
    public string customBiomePath = "";
    
    [Header("Arena Layout")]
    public Vector3 arenaSize = new Vector3(50f, 10f, 50f);
    public Vector3 playerSpawnPoint = Vector3.zero;
    public List<Vector3> enemySpawnPoints = new List<Vector3>();
    public List<PropPlacement> destructibleProps = new List<PropPlacement>();
    public List<PropPlacement> staticProps = new List<PropPlacement>();
    public List<TrapPlacement> traps = new List<TrapPlacement>();
    
    [Header("Environmental Effects")]
    public string[] environmentalEffects = new string[0];
    public FogSettings fogSettings = new FogSettings();
    public LightingSettings lightingSettings = new LightingSettings();
    public WeatherEffect weatherEffect = WeatherEffect.None;
    public float weatherIntensity = 1f;
    
    [Header("Win/Loss Conditions")]
    public WinConditionType winCondition = WinConditionType.KillAllEnemies;
    public float timeLimitSeconds = 300f; // 5 minutes default
    public int killTarget = 10;
    public Vector3 targetLocation = Vector3.zero;
    public float targetRadius = 5f;
    
    [Header("Gameplay Modifiers")]
    public float scoreMultiplier = 1f;
    public float difficultyMultiplier = 1f;
    public bool allowMagic = true;
    public bool allowHealing = true;
    public bool infiniteAmmo = false;
    public bool respawnEnemies = false;
    public float respawnDelay = 30f;
    
    [Header("Audio Settings")]
    public string backgroundMusic = "";
    public string ambientSounds = "";
    public float musicVolume = 0.7f;
    public float ambientVolume = 0.5f;
    
    [Header("Visual Effects")]
    public ParticleEffect[] particleEffects = new ParticleEffect[0];
    public PostProcessingProfile postProcessingProfile;
    public bool enableDynamicLighting = true;
    public bool enableShadows = true;
    
    [Header("Arena Boundaries")]
    public BoundaryType boundaryType = BoundaryType.Invisible;
    public float boundaryHeight = 20f;
    public Material boundaryMaterial;
    
    /// <summary>
    /// Available biome types for arena creation
    /// </summary>
    public enum BiomeType
    {
        Ashlands,
        ForestOfShadows,
        FrozenNorth,
        CityOfTheSky,
        KingdomOfSouthernBolt,
        Custom
    }
    
    /// <summary>
    /// Win condition types for arena challenges
    /// </summary>
    public enum WinConditionType
    {
        KillAllEnemies,
        SurviveTimeLimit,
        ReachLocation,
        KillSpecificTarget,
        CollectItems,
        DefendObject,
        EscapeArena,
        ScoreThreshold
    }
    
    /// <summary>
    /// Weather effects available in arenas
    /// </summary>
    public enum WeatherEffect
    {
        None,
        Rain,
        Snow,
        Fog,
        Sandstorm,
        AshFall,
        Lightning,
        Wind
    }
    
    /// <summary>
    /// Arena boundary types
    /// </summary>
    public enum BoundaryType
    {
        Invisible,
        Wall,
        Fire,
        Energy,
        Void
    }
    
    /// <summary>
    /// Placement data for props in the arena
    /// </summary>
    [System.Serializable]
    public class PropPlacement
    {
        public string propId;
        public string propName;
        public Vector3 position;
        public Vector3 rotation;
        public Vector3 scale = Vector3.one;
        public bool isDestructible = false;
        public float health = 100f;
        public string[] tags = new string[0];
        public Material customMaterial;
    }
    
    /// <summary>
    /// Placement data for traps in the arena
    /// </summary>
    [System.Serializable]
    public class TrapPlacement
    {
        public string trapId;
        public string trapType;
        public Vector3 position;
        public Vector3 rotation;
        public float triggerRadius = 2f;
        public float damage = 50f;
        public float cooldown = 5f;
        public bool isReusable = true;
        public string[] triggerTags = new string[] { "Player" };
    }
    
    /// <summary>
    /// Fog settings for atmospheric effects
    /// </summary>
    [System.Serializable]
    public class FogSettings
    {
        public bool enableFog = false;
        public Color fogColor = Color.gray;
        public float fogDensity = 0.01f;
        public float fogStartDistance = 10f;
        public float fogEndDistance = 100f;
        public FogMode fogMode = FogMode.ExponentialSquared;
    }
    
    /// <summary>
    /// Lighting settings for arena atmosphere
    /// </summary>
    [System.Serializable]
    public class LightingSettings
    {
        public Color ambientColor = Color.gray;
        public float ambientIntensity = 1f;
        public Color directionalLightColor = Color.white;
        public float directionalLightIntensity = 1f;
        public Vector3 directionalLightRotation = new Vector3(50f, -30f, 0f);
        public bool enableRealtimeLighting = true;
        public bool enableLightProbes = true;
    }
    
    /// <summary>
    /// Particle effect settings
    /// </summary>
    [System.Serializable]
    public class ParticleEffect
    {
        public string effectId;
        public string effectName;
        public Vector3 position;
        public Vector3 rotation;
        public float scale = 1f;
        public bool looping = true;
        public float duration = 10f;
        public Color tintColor = Color.white;
    }
    
    /// <summary>
    /// Post-processing profile reference
    /// </summary>
    [System.Serializable]
    public class PostProcessingProfile
    {
        public string profileName;
        public bool enableBloom = false;
        public bool enableVignette = false;
        public bool enableColorGrading = false;
        public bool enableDepthOfField = false;
        public bool enableChromaticAberration = false;
    }
    
    /// <summary>
    /// Validate arena data for consistency and completeness
    /// </summary>
    public bool ValidateArenaData()
    {
        bool isValid = true;
        
        // Check required fields
        if (string.IsNullOrEmpty(arenaName))
        {
            Debug.LogError("Arena name is required");
            isValid = false;
        }
        
        if (string.IsNullOrEmpty(arenaId))
        {
            arenaId = System.Guid.NewGuid().ToString();
        }
        
        // Validate arena size
        if (arenaSize.x <= 0 || arenaSize.y <= 0 || arenaSize.z <= 0)
        {
            Debug.LogError("Arena size must be positive");
            isValid = false;
        }
        
        // Validate spawn points
        if (enemySpawnPoints.Count == 0 && winCondition == WinConditionType.KillAllEnemies)
        {
            Debug.LogWarning("No enemy spawn points defined for kill-all win condition");
        }
        
        // Validate win conditions
        switch (winCondition)
        {
            case WinConditionType.SurviveTimeLimit:
                if (timeLimitSeconds <= 0)
                {
                    Debug.LogError("Time limit must be positive for survive win condition");
                    isValid = false;
                }
                break;
            case WinConditionType.KillSpecificTarget:
                if (killTarget <= 0)
                {
                    Debug.LogError("Kill target must be positive");
                    isValid = false;
                }
                break;
            case WinConditionType.ReachLocation:
                if (targetRadius <= 0)
                {
                    Debug.LogError("Target radius must be positive");
                    isValid = false;
                }
                break;
        }
        
        // Validate props
        foreach (var prop in destructibleProps)
        {
            if (string.IsNullOrEmpty(prop.propId))
            {
                Debug.LogWarning("Destructible prop missing ID");
            }
            if (prop.isDestructible && prop.health <= 0)
            {
                Debug.LogWarning("Destructible prop has invalid health");
            }
        }
        
        // Validate traps
        foreach (var trap in traps)
        {
            if (string.IsNullOrEmpty(trap.trapId))
            {
                Debug.LogWarning("Trap missing ID");
            }
            if (trap.triggerRadius <= 0)
            {
                Debug.LogWarning("Trap has invalid trigger radius");
            }
        }
        
        return isValid;
    }
    
    /// <summary>
    /// Get arena bounds based on size and position
    /// </summary>
    public Bounds GetArenaBounds()
    {
        return new Bounds(playerSpawnPoint, arenaSize);
    }
    
    /// <summary>
    /// Check if a position is within arena bounds
    /// </summary>
    public bool IsPositionInBounds(Vector3 position)
    {
        Bounds bounds = GetArenaBounds();
        return bounds.Contains(position);
    }
    
    /// <summary>
    /// Get biome-specific default settings
    /// </summary>
    public void ApplyBiomeDefaults()
    {
        switch (biomeType)
        {
            case BiomeType.Ashlands:
                ApplyAshlandsDefaults();
                break;
            case BiomeType.ForestOfShadows:
                ApplyForestDefaults();
                break;
            case BiomeType.FrozenNorth:
                ApplyFrozenDefaults();
                break;
            case BiomeType.CityOfTheSky:
                ApplySkyDefaults();
                break;
            case BiomeType.KingdomOfSouthernBolt:
                ApplyKingdomDefaults();
                break;
        }
    }
    
    void ApplyAshlandsDefaults()
    {
        fogSettings.enableFog = true;
        fogSettings.fogColor = new Color(0.3f, 0.2f, 0.15f);
        fogSettings.fogDensity = 0.015f;
        
        lightingSettings.ambientColor = new Color(0.4f, 0.25f, 0.15f);
        lightingSettings.directionalLightColor = new Color(1f, 0.6f, 0.3f);
        
        weatherEffect = WeatherEffect.AshFall;
        environmentalEffects = new string[] { "ember_particles", "heat_distortion" };
    }
    
    void ApplyForestDefaults()
    {
        fogSettings.enableFog = true;
        fogSettings.fogColor = new Color(0.2f, 0.25f, 0.3f);
        fogSettings.fogDensity = 0.02f;
        
        lightingSettings.ambientColor = new Color(0.3f, 0.35f, 0.4f);
        lightingSettings.directionalLightColor = new Color(0.6f, 0.7f, 0.8f);
        
        weatherEffect = WeatherEffect.Fog;
        environmentalEffects = new string[] { "leaf_particles", "shadow_wisps" };
    }
    
    void ApplyFrozenDefaults()
    {
        fogSettings.enableFog = true;
        fogSettings.fogColor = new Color(0.8f, 0.9f, 1f);
        fogSettings.fogDensity = 0.008f;
        
        lightingSettings.ambientColor = new Color(0.7f, 0.8f, 1f);
        lightingSettings.directionalLightColor = new Color(0.9f, 0.95f, 1f);
        
        weatherEffect = WeatherEffect.Snow;
        environmentalEffects = new string[] { "snow_particles", "ice_crystals" };
    }
    
    void ApplySkyDefaults()
    {
        fogSettings.enableFog = true;
        fogSettings.fogColor = new Color(1f, 0.95f, 0.8f);
        fogSettings.fogDensity = 0.003f;
        
        lightingSettings.ambientColor = new Color(1f, 0.95f, 0.8f);
        lightingSettings.directionalLightColor = new Color(1f, 0.95f, 0.8f);
        lightingSettings.directionalLightIntensity = 2.5f;
        
        weatherEffect = WeatherEffect.Wind;
        environmentalEffects = new string[] { "cloud_particles", "light_rays" };
    }
    
    void ApplyKingdomDefaults()
    {
        fogSettings.enableFog = true;
        fogSettings.fogColor = new Color(0.8f, 0.7f, 0.5f);
        fogSettings.fogDensity = 0.005f;
        
        lightingSettings.ambientColor = new Color(0.9f, 0.8f, 0.6f);
        lightingSettings.directionalLightColor = new Color(1f, 0.95f, 0.8f);
        
        weatherEffect = WeatherEffect.None;
        environmentalEffects = new string[] { "dust_particles", "sunbeams" };
    }
    
    /// <summary>
    /// Create a copy of this arena data
    /// </summary>
    public ArenaData Clone()
    {
        ArenaData clone = CreateInstance<ArenaData>();
        
        // Copy basic info
        clone.arenaName = arenaName + " (Copy)";
        clone.arenaId = System.Guid.NewGuid().ToString();
        clone.description = description;
        clone.author = author;
        clone.version = version;
        
        // Copy biome settings
        clone.biomeType = biomeType;
        clone.customBiomePath = customBiomePath;
        
        // Copy layout
        clone.arenaSize = arenaSize;
        clone.playerSpawnPoint = playerSpawnPoint;
        clone.enemySpawnPoints = new List<Vector3>(enemySpawnPoints);
        clone.destructibleProps = new List<PropPlacement>(destructibleProps);
        clone.staticProps = new List<PropPlacement>(staticProps);
        clone.traps = new List<TrapPlacement>(traps);
        
        // Copy environmental effects
        clone.environmentalEffects = (string[])environmentalEffects.Clone();
        clone.fogSettings = fogSettings;
        clone.lightingSettings = lightingSettings;
        clone.weatherEffect = weatherEffect;
        clone.weatherIntensity = weatherIntensity;
        
        // Copy win conditions
        clone.winCondition = winCondition;
        clone.timeLimitSeconds = timeLimitSeconds;
        clone.killTarget = killTarget;
        clone.targetLocation = targetLocation;
        clone.targetRadius = targetRadius;
        
        // Copy modifiers
        clone.scoreMultiplier = scoreMultiplier;
        clone.difficultyMultiplier = difficultyMultiplier;
        clone.allowMagic = allowMagic;
        clone.allowHealing = allowHealing;
        clone.infiniteAmmo = infiniteAmmo;
        clone.respawnEnemies = respawnEnemies;
        clone.respawnDelay = respawnDelay;
        
        // Copy audio settings
        clone.backgroundMusic = backgroundMusic;
        clone.ambientSounds = ambientSounds;
        clone.musicVolume = musicVolume;
        clone.ambientVolume = ambientVolume;
        
        // Copy visual effects
        clone.particleEffects = (ParticleEffect[])particleEffects.Clone();
        clone.postProcessingProfile = postProcessingProfile;
        clone.enableDynamicLighting = enableDynamicLighting;
        clone.enableShadows = enableShadows;
        
        // Copy boundaries
        clone.boundaryType = boundaryType;
        clone.boundaryHeight = boundaryHeight;
        clone.boundaryMaterial = boundaryMaterial;
        
        return clone;
    }
}
