using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Systems
{
    /// <summary>
    /// Quest System for Cinder of Darkness.
    /// Manages main and side quests with progress tracking and categorization.
    /// </summary>
    public class QuestSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Quest Settings")]
        [SerializeField] private string questSaveKey = "CinderQuests";
        [SerializeField] private bool autoSaveProgress = true;
        [SerializeField] private float questUpdateInterval = 1f;

        [Header("UI References")]
        [SerializeField] private GameObject questLogUI;
        [SerializeField] private Transform questListContent;
        [SerializeField] private GameObject questItemPrefab;
        [SerializeField] private TextMeshProUGUI questDetailsTitle;
        [SerializeField] private TextMeshProUGUI questDetailsDescription;
        [SerializeField] private TextMeshProUGUI questDetailsProgress;
        [SerializeField] private Transform questObjectivesContent;
        [SerializeField] private GameObject questObjectivePrefab;

        [Header("Quest Categories")]
        [SerializeField] private Button mainQuestsButton;
        [SerializeField] private Button sideQuestsButton;
        [SerializeField] private Button completedQuestsButton;
        [SerializeField] private Dropdown regionFilterDropdown;

        [Header("Quest Database")]
        [SerializeField] private QuestData[] availableQuests;

        [Header("Audio")]
        [SerializeField] private AudioClip questStartSound;
        [SerializeField] private AudioClip questCompleteSound;
        [SerializeField] private AudioClip questUpdateSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Public Properties
        public static QuestSystem Instance { get; private set; }
        public QuestCategory CurrentCategory { get; private set; } = QuestCategory.Main;
        public string CurrentRegionFilter { get; private set; } = "All";
        public QuestData SelectedQuest { get; private set; }
        #endregion

        #region Private Fields
        private Dictionary<string, QuestState> questStates = new Dictionary<string, QuestState>();
        private List<QuestData> activeQuests = new List<QuestData>();
        private List<QuestData> completedQuests = new List<QuestData>();
        private Dictionary<string, GameObject> questListItems = new Dictionary<string, GameObject>();
        private float lastUpdateTime;
        #endregion

        #region Events
        public System.Action<QuestData> OnQuestStarted;
        public System.Action<QuestData> OnQuestCompleted;
        public System.Action<QuestData> OnQuestFailed;
        public System.Action<QuestData, QuestObjective> OnObjectiveCompleted;
        public System.Action<QuestData> OnQuestUpdated;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Quest System singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeQuestSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and load quest data.
        /// </summary>
        private void Start()
        {
            SetupComponents();
            LoadQuestStates();
            SetupEventListeners();
            RefreshQuestList();
        }

        /// <summary>
        /// Update quest progress and objectives.
        /// </summary>
        private void Update()
        {
            if (Time.time - lastUpdateTime >= questUpdateInterval)
            {
                UpdateActiveQuests();
                lastUpdateTime = Time.time;
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize quest system.
        /// </summary>
        private void InitializeQuestSystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Initialize quest states for all available quests
            foreach (var quest in availableQuests)
            {
                if (!questStates.ContainsKey(quest.id))
                {
                    questStates[quest.id] = new QuestState
                    {
                        questId = quest.id,
                        status = QuestStatus.NotStarted,
                        currentObjectiveIndex = 0,
                        objectiveProgress = new Dictionary<string, float>(),
                        startTime = System.DateTime.MinValue,
                        completionTime = System.DateTime.MinValue
                    };

                    // Initialize objective progress
                    foreach (var objective in quest.objectives)
                    {
                        questStates[quest.id].objectiveProgress[objective.id] = 0f;
                    }
                }
            }
        }

        /// <summary>
        /// Setup component references.
        /// </summary>
        private void SetupComponents()
        {
            // Hide quest log initially
            if (questLogUI != null)
            {
                questLogUI.SetActive(false);
            }
        }

        /// <summary>
        /// Setup UI event listeners.
        /// </summary>
        private void SetupEventListeners()
        {
            if (mainQuestsButton != null)
                mainQuestsButton.onClick.AddListener(() => SetQuestCategory(QuestCategory.Main));

            if (sideQuestsButton != null)
                sideQuestsButton.onClick.AddListener(() => SetQuestCategory(QuestCategory.Side));

            if (completedQuestsButton != null)
                completedQuestsButton.onClick.AddListener(() => SetQuestCategory(QuestCategory.Completed));

            if (regionFilterDropdown != null)
                regionFilterDropdown.onValueChanged.AddListener(OnRegionFilterChanged);

            // Setup region filter options
            SetupRegionFilter();
        }

        /// <summary>
        /// Setup region filter dropdown.
        /// </summary>
        private void SetupRegionFilter()
        {
            if (regionFilterDropdown == null) return;

            var regions = new HashSet<string> { "All" };
            foreach (var quest in availableQuests)
            {
                if (!string.IsNullOrEmpty(quest.region))
                {
                    regions.Add(quest.region);
                }
            }

            regionFilterDropdown.ClearOptions();
            regionFilterDropdown.AddOptions(regions.ToList());
        }
        #endregion

        #region Quest Management
        /// <summary>
        /// Start a quest by ID.
        /// </summary>
        /// <param name="questId">Quest ID to start</param>
        public void StartQuest(string questId)
        {
            var quest = GetQuestData(questId);
            if (quest == null)
            {
                Debug.LogWarning($"Quest not found: {questId}");
                return;
            }

            if (IsQuestActive(questId) || IsQuestCompleted(questId))
            {
                Debug.Log($"Quest already started or completed: {questId}");
                return;
            }

            // Check prerequisites
            if (!CheckQuestPrerequisites(quest))
            {
                Debug.Log($"Quest prerequisites not met: {questId}");
                return;
            }

            // Start the quest
            questStates[questId].status = QuestStatus.Active;
            questStates[questId].startTime = System.DateTime.Now;
            questStates[questId].currentObjectiveIndex = 0;

            activeQuests.Add(quest);

            // Save progress
            if (autoSaveProgress)
            {
                SaveQuestStates();
            }

            // Play sound
            PlayQuestSound(questStartSound);

            // Update UI
            RefreshQuestList();

            // Trigger events
            OnQuestStarted?.Invoke(quest);

            Debug.Log($"Quest started: {quest.title}");
        }

        /// <summary>
        /// Complete a quest by ID.
        /// </summary>
        /// <param name="questId">Quest ID to complete</param>
        public void CompleteQuest(string questId)
        {
            var quest = GetQuestData(questId);
            if (quest == null || !IsQuestActive(questId))
                return;

            // Update quest state
            questStates[questId].status = QuestStatus.Completed;
            questStates[questId].completionTime = System.DateTime.Now;

            // Move from active to completed
            activeQuests.Remove(quest);
            completedQuests.Add(quest);

            // Give rewards
            GiveQuestRewards(quest);

            // Save progress
            if (autoSaveProgress)
            {
                SaveQuestStates();
            }

            // Play sound
            PlayQuestSound(questCompleteSound);

            // Update UI
            RefreshQuestList();

            // Trigger events
            OnQuestCompleted?.Invoke(quest);

            Debug.Log($"Quest completed: {quest.title}");
        }

        /// <summary>
        /// Fail a quest by ID.
        /// </summary>
        /// <param name="questId">Quest ID to fail</param>
        public void FailQuest(string questId)
        {
            var quest = GetQuestData(questId);
            if (quest == null || !IsQuestActive(questId))
                return;

            // Update quest state
            questStates[questId].status = QuestStatus.Failed;

            // Remove from active quests
            activeQuests.Remove(quest);

            // Save progress
            if (autoSaveProgress)
            {
                SaveQuestStates();
            }

            // Update UI
            RefreshQuestList();

            // Trigger events
            OnQuestFailed?.Invoke(quest);

            Debug.Log($"Quest failed: {quest.title}");
        }

        /// <summary>
        /// Update objective progress for a quest.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <param name="objectiveId">Objective ID</param>
        /// <param name="progress">Progress value (0-1)</param>
        public void UpdateObjectiveProgress(string questId, string objectiveId, float progress)
        {
            if (!IsQuestActive(questId))
                return;

            var quest = GetQuestData(questId);
            if (quest == null)
                return;

            var objective = quest.objectives.FirstOrDefault(o => o.id == objectiveId);
            if (objective == null)
                return;

            // Update progress
            float oldProgress = questStates[questId].objectiveProgress[objectiveId];
            questStates[questId].objectiveProgress[objectiveId] = Mathf.Clamp01(progress);

            // Check if objective is completed
            if (progress >= 1f && oldProgress < 1f)
            {
                CompleteObjective(questId, objectiveId);
            }

            // Update UI
            if (SelectedQuest != null && SelectedQuest.id == questId)
            {
                UpdateQuestDetails();
            }

            // Save progress
            if (autoSaveProgress)
            {
                SaveQuestStates();
            }

            // Trigger events
            OnQuestUpdated?.Invoke(quest);
        }

        /// <summary>
        /// Complete a quest objective.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <param name="objectiveId">Objective ID</param>
        private void CompleteObjective(string questId, string objectiveId)
        {
            var quest = GetQuestData(questId);
            if (quest == null)
                return;

            var objective = quest.objectives.FirstOrDefault(o => o.id == objectiveId);
            if (objective == null)
                return;

            // Play sound
            PlayQuestSound(questUpdateSound);

            // Trigger events
            OnObjectiveCompleted?.Invoke(quest, objective);

            // Check if all objectives are completed
            bool allObjectivesCompleted = quest.objectives.All(o =>
                questStates[questId].objectiveProgress[o.id] >= 1f);

            if (allObjectivesCompleted)
            {
                CompleteQuest(questId);
            }
            else
            {
                // Move to next objective if sequential
                if (quest.isSequential)
                {
                    int currentIndex = questStates[questId].currentObjectiveIndex;
                    if (currentIndex < quest.objectives.Length - 1)
                    {
                        questStates[questId].currentObjectiveIndex = currentIndex + 1;
                    }
                }
            }

            Debug.Log($"Objective completed: {objective.description}");
        }

        /// <summary>
        /// Check if quest prerequisites are met.
        /// </summary>
        /// <param name="quest">Quest to check</param>
        /// <returns>True if prerequisites are met</returns>
        private bool CheckQuestPrerequisites(QuestData quest)
        {
            // Check level requirement
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null && playerStats.GetLevel() < quest.requiredLevel)
            {
                return false;
            }

            // Check prerequisite quests
            foreach (string prerequisiteId in quest.prerequisiteQuests)
            {
                if (!IsQuestCompleted(prerequisiteId))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Give quest rewards to player.
        /// </summary>
        /// <param name="quest">Completed quest</param>
        private void GiveQuestRewards(QuestData quest)
        {
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null)
            {
                // Give experience
                if (quest.experienceReward > 0)
                {
                    playerStats.AddExperience(quest.experienceReward);
                }

                // Give gold
                if (quest.goldReward > 0)
                {
                    playerStats.AddGold(quest.goldReward);
                }

                // Give items (would need inventory system)
                foreach (var item in quest.itemRewards)
                {
                    Debug.Log($"Received item: {item.name} x{item.quantity}");
                    // Add to inventory when inventory system is available
                }
            }
        }
        #endregion

        #region Quest Queries
        /// <summary>
        /// Check if quest is active.
        /// </summary>
        /// <param name="questId">Quest ID to check</param>
        /// <returns>True if quest is active</returns>
        public bool IsQuestActive(string questId)
        {
            return questStates.ContainsKey(questId) && questStates[questId].status == QuestStatus.Active;
        }

        /// <summary>
        /// Check if quest is completed.
        /// </summary>
        /// <param name="questId">Quest ID to check</param>
        /// <returns>True if quest is completed</returns>
        public bool IsQuestCompleted(string questId)
        {
            return questStates.ContainsKey(questId) && questStates[questId].status == QuestStatus.Completed;
        }

        /// <summary>
        /// Get quest data by ID.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <returns>Quest data or null</returns>
        public QuestData GetQuestData(string questId)
        {
            return availableQuests.FirstOrDefault(q => q.id == questId);
        }

        /// <summary>
        /// Get quest state by ID.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <returns>Quest state or null</returns>
        public QuestState GetQuestState(string questId)
        {
            return questStates.ContainsKey(questId) ? questStates[questId] : null;
        }

        /// <summary>
        /// Get all active quests.
        /// </summary>
        /// <returns>List of active quests</returns>
        public List<QuestData> GetActiveQuests()
        {
            return new List<QuestData>(activeQuests);
        }

        /// <summary>
        /// Get all completed quests.
        /// </summary>
        /// <returns>List of completed quests</returns>
        public List<QuestData> GetCompletedQuests()
        {
            return new List<QuestData>(completedQuests);
        }

        /// <summary>
        /// Get filtered quests based on current category and region.
        /// </summary>
        /// <returns>Filtered quest list</returns>
        private List<QuestData> GetFilteredQuests()
        {
            List<QuestData> quests = new List<QuestData>();

            switch (CurrentCategory)
            {
                case QuestCategory.Main:
                    quests = activeQuests.Where(q => q.category == QuestCategory.Main).ToList();
                    break;
                case QuestCategory.Side:
                    quests = activeQuests.Where(q => q.category == QuestCategory.Side).ToList();
                    break;
                case QuestCategory.Completed:
                    quests = completedQuests.ToList();
                    break;
            }

            // Apply region filter
            if (CurrentRegionFilter != "All")
            {
                quests = quests.Where(q => q.region == CurrentRegionFilter).ToList();
            }

            return quests;
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Set quest category filter.
        /// </summary>
        /// <param name="category">Quest category</param>
        public void SetQuestCategory(QuestCategory category)
        {
            CurrentCategory = category;
            RefreshQuestList();
        }

        /// <summary>
        /// Handle region filter change.
        /// </summary>
        /// <param name="index">Dropdown index</param>
        private void OnRegionFilterChanged(int index)
        {
            if (regionFilterDropdown != null && index >= 0 && index < regionFilterDropdown.options.Count)
            {
                CurrentRegionFilter = regionFilterDropdown.options[index].text;
                RefreshQuestList();
            }
        }

        /// <summary>
        /// Refresh quest list UI.
        /// </summary>
        private void RefreshQuestList()
        {
            if (questListContent == null || questItemPrefab == null) return;

            // Clear existing items
            foreach (Transform child in questListContent)
            {
                Destroy(child.gameObject);
            }
            questListItems.Clear();

            // Create items for filtered quests
            var filteredQuests = GetFilteredQuests();
            foreach (var quest in filteredQuests)
            {
                CreateQuestListItem(quest);
            }

            // Select first quest if none selected
            if (filteredQuests.Count > 0 && SelectedQuest == null)
            {
                SelectQuest(filteredQuests[0]);
            }
        }

        /// <summary>
        /// Create quest list item.
        /// </summary>
        /// <param name="quest">Quest data</param>
        private void CreateQuestListItem(QuestData quest)
        {
            GameObject item = Instantiate(questItemPrefab, questListContent);
            var itemUI = item.GetComponent<QuestListItemUI>();

            if (itemUI != null)
            {
                var questState = GetQuestState(quest.id);
                itemUI.Setup(quest, questState);
                itemUI.OnQuestSelected += SelectQuest;
            }

            questListItems[quest.id] = item;
        }

        /// <summary>
        /// Select a quest to show details.
        /// </summary>
        /// <param name="quest">Quest to select</param>
        public void SelectQuest(QuestData quest)
        {
            SelectedQuest = quest;
            UpdateQuestDetails();
        }

        /// <summary>
        /// Update quest details panel.
        /// </summary>
        private void UpdateQuestDetails()
        {
            if (SelectedQuest == null) return;

            var questState = GetQuestState(SelectedQuest.id);

            // Update title and description
            if (questDetailsTitle != null)
                questDetailsTitle.text = SelectedQuest.title;

            if (questDetailsDescription != null)
                questDetailsDescription.text = SelectedQuest.description;

            // Update progress
            if (questDetailsProgress != null && questState != null)
            {
                float progress = CalculateQuestProgress(SelectedQuest.id);
                questDetailsProgress.text = $"Progress: {progress * 100:F0}%";
            }

            // Update objectives
            UpdateQuestObjectives();
        }

        /// <summary>
        /// Update quest objectives display.
        /// </summary>
        private void UpdateQuestObjectives()
        {
            if (questObjectivesContent == null || questObjectivePrefab == null || SelectedQuest == null)
                return;

            // Clear existing objectives
            foreach (Transform child in questObjectivesContent)
            {
                Destroy(child.gameObject);
            }

            var questState = GetQuestState(SelectedQuest.id);
            if (questState == null) return;

            // Create objective items
            for (int i = 0; i < SelectedQuest.objectives.Length; i++)
            {
                var objective = SelectedQuest.objectives[i];

                // Skip future objectives in sequential quests
                if (SelectedQuest.isSequential && i > questState.currentObjectiveIndex)
                    continue;

                GameObject objectiveItem = Instantiate(questObjectivePrefab, questObjectivesContent);
                var objectiveUI = objectiveItem.GetComponent<QuestObjectiveUI>();

                if (objectiveUI != null)
                {
                    float progress = questState.objectiveProgress.ContainsKey(objective.id) ?
                                   questState.objectiveProgress[objective.id] : 0f;
                    bool isCompleted = progress >= 1f;
                    bool isCurrent = i == questState.currentObjectiveIndex;

                    objectiveUI.Setup(objective, progress, isCompleted, isCurrent);
                }
            }
        }

        /// <summary>
        /// Calculate overall quest progress.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <returns>Progress value (0-1)</returns>
        private float CalculateQuestProgress(string questId)
        {
            var quest = GetQuestData(questId);
            var questState = GetQuestState(questId);

            if (quest == null || questState == null || quest.objectives.Length == 0)
                return 0f;

            float totalProgress = 0f;
            foreach (var objective in quest.objectives)
            {
                if (questState.objectiveProgress.ContainsKey(objective.id))
                {
                    totalProgress += questState.objectiveProgress[objective.id];
                }
            }

            return totalProgress / quest.objectives.Length;
        }

        /// <summary>
        /// Show quest log UI.
        /// </summary>
        public void ShowQuestLog()
        {
            if (questLogUI != null)
            {
                questLogUI.SetActive(true);
                RefreshQuestList();
            }
        }

        /// <summary>
        /// Hide quest log UI.
        /// </summary>
        public void HideQuestLog()
        {
            if (questLogUI != null)
            {
                questLogUI.SetActive(false);
            }
        }

        /// <summary>
        /// Toggle quest log UI.
        /// </summary>
        public void ToggleQuestLog()
        {
            if (questLogUI != null)
            {
                if (questLogUI.activeSelf)
                {
                    HideQuestLog();
                }
                else
                {
                    ShowQuestLog();
                }
            }
        }
        #endregion

        #region Quest Updates
        /// <summary>
        /// Update all active quests.
        /// </summary>
        private void UpdateActiveQuests()
        {
            foreach (var quest in activeQuests.ToList())
            {
                UpdateQuestObjectives(quest);
            }
        }

        /// <summary>
        /// Update quest objectives based on game state.
        /// </summary>
        /// <param name="quest">Quest to update</param>
        private void UpdateQuestObjectives(QuestData quest)
        {
            var questState = GetQuestState(quest.id);
            if (questState == null) return;

            foreach (var objective in quest.objectives)
            {
                // Skip completed objectives
                if (questState.objectiveProgress[objective.id] >= 1f)
                    continue;

                // Skip future objectives in sequential quests
                if (quest.isSequential)
                {
                    int objectiveIndex = System.Array.IndexOf(quest.objectives, objective);
                    if (objectiveIndex > questState.currentObjectiveIndex)
                        continue;
                }

                // Update objective based on type
                UpdateObjectiveByType(quest.id, objective);
            }
        }

        /// <summary>
        /// Update objective progress based on objective type.
        /// </summary>
        /// <param name="questId">Quest ID</param>
        /// <param name="objective">Objective to update</param>
        private void UpdateObjectiveByType(string questId, QuestObjective objective)
        {
            switch (objective.type)
            {
                case QuestObjectiveType.Kill:
                    // Would check enemy kill count
                    break;
                case QuestObjectiveType.Collect:
                    // Would check inventory for items
                    break;
                case QuestObjectiveType.Interact:
                    // Would check interaction flags
                    break;
                case QuestObjectiveType.Reach:
                    // Would check player position
                    break;
                case QuestObjectiveType.Escort:
                    // Would check escort target status
                    break;
                case QuestObjectiveType.Defend:
                    // Would check defense objective status
                    break;
                case QuestObjectiveType.Custom:
                    // Would handle custom objective logic
                    break;
            }
        }
        #endregion

        #region Save/Load
        /// <summary>
        /// Save quest states to PlayerPrefs.
        /// </summary>
        private void SaveQuestStates()
        {
            try
            {
                var saveData = new QuestSaveData
                {
                    questStates = questStates,
                    activeQuestIds = activeQuests.Select(q => q.id).ToArray(),
                    completedQuestIds = completedQuests.Select(q => q.id).ToArray()
                };

                string json = JsonUtility.ToJson(saveData, true);
                PlayerPrefs.SetString(questSaveKey, json);
                PlayerPrefs.Save();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save quest states: {e.Message}");
            }
        }

        /// <summary>
        /// Load quest states from PlayerPrefs.
        /// </summary>
        private void LoadQuestStates()
        {
            try
            {
                string json = PlayerPrefs.GetString(questSaveKey, "");
                if (!string.IsNullOrEmpty(json))
                {
                    var saveData = JsonUtility.FromJson<QuestSaveData>(json);

                    questStates = saveData.questStates ?? new Dictionary<string, QuestState>();

                    // Rebuild active and completed quest lists
                    activeQuests.Clear();
                    completedQuests.Clear();

                    foreach (string questId in saveData.activeQuestIds ?? new string[0])
                    {
                        var quest = GetQuestData(questId);
                        if (quest != null)
                        {
                            activeQuests.Add(quest);
                        }
                    }

                    foreach (string questId in saveData.completedQuestIds ?? new string[0])
                    {
                        var quest = GetQuestData(questId);
                        if (quest != null)
                        {
                            completedQuests.Add(quest);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load quest states: {e.Message}");
            }
        }

        /// <summary>
        /// Reset all quest progress.
        /// </summary>
        public void ResetAllQuests()
        {
            questStates.Clear();
            activeQuests.Clear();
            completedQuests.Clear();

            InitializeQuestSystem();
            SaveQuestStates();
            RefreshQuestList();

            Debug.Log("All quests reset");
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play quest sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlayQuestSound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get quest statistics.
        /// </summary>
        /// <returns>Quest statistics</returns>
        public QuestStats GetQuestStats()
        {
            return new QuestStats
            {
                totalQuests = availableQuests.Length,
                activeQuests = activeQuests.Count,
                completedQuests = completedQuests.Count,
                mainQuestsCompleted = completedQuests.Count(q => q.category == QuestCategory.Main),
                sideQuestsCompleted = completedQuests.Count(q => q.category == QuestCategory.Side)
            };
        }

        /// <summary>
        /// Check if player can start quest.
        /// </summary>
        /// <param name="questId">Quest ID to check</param>
        /// <returns>True if quest can be started</returns>
        public bool CanStartQuest(string questId)
        {
            var quest = GetQuestData(questId);
            return quest != null && !IsQuestActive(questId) && !IsQuestCompleted(questId) && CheckQuestPrerequisites(quest);
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Quest data structure.
    /// </summary>
    [System.Serializable]
    public class QuestData
    {
        public string id;
        public string title;
        public string description;
        public string region;
        public QuestType type;
        public QuestCategory category;
        public int requiredLevel;
        public string[] prerequisiteQuests;
        public QuestObjective[] objectives;
        public bool isSequential;
        public int experienceReward;
        public int goldReward;
        public QuestItemReward[] itemRewards;
    }

    /// <summary>
    /// Quest objective structure.
    /// </summary>
    [System.Serializable]
    public class QuestObjective
    {
        public string id;
        public string description;
        public QuestObjectiveType type;
        public string targetId;
        public int targetCount;
        public bool isOptional;
    }

    /// <summary>
    /// Quest item reward structure.
    /// </summary>
    [System.Serializable]
    public class QuestItemReward
    {
        public string name;
        public int quantity;
    }

    /// <summary>
    /// Quest state for tracking progress.
    /// </summary>
    [System.Serializable]
    public class QuestState
    {
        public string questId;
        public QuestStatus status;
        public int currentObjectiveIndex;
        public Dictionary<string, float> objectiveProgress;
        public System.DateTime startTime;
        public System.DateTime completionTime;
    }

    /// <summary>
    /// Quest enumerations.
    /// </summary>
    public enum QuestType
    {
        Story,
        Side,
        Daily,
        Weekly,
        Challenge
    }

    public enum QuestCategory
    {
        Main,
        Side,
        Completed,
        Failed
    }

    public enum QuestStatus
    {
        NotStarted,
        Active,
        Completed,
        Failed
    }

    public enum QuestObjectiveType
    {
        Kill,
        Collect,
        Interact,
        Reach,
        Escort,
        Defend,
        Custom
    }

    /// <summary>
    /// Quest save data structure.
    /// </summary>
    [System.Serializable]
    public class QuestSaveData
    {
        public Dictionary<string, QuestState> questStates;
        public string[] activeQuestIds;
        public string[] completedQuestIds;
    }

    /// <summary>
    /// Quest statistics structure.
    /// </summary>
    [System.Serializable]
    public class QuestStats
    {
        public int totalQuests;
        public int activeQuests;
        public int completedQuests;
        public int mainQuestsCompleted;
        public int sideQuestsCompleted;
    }

    /// <summary>
    /// Quest list item UI component.
    /// </summary>
    public class QuestListItemUI : MonoBehaviour
    {
        [Header("UI Components")]
        public TextMeshProUGUI questTitle;
        public TextMeshProUGUI questType;
        public TextMeshProUGUI questRegion;
        public Slider progressBar;
        public TextMeshProUGUI progressText;
        public Button selectButton;

        private QuestData questData;
        public System.Action<QuestData> OnQuestSelected;

        /// <summary>
        /// Setup quest list item.
        /// </summary>
        /// <param name="quest">Quest data</param>
        /// <param name="questState">Quest state</param>
        public void Setup(QuestData quest, QuestState questState)
        {
            questData = quest;

            if (questTitle != null)
                questTitle.text = quest.title;

            if (questType != null)
                questType.text = quest.type.ToString();

            if (questRegion != null)
                questRegion.text = quest.region;

            if (questState != null)
            {
                float progress = CalculateProgress(quest, questState);

                if (progressBar != null)
                    progressBar.value = progress;

                if (progressText != null)
                    progressText.text = $"{progress * 100:F0}%";
            }

            if (selectButton != null)
                selectButton.onClick.AddListener(() => OnQuestSelected?.Invoke(questData));
        }

        /// <summary>
        /// Calculate quest progress.
        /// </summary>
        /// <param name="quest">Quest data</param>
        /// <param name="questState">Quest state</param>
        /// <returns>Progress value (0-1)</returns>
        private float CalculateProgress(QuestData quest, QuestState questState)
        {
            if (quest.objectives.Length == 0) return 0f;

            float totalProgress = 0f;
            foreach (var objective in quest.objectives)
            {
                if (questState.objectiveProgress.ContainsKey(objective.id))
                {
                    totalProgress += questState.objectiveProgress[objective.id];
                }
            }

            return totalProgress / quest.objectives.Length;
        }
    }

    /// <summary>
    /// Quest objective UI component.
    /// </summary>
    public class QuestObjectiveUI : MonoBehaviour
    {
        [Header("UI Components")]
        public TextMeshProUGUI objectiveDescription;
        public Slider progressBar;
        public TextMeshProUGUI progressText;
        public GameObject completedIndicator;
        public GameObject currentIndicator;

        /// <summary>
        /// Setup quest objective display.
        /// </summary>
        /// <param name="objective">Quest objective</param>
        /// <param name="progress">Current progress (0-1)</param>
        /// <param name="isCompleted">Whether objective is completed</param>
        /// <param name="isCurrent">Whether this is the current objective</param>
        public void Setup(QuestObjective objective, float progress, bool isCompleted, bool isCurrent)
        {
            if (objectiveDescription != null)
            {
                string description = objective.description;
                if (objective.targetCount > 1)
                {
                    int currentCount = Mathf.RoundToInt(progress * objective.targetCount);
                    description += $" ({currentCount}/{objective.targetCount})";
                }
                objectiveDescription.text = description;
            }

            if (progressBar != null)
            {
                progressBar.gameObject.SetActive(!isCompleted);
                progressBar.value = progress;
            }

            if (progressText != null)
            {
                progressText.gameObject.SetActive(!isCompleted);
                progressText.text = $"{progress * 100:F0}%";
            }

            if (completedIndicator != null)
                completedIndicator.SetActive(isCompleted);

            if (currentIndicator != null)
                currentIndicator.SetActive(isCurrent && !isCompleted);
        }
    }
    #endregion
}
