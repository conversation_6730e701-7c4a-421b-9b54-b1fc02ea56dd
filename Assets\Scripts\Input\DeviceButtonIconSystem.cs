using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using TMPro;

/// <summary>
/// Manages device-specific button icons and prompts for Cinder of Darkness
/// Automatically updates UI elements based on active input device
/// </summary>
public class DeviceButtonIconSystem : MonoBehaviour
{
    [Header("Input System")]
    public CinderInput cinderInput;
    public MultiInputControlSystem multiInputSystem;
    
    [Header("Icon Sets")]
    public DeviceIconSet keyboardMouseIcons;
    public DeviceIconSet xboxIcons;
    public DeviceIconSet playStationIcons;
    
    [Header("UI Elements")]
    public List<ButtonPrompt> buttonPrompts = new List<ButtonPrompt>();
    
    [Header("Auto-Update Settings")]
    public bool autoUpdateOnDeviceChange = true;
    public float updateDelay = 0.1f;
    
    // Private fields
    private string currentControlScheme = "";
    private Dictionary<string, Sprite> currentIconSet = new Dictionary<string, Sprite>();
    
    void Start()
    {
        InitializeIconSystem();
        UpdateCurrentIconSet();
    }
    
    void Update()
    {
        if (autoUpdateOnDeviceChange)
        {
            CheckForDeviceChange();
        }
    }
    
    void InitializeIconSystem()
    {
        // Get references if not assigned
        if (cinderInput == null)
        {
            var multiInput = FindObjectOfType<MultiInputControlSystem>();
            if (multiInput != null)
            {
                cinderInput = multiInput.cinderInput;
            }
        }
        
        if (multiInputSystem == null)
        {
            multiInputSystem = FindObjectOfType<MultiInputControlSystem>();
        }
        
        // Find all button prompts in scene if not manually assigned
        if (buttonPrompts.Count == 0)
        {
            var prompts = FindObjectsOfType<ButtonPrompt>();
            buttonPrompts.AddRange(prompts);
        }
        
        Debug.Log($"Device Button Icon System initialized with {buttonPrompts.Count} button prompts");
    }
    
    void CheckForDeviceChange()
    {
        if (cinderInput == null) return;
        
        string newScheme = cinderInput.GetCurrentControlScheme();
        if (newScheme != currentControlScheme)
        {
            currentControlScheme = newScheme;
            Invoke(nameof(UpdateCurrentIconSet), updateDelay);
        }
    }
    
    void UpdateCurrentIconSet()
    {
        currentIconSet.Clear();
        
        DeviceIconSet iconSet = GetCurrentDeviceIconSet();
        if (iconSet != null)
        {
            PopulateIconDictionary(iconSet);
            UpdateAllButtonPrompts();
            Debug.Log($"Updated button icons for device: {currentControlScheme}");
        }
    }
    
    DeviceIconSet GetCurrentDeviceIconSet()
    {
        if (cinderInput == null) return keyboardMouseIcons;
        
        if (cinderInput.IsUsingGamepad())
        {
            // Detect specific gamepad type
            var gamepad = Gamepad.current;
            if (gamepad != null)
            {
                string deviceName = gamepad.name.ToLower();
                if (deviceName.Contains("dualshock") || deviceName.Contains("dualsense") || deviceName.Contains("playstation"))
                {
                    return playStationIcons;
                }
                else if (deviceName.Contains("xbox") || deviceName.Contains("xinput"))
                {
                    return xboxIcons;
                }
            }
            
            // Default to Xbox icons for unknown gamepads
            return xboxIcons;
        }
        
        return keyboardMouseIcons;
    }
    
    void PopulateIconDictionary(DeviceIconSet iconSet)
    {
        // Gameplay actions
        currentIconSet["Move"] = iconSet.moveIcon;
        currentIconSet["Look"] = iconSet.lookIcon;
        currentIconSet["Jump"] = iconSet.jumpIcon;
        currentIconSet["Attack"] = iconSet.attackIcon;
        currentIconSet["HeavyAttack"] = iconSet.heavyAttackIcon;
        currentIconSet["Block"] = iconSet.blockIcon;
        currentIconSet["Dodge"] = iconSet.dodgeIcon;
        currentIconSet["Interact"] = iconSet.interactIcon;
        currentIconSet["SwitchWeapon"] = iconSet.switchWeaponIcon;
        currentIconSet["SwitchMagic"] = iconSet.switchMagicIcon;
        currentIconSet["Magic1"] = iconSet.magic1Icon;
        currentIconSet["Magic2"] = iconSet.magic2Icon;
        currentIconSet["Magic3"] = iconSet.magic3Icon;
        currentIconSet["Magic4"] = iconSet.magic4Icon;
        currentIconSet["OpenMenu"] = iconSet.menuIcon;
        
        // Menu actions
        currentIconSet["Navigate"] = iconSet.navigateIcon;
        currentIconSet["Confirm"] = iconSet.confirmIcon;
        currentIconSet["Cancel"] = iconSet.cancelIcon;
        currentIconSet["CloseMenu"] = iconSet.menuIcon;
    }
    
    void UpdateAllButtonPrompts()
    {
        foreach (var prompt in buttonPrompts)
        {
            if (prompt != null)
            {
                UpdateButtonPrompt(prompt);
            }
        }
    }
    
    void UpdateButtonPrompt(ButtonPrompt prompt)
    {
        if (currentIconSet.ContainsKey(prompt.actionName))
        {
            Sprite icon = currentIconSet[prompt.actionName];
            prompt.SetIcon(icon);
            
            // Update text if needed
            string displayText = GetDisplayTextForAction(prompt.actionName);
            prompt.SetText(displayText);
        }
    }
    
    string GetDisplayTextForAction(string actionName)
    {
        if (cinderInput == null) return actionName;
        
        // Get the actual binding display string
        InputAction action = FindActionByName(actionName);
        if (action != null)
        {
            return action.GetBindingDisplayString();
        }
        
        return actionName;
    }
    
    InputAction FindActionByName(string actionName)
    {
        if (cinderInput == null) return null;
        
        // Check gameplay actions
        var gameplayMap = cinderInput.Gameplay;
        var action = gameplayMap.FindAction(actionName);
        if (action != null) return action;
        
        // Check menu actions
        var menuMap = cinderInput.Menu;
        action = menuMap.FindAction(actionName);
        if (action != null) return action;
        
        return null;
    }
    
    // Public API
    public void RegisterButtonPrompt(ButtonPrompt prompt)
    {
        if (!buttonPrompts.Contains(prompt))
        {
            buttonPrompts.Add(prompt);
            UpdateButtonPrompt(prompt);
        }
    }
    
    public void UnregisterButtonPrompt(ButtonPrompt prompt)
    {
        buttonPrompts.Remove(prompt);
    }
    
    public Sprite GetIconForAction(string actionName)
    {
        if (currentIconSet.ContainsKey(actionName))
        {
            return currentIconSet[actionName];
        }
        return null;
    }
    
    public string GetCurrentDeviceName()
    {
        return currentControlScheme;
    }
    
    public bool IsUsingGamepad()
    {
        return cinderInput?.IsUsingGamepad() ?? false;
    }
    
    public void ForceUpdate()
    {
        UpdateCurrentIconSet();
    }
}

[System.Serializable]
public class DeviceIconSet
{
    [Header("Movement")]
    public Sprite moveIcon;
    public Sprite lookIcon;
    public Sprite jumpIcon;
    
    [Header("Combat")]
    public Sprite attackIcon;
    public Sprite heavyAttackIcon;
    public Sprite blockIcon;
    public Sprite dodgeIcon;
    
    [Header("Interaction")]
    public Sprite interactIcon;
    public Sprite switchWeaponIcon;
    public Sprite switchMagicIcon;
    
    [Header("Magic")]
    public Sprite magic1Icon;
    public Sprite magic2Icon;
    public Sprite magic3Icon;
    public Sprite magic4Icon;
    
    [Header("Menu")]
    public Sprite menuIcon;
    public Sprite navigateIcon;
    public Sprite confirmIcon;
    public Sprite cancelIcon;
}

[System.Serializable]
public class ButtonPrompt : MonoBehaviour
{
    [Header("Button Prompt Settings")]
    public string actionName;
    public Image iconImage;
    public TextMeshProUGUI promptText;
    public bool showText = true;
    public bool showIcon = true;
    
    [Header("Animation")]
    public bool animateOnUpdate = true;
    public float animationDuration = 0.2f;
    
    void Start()
    {
        // Auto-register with icon system
        var iconSystem = FindObjectOfType<DeviceButtonIconSystem>();
        if (iconSystem != null)
        {
            iconSystem.RegisterButtonPrompt(this);
        }
    }
    
    void OnDestroy()
    {
        // Auto-unregister from icon system
        var iconSystem = FindObjectOfType<DeviceButtonIconSystem>();
        if (iconSystem != null)
        {
            iconSystem.UnregisterButtonPrompt(this);
        }
    }
    
    public void SetIcon(Sprite icon)
    {
        if (iconImage != null && showIcon)
        {
            iconImage.sprite = icon;
            iconImage.gameObject.SetActive(icon != null);
            
            if (animateOnUpdate && icon != null)
            {
                AnimateIconChange();
            }
        }
    }
    
    public void SetText(string text)
    {
        if (promptText != null && showText)
        {
            promptText.text = text;
            promptText.gameObject.SetActive(!string.IsNullOrEmpty(text));
        }
    }
    
    void AnimateIconChange()
    {
        if (iconImage != null)
        {
            // Simple scale animation
            LeanTween.scale(iconImage.gameObject, Vector3.one * 1.1f, animationDuration * 0.5f)
                .setEase(LeanTweenType.easeOutQuad)
                .setOnComplete(() =>
                {
                    LeanTween.scale(iconImage.gameObject, Vector3.one, animationDuration * 0.5f)
                        .setEase(LeanTweenType.easeInQuad);
                });
        }
    }
    
    public void SetActionName(string newActionName)
    {
        actionName = newActionName;
        
        // Update immediately if icon system is available
        var iconSystem = FindObjectOfType<DeviceButtonIconSystem>();
        if (iconSystem != null)
        {
            Sprite icon = iconSystem.GetIconForAction(actionName);
            SetIcon(icon);
        }
    }
    
    public void SetVisibility(bool visible)
    {
        gameObject.SetActive(visible);
    }
    
    public void SetShowText(bool show)
    {
        showText = show;
        if (promptText != null)
        {
            promptText.gameObject.SetActive(show && !string.IsNullOrEmpty(promptText.text));
        }
    }
    
    public void SetShowIcon(bool show)
    {
        showIcon = show;
        if (iconImage != null)
        {
            iconImage.gameObject.SetActive(show && iconImage.sprite != null);
        }
    }
}
