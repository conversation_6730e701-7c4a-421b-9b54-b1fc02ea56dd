using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections;
using System.Collections.Generic;

public class BrutalCombatSystem : MonoBehaviour
{
    [Header("Gore Settings")]
    public bool brutalModeEnabled = true;
    public GoreLevel currentGoreLevel = GoreLevel.High;
    public float bloodSprayIntensity = 1f;
    public int maxBloodDecals = 50;

    [Header("Dismemberment System")]
    public bool dismembermentEnabled = true;
    public GameObject[] dismemberableParts; // Head, arms, legs
    public GameObject[] bloodSprayPrefabs;
    public GameObject[] severedLimbPrefabs;
    public Material bloodMaterial;

    [Header("Finisher System")]
    public FinisherMove[] availableFinishers;
    public float finisherRange = 2f;
    public KeyCode finisherKey = KeyCode.F;
    public AnimationCurve finisherCameraCurve;

    [Header("Blood Physics")]
    public PhysicMaterial bloodPhysics;
    public float bloodLifetime = 30f;
    public int bloodParticleCount = 100;
    public Color[] bloodColors = { Color.red, new Color(0.8f, 0f, 0f), new Color(0.6f, 0f, 0f) };

    [Header("Impact Effects")]
    public GameObject[] impactEffects;
    public AudioClip[] impactSounds;
    public float screenShakeIntensity = 0.5f;
    public float slowMotionDuration = 0.3f;

    [Header("Cultural Combat Styles")]
    public CombatStyle[] culturalStyles;

    private PlayerCombat playerCombat;
    private Camera playerCamera;
    private List<GameObject> activeBloodDecals = new List<GameObject>();
    private AudioSource combatAudioSource;
    private bool isPerformingFinisher = false;

    public enum GoreLevel
    {
        Off,
        Low,
        Medium,
        High,
        Extreme
    }

    [System.Serializable]
    public class FinisherMove
    {
        public string name;
        public CulturalOrigin origin;
        public AnimationClip animation;
        public float damage;
        public bool causesDismemberment;
        public DismembermentType dismembermentType;
        public AudioClip finisherSound;
        public GameObject specialEffect;
    }

    [System.Serializable]
    public class CombatStyle
    {
        public CulturalOrigin origin;
        public string styleName;
        public FinisherMove[] finishers;
        public float attackSpeedMultiplier;
        public float damageMultiplier;
        public AudioClip[] combatSounds;
    }

    public enum CulturalOrigin
    {
        Arab,
        Greek,
        Samurai,
        Crusader,
        Viking,
        Mixed
    }

    public enum DismembermentType
    {
        None,
        Head,
        LeftArm,
        RightArm,
        LeftLeg,
        RightLeg,
        Torso
    }

    void Start()
    {
        playerCombat = GetComponent<PlayerCombat>();
        playerCamera = Camera.main;
        combatAudioSource = GetComponent<AudioSource>();

        // Load gore settings from player preferences
        LoadGoreSettings();

        // Subscribe to combat events
        if (playerCombat != null)
        {
            playerCombat.OnAttackHit += OnAttackHit;
            playerCombat.OnEnemyKilled += OnEnemyKilled;
        }
    }

    void Update()
    {
        HandleFinisherInput();
        CleanupBloodDecals();
    }

    void LoadGoreSettings()
    {
        int goreLevel = PlayerPrefs.GetInt("GoreLevel", (int)GoreLevel.High);
        currentGoreLevel = (GoreLevel)goreLevel;

        brutalModeEnabled = PlayerPrefs.GetInt("BrutalMode", 1) == 1;
        dismembermentEnabled = PlayerPrefs.GetInt("Dismemberment", 1) == 1;

        ApplyGoreSettings();
    }

    void ApplyGoreSettings()
    {
        switch (currentGoreLevel)
        {
            case GoreLevel.Off:
                bloodSprayIntensity = 0f;
                dismembermentEnabled = false;
                break;
            case GoreLevel.Low:
                bloodSprayIntensity = 0.3f;
                dismembermentEnabled = false;
                break;
            case GoreLevel.Medium:
                bloodSprayIntensity = 0.6f;
                dismembermentEnabled = true;
                break;
            case GoreLevel.High:
                bloodSprayIntensity = 1f;
                dismembermentEnabled = true;
                break;
            case GoreLevel.Extreme:
                bloodSprayIntensity = 1.5f;
                dismembermentEnabled = true;
                break;
        }
    }

    void HandleFinisherInput()
    {
        // Use new Input System if available, fallback to old system
        bool finisherPressed = false;

        var keyboard = Keyboard.current;
        if (keyboard != null)
        {
            finisherPressed = keyboard.fKey.wasPressedThisFrame;
        }
        else
        {
            // Fallback to old input system
            finisherPressed = Input.GetKeyDown(finisherKey);
        }

        if (finisherPressed && !isPerformingFinisher)
        {
            TryPerformFinisher();
        }
    }

    void TryPerformFinisher()
    {
        // Find nearby stunned or low-health enemies
        Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, finisherRange);

        foreach (Collider enemy in nearbyEnemies)
        {
            EnemyHealth enemyHealth = enemy.GetComponent<EnemyHealth>();
            if (enemyHealth != null && CanPerformFinisher(enemyHealth))
            {
                StartCoroutine(ExecuteFinisher(enemy.gameObject, enemyHealth));
                break;
            }
        }
    }

    bool CanPerformFinisher(EnemyHealth enemyHealth)
    {
        return enemyHealth.GetHealthPercentage() <= 0.2f || enemyHealth.IsStunned();
    }

    IEnumerator ExecuteFinisher(GameObject target, EnemyHealth enemyHealth)
    {
        isPerformingFinisher = true;

        // Choose appropriate finisher based on cultural style
        FinisherMove finisher = GetRandomFinisher();

        // Disable player movement
        playerCombat.enabled = false;

        // Position player for finisher
        Vector3 finisherPosition = target.transform.position + (transform.position - target.transform.position).normalized * 1.5f;
        transform.position = finisherPosition;
        transform.LookAt(target.transform);

        // Camera work
        StartCoroutine(FinisherCameraWork(target.transform));

        // Play finisher animation
        Animator playerAnimator = GetComponent<Animator>();
        if (playerAnimator != null && finisher.animation != null)
        {
            playerAnimator.Play(finisher.animation.name);
        }

        // Wait for impact moment
        yield return new WaitForSeconds(0.8f);

        // Apply finisher effects
        ApplyFinisherEffects(target, finisher);

        // Kill enemy
        enemyHealth.TakeDamage(finisher.damage);

        // Wait for animation to complete
        yield return new WaitForSeconds(1.2f);

        // Re-enable player movement
        playerCombat.enabled = true;
        isPerformingFinisher = false;
    }

    IEnumerator FinisherCameraWork(Transform target)
    {
        Vector3 originalPosition = playerCamera.transform.position;
        Quaternion originalRotation = playerCamera.transform.rotation;

        // Move camera for dramatic angle
        Vector3 finisherCameraPos = target.position + Vector3.up * 2f + Vector3.back * 3f;
        Quaternion finisherCameraRot = Quaternion.LookRotation(target.position - finisherCameraPos);

        float duration = 2f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = finisherCameraCurve.Evaluate(elapsed / duration);

            playerCamera.transform.position = Vector3.Lerp(originalPosition, finisherCameraPos, progress);
            playerCamera.transform.rotation = Quaternion.Lerp(originalRotation, finisherCameraRot, progress);

            yield return null;
        }

        // Return camera to original position
        elapsed = 0f;
        while (elapsed < 1f)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / 1f;

            playerCamera.transform.position = Vector3.Lerp(finisherCameraPos, originalPosition, progress);
            playerCamera.transform.rotation = Quaternion.Lerp(finisherCameraRot, originalRotation, progress);

            yield return null;
        }
    }

    void ApplyFinisherEffects(GameObject target, FinisherMove finisher)
    {
        // Screen shake
        StartCoroutine(ScreenShake());

        // Slow motion
        StartCoroutine(SlowMotionEffect());

        // Blood spray
        CreateBloodSpray(target.transform.position, finisher.dismembermentType);

        // Dismemberment
        if (finisher.causesDismemberment && dismembermentEnabled)
        {
            PerformDismemberment(target, finisher.dismembermentType);
        }

        // Sound effects
        if (finisher.finisherSound != null && combatAudioSource != null)
        {
            combatAudioSource.PlayOneShot(finisher.finisherSound);
        }

        // Special effects
        if (finisher.specialEffect != null)
        {
            Instantiate(finisher.specialEffect, target.transform.position, Quaternion.identity);
        }
    }

    void OnAttackHit(Vector3 hitPosition, float damage, GameObject target)
    {
        if (currentGoreLevel == GoreLevel.Off) return;

        // Create blood spray on hit
        CreateBloodSpray(hitPosition, DismembermentType.None);

        // Impact effects
        CreateImpactEffect(hitPosition);

        // Screen shake for heavy hits
        if (damage > 30f)
        {
            StartCoroutine(ScreenShake(0.3f));
        }
    }

    void OnEnemyKilled(GameObject enemy)
    {
        // Create death blood pool
        CreateBloodPool(enemy.transform.position);

        // Ragdoll physics
        ApplyRagdollPhysics(enemy);
    }

    void CreateBloodSpray(Vector3 position, DismembermentType dismembermentType)
    {
        if (bloodSprayIntensity <= 0f) return;

        int particleCount = Mathf.RoundToInt(bloodParticleCount * bloodSprayIntensity);

        for (int i = 0; i < particleCount; i++)
        {
            GameObject bloodParticle = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            bloodParticle.transform.position = position + Random.insideUnitSphere * 0.5f;
            bloodParticle.transform.localScale = Vector3.one * Random.Range(0.05f, 0.15f);

            // Blood material
            Renderer bloodRenderer = bloodParticle.GetComponent<Renderer>();
            bloodRenderer.material = bloodMaterial;
            bloodRenderer.material.color = bloodColors[Random.Range(0, bloodColors.Length)];

            // Physics
            Rigidbody bloodRb = bloodParticle.AddComponent<Rigidbody>();
            bloodRb.material = bloodPhysics;
            bloodRb.AddForce(Random.insideUnitSphere * Random.Range(5f, 15f), ForceMode.Impulse);

            // Cleanup
            Destroy(bloodParticle, bloodLifetime);
            activeBloodDecals.Add(bloodParticle);
        }
    }

    void CreateBloodPool(Vector3 position)
    {
        GameObject bloodPool = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        bloodPool.transform.position = position;
        bloodPool.transform.localScale = new Vector3(2f, 0.01f, 2f);

        Renderer poolRenderer = bloodPool.GetComponent<Renderer>();
        poolRenderer.material = bloodMaterial;
        poolRenderer.material.color = new Color(0.5f, 0f, 0f, 0.8f);

        Destroy(bloodPool.GetComponent<Collider>());
        Destroy(bloodPool, bloodLifetime * 2f);
        activeBloodDecals.Add(bloodPool);
    }

    void PerformDismemberment(GameObject target, DismembermentType type)
    {
        if (type == DismembermentType.None) return;

        // Find the appropriate body part
        Transform bodyPart = FindBodyPart(target, type);
        if (bodyPart == null) return;

        // Create severed limb
        GameObject severedLimb = Instantiate(GetSeveredLimbPrefab(type), bodyPart.position, bodyPart.rotation);

        // Add physics to severed limb
        Rigidbody limbRb = severedLimb.AddComponent<Rigidbody>();
        limbRb.AddForce(Random.insideUnitSphere * 5f, ForceMode.Impulse);
        limbRb.AddTorque(Random.insideUnitSphere * 10f, ForceMode.Impulse);

        // Hide original body part
        bodyPart.gameObject.SetActive(false);

        // Extra blood spray for dismemberment
        CreateBloodSpray(bodyPart.position, type);

        Debug.Log($"Dismembered: {type}");
    }

    Transform FindBodyPart(GameObject target, DismembermentType type)
    {
        // This would search for specific body part transforms in a full implementation
        // For prototype, return a random child transform
        if (target.transform.childCount > 0)
        {
            return target.transform.GetChild(Random.Range(0, target.transform.childCount));
        }
        return target.transform;
    }

    GameObject GetSeveredLimbPrefab(DismembermentType type)
    {
        if (severedLimbPrefabs.Length > 0)
        {
            return severedLimbPrefabs[Random.Range(0, severedLimbPrefabs.Length)];
        }
        return null;
    }

    void CreateImpactEffect(Vector3 position)
    {
        if (impactEffects.Length > 0)
        {
            GameObject effect = Instantiate(impactEffects[Random.Range(0, impactEffects.Length)], position, Quaternion.identity);
            Destroy(effect, 2f);
        }

        if (impactSounds.Length > 0 && combatAudioSource != null)
        {
            combatAudioSource.PlayOneShot(impactSounds[Random.Range(0, impactSounds.Length)]);
        }
    }

    void ApplyRagdollPhysics(GameObject enemy)
    {
        // Enable ragdoll physics on death
        Rigidbody[] rigidbodies = enemy.GetComponentsInChildren<Rigidbody>();
        foreach (Rigidbody rb in rigidbodies)
        {
            rb.isKinematic = false;
            rb.AddForce(Random.insideUnitSphere * 3f, ForceMode.Impulse);
        }

        // Disable animator
        Animator enemyAnimator = enemy.GetComponent<Animator>();
        if (enemyAnimator != null)
        {
            enemyAnimator.enabled = false;
        }
    }

    IEnumerator ScreenShake(float intensity = -1f)
    {
        if (intensity < 0f) intensity = screenShakeIntensity;

        Vector3 originalPosition = playerCamera.transform.localPosition;
        float duration = 0.3f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            float currentIntensity = intensity * (1f - progress);

            Vector3 randomOffset = Random.insideUnitSphere * currentIntensity;
            playerCamera.transform.localPosition = originalPosition + randomOffset;

            yield return null;
        }

        playerCamera.transform.localPosition = originalPosition;
    }

    IEnumerator SlowMotionEffect()
    {
        float originalTimeScale = Time.timeScale;
        Time.timeScale = 0.3f;

        yield return new WaitForSecondsRealtime(slowMotionDuration);

        Time.timeScale = originalTimeScale;
    }

    void CleanupBloodDecals()
    {
        if (activeBloodDecals.Count > maxBloodDecals)
        {
            GameObject oldestDecal = activeBloodDecals[0];
            activeBloodDecals.RemoveAt(0);
            if (oldestDecal != null)
            {
                Destroy(oldestDecal);
            }
        }
    }

    FinisherMove GetRandomFinisher()
    {
        if (availableFinishers.Length > 0)
        {
            return availableFinishers[Random.Range(0, availableFinishers.Length)];
        }

        // Default finisher
        return new FinisherMove
        {
            name = "Basic Execution",
            damage = 1000f,
            causesDismemberment = true,
            dismembermentType = DismembermentType.Head
        };
    }

    public void SetGoreLevel(GoreLevel level)
    {
        currentGoreLevel = level;
        PlayerPrefs.SetInt("GoreLevel", (int)level);
        ApplyGoreSettings();
    }

    public void ToggleBrutalMode(bool enabled)
    {
        brutalModeEnabled = enabled;
        PlayerPrefs.SetInt("BrutalMode", enabled ? 1 : 0);
    }
}
