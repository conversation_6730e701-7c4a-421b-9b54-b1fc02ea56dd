using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class EconomySystem : MonoBehaviour
{
    [Header("Currency")]
    public int goldCoins = 100;
    public int silverCoins = 50;
    public float goldToSilverRate = 10f; // 1 gold = 10 silver

    [Header("Inventory")]
    public List<InventoryItem> inventory = new List<InventoryItem>();
    public int maxInventorySlots = 50;

    [Header("Storage")]
    public List<StorageLocation> storageLocations = new List<StorageLocation>();

    [Header("Rare Items")]
    public RareItem[] legendaryItems;
    public RareItem[] mythicalItems;

    [Header("Trading")]
    public float reputationPriceModifier = 1f;
    public float moralityPriceModifier = 1f;

    private PhilosophicalMoralitySystem moralitySystem;
    private HostilitySystem hostilitySystem;

    [System.Serializable]
    public class InventoryItem
    {
        public string itemName;
        public ItemType type;
        public ItemRarity rarity;
        public int quantity;
        public float baseValue;
        public string description;
        public GameObject itemPrefab;
        public Sprite itemIcon;
        public bool isStackable;
        public bool isQuestItem;

        public enum ItemType
        {
            Weapon,
            Armor,
            Consumable,
            Material,
            Valuable,
            QuestItem,
            Magical,
            Rare
        }

        public enum ItemRarity
        {
            Common,
            Uncommon,
            Rare,
            Epic,
            Legendary,
            Mythical
        }
    }

    [System.Serializable]
    public class StorageLocation
    {
        public string locationName;
        public Vector3 position;
        public List<InventoryItem> storedItems;
        public int maxStorageSlots;
        public bool requiresKey;
        public string keyItemName;
    }

    [System.Serializable]
    public class RareItem
    {
        public string itemName;
        public string loreDescription;
        public RareItemType type;
        public float powerLevel;
        public string[] specialEffects;
        public UnlockCondition unlockCondition;
        public GameObject itemModel;
        public AudioClip discoverySound;

        public enum RareItemType
        {
            AshenMirror,
            Dawnblade,
            SinChain,
            SagesClay,
            ShardOfAmnesia,
            CursedArtifact,
            BlessedRelic,
            AncientWeapon
        }
    }

    [System.Serializable]
    public class UnlockCondition
    {
        public ConditionType type;
        public float requiredValue;
        public string requiredEvent;
        public PhilosophicalMoralitySystem.MoralPath requiredPath;

        public enum ConditionType
        {
            KillCount,
            MoralAlignment,
            StoryProgress,
            SpecificEvent,
            TimeOfDay,
            Location
        }
    }

    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        hostilitySystem = GetComponent<HostilitySystem>();

        InitializeEconomy();
        InitializeRareItems();
    }

    void Update()
    {
        UpdatePriceModifiers();
        CheckRareItemUnlocks();
    }

    void InitializeEconomy()
    {
        // Initialize starting inventory
        AddItem("Traveler's Cloak", InventoryItem.ItemType.Armor, InventoryItem.ItemRarity.Common, 1, 25f);
        AddItem("Iron Sword", InventoryItem.ItemType.Weapon, InventoryItem.ItemRarity.Common, 1, 50f);
        AddItem("Bread", InventoryItem.ItemType.Consumable, InventoryItem.ItemRarity.Common, 3, 5f);

        Debug.Log($"Economy initialized - {goldCoins} gold, {silverCoins} silver");
    }

    void InitializeRareItems()
    {
        // Ashen Mirror - shows alternate future self
        RareItem ashenMirror = new RareItem
        {
            itemName = "Ashen Mirror",
            loreDescription = "A mirror forged from the ashes of fallen heroes. It shows not who you are, but who you could become.",
            type = RareItem.RareItemType.AshenMirror,
            powerLevel = 100f,
            specialEffects = new string[] { "Shows alternate future self", "Reveals consequences of choices", "Grants wisdom +50" },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.MoralAlignment,
                requiredValue = 50f,
                requiredPath = PhilosophicalMoralitySystem.MoralPath.Conflicted
            }
        };

        // Dawnblade - activates only during sunrise
        RareItem dawnblade = new RareItem
        {
            itemName = "Dawnblade",
            loreDescription = "A sword that drinks the light of dawn. Its power waxes and wanes with the sun's journey across the sky.",
            type = RareItem.RareItemType.Dawnblade,
            powerLevel = 150f,
            specialEffects = new string[] { "Active only at sunrise", "Damage +200% during dawn", "Blinds enemies with light" },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.TimeOfDay,
                requiredValue = 6f, // 6 AM
                requiredPath = PhilosophicalMoralitySystem.MoralPath.Good
            }
        };

        // Sin Chain - grows stronger with sins
        RareItem sinChain = new RareItem
        {
            itemName = "Sin Chain",
            loreDescription = "A whip forged from the guilt of the damned. Each sin committed adds another link to its length.",
            type = RareItem.RareItemType.SinChain,
            powerLevel = 75f,
            specialEffects = new string[] { "Grows stronger with sins", "Damage scales with evil acts", "Binds enemies with guilt" },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.KillCount,
                requiredValue = 50f,
                requiredPath = PhilosophicalMoralitySystem.MoralPath.Evil
            }
        };

        // Sage's Clay - crafting material
        RareItem sagesClay = new RareItem
        {
            itemName = "Sage's Clay",
            loreDescription = "Clay blessed by ancient sages. It molds itself to the user's spiritual needs.",
            type = RareItem.RareItemType.SagesClay,
            powerLevel = 50f,
            specialEffects = new string[] { "Crafts earth-bound armor", "Creates protective runes", "Adapts to user's alignment" },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.SpecificEvent,
                requiredEvent = "MeditationMastery"
            }
        };

        // Shard of Amnesia - forget one major choice
        RareItem shardOfAmnesia = new RareItem
        {
            itemName = "Shard of Amnesia",
            loreDescription = "A crystalline fragment that holds the power to erase memories. Use wisely - some things are better forgotten.",
            type = RareItem.RareItemType.ShardOfAmnesia,
            powerLevel = 200f,
            specialEffects = new string[] { "Forget one major choice", "Reset moral alignment partially", "Removes trauma memories" },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.StoryProgress,
                requiredValue = 0.75f
            }
        };

        legendaryItems = new RareItem[] { ashenMirror, dawnblade, sinChain };
        mythicalItems = new RareItem[] { sagesClay, shardOfAmnesia };
    }

    void UpdatePriceModifiers()
    {
        // Reputation affects prices
        if (hostilitySystem != null)
        {
            float reputation = hostilitySystem.GetGlobalReputation();
            reputationPriceModifier = Mathf.Lerp(1.5f, 0.7f, (reputation + 100f) / 200f);
        }

        // Morality affects prices
        if (moralitySystem != null)
        {
            switch (moralitySystem.GetCurrentPath())
            {
                case PhilosophicalMoralitySystem.MoralPath.Good:
                    moralityPriceModifier = 0.8f; // Good discount
                    break;
                case PhilosophicalMoralitySystem.MoralPath.Evil:
                    moralityPriceModifier = 1.3f; // Evil markup
                    break;
                default:
                    moralityPriceModifier = 1f;
                    break;
            }
        }
    }

    void CheckRareItemUnlocks()
    {
        // Check legendary items
        foreach (RareItem item in legendaryItems)
        {
            if (!HasItem(item.itemName) && IsUnlockConditionMet(item.unlockCondition))
            {
                UnlockRareItem(item);
            }
        }

        // Check mythical items
        foreach (RareItem item in mythicalItems)
        {
            if (!HasItem(item.itemName) && IsUnlockConditionMet(item.unlockCondition))
            {
                UnlockRareItem(item);
            }
        }
    }

    bool IsUnlockConditionMet(UnlockCondition condition)
    {
        switch (condition.type)
        {
            case UnlockCondition.ConditionType.KillCount:
                DeathConsequenceSystem deathSystem = GetComponent<DeathConsequenceSystem>();
                return deathSystem != null && deathSystem.GetTotalKills() >= condition.requiredValue;

            case UnlockCondition.ConditionType.MoralAlignment:
                return moralitySystem != null && moralitySystem.GetCurrentPath() == condition.requiredPath;

            case UnlockCondition.ConditionType.StoryProgress:
                GameManager gameManager = GameManager.Instance;
                return gameManager != null && gameManager.currentStoryProgress >= condition.requiredValue;

            case UnlockCondition.ConditionType.TimeOfDay:
                float currentHour = System.DateTime.Now.Hour;
                return Mathf.Abs(currentHour - condition.requiredValue) < 1f;

            case UnlockCondition.ConditionType.SpecificEvent:
                // Check if specific event has occurred
                return GameManager.Instance != null && GameManager.Instance.IsQuestCompleted(condition.requiredEvent);

            default:
                return false;
        }
    }

    void UnlockRareItem(RareItem item)
    {
        // Add item to inventory
        AddRareItem(item);

        // Play discovery effects
        if (item.discoverySound != null)
        {
            AudioSource.PlayClipAtPoint(item.discoverySound, transform.position);
        }

        // Show discovery message
        ShowEconomyMessage($"RARE ITEM DISCOVERED: {item.itemName}");
        ShowEconomyMessage(item.loreDescription);

        Debug.Log($"Rare item unlocked: {item.itemName}");
    }

    public bool AddItem(string itemName, InventoryItem.ItemType type, InventoryItem.ItemRarity rarity, int quantity, float value)
    {
        if (inventory.Count >= maxInventorySlots)
        {
            ShowEconomyMessage("Inventory full! Cannot add more items.");
            return false;
        }

        // Check if item already exists and is stackable
        InventoryItem existingItem = inventory.Find(item => item.itemName == itemName && item.isStackable);
        if (existingItem != null)
        {
            existingItem.quantity += quantity;
            return true;
        }

        // Add new item
        InventoryItem newItem = new InventoryItem
        {
            itemName = itemName,
            type = type,
            rarity = rarity,
            quantity = quantity,
            baseValue = value,
            isStackable = type == InventoryItem.ItemType.Consumable || type == InventoryItem.ItemType.Material
        };

        inventory.Add(newItem);
        return true;
    }

    public void AddRareItem(RareItem rareItem)
    {
        InventoryItem item = new InventoryItem
        {
            itemName = rareItem.itemName,
            type = InventoryItem.ItemType.Rare,
            rarity = InventoryItem.ItemRarity.Legendary,
            quantity = 1,
            baseValue = rareItem.powerLevel * 10f,
            description = rareItem.loreDescription,
            isStackable = false,
            isQuestItem = true
        };

        inventory.Add(item);
    }

    public bool RemoveItem(string itemName, int quantity = 1)
    {
        InventoryItem item = inventory.Find(i => i.itemName == itemName);
        if (item == null || item.quantity < quantity)
        {
            return false;
        }

        item.quantity -= quantity;
        if (item.quantity <= 0)
        {
            inventory.Remove(item);
        }

        return true;
    }

    public bool HasItem(string itemName)
    {
        return inventory.Exists(item => item.itemName == itemName);
    }

    public float CalculateItemPrice(InventoryItem item, bool isSelling = false)
    {
        float basePrice = item.baseValue;

        // Apply rarity multiplier
        float rarityMultiplier = 1f;
        switch (item.rarity)
        {
            case InventoryItem.ItemRarity.Uncommon: rarityMultiplier = 1.5f; break;
            case InventoryItem.ItemRarity.Rare: rarityMultiplier = 3f; break;
            case InventoryItem.ItemRarity.Epic: rarityMultiplier = 6f; break;
            case InventoryItem.ItemRarity.Legendary: rarityMultiplier = 15f; break;
            case InventoryItem.ItemRarity.Mythical: rarityMultiplier = 50f; break;
        }

        float finalPrice = basePrice * rarityMultiplier;

        if (!isSelling)
        {
            // Buying - apply reputation and morality modifiers
            finalPrice *= reputationPriceModifier * moralityPriceModifier;
        }
        else
        {
            // Selling - get less money
            finalPrice *= 0.6f;
        }

        return finalPrice;
    }

    public bool CanAfford(float price)
    {
        float totalWealth = goldCoins + (silverCoins / goldToSilverRate);
        return totalWealth >= price;
    }

    public bool SpendMoney(float amount)
    {
        if (!CanAfford(amount))
        {
            return false;
        }

        // Convert to silver for easier calculation
        float totalSilver = (goldCoins * goldToSilverRate) + silverCoins;
        float amountInSilver = amount * goldToSilverRate;

        totalSilver -= amountInSilver;

        // Convert back to gold and silver
        goldCoins = Mathf.FloorToInt(totalSilver / goldToSilverRate);
        silverCoins = Mathf.FloorToInt(totalSilver % goldToSilverRate);

        return true;
    }

    public void EarnMoney(float amount)
    {
        float amountInSilver = amount * goldToSilverRate;
        silverCoins += Mathf.FloorToInt(amountInSilver);

        // Convert excess silver to gold
        if (silverCoins >= goldToSilverRate)
        {
            int newGold = Mathf.FloorToInt(silverCoins / goldToSilverRate);
            goldCoins += newGold;
            silverCoins -= Mathf.FloorToInt(newGold * goldToSilverRate);
        }
    }

    public void StoreItem(string itemName, string locationName)
    {
        StorageLocation location = storageLocations.Find(loc => loc.locationName == locationName);
        if (location == null)
        {
            ShowEconomyMessage($"Storage location '{locationName}' not found.");
            return;
        }

        InventoryItem item = inventory.Find(i => i.itemName == itemName);
        if (item == null)
        {
            ShowEconomyMessage($"Item '{itemName}' not found in inventory.");
            return;
        }

        if (location.storedItems.Count >= location.maxStorageSlots)
        {
            ShowEconomyMessage($"Storage location '{locationName}' is full.");
            return;
        }

        // Move item to storage
        location.storedItems.Add(item);
        inventory.Remove(item);

        ShowEconomyMessage($"Stored {itemName} at {locationName}.");
    }

    public void SetTitlePriceModifier(float modifier)
    {
        moralityPriceModifier = modifier;
        Debug.Log($"Title price modifier set to: {modifier}");
    }

    void ShowEconomyMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }

        Debug.Log($"Economy: {message}");
    }

    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    // Getters
    public int GetGoldCoins() => goldCoins;
    public int GetSilverCoins() => silverCoins;
    public float GetTotalWealth() => goldCoins + (silverCoins / goldToSilverRate);
    public List<InventoryItem> GetInventory() => inventory;
    public int GetInventoryCount() => inventory.Count;
    public bool IsInventoryFull() => inventory.Count >= maxInventorySlots;
}
