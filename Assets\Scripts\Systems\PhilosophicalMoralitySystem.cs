using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class PhilosophicalMoralitySystem : MonoBehaviour
{
    [Header("Core Philosophy")]
    public MoralPath currentPath = MoralPath.Neutral;
    public float evilProgression = 0f; // 0 = broken/weak, 100 = barbaric/feared
    public float goodProgression = 0f; // 0 = basic good, 100 = legendary protector
    public bool maintainsCoreValues = true;

    [Head<PERSON>("Evil Path Progression")]
    public EvilStage currentEvilStage = EvilStage.None;
    public float fearLevel = 0f; // How much others fear The Cinderborn
    public float barbarismLevel = 0f; // How savage The Cinderborn has become

    [Header("Good Path Progression")]
    public GoodStage currentGoodStage = GoodStage.None;
    public float admirationLevel = 0f; // How much others admire The Cinderborn
    public float protectorLevel = 0f; // How protective The Cinderborn has become

    [Header("Appearance-Based Reactions")]
    public ClothingType currentClothing = ClothingType.Ragged;
    public ArmorType currentArmor = ArmorType.None;
    public WeaponQuality currentWeaponQuality = WeaponQuality.Basic;

    [Header("Core Values (Unchanging)")]
    public string[] coreValues = {
        "I am the ash between fire and void",
        "Strength comes from within, not from others' fear or love",
        "Every choice defines who I become",
        "I walk my own path, regardless of others' expectations",
        "The weak deserve protection, the wicked deserve justice"
    };

    private PlayerStats playerStats;
    private HostilitySystem hostilitySystem;
    private PsychologicalSystem psycheSystem;
    private GameManager gameManager;

    public enum MoralPath
    {
        Neutral,
        Evil,
        Good,
        Conflicted
    }

    public enum EvilStage
    {
        None,
        Broken,      // Early evil - appears weak and desperate
        Ruthless,    // Mid evil - cold and calculating
        Barbaric,    // Late evil - savage and feared by all
        Demonic      // End evil - even demons fear The Cinderborn
    }

    public enum GoodStage
    {
        None,
        Compassionate, // Early good - shows mercy and kindness
        Protective,    // Mid good - actively protects innocents
        Heroic,        // Late good - legendary protector
        Saintly        // End good - revered as a living saint
    }

    public enum ClothingType
    {
        Ragged,      // Beggar/mercenary appearance
        Common,      // Average traveler
        Fine,        // Well-dressed merchant
        Noble,       // Aristocratic clothing
        Royal        // Regal attire
    }

    public enum ArmorType
    {
        None,
        Leather,     // Basic protection
        Chain,       // Moderate protection
        Plate,       // Heavy protection
        Legendary    // Mythical armor
    }

    public enum WeaponQuality
    {
        Basic,
        Fine,
        Masterwork,
        Legendary,
        Mythical
    }

    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        hostilitySystem = GetComponent<HostilitySystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        gameManager = GameManager.Instance;

        InitializeMoralitySystem();
    }

    void Update()
    {
        UpdateMoralProgression();
        UpdateAppearanceReactions();
        CheckForStageTransitions();
    }

    void InitializeMoralitySystem()
    {
        // The Cinderborn starts neutral but with strong core values
        currentPath = MoralPath.Neutral;
        maintainsCoreValues = true;

        Debug.Log("The Cinderborn's philosophical morality system initialized");
        Debug.Log("Core philosophy: Evil ≠ Weakness, Good ≠ Nobility");
    }

    void UpdateMoralProgression()
    {
        // Evil progression: broken → barbaric
        if (currentPath == MoralPath.Evil)
        {
            evilProgression = Mathf.Clamp01(evilProgression + Time.deltaTime * 0.1f);

            // Evil makes you feared, not loved
            fearLevel = Mathf.Lerp(10f, 100f, evilProgression);
            barbarismLevel = Mathf.Lerp(0f, 100f, evilProgression);

            // Update evil stage
            UpdateEvilStage();
        }

        // Good progression: compassionate → saintly
        if (currentPath == MoralPath.Good)
        {
            goodProgression = Mathf.Clamp01(goodProgression + Time.deltaTime * 0.1f);

            // Good makes you admired and trusted
            admirationLevel = Mathf.Lerp(10f, 100f, goodProgression);
            protectorLevel = Mathf.Lerp(0f, 100f, goodProgression);

            // Update good stage
            UpdateGoodStage();
        }
    }

    void UpdateEvilStage()
    {
        EvilStage newStage = currentEvilStage;

        if (evilProgression < 0.25f)
            newStage = EvilStage.Broken;
        else if (evilProgression < 0.5f)
            newStage = EvilStage.Ruthless;
        else if (evilProgression < 0.75f)
            newStage = EvilStage.Barbaric;
        else
            newStage = EvilStage.Demonic;

        if (newStage != currentEvilStage)
        {
            TransitionToEvilStage(newStage);
        }
    }

    void UpdateGoodStage()
    {
        GoodStage newStage = currentGoodStage;

        if (goodProgression < 0.25f)
            newStage = GoodStage.Compassionate;
        else if (goodProgression < 0.5f)
            newStage = GoodStage.Protective;
        else if (goodProgression < 0.75f)
            newStage = GoodStage.Heroic;
        else
            newStage = GoodStage.Saintly;

        if (newStage != currentGoodStage)
        {
            TransitionToGoodStage(newStage);
        }
    }

    void TransitionToEvilStage(EvilStage newStage)
    {
        EvilStage oldStage = currentEvilStage;
        currentEvilStage = newStage;

        Debug.Log($"Evil progression: {oldStage} → {newStage}");

        switch (newStage)
        {
            case EvilStage.Broken:
                ShowMoralMessage("The Cinderborn appears broken and desperate... but this is only the beginning.");
                break;
            case EvilStage.Ruthless:
                ShowMoralMessage("The weakness fades. The Cinderborn becomes cold, calculating, ruthless.");
                break;
            case EvilStage.Barbaric:
                ShowMoralMessage("Savagery takes hold. The Cinderborn is feared by all who witness their brutality.");
                break;
            case EvilStage.Demonic:
                ShowMoralMessage("Even demons whisper The Cinderborn's name in fear. Evil incarnate walks the earth.");
                break;
        }

        UpdateCharacterAppearance();
    }

    void TransitionToGoodStage(GoodStage newStage)
    {
        GoodStage oldStage = currentGoodStage;
        currentGoodStage = newStage;

        Debug.Log($"Good progression: {oldStage} → {newStage}");

        switch (newStage)
        {
            case GoodStage.Compassionate:
                ShowMoralMessage("The Cinderborn chooses mercy. Compassion becomes their strength.");
                break;
            case GoodStage.Protective:
                ShowMoralMessage("The innocent find shelter in The Cinderborn's shadow. A protector emerges.");
                break;
            case GoodStage.Heroic:
                ShowMoralMessage("Legends speak of The Cinderborn's heroic deeds. Hope returns to the world.");
                break;
            case GoodStage.Saintly:
                ShowMoralMessage("The Cinderborn transcends mortality. They are revered as a living saint.");
                break;
        }

        UpdateCharacterAppearance();
    }

    void UpdateCharacterAppearance()
    {
        CharacterProgression characterProgression = GetComponent<CharacterProgression>();
        if (characterProgression == null) return;

        // Evil path appearance changes
        if (currentPath == MoralPath.Evil)
        {
            switch (currentEvilStage)
            {
                case EvilStage.Broken:
                    // Appears weak, ragged, desperate
                    characterProgression.SetPosture(CharacterProgression.PostureType.Hunched);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Dull);
                    break;
                case EvilStage.Ruthless:
                    // Cold, calculating appearance
                    characterProgression.SetPosture(CharacterProgression.PostureType.Rigid);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Cold);
                    break;
                case EvilStage.Barbaric:
                    // Savage, intimidating presence
                    characterProgression.SetPosture(CharacterProgression.PostureType.Aggressive);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Red);
                    break;
                case EvilStage.Demonic:
                    // Terrifying, otherworldly aura
                    characterProgression.SetPosture(CharacterProgression.PostureType.Menacing);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Black);
                    break;
            }
        }

        // Good path appearance changes
        if (currentPath == MoralPath.Good)
        {
            switch (currentGoodStage)
            {
                case GoodStage.Compassionate:
                    // Gentle, approachable demeanor
                    characterProgression.SetPosture(CharacterProgression.PostureType.Open);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Warm);
                    break;
                case GoodStage.Protective:
                    // Strong, reliable presence
                    characterProgression.SetPosture(CharacterProgression.PostureType.Confident);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Determined);
                    break;
                case GoodStage.Heroic:
                    // Noble, inspiring aura
                    characterProgression.SetPosture(CharacterProgression.PostureType.Noble);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Bright);
                    break;
                case GoodStage.Saintly:
                    // Divine, transcendent presence
                    characterProgression.SetPosture(CharacterProgression.PostureType.Serene);
                    characterProgression.SetEyeColor(CharacterProgression.EyeColor.Golden);
                    break;
            }
        }
    }

    void UpdateAppearanceReactions()
    {
        // NPCs react to clothing and gear quality
        float appearanceModifier = CalculateAppearanceModifier();

        if (hostilitySystem != null)
        {
            hostilitySystem.appearanceModifier = appearanceModifier;
        }
    }

    float CalculateAppearanceModifier()
    {
        float clothingValue = (int)currentClothing * 0.2f;
        float armorValue = (int)currentArmor * 0.3f;
        float weaponValue = (int)currentWeaponQuality * 0.25f;

        return clothingValue + armorValue + weaponValue;
    }

    void CheckForStageTransitions()
    {
        // Check if moral path should change based on recent actions
        if (playerStats != null)
        {
            PlayerStats.MoralPath statsPath = playerStats.GetCurrentPath();

            switch (statsPath)
            {
                case PlayerStats.MoralPath.Moon:
                    if (currentPath != MoralPath.Evil)
                    {
                        TransitionToMoralPath(MoralPath.Evil);
                    }
                    break;
                case PlayerStats.MoralPath.Sun:
                    if (currentPath != MoralPath.Good)
                    {
                        TransitionToMoralPath(MoralPath.Good);
                    }
                    break;
                case PlayerStats.MoralPath.Eclipse:
                    if (currentPath != MoralPath.Conflicted)
                    {
                        TransitionToMoralPath(MoralPath.Conflicted);
                    }
                    break;
            }
        }
    }

    void TransitionToMoralPath(MoralPath newPath)
    {
        MoralPath oldPath = currentPath;
        currentPath = newPath;

        Debug.Log($"Moral path transition: {oldPath} → {newPath}");

        // Reset progression when changing paths
        if (newPath == MoralPath.Evil)
        {
            goodProgression = 0f;
            currentGoodStage = GoodStage.None;
        }
        else if (newPath == MoralPath.Good)
        {
            evilProgression = 0f;
            currentEvilStage = EvilStage.None;
        }

        // Core values never change
        ShowCoreValueReminder();
    }

    void ShowCoreValueReminder()
    {
        string coreValue = coreValues[Random.Range(0, coreValues.Length)];
        ShowMoralMessage($"Core Value: \"{coreValue}\"");
    }

    public void OnMoralChoice(bool isEvil, float intensity)
    {
        if (isEvil)
        {
            evilProgression += intensity * 0.1f;

            // Evil choices don't make you weak - they make you feared
            if (currentEvilStage == EvilStage.Broken)
            {
                ShowMoralMessage("The Cinderborn may appear broken, but darkness is growing within...");
            }
        }
        else
        {
            goodProgression += intensity * 0.1f;

            // Good choices don't require nobility - just strength of character
            ShowMoralMessage("The Cinderborn chooses the harder path - not for glory, but for principle.");
        }

        // Core values remain unchanged
        maintainsCoreValues = true;
    }

    public void UpdateClothing(ClothingType newClothing)
    {
        currentClothing = newClothing;
        Debug.Log($"Clothing changed to: {newClothing}");
    }

    public void UpdateArmor(ArmorType newArmor)
    {
        currentArmor = newArmor;
        Debug.Log($"Armor changed to: {newArmor}");
    }

    public void UpdateWeaponQuality(WeaponQuality newQuality)
    {
        currentWeaponQuality = newQuality;
        Debug.Log($"Weapon quality changed to: {newQuality}");
    }

    public string GetNPCReactionText()
    {
        // Combine moral path and appearance for NPC reactions
        string reactionText = "";

        // Appearance-based reactions
        switch (currentClothing)
        {
            case ClothingType.Ragged:
                reactionText += "A beggar? Or perhaps a mercenary down on their luck... ";
                break;
            case ClothingType.Noble:
                reactionText += "Such fine clothing... surely a person of importance... ";
                break;
        }

        // Moral path reactions
        switch (currentPath)
        {
            case MoralPath.Evil:
                switch (currentEvilStage)
                {
                    case EvilStage.Broken:
                        reactionText += "They seem... desperate. Dangerous in their desperation.";
                        break;
                    case EvilStage.Barbaric:
                        reactionText += "By the gods... the very air grows cold in their presence.";
                        break;
                    case EvilStage.Demonic:
                        reactionText += "*whispers* It's them... the one even demons fear...";
                        break;
                }
                break;
            case MoralPath.Good:
                switch (currentGoodStage)
                {
                    case GoodStage.Compassionate:
                        reactionText += "There's kindness in their eyes. I feel... safe.";
                        break;
                    case GoodStage.Heroic:
                        reactionText += "The legendary protector! They're real!";
                        break;
                    case GoodStage.Saintly:
                        reactionText += "*kneels* A living saint walks among us...";
                        break;
                }
                break;
        }

        return reactionText;
    }

    public bool GetNPCLoyalty()
    {
        // Evil path never earns true loyalty - only fear and silence
        if (currentPath == MoralPath.Evil)
        {
            return false;
        }

        // Good path earns admiration and trust, which can become loyalty
        if (currentPath == MoralPath.Good && currentGoodStage >= GoodStage.Protective)
        {
            return true;
        }

        return false;
    }

    void ShowMoralMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }

        Debug.Log($"Moral System: {message}");
    }

    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    // Getters
    public MoralPath GetCurrentPath() => currentPath;
    public EvilStage GetEvilStage() => currentEvilStage;
    public GoodStage GetGoodStage() => currentGoodStage;
    public float GetFearLevel() => fearLevel;
    public float GetAdmirationLevel() => admirationLevel;
    public bool MaintainsCoreValues() => maintainsCoreValues;
    public float GetAppearanceModifier() => CalculateAppearanceModifier();
    public bool IsInMoralChoice() => false; // Placeholder - would be set by dialogue system
}
