using UnityEngine;
using UnityEngine.UI;

public class EnemyHealth : MonoBehaviour
{
    [Header("Health Settings")]
    public float maxHealth = 100f;
    public bool showHealthBar = true;
    public GameObject healthBarPrefab;
    
    [Header("Death Settings")]
    public GameObject deathEffect;
    public float deathDelay = 2f;
    public bool dropLoot = true;
    public GameObject[] lootPrefabs;
    
    private float currentHealth;
    private Slider healthBar;
    private Canvas healthBarCanvas;
    private EnemyAI enemyAI;
    private bool isDead = false;
    
    // Events
    public System.Action OnDeath;
    public System.Action<float> OnHealthChanged;
    
    void Start()
    {
        currentHealth = maxHealth;
        enemyAI = GetComponent<EnemyAI>();
        
        if (showHealthBar)
        {
            CreateHealthBar();
        }
    }
    
    void Update()
    {
        if (healthBarCanvas != null)
        {
            // Make health bar face the camera
            Camera mainCamera = Camera.main;
            if (mainCamera != null)
            {
                healthBarCanvas.transform.LookAt(mainCamera.transform);
                healthBarCanvas.transform.Rotate(0, 180, 0);
            }
        }
    }
    
    void CreateHealthBar()
    {
        if (healthBarPrefab != null)
        {
            GameObject healthBarObj = Instantiate(healthBarPrefab, transform);
            healthBarCanvas = healthBarObj.GetComponent<Canvas>();
            healthBar = healthBarObj.GetComponentInChildren<Slider>();
        }
        else
        {
            // Create simple health bar
            GameObject healthBarObj = new GameObject("HealthBar");
            healthBarObj.transform.SetParent(transform);
            healthBarObj.transform.localPosition = Vector3.up * 2.5f;
            
            healthBarCanvas = healthBarObj.AddComponent<Canvas>();
            healthBarCanvas.renderMode = RenderMode.WorldSpace;
            healthBarCanvas.worldCamera = Camera.main;
            
            GameObject healthBarBG = new GameObject("Background");
            healthBarBG.transform.SetParent(healthBarObj.transform);
            healthBarBG.transform.localPosition = Vector3.zero;
            healthBarBG.transform.localScale = new Vector3(0.01f, 0.01f, 0.01f);
            
            Image bgImage = healthBarBG.AddComponent<Image>();
            bgImage.color = Color.red;
            
            RectTransform bgRect = healthBarBG.GetComponent<RectTransform>();
            bgRect.sizeDelta = new Vector2(100, 10);
            
            GameObject healthBarFill = new GameObject("Fill");
            healthBarFill.transform.SetParent(healthBarBG.transform);
            healthBarFill.transform.localPosition = Vector3.zero;
            healthBarFill.transform.localScale = Vector3.one;
            
            Image fillImage = healthBarFill.AddComponent<Image>();
            fillImage.color = Color.green;
            
            healthBar = healthBarBG.AddComponent<Slider>();
            healthBar.fillRect = healthBarFill.GetComponent<RectTransform>();
            healthBar.value = 1f;
        }
        
        UpdateHealthBar();
    }
    
    public void TakeDamage(float damage)
    {
        if (isDead) return;
        
        currentHealth = Mathf.Max(0, currentHealth - damage);
        
        // Trigger damage effects
        OnHealthChanged?.Invoke(currentHealth / maxHealth);
        
        // Update health bar
        UpdateHealthBar();
        
        // Notify AI of damage
        if (enemyAI != null)
        {
            enemyAI.OnTakeDamage(damage);
        }
        
        // Check for death
        if (currentHealth <= 0)
        {
            Die();
        }
        
        // Create damage number effect
        CreateDamageNumber(damage);
        
        Debug.Log($"{gameObject.name} took {damage} damage. Health: {currentHealth}/{maxHealth}");
    }
    
    public void Heal(float amount)
    {
        if (isDead) return;
        
        currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
        OnHealthChanged?.Invoke(currentHealth / maxHealth);
        UpdateHealthBar();
    }
    
    void Die()
    {
        if (isDead) return;
        isDead = true;
        
        Debug.Log($"{gameObject.name} has died!");
        
        // Trigger death event
        OnDeath?.Invoke();
        
        // Disable AI
        if (enemyAI != null)
        {
            enemyAI.enabled = false;
        }
        
        // Disable colliders
        Collider[] colliders = GetComponents<Collider>();
        foreach (Collider col in colliders)
        {
            col.enabled = false;
        }
        
        // Create death effect
        if (deathEffect != null)
        {
            Instantiate(deathEffect, transform.position, transform.rotation);
        }
        
        // Drop loot
        if (dropLoot)
        {
            DropLoot();
        }
        
        // Hide health bar
        if (healthBarCanvas != null)
        {
            healthBarCanvas.gameObject.SetActive(false);
        }
        
        // Destroy after delay
        Destroy(gameObject, deathDelay);
    }
    
    void DropLoot()
    {
        if (lootPrefabs == null || lootPrefabs.Length == 0) return;
        
        // Drop random loot
        GameObject lootToDrop = lootPrefabs[Random.Range(0, lootPrefabs.Length)];
        if (lootToDrop != null)
        {
            Vector3 dropPosition = transform.position + Vector3.up * 0.5f;
            GameObject droppedLoot = Instantiate(lootToDrop, dropPosition, Quaternion.identity);
            
            // Add some random force to the dropped loot
            Rigidbody lootRb = droppedLoot.GetComponent<Rigidbody>();
            if (lootRb != null)
            {
                Vector3 randomForce = new Vector3(
                    Random.Range(-3f, 3f),
                    Random.Range(2f, 5f),
                    Random.Range(-3f, 3f)
                );
                lootRb.AddForce(randomForce, ForceMode.Impulse);
            }
        }
    }
    
    void CreateDamageNumber(float damage)
    {
        // Create floating damage number
        GameObject damageNumber = new GameObject("DamageNumber");
        damageNumber.transform.position = transform.position + Vector3.up * 2f;
        
        Canvas canvas = damageNumber.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = Camera.main;
        
        Text damageText = damageNumber.AddComponent<Text>();
        damageText.text = damage.ToString("F0");
        damageText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        damageText.fontSize = 24;
        damageText.color = Color.red;
        damageText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform rectTransform = damageNumber.GetComponent<RectTransform>();
        rectTransform.sizeDelta = new Vector2(100, 50);
        
        // Animate the damage number
        StartCoroutine(AnimateDamageNumber(damageNumber));
    }
    
    System.Collections.IEnumerator AnimateDamageNumber(GameObject damageNumber)
    {
        float duration = 1f;
        float elapsed = 0f;
        Vector3 startPos = damageNumber.transform.position;
        Vector3 endPos = startPos + Vector3.up * 2f;
        
        Text damageText = damageNumber.GetComponent<Text>();
        Color startColor = damageText.color;
        Color endColor = new Color(startColor.r, startColor.g, startColor.b, 0f);
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            // Move upward
            damageNumber.transform.position = Vector3.Lerp(startPos, endPos, progress);
            
            // Fade out
            damageText.color = Color.Lerp(startColor, endColor, progress);
            
            yield return null;
        }
        
        Destroy(damageNumber);
    }
    
    void UpdateHealthBar()
    {
        if (healthBar != null)
        {
            healthBar.value = currentHealth / maxHealth;
            
            // Change color based on health percentage
            Image fillImage = healthBar.fillRect.GetComponent<Image>();
            if (fillImage != null)
            {
                float healthPercentage = currentHealth / maxHealth;
                if (healthPercentage > 0.6f)
                    fillImage.color = Color.green;
                else if (healthPercentage > 0.3f)
                    fillImage.color = Color.yellow;
                else
                    fillImage.color = Color.red;
            }
        }
    }
    
    // Getters
    public float GetHealthPercentage() => currentHealth / maxHealth;
    public bool IsDead() => isDead;
    public float GetCurrentHealth() => currentHealth;
    public float GetMaxHealth() => maxHealth;
}
