using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using TMPro;

/// <summary>
/// Arena Object Palette for Cinder of Darkness Arena Editor
/// Provides a grid of selectable objects for placement in arenas
/// </summary>
public class ArenaObjectPalette : MonoBehaviour
{
    [Header("UI References")]
    public GameObject palettePanel;
    public Transform paletteGrid;
    public GameObject paletteItemPrefab;
    public ScrollRect paletteScrollRect;
    
    [Header("Category Tabs")]
    public Transform categoryTabParent;
    public GameObject categoryTabPrefab;
    public Button[] categoryButtons;
    
    [Header("Object Categories")]
    public PaletteCategory[] categories;
    
    [Header("Selection")]
    public Image selectionHighlight;
    public Color selectedColor = Color.yellow;
    public Color normalColor = Color.white;
    
    // State
    private PaletteCategory currentCategory;
    private PaletteItem selectedItem;
    private List<GameObject> paletteItems = new List<GameObject>();
    private ArenaEditorManager editorManager;
    
    [System.Serializable]
    public class PaletteCategory
    {
        public string categoryName;
        public ArenaEditorManager.PlacementMode placementMode;
        public PaletteItem[] items;
        public Sprite categoryIcon;
        public Color categoryColor = Color.white;
    }
    
    [System.Serializable]
    public class PaletteItem
    {
        public string itemName;
        public string itemId;
        public GameObject prefab;
        public Sprite icon;
        public string description;
        public ArenaEditorManager.PlacementMode placementMode;
        public bool isUnlocked = true;
        public string[] tags;
    }
    
    void Start()
    {
        editorManager = FindObjectOfType<ArenaEditorManager>();
        SetupPalette();
        SetupCategoryTabs();
        
        if (categories.Length > 0)
        {
            SelectCategory(categories[0]);
        }
    }
    
    void SetupPalette()
    {
        if (palettePanel != null)
            palettePanel.SetActive(false);
        
        CreateDefaultCategories();
    }
    
    void CreateDefaultCategories()
    {
        if (categories == null || categories.Length == 0)
        {
            categories = new PaletteCategory[]
            {
                CreateEnemyCategory(),
                CreatePropsCategory(),
                CreateTrapsCategory(),
                CreateEnvironmentalCategory(),
                CreateSpawnPointsCategory()
            };
        }
    }
    
    PaletteCategory CreateEnemyCategory()
    {
        return new PaletteCategory
        {
            categoryName = "Enemies",
            placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
            categoryColor = Color.red,
            items = new PaletteItem[]
            {
                new PaletteItem
                {
                    itemName = "Shadow Warrior",
                    itemId = "enemy_shadow_warrior",
                    placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
                    description = "Basic melee enemy with shadow abilities",
                    tags = new string[] { "melee", "shadow" }
                },
                new PaletteItem
                {
                    itemName = "Flame Archer",
                    itemId = "enemy_flame_archer",
                    placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
                    description = "Ranged enemy with fire arrows",
                    tags = new string[] { "ranged", "fire" }
                },
                new PaletteItem
                {
                    itemName = "Ice Golem",
                    itemId = "enemy_ice_golem",
                    placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
                    description = "Heavy tank enemy with ice attacks",
                    tags = new string[] { "tank", "ice" }
                },
                new PaletteItem
                {
                    itemName = "Wind Spirit",
                    itemId = "enemy_wind_spirit",
                    placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
                    description = "Fast flying enemy with wind magic",
                    tags = new string[] { "flying", "wind" }
                }
            }
        };
    }
    
    PaletteCategory CreatePropsCategory()
    {
        return new PaletteCategory
        {
            categoryName = "Props",
            placementMode = ArenaEditorManager.PlacementMode.DestructibleProp,
            categoryColor = Color.brown,
            items = new PaletteItem[]
            {
                new PaletteItem
                {
                    itemName = "Wooden Crate",
                    itemId = "prop_wooden_crate",
                    placementMode = ArenaEditorManager.PlacementMode.DestructibleProp,
                    description = "Destructible wooden crate",
                    tags = new string[] { "destructible", "wood" }
                },
                new PaletteItem
                {
                    itemName = "Stone Pillar",
                    itemId = "prop_stone_pillar",
                    placementMode = ArenaEditorManager.PlacementMode.StaticProp,
                    description = "Indestructible stone pillar for cover",
                    tags = new string[] { "static", "stone", "cover" }
                },
                new PaletteItem
                {
                    itemName = "Crystal Formation",
                    itemId = "prop_crystal_formation",
                    placementMode = ArenaEditorManager.PlacementMode.StaticProp,
                    description = "Magical crystal formation",
                    tags = new string[] { "static", "crystal", "magic" }
                },
                new PaletteItem
                {
                    itemName = "Ash Barrel",
                    itemId = "prop_ash_barrel",
                    placementMode = ArenaEditorManager.PlacementMode.DestructibleProp,
                    description = "Explosive ash barrel",
                    tags = new string[] { "destructible", "explosive" }
                }
            }
        };
    }
    
    PaletteCategory CreateTrapsCategory()
    {
        return new PaletteCategory
        {
            categoryName = "Traps",
            placementMode = ArenaEditorManager.PlacementMode.Trap,
            categoryColor = Color.orange,
            items = new PaletteItem[]
            {
                new PaletteItem
                {
                    itemName = "Spike Trap",
                    itemId = "trap_spikes",
                    placementMode = ArenaEditorManager.PlacementMode.Trap,
                    description = "Ground spikes that emerge when triggered",
                    tags = new string[] { "ground", "physical" }
                },
                new PaletteItem
                {
                    itemName = "Fire Geyser",
                    itemId = "trap_fire_geyser",
                    placementMode = ArenaEditorManager.PlacementMode.Trap,
                    description = "Erupts flames from the ground",
                    tags = new string[] { "ground", "fire", "elemental" }
                },
                new PaletteItem
                {
                    itemName = "Poison Gas",
                    itemId = "trap_poison_gas",
                    placementMode = ArenaEditorManager.PlacementMode.Trap,
                    description = "Releases toxic gas cloud",
                    tags = new string[] { "area", "poison", "gas" }
                },
                new PaletteItem
                {
                    itemName = "Lightning Rod",
                    itemId = "trap_lightning_rod",
                    placementMode = ArenaEditorManager.PlacementMode.Trap,
                    description = "Strikes nearby enemies with lightning",
                    tags = new string[] { "ranged", "lightning", "elemental" }
                }
            }
        };
    }
    
    PaletteCategory CreateEnvironmentalCategory()
    {
        return new PaletteCategory
        {
            categoryName = "Environment",
            placementMode = ArenaEditorManager.PlacementMode.EnvironmentalTrigger,
            categoryColor = Color.green,
            items = new PaletteItem[]
            {
                new PaletteItem
                {
                    itemName = "Fog Zone",
                    itemId = "env_fog_zone",
                    placementMode = ArenaEditorManager.PlacementMode.EnvironmentalTrigger,
                    description = "Area with reduced visibility",
                    tags = new string[] { "visibility", "area" }
                },
                new PaletteItem
                {
                    itemName = "Healing Spring",
                    itemId = "env_healing_spring",
                    placementMode = ArenaEditorManager.PlacementMode.EnvironmentalTrigger,
                    description = "Slowly heals players in the area",
                    tags = new string[] { "healing", "area", "beneficial" }
                },
                new PaletteItem
                {
                    itemName = "Wind Current",
                    itemId = "env_wind_current",
                    placementMode = ArenaEditorManager.PlacementMode.EnvironmentalTrigger,
                    description = "Pushes objects in a direction",
                    tags = new string[] { "wind", "movement" }
                },
                new PaletteItem
                {
                    itemName = "Lava Pool",
                    itemId = "env_lava_pool",
                    placementMode = ArenaEditorManager.PlacementMode.EnvironmentalTrigger,
                    description = "Damages anything that touches it",
                    tags = new string[] { "fire", "damage", "hazard" }
                }
            }
        };
    }
    
    PaletteCategory CreateSpawnPointsCategory()
    {
        return new PaletteCategory
        {
            categoryName = "Spawn Points",
            placementMode = ArenaEditorManager.PlacementMode.PlayerSpawn,
            categoryColor = Color.blue,
            items = new PaletteItem[]
            {
                new PaletteItem
                {
                    itemName = "Player Spawn",
                    itemId = "spawn_player",
                    placementMode = ArenaEditorManager.PlacementMode.PlayerSpawn,
                    description = "Starting position for the player",
                    tags = new string[] { "spawn", "player" }
                },
                new PaletteItem
                {
                    itemName = "Enemy Spawn",
                    itemId = "spawn_enemy",
                    placementMode = ArenaEditorManager.PlacementMode.EnemySpawn,
                    description = "Position where enemies will spawn",
                    tags = new string[] { "spawn", "enemy" }
                }
            }
        };
    }
    
    void SetupCategoryTabs()
    {
        if (categoryTabParent == null) return;
        
        // Clear existing tabs
        foreach (Transform child in categoryTabParent)
        {
            Destroy(child.gameObject);
        }
        
        // Create tabs for each category
        for (int i = 0; i < categories.Length; i++)
        {
            CreateCategoryTab(categories[i], i);
        }
    }
    
    void CreateCategoryTab(PaletteCategory category, int index)
    {
        if (categoryTabPrefab == null) return;
        
        GameObject tab = Instantiate(categoryTabPrefab, categoryTabParent);
        
        // Setup tab button
        var button = tab.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.AddListener(() => SelectCategory(category));
            
            // Set tab color
            var buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                buttonImage.color = category.categoryColor;
            }
        }
        
        // Setup tab text
        var text = tab.GetComponentInChildren<TextMeshProUGUI>();
        if (text != null)
        {
            text.text = category.categoryName;
        }
        
        // Setup tab icon
        var icon = tab.GetComponentInChildren<Image>();
        if (icon != null && icon != button.GetComponent<Image>() && category.categoryIcon != null)
        {
            icon.sprite = category.categoryIcon;
        }
    }
    
    public void SelectCategory(PaletteCategory category)
    {
        currentCategory = category;
        RefreshPaletteItems();
        
        Debug.Log($"Selected category: {category.categoryName}");
    }
    
    void RefreshPaletteItems()
    {
        ClearPaletteItems();
        
        if (currentCategory == null || currentCategory.items == null) return;
        
        foreach (var item in currentCategory.items)
        {
            CreatePaletteItemUI(item);
        }
    }
    
    void ClearPaletteItems()
    {
        foreach (var item in paletteItems)
        {
            if (item != null)
                Destroy(item);
        }
        paletteItems.Clear();
    }
    
    void CreatePaletteItemUI(PaletteItem item)
    {
        if (paletteItemPrefab == null || paletteGrid == null) return;
        
        GameObject itemUI = Instantiate(paletteItemPrefab, paletteGrid);
        paletteItems.Add(itemUI);
        
        // Setup item button
        var button = itemUI.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.AddListener(() => SelectItem(item));
            button.interactable = item.isUnlocked;
        }
        
        // Setup item icon
        var icon = itemUI.GetComponentInChildren<Image>();
        if (icon != null && item.icon != null)
        {
            icon.sprite = item.icon;
        }
        
        // Setup item text
        var text = itemUI.GetComponentInChildren<TextMeshProUGUI>();
        if (text != null)
        {
            text.text = item.itemName;
            text.color = item.isUnlocked ? Color.white : Color.gray;
        }
        
        // Setup tooltip
        var tooltip = itemUI.GetComponent<Tooltip>();
        if (tooltip == null)
            tooltip = itemUI.AddComponent<Tooltip>();
        
        tooltip.tooltipText = $"{item.itemName}\n{item.description}";
    }
    
    public void SelectItem(PaletteItem item)
    {
        if (!item.isUnlocked) return;
        
        selectedItem = item;
        
        // Set placement mode in editor
        if (editorManager != null)
        {
            editorManager.SetPlacementMode(item.placementMode);
        }
        
        // Update visual selection
        UpdateSelectionHighlight();
        
        Debug.Log($"Selected item: {item.itemName}");
    }
    
    void UpdateSelectionHighlight()
    {
        // Update all palette item colors
        foreach (var itemUI in paletteItems)
        {
            var button = itemUI.GetComponent<Button>();
            if (button != null)
            {
                var colors = button.colors;
                colors.normalColor = normalColor;
                button.colors = colors;
            }
        }
        
        // Highlight selected item
        if (selectedItem != null)
        {
            var selectedItemUI = FindPaletteItemUI(selectedItem);
            if (selectedItemUI != null)
            {
                var button = selectedItemUI.GetComponent<Button>();
                if (button != null)
                {
                    var colors = button.colors;
                    colors.normalColor = selectedColor;
                    button.colors = colors;
                }
            }
        }
    }
    
    GameObject FindPaletteItemUI(PaletteItem item)
    {
        foreach (var itemUI in paletteItems)
        {
            var button = itemUI.GetComponent<Button>();
            if (button != null)
            {
                // Check if this UI represents the selected item
                var text = itemUI.GetComponentInChildren<TextMeshProUGUI>();
                if (text != null && text.text == item.itemName)
                {
                    return itemUI;
                }
            }
        }
        return null;
    }
    
    public GameObject GetPrefabForMode(ArenaEditorManager.PlacementMode mode)
    {
        if (selectedItem != null && selectedItem.placementMode == mode)
        {
            return selectedItem.prefab;
        }
        
        // Search all categories for a prefab with the matching mode
        foreach (var category in categories)
        {
            foreach (var item in category.items)
            {
                if (item.placementMode == mode && item.prefab != null)
                {
                    return item.prefab;
                }
            }
        }
        
        return null;
    }
    
    public void ShowPalette()
    {
        if (palettePanel != null)
            palettePanel.SetActive(true);
    }
    
    public void HidePalette()
    {
        if (palettePanel != null)
            palettePanel.SetActive(false);
    }
    
    public void TogglePalette()
    {
        if (palettePanel != null)
            palettePanel.SetActive(!palettePanel.activeSelf);
    }
    
    // Search functionality
    public void SearchItems(string searchTerm)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            RefreshPaletteItems();
            return;
        }
        
        ClearPaletteItems();
        
        foreach (var category in categories)
        {
            foreach (var item in category.items)
            {
                if (ItemMatchesSearch(item, searchTerm))
                {
                    CreatePaletteItemUI(item);
                }
            }
        }
    }
    
    bool ItemMatchesSearch(PaletteItem item, string searchTerm)
    {
        searchTerm = searchTerm.ToLower();
        
        if (item.itemName.ToLower().Contains(searchTerm))
            return true;
        
        if (item.description.ToLower().Contains(searchTerm))
            return true;
        
        foreach (var tag in item.tags)
        {
            if (tag.ToLower().Contains(searchTerm))
                return true;
        }
        
        return false;
    }
}

/// <summary>
/// Simple tooltip component for palette items
/// </summary>
public class Tooltip : MonoBehaviour, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler
{
    public string tooltipText;
    private GameObject tooltipUI;
    
    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData eventData)
    {
        ShowTooltip();
    }
    
    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData eventData)
    {
        HideTooltip();
    }
    
    void ShowTooltip()
    {
        if (string.IsNullOrEmpty(tooltipText)) return;
        
        // Create simple tooltip
        tooltipUI = new GameObject("Tooltip");
        var canvas = tooltipUI.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000;
        
        var text = tooltipUI.AddComponent<TextMeshProUGUI>();
        text.text = tooltipText;
        text.fontSize = 14;
        text.color = Color.white;
        
        var rect = tooltipUI.GetComponent<RectTransform>();
        rect.position = Input.mousePosition;
    }
    
    void HideTooltip()
    {
        if (tooltipUI != null)
        {
            Destroy(tooltipUI);
        }
    }
}
