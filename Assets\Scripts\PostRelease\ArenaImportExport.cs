using UnityEngine;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;
using TMPro;
using UnityEngine.UI;

/// <summary>
/// Arena Import/Export System for Cinder of Darkness
/// Handles importing and exporting arena files with drag-and-drop support
/// </summary>
public class ArenaImportExport : MonoBehaviour
{
    [Header("Import/Export UI")]
    public GameObject importExportUI;
    public Button importButton;
    public Button exportButton;
    public Button exportAllButton;
    public TMP_Dropdown exportArenaDropdown;
    public TMP_InputField exportPathField;
    
    [Header("Drag and Drop")]
    public GameObject dropZone;
    public TextMeshProUGUI dropZoneText;
    public Color normalDropZoneColor = Color.gray;
    public Color highlightDropZoneColor = Color.green;
    public Color errorDropZoneColor = Color.red;
    
    [Header("Progress")]
    public GameObject progressPanel;
    public Slider progressSlider;
    public TextMeshProUGUI progressText;
    
    [Header("Confirmation Dialogs")]
    public GameObject confirmationDialog;
    public TextMeshProUGUI confirmationText;
    public Button confirmButton;
    public Button cancelButton;
    
    // Static instance
    private static ArenaImportExport instance;
    public static ArenaImportExport Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaImportExport>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaImportExport");
                    instance = go.AddComponent<ArenaImportExport>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Import/Export state
    private List<string> pendingImports = new List<string>();
    private System.Action<bool> currentConfirmationCallback;
    private bool isDragOver = false;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeImportExport();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        SetupDragAndDrop();
    }
    
    void InitializeImportExport()
    {
        Debug.Log("Arena Import/Export system initialized");
    }
    
    void SetupUI()
    {
        if (importExportUI != null)
            importExportUI.SetActive(false);
        
        if (progressPanel != null)
            progressPanel.SetActive(false);
        
        if (confirmationDialog != null)
            confirmationDialog.SetActive(false);
        
        if (importButton != null)
            importButton.onClick.AddListener(OpenImportDialog);
        
        if (exportButton != null)
            exportButton.onClick.AddListener(ExportSelectedArena);
        
        if (exportAllButton != null)
            exportAllButton.onClick.AddListener(ExportAllArenas);
        
        if (confirmButton != null)
            confirmButton.onClick.AddListener(ConfirmAction);
        
        if (cancelButton != null)
            cancelButton.onClick.AddListener(CancelAction);
        
        RefreshExportDropdown();
    }
    
    void SetupDragAndDrop()
    {
        if (dropZone != null)
        {
            var dropHandler = dropZone.GetComponent<DropHandler>();
            if (dropHandler == null)
            {
                dropHandler = dropZone.AddComponent<DropHandler>();
            }
            
            dropHandler.OnFilesDropped += OnFilesDropped;
            dropHandler.OnDragEnter += OnDragEnter;
            dropHandler.OnDragExit += OnDragExit;
        }
    }
    
    void RefreshExportDropdown()
    {
        if (exportArenaDropdown == null) return;
        
        var arenas = ArenaManager.GetLoadedArenas();
        var arenaNames = new List<string>();
        
        foreach (var arena in arenas)
        {
            arenaNames.Add($"{arena.arenaName} ({arena.author})");
        }
        
        exportArenaDropdown.ClearOptions();
        exportArenaDropdown.AddOptions(arenaNames);
    }
    
    // Public API
    public static void OpenImportExportUI()
    {
        Instance.OpenImportExportUIInternal();
    }
    
    public static void CloseImportExportUI()
    {
        Instance.CloseImportExportUIInternal();
    }
    
    public static void ImportArenaFile(string filePath)
    {
        Instance.ImportArenaFileInternal(filePath);
    }
    
    public static void ExportArena(string arenaId, string exportPath)
    {
        Instance.ExportArenaInternal(arenaId, exportPath);
    }
    
    public static void ExportAllUserArenas(string exportDirectory)
    {
        Instance.ExportAllUserArenasInternal(exportDirectory);
    }
    
    void OpenImportExportUIInternal()
    {
        if (importExportUI != null)
            importExportUI.SetActive(true);
        
        RefreshExportDropdown();
    }
    
    void CloseImportExportUIInternal()
    {
        if (importExportUI != null)
            importExportUI.SetActive(false);
    }
    
    void OpenImportDialog()
    {
        // In a full implementation, this would open a file browser
        // For now, we'll use the drag-and-drop system
        ShowMessage("Drag and drop .arenamod or .json files into the drop zone to import them.");
    }
    
    void ExportSelectedArena()
    {
        if (exportArenaDropdown == null) return;
        
        var arenas = ArenaManager.GetLoadedArenas();
        if (exportArenaDropdown.value >= 0 && exportArenaDropdown.value < arenas.Length)
        {
            var selectedArena = arenas[exportArenaDropdown.value];
            string exportPath = GetExportPath(selectedArena.arenaName);
            
            ExportArenaInternal(selectedArena.arenaId, exportPath);
        }
    }
    
    void ExportAllArenas()
    {
        ShowConfirmation("Export all user-created arenas?", (confirmed) =>
        {
            if (confirmed)
            {
                string exportDir = Path.Combine(Application.persistentDataPath, "Exports", "Arenas");
                ExportAllUserArenasInternal(exportDir);
            }
        });
    }
    
    string GetExportPath(string arenaName)
    {
        string fileName = SanitizeFileName(arenaName) + ".arenamod";
        
        if (exportPathField != null && !string.IsNullOrEmpty(exportPathField.text))
        {
            return Path.Combine(exportPathField.text, fileName);
        }
        
        // Default export path
        string exportDir = Path.Combine(Application.persistentDataPath, "Exports");
        Directory.CreateDirectory(exportDir);
        return Path.Combine(exportDir, fileName);
    }
    
    void ImportArenaFileInternal(string filePath)
    {
        if (!File.Exists(filePath))
        {
            ShowError($"File not found: {filePath}");
            return;
        }
        
        string extension = Path.GetExtension(filePath).ToLower();
        
        if (extension != ".arenamod" && extension != ".json")
        {
            ShowError("Invalid file format. Only .arenamod and .json files are supported.");
            return;
        }
        
        try
        {
            ShowProgress("Importing arena...", 0f);
            
            string arenaId = ArenaManager.ImportArenaFromFile(filePath);
            
            if (!string.IsNullOrEmpty(arenaId))
            {
                ShowProgress("Import complete!", 1f);
                ShowMessage($"Arena imported successfully!");
                
                // Refresh UI
                RefreshExportDropdown();
                
                // Hide progress after delay
                StartCoroutine(HideProgressAfterDelay(2f));
            }
            else
            {
                ShowError("Failed to import arena. Please check the file format.");
            }
        }
        catch (System.Exception e)
        {
            ShowError($"Import failed: {e.Message}");
        }
    }
    
    void ExportArenaInternal(string arenaId, string exportPath)
    {
        try
        {
            ShowProgress("Exporting arena...", 0f);
            
            bool success = ArenaManager.ExportArenaToFile(arenaId, exportPath);
            
            if (success)
            {
                ShowProgress("Export complete!", 1f);
                ShowMessage($"Arena exported to: {exportPath}");
                
                // Hide progress after delay
                StartCoroutine(HideProgressAfterDelay(2f));
            }
            else
            {
                ShowError("Failed to export arena.");
            }
        }
        catch (System.Exception e)
        {
            ShowError($"Export failed: {e.Message}");
        }
    }
    
    void ExportAllUserArenasInternal(string exportDirectory)
    {
        try
        {
            Directory.CreateDirectory(exportDirectory);
            
            var arenas = ArenaManager.GetLoadedArenas();
            var userArenas = arenas.Where(a => a.isUserCreated).ToArray();
            
            if (userArenas.Length == 0)
            {
                ShowMessage("No user-created arenas to export.");
                return;
            }
            
            ShowProgress("Exporting arenas...", 0f);
            
            for (int i = 0; i < userArenas.Length; i++)
            {
                var arena = userArenas[i];
                string fileName = SanitizeFileName(arena.arenaName) + ".arenamod";
                string exportPath = Path.Combine(exportDirectory, fileName);
                
                ArenaManager.ExportArenaToFile(arena.arenaId, exportPath);
                
                float progress = (float)(i + 1) / userArenas.Length;
                ShowProgress($"Exporting {arena.arenaName}...", progress);
                
                yield return null; // Allow UI to update
            }
            
            ShowProgress("Export complete!", 1f);
            ShowMessage($"Exported {userArenas.Length} arenas to: {exportDirectory}");
            
            // Hide progress after delay
            StartCoroutine(HideProgressAfterDelay(2f));
        }
        catch (System.Exception e)
        {
            ShowError($"Batch export failed: {e.Message}");
        }
    }
    
    // Drag and Drop handlers
    void OnFilesDropped(string[] filePaths)
    {
        OnDragExit();
        
        List<string> validFiles = new List<string>();
        List<string> invalidFiles = new List<string>();
        
        foreach (string filePath in filePaths)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            if (extension == ".arenamod" || extension == ".json")
            {
                validFiles.Add(filePath);
            }
            else
            {
                invalidFiles.Add(filePath);
            }
        }
        
        if (invalidFiles.Count > 0)
        {
            ShowError($"Invalid files ignored: {string.Join(", ", invalidFiles)}");
        }
        
        if (validFiles.Count > 0)
        {
            if (validFiles.Count == 1)
            {
                ImportArenaFileInternal(validFiles[0]);
            }
            else
            {
                ShowConfirmation($"Import {validFiles.Count} arena files?", (confirmed) =>
                {
                    if (confirmed)
                    {
                        StartCoroutine(ImportMultipleFiles(validFiles));
                    }
                });
            }
        }
    }
    
    void OnDragEnter()
    {
        isDragOver = true;
        UpdateDropZoneVisual(highlightDropZoneColor);
        
        if (dropZoneText != null)
            dropZoneText.text = "Drop arena files here";
    }
    
    void OnDragExit()
    {
        isDragOver = false;
        UpdateDropZoneVisual(normalDropZoneColor);
        
        if (dropZoneText != null)
            dropZoneText.text = "Drag and drop arena files here";
    }
    
    void UpdateDropZoneVisual(Color color)
    {
        if (dropZone != null)
        {
            var image = dropZone.GetComponent<Image>();
            if (image != null)
            {
                image.color = color;
            }
        }
    }
    
    System.Collections.IEnumerator ImportMultipleFiles(List<string> filePaths)
    {
        ShowProgress("Importing arenas...", 0f);
        
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < filePaths.Count; i++)
        {
            try
            {
                string arenaId = ArenaManager.ImportArenaFromFile(filePaths[i]);
                if (!string.IsNullOrEmpty(arenaId))
                {
                    successCount++;
                }
                else
                {
                    failCount++;
                }
            }
            catch
            {
                failCount++;
            }
            
            float progress = (float)(i + 1) / filePaths.Count;
            ShowProgress($"Importing arena {i + 1} of {filePaths.Count}...", progress);
            
            yield return null; // Allow UI to update
        }
        
        ShowProgress("Import complete!", 1f);
        ShowMessage($"Import complete! Success: {successCount}, Failed: {failCount}");
        
        // Refresh UI
        RefreshExportDropdown();
        
        // Hide progress after delay
        StartCoroutine(HideProgressAfterDelay(2f));
    }
    
    System.Collections.IEnumerator HideProgressAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (progressPanel != null)
            progressPanel.SetActive(false);
    }
    
    // UI Helper methods
    void ShowProgress(string message, float progress)
    {
        if (progressPanel != null)
            progressPanel.SetActive(true);
        
        if (progressText != null)
            progressText.text = message;
        
        if (progressSlider != null)
            progressSlider.value = progress;
    }
    
    void ShowMessage(string message)
    {
        Debug.Log($"Arena Import/Export: {message}");
        // In a full implementation, this would show a proper message UI
    }
    
    void ShowError(string error)
    {
        Debug.LogError($"Arena Import/Export Error: {error}");
        UpdateDropZoneVisual(errorDropZoneColor);
        
        // Reset drop zone color after delay
        StartCoroutine(ResetDropZoneColorAfterDelay(2f));
    }
    
    System.Collections.IEnumerator ResetDropZoneColorAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        UpdateDropZoneVisual(normalDropZoneColor);
    }
    
    void ShowConfirmation(string message, System.Action<bool> callback)
    {
        if (confirmationDialog != null)
            confirmationDialog.SetActive(true);
        
        if (confirmationText != null)
            confirmationText.text = message;
        
        currentConfirmationCallback = callback;
    }
    
    void ConfirmAction()
    {
        if (confirmationDialog != null)
            confirmationDialog.SetActive(false);
        
        currentConfirmationCallback?.Invoke(true);
        currentConfirmationCallback = null;
    }
    
    void CancelAction()
    {
        if (confirmationDialog != null)
            confirmationDialog.SetActive(false);
        
        currentConfirmationCallback?.Invoke(false);
        currentConfirmationCallback = null;
    }
    
    string SanitizeFileName(string fileName)
    {
        string sanitized = fileName;
        char[] invalidChars = Path.GetInvalidFileNameChars();
        
        foreach (char c in invalidChars)
        {
            sanitized = sanitized.Replace(c, '_');
        }
        
        return sanitized;
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        switch (eventName)
        {
            case "ArenaCreated":
            case "ArenaDeleted":
                RefreshExportDropdown();
                break;
        }
    }
}

/// <summary>
/// Drag and Drop handler component for arena files
/// </summary>
public class DropHandler : MonoBehaviour, UnityEngine.EventSystems.IDropHandler, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler
{
    public System.Action<string[]> OnFilesDropped;
    public System.Action OnDragEnter;
    public System.Action OnDragExit;
    
    public void OnDrop(UnityEngine.EventSystems.PointerEventData eventData)
    {
        // In a full implementation, this would handle actual file drops
        // For now, we'll simulate with a file browser
        Debug.Log("File drop detected (simulated)");
        
        // Simulate dropped files
        string[] simulatedFiles = { "example_arena.arenamod" };
        OnFilesDropped?.Invoke(simulatedFiles);
    }
    
    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData eventData)
    {
        OnDragEnter?.Invoke();
    }
    
    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData eventData)
    {
        OnDragExit?.Invoke();
    }
}
