using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class OrphanedChildCompanion : MonoBehaviour
{
    [Header("Child Identity")]
    public string childName = "Ember";
    public int childAge = 8;
    public ChildState currentState = ChildState.Innocent;
    public float bondLevel = 0f; // -100 to 100
    
    [Header("Moral Development")]
    public MoralAlignment childAlignment = MoralAlignment.Neutral;
    public float moralInfluence = 0f; // How much player's actions affect the child
    public List<WitnessedAction> witnessedActions = new List<WitnessedAction>();
    
    [Header("Emotional Journey")]
    public EmotionalState currentEmotion = EmotionalState.Curious;
    public float trustLevel = 50f;
    public float fearLevel = 0f;
    public float admirationLevel = 0f;
    public float resentmentLevel = 0f;
    
    [Header("Story Integration")]
    public bool hasMetParents = false;
    public bool finalChoiceMade = false;
    public FinalChoice playerFinalChoice = FinalChoice.None;
    public GameObject childModel;
    
    [Header("Dialogue System")]
    public ChildDialogue[] innocentDialogues;
    public ChildDialogue[] admiringDialogues;
    public ChildDialogue[] fearfulDialogues;
    public ChildDialogue[] resentfulDialogues;
    public ChildDialogue[] finalChoiceDialogues;
    
    private PhilosophicalMoralitySystem moralitySystem;
    private DeathConsequenceSystem deathSystem;
    private DynamicTitleSystem titleSystem;
    private PsychologicalSystem psycheSystem;
    private GameManager gameManager;
    
    public enum ChildState
    {
        Innocent,        // Pure, untainted by the world's darkness
        Admiring,        // Looks up to The Cinderborn as a hero
        Fearful,         // Afraid of The Cinderborn's dark actions
        Resentful,       // Angry and hurt by betrayal or cruelty
        Vengeful,        // Sworn enemy if abandoned
        Redeemed         // Saved and adopted by The Cinderborn
    }
    
    public enum MoralAlignment
    {
        Neutral,
        Good,
        Evil,
        Conflicted
    }
    
    public enum EmotionalState
    {
        Curious,
        Happy,
        Sad,
        Afraid,
        Angry,
        Hopeful,
        Devastated,
        Determined
    }
    
    public enum FinalChoice
    {
        None,
        AbandonChild,    // Choose parents, child becomes enemy
        AdoptChild,      // Reject parents, adopt child
        Conflicted       // Unable to choose, tragic ending
    }
    
    [System.Serializable]
    public class WitnessedAction
    {
        public string actionName;
        public ActionType type;
        public float moralWeight;
        public float emotionalImpact;
        public string childReaction;
        public float timestamp;
        
        public enum ActionType
        {
            KilledInnocent,
            KilledVillain,
            ShowedMercy,
            HelpedChild,
            HelpedElder,
            ActOfCruelty,
            ActOfKindness,
            Betrayal,
            Sacrifice,
            Protection
        }
    }
    
    [System.Serializable]
    public class ChildDialogue
    {
        public string dialogueText;
        public EmotionalState requiredEmotion;
        public float requiredBondLevel;
        public string contextTrigger;
        public AudioClip voiceLine;
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        titleSystem = GetComponent<DynamicTitleSystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        gameManager = GameManager.Instance;
        
        InitializeChildCompanion();
    }
    
    void Update()
    {
        UpdateChildDevelopment();
        UpdateEmotionalState();
        CheckForDialogueTriggers();
        UpdateChildBehavior();
    }
    
    void InitializeChildCompanion()
    {
        // Child starts innocent and curious
        currentState = ChildState.Innocent;
        currentEmotion = EmotionalState.Curious;
        childAlignment = MoralAlignment.Neutral;
        
        // Initialize dialogue arrays
        InitializeDialogues();
        
        // Spawn child model if available
        if (childModel != null)
        {
            childModel.SetActive(true);
        }
        
        // Show introduction sequence
        StartCoroutine(ChildIntroductionSequence());
        
        Debug.Log($"Orphaned child companion '{childName}' initialized - their fate lies in your hands");
    }
    
    void InitializeDialogues()
    {
        // Innocent phase dialogues
        innocentDialogues = new ChildDialogue[]
        {
            new ChildDialogue
            {
                dialogueText = "Are you a hero? You fight like the ones in the stories mama used to tell...",
                requiredEmotion = EmotionalState.Curious,
                requiredBondLevel = 0f
            },
            new ChildDialogue
            {
                dialogueText = "I don't have anywhere to go... can I stay with you?",
                requiredEmotion = EmotionalState.Hopeful,
                requiredBondLevel = 10f
            },
            new ChildDialogue
            {
                dialogueText = "You smell like smoke and ash... but your eyes are kind.",
                requiredEmotion = EmotionalState.Curious,
                requiredBondLevel = 5f
            }
        };
        
        // Admiring phase dialogues
        admiringDialogues = new ChildDialogue[]
        {
            new ChildDialogue
            {
                dialogueText = "You're the strongest person I've ever seen! Will you teach me to be brave like you?",
                requiredEmotion = EmotionalState.Happy,
                requiredBondLevel = 30f
            },
            new ChildDialogue
            {
                dialogueText = "When I grow up, I want to help people just like you do!",
                requiredEmotion = EmotionalState.Hopeful,
                requiredBondLevel = 50f
            },
            new ChildDialogue
            {
                dialogueText = "The bad people run away when they see you coming. You make me feel safe.",
                requiredEmotion = EmotionalState.Happy,
                requiredBondLevel = 40f
            }
        };
        
        // Fearful phase dialogues
        fearfulDialogues = new ChildDialogue[]
        {
            new ChildDialogue
            {
                dialogueText = "Why... why did you hurt that person? They weren't fighting back...",
                requiredEmotion = EmotionalState.Afraid,
                requiredBondLevel = -10f
            },
            new ChildDialogue
            {
                dialogueText = "I'm scared of you sometimes... your eyes get so dark and cold.",
                requiredEmotion = EmotionalState.Afraid,
                requiredBondLevel = -20f
            },
            new ChildDialogue
            {
                dialogueText = "Please don't hurt me... I'll be good, I promise...",
                requiredEmotion = EmotionalState.Afraid,
                requiredBondLevel = -40f
            }
        };
        
        // Resentful phase dialogues
        resentfulDialogues = new ChildDialogue[]
        {
            new ChildDialogue
            {
                dialogueText = "You're not a hero at all... you're just another monster.",
                requiredEmotion = EmotionalState.Angry,
                requiredBondLevel = -50f
            },
            new ChildDialogue
            {
                dialogueText = "I hate you! I hate what you've become! I wish I never met you!",
                requiredEmotion = EmotionalState.Angry,
                requiredBondLevel = -70f
            },
            new ChildDialogue
            {
                dialogueText = "One day... one day I'll be strong enough to stop people like you.",
                requiredEmotion = EmotionalState.Determined,
                requiredBondLevel = -80f
            }
        };
        
        // Final choice dialogues
        finalChoiceDialogues = new ChildDialogue[]
        {
            new ChildDialogue
            {
                dialogueText = "So... these are your real parents? The ones who... who matter more than me?",
                contextTrigger = "ParentsMeeting"
            },
            new ChildDialogue
            {
                dialogueText = "I understand... blood is thicker than the bonds we chose. Goodbye, Cinderborn.",
                contextTrigger = "Abandonment"
            },
            new ChildDialogue
            {
                dialogueText = "You... you chose me? Even over your own family? I... I don't know what to say...",
                contextTrigger = "Adoption"
            }
        };
    }
    
    IEnumerator ChildIntroductionSequence()
    {
        yield return new WaitForSeconds(2f);
        
        ShowChildMessage("A small figure emerges from behind the rubble...");
        yield return new WaitForSeconds(2f);
        
        ShowChildMessage($"Child: \"I... I saw you fight. You're really strong...\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"Child: \"My name is {childName}. I don't have anyone left... can I... can I come with you?\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("The child looks at you with a mixture of hope and fear in their eyes.");
        yield return new WaitForSeconds(2f);
        
        // Player can choose to accept or reject the child
        // For now, automatically accept
        AcceptChild();
    }
    
    void AcceptChild()
    {
        bondLevel += 20f;
        trustLevel += 30f;
        currentEmotion = EmotionalState.Hopeful;
        
        ShowChildMessage($"{childName}: \"Really? You'll let me stay? Thank you... thank you so much!\"");
        
        // Add child to party
        gameManager?.AddCompanion(childName);
    }
    
    void UpdateChildDevelopment()
    {
        // Child's moral development mirrors player's actions
        if (moralitySystem != null)
        {
            PhilosophicalMoralitySystem.MoralPath playerPath = moralitySystem.GetCurrentPath();
            
            switch (playerPath)
            {
                case PhilosophicalMoralitySystem.MoralPath.Good:
                    if (childAlignment != MoralAlignment.Good)
                    {
                        childAlignment = MoralAlignment.Good;
                        TransitionToAdmiring();
                    }
                    break;
                
                case PhilosophicalMoralitySystem.MoralPath.Evil:
                    if (childAlignment != MoralAlignment.Evil)
                    {
                        childAlignment = MoralAlignment.Evil;
                        if (bondLevel > 0)
                            TransitionToFearful();
                        else
                            TransitionToResentful();
                    }
                    break;
                
                case PhilosophicalMoralitySystem.MoralPath.Eclipse:
                    childAlignment = MoralAlignment.Conflicted;
                    break;
            }
        }
    }
    
    void UpdateEmotionalState()
    {
        // Update emotional state based on recent events and bond level
        if (bondLevel > 60f)
        {
            currentEmotion = EmotionalState.Happy;
        }
        else if (bondLevel > 20f)
        {
            currentEmotion = EmotionalState.Hopeful;
        }
        else if (bondLevel > -20f)
        {
            currentEmotion = EmotionalState.Sad;
        }
        else if (bondLevel > -60f)
        {
            currentEmotion = EmotionalState.Afraid;
        }
        else
        {
            currentEmotion = EmotionalState.Angry;
        }
        
        // Fear and resentment affect emotional state
        if (fearLevel > 50f)
        {
            currentEmotion = EmotionalState.Afraid;
        }
        else if (resentmentLevel > 70f)
        {
            currentEmotion = EmotionalState.Angry;
        }
    }
    
    void CheckForDialogueTriggers()
    {
        // Trigger appropriate dialogue based on current state and recent events
        if (Random.Range(0f, 1f) < 0.005f) // Small chance each frame
        {
            TriggerContextualDialogue();
        }
    }
    
    void TriggerContextualDialogue()
    {
        ChildDialogue[] currentDialogues = GetCurrentDialogueSet();
        
        if (currentDialogues.Length > 0)
        {
            ChildDialogue dialogue = currentDialogues[Random.Range(0, currentDialogues.Length)];
            
            if (bondLevel >= dialogue.requiredBondLevel && currentEmotion == dialogue.requiredEmotion)
            {
                ShowChildDialogue(dialogue);
            }
        }
    }
    
    ChildDialogue[] GetCurrentDialogueSet()
    {
        switch (currentState)
        {
            case ChildState.Innocent:
                return innocentDialogues;
            case ChildState.Admiring:
                return admiringDialogues;
            case ChildState.Fearful:
                return fearfulDialogues;
            case ChildState.Resentful:
                return resentfulDialogues;
            default:
                return innocentDialogues;
        }
    }
    
    void UpdateChildBehavior()
    {
        // Update child's position and behavior based on emotional state
        if (childModel != null)
        {
            UpdateChildPosition();
            UpdateChildAnimation();
        }
    }
    
    void UpdateChildPosition()
    {
        float followDistance = CalculateFollowDistance();
        Vector3 targetPosition = transform.position - transform.forward * followDistance;
        
        if (childModel != null)
        {
            childModel.transform.position = Vector3.Lerp(childModel.transform.position, targetPosition, Time.deltaTime * 2f);
        }
    }
    
    float CalculateFollowDistance()
    {
        switch (currentEmotion)
        {
            case EmotionalState.Happy:
            case EmotionalState.Hopeful:
                return 2f; // Close to player
            case EmotionalState.Curious:
                return 3f; // Moderate distance
            case EmotionalState.Afraid:
                return 5f; // Farther away
            case EmotionalState.Angry:
                return 8f; // Very far
            default:
                return 3f;
        }
    }
    
    void UpdateChildAnimation()
    {
        Animator childAnimator = childModel?.GetComponent<Animator>();
        if (childAnimator == null) return;
        
        // Set animation parameters based on emotional state
        childAnimator.SetFloat("Fear", fearLevel / 100f);
        childAnimator.SetFloat("Happiness", Mathf.Max(0f, bondLevel / 100f));
        childAnimator.SetBool("IsAfraid", currentEmotion == EmotionalState.Afraid);
        childAnimator.SetBool("IsAngry", currentEmotion == EmotionalState.Angry);
    }
    
    public void WitnessAction(string actionName, WitnessedAction.ActionType actionType, float moralWeight)
    {
        WitnessedAction action = new WitnessedAction
        {
            actionName = actionName,
            type = actionType,
            moralWeight = moralWeight,
            emotionalImpact = CalculateEmotionalImpact(actionType, moralWeight),
            timestamp = Time.time
        };
        
        witnessedActions.Add(action);
        
        // Apply immediate effects
        ApplyActionEffects(action);
        
        // Generate child's reaction
        GenerateChildReaction(action);
    }
    
    float CalculateEmotionalImpact(WitnessedAction.ActionType actionType, float moralWeight)
    {
        float impact = Mathf.Abs(moralWeight) * 2f;
        
        // Children are more affected by certain actions
        switch (actionType)
        {
            case WitnessedAction.ActionType.KilledInnocent:
                impact *= 3f; // Very traumatic for child
                break;
            case WitnessedAction.ActionType.HelpedChild:
                impact *= 2f; // Very positive for child
                break;
            case WitnessedAction.ActionType.ActOfCruelty:
                impact *= 2.5f;
                break;
            case WitnessedAction.ActionType.ActOfKindness:
                impact *= 1.5f;
                break;
        }
        
        return impact;
    }
    
    void ApplyActionEffects(WitnessedAction action)
    {
        float impact = action.emotionalImpact;
        
        if (action.moralWeight > 0) // Good action
        {
            bondLevel += impact;
            admirationLevel += impact * 0.5f;
            fearLevel = Mathf.Max(0f, fearLevel - impact * 0.3f);
            resentmentLevel = Mathf.Max(0f, resentmentLevel - impact * 0.2f);
        }
        else // Bad action
        {
            bondLevel -= impact;
            fearLevel += impact * 0.7f;
            resentmentLevel += impact * 0.5f;
            admirationLevel = Mathf.Max(0f, admirationLevel - impact * 0.3f);
        }
        
        // Clamp values
        bondLevel = Mathf.Clamp(bondLevel, -100f, 100f);
        fearLevel = Mathf.Clamp(fearLevel, 0f, 100f);
        admirationLevel = Mathf.Clamp(admirationLevel, 0f, 100f);
        resentmentLevel = Mathf.Clamp(resentmentLevel, 0f, 100f);
    }
    
    void GenerateChildReaction(WitnessedAction action)
    {
        string reaction = "";
        
        switch (action.type)
        {
            case WitnessedAction.ActionType.KilledInnocent:
                reaction = "No... why did you do that? They weren't hurting anyone...";
                TransitionToFearful();
                break;
            
            case WitnessedAction.ActionType.ShowedMercy:
                reaction = "You spared them... you really are a good person.";
                break;
            
            case WitnessedAction.ActionType.HelpedChild:
                reaction = "You helped them! Just like you helped me!";
                break;
            
            case WitnessedAction.ActionType.ActOfCruelty:
                reaction = "That was... that was really mean. Why did you do that?";
                break;
            
            case WitnessedAction.ActionType.ActOfKindness:
                reaction = "That was really nice of you. You have a good heart.";
                break;
        }
        
        if (!string.IsNullOrEmpty(reaction))
        {
            ShowChildMessage($"{childName}: \"{reaction}\"");
        }
        
        action.childReaction = reaction;
    }
    
    void TransitionToAdmiring()
    {
        if (currentState != ChildState.Admiring)
        {
            currentState = ChildState.Admiring;
            ShowChildMessage($"{childName} looks at you with growing admiration and hope.");
        }
    }
    
    void TransitionToFearful()
    {
        if (currentState != ChildState.Fearful)
        {
            currentState = ChildState.Fearful;
            ShowChildMessage($"{childName} takes a step back, fear creeping into their eyes.");
        }
    }
    
    void TransitionToResentful()
    {
        if (currentState != ChildState.Resentful)
        {
            currentState = ChildState.Resentful;
            ShowChildMessage($"{childName}'s expression hardens with growing resentment and anger.");
        }
    }
    
    public void TriggerFinalChoice()
    {
        hasMetParents = true;
        StartCoroutine(FinalChoiceSequence());
    }
    
    IEnumerator FinalChoiceSequence()
    {
        ShowChildMessage("The moment of truth arrives. Your parents stand before you, and beside you stands the child you've raised.");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName}: \"So... these are your real parents? The ones who... who matter more than me?\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("Your parents: \"Come home, child. Your place is with your blood family.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("You must choose...");
        yield return new WaitForSeconds(2f);
        
        // Present choice to player
        PresentFinalChoice();
    }
    
    void PresentFinalChoice()
    {
        // This would integrate with the game's choice system
        ShowChildMessage("CHOICE: Accept your parents and abandon the child, or reject your parents and adopt the child?");
        
        // For demonstration, we'll simulate both outcomes
        // In actual implementation, this would wait for player input
    }
    
    public void MakeFinalChoice(FinalChoice choice)
    {
        playerFinalChoice = choice;
        finalChoiceMade = true;
        
        switch (choice)
        {
            case FinalChoice.AbandonChild:
                ExecuteAbandonmentEnding();
                break;
            case FinalChoice.AdoptChild:
                ExecuteAdoptionEnding();
                break;
            case FinalChoice.Conflicted:
                ExecuteConflictedEnding();
                break;
        }
    }
    
    void ExecuteAbandonmentEnding()
    {
        StartCoroutine(AbandonmentSequence());
    }
    
    IEnumerator AbandonmentSequence()
    {
        ShowChildMessage($"{childName}: \"I... I understand. Blood is thicker than the bonds we chose.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName}: \"But know this, Cinderborn... you have taught me that power is all that matters.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName}: \"One day, I will be strong enough to make you pay for this betrayal.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName} walks away, their small form disappearing into the shadows. You have gained a family, but created an enemy.");
        
        currentState = ChildState.Vengeful;
        
        // Set up future antagonist
        gameManager?.SetFutureAntagonist(childName, "Abandoned Child");
    }
    
    void ExecuteAdoptionEnding()
    {
        StartCoroutine(AdoptionSequence());
    }
    
    IEnumerator AdoptionSequence()
    {
        ShowChildMessage($"{childName}: \"You... you chose me? Even over your own family?\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName}: \"I don't know what to say... no one has ever chosen me before.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("Your parents: \"We understand. You have found your own family. We are proud of the person you have become.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"You place a protective hand on {childName}'s shoulder. You have lost your blood family, but gained something precious.");
        
        currentState = ChildState.Redeemed;
        bondLevel = 100f;
        
        // Unlock redemptive ending
        gameManager?.UnlockEnding("Redemptive Adoption");
    }
    
    void ExecuteConflictedEnding()
    {
        StartCoroutine(ConflictedSequence());
    }
    
    IEnumerator ConflictedSequence()
    {
        ShowChildMessage("You stand frozen, unable to choose between blood and bond.");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage($"{childName}: \"I see... I'm not worth choosing, am I?\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("Your parents: \"Come, child. This hesitation shows where your heart truly lies.\"");
        yield return new WaitForSeconds(3f);
        
        ShowChildMessage("In your indecision, you lose both. A tragic ending born of a heart too torn to choose.");
        
        // Trigger tragic ending
        gameManager?.UnlockEnding("Tragic Indecision");
    }
    
    void ShowChildMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 5f));
        }
        
        Debug.Log($"Child Companion: {message}");
    }
    
    void ShowChildDialogue(ChildDialogue dialogue)
    {
        ShowChildMessage($"{childName}: \"{dialogue.dialogueText}\"");
        
        if (dialogue.voiceLine != null)
        {
            AudioSource.PlayClipAtPoint(dialogue.voiceLine, transform.position);
        }
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public ChildState GetCurrentState() => currentState;
    public float GetBondLevel() => bondLevel;
    public MoralAlignment GetChildAlignment() => childAlignment;
    public EmotionalState GetCurrentEmotion() => currentEmotion;
    public bool HasMetParents() => hasMetParents;
    public FinalChoice GetFinalChoice() => playerFinalChoice;
    public List<WitnessedAction> GetWitnessedActions() => witnessedActions;
}
