using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using TMPro;
using Newtonsoft.Json;

/// <summary>
/// Arena Editor Manager for Cinder of Darkness
/// Handles the creation, editing, and management of custom arenas
/// Integrates with the modding system for save/load functionality
/// </summary>
public class ArenaEditorManager : MonoBehaviour
{
    [Header("UI References")]
    public GameObject arenaEditorUI;
    public GameObject mainMenuPanel;
    public GameObject editorPanel;
    public GameObject previewPanel;
    
    [Header("Main Menu UI")]
    public Button createNewArenaButton;
    public Button loadExistingArenaButton;
    public Button saveArenaButton;
    public Button testArenaButton;
    public Button exitEditorButton;
    public TMP_Dropdown existingArenasDropdown;
    
    [Header("Editor UI")]
    public TMP_InputField arenaNameField;
    public TMP_InputField arenaDescriptionField;
    public TMP_Dropdown biomeDropdown;
    public TMP_Dropdown winConditionDropdown;
    public TMP_InputField timeLimitField;
    public Slider difficultySlider;
    public Toggle allowMagicToggle;
    public Toggle allowHealingToggle;
    
    [Header("Preview Settings")]
    public Camera previewCamera;
    public Transform previewArea;
    public GameObject[] biomePrefabs;
    
    [Header("File Management")]
    public string arenasFolder = "Mods/Arenas";
    public string arenaFileExtension = ".arenamod";
    
    // Static instance
    private static ArenaEditorManager instance;
    public static ArenaEditorManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaEditorManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaEditorManager");
                    instance = go.AddComponent<ArenaEditorManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Arena editor state
    private ArenaData currentArena;
    private GameObject currentPreview;
    private List<string> availableArenas = new List<string>();
    private bool isEditorOpen = false;
    private bool hasUnsavedChanges = false;
    
    // Editor modes
    private EditorMode currentMode = EditorMode.Overview;
    
    public enum EditorMode
    {
        Overview,
        Layout,
        Environment,
        Gameplay,
        Testing
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeArenaEditor();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        CreateArenasDirectory();
        RefreshAvailableArenas();
    }
    
    void InitializeArenaEditor()
    {
        Debug.Log("Arena Editor Manager initialized");
    }
    
    void SetupUI()
    {
        // Setup main menu buttons
        if (createNewArenaButton != null)
            createNewArenaButton.onClick.AddListener(CreateNewArena);
        
        if (loadExistingArenaButton != null)
            loadExistingArenaButton.onClick.AddListener(LoadExistingArena);
        
        if (saveArenaButton != null)
            saveArenaButton.onClick.AddListener(SaveCurrentArena);
        
        if (testArenaButton != null)
            testArenaButton.onClick.AddListener(TestCurrentArena);
        
        if (exitEditorButton != null)
            exitEditorButton.onClick.AddListener(ExitArenaEditor);
        
        // Setup editor UI
        if (arenaNameField != null)
            arenaNameField.onValueChanged.AddListener(OnArenaNameChanged);
        
        if (arenaDescriptionField != null)
            arenaDescriptionField.onValueChanged.AddListener(OnArenaDescriptionChanged);
        
        if (biomeDropdown != null)
        {
            SetupBiomeDropdown();
            biomeDropdown.onValueChanged.AddListener(OnBiomeChanged);
        }
        
        if (winConditionDropdown != null)
        {
            SetupWinConditionDropdown();
            winConditionDropdown.onValueChanged.AddListener(OnWinConditionChanged);
        }
        
        if (timeLimitField != null)
            timeLimitField.onValueChanged.AddListener(OnTimeLimitChanged);
        
        if (difficultySlider != null)
            difficultySlider.onValueChanged.AddListener(OnDifficultyChanged);
        
        if (allowMagicToggle != null)
            allowMagicToggle.onValueChanged.AddListener(OnAllowMagicChanged);
        
        if (allowHealingToggle != null)
            allowHealingToggle.onValueChanged.AddListener(OnAllowHealingChanged);
        
        // Initially hide editor UI
        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(false);
    }
    
    void SetupBiomeDropdown()
    {
        if (biomeDropdown == null) return;
        
        var biomeNames = System.Enum.GetNames(typeof(ArenaData.BiomeType)).ToList();
        biomeDropdown.ClearOptions();
        biomeDropdown.AddOptions(biomeNames);
    }
    
    void SetupWinConditionDropdown()
    {
        if (winConditionDropdown == null) return;
        
        var winConditionNames = System.Enum.GetNames(typeof(ArenaData.WinConditionType)).ToList();
        winConditionDropdown.ClearOptions();
        winConditionDropdown.AddOptions(winConditionNames);
    }
    
    void CreateArenasDirectory()
    {
        string fullPath = Path.Combine(Application.persistentDataPath, arenasFolder);
        
        if (!Directory.Exists(fullPath))
        {
            Directory.CreateDirectory(fullPath);
            Debug.Log($"Created arenas directory: {fullPath}");
        }
    }
    
    void RefreshAvailableArenas()
    {
        availableArenas.Clear();
        string fullPath = Path.Combine(Application.persistentDataPath, arenasFolder);
        
        if (Directory.Exists(fullPath))
        {
            string[] arenaFiles = Directory.GetFiles(fullPath, $"*{arenaFileExtension}");
            
            foreach (string file in arenaFiles)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                availableArenas.Add(fileName);
            }
        }
        
        // Update dropdown
        if (existingArenasDropdown != null)
        {
            existingArenasDropdown.ClearOptions();
            existingArenasDropdown.AddOptions(availableArenas);
        }
        
        Debug.Log($"Found {availableArenas.Count} existing arenas");
    }
    
    // Public API
    public static bool IsArenaEditorOpen()
    {
        return Instance.isEditorOpen;
    }
    
    public static void OpenArenaEditor()
    {
        Instance.OpenArenaEditorInternal();
    }
    
    public static void CloseArenaEditor()
    {
        Instance.CloseArenaEditorInternal();
    }
    
    public static ArenaData GetCurrentArena()
    {
        return Instance.currentArena;
    }
    
    public static void LoadArenaForEditing(string arenaName)
    {
        Instance.LoadArenaForEditingInternal(arenaName);
    }
    
    void OpenArenaEditorInternal()
    {
        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(true);
        
        if (mainMenuPanel != null)
            mainMenuPanel.SetActive(true);
        
        if (editorPanel != null)
            editorPanel.SetActive(false);
        
        isEditorOpen = true;
        RefreshAvailableArenas();
        
        Debug.Log("Arena Editor opened");
    }
    
    void CloseArenaEditorInternal()
    {
        if (hasUnsavedChanges)
        {
            // Show save confirmation dialog
            if (ShowSaveConfirmationDialog())
            {
                SaveCurrentArena();
            }
        }
        
        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(false);
        
        CleanupPreview();
        isEditorOpen = false;
        hasUnsavedChanges = false;
        
        Debug.Log("Arena Editor closed");
    }
    
    bool ShowSaveConfirmationDialog()
    {
        // In a full implementation, this would show a proper dialog
        // For now, return true to auto-save
        return true;
    }
    
    // Arena Management
    void CreateNewArena()
    {
        currentArena = ScriptableObject.CreateInstance<ArenaData>();
        currentArena.arenaName = "New Arena";
        currentArena.arenaId = System.Guid.NewGuid().ToString();
        currentArena.author = "Player";
        currentArena.ApplyBiomeDefaults();
        
        SwitchToEditorMode();
        UpdateUIFromArena();
        UpdatePreview();
        
        hasUnsavedChanges = false;
        Debug.Log("Created new arena");
    }
    
    void LoadExistingArena()
    {
        if (existingArenasDropdown == null || availableArenas.Count == 0) return;
        
        string selectedArena = availableArenas[existingArenasDropdown.value];
        LoadArenaForEditingInternal(selectedArena);
    }
    
    void LoadArenaForEditingInternal(string arenaName)
    {
        string filePath = Path.Combine(Application.persistentDataPath, arenasFolder, arenaName + arenaFileExtension);
        
        if (!File.Exists(filePath))
        {
            Debug.LogError($"Arena file not found: {filePath}");
            return;
        }
        
        try
        {
            string json = File.ReadAllText(filePath);
            var arenaJson = JsonConvert.DeserializeObject<ArenaDataJson>(json);
            
            currentArena = ConvertFromJson(arenaJson);
            
            SwitchToEditorMode();
            UpdateUIFromArena();
            UpdatePreview();
            
            hasUnsavedChanges = false;
            Debug.Log($"Loaded arena: {arenaName}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load arena {arenaName}: {e.Message}");
        }
    }
    
    void SaveCurrentArena()
    {
        if (currentArena == null)
        {
            Debug.LogWarning("No arena to save");
            return;
        }
        
        if (!currentArena.ValidateArenaData())
        {
            Debug.LogError("Arena validation failed");
            return;
        }
        
        string fileName = SanitizeFileName(currentArena.arenaName);
        string filePath = Path.Combine(Application.persistentDataPath, arenasFolder, fileName + arenaFileExtension);
        
        try
        {
            var arenaJson = ConvertToJson(currentArena);
            string json = JsonConvert.SerializeObject(arenaJson, Formatting.Indented);
            
            File.WriteAllText(filePath, json);
            
            hasUnsavedChanges = false;
            RefreshAvailableArenas();
            
            Debug.Log($"Arena saved: {fileName}");
            
            // Notify modding system
            var moddingSystem = ModdingSystem.Instance;
            if (moddingSystem != null)
            {
                moddingSystem.ScanForMods();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save arena: {e.Message}");
        }
    }
    
    void TestCurrentArena()
    {
        if (currentArena == null)
        {
            Debug.LogWarning("No arena to test");
            return;
        }
        
        // Save arena first
        SaveCurrentArena();
        
        // Switch to sandbox mode and load arena
        SandboxMode.EnableSandboxMode(true);
        
        // Load arena in sandbox for testing
        TestArenaInSandbox();
        
        Debug.Log($"Testing arena: {currentArena.arenaName}");
    }
    
    void TestArenaInSandbox()
    {
        // Implementation would load the arena in sandbox mode
        // For now, just log the action
        Debug.Log("Arena loaded in sandbox mode for testing");
        
        // Close editor temporarily
        CloseArenaEditorInternal();
    }
    
    void ExitArenaEditor()
    {
        CloseArenaEditorInternal();
    }
    
    void SwitchToEditorMode()
    {
        if (mainMenuPanel != null)
            mainMenuPanel.SetActive(false);
        
        if (editorPanel != null)
            editorPanel.SetActive(true);
        
        currentMode = EditorMode.Overview;
    }
    
    void UpdateUIFromArena()
    {
        if (currentArena == null) return;
        
        if (arenaNameField != null)
            arenaNameField.text = currentArena.arenaName;
        
        if (arenaDescriptionField != null)
            arenaDescriptionField.text = currentArena.description;
        
        if (biomeDropdown != null)
            biomeDropdown.value = (int)currentArena.biomeType;
        
        if (winConditionDropdown != null)
            winConditionDropdown.value = (int)currentArena.winCondition;
        
        if (timeLimitField != null)
            timeLimitField.text = currentArena.timeLimitSeconds.ToString();
        
        if (difficultySlider != null)
            difficultySlider.value = currentArena.difficultyMultiplier;
        
        if (allowMagicToggle != null)
            allowMagicToggle.isOn = currentArena.allowMagic;
        
        if (allowHealingToggle != null)
            allowHealingToggle.isOn = currentArena.allowHealing;
    }
    
    void UpdatePreview()
    {
        CleanupPreview();
        
        if (currentArena == null || previewArea == null) return;
        
        // Load biome prefab for preview
        GameObject biomePrefab = GetBiomePrefab(currentArena.biomeType);
        if (biomePrefab != null)
        {
            currentPreview = Instantiate(biomePrefab, previewArea.position, previewArea.rotation);
            
            // Apply arena settings to preview
            ApplyArenaSettingsToPreview();
            
            // Position preview camera
            if (previewCamera != null)
            {
                PositionPreviewCamera();
            }
        }
    }
    
    GameObject GetBiomePrefab(ArenaData.BiomeType biomeType)
    {
        if (biomePrefabs == null || biomePrefabs.Length == 0) return null;
        
        int index = (int)biomeType;
        if (index >= 0 && index < biomePrefabs.Length)
        {
            return biomePrefabs[index];
        }
        
        return null;
    }
    
    void ApplyArenaSettingsToPreview()
    {
        if (currentPreview == null || currentArena == null) return;
        
        // Apply fog settings
        if (currentArena.fogSettings.enableFog)
        {
            RenderSettings.fog = true;
            RenderSettings.fogColor = currentArena.fogSettings.fogColor;
            RenderSettings.fogMode = currentArena.fogSettings.fogMode;
            RenderSettings.fogDensity = currentArena.fogSettings.fogDensity;
        }
        
        // Apply lighting settings
        RenderSettings.ambientLight = currentArena.lightingSettings.ambientColor;
        
        var directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.color = currentArena.lightingSettings.directionalLightColor;
            directionalLight.intensity = currentArena.lightingSettings.directionalLightIntensity;
            directionalLight.transform.eulerAngles = currentArena.lightingSettings.directionalLightRotation;
        }
    }
    
    void PositionPreviewCamera()
    {
        if (previewCamera == null || currentArena == null) return;
        
        Vector3 arenaCenter = currentArena.playerSpawnPoint;
        Vector3 cameraOffset = new Vector3(0, currentArena.arenaSize.y * 0.5f, -currentArena.arenaSize.z * 0.3f);
        
        previewCamera.transform.position = arenaCenter + cameraOffset;
        previewCamera.transform.LookAt(arenaCenter);
    }
    
    void CleanupPreview()
    {
        if (currentPreview != null)
        {
            DestroyImmediate(currentPreview);
            currentPreview = null;
        }
        
        // Reset render settings
        RenderSettings.fog = false;
    }
    
    // UI Event Handlers
    void OnArenaNameChanged(string newName)
    {
        if (currentArena != null)
        {
            currentArena.arenaName = newName;
            hasUnsavedChanges = true;
        }
    }
    
    void OnArenaDescriptionChanged(string newDescription)
    {
        if (currentArena != null)
        {
            currentArena.description = newDescription;
            hasUnsavedChanges = true;
        }
    }
    
    void OnBiomeChanged(int biomeIndex)
    {
        if (currentArena != null)
        {
            currentArena.biomeType = (ArenaData.BiomeType)biomeIndex;
            currentArena.ApplyBiomeDefaults();
            UpdatePreview();
            hasUnsavedChanges = true;
        }
    }
    
    void OnWinConditionChanged(int conditionIndex)
    {
        if (currentArena != null)
        {
            currentArena.winCondition = (ArenaData.WinConditionType)conditionIndex;
            hasUnsavedChanges = true;
        }
    }
    
    void OnTimeLimitChanged(string newTimeLimit)
    {
        if (currentArena != null && float.TryParse(newTimeLimit, out float timeLimit))
        {
            currentArena.timeLimitSeconds = timeLimit;
            hasUnsavedChanges = true;
        }
    }
    
    void OnDifficultyChanged(float newDifficulty)
    {
        if (currentArena != null)
        {
            currentArena.difficultyMultiplier = newDifficulty;
            hasUnsavedChanges = true;
        }
    }
    
    void OnAllowMagicChanged(bool allowMagic)
    {
        if (currentArena != null)
        {
            currentArena.allowMagic = allowMagic;
            hasUnsavedChanges = true;
        }
    }
    
    void OnAllowHealingChanged(bool allowHealing)
    {
        if (currentArena != null)
        {
            currentArena.allowHealing = allowHealing;
            hasUnsavedChanges = true;
        }
    }
    
    // JSON Conversion (for file serialization)
    ArenaDataJson ConvertToJson(ArenaData arena)
    {
        return new ArenaDataJson
        {
            modId = arena.arenaId,
            name = arena.arenaName,
            author = arena.author,
            version = arena.version,
            description = arena.description,
            arenaData = arena
        };
    }
    
    ArenaData ConvertFromJson(ArenaDataJson arenaJson)
    {
        return arenaJson.arenaData;
    }
    
    string SanitizeFileName(string fileName)
    {
        string sanitized = fileName;
        char[] invalidChars = Path.GetInvalidFileNameChars();
        
        foreach (char c in invalidChars)
        {
            sanitized = sanitized.Replace(c, '_');
        }
        
        return sanitized;
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Handle game events if needed
    }
    
    void Update()
    {
        // Toggle Arena Editor with F4 (if unlocked)
        if (Input.GetKeyDown(KeyCode.F4))
        {
            if (ModdingSystem.IsToolUnlocked("arena_creator"))
            {
                if (IsArenaEditorOpen())
                    CloseArenaEditorInternal();
                else
                    OpenArenaEditorInternal();
            }
        }
    }
    
    // JSON serialization structure
    [System.Serializable]
    public class ArenaDataJson
    {
        public string modId;
        public string name;
        public string author;
        public string version;
        public string description;
        public ArenaData arenaData;
    }
}
