using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;

namespace CinderOfDarkness.Map
{
    /// <summary>
    /// Map Marker UI component for displaying markers on the world map.
    /// Handles marker interaction and visual representation.
    /// </summary>
    public class MapMarkerUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerClickHandler, IPointerEnterHandler, IPointerExitHandler
    {
        #region Serialized Fields
        [Header("UI Components")]
        [SerializeField] private Image markerIcon;
        [SerializeField] private TextMeshProUGUI markerTitle;
        [SerializeField] private GameObject tooltipPanel;
        [SerializeField] private TextMeshProUGUI tooltipTitle;
        [SerializeField] private TextMeshProUGUI tooltipDescription;
        [SerializeField] private Button deleteButton;

        [Header("Visual Settings")]
        [SerializeField] private float hoverScale = 1.2f;
        [SerializeField] private float animationDuration = 0.2f;
        [SerializeField] private Color hoverColor = Color.yellow;

        [Header("Audio")]
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip clickSound;
        #endregion

        #region Public Properties
        public MapMarker MarkerData { get; private set; }
        public bool IsHovered { get; private set; }
        #endregion

        #region Private Fields
        private Vector3 originalScale;
        private Color originalColor;
        private AudioSource audioSource;
        private bool isInitialized = false;
        #endregion

        #region Events
        public System.Action<MapMarker> OnMarkerClicked;
        public System.Action<MapMarker> OnMarkerDeleted;
        public System.Action<MapMarker> OnMarkerHovered;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            originalScale = transform.localScale;
            audioSource = GetComponent<AudioSource>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }

            // Setup delete button
            if (deleteButton != null)
            {
                deleteButton.onClick.AddListener(OnDeleteButtonClicked);
                deleteButton.gameObject.SetActive(false);
            }

            // Hide tooltip initially
            if (tooltipPanel != null)
            {
                tooltipPanel.SetActive(false);
            }
        }

        private void Start()
        {
            if (markerIcon != null)
            {
                originalColor = markerIcon.color;
            }
        }
        #endregion

        #region Setup
        /// <summary>
        /// Setup marker with data.
        /// </summary>
        /// <param name="marker">Marker data</param>
        public void Setup(MapMarker marker)
        {
            MarkerData = marker;
            UpdateVisuals();
            isInitialized = true;
        }

        /// <summary>
        /// Update marker visuals based on data.
        /// </summary>
        private void UpdateVisuals()
        {
            if (MarkerData == null) return;

            // Update icon
            if (markerIcon != null && MarkerData.markerType.icon != null)
            {
                markerIcon.sprite = MarkerData.markerType.icon;
                markerIcon.color = MarkerData.markerType.color;
                originalColor = markerIcon.color;
            }

            // Update title
            if (markerTitle != null)
            {
                markerTitle.text = MarkerData.title;
            }

            // Update tooltip
            UpdateTooltip();

            // Show delete button for custom markers
            if (deleteButton != null)
            {
                deleteButton.gameObject.SetActive(MarkerData.isCustom);
            }

            // Set visibility
            gameObject.SetActive(MarkerData.isVisible);
        }

        /// <summary>
        /// Update tooltip content.
        /// </summary>
        private void UpdateTooltip()
        {
            if (tooltipTitle != null)
            {
                tooltipTitle.text = MarkerData.title;
            }

            if (tooltipDescription != null)
            {
                string description = MarkerData.description;
                if (string.IsNullOrEmpty(description))
                {
                    description = $"Marker Type: {MarkerData.markerType.typeName}";
                }
                tooltipDescription.text = description;
            }
        }
        #endregion

        #region Interaction Handlers
        /// <summary>
        /// Handle pointer click events.
        /// </summary>
        /// <param name="eventData">Pointer event data</param>
        public void OnPointerClick(PointerEventData eventData)
        {
            if (!isInitialized || MarkerData == null) return;

            PlaySound(clickSound);
            OnMarkerClicked?.Invoke(MarkerData);

            // Could implement marker-specific actions here
            switch (MarkerData.markerType.typeName.ToLower())
            {
                case "quest":
                    HandleQuestMarkerClick();
                    break;
                case "shop":
                    HandleShopMarkerClick();
                    break;
                case "waypoint":
                    HandleWaypointMarkerClick();
                    break;
                default:
                    HandleGenericMarkerClick();
                    break;
            }
        }

        /// <summary>
        /// Handle pointer enter events.
        /// </summary>
        /// <param name="eventData">Pointer event data</param>
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (!isInitialized) return;

            IsHovered = true;
            PlaySound(hoverSound);
            
            // Scale animation
            LeanTween.scale(gameObject, originalScale * hoverScale, animationDuration)
                .setEase(LeanTweenType.easeOutBack);

            // Color change
            if (markerIcon != null)
            {
                LeanTween.value(gameObject, originalColor, hoverColor, animationDuration)
                    .setOnUpdate((Color color) => markerIcon.color = color);
            }

            // Show tooltip
            if (tooltipPanel != null)
            {
                tooltipPanel.SetActive(true);
                UpdateTooltipPosition();
            }

            OnMarkerHovered?.Invoke(MarkerData);
        }

        /// <summary>
        /// Handle pointer exit events.
        /// </summary>
        /// <param name="eventData">Pointer event data</param>
        public void OnPointerExit(PointerEventData eventData)
        {
            if (!isInitialized) return;

            IsHovered = false;

            // Scale animation
            LeanTween.scale(gameObject, originalScale, animationDuration)
                .setEase(LeanTweenType.easeOutBack);

            // Color change
            if (markerIcon != null)
            {
                LeanTween.value(gameObject, markerIcon.color, originalColor, animationDuration)
                    .setOnUpdate((Color color) => markerIcon.color = color);
            }

            // Hide tooltip
            if (tooltipPanel != null)
            {
                tooltipPanel.SetActive(false);
            }
        }

        /// <summary>
        /// Handle delete button click.
        /// </summary>
        private void OnDeleteButtonClicked()
        {
            if (MarkerData != null && MarkerData.isCustom)
            {
                OnMarkerDeleted?.Invoke(MarkerData);
            }
        }
        #endregion

        #region Marker Type Handlers
        /// <summary>
        /// Handle quest marker click.
        /// </summary>
        private void HandleQuestMarkerClick()
        {
            Debug.Log($"Quest marker clicked: {MarkerData.title}");
            // Could open quest details or navigate to quest location
        }

        /// <summary>
        /// Handle shop marker click.
        /// </summary>
        private void HandleShopMarkerClick()
        {
            Debug.Log($"Shop marker clicked: {MarkerData.title}");
            // Could show shop information or set navigation target
        }

        /// <summary>
        /// Handle waypoint marker click.
        /// </summary>
        private void HandleWaypointMarkerClick()
        {
            Debug.Log($"Waypoint marker clicked: {MarkerData.title}");
            // Could set as navigation target or fast travel destination
        }

        /// <summary>
        /// Handle generic marker click.
        /// </summary>
        private void HandleGenericMarkerClick()
        {
            Debug.Log($"Marker clicked: {MarkerData.title}");
            // Generic marker interaction
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Update tooltip position relative to marker.
        /// </summary>
        private void UpdateTooltipPosition()
        {
            if (tooltipPanel == null) return;

            RectTransform tooltipRect = tooltipPanel.GetComponent<RectTransform>();
            if (tooltipRect != null)
            {
                // Position tooltip above marker
                Vector3 markerPosition = transform.position;
                Vector3 tooltipPosition = markerPosition + Vector3.up * 50f;
                tooltipRect.position = tooltipPosition;

                // Ensure tooltip stays within screen bounds
                Canvas canvas = GetComponentInParent<Canvas>();
                if (canvas != null)
                {
                    RectTransform canvasRect = canvas.GetComponent<RectTransform>();
                    Vector3[] corners = new Vector3[4];
                    tooltipRect.GetWorldCorners(corners);

                    // Check if tooltip goes outside canvas bounds and adjust
                    if (corners[2].x > canvasRect.rect.width)
                    {
                        tooltipPosition.x -= (corners[2].x - canvasRect.rect.width);
                    }
                    if (corners[0].x < 0)
                    {
                        tooltipPosition.x -= corners[0].x;
                    }
                    if (corners[2].y > canvasRect.rect.height)
                    {
                        tooltipPosition.y = markerPosition.y - 50f; // Position below marker
                    }

                    tooltipRect.position = tooltipPosition;
                }
            }
        }

        /// <summary>
        /// Play audio clip.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }

        /// <summary>
        /// Set marker visibility.
        /// </summary>
        /// <param name="visible">Visibility state</param>
        public void SetVisible(bool visible)
        {
            if (MarkerData != null)
            {
                MarkerData.isVisible = visible;
            }
            gameObject.SetActive(visible);
        }

        /// <summary>
        /// Update marker data and refresh visuals.
        /// </summary>
        /// <param name="newData">New marker data</param>
        public void UpdateMarkerData(MapMarker newData)
        {
            MarkerData = newData;
            UpdateVisuals();
        }

        /// <summary>
        /// Get distance to player.
        /// </summary>
        /// <returns>Distance to player in world units</returns>
        public float GetDistanceToPlayer()
        {
            if (MarkerData == null) return float.MaxValue;

            Transform player = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (player != null)
            {
                return Vector3.Distance(MarkerData.worldPosition, player.position);
            }

            return float.MaxValue;
        }

        /// <summary>
        /// Check if marker is within specified range of player.
        /// </summary>
        /// <param name="range">Range to check</param>
        /// <returns>True if within range</returns>
        public bool IsWithinRangeOfPlayer(float range)
        {
            return GetDistanceToPlayer() <= range;
        }
        #endregion

        #region Animation Methods
        /// <summary>
        /// Play marker appearance animation.
        /// </summary>
        public void PlayAppearAnimation()
        {
            transform.localScale = Vector3.zero;
            LeanTween.scale(gameObject, originalScale, animationDuration)
                .setEase(LeanTweenType.easeOutBack);

            if (markerIcon != null)
            {
                Color transparentColor = originalColor;
                transparentColor.a = 0f;
                markerIcon.color = transparentColor;
                
                LeanTween.value(gameObject, 0f, originalColor.a, animationDuration)
                    .setOnUpdate((float alpha) => {
                        Color color = originalColor;
                        color.a = alpha;
                        markerIcon.color = color;
                    });
            }
        }

        /// <summary>
        /// Play marker disappearance animation.
        /// </summary>
        /// <param name="onComplete">Callback when animation completes</param>
        public void PlayDisappearAnimation(System.Action onComplete = null)
        {
            LeanTween.scale(gameObject, Vector3.zero, animationDuration)
                .setEase(LeanTweenType.easeInBack)
                .setOnComplete(() => {
                    gameObject.SetActive(false);
                    onComplete?.Invoke();
                });

            if (markerIcon != null)
            {
                LeanTween.value(gameObject, markerIcon.color.a, 0f, animationDuration)
                    .setOnUpdate((float alpha) => {
                        Color color = markerIcon.color;
                        color.a = alpha;
                        markerIcon.color = color;
                    });
            }
        }

        /// <summary>
        /// Play pulse animation for important markers.
        /// </summary>
        public void PlayPulseAnimation()
        {
            LeanTween.scale(gameObject, originalScale * 1.1f, 0.5f)
                .setEase(LeanTweenType.easeInOutSine)
                .setLoopPingPong();
        }

        /// <summary>
        /// Stop all animations.
        /// </summary>
        public void StopAnimations()
        {
            LeanTween.cancel(gameObject);
            transform.localScale = originalScale;
            if (markerIcon != null)
            {
                markerIcon.color = originalColor;
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set marker as selected.
        /// </summary>
        /// <param name="selected">Selection state</param>
        public void SetSelected(bool selected)
        {
            if (selected)
            {
                // Add selection visual feedback
                if (markerIcon != null)
                {
                    markerIcon.color = Color.cyan;
                }
                transform.localScale = originalScale * 1.15f;
            }
            else
            {
                // Remove selection visual feedback
                if (markerIcon != null)
                {
                    markerIcon.color = originalColor;
                }
                transform.localScale = originalScale;
            }
        }

        /// <summary>
        /// Get marker world position.
        /// </summary>
        /// <returns>World position of marker</returns>
        public Vector3 GetWorldPosition()
        {
            return MarkerData?.worldPosition ?? Vector3.zero;
        }

        /// <summary>
        /// Check if marker is custom (player-created).
        /// </summary>
        /// <returns>True if custom marker</returns>
        public bool IsCustomMarker()
        {
            return MarkerData?.isCustom ?? false;
        }
        #endregion
    }
}
