# MOMO - Game Design Document

## 🎮 Game Overview

**Title**: MOMO  
**Genre**: 3D Souls-like Action RPG  
**Platform**: PC (Windows, Mac, Linux)  
**Target Rating**: M (Mature 17+)  
**Development Time**: 18-24 months (full version)  
**Team Size**: 5-8 developers  

### Core Vision
A dark, narrative-driven Souls-like game where a 15-year-old orphaned boy discovers his destiny as the child of opposing cosmic forces. Every choice shapes not only the story but the very nature of the world itself.

## 📖 Narrative Design

### Main Story
**Protagonist**: <PERSON><PERSON>, a 15-year-old orphan rejected and hated by society  
**Setting**: Aethermoor, a dark medieval fantasy realm  
**Central Conflict**: The eternal struggle between Light (Sun) and Darkness (Moon)  

### Three-Act Structure

#### Act I: The Outcast (Levels 1-10)
- **Opening**: <PERSON><PERSON> is cast out from his village after a mysterious incident
- **Inciting Incident**: Discovery of supernatural abilities during a bandit attack
- **First Choice**: Save the village that rejected him or let it burn
- **Act Climax**: First encounter with a Sage's emissary

#### Act II: The Awakening (Levels 11-25)
- **Rising Action**: Journey through various kingdoms, each representing different moral philosophies
- **Midpoint**: Discovery of true parentage (<PERSON> Sage mother, <PERSON> Sage father)
- **Crisis**: Forced to choose between saving one parent or the other
- **Act Climax**: Confrontation with the first major faction leader

#### Act III: The Convergence (Levels 26-40)
- **Final Choice**: Embrace Sun, Moon, or forge the Eclipse path
- **Climax**: Battle against AI mirror of player's choices and combat style
- **Resolution**: Multiple endings based on player's moral journey

### Character Development

#### Kael's Journey
- **Physical**: From weak outcast to powerful warrior/mage
- **Emotional**: From bitter hatred to chosen philosophy (compassion/power/balance)
- **Spiritual**: Discovery of divine heritage and cosmic responsibility

#### Supporting Characters
- **Elder Theron**: Wise village elder, potential mentor or enemy
- **Gareth the Trader**: Morally ambiguous merchant, alliance or betrayal
- **Lady Seraphina**: Sun Sage's champion, represents pure light
- **Lord Malachar**: Moon Sage's champion, represents necessary darkness
- **The Twins**: Eclipse followers who show the balance path

## ⚔️ Combat System

### Core Mechanics
- **Real-time combat** inspired by Dark Souls
- **Stamina-based** system preventing button mashing
- **Risk/reward** mechanics with timing and positioning
- **Moral alignment** affects available abilities

### Weapon Categories

#### Light Weapons (Sun-aligned)
- **Rapier**: Fast, precise, low damage
- **Blessed Blade**: Heals user on critical hits
- **Sunburst Spear**: Long reach, anti-undead bonus

#### Heavy Weapons (Moon-aligned)
- **Greatsword**: Slow, devastating damage
- **Cursed Mace**: Drains enemy health
- **Shadow Axe**: Ignores armor, causes fear

#### Balanced Weapons (Eclipse-aligned)
- **Katana**: Medium speed, high critical chance
- **Staff-Blade**: Melee/magic hybrid
- **Twin Daggers**: Dual-wielding, combo system

### Magic Schools

#### Sun Magic (Light)
- **Healing**: Restore health and cure ailments
- **Protection**: Barriers and damage reduction
- **Purification**: Remove curses and debuffs
- **Divine Wrath**: Powerful but costly offensive spells

#### Moon Magic (Darkness)
- **Necromancy**: Raise undead allies
- **Curses**: Debuff enemies and drain stats
- **Shadow**: Stealth and illusion magic
- **Blood Magic**: Powerful spells using health as cost

#### Eclipse Magic (Balance)
- **Transmutation**: Change enemy types temporarily
- **Binding**: Control enemy actions
- **Reflection**: Turn enemy attacks back on them
- **Harmony**: Combine light and dark effects

## 🌍 World Design

### Regions

#### The Outlands (Starting Area)
- **Theme**: Rejection and survival
- **Enemies**: Bandits, wild animals, desperate outcasts
- **Key Locations**: Abandoned village, hidden caves, merchant camps
- **Moral Choices**: Help fellow outcasts or exploit their desperation

#### The Sunward Kingdoms
- **Theme**: Order, justice, rigid morality
- **Enemies**: Corrupted paladins, zealots, fallen angels
- **Key Locations**: Golden cathedral, court of justice, healing sanctuaries
- **Moral Choices**: Uphold law vs. show mercy, tradition vs. progress

#### The Moonward Territories
- **Theme**: Power, freedom, moral ambiguity
- **Enemies**: Demons, dark knights, enslaved souls
- **Key Locations**: Shadow fortress, blood markets, necromantic towers
- **Moral Choices**: Seize power vs. protect innocents, ends vs. means

#### The Eclipse Sanctum
- **Theme**: Balance, wisdom, difficult truths
- **Enemies**: Extremists from both sides, corrupted balance-keepers
- **Key Locations**: Floating monastery, mirror lakes, twilight gardens
- **Moral Choices**: Maintain balance vs. take decisive action

### Environmental Storytelling
- **Visual Narratives**: Each area tells stories through architecture and decay
- **Hidden Lore**: Discoverable texts, murals, and artifacts
- **Dynamic Changes**: World reflects player's moral choices
- **Atmospheric Details**: Weather, lighting, and sound reinforce themes

## 🎭 Moral Choice System

### Alignment Mechanics
- **Sun Points**: Gained through compassion, self-sacrifice, forgiveness
- **Moon Points**: Gained through pragmatism, self-interest, revenge
- **Eclipse Balance**: Requires maintaining both alignments

### Choice Consequences

#### Immediate Effects
- **Dialogue Options**: Different responses available based on alignment
- **NPC Reactions**: Characters respond to player's reputation
- **Ability Access**: Certain spells/weapons locked by alignment
- **World State**: Environmental changes reflect moral choices

#### Long-term Impact
- **Story Branches**: Major plot points change based on alignment
- **Ending Variations**: Multiple conclusions based on player journey
- **Character Fates**: NPCs live, die, or transform based on choices
- **Legacy System**: Choices affect subsequent playthroughs

### Example Moral Dilemmas

#### The Starving Village
- **Sun Choice**: Share your food, go hungry yourself
- **Moon Choice**: Demand payment for food, profit from desperation
- **Eclipse Choice**: Teach them to fish, sustainable but slower solution

#### The Corrupt Noble
- **Sun Choice**: Expose publicly, risk innocent casualties
- **Moon Choice**: Blackmail for personal gain
- **Eclipse Choice**: Work within system to gradually reform

#### The Dying Enemy
- **Sun Choice**: Heal them despite past wrongs
- **Moon Choice**: Let them die, take their equipment
- **Eclipse Choice**: Offer quick, merciful death

## 🤖 AI Mirror Boss System

### Learning Mechanics
- **Combat Pattern Recognition**: AI learns player's preferred attacks
- **Timing Analysis**: Adapts to player's reaction speeds
- **Strategy Mimicry**: Copies successful player tactics
- **Moral Reflection**: Uses player's alignment against them

### Adaptive Behavior
- **Weapon Mastery**: Mirrors player's weapon preferences
- **Magic Usage**: Casts spells player has used most
- **Movement Patterns**: Adopts player's positioning habits
- **Psychological Warfare**: Taunts based on player's moral choices

### Boss Phases
1. **Recognition**: AI studies player during initial combat
2. **Mimicry**: Copies player's exact fighting style
3. **Improvement**: Executes player's tactics more efficiently
4. **Transcendence**: Combines all learned behaviors into perfect form

## 🎵 Audio Design

### Dynamic Music System
- **Adaptive Scoring**: Music changes based on moral alignment
- **Combat Themes**: Different tracks for each weapon/magic type
- **Emotional Resonance**: Music reflects story beats and choices
- **Cultural Motifs**: Each region has distinct musical identity

### Voice Acting
- **Archaic Language**: Elden Ring-inspired dialogue style
- **Emotional Range**: Voice actors convey moral complexity
- **Multiple Takes**: Different line readings based on player alignment
- **Ambient Voices**: Background conversations reveal world state

### Sound Effects
- **Weapon Feedback**: Each weapon type has unique audio signature
- **Magic Resonance**: Spells sound different based on alignment
- **Environmental Audio**: World sounds reflect moral state
- **UI Feedback**: Interface sounds reinforce player choices

## 🎨 Visual Design

### Art Direction
- **Dark Fantasy**: Gritty, realistic medieval aesthetic
- **Moral Visualization**: Colors and lighting reflect alignment
- **Symbolic Imagery**: Sun, moon, and eclipse motifs throughout
- **Character Design**: Reflects moral journey through appearance

### Technical Specifications
- **Engine**: Unity 3D with Universal Render Pipeline
- **Target Resolution**: 1080p/60fps minimum, 4K/60fps optimal
- **Lighting**: Dynamic lighting system reflecting moral choices
- **Post-Processing**: Atmospheric effects enhance mood

## 📊 Progression Systems

### Character Development
- **Level-based**: Traditional XP system with moral modifiers
- **Skill Trees**: Separate trees for Sun, Moon, and Eclipse abilities
- **Equipment Mastery**: Weapons improve with use
- **Moral Evolution**: Alignment affects available progression paths

### Difficulty Scaling
- **Adaptive Challenge**: Game adjusts to player skill level
- **Moral Consequences**: Some choices make game harder/easier
- **New Game Plus**: Carry over certain progress, face new challenges
- **Achievement System**: Rewards for various moral paths and playstyles

## 🎯 Target Audience

### Primary Audience
- **Age**: 18-35 years old
- **Gaming Experience**: Familiar with Souls-like games
- **Interests**: Narrative-driven games, moral choice systems
- **Platform Preference**: PC gaming enthusiasts

### Secondary Audience
- **RPG Enthusiasts**: Players who enjoy character progression
- **Story Seekers**: Gamers who prioritize narrative
- **Challenge Seekers**: Players who enjoy difficult combat
- **Replay Value Seekers**: Gamers who replay for different outcomes

## 🚀 Post-Launch Content

### DLC Expansions
1. **The Sage Wars**: Prequel showing the original conflict
2. **The Other Realms**: Explore demon and angel homelands
3. **The Eclipse Prophecy**: Extended ending content
4. **The Mirror Shards**: Additional AI boss encounters

### Community Features
- **Moral Choice Statistics**: Global data on player decisions
- **Build Sharing**: Share character builds and strategies
- **Screenshot Mode**: Capture and share memorable moments
- **Speedrun Support**: Tools for competitive play

This design document provides the foundation for creating a deep, meaningful gaming experience that challenges players not just mechanically, but morally and emotionally.
