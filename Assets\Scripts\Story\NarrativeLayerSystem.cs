using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class NarrativeLayerSystem : MonoBehaviour
{
    [Header("Story Progression")]
    public float currentStoryProgress = 0f; // 0 to 1
    public StoryArc currentArc = StoryArc.Awakening;
    public List<string> completedEvents = new List<string>();
    
    [Header("Hidden Boss System")]
    public HiddenBossData[] hiddenBosses;
    public List<string> revealedBosses = new List<string>();
    
    [<PERSON>er("Philosophical Themes")]
    public PhilosophicalQuote[] philosophicalQuotes;
    public float lastPhilosophyTime = 0f;
    
    private PlayerStats playerStats;
    private HostilitySystem hostilitySystem;
    private PsychologicalSystem psycheSystem;
    private GameManager gameManager;
    
    public enum StoryArc
    {
        Awakening,      // Tutorial and early game
        Discovery,      // Learning about heritage
        Conflict,       // Major moral choices
        Revelation,     // Truth about parentage
        Convergence,    // Final choices and alliances
        Judgment        // Endgame and consequences
    }
    
    [System.Serializable]
    public class HiddenBossData
    {
        [Header("Identity")]
        public string characterName;
        public string friendlyName;
        public string bossTitle;
        public BossType bossType;
        
        [Header("Disguise")]
        public string initialRole; // "<PERSON>tor", "<PERSON>", "<PERSON>", etc.
        public GameObject friendlyModel;
        public GameObject bossModel;
        
        [Header("Foreshadowing")]
        public ForeshadowingClue[] clues;
        public string[] crypticDialogue;
        public TriggerCondition revealCondition;
        
        [Header("Boss Fight")]
        public float bossHealth;
        public string[] bossPhases;
        public string defeatConsequence;
        public string spareConsequence;
        
        public enum BossType
        {
            FallenMentor,       // The old mentor in the forest
            CorruptedNoble,     // The noble swordsman who transforms
            DemonLord,          // Father of the possessed swordsman
            FallenAngel,        // The angel who may cut player's arm
            VillageElder,       // Betrayal boss fight
            RaisedMother        // The old woman who raised the player
        }
    }
    
    [System.Serializable]
    public class ForeshadowingClue
    {
        public string clueText;
        public ClueType type;
        public float storyProgressRequired;
        public bool playerNoticed;
        
        public enum ClueType
        {
            Dialogue,
            Visual,
            Audio,
            Environmental,
            Behavioral
        }
    }
    
    [System.Serializable]
    public class TriggerCondition
    {
        public float storyProgress;
        public PlayerStats.MoralPath requiredPath;
        public string[] requiredEvents;
        public bool playerMustDiscover;
    }
    
    [System.Serializable]
    public class PhilosophicalQuote
    {
        public string quote;
        public string context;
        public QuoteTheme theme;
        public float storyProgressRequired;
        
        public enum QuoteTheme
        {
            Power,
            Redemption,
            Violence,
            Peace,
            Identity,
            Destiny,
            Morality,
            Suffering
        }
    }
    
    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        hostilitySystem = GetComponent<HostilitySystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        gameManager = GameManager.Instance;
        
        InitializeNarrativeSystem();
        LoadStoryProgress();
        
        // Subscribe to events
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }
        
        StartCoroutine(NarrativeUpdateLoop());
    }
    
    void InitializeNarrativeSystem()
    {
        InitializeHiddenBosses();
        InitializePhilosophicalQuotes();
    }
    
    void InitializeHiddenBosses()
    {
        // The Old Mentor (Hidden Boss)
        HiddenBossData mentor = new HiddenBossData
        {
            characterName = "Master Aldric",
            friendlyName = "The Wise Hermit",
            bossTitle = "Aldric the Fallen, First of the Corrupted",
            bossType = HiddenBossData.BossType.FallenMentor,
            initialRole = "Mentor",
            bossHealth = 800f,
            defeatConsequence = "The forest withers, but dark knowledge is gained",
            spareConsequence = "Aldric finds peace, blessing the Cinderborn with wisdom"
        };
        
        mentor.clues = new ForeshadowingClue[]
        {
            new ForeshadowingClue
            {
                clueText = "His eyes sometimes flash with an unnatural darkness...",
                type = ForeshadowingClue.ClueType.Visual,
                storyProgressRequired = 0.1f
            },
            new ForeshadowingClue
            {
                clueText = "He speaks of 'old mistakes' with deep regret",
                type = ForeshadowingClue.ClueType.Dialogue,
                storyProgressRequired = 0.2f
            },
            new ForeshadowingClue
            {
                clueText = "The animals avoid his dwelling, sensing something wrong",
                type = ForeshadowingClue.ClueType.Environmental,
                storyProgressRequired = 0.15f
            }
        };
        
        mentor.crypticDialogue = new string[]
        {
            "Power corrupts, young one... I know this better than most.",
            "The path I walked led only to darkness. Do not follow my footsteps.",
            "Sometimes the greatest evil comes from the greatest good, corrupted.",
            "I have seen what lies at the end of the dark path... and I became it."
        };
        
        // The Noble Swordsman (Transforms into Demon)
        HiddenBossData noble = new HiddenBossData
        {
            characterName = "Sir Gareth the Pure",
            friendlyName = "The Honorable Knight",
            bossTitle = "Gareth the Possessed, Vessel of Shadows",
            bossType = HiddenBossData.BossType.CorruptedNoble,
            initialRole = "Ally",
            bossHealth = 600f,
            defeatConsequence = "The demon is banished, but Gareth dies",
            spareConsequence = "Gareth overcomes the possession through the Cinderborn's mercy"
        };
        
        noble.clues = new ForeshadowingClue[]
        {
            new ForeshadowingClue
            {
                clueText = "His sword sometimes whispers in an unknown tongue",
                type = ForeshadowingClue.ClueType.Audio,
                storyProgressRequired = 0.3f
            },
            new ForeshadowingClue
            {
                clueText = "He clutches his head in pain during quiet moments",
                type = ForeshadowingClue.ClueType.Behavioral,
                storyProgressRequired = 0.35f
            }
        };
        
        // The Village Elder (Betrayal Boss)
        HiddenBossData elder = new HiddenBossData
        {
            characterName = "Elder Theron",
            friendlyName = "The Village Elder",
            bossTitle = "Theron the Betrayer, Keeper of False Truths",
            bossType = HiddenBossData.BossType.VillageElder,
            initialRole = "Mentor",
            bossHealth = 400f,
            defeatConsequence = "The village learns the truth of his deception",
            spareConsequence = "Theron repents and reveals hidden knowledge"
        };
        
        elder.clues = new ForeshadowingClue[]
        {
            new ForeshadowingClue
            {
                clueText = "He knows too much about the Cinderborn's past...",
                type = ForeshadowingClue.ClueType.Dialogue,
                storyProgressRequired = 0.4f
            },
            new ForeshadowingClue
            {
                clueText = "His stories about the past sometimes contradict each other",
                type = ForeshadowingClue.ClueType.Dialogue,
                storyProgressRequired = 0.45f
            }
        };
        
        hiddenBosses = new HiddenBossData[] { mentor, noble, elder };
    }
    
    void InitializePhilosophicalQuotes()
    {
        philosophicalQuotes = new PhilosophicalQuote[]
        {
            new PhilosophicalQuote
            {
                quote = "I am not a hero or villain. I am the ash, and to ash I shall return.",
                context = "The Cinderborn's core philosophy",
                theme = PhilosophicalQuote.QuoteTheme.Identity,
                storyProgressRequired = 0f
            },
            new PhilosophicalQuote
            {
                quote = "In this world, is the destiny of mankind controlled by some transcendental entity or law? Is it like the hand of God hovering above? At least it is true that man has no control, even over his own will.",
                context = "Reflecting on fate and free will",
                theme = PhilosophicalQuote.QuoteTheme.Destiny,
                storyProgressRequired = 0.2f
            },
            new PhilosophicalQuote
            {
                quote = "The sword is a tool of death. Swordsmanship is the art of death. No matter what pretty words you use to speak of it, this is its true nature.",
                context = "Understanding the nature of violence",
                theme = PhilosophicalQuote.QuoteTheme.Violence,
                storyProgressRequired = 0.3f
            },
            new PhilosophicalQuote
            {
                quote = "Perhaps there is no greater agony than bearing an untold story inside you.",
                context = "The burden of hidden truth",
                theme = PhilosophicalQuote.QuoteTheme.Suffering,
                storyProgressRequired = 0.5f
            },
            new PhilosophicalQuote
            {
                quote = "The flame that burns twice as bright burns half as long. But what of the ember that glows eternal?",
                context = "Contemplating the nature of power",
                theme = PhilosophicalQuote.QuoteTheme.Power,
                storyProgressRequired = 0.7f
            }
        };
    }
    
    void LoadStoryProgress()
    {
        currentStoryProgress = PlayerPrefs.GetFloat("StoryProgress", 0f);
        currentArc = (StoryArc)PlayerPrefs.GetInt("CurrentArc", 0);
        
        string completedData = PlayerPrefs.GetString("CompletedEvents", "");
        if (!string.IsNullOrEmpty(completedData))
        {
            completedEvents = new List<string>(completedData.Split(','));
        }
        
        string revealedData = PlayerPrefs.GetString("RevealedBosses", "");
        if (!string.IsNullOrEmpty(revealedData))
        {
            revealedBosses = new List<string>(revealedData.Split(','));
        }
    }
    
    IEnumerator NarrativeUpdateLoop()
    {
        while (true)
        {
            yield return new WaitForSeconds(5f);
            
            UpdateStoryProgress();
            CheckForBossReveals();
            CheckForPhilosophicalMoments();
            UpdateForeshadowingClues();
        }
    }
    
    void UpdateStoryProgress()
    {
        // Calculate story progress based on completed events and player level
        float eventProgress = completedEvents.Count * 0.05f;
        float levelProgress = (playerStats.GetCurrentLevel() - 1) * 0.02f;
        
        currentStoryProgress = Mathf.Clamp01(eventProgress + levelProgress);
        
        // Update story arc based on progress
        StoryArc newArc = CalculateCurrentArc();
        if (newArc != currentArc)
        {
            TransitionToNewArc(newArc);
        }
        
        SaveStoryProgress();
    }
    
    StoryArc CalculateCurrentArc()
    {
        if (currentStoryProgress < 0.15f) return StoryArc.Awakening;
        if (currentStoryProgress < 0.35f) return StoryArc.Discovery;
        if (currentStoryProgress < 0.55f) return StoryArc.Conflict;
        if (currentStoryProgress < 0.75f) return StoryArc.Revelation;
        if (currentStoryProgress < 0.95f) return StoryArc.Convergence;
        return StoryArc.Judgment;
    }
    
    void TransitionToNewArc(StoryArc newArc)
    {
        Debug.Log($"Story arc transition: {currentArc} -> {newArc}");
        currentArc = newArc;
        
        // Trigger arc-specific events
        switch (newArc)
        {
            case StoryArc.Discovery:
                TriggerEvent("HeritageHints");
                break;
            case StoryArc.Revelation:
                TriggerEvent("ParentageRevealed");
                break;
            case StoryArc.Judgment:
                TriggerEvent("FinalChoiceApproaches");
                break;
        }
    }
    
    void CheckForBossReveals()
    {
        foreach (HiddenBossData boss in hiddenBosses)
        {
            if (!revealedBosses.Contains(boss.characterName) && ShouldRevealBoss(boss))
            {
                RevealHiddenBoss(boss);
            }
        }
    }
    
    bool ShouldRevealBoss(HiddenBossData boss)
    {
        TriggerCondition condition = boss.revealCondition;
        
        // Check story progress
        if (currentStoryProgress < condition.storyProgress)
            return false;
        
        // Check moral path requirement
        if (condition.requiredPath != PlayerStats.MoralPath.Eclipse && 
            condition.requiredPath != playerStats.GetCurrentPath())
            return false;
        
        // Check required events
        foreach (string requiredEvent in condition.requiredEvents)
        {
            if (!completedEvents.Contains(requiredEvent))
                return false;
        }
        
        return true;
    }
    
    void RevealHiddenBoss(HiddenBossData boss)
    {
        revealedBosses.Add(boss.characterName);
        
        Debug.Log($"Hidden boss revealed: {boss.friendlyName} is actually {boss.bossTitle}!");
        
        // Show dramatic revelation
        StartCoroutine(PlayBossRevealSequence(boss));
        
        // Update hostility system
        if (hostilitySystem != null)
        {
            hostilitySystem.ModifyReputation(-20f, $"Truth about {boss.friendlyName} revealed");
        }
        
        // Add psychological trauma
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(15f, $"The betrayal of {boss.friendlyName} cuts deep");
        }
    }
    
    IEnumerator PlayBossRevealSequence(HiddenBossData boss)
    {
        // Dramatic pause
        Time.timeScale = 0.1f;
        
        // Show revelation text
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string revelationText = $"The truth is revealed...\n{boss.friendlyName} is {boss.bossTitle}!";
            gameUI.ShowInteractionPrompt(revelationText);
        }
        
        yield return new WaitForSecondsRealtime(3f);
        
        // Return to normal time
        Time.timeScale = 1f;
        
        if (gameUI != null)
        {
            gameUI.HideInteractionPrompt();
        }
        
        // Transform the character model
        TransformCharacterToBoss(boss);
    }
    
    void TransformCharacterToBoss(HiddenBossData boss)
    {
        // Find the character in the scene and transform them
        NPCController[] npcs = FindObjectsOfType<NPCController>();
        
        foreach (NPCController npc in npcs)
        {
            if (npc.npcName.Contains(boss.friendlyName) || npc.npcName.Contains(boss.characterName))
            {
                // Switch models
                if (boss.friendlyModel != null)
                    boss.friendlyModel.SetActive(false);
                if (boss.bossModel != null)
                    boss.bossModel.SetActive(true);
                
                // Update NPC to boss
                npc.isHostile = true;
                npc.npcName = boss.bossTitle;
                
                // Add boss component
                BossController bossController = npc.gameObject.AddComponent<BossController>();
                bossController.bossName = boss.bossTitle;
                bossController.maxHealth = boss.bossHealth;
                
                break;
            }
        }
    }
    
    void CheckForPhilosophicalMoments()
    {
        if (Time.time - lastPhilosophyTime < 180f) return; // 3 minutes cooldown
        
        // Check for appropriate moments for philosophy
        bool inCombat = false; // Would check combat state
        bool inDialogue = false; // Would check dialogue state
        
        if (!inCombat && !inDialogue && Random.Range(0f, 1f) < 0.1f)
        {
            TriggerPhilosophicalMoment();
        }
    }
    
    void TriggerPhilosophicalMoment()
    {
        PhilosophicalQuote[] availableQuotes = GetAvailableQuotes();
        
        if (availableQuotes.Length > 0)
        {
            PhilosophicalQuote quote = availableQuotes[Random.Range(0, availableQuotes.Length)];
            ShowPhilosophicalQuote(quote);
            lastPhilosophyTime = Time.time;
        }
    }
    
    PhilosophicalQuote[] GetAvailableQuotes()
    {
        List<PhilosophicalQuote> available = new List<PhilosophicalQuote>();
        
        foreach (PhilosophicalQuote quote in philosophicalQuotes)
        {
            if (currentStoryProgress >= quote.storyProgressRequired)
            {
                available.Add(quote);
            }
        }
        
        return available.ToArray();
    }
    
    void ShowPhilosophicalQuote(PhilosophicalQuote quote)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string displayText = $"The Cinderborn reflects:\n\"{quote.quote}\"";
            gameUI.ShowInteractionPrompt(displayText);
            StartCoroutine(HideQuoteAfterDelay(gameUI, 6f));
        }
        
        Debug.Log($"Philosophical moment: {quote.quote}");
    }
    
    IEnumerator HideQuoteAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    void UpdateForeshadowingClues()
    {
        foreach (HiddenBossData boss in hiddenBosses)
        {
            if (revealedBosses.Contains(boss.characterName)) continue;
            
            foreach (ForeshadowingClue clue in boss.clues)
            {
                if (!clue.playerNoticed && currentStoryProgress >= clue.storyProgressRequired)
                {
                    ShowForeshadowingClue(clue, boss);
                    clue.playerNoticed = true;
                }
            }
        }
    }
    
    void ShowForeshadowingClue(ForeshadowingClue clue, HiddenBossData boss)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string clueText = $"Something feels off about {boss.friendlyName}...\n{clue.clueText}";
            gameUI.ShowInteractionPrompt(clueText);
            StartCoroutine(HideQuoteAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Foreshadowing clue revealed for {boss.friendlyName}: {clue.clueText}");
    }
    
    public void TriggerEvent(string eventName)
    {
        if (!completedEvents.Contains(eventName))
        {
            completedEvents.Add(eventName);
            Debug.Log($"Story event triggered: {eventName}");
            
            // Handle specific events
            HandleStoryEvent(eventName);
        }
    }
    
    void HandleStoryEvent(string eventName)
    {
        switch (eventName)
        {
            case "HeritageHints":
                ShowHeritageHints();
                break;
            case "ParentageRevealed":
                RevealParentage();
                break;
            case "FinalChoiceApproaches":
                PrepareForFinalChoice();
                break;
        }
    }
    
    void ShowHeritageHints()
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = "The Cinderborn begins to understand... there is more to their heritage than meets the eye.";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideQuoteAfterDelay(gameUI, 5f));
        }
    }
    
    void RevealParentage()
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = "The truth is revealed: The Cinderborn is the child of both Sun and Moon Sages, born of opposing forces.";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideQuoteAfterDelay(gameUI, 8f));
        }
        
        if (gameManager != null)
        {
            gameManager.playerKnowsHeritage = true;
        }
    }
    
    void PrepareForFinalChoice()
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = "The final choice approaches. Will the Cinderborn embrace light, shadow, or forge a new path?";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideQuoteAfterDelay(gameUI, 6f));
        }
    }
    
    void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        // Trigger path-specific narrative events
        switch (newPath)
        {
            case PlayerStats.MoralPath.Sun:
                TriggerEvent("ChooseLightPath");
                break;
            case PlayerStats.MoralPath.Moon:
                TriggerEvent("ChooseDarkPath");
                break;
            case PlayerStats.MoralPath.Eclipse:
                TriggerEvent("ChooseBalancePath");
                break;
        }
    }
    
    void SaveStoryProgress()
    {
        PlayerPrefs.SetFloat("StoryProgress", currentStoryProgress);
        PlayerPrefs.SetInt("CurrentArc", (int)currentArc);
        PlayerPrefs.SetString("CompletedEvents", string.Join(",", completedEvents));
        PlayerPrefs.SetString("RevealedBosses", string.Join(",", revealedBosses));
        PlayerPrefs.Save();
    }
    
    // Getters
    public float GetStoryProgress() => currentStoryProgress;
    public StoryArc GetCurrentArc() => currentArc;
    public bool IsEventCompleted(string eventName) => completedEvents.Contains(eventName);
    public bool IsBossRevealed(string bossName) => revealedBosses.Contains(bossName);
}
