using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// ScriptableObject database for all Dynamic World Events in Cinder of Darkness
/// Central registry for event definitions, scheduling, and management
/// </summary>
[CreateAssetMenu(fileName = "EventRegistry", menuName = "Cinder of Darkness/Event Registry")]
public class EventRegistry : ScriptableObject
{
    [Header("Event Database")]
    public WorldEvent[] allEvents;
    
    [Header("Event Categories")]
    public EventCategory[] eventCategories;
    
    [Header("Global Event Settings")]
    public float globalEventFrequency = 1f;
    public int maxConcurrentEvents = 3;
    public bool enableRandomEvents = true;
    public bool enableChainedEvents = true;
    
    [Header("Event Weights by Type")]
    public EventTypeWeight[] eventTypeWeights;
    
    [Header("Seasonal Modifiers")]
    public SeasonalEventModifier[] seasonalModifiers;
    
    // Runtime data
    private Dictionary<string, WorldEvent> eventLookup;
    private Dictionary<WorldEvent.EventType, List<WorldEvent>> eventsByType;
    private Dictionary<string, EventCategory> categoryLookup;
    
    /// <summary>
    /// Event category for organization and filtering
    /// </summary>
    [System.Serializable]
    public class EventCategory
    {
        public string categoryId;
        public string categoryName;
        public string description;
        public Color categoryColor = Color.white;
        public Sprite categoryIcon;
        public WorldEvent[] categoryEvents;
        public bool enabledByDefault = true;
        public float frequencyMultiplier = 1f;
    }
    
    /// <summary>
    /// Weight settings for different event types
    /// </summary>
    [System.Serializable]
    public class EventTypeWeight
    {
        public WorldEvent.EventType eventType;
        public float weight = 1f;
        public float cooldownMultiplier = 1f;
        public int maxConcurrent = 1;
    }
    
    /// <summary>
    /// Seasonal modifiers for event frequency and types
    /// </summary>
    [System.Serializable]
    public class SeasonalEventModifier
    {
        public Season season;
        public float frequencyMultiplier = 1f;
        public WorldEvent.EventType[] favoredTypes;
        public WorldEvent.EventType[] suppressedTypes;
        public string[] seasonalEvents;
    }
    
    void OnEnable()
    {
        BuildLookupTables();
    }
    
    void OnValidate()
    {
        BuildLookupTables();
        ValidateRegistry();
    }
    
    /// <summary>
    /// Build lookup tables for efficient event access
    /// </summary>
    void BuildLookupTables()
    {
        // Build event lookup dictionary
        eventLookup = new Dictionary<string, WorldEvent>();
        eventsByType = new Dictionary<WorldEvent.EventType, List<WorldEvent>>();
        
        if (allEvents != null)
        {
            foreach (var worldEvent in allEvents)
            {
                if (worldEvent != null && !string.IsNullOrEmpty(worldEvent.eventId))
                {
                    eventLookup[worldEvent.eventId] = worldEvent;
                    
                    if (!eventsByType.ContainsKey(worldEvent.eventType))
                    {
                        eventsByType[worldEvent.eventType] = new List<WorldEvent>();
                    }
                    eventsByType[worldEvent.eventType].Add(worldEvent);
                }
            }
        }
        
        // Build category lookup
        categoryLookup = new Dictionary<string, EventCategory>();
        if (eventCategories != null)
        {
            foreach (var category in eventCategories)
            {
                if (category != null && !string.IsNullOrEmpty(category.categoryId))
                {
                    categoryLookup[category.categoryId] = category;
                }
            }
        }
    }
    
    /// <summary>
    /// Validate registry data for consistency
    /// </summary>
    void ValidateRegistry()
    {
        if (allEvents == null) return;
        
        HashSet<string> eventIds = new HashSet<string>();
        
        foreach (var worldEvent in allEvents)
        {
            if (worldEvent == null) continue;
            
            // Check for duplicate IDs
            if (!string.IsNullOrEmpty(worldEvent.eventId))
            {
                if (eventIds.Contains(worldEvent.eventId))
                {
                    Debug.LogError($"Duplicate event ID found: {worldEvent.eventId}");
                }
                else
                {
                    eventIds.Add(worldEvent.eventId);
                }
            }
            
            // Validate individual events
            worldEvent.ValidateEvent();
        }
        
        // Validate event type weights
        if (eventTypeWeights != null)
        {
            var typeWeightDict = new Dictionary<WorldEvent.EventType, int>();
            foreach (var weight in eventTypeWeights)
            {
                if (typeWeightDict.ContainsKey(weight.eventType))
                {
                    Debug.LogWarning($"Duplicate event type weight for: {weight.eventType}");
                }
                else
                {
                    typeWeightDict[weight.eventType] = 1;
                }
            }
        }
    }
    
    /// <summary>
    /// Get event by ID
    /// </summary>
    public WorldEvent GetEvent(string eventId)
    {
        if (eventLookup == null) BuildLookupTables();
        return eventLookup.ContainsKey(eventId) ? eventLookup[eventId] : null;
    }
    
    /// <summary>
    /// Get all events of a specific type
    /// </summary>
    public List<WorldEvent> GetEventsByType(WorldEvent.EventType eventType)
    {
        if (eventsByType == null) BuildLookupTables();
        return eventsByType.ContainsKey(eventType) ? eventsByType[eventType] : new List<WorldEvent>();
    }
    
    /// <summary>
    /// Get events that can trigger based on current context
    /// </summary>
    public List<WorldEvent> GetTriggerable Events(EventTriggerContext context, List<string> activeEventIds = null)
    {
        if (allEvents == null) return new List<WorldEvent>();
        
        var triggerableEvents = new List<WorldEvent>();
        activeEventIds = activeEventIds ?? new List<string>();
        
        foreach (var worldEvent in allEvents)
        {
            if (worldEvent == null) continue;
            
            // Skip if event is already active
            if (activeEventIds.Contains(worldEvent.eventId)) continue;
            
            // Check if event can trigger
            if (worldEvent.CanTrigger(context))
            {
                triggerableEvents.Add(worldEvent);
            }
        }
        
        return triggerableEvents;
    }
    
    /// <summary>
    /// Get random event based on weights and context
    /// </summary>
    public WorldEvent GetRandomEvent(EventTriggerContext context, List<string> activeEventIds = null)
    {
        var triggerableEvents = GetTriggerableEvents(context, activeEventIds);
        if (triggerableEvents.Count == 0) return null;
        
        // Apply seasonal modifiers
        var seasonalModifier = GetSeasonalModifier(context.currentSeason);
        if (seasonalModifier != null)
        {
            // Filter by seasonal preferences
            var seasonalEvents = triggerableEvents.Where(e => 
                seasonalModifier.favoredTypes.Contains(e.eventType) ||
                seasonalModifier.seasonalEvents.Contains(e.eventId)
            ).ToList();
            
            if (seasonalEvents.Count > 0)
            {
                triggerableEvents = seasonalEvents;
            }
            
            // Remove suppressed types
            triggerableEvents = triggerableEvents.Where(e => 
                !seasonalModifier.suppressedTypes.Contains(e.eventType)
            ).ToList();
        }
        
        if (triggerableEvents.Count == 0) return null;
        
        // Apply type weights
        var weightedEvents = new List<(WorldEvent, float)>();
        
        foreach (var worldEvent in triggerableEvents)
        {
            float weight = GetEventTypeWeight(worldEvent.eventType);
            
            // Apply severity modifier
            switch (worldEvent.severity)
            {
                case WorldEvent.EventSeverity.Minor:
                    weight *= 2f;
                    break;
                case WorldEvent.EventSeverity.Moderate:
                    weight *= 1.5f;
                    break;
                case WorldEvent.EventSeverity.Major:
                    weight *= 1f;
                    break;
                case WorldEvent.EventSeverity.Critical:
                    weight *= 0.5f;
                    break;
            }
            
            weightedEvents.Add((worldEvent, weight));
        }
        
        // Select random event based on weights
        float totalWeight = weightedEvents.Sum(we => we.Item2);
        float randomValue = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        foreach (var (worldEvent, weight) in weightedEvents)
        {
            currentWeight += weight;
            if (randomValue <= currentWeight)
            {
                return worldEvent;
            }
        }
        
        // Fallback to first event
        return weightedEvents.Count > 0 ? weightedEvents[0].Item1 : null;
    }
    
    /// <summary>
    /// Get events by category
    /// </summary>
    public List<WorldEvent> GetEventsByCategory(string categoryId)
    {
        if (categoryLookup == null) BuildLookupTables();
        
        if (categoryLookup.ContainsKey(categoryId))
        {
            var category = categoryLookup[categoryId];
            return category.categoryEvents?.ToList() ?? new List<WorldEvent>();
        }
        
        return new List<WorldEvent>();
    }
    
    /// <summary>
    /// Get all event categories
    /// </summary>
    public EventCategory[] GetAllCategories()
    {
        return eventCategories ?? new EventCategory[0];
    }
    
    /// <summary>
    /// Get event type weight
    /// </summary>
    float GetEventTypeWeight(WorldEvent.EventType eventType)
    {
        if (eventTypeWeights == null) return 1f;
        
        var typeWeight = eventTypeWeights.FirstOrDefault(w => w.eventType == eventType);
        return typeWeight?.weight ?? 1f;
    }
    
    /// <summary>
    /// Get seasonal modifier for current season
    /// </summary>
    SeasonalEventModifier GetSeasonalModifier(Season season)
    {
        if (seasonalModifiers == null) return null;
        return seasonalModifiers.FirstOrDefault(m => m.season == season);
    }
    
    /// <summary>
    /// Get events that can chain from a completed event
    /// </summary>
    public List<WorldEvent> GetChainableEvents(string completedEventId, EventTriggerContext context)
    {
        if (!enableChainedEvents) return new List<WorldEvent>();
        
        var chainableEvents = new List<WorldEvent>();
        
        foreach (var worldEvent in allEvents)
        {
            if (worldEvent == null || worldEvent.triggerType != WorldEvent.EventTriggerType.ChainedEvent) continue;
            
            if (worldEvent.triggerConditions?.requiredPreviousEvents?.Contains(completedEventId) == true)
            {
                if (worldEvent.CanTrigger(context))
                {
                    chainableEvents.Add(worldEvent);
                }
            }
        }
        
        return chainableEvents;
    }
    
    /// <summary>
    /// Check if maximum concurrent events of a type is reached
    /// </summary>
    public bool CanStartEventOfType(WorldEvent.EventType eventType, List<WorldEvent> activeEvents)
    {
        if (eventTypeWeights == null) return true;
        
        var typeWeight = eventTypeWeights.FirstOrDefault(w => w.eventType == eventType);
        if (typeWeight == null) return true;
        
        int currentCount = activeEvents.Count(e => e.eventType == eventType);
        return currentCount < typeWeight.maxConcurrent;
    }
    
    /// <summary>
    /// Get events affecting a specific area
    /// </summary>
    public List<WorldEvent> GetEventsAffectingArea(string areaId, WorldEvent.EventType? eventType = null)
    {
        if (allEvents == null) return new List<WorldEvent>();
        
        var affectingEvents = new List<WorldEvent>();
        
        foreach (var worldEvent in allEvents)
        {
            if (worldEvent == null) continue;
            
            // Check event type filter
            if (eventType.HasValue && worldEvent.eventType != eventType.Value) continue;
            
            // Check if event affects the area
            bool affectsArea = worldEvent.affectsGlobalWorld ||
                              worldEvent.affectedRealms.Contains(areaId) ||
                              worldEvent.affectedTowns.Contains(areaId) ||
                              worldEvent.affectedZones.Contains(areaId);
            
            if (affectsArea)
            {
                affectingEvents.Add(worldEvent);
            }
        }
        
        return affectingEvents;
    }
    
    /// <summary>
    /// Create default event registry with basic events
    /// </summary>
    [ContextMenu("Create Default Events")]
    void CreateDefaultEvents()
    {
        var defaultEvents = new List<WorldEvent>();
        
        // Create basic invasion event
        var orcInvasion = CreateInstance<WorldEvent>();
        orcInvasion.eventId = "orc_invasion_01";
        orcInvasion.eventName = "Orc Warband Invasion";
        orcInvasion.eventDescription = "A fierce orc warband has invaded the southern territories, pillaging villages and threatening trade routes.";
        orcInvasion.eventType = WorldEvent.EventType.Invasion;
        orcInvasion.severity = WorldEvent.EventSeverity.Major;
        orcInvasion.triggerType = WorldEvent.EventTriggerType.RandomChance;
        orcInvasion.baseDurationDays = 7f;
        orcInvasion.affectedRealms = new string[] { "KingdomOfSouthernBolt" };
        orcInvasion.canRecur = true;
        orcInvasion.cooldownDays = 45f;
        defaultEvents.Add(orcInvasion);
        
        // Create ash storm event
        var ashStorm = CreateInstance<WorldEvent>();
        ashStorm.eventId = "ash_storm_01";
        ashStorm.eventName = "Great Ash Storm";
        ashStorm.eventDescription = "Violent winds carry burning ash across the land, reducing visibility and making travel treacherous.";
        ashStorm.eventType = WorldEvent.EventType.NaturalDisaster;
        ashStorm.severity = WorldEvent.EventSeverity.Moderate;
        ashStorm.triggerType = WorldEvent.EventTriggerType.RandomChance;
        ashStorm.baseDurationDays = 3f;
        ashStorm.affectedRealms = new string[] { "Ashlands" };
        ashStorm.canRecur = true;
        ashStorm.cooldownDays = 20f;
        defaultEvents.Add(ashStorm);
        
        // Create disease outbreak event
        var plague = CreateInstance<WorldEvent>();
        plague.eventId = "plague_outbreak_01";
        plague.eventName = "Crimson Fever Outbreak";
        plague.eventDescription = "A mysterious disease spreads through the population, causing shops to close and NPCs to fall ill.";
        plague.eventType = WorldEvent.EventType.Disease;
        plague.severity = WorldEvent.EventSeverity.Major;
        plague.triggerType = WorldEvent.EventTriggerType.TimeBased;
        plague.baseDurationDays = 10f;
        plague.affectsGlobalWorld = true;
        plague.canRecur = true;
        plague.cooldownDays = 60f;
        defaultEvents.Add(plague);
        
        // Create religious pilgrimage event
        var pilgrimage = CreateInstance<WorldEvent>();
        pilgrimage.eventId = "flame_pilgrimage_01";
        pilgrimage.eventName = "Pilgrimage of the Eternal Flame";
        pilgrimage.eventDescription = "Devoted followers of the Faith of the Flame journey across realms, seeking sacred sites and spreading their beliefs.";
        pilgrimage.eventType = WorldEvent.EventType.Religious;
        pilgrimage.severity = WorldEvent.EventSeverity.Minor;
        pilgrimage.triggerType = WorldEvent.EventTriggerType.MoralityBased;
        pilgrimage.baseDurationDays = 14f;
        pilgrimage.affectsGlobalWorld = true;
        pilgrimage.canRecur = true;
        pilgrimage.cooldownDays = 90f;
        defaultEvents.Add(pilgrimage);
        
        // Create faction war event
        var factionWar = CreateInstance<WorldEvent>();
        factionWar.eventId = "realm_conflict_01";
        factionWar.eventName = "Border Conflict";
        factionWar.eventDescription = "Tensions between the Kingdom of Light and Shadow Realm erupt into open warfare, affecting trade and travel.";
        factionWar.eventType = WorldEvent.EventType.Political;
        factionWar.severity = WorldEvent.EventSeverity.Critical;
        factionWar.triggerType = WorldEvent.EventTriggerType.QuestMilestone;
        factionWar.baseDurationDays = 21f;
        factionWar.affectedRealms = new string[] { "KingdomOfSouthernBolt", "ForestOfShadows" };
        factionWar.canRecur = false;
        factionWar.cooldownDays = 180f;
        defaultEvents.Add(factionWar);
        
        allEvents = defaultEvents.ToArray();
        
        Debug.Log($"Created {defaultEvents.Count} default events");
    }
}
