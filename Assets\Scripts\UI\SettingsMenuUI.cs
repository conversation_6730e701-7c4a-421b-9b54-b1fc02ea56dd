using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Audio;
using TMPro;
using System.Collections.Generic;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Settings Menu UI for Cinder of Darkness.
    /// Handles graphics, audio, controls, and language settings.
    /// </summary>
    public class SettingsMenuUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Settings Panel")]
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private Button closeButton;
        [SerializeField] private Button applyButton;
        [SerializeField] private Button resetButton;

        [Header("Tab System")]
        [SerializeField] private Button graphicsTabButton;
        [SerializeField] private Button audioTabButton;
        [SerializeField] private Button controlsTabButton;
        [SerializeField] private Button languageTabButton;
        [SerializeField] private GameObject graphicsPanel;
        [SerializeField] private GameObject audioPanel;
        [SerializeField] private GameObject controlsPanel;
        [SerializeField] private GameObject languagePanel;

        [Header("Graphics Settings")]
        [SerializeField] private TMP_Dropdown qualityDropdown;
        [SerializeField] private TMP_Dropdown resolutionDropdown;
        [SerializeField] private Toggle fullscreenToggle;
        [SerializeField] private Toggle vsyncToggle;
        [SerializeField] private Slider fovSlider;
        [SerializeField] private TextMeshProUGUI fovValueText;

        [Header("Audio Settings")]
        [SerializeField] private AudioMixer audioMixer;
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Slider voiceVolumeSlider;
        [SerializeField] private TextMeshProUGUI masterVolumeText;
        [SerializeField] private TextMeshProUGUI musicVolumeText;
        [SerializeField] private TextMeshProUGUI sfxVolumeText;
        [SerializeField] private TextMeshProUGUI voiceVolumeText;

        [Header("Controls Settings")]
        [SerializeField] private Slider mouseSensitivitySlider;
        [SerializeField] private TextMeshProUGUI mouseSensitivityText;
        [SerializeField] private Toggle invertYToggle;
        [SerializeField] private Transform keybindContainer;
        [SerializeField] private GameObject keybindPrefab;

        [Header("Language Settings")]
        [SerializeField] private TMP_Dropdown languageDropdown;
        [SerializeField] private Toggle rtlToggle;
        [SerializeField] private TextMeshProUGUI languagePreviewText;
        [SerializeField] private Button testLanguageButton;

        [Header("Audio")]
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip tabSwitchSound;
        [SerializeField] private AudioClip applySound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private SettingsTab currentTab = SettingsTab.Graphics;
        private LocalizationManager localizationManager;
        private MultiInputControlSystem inputSystem;
        private bool isOpen = false;
        
        // Settings values
        private Resolution[] availableResolutions;
        private List<KeybindUI> keybindUIs = new List<KeybindUI>();
        
        // Temporary settings (applied on confirm)
        private GameSettings tempSettings;
        private GameSettings originalSettings;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
            LoadSettings();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            localizationManager = FindObjectOfType<LocalizationManager>();
            inputSystem = FindObjectOfType<MultiInputControlSystem>();
            
            if (localizationManager == null)
            {
                var locObj = new GameObject("LocalizationManager");
                localizationManager = locObj.AddComponent<LocalizationManager>();
            }
        }

        private void SetupUI()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(false);

            SetupResolutionDropdown();
            SetupQualityDropdown();
            SetupLanguageDropdown();
            SetupKeybinds();
            
            SetTab(SettingsTab.Graphics);
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseSettings);

            if (applyButton != null)
                applyButton.onClick.AddListener(ApplySettings);

            if (resetButton != null)
                resetButton.onClick.AddListener(ResetToDefaults);

            // Tab buttons
            if (graphicsTabButton != null)
                graphicsTabButton.onClick.AddListener(() => SetTab(SettingsTab.Graphics));

            if (audioTabButton != null)
                audioTabButton.onClick.AddListener(() => SetTab(SettingsTab.Audio));

            if (controlsTabButton != null)
                controlsTabButton.onClick.AddListener(() => SetTab(SettingsTab.Controls));

            if (languageTabButton != null)
                languageTabButton.onClick.AddListener(() => SetTab(SettingsTab.Language));

            // Graphics settings
            if (qualityDropdown != null)
                qualityDropdown.onValueChanged.AddListener(OnQualityChanged);

            if (resolutionDropdown != null)
                resolutionDropdown.onValueChanged.AddListener(OnResolutionChanged);

            if (fullscreenToggle != null)
                fullscreenToggle.onValueChanged.AddListener(OnFullscreenChanged);

            if (vsyncToggle != null)
                vsyncToggle.onValueChanged.AddListener(OnVSyncChanged);

            if (fovSlider != null)
                fovSlider.onValueChanged.AddListener(OnFOVChanged);

            // Audio settings
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);

            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);

            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);

            if (voiceVolumeSlider != null)
                voiceVolumeSlider.onValueChanged.AddListener(OnVoiceVolumeChanged);

            // Controls settings
            if (mouseSensitivitySlider != null)
                mouseSensitivitySlider.onValueChanged.AddListener(OnMouseSensitivityChanged);

            if (invertYToggle != null)
                invertYToggle.onValueChanged.AddListener(OnInvertYChanged);

            // Language settings
            if (languageDropdown != null)
                languageDropdown.onValueChanged.AddListener(OnLanguageChanged);

            if (rtlToggle != null)
                rtlToggle.onValueChanged.AddListener(OnRTLChanged);

            if (testLanguageButton != null)
                testLanguageButton.onClick.AddListener(TestLanguage);
        }

        private void SetupResolutionDropdown()
        {
            if (resolutionDropdown == null) return;

            availableResolutions = Screen.resolutions;
            resolutionDropdown.ClearOptions();

            List<string> options = new List<string>();
            int currentResolutionIndex = 0;

            for (int i = 0; i < availableResolutions.Length; i++)
            {
                string option = $"{availableResolutions[i].width} x {availableResolutions[i].height}";
                options.Add(option);

                if (availableResolutions[i].width == Screen.currentResolution.width &&
                    availableResolutions[i].height == Screen.currentResolution.height)
                {
                    currentResolutionIndex = i;
                }
            }

            resolutionDropdown.AddOptions(options);
            resolutionDropdown.value = currentResolutionIndex;
        }

        private void SetupQualityDropdown()
        {
            if (qualityDropdown == null) return;

            qualityDropdown.ClearOptions();
            qualityDropdown.AddOptions(new List<string>(QualitySettings.names));
            qualityDropdown.value = QualitySettings.GetQualityLevel();
        }

        private void SetupLanguageDropdown()
        {
            if (languageDropdown == null) return;

            languageDropdown.ClearOptions();
            var languages = new List<string> { "English", "Arabic" };
            languageDropdown.AddOptions(languages);
            
            if (localizationManager != null)
            {
                string currentLang = localizationManager.GetCurrentLanguage();
                languageDropdown.value = currentLang == "Arabic" ? 1 : 0;
            }
        }

        private void SetupKeybinds()
        {
            if (keybindPrefab == null || keybindContainer == null) return;

            ClearKeybinds();

            // Create keybind entries for common actions
            string[] actions = { "Move Forward", "Move Backward", "Move Left", "Move Right", 
                               "Jump", "Run", "Crouch", "Interact", "Attack", "Block", 
                               "Cast Spell", "Open Inventory", "Open Map", "Pause" };

            foreach (string action in actions)
            {
                CreateKeybindEntry(action);
            }
        }

        private void CreateKeybindEntry(string actionName)
        {
            GameObject entry = Instantiate(keybindPrefab, keybindContainer);
            var keybindUI = entry.GetComponent<KeybindUI>();
            
            if (keybindUI != null)
            {
                keybindUI.Setup(actionName, inputSystem);
                keybindUIs.Add(keybindUI);
            }
        }

        private void ClearKeybinds()
        {
            foreach (var keybind in keybindUIs)
            {
                if (keybind != null)
                    Destroy(keybind.gameObject);
            }
            keybindUIs.Clear();
        }
        #endregion

        #region Settings Management
        public void OpenSettings()
        {
            if (settingsPanel != null)
            {
                settingsPanel.SetActive(true);
                isOpen = true;
                
                // Store original settings
                originalSettings = GameSettings.LoadSettings();
                tempSettings = originalSettings.Clone();
                
                UpdateUIFromSettings();
                PlaySound(buttonClickSound);
            }
        }

        public void CloseSettings()
        {
            if (settingsPanel != null)
            {
                settingsPanel.SetActive(false);
                isOpen = false;
                
                // Revert to original settings if not applied
                if (originalSettings != null)
                {
                    ApplyGameSettings(originalSettings);
                }
                
                PlaySound(buttonClickSound);
            }
        }

        public void ApplySettings()
        {
            if (tempSettings != null)
            {
                ApplyGameSettings(tempSettings);
                tempSettings.SaveSettings();
                originalSettings = tempSettings.Clone();
                
                PlaySound(applySound);
            }
        }

        public void ResetToDefaults()
        {
            tempSettings = GameSettings.GetDefaultSettings();
            UpdateUIFromSettings();
            PlaySound(buttonClickSound);
        }

        private void LoadSettings()
        {
            var settings = GameSettings.LoadSettings();
            ApplyGameSettings(settings);
        }

        private void UpdateUIFromSettings()
        {
            if (tempSettings == null) return;

            // Graphics
            if (qualityDropdown != null)
                qualityDropdown.value = tempSettings.qualityLevel;

            if (fullscreenToggle != null)
                fullscreenToggle.isOn = tempSettings.fullscreen;

            if (vsyncToggle != null)
                vsyncToggle.isOn = tempSettings.vsync;

            if (fovSlider != null)
            {
                fovSlider.value = tempSettings.fieldOfView;
                UpdateFOVText(tempSettings.fieldOfView);
            }

            // Audio
            if (masterVolumeSlider != null)
            {
                masterVolumeSlider.value = tempSettings.masterVolume;
                UpdateVolumeText(masterVolumeText, tempSettings.masterVolume);
            }

            if (musicVolumeSlider != null)
            {
                musicVolumeSlider.value = tempSettings.musicVolume;
                UpdateVolumeText(musicVolumeText, tempSettings.musicVolume);
            }

            if (sfxVolumeSlider != null)
            {
                sfxVolumeSlider.value = tempSettings.sfxVolume;
                UpdateVolumeText(sfxVolumeText, tempSettings.sfxVolume);
            }

            if (voiceVolumeSlider != null)
            {
                voiceVolumeSlider.value = tempSettings.voiceVolume;
                UpdateVolumeText(voiceVolumeText, tempSettings.voiceVolume);
            }

            // Controls
            if (mouseSensitivitySlider != null)
            {
                mouseSensitivitySlider.value = tempSettings.mouseSensitivity;
                UpdateMouseSensitivityText(tempSettings.mouseSensitivity);
            }

            if (invertYToggle != null)
                invertYToggle.isOn = tempSettings.invertY;

            // Language
            if (languageDropdown != null)
                languageDropdown.value = tempSettings.language == "Arabic" ? 1 : 0;

            if (rtlToggle != null)
                rtlToggle.isOn = tempSettings.enableRTL;
        }

        private void ApplyGameSettings(GameSettings settings)
        {
            // Graphics
            QualitySettings.SetQualityLevel(settings.qualityLevel);
            Screen.fullScreen = settings.fullscreen;
            QualitySettings.vSyncCount = settings.vsync ? 1 : 0;

            // Set resolution
            if (settings.resolutionIndex >= 0 && settings.resolutionIndex < availableResolutions.Length)
            {
                var resolution = availableResolutions[settings.resolutionIndex];
                Screen.SetResolution(resolution.width, resolution.height, settings.fullscreen);
            }

            // Audio
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", Mathf.Log10(settings.masterVolume) * 20);
                audioMixer.SetFloat("MusicVolume", Mathf.Log10(settings.musicVolume) * 20);
                audioMixer.SetFloat("SFXVolume", Mathf.Log10(settings.sfxVolume) * 20);
                audioMixer.SetFloat("VoiceVolume", Mathf.Log10(settings.voiceVolume) * 20);
            }

            // Controls
            if (inputSystem != null)
            {
                inputSystem.SetMouseSensitivity(settings.mouseSensitivity);
                inputSystem.SetInvertY(settings.invertY);
            }

            // Language
            if (localizationManager != null)
            {
                localizationManager.SetLanguage(settings.language);
                localizationManager.SetRTLEnabled(settings.enableRTL);
            }
        }
        #endregion

        #region Tab Management
        private void SetTab(SettingsTab tab)
        {
            currentTab = tab;
            UpdateTabButtons();
            UpdateTabPanels();
            PlaySound(tabSwitchSound);
        }

        private void UpdateTabButtons()
        {
            if (graphicsTabButton != null)
                graphicsTabButton.interactable = currentTab != SettingsTab.Graphics;

            if (audioTabButton != null)
                audioTabButton.interactable = currentTab != SettingsTab.Audio;

            if (controlsTabButton != null)
                controlsTabButton.interactable = currentTab != SettingsTab.Controls;

            if (languageTabButton != null)
                languageTabButton.interactable = currentTab != SettingsTab.Language;
        }

        private void UpdateTabPanels()
        {
            if (graphicsPanel != null)
                graphicsPanel.SetActive(currentTab == SettingsTab.Graphics);

            if (audioPanel != null)
                audioPanel.SetActive(currentTab == SettingsTab.Audio);

            if (controlsPanel != null)
                controlsPanel.SetActive(currentTab == SettingsTab.Controls);

            if (languagePanel != null)
                languagePanel.SetActive(currentTab == SettingsTab.Language);
        }
        #endregion

        #region Event Handlers
        private void OnQualityChanged(int value)
        {
            if (tempSettings != null)
                tempSettings.qualityLevel = value;
        }

        private void OnResolutionChanged(int value)
        {
            if (tempSettings != null)
                tempSettings.resolutionIndex = value;
        }

        private void OnFullscreenChanged(bool value)
        {
            if (tempSettings != null)
                tempSettings.fullscreen = value;
        }

        private void OnVSyncChanged(bool value)
        {
            if (tempSettings != null)
                tempSettings.vsync = value;
        }

        private void OnFOVChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.fieldOfView = value;
            UpdateFOVText(value);
        }

        private void OnMasterVolumeChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.masterVolume = value;
            UpdateVolumeText(masterVolumeText, value);
        }

        private void OnMusicVolumeChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.musicVolume = value;
            UpdateVolumeText(musicVolumeText, value);
        }

        private void OnSFXVolumeChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.sfxVolume = value;
            UpdateVolumeText(sfxVolumeText, value);
        }

        private void OnVoiceVolumeChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.voiceVolume = value;
            UpdateVolumeText(voiceVolumeText, value);
        }

        private void OnMouseSensitivityChanged(float value)
        {
            if (tempSettings != null)
                tempSettings.mouseSensitivity = value;
            UpdateMouseSensitivityText(value);
        }

        private void OnInvertYChanged(bool value)
        {
            if (tempSettings != null)
                tempSettings.invertY = value;
        }

        private void OnLanguageChanged(int value)
        {
            if (tempSettings != null)
            {
                tempSettings.language = value == 1 ? "Arabic" : "English";
                tempSettings.enableRTL = value == 1;
                
                if (rtlToggle != null)
                    rtlToggle.isOn = tempSettings.enableRTL;
                
                UpdateLanguagePreview();
            }
        }

        private void OnRTLChanged(bool value)
        {
            if (tempSettings != null)
                tempSettings.enableRTL = value;
            UpdateLanguagePreview();
        }

        private void TestLanguage()
        {
            if (localizationManager != null && tempSettings != null)
            {
                localizationManager.SetLanguage(tempSettings.language);
                localizationManager.SetRTLEnabled(tempSettings.enableRTL);
                UpdateLanguagePreview();
            }
        }
        #endregion

        #region UI Updates
        private void UpdateFOVText(float value)
        {
            if (fovValueText != null)
                fovValueText.text = $"{value:F0}°";
        }

        private void UpdateVolumeText(TextMeshProUGUI text, float value)
        {
            if (text != null)
                text.text = $"{value * 100:F0}%";
        }

        private void UpdateMouseSensitivityText(float value)
        {
            if (mouseSensitivityText != null)
                mouseSensitivityText.text = $"{value:F2}";
        }

        private void UpdateLanguagePreview()
        {
            if (languagePreviewText != null && tempSettings != null)
            {
                string preview = tempSettings.language == "Arabic" ? 
                    "مرحبا بك في لعبة رماد الظلام" : 
                    "Welcome to Cinder of Darkness";
                
                languagePreviewText.text = preview;
                languagePreviewText.isRightToLeftText = tempSettings.enableRTL;
            }
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public Properties
        public bool IsOpen => isOpen;
        #endregion
    }

    #region Supporting Classes
    public enum SettingsTab
    {
        Graphics,
        Audio,
        Controls,
        Language
    }

    [System.Serializable]
    public class GameSettings
    {
        [Header("Graphics")]
        public int qualityLevel = 2;
        public int resolutionIndex = 0;
        public bool fullscreen = true;
        public bool vsync = true;
        public float fieldOfView = 75f;

        [Header("Audio")]
        public float masterVolume = 1f;
        public float musicVolume = 0.8f;
        public float sfxVolume = 1f;
        public float voiceVolume = 1f;

        [Header("Controls")]
        public float mouseSensitivity = 1f;
        public bool invertY = false;

        [Header("Language")]
        public string language = "English";
        public bool enableRTL = false;

        public GameSettings Clone()
        {
            return JsonUtility.FromJson<GameSettings>(JsonUtility.ToJson(this));
        }

        public void SaveSettings()
        {
            string json = JsonUtility.ToJson(this);
            PlayerPrefs.SetString("GameSettings", json);
            PlayerPrefs.Save();
        }

        public static GameSettings LoadSettings()
        {
            if (PlayerPrefs.HasKey("GameSettings"))
            {
                string json = PlayerPrefs.GetString("GameSettings");
                return JsonUtility.FromJson<GameSettings>(json);
            }
            return GetDefaultSettings();
        }

        public static GameSettings GetDefaultSettings()
        {
            return new GameSettings();
        }
    }

    public class KeybindUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI actionNameText;
        [SerializeField] private Button keybindButton;
        [SerializeField] private TextMeshProUGUI keybindText;

        private string actionName;
        private MultiInputControlSystem inputSystem;
        private bool isWaitingForInput = false;

        public void Setup(string action, MultiInputControlSystem input)
        {
            actionName = action;
            inputSystem = input;

            if (actionNameText != null)
                actionNameText.text = actionName;

            if (keybindButton != null)
                keybindButton.onClick.AddListener(StartRebind);

            UpdateKeybindDisplay();
        }

        private void StartRebind()
        {
            isWaitingForInput = true;
            if (keybindText != null)
                keybindText.text = "Press any key...";
        }

        private void Update()
        {
            if (isWaitingForInput && Input.inputString.Length > 0)
            {
                KeyCode newKey = (KeyCode)System.Enum.Parse(typeof(KeyCode), Input.inputString.ToUpper());
                
                if (inputSystem != null)
                {
                    inputSystem.SetKeybind(actionName, newKey);
                }

                isWaitingForInput = false;
                UpdateKeybindDisplay();
            }
        }

        private void UpdateKeybindDisplay()
        {
            if (keybindText != null && inputSystem != null)
            {
                KeyCode key = inputSystem.GetKeybind(actionName);
                keybindText.text = key.ToString();
            }
        }
    }
    #endregion
}
