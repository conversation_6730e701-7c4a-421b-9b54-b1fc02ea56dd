using UnityEngine;
using UnityEngine.Audio;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Centralized audio management system for Cinder of Darkness
/// Handles background music, SFX channels, volume control, and crossfades
/// </summary>
public class AudioManager : MonoBehaviour
{
    [Header("Audio Mixer")]
    public AudioMixerGroup masterMixer;
    public AudioMixerGroup musicMixer;
    public AudioMixerGroup sfxMixer;
    public AudioMixerGroup ambientMixer;
    public AudioMixerGroup voiceMixer;
    public AudioMixerGroup uiMixer;
    
    [Header("Audio Sources")]
    public AudioSource musicSource;
    public AudioSource musicSourceSecondary; // For crossfading
    public AudioSource ambientSource;
    public AudioSource voiceSource;
    public AudioSource uiSource;
    
    [Header("SFX Pool")]
    public int sfxPoolSize = 10;
    public GameObject sfxSourcePrefab;
    
    [Header("Volume Settings")]
    [Range(0f, 1f)]
    public float masterVolume = 1f;
    [Range(0f, 1f)]
    public float musicVolume = 0.7f;
    [Range(0f, 1f)]
    public float sfxVolume = 0.8f;
    [Range(0f, 1f)]
    public float ambientVolume = 0.6f;
    [Range(0f, 1f)]
    public float voiceVolume = 0.9f;
    [Range(0f, 1f)]
    public float uiVolume = 0.8f;
    
    [Header("Crossfade Settings")]
    public float defaultCrossfadeDuration = 2f;
    public AnimationCurve crossfadeCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    
    [Header("Audio Clips")]
    public AudioClip[] menuMusic;
    public AudioClip[] gameplayMusic;
    public AudioClip[] combatMusic;
    public AudioClip[] ambientSounds;
    public AudioClip[] uiSounds;
    
    // Static instance
    private static AudioManager instance;
    public static AudioManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<AudioManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("AudioManager");
                    instance = go.AddComponent<AudioManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Private fields
    private List<AudioSource> sfxPool = new List<AudioSource>();
    private Dictionary<string, AudioClip> audioClipCache = new Dictionary<string, AudioClip>();
    private bool isCrossfading = false;
    private AudioClip currentMusicClip;
    private Coroutine currentCrossfadeCoroutine;
    
    // Audio categories
    public enum AudioCategory
    {
        Music,
        SFX,
        Ambient,
        Voice,
        UI,
        Combat
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudioManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        ApplyVolumeSettings();
        LoadAudioClips();
    }
    
    void InitializeAudioManager()
    {
        // Create audio sources if not assigned
        if (musicSource == null)
        {
            musicSource = gameObject.AddComponent<AudioSource>();
            musicSource.loop = true;
            musicSource.playOnAwake = false;
            musicSource.outputAudioMixerGroup = musicMixer;
        }
        
        if (musicSourceSecondary == null)
        {
            musicSourceSecondary = gameObject.AddComponent<AudioSource>();
            musicSourceSecondary.loop = true;
            musicSourceSecondary.playOnAwake = false;
            musicSourceSecondary.outputAudioMixerGroup = musicMixer;
        }
        
        if (ambientSource == null)
        {
            ambientSource = gameObject.AddComponent<AudioSource>();
            ambientSource.loop = true;
            ambientSource.playOnAwake = false;
            ambientSource.outputAudioMixerGroup = ambientMixer;
        }
        
        if (voiceSource == null)
        {
            voiceSource = gameObject.AddComponent<AudioSource>();
            voiceSource.loop = false;
            voiceSource.playOnAwake = false;
            voiceSource.outputAudioMixerGroup = voiceMixer;
        }
        
        if (uiSource == null)
        {
            uiSource = gameObject.AddComponent<AudioSource>();
            uiSource.loop = false;
            uiSource.playOnAwake = false;
            uiSource.outputAudioMixerGroup = uiMixer;
        }
        
        // Initialize SFX pool
        InitializeSFXPool();
        
        Debug.Log("AudioManager initialized");
    }
    
    void InitializeSFXPool()
    {
        for (int i = 0; i < sfxPoolSize; i++)
        {
            GameObject sfxGO;
            
            if (sfxSourcePrefab != null)
            {
                sfxGO = Instantiate(sfxSourcePrefab, transform);
            }
            else
            {
                sfxGO = new GameObject($"SFX_Source_{i}");
                sfxGO.transform.SetParent(transform);
            }
            
            AudioSource sfxSource = sfxGO.GetComponent<AudioSource>();
            if (sfxSource == null)
            {
                sfxSource = sfxGO.AddComponent<AudioSource>();
            }
            
            sfxSource.loop = false;
            sfxSource.playOnAwake = false;
            sfxSource.outputAudioMixerGroup = sfxMixer;
            
            sfxPool.Add(sfxSource);
        }
    }
    
    void LoadAudioClips()
    {
        // Load audio clips from Resources folder
        AudioClip[] clips = Resources.LoadAll<AudioClip>("Audio");
        
        foreach (AudioClip clip in clips)
        {
            if (!audioClipCache.ContainsKey(clip.name))
            {
                audioClipCache.Add(clip.name, clip);
            }
        }
        
        Debug.Log($"Loaded {audioClipCache.Count} audio clips into cache");
    }
    
    // Volume Control
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    public void SetMusicVolume(float volume)
    {
        musicVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    public void SetSFXVolume(float volume)
    {
        sfxVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    public void SetAmbientVolume(float volume)
    {
        ambientVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    public void SetVoiceVolume(float volume)
    {
        voiceVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    public void SetUIVolume(float volume)
    {
        uiVolume = Mathf.Clamp01(volume);
        ApplyVolumeSettings();
    }
    
    void ApplyVolumeSettings()
    {
        if (masterMixer != null)
        {
            masterMixer.audioMixer.SetFloat("MasterVolume", LinearToDecibel(masterVolume));
        }
        
        if (musicMixer != null)
        {
            musicMixer.audioMixer.SetFloat("MusicVolume", LinearToDecibel(musicVolume));
        }
        
        if (sfxMixer != null)
        {
            sfxMixer.audioMixer.SetFloat("SFXVolume", LinearToDecibel(sfxVolume));
        }
        
        if (ambientMixer != null)
        {
            ambientMixer.audioMixer.SetFloat("AmbientVolume", LinearToDecibel(ambientVolume));
        }
        
        if (voiceMixer != null)
        {
            voiceMixer.audioMixer.SetFloat("VoiceVolume", LinearToDecibel(voiceVolume));
        }
        
        if (uiMixer != null)
        {
            uiMixer.audioMixer.SetFloat("UIVolume", LinearToDecibel(uiVolume));
        }
        
        // Also apply directly to sources as fallback
        if (musicSource != null)
            musicSource.volume = musicVolume * masterVolume;
        
        if (ambientSource != null)
            ambientSource.volume = ambientVolume * masterVolume;
        
        if (voiceSource != null)
            voiceSource.volume = voiceVolume * masterVolume;
        
        if (uiSource != null)
            uiSource.volume = uiVolume * masterVolume;
    }
    
    float LinearToDecibel(float linear)
    {
        if (linear <= 0f)
            return -80f;
        
        return Mathf.Log10(linear) * 20f;
    }
    
    // Music Control
    public void PlayMusic(AudioClip clip, bool loop = true, float fadeInDuration = 0f)
    {
        if (clip == null) return;
        
        if (fadeInDuration > 0f && musicSource.isPlaying)
        {
            CrossfadeMusic(clip, fadeInDuration);
        }
        else
        {
            musicSource.clip = clip;
            musicSource.loop = loop;
            musicSource.Play();
            currentMusicClip = clip;
        }
    }
    
    public void PlayMusic(string clipName, bool loop = true, float fadeInDuration = 0f)
    {
        AudioClip clip = GetAudioClip(clipName);
        PlayMusic(clip, loop, fadeInDuration);
    }
    
    public void CrossfadeMusic(AudioClip newClip, float duration = -1f)
    {
        if (newClip == null) return;
        
        if (duration < 0f)
            duration = defaultCrossfadeDuration;
        
        if (currentCrossfadeCoroutine != null)
        {
            StopCoroutine(currentCrossfadeCoroutine);
        }
        
        currentCrossfadeCoroutine = StartCoroutine(CrossfadeMusicCoroutine(newClip, duration));
    }
    
    IEnumerator CrossfadeMusicCoroutine(AudioClip newClip, float duration)
    {
        isCrossfading = true;
        
        // Setup secondary source
        musicSourceSecondary.clip = newClip;
        musicSourceSecondary.volume = 0f;
        musicSourceSecondary.Play();
        
        float elapsedTime = 0f;
        float startVolume = musicSource.volume;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.unscaledDeltaTime;
            float progress = elapsedTime / duration;
            float curveValue = crossfadeCurve.Evaluate(progress);
            
            musicSource.volume = Mathf.Lerp(startVolume, 0f, curveValue);
            musicSourceSecondary.volume = Mathf.Lerp(0f, musicVolume * masterVolume, curveValue);
            
            yield return null;
        }
        
        // Swap sources
        musicSource.Stop();
        AudioSource temp = musicSource;
        musicSource = musicSourceSecondary;
        musicSourceSecondary = temp;
        
        musicSource.volume = musicVolume * masterVolume;
        currentMusicClip = newClip;
        
        isCrossfading = false;
        currentCrossfadeCoroutine = null;
    }
    
    public void StopMusic(float fadeOutDuration = 0f)
    {
        if (fadeOutDuration > 0f)
        {
            StartCoroutine(FadeOutMusicCoroutine(fadeOutDuration));
        }
        else
        {
            musicSource.Stop();
            currentMusicClip = null;
        }
    }
    
    IEnumerator FadeOutMusicCoroutine(float duration)
    {
        float elapsedTime = 0f;
        float startVolume = musicSource.volume;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.unscaledDeltaTime;
            float progress = elapsedTime / duration;
            
            musicSource.volume = Mathf.Lerp(startVolume, 0f, progress);
            
            yield return null;
        }
        
        musicSource.Stop();
        musicSource.volume = musicVolume * masterVolume;
        currentMusicClip = null;
    }
    
    // SFX Control
    public void PlaySFX(AudioClip clip, float volume = 1f, float pitch = 1f, Vector3 position = default)
    {
        if (clip == null) return;
        
        AudioSource availableSource = GetAvailableSFXSource();
        if (availableSource != null)
        {
            availableSource.clip = clip;
            availableSource.volume = volume * sfxVolume * masterVolume;
            availableSource.pitch = pitch;
            
            if (position != default)
            {
                availableSource.transform.position = position;
                availableSource.spatialBlend = 1f; // 3D sound
            }
            else
            {
                availableSource.spatialBlend = 0f; // 2D sound
            }
            
            availableSource.Play();
        }
    }
    
    public void PlaySFX(string clipName, float volume = 1f, float pitch = 1f, Vector3 position = default)
    {
        AudioClip clip = GetAudioClip(clipName);
        PlaySFX(clip, volume, pitch, position);
    }
    
    AudioSource GetAvailableSFXSource()
    {
        foreach (AudioSource source in sfxPool)
        {
            if (!source.isPlaying)
            {
                return source;
            }
        }
        
        // If no available source, use the first one (interrupt)
        return sfxPool.Count > 0 ? sfxPool[0] : null;
    }
    
    // Ambient Control
    public void PlayAmbient(AudioClip clip, bool loop = true, float volume = 1f)
    {
        if (clip == null) return;
        
        ambientSource.clip = clip;
        ambientSource.loop = loop;
        ambientSource.volume = volume * ambientVolume * masterVolume;
        ambientSource.Play();
    }
    
    public void PlayAmbient(string clipName, bool loop = true, float volume = 1f)
    {
        AudioClip clip = GetAudioClip(clipName);
        PlayAmbient(clip, loop, volume);
    }
    
    public void StopAmbient()
    {
        ambientSource.Stop();
    }
    
    // Voice Control
    public void PlayVoice(AudioClip clip, float volume = 1f)
    {
        if (clip == null) return;
        
        voiceSource.clip = clip;
        voiceSource.volume = volume * voiceVolume * masterVolume;
        voiceSource.Play();
    }
    
    public void PlayVoice(string clipName, float volume = 1f)
    {
        AudioClip clip = GetAudioClip(clipName);
        PlayVoice(clip, volume);
    }
    
    public void StopVoice()
    {
        voiceSource.Stop();
    }
    
    // UI Control
    public void PlayUI(AudioClip clip, float volume = 1f)
    {
        if (clip == null) return;
        
        uiSource.clip = clip;
        uiSource.volume = volume * uiVolume * masterVolume;
        uiSource.Play();
    }
    
    public void PlayUI(string clipName, float volume = 1f)
    {
        AudioClip clip = GetAudioClip(clipName);
        PlayUI(clip, volume);
    }
    
    // Utility Methods
    AudioClip GetAudioClip(string clipName)
    {
        if (audioClipCache.ContainsKey(clipName))
        {
            return audioClipCache[clipName];
        }
        
        // Try to load from Resources
        AudioClip clip = Resources.Load<AudioClip>($"Audio/{clipName}");
        if (clip != null)
        {
            audioClipCache.Add(clipName, clip);
            return clip;
        }
        
        Debug.LogWarning($"Audio clip not found: {clipName}");
        return null;
    }
    
    public bool IsPlayingMusic()
    {
        return musicSource.isPlaying;
    }
    
    public bool IsPlayingAmbient()
    {
        return ambientSource.isPlaying;
    }
    
    public bool IsPlayingVoice()
    {
        return voiceSource.isPlaying;
    }
    
    public AudioClip GetCurrentMusicClip()
    {
        return currentMusicClip;
    }
    
    // Pause/Resume
    public void PauseAll()
    {
        musicSource.Pause();
        ambientSource.Pause();
        voiceSource.Pause();
        
        foreach (AudioSource source in sfxPool)
        {
            if (source.isPlaying)
                source.Pause();
        }
    }
    
    public void ResumeAll()
    {
        musicSource.UnPause();
        ambientSource.UnPause();
        voiceSource.UnPause();
        
        foreach (AudioSource source in sfxPool)
        {
            source.UnPause();
        }
    }
    
    public void StopAll()
    {
        musicSource.Stop();
        ambientSource.Stop();
        voiceSource.Stop();
        uiSource.Stop();
        
        foreach (AudioSource source in sfxPool)
        {
            source.Stop();
        }
    }
    
    // Context-aware music
    public void PlayContextualMusic(AudioCategory category)
    {
        AudioClip[] clips = null;
        
        switch (category)
        {
            case AudioCategory.Music:
                clips = gameplayMusic;
                break;
            case AudioCategory.Combat:
                clips = combatMusic;
                break;
            case AudioCategory.Ambient:
                clips = ambientSounds;
                break;
        }
        
        if (clips != null && clips.Length > 0)
        {
            AudioClip randomClip = clips[Random.Range(0, clips.Length)];
            
            if (category == AudioCategory.Combat)
            {
                CrossfadeMusic(randomClip, 1f);
            }
            else
            {
                PlayMusic(randomClip, true, defaultCrossfadeDuration);
            }
        }
    }
    
    // Integration with game systems
    public void OnSceneChanged(string sceneName)
    {
        if (sceneName == "MainMenu")
        {
            if (menuMusic.Length > 0)
            {
                PlayMusic(menuMusic[0], true, defaultCrossfadeDuration);
            }
        }
        else
        {
            PlayContextualMusic(AudioCategory.Music);
        }
    }
    
    public void OnCombatStateChanged(bool inCombat)
    {
        if (inCombat)
        {
            PlayContextualMusic(AudioCategory.Combat);
        }
        else
        {
            PlayContextualMusic(AudioCategory.Music);
        }
    }
}
