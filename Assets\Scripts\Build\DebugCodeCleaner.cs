#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;
using System.Collections.Generic;

/// <summary>
/// Debug Code Cleaner for Cinder of Darkness
/// Automatically wraps or removes debug statements for release builds
/// </summary>
public class DebugCodeCleaner : EditorWindow
{
    [MenuItem("Cinder of Darkness/Build Tools/Clean Debug Code")]
    public static void ShowWindow()
    {
        GetWindow<DebugCodeCleaner>("Debug Code Cleaner");
    }
    
    private bool cleanDebugLogs = true;
    private bool cleanDebugWarnings = true;
    private bool cleanDebugErrors = false; // Keep errors for production
    private bool wrapInsteadOfRemove = true;
    private bool createBackup = true;
    
    private List<string> processedFiles = new List<string>();
    private List<string> modifiedFiles = new List<string>();
    
    void OnGUI()
    {
        GUILayout.Label("Debug Code Cleanup for Release Build", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        cleanDebugLogs = EditorGUILayout.Toggle("Clean Debug.Log statements", cleanDebugLogs);
        cleanDebugWarnings = EditorGUILayout.Toggle("Clean Debug.LogWarning statements", cleanDebugWarnings);
        cleanDebugErrors = EditorGUILayout.Toggle("Clean Debug.LogError statements", cleanDebugErrors);
        
        GUILayout.Space(10);
        wrapInsteadOfRemove = EditorGUILayout.Toggle("Wrap with #if UNITY_EDITOR (instead of remove)", wrapInsteadOfRemove);
        createBackup = EditorGUILayout.Toggle("Create backup files", createBackup);
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("Clean All Scripts", GUILayout.Height(30)))
        {
            CleanAllScripts();
        }
        
        if (GUILayout.Button("Restore from Backups", GUILayout.Height(30)))
        {
            RestoreFromBackups();
        }
        
        GUILayout.Space(20);
        
        if (processedFiles.Count > 0)
        {
            GUILayout.Label($"Processed {processedFiles.Count} files", EditorStyles.helpBox);
            GUILayout.Label($"Modified {modifiedFiles.Count} files", EditorStyles.helpBox);
        }
    }
    
    void CleanAllScripts()
    {
        processedFiles.Clear();
        modifiedFiles.Clear();
        
        string[] scriptPaths = Directory.GetFiles("Assets/Scripts", "*.cs", SearchOption.AllDirectories);
        
        foreach (string scriptPath in scriptPaths)
        {
            ProcessScript(scriptPath);
        }
        
        AssetDatabase.Refresh();
        
        Debug.Log($"Debug code cleanup completed. Processed {processedFiles.Count} files, modified {modifiedFiles.Count} files.");
    }
    
    void ProcessScript(string filePath)
    {
        processedFiles.Add(filePath);
        
        string content = File.ReadAllText(filePath);
        string originalContent = content;
        
        // Skip files that already have proper debug wrapping
        if (content.Contains("#if UNITY_EDITOR") && content.Contains("Debug.Log"))
        {
            return;
        }
        
        // Create backup if requested
        if (createBackup)
        {
            string backupPath = filePath + ".backup";
            File.WriteAllText(backupPath, content);
        }
        
        // Process different debug statement types
        if (cleanDebugLogs)
        {
            content = ProcessDebugStatements(content, @"Debug\.Log\s*\([^;]*\);?", "Debug.Log");
        }
        
        if (cleanDebugWarnings)
        {
            content = ProcessDebugStatements(content, @"Debug\.LogWarning\s*\([^;]*\);?", "Debug.LogWarning");
        }
        
        if (cleanDebugErrors)
        {
            content = ProcessDebugStatements(content, @"Debug\.LogError\s*\([^;]*\);?", "Debug.LogError");
        }
        
        // Only write if content changed
        if (content != originalContent)
        {
            File.WriteAllText(filePath, content);
            modifiedFiles.Add(filePath);
        }
    }
    
    string ProcessDebugStatements(string content, string pattern, string debugType)
    {
        if (wrapInsteadOfRemove)
        {
            return WrapDebugStatements(content, pattern);
        }
        else
        {
            return RemoveDebugStatements(content, pattern);
        }
    }
    
    string WrapDebugStatements(string content, string pattern)
    {
        var regex = new Regex(pattern, RegexOptions.Multiline);
        var matches = regex.Matches(content);
        
        // Process matches in reverse order to maintain string positions
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            string debugStatement = match.Value;
            
            // Skip if already wrapped
            int lineStart = content.LastIndexOf('\n', match.Index) + 1;
            string linePrefix = content.Substring(lineStart, match.Index - lineStart);
            
            if (linePrefix.Trim().StartsWith("#if") || 
                content.Substring(Math.Max(0, match.Index - 50), Math.Min(50, content.Length - Math.Max(0, match.Index - 50))).Contains("#if UNITY_EDITOR"))
            {
                continue;
            }
            
            // Get indentation
            string indentation = GetIndentation(content, match.Index);
            
            // Wrap the debug statement
            string wrappedStatement = $"{indentation}#if UNITY_EDITOR\n{indentation}{debugStatement}\n{indentation}#endif";
            
            content = content.Remove(match.Index, match.Length);
            content = content.Insert(match.Index, wrappedStatement);
        }
        
        return content;
    }
    
    string RemoveDebugStatements(string content, string pattern)
    {
        var regex = new Regex(pattern, RegexOptions.Multiline);
        return regex.Replace(content, "");
    }
    
    string GetIndentation(string content, int position)
    {
        int lineStart = content.LastIndexOf('\n', position) + 1;
        int indentEnd = lineStart;
        
        while (indentEnd < content.Length && (content[indentEnd] == ' ' || content[indentEnd] == '\t'))
        {
            indentEnd++;
        }
        
        return content.Substring(lineStart, indentEnd - lineStart);
    }
    
    void RestoreFromBackups()
    {
        string[] backupFiles = Directory.GetFiles("Assets/Scripts", "*.backup", SearchOption.AllDirectories);
        
        foreach (string backupFile in backupFiles)
        {
            string originalFile = backupFile.Replace(".backup", "");
            if (File.Exists(originalFile))
            {
                string backupContent = File.ReadAllText(backupFile);
                File.WriteAllText(originalFile, backupContent);
                File.Delete(backupFile);
            }
        }
        
        AssetDatabase.Refresh();
        Debug.Log($"Restored {backupFiles.Length} files from backups.");
    }
}
#endif
