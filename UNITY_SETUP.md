# MOMO - Unity Project Setup Guide

## Prerequisites
- Unity 2022.3 LTS or newer
- Visual Studio or Visual Studio Code
- Git (optional, for version control)

## Project Creation Steps

### 1. Create New Unity Project
1. Open Unity Hub
2. Click "New Project"
3. Select "3D (Built-in Render Pipeline)" template
4. Set Project Name: "MOMO"
5. Set Location: Your desired directory
6. Click "Create Project"

### 2. Import Scripts
1. Copy all scripts from the `Assets/Scripts/` folder into your Unity project's `Assets/Scripts/` folder
2. Unity will automatically compile the scripts

### 3. Configure Project Settings

#### Input Manager
1. Go to `Edit > Project Settings > Input Manager`
2. Ensure these inputs are configured:
   - Horizontal (A/D keys, Left/Right arrows)
   - Vertical (W/S keys, Up/Down arrows)
   - Mouse X
   - Mouse Y
   - Jump (Space key)
   - Fire1 (Left mouse button)
   - Fire2 (Right mouse button)

#### Tags and Layers
1. Go to `Edit > Project Settings > Tags and Layers`
2. Add these tags:
   - Player
   - Enemy
   - NPC
   - Environment
   - Ground
3. Add these layers:
   - Player (Layer 8)
   - Enemy (Layer 9)
   - NPC (Layer 10)
   - Environment (Layer 11)

#### Quality Settings
1. Go to `Edit > Project Settings > Quality`
2. Set default quality level to "Good" or "Beautiful"

### 4. Scene Setup

#### Create Main Game Scene
1. Create new scene: `File > New Scene`
2. Save as "GameScene" in `Assets/Scenes/`
3. Add the `SceneSetup` script to an empty GameObject
4. Configure the SceneSetup component:
   - Check "Generate On Start"
   - Set world size (default: 100x10x100)
   - Set number of NPCs: 2
   - Set number of enemies: 5

#### Create Main Menu Scene
1. Create new scene: `File > New Scene`
2. Save as "MainMenu" in `Assets/Scenes/`
3. Create UI Canvas
4. Add MainMenu script to Canvas
5. Create UI elements (buttons, panels, etc.)

### 5. Build Settings
1. Go to `File > Build Settings`
2. Add scenes in this order:
   - MainMenu (index 0)
   - GameScene (index 1)
3. Set target platform (PC, Mac & Linux Standalone)

### 6. Create Prefabs

#### Player Prefab
1. Create empty GameObject named "Player"
2. Add these components:
   - CharacterController
   - PlayerController script
   - PlayerStats script
   - PlayerCombat script
3. Create child object "CameraHolder" with Camera component
4. Save as prefab in `Assets/Prefabs/`

#### NPC Prefabs
1. Create Capsule primitive
2. Add NPCController script
3. Configure dialogue trees
4. Save as prefab

#### Enemy Prefabs
1. Create Cube primitive
2. Add these components:
   - NavMeshAgent
   - EnemyHealth script
   - EnemyAI script
3. Save as prefab

#### UI Prefabs
1. Create Canvas for game UI
2. Add GameUI script
3. Create health/mana/stamina bars
4. Create dialogue panel
5. Save as prefab

### 7. Materials and Textures

#### Create Basic Materials
1. Create folder `Assets/Materials/`
2. Create materials for:
   - Ground (dark green)
   - Player (blue)
   - NPCs (yellow/blue)
   - Enemies (red)
   - Environment (brown/gray)

### 8. Audio Setup
1. Create folder `Assets/Audio/`
2. Add placeholder audio files:
   - Background music
   - Combat sounds
   - UI sounds
   - Ambient sounds

### 9. Testing the Prototype

#### Initial Test
1. Open GameScene
2. Press Play
3. Verify:
   - Player spawns and can move
   - Camera controls work
   - NPCs are present and interactable
   - Enemies patrol and attack
   - UI displays correctly

#### Combat Test
1. Approach an enemy
2. Test melee attacks (Left click)
3. Test magic attacks (Right click)
4. Test weapon switching (1, 2 keys)
5. Verify damage numbers and health bars

#### Dialogue Test
1. Approach an NPC
2. Press E to interact
3. Test dialogue choices
4. Verify moral alignment changes
5. Test alliance/betrayal mechanics

#### UI Test
1. Test pause menu (Escape)
2. Test character menu (Tab)
3. Test perspective toggle (V)
4. Verify all UI elements update correctly

### 10. Performance Optimization

#### Recommended Settings
- Use object pooling for projectiles
- Limit draw calls with texture atlasing
- Use LOD groups for distant objects
- Implement frustum culling
- Use NavMesh for AI pathfinding

### 11. Build and Distribution

#### Development Build
1. Go to `File > Build Settings`
2. Check "Development Build"
3. Click "Build and Run"

#### Release Build
1. Uncheck "Development Build"
2. Set compression to LZ4HC
3. Build for target platform

## Troubleshooting

### Common Issues
1. **Scripts not compiling**: Check for syntax errors in console
2. **Player not moving**: Verify CharacterController is attached
3. **Camera not following**: Check camera parent hierarchy
4. **NPCs not responding**: Ensure dialogue trees are assigned
5. **Enemies not attacking**: Check NavMesh generation

### Performance Issues
1. **Low FPS**: Reduce enemy count or world size
2. **Memory usage**: Use object pooling for frequently spawned objects
3. **Loading times**: Optimize texture sizes and audio compression

## Next Steps for Full Development

### Advanced Features to Add
1. **Save System**: Implement file-based saving
2. **Quest System**: Create quest manager and tracking
3. **Inventory System**: Add item management
4. **Skill Trees**: Implement character progression
5. **Advanced AI**: Create more sophisticated enemy behaviors
6. **Multiplayer**: Add network support for co-op play

### Art and Audio
1. **3D Models**: Replace primitives with proper character models
2. **Animations**: Add character and enemy animations
3. **Textures**: Create detailed environment textures
4. **Music**: Compose dynamic soundtrack
5. **Voice Acting**: Record dialogue for key characters

### Polish
1. **Particle Effects**: Add visual effects for magic and combat
2. **Post-Processing**: Implement lighting and color grading
3. **UI Polish**: Create custom UI elements and animations
4. **Accessibility**: Add colorblind support and key remapping

This prototype provides a solid foundation for the full MOMO game, demonstrating all core mechanics and systems in a playable form.
