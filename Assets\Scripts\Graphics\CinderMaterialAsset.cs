using UnityEngine;

/// <summary>
/// ScriptableObject to hold all visual material assets for Cinder of Darkness
/// Provides organized access to environmental and narrative effect materials
/// </summary>
[CreateAssetMenu(fileName = "CinderMaterialAsset", menuName = "Cinder of Darkness/Material Asset")]
public class CinderMaterialAsset : ScriptableObject
{
    [Head<PERSON>("Fire Materials")]
    [Tooltip("Soft flame texture with ember emission")]
    public Material softFlame;
    
    [Tooltip("Ember glow for weapon enchantments")]
    public Material emberGlow;
    
    [Tooltip("Torch flame for environmental lighting")]
    public Material torchFlame;
    
    [Tooltip("Weapon enchantment material with magical glow")]
    public Material weaponEnchantment;
    
    [Header("Ash Materials")]
    [Tooltip("Floating ash particles with grayscale tone")]
    public Material floatingAsh;
    
    [Tooltip("Semi-transparent ash fog surface")]
    public Material ashFog;
    
    [Tooltip("Cursed ash for dark zones")]
    public Material cursedAsh;
    
    [Tooltip("Dream state ash for reflective sequences")]
    public Material dreamAsh;
    
    [<PERSON><PERSON>("Blood Materials")]
    [Tooltip("Fresh blood with gloss and ripple effects")]
    public Material freshBlood;
    
    [<PERSON><PERSON><PERSON>("Dried blood for ground stains")]
    public Material driedBlood;
    
    [<PERSON>lt<PERSON>("Blood stain for clothes and surfaces")]
    public Material bloodStain;
    
    [Tooltip("Gore material for intense combat scenes")]
    public Material gore;
    
    [Header("Spirit Materials")]
    [Tooltip("Translucent shader with fade in/out")]
    public Material translucentSpirit;
    
    [Tooltip("Light blue spectral edge glow")]
    public Material spectralGlow;
    
    [Tooltip("Ghost manifestation for hallucinations")]
    public Material ghostManifestation;
    
    [Tooltip("Dream echo for memory sequences")]
    public Material dreamEcho;
    
    [Header("Combat FX Materials")]
    [Tooltip("Slash trails for weapon attacks")]
    public Material slashTrail;
    
    [Tooltip("Weapon glow for enhanced weapons")]
    public Material weaponGlow;
    
    [Tooltip("Hit impact effects")]
    public Material hitImpact;
    
    [Tooltip("Strong attack distortion shader")]
    public Material strongAttackDistortion;
    
    [Tooltip("Block effect for defensive actions")]
    public Material blockEffect;
    
    [Header("Material Categories")]
    [Tooltip("All fire-related materials")]
    public Material[] fireMaterials;
    
    [Tooltip("All ash-related materials")]
    public Material[] ashMaterials;
    
    [Tooltip("All blood-related materials")]
    public Material[] bloodMaterials;
    
    [Tooltip("All spirit-related materials")]
    public Material[] spiritMaterials;
    
    [Tooltip("All combat FX materials")]
    public Material[] combatFXMaterials;
    
    void OnValidate()
    {
        // Auto-populate material arrays
        fireMaterials = new Material[] { softFlame, emberGlow, torchFlame, weaponEnchantment };
        ashMaterials = new Material[] { floatingAsh, ashFog, cursedAsh, dreamAsh };
        bloodMaterials = new Material[] { freshBlood, driedBlood, bloodStain, gore };
        spiritMaterials = new Material[] { translucentSpirit, spectralGlow, ghostManifestation, dreamEcho };
        combatFXMaterials = new Material[] { slashTrail, weaponGlow, hitImpact, strongAttackDistortion, blockEffect };
    }
    
    /// <summary>
    /// Get a random fire material
    /// </summary>
    public Material GetRandomFireMaterial()
    {
        if (fireMaterials == null || fireMaterials.Length == 0)
            return softFlame;
        
        return fireMaterials[Random.Range(0, fireMaterials.Length)];
    }
    
    /// <summary>
    /// Get a random ash material
    /// </summary>
    public Material GetRandomAshMaterial()
    {
        if (ashMaterials == null || ashMaterials.Length == 0)
            return floatingAsh;
        
        return ashMaterials[Random.Range(0, ashMaterials.Length)];
    }
    
    /// <summary>
    /// Get a random blood material
    /// </summary>
    public Material GetRandomBloodMaterial()
    {
        if (bloodMaterials == null || bloodMaterials.Length == 0)
            return freshBlood;
        
        return bloodMaterials[Random.Range(0, bloodMaterials.Length)];
    }
    
    /// <summary>
    /// Get a random spirit material
    /// </summary>
    public Material GetRandomSpiritMaterial()
    {
        if (spiritMaterials == null || spiritMaterials.Length == 0)
            return translucentSpirit;
        
        return spiritMaterials[Random.Range(0, spiritMaterials.Length)];
    }
    
    /// <summary>
    /// Get a random combat FX material
    /// </summary>
    public Material GetRandomCombatFXMaterial()
    {
        if (combatFXMaterials == null || combatFXMaterials.Length == 0)
            return slashTrail;
        
        return combatFXMaterials[Random.Range(0, combatFXMaterials.Length)];
    }
    
    /// <summary>
    /// Get material by effect type
    /// </summary>
    public Material GetMaterialByType(EffectType effectType)
    {
        switch (effectType)
        {
            case EffectType.Fire:
                return GetRandomFireMaterial();
            case EffectType.Ash:
                return GetRandomAshMaterial();
            case EffectType.Blood:
                return GetRandomBloodMaterial();
            case EffectType.Spirit:
                return GetRandomSpiritMaterial();
            case EffectType.CombatFX:
                return GetRandomCombatFXMaterial();
            default:
                return softFlame;
        }
    }
    
    /// <summary>
    /// Get specific fire material by intensity
    /// </summary>
    public Material GetFireMaterialByIntensity(FireIntensity intensity)
    {
        switch (intensity)
        {
            case FireIntensity.Soft:
                return softFlame;
            case FireIntensity.Ember:
                return emberGlow;
            case FireIntensity.Torch:
                return torchFlame;
            case FireIntensity.Enchanted:
                return weaponEnchantment;
            default:
                return softFlame;
        }
    }
    
    /// <summary>
    /// Get specific ash material by state
    /// </summary>
    public Material GetAshMaterialByState(AshState state)
    {
        switch (state)
        {
            case AshState.Floating:
                return floatingAsh;
            case AshState.Fog:
                return ashFog;
            case AshState.Cursed:
                return cursedAsh;
            case AshState.Dream:
                return dreamAsh;
            default:
                return floatingAsh;
        }
    }
    
    /// <summary>
    /// Get specific blood material by condition
    /// </summary>
    public Material GetBloodMaterialByCondition(BloodCondition condition)
    {
        switch (condition)
        {
            case BloodCondition.Fresh:
                return freshBlood;
            case BloodCondition.Dried:
                return driedBlood;
            case BloodCondition.Stain:
                return bloodStain;
            case BloodCondition.Gore:
                return gore;
            default:
                return freshBlood;
        }
    }
    
    /// <summary>
    /// Get specific spirit material by manifestation type
    /// </summary>
    public Material GetSpiritMaterialByType(SpiritType spiritType)
    {
        switch (spiritType)
        {
            case SpiritType.Translucent:
                return translucentSpirit;
            case SpiritType.Spectral:
                return spectralGlow;
            case SpiritType.Ghost:
                return ghostManifestation;
            case SpiritType.Dream:
                return dreamEcho;
            default:
                return translucentSpirit;
        }
    }
    
    /// <summary>
    /// Get specific combat FX material by action
    /// </summary>
    public Material GetCombatFXMaterialByAction(CombatAction action)
    {
        switch (action)
        {
            case CombatAction.Slash:
                return slashTrail;
            case CombatAction.Glow:
                return weaponGlow;
            case CombatAction.Impact:
                return hitImpact;
            case CombatAction.StrongAttack:
                return strongAttackDistortion;
            case CombatAction.Block:
                return blockEffect;
            default:
                return slashTrail;
        }
    }
    
    /// <summary>
    /// Validate that all materials are assigned
    /// </summary>
    public bool ValidateAsset()
    {
        bool isValid = true;
        
        // Check fire materials
        if (softFlame == null || emberGlow == null || torchFlame == null || weaponEnchantment == null)
        {
            Debug.LogWarning("Some fire materials are missing!");
            isValid = false;
        }
        
        // Check ash materials
        if (floatingAsh == null || ashFog == null || cursedAsh == null || dreamAsh == null)
        {
            Debug.LogWarning("Some ash materials are missing!");
            isValid = false;
        }
        
        // Check blood materials
        if (freshBlood == null || driedBlood == null || bloodStain == null || gore == null)
        {
            Debug.LogWarning("Some blood materials are missing!");
            isValid = false;
        }
        
        // Check spirit materials
        if (translucentSpirit == null || spectralGlow == null || ghostManifestation == null || dreamEcho == null)
        {
            Debug.LogWarning("Some spirit materials are missing!");
            isValid = false;
        }
        
        // Check combat FX materials
        if (slashTrail == null || weaponGlow == null || hitImpact == null || strongAttackDistortion == null || blockEffect == null)
        {
            Debug.LogWarning("Some combat FX materials are missing!");
            isValid = false;
        }
        
        return isValid;
    }
    
    /// <summary>
    /// Get asset status for debugging
    /// </summary>
    public string GetAssetStatus()
    {
        int totalMaterials = 20; // Total expected materials
        int assignedMaterials = 0;
        
        // Count assigned materials
        if (softFlame != null) assignedMaterials++;
        if (emberGlow != null) assignedMaterials++;
        if (torchFlame != null) assignedMaterials++;
        if (weaponEnchantment != null) assignedMaterials++;
        if (floatingAsh != null) assignedMaterials++;
        if (ashFog != null) assignedMaterials++;
        if (cursedAsh != null) assignedMaterials++;
        if (dreamAsh != null) assignedMaterials++;
        if (freshBlood != null) assignedMaterials++;
        if (driedBlood != null) assignedMaterials++;
        if (bloodStain != null) assignedMaterials++;
        if (gore != null) assignedMaterials++;
        if (translucentSpirit != null) assignedMaterials++;
        if (spectralGlow != null) assignedMaterials++;
        if (ghostManifestation != null) assignedMaterials++;
        if (dreamEcho != null) assignedMaterials++;
        if (slashTrail != null) assignedMaterials++;
        if (weaponGlow != null) assignedMaterials++;
        if (hitImpact != null) assignedMaterials++;
        if (strongAttackDistortion != null) assignedMaterials++;
        if (blockEffect != null) assignedMaterials++;
        
        bool isValid = ValidateAsset();
        
        return $"Cinder Material Asset Status:\n" +
               $"Assigned Materials: {assignedMaterials}/{totalMaterials}\n" +
               $"Asset Valid: {isValid}\n" +
               $"Fire Materials: {(fireMaterials?.Length ?? 0)}/4\n" +
               $"Ash Materials: {(ashMaterials?.Length ?? 0)}/4\n" +
               $"Blood Materials: {(bloodMaterials?.Length ?? 0)}/4\n" +
               $"Spirit Materials: {(spiritMaterials?.Length ?? 0)}/4\n" +
               $"Combat FX Materials: {(combatFXMaterials?.Length ?? 0)}/5";
    }
}

// Enums for material categorization
public enum EffectType
{
    Fire,
    Ash,
    Blood,
    Spirit,
    CombatFX
}

public enum FireIntensity
{
    Soft,
    Ember,
    Torch,
    Enchanted
}

public enum AshState
{
    Floating,
    Fog,
    Cursed,
    Dream
}

public enum BloodCondition
{
    Fresh,
    Dried,
    Stain,
    Gore
}

public enum SpiritType
{
    Translucent,
    Spectral,
    Ghost,
    Dream
}

public enum CombatAction
{
    Slash,
    Glow,
    Impact,
    StrongAttack,
    Block
}
