using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class MysteriousStranger : MonoBehaviour
{
    [Header("Appearance")]
    public Material greenHairMaterial;
    public GameObject whiteSwordPrefab;
    public GameObject[] cloakVariations;
    public ParticleSystem mysticalAura;
    
    [Header("Behavior")]
    public float appearanceChance = 0.05f; // 5% chance per area
    public float disappearanceTime = 10f;
    public string[] mysteriousQuestions;
    public string[] crypticResponses;
    
    [Header("Locations")]
    public Transform[] possibleSpawnPoints;
    public bool canAppearAnywhere = true;
    public float minDistanceFromPlayer = 20f;
    public float maxDistanceFromPlayer = 50f;
    
    [Header("Audio")]
    public AudioClip[] mysteriousVoiceLines;
    public AudioClip appearanceSound;
    public AudioClip disappearanceSound;
    
    private bool isVisible = false;
    private bool hasSpokenToPlayer = false;
    private float visibilityTimer = 0f;
    private AudioSource voiceSource;
    private Renderer[] renderers;
    private PlayerController player;
    private int encounterCount = 0;
    
    // Static tracking across all instances
    private static List<Vector3> previousAppearanceLocations = new List<Vector3>();
    private static int globalEncounterCount = 0;
    
    void Start()
    {
        voiceSource = GetComponent<AudioSource>();
        renderers = GetComponentsInChildren<Renderer>();
        player = FindObjectOfType<PlayerController>();
        
        SetupAppearance();
        
        // Start invisible
        SetVisibility(false);
        
        // Begin the mysterious appearance cycle
        StartCoroutine(MysteriousAppearanceCycle());
        
        Debug.Log("Mysterious Stranger initialized - watching from the shadows...");
    }
    
    void Update()
    {
        if (isVisible)
        {
            HandleVisibleBehavior();
            UpdateVisibilityTimer();
        }
        else
        {
            HandleInvisibleBehavior();
        }
        
        UpdateMysticalEffects();
    }
    
    void SetupAppearance()
    {
        // Setup green hair
        SkinnedMeshRenderer hairRenderer = GetComponentInChildren<SkinnedMeshRenderer>();
        if (hairRenderer != null && greenHairMaterial != null)
        {
            hairRenderer.material = greenHairMaterial;
        }
        
        // Equip white sword
        if (whiteSwordPrefab != null)
        {
            Transform weaponHolder = transform.Find("WeaponHolder");
            if (weaponHolder == null)
            {
                weaponHolder = new GameObject("WeaponHolder").transform;
                weaponHolder.SetParent(transform);
                weaponHolder.localPosition = new Vector3(0.5f, 1f, 0f);
            }
            
            GameObject whiteSword = Instantiate(whiteSwordPrefab, weaponHolder);
            whiteSword.name = "Mysterious White Sword";
            
            // Add mystical glow to sword
            Renderer swordRenderer = whiteSword.GetComponent<Renderer>();
            if (swordRenderer != null)
            {
                swordRenderer.material.EnableKeyword("_EMISSION");
                swordRenderer.material.SetColor("_EmissionColor", Color.white * 1.5f);
            }
        }
        
        // Setup cloak
        if (cloakVariations.Length > 0)
        {
            GameObject selectedCloak = cloakVariations[Random.Range(0, cloakVariations.Length)];
            selectedCloak.SetActive(true);
        }
        
        // Setup mystical aura
        if (mysticalAura != null)
        {
            var main = mysticalAura.main;
            main.startColor = new Color(0.8f, 1f, 0.8f, 0.3f); // Pale green
            main.startSize = 2f;
            main.startLifetime = 3f;
            
            var emission = mysticalAura.emission;
            emission.rateOverTime = 20f;
        }
    }
    
    IEnumerator MysteriousAppearanceCycle()
    {
        while (true)
        {
            // Wait for random interval
            float waitTime = Random.Range(60f, 300f); // 1-5 minutes
            yield return new WaitForSeconds(waitTime);
            
            // Check if should appear
            if (ShouldAppear())
            {
                yield return StartCoroutine(AppearMysteriously());
                
                // Stay visible for a while
                yield return new WaitForSeconds(disappearanceTime);
                
                // Disappear if player hasn't interacted
                if (!hasSpokenToPlayer)
                {
                    yield return StartCoroutine(DisappearMysteriously());
                }
            }
        }
    }
    
    bool ShouldAppear()
    {
        if (isVisible) return false;
        if (player == null) return false;
        
        // Base chance
        if (Random.Range(0f, 1f) > appearanceChance) return false;
        
        // Don't appear too frequently
        if (Time.time - GetLastAppearanceTime() < 120f) return false;
        
        // Don't appear if player is in combat
        BrutalCombatSystem combatSystem = player.GetComponent<BrutalCombatSystem>();
        if (combatSystem != null && combatSystem.enabled) return false;
        
        return true;
    }
    
    float GetLastAppearanceTime()
    {
        return PlayerPrefs.GetFloat("LastStrangerAppearance", 0f);
    }
    
    void SetLastAppearanceTime()
    {
        PlayerPrefs.SetFloat("LastStrangerAppearance", Time.time);
    }
    
    IEnumerator AppearMysteriously()
    {
        // Choose appearance location
        Vector3 appearanceLocation = ChooseAppearanceLocation();
        transform.position = appearanceLocation;
        
        // Face away from player initially
        if (player != null)
        {
            Vector3 directionAwayFromPlayer = (transform.position - player.transform.position).normalized;
            transform.rotation = Quaternion.LookRotation(directionAwayFromPlayer);
        }
        
        // Play appearance sound
        if (appearanceSound != null && voiceSource != null)
        {
            voiceSource.PlayOneShot(appearanceSound);
        }
        
        // Fade in effect
        yield return StartCoroutine(FadeIn());
        
        isVisible = true;
        visibilityTimer = 0f;
        hasSpokenToPlayer = false;
        encounterCount++;
        globalEncounterCount++;
        
        SetLastAppearanceTime();
        
        // Add to previous locations
        previousAppearanceLocations.Add(appearanceLocation);
        if (previousAppearanceLocations.Count > 10)
        {
            previousAppearanceLocations.RemoveAt(0);
        }
        
        Debug.Log($"Mysterious Stranger appeared at {appearanceLocation} (Encounter #{encounterCount})");
        
        // Occasionally speak without player interaction
        if (Random.Range(0f, 1f) < 0.3f)
        {
            yield return new WaitForSeconds(Random.Range(2f, 5f));
            SpeakMysteriousPhrase();
        }
    }
    
    Vector3 ChooseAppearanceLocation()
    {
        Vector3 chosenLocation;
        
        if (possibleSpawnPoints.Length > 0 && Random.Range(0f, 1f) < 0.7f)
        {
            // Use predefined spawn points 70% of the time
            Transform spawnPoint = possibleSpawnPoints[Random.Range(0, possibleSpawnPoints.Length)];
            chosenLocation = spawnPoint.position;
        }
        else if (canAppearAnywhere && player != null)
        {
            // Appear randomly around player 30% of the time
            Vector3 playerPos = player.transform.position;
            Vector3 randomDirection = Random.insideUnitSphere.normalized;
            randomDirection.y = 0; // Keep on ground level
            
            float distance = Random.Range(minDistanceFromPlayer, maxDistanceFromPlayer);
            chosenLocation = playerPos + randomDirection * distance;
            
            // Ensure valid ground position
            RaycastHit hit;
            if (Physics.Raycast(chosenLocation + Vector3.up * 10f, Vector3.down, out hit, 20f))
            {
                chosenLocation = hit.point;
            }
        }
        else
        {
            // Fallback to current position
            chosenLocation = transform.position;
        }
        
        // Avoid appearing too close to previous locations
        foreach (Vector3 prevLocation in previousAppearanceLocations)
        {
            if (Vector3.Distance(chosenLocation, prevLocation) < 15f)
            {
                // Try a different location
                return ChooseAppearanceLocation();
            }
        }
        
        return chosenLocation;
    }
    
    IEnumerator FadeIn()
    {
        float fadeTime = 2f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = elapsed / fadeTime;
            
            SetVisibility(true, alpha);
            
            yield return null;
        }
        
        SetVisibility(true, 1f);
    }
    
    IEnumerator DisappearMysteriously()
    {
        // Play disappearance sound
        if (disappearanceSound != null && voiceSource != null)
        {
            voiceSource.PlayOneShot(disappearanceSound);
        }
        
        // Fade out effect
        yield return StartCoroutine(FadeOut());
        
        isVisible = false;
        SetVisibility(false);
        
        Debug.Log("Mysterious Stranger disappeared into the shadows...");
    }
    
    IEnumerator FadeOut()
    {
        float fadeTime = 1.5f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = 1f - (elapsed / fadeTime);
            
            SetVisibility(true, alpha);
            
            yield return null;
        }
    }
    
    void SetVisibility(bool visible, float alpha = 1f)
    {
        foreach (Renderer renderer in renderers)
        {
            if (renderer != null)
            {
                renderer.enabled = visible;
                if (visible)
                {
                    Color color = renderer.material.color;
                    color.a = alpha;
                    renderer.material.color = color;
                }
            }
        }
        
        if (mysticalAura != null)
        {
            if (visible)
            {
                mysticalAura.Play();
                var main = mysticalAura.main;
                Color auraColor = main.startColor.color;
                auraColor.a = alpha * 0.3f;
                main.startColor = auraColor;
            }
            else
            {
                mysticalAura.Stop();
            }
        }
    }
    
    void HandleVisibleBehavior()
    {
        if (player == null) return;
        
        float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);
        
        // Face player when they get close
        if (distanceToPlayer < 10f)
        {
            Vector3 directionToPlayer = (player.transform.position - transform.position).normalized;
            directionToPlayer.y = 0;
            transform.rotation = Quaternion.Slerp(transform.rotation, Quaternion.LookRotation(directionToPlayer), Time.deltaTime * 2f);
        }
        
        // Interact if player gets very close
        if (distanceToPlayer < 3f && !hasSpokenToPlayer)
        {
            InteractWithPlayer();
        }
        
        // Occasionally move slightly
        if (Random.Range(0f, 1f) < 0.001f)
        {
            StartCoroutine(SubtleMovement());
        }
    }
    
    void HandleInvisibleBehavior()
    {
        // Occasionally move to new position while invisible
        if (Random.Range(0f, 1f) < 0.0001f)
        {
            Vector3 newPosition = ChooseAppearanceLocation();
            transform.position = newPosition;
        }
    }
    
    void UpdateVisibilityTimer()
    {
        visibilityTimer += Time.deltaTime;
        
        // Disappear after time limit if not interacted with
        if (visibilityTimer >= disappearanceTime && !hasSpokenToPlayer)
        {
            StartCoroutine(DisappearMysteriously());
        }
    }
    
    void UpdateMysticalEffects()
    {
        if (isVisible && mysticalAura != null)
        {
            // Pulse the aura
            var emission = mysticalAura.emission;
            float pulseRate = 20f + Mathf.Sin(Time.time * 2f) * 10f;
            emission.rateOverTime = pulseRate;
        }
    }
    
    IEnumerator SubtleMovement()
    {
        Vector3 startPosition = transform.position;
        Vector3 targetPosition = startPosition + Random.insideUnitSphere * 2f;
        targetPosition.y = startPosition.y; // Keep same height
        
        float moveTime = 3f;
        float elapsed = 0f;
        
        while (elapsed < moveTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / moveTime;
            
            transform.position = Vector3.Lerp(startPosition, targetPosition, progress);
            
            yield return null;
        }
    }
    
    void InteractWithPlayer()
    {
        hasSpokenToPlayer = true;
        
        // Choose appropriate response based on encounter count and player progress
        string response = GetContextualResponse();
        
        // Display the question
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"Mysterious Stranger: \"{response}\"");
            StartCoroutine(HidePromptAfterDelay(gameUI, 8f));
        }
        
        // Play voice line
        if (mysteriousVoiceLines.Length > 0 && voiceSource != null)
        {
            AudioClip voiceLine = mysteriousVoiceLines[Random.Range(0, mysteriousVoiceLines.Length)];
            voiceSource.PlayOneShot(voiceLine);
        }
        
        Debug.Log($"Mysterious Stranger speaks: \"{response}\"");
        
        // Disappear after speaking
        StartCoroutine(DisappearAfterSpeaking());
    }
    
    string GetContextualResponse()
    {
        PlayerStats playerStats = player.GetComponent<PlayerStats>();
        
        // Base question
        string baseQuestion = "Do you know the path?";
        
        // Contextual responses based on player's journey
        if (playerStats != null)
        {
            PlayerStats.MoralPath currentPath = playerStats.GetCurrentPath();
            
            switch (encounterCount)
            {
                case 1:
                    return baseQuestion;
                    
                case 2:
                    return "The path reveals itself to those who seek...";
                    
                case 3:
                    switch (currentPath)
                    {
                        case PlayerStats.MoralPath.Sun:
                            return "Light guides, but shadows teach. Do you know the path?";
                        case PlayerStats.MoralPath.Moon:
                            return "Darkness empowers, but light reveals. Do you know the path?";
                        case PlayerStats.MoralPath.Eclipse:
                            return "Balance is wisdom. You begin to know the path.";
                        default:
                            return "Still wandering, I see. Do you know the path?";
                    }
                    
                case 4:
                    return "The cinder burns within you. The path becomes clearer.";
                    
                case 5:
                    return "Soon, you will not need to ask. The path will ask of you.";
                    
                default:
                    if (globalEncounterCount > 10)
                    {
                        return "We meet again, seeker. The path remembers all who walk it.";
                    }
                    else
                    {
                        return crypticResponses[Random.Range(0, crypticResponses.Length)];
                    }
            }
        }
        
        return baseQuestion;
    }
    
    void SpeakMysteriousPhrase()
    {
        string[] phrases = {
            "The cinder burns brightest in darkness...",
            "Two bloods, one destiny...",
            "The path chooses its own...",
            "Light and shadow dance eternal...",
            "Do you hear the whispers of the ancients?"
        };
        
        string phrase = phrases[Random.Range(0, phrases.Length)];
        
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"Mysterious Stranger: \"{phrase}\"");
            StartCoroutine(HidePromptAfterDelay(gameUI, 5f));
        }
        
        Debug.Log($"Mysterious Stranger whispers: \"{phrase}\"");
    }
    
    IEnumerator HidePromptAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    IEnumerator DisappearAfterSpeaking()
    {
        yield return new WaitForSeconds(3f);
        yield return StartCoroutine(DisappearMysteriously());
    }
    
    // Public methods for external triggers
    public void ForceAppearance(Vector3 location)
    {
        if (!isVisible)
        {
            transform.position = location;
            StartCoroutine(AppearMysteriously());
        }
    }
    
    public void ForceDisappearance()
    {
        if (isVisible)
        {
            StartCoroutine(DisappearMysteriously());
        }
    }
    
    public bool IsCurrentlyVisible()
    {
        return isVisible;
    }
    
    public int GetEncounterCount()
    {
        return encounterCount;
    }
    
    public static int GetGlobalEncounterCount()
    {
        return globalEncounterCount;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw appearance range
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, minDistanceFromPlayer);
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, maxDistanceFromPlayer);
        
        // Draw possible spawn points
        if (possibleSpawnPoints != null)
        {
            Gizmos.color = Color.yellow;
            foreach (Transform spawnPoint in possibleSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    Gizmos.DrawWireCube(spawnPoint.position, Vector3.one * 2f);
                }
            }
        }
    }
}
