using UnityEngine;

/// <summary>
/// Ensures spirit whisper assets are properly initialized for the RegretAfterKillingSystem
/// Automatically runs when the game starts to verify audio assets are available
/// </summary>
public class SpiritWhisperInitializer : MonoBehaviour
{
    [Header("Initialization Settings")]
    [Tooltip("Automatically find and initialize RegretAfterKillingSystem components")]
    public bool autoInitialize = true;
    
    [Tooltip("Show initialization status in console")]
    public bool showDebugLogs = true;
    
    [Tooltip("Create a test object if no RegretAfterKillingSystem is found")]
    public bool createTestSystemIfMissing = false;
    
    [Header("Asset Verification")]
    [Tooltip("Verify all 10 spirit whisper clips are available")]
    public bool verifyAllClips = true;
    
    [Tooltip("Test audio playback during initialization")]
    public bool testAudioPlayback = false;
    
    private static bool hasInitialized = false;
    
    void Awake()
    {
        // Ensure this only runs once per game session
        if (hasInitialized) return;
        
        if (autoInitialize)
        {
            InitializeSpiritWhisperSystem();
        }
        
        hasInitialized = true;
    }
    
    void InitializeSpiritWhisperSystem()
    {
        if (showDebugLogs)
        {
            Debug.Log("Initializing Spirit Whisper System for Cinder of Darkness...");
        }
        
        // Step 1: Verify spirit whisper asset exists
        SpiritWhisperAsset asset = VerifySpiritWhisperAsset();
        
        // Step 2: Find all RegretAfterKillingSystem components
        RegretAfterKillingSystem[] regretSystems = FindObjectsOfType<RegretAfterKillingSystem>();
        
        if (regretSystems.Length == 0)
        {
            if (showDebugLogs)
            {
                Debug.LogWarning("No RegretAfterKillingSystem components found in scene.");
            }
            
            if (createTestSystemIfMissing)
            {
                CreateTestRegretSystem(asset);
            }
        }
        else
        {
            // Step 3: Initialize each system with the asset
            InitializeRegretSystems(regretSystems, asset);
        }
        
        // Step 4: Verify audio clips if requested
        if (verifyAllClips && asset != null)
        {
            VerifyAudioClips(asset);
        }
        
        // Step 5: Test audio playback if requested
        if (testAudioPlayback && asset != null)
        {
            TestAudioPlayback(asset);
        }
        
        if (showDebugLogs)
        {
            Debug.Log("Spirit Whisper System initialization complete.");
        }
    }
    
    SpiritWhisperAsset VerifySpiritWhisperAsset()
    {
        SpiritWhisperAsset asset = null;
        
        // Try to load from Resources first
        asset = Resources.Load<SpiritWhisperAsset>("SpiritWhisperAsset");
        
        if (asset != null)
        {
            if (showDebugLogs)
            {
                Debug.Log("SpiritWhisperAsset loaded from Resources folder.");
            }
            return asset;
        }
        
        #if UNITY_EDITOR
        // Try to find in project (Editor only)
        string[] guids = UnityEditor.AssetDatabase.FindAssets("t:SpiritWhisperAsset");
        if (guids.Length > 0)
        {
            string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
            asset = UnityEditor.AssetDatabase.LoadAssetAtPath<SpiritWhisperAsset>(path);
            
            if (asset != null)
            {
                if (showDebugLogs)
                {
                    Debug.Log($"SpiritWhisperAsset found in project at: {path}");
                }
                return asset;
            }
        }
        
        // If not found, try to generate it
        if (showDebugLogs)
        {
            Debug.LogWarning("SpiritWhisperAsset not found. Attempting to generate...");
        }
        
        // Trigger auto-generation
        TriggerAssetGeneration();
        
        // Try loading again after generation
        asset = Resources.Load<SpiritWhisperAsset>("SpiritWhisperAsset");
        if (asset != null)
        {
            if (showDebugLogs)
            {
                Debug.Log("SpiritWhisperAsset successfully generated and loaded.");
            }
        }
        #else
        if (showDebugLogs)
        {
            Debug.LogError("SpiritWhisperAsset not found in Resources folder! Please ensure assets are properly built.");
        }
        #endif
        
        return asset;
    }
    
    void InitializeRegretSystems(RegretAfterKillingSystem[] systems, SpiritWhisperAsset asset)
    {
        int initializedCount = 0;
        
        foreach (RegretAfterKillingSystem system in systems)
        {
            if (system.spiritWhisperAsset == null && asset != null)
            {
                // Use reflection to set the private field if needed, or make it public
                system.spiritWhisperAsset = asset;
                initializedCount++;
                
                if (showDebugLogs)
                {
                    Debug.Log($"Assigned SpiritWhisperAsset to RegretAfterKillingSystem on {system.gameObject.name}");
                }
            }
        }
        
        if (showDebugLogs)
        {
            Debug.Log($"Initialized {initializedCount} RegretAfterKillingSystem components with spirit whisper assets.");
        }
    }
    
    void CreateTestRegretSystem(SpiritWhisperAsset asset)
    {
        GameObject testObject = new GameObject("TestRegretAfterKillingSystem");
        RegretAfterKillingSystem testSystem = testObject.AddComponent<RegretAfterKillingSystem>();
        
        if (asset != null)
        {
            testSystem.spiritWhisperAsset = asset;
        }
        
        // Add a tester component as well
        SpiritWhisperTester tester = testObject.AddComponent<SpiritWhisperTester>();
        tester.spiritWhisperAsset = asset;
        
        if (showDebugLogs)
        {
            Debug.Log("Created test RegretAfterKillingSystem with SpiritWhisperTester. Press 'T' to test whispers.");
        }
    }
    
    void VerifyAudioClips(SpiritWhisperAsset asset)
    {
        if (asset.spiritWhispers == null)
        {
            if (showDebugLogs)
            {
                Debug.LogError("SpiritWhisperAsset has null spiritWhispers array!");
            }
            return;
        }
        
        int validClips = 0;
        int totalClips = asset.spiritWhispers.Length;
        
        for (int i = 0; i < totalClips; i++)
        {
            if (asset.spiritWhispers[i] != null)
            {
                validClips++;
                
                if (showDebugLogs && i < 3) // Only log first 3 to avoid spam
                {
                    AudioClip clip = asset.spiritWhispers[i];
                    Debug.Log($"Verified clip {i}: {clip.name} ({clip.length:F1}s, {clip.frequency}Hz)");
                }
            }
            else
            {
                if (showDebugLogs)
                {
                    Debug.LogWarning($"Spirit whisper clip at index {i} is null!");
                }
            }
        }
        
        if (showDebugLogs)
        {
            Debug.Log($"Audio clip verification: {validClips}/{totalClips} clips are valid.");
        }
        
        if (validClips < totalClips)
        {
            Debug.LogWarning($"Some spirit whisper clips are missing! Expected 10, found {validClips}.");
        }
    }
    
    void TestAudioPlayback(SpiritWhisperAsset asset)
    {
        if (asset.spiritWhispers == null || asset.spiritWhispers.Length == 0)
        {
            if (showDebugLogs)
            {
                Debug.LogWarning("Cannot test audio playback - no clips available.");
            }
            return;
        }
        
        // Create a temporary audio source for testing
        GameObject tempAudioObject = new GameObject("TempSpiritWhisperTest");
        AudioSource tempAudioSource = tempAudioObject.AddComponent<AudioSource>();
        
        // Get a random clip for testing
        AudioClip testClip = asset.GetRandomWhisper();
        if (testClip != null)
        {
            tempAudioSource.clip = testClip;
            tempAudioSource.volume = asset.defaultVolume;
            tempAudioSource.pitch = 1f;
            tempAudioSource.Play();
            
            if (showDebugLogs)
            {
                Debug.Log($"Testing audio playback with clip: {testClip.name}");
            }
            
            // Clean up after the clip finishes
            Destroy(tempAudioObject, testClip.length + 1f);
        }
        else
        {
            if (showDebugLogs)
            {
                Debug.LogWarning("Could not get a valid clip for audio testing.");
            }
            Destroy(tempAudioObject);
        }
    }
    
    #if UNITY_EDITOR
    void TriggerAssetGeneration()
    {
        // This would trigger the auto-generation system
        // In a real implementation, this might call the AutoGenerateSpiritWhispers class
        if (showDebugLogs)
        {
            Debug.Log("Triggering spirit whisper asset generation...");
        }
        
        // The AutoGenerateSpiritWhispers class should handle this automatically
        // when the project loads, but we can provide a manual trigger here
    }
    #endif
    
    // Public methods for manual initialization
    public void ManualInitialize()
    {
        hasInitialized = false;
        InitializeSpiritWhisperSystem();
    }
    
    public void VerifySystemStatus()
    {
        SpiritWhisperAsset asset = Resources.Load<SpiritWhisperAsset>("SpiritWhisperAsset");
        RegretAfterKillingSystem[] systems = FindObjectsOfType<RegretAfterKillingSystem>();
        
        Debug.Log($"=== Spirit Whisper System Status ===");
        Debug.Log($"Asset Available: {(asset != null ? "Yes" : "No")}");
        Debug.Log($"Audio Clips: {(asset?.spiritWhispers?.Length ?? 0)}/10");
        Debug.Log($"Regret Systems: {systems.Length}");
        
        int systemsWithAssets = 0;
        foreach (var system in systems)
        {
            if (system.spiritWhisperAsset != null)
                systemsWithAssets++;
        }
        
        Debug.Log($"Systems with Assets: {systemsWithAssets}/{systems.Length}");
        Debug.Log($"Initialization Status: {(hasInitialized ? "Complete" : "Pending")}");
    }
    
    // Static method for external initialization
    public static void EnsureInitialized()
    {
        if (!hasInitialized)
        {
            GameObject initObject = new GameObject("SpiritWhisperInitializer");
            SpiritWhisperInitializer initializer = initObject.AddComponent<SpiritWhisperInitializer>();
            initializer.showDebugLogs = false; // Quiet initialization
            initializer.InitializeSpiritWhisperSystem();
            
            // Clean up the temporary object
            if (Application.isPlaying)
            {
                Destroy(initObject);
            }
            else
            {
                DestroyImmediate(initObject);
            }
        }
    }
}
