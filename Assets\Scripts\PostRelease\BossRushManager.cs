using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
using TMPro;

/// <summary>
/// Boss Rush Manager for Cinder of Darkness
/// Handles Boss Rush mode with support for custom arenas and player-created content
/// </summary>
public class BossRushManager : MonoBehaviour
{
    [Header("Boss Rush UI")]
    public GameObject bossRushUI;
    public GameObject arenaSelectionUI;
    public TextMeshProUGUI currentArenaText;
    public TextMeshProUGUI arenaAuthorText;
    public TextMeshProUGUI progressText;
    public Slider progressSlider;
    public Button nextArenaButton;
    public Button skipArenaButton;
    
    [Header("Arena Queue")]
    public Transform arenaQueueParent;
    public GameObject arenaQueueItemPrefab;
    public Button randomizeQueueButton;
    public Button clearQueueButton;
    public Toggle includeCustomArenasToggle;
    
    [Header("Boss Rush Settings")]
    public int maxArenasInRush = 10;
    public float arenaTransitionDelay = 3f;
    public bool allowSkipping = true;
    public bool showArenaIntros = true;
    
    // Static instance
    private static BossRushManager instance;
    public static BossRushManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<BossRushManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("BossRushManager");
                    instance = go.AddComponent<BossRushManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Boss Rush state
    private List<ArenaData> arenaQueue = new List<ArenaData>();
    private List<ArenaData> customArenas = new List<ArenaData>();
    private int currentArenaIndex = 0;
    private bool isBossRushActive = false;
    private float rushStartTime;
    private BossRushResult currentResult;
    
    [System.Serializable]
    public class BossRushResult
    {
        public int arenasCompleted;
        public int totalArenas;
        public float totalTime;
        public int totalEnemiesKilled;
        public bool playerDied;
        public List<ArenaResult> arenaResults;
    }
    
    [System.Serializable]
    public class ArenaResult
    {
        public string arenaName;
        public string arenaAuthor;
        public float completionTime;
        public bool completed;
        public int enemiesKilled;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeBossRushManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        LoadDefaultArenas();
    }
    
    void InitializeBossRushManager()
    {
        Debug.Log("Boss Rush Manager initialized");
    }
    
    void SetupUI()
    {
        if (bossRushUI != null)
            bossRushUI.SetActive(false);
        
        if (arenaSelectionUI != null)
            arenaSelectionUI.SetActive(false);
        
        if (nextArenaButton != null)
            nextArenaButton.onClick.AddListener(NextArena);
        
        if (skipArenaButton != null)
        {
            skipArenaButton.onClick.AddListener(SkipArena);
            skipArenaButton.gameObject.SetActive(allowSkipping);
        }
        
        if (randomizeQueueButton != null)
            randomizeQueueButton.onClick.AddListener(RandomizeQueue);
        
        if (clearQueueButton != null)
            clearQueueButton.onClick.AddListener(ClearQueue);
        
        if (includeCustomArenasToggle != null)
            includeCustomArenasToggle.onValueChanged.AddListener(OnIncludeCustomArenasChanged);
    }
    
    void LoadDefaultArenas()
    {
        // Load default arenas from Trials of the Ash
        var trialsSystem = FindObjectOfType<TrialsOfTheAsh>();
        if (trialsSystem != null)
        {
            var trials = TrialsOfTheAsh.GetAvailableTrials();
            foreach (var trial in trials)
            {
                if (trial.type == TrialsOfTheAsh.TrialType.Gauntlet)
                {
                    // Convert trial to arena data
                    var arenaData = CreateArenaFromTrial(trial);
                    if (arenaData != null)
                    {
                        arenaQueue.Add(arenaData);
                    }
                }
            }
        }
    }
    
    ArenaData CreateArenaFromTrial(TrialsOfTheAsh.TrialDefinition trial)
    {
        var arena = ScriptableObject.CreateInstance<ArenaData>();
        arena.arenaName = trial.trialName;
        arena.arenaId = trial.trialId;
        arena.description = trial.description;
        arena.author = "Cinder of Darkness";
        arena.version = "1.0";
        arena.biomeType = ArenaData.BiomeType.Ashlands; // Default biome
        arena.winCondition = ArenaData.WinConditionType.KillAllEnemies;
        arena.timeLimitSeconds = trial.timeLimit;
        arena.allowHealing = trial.allowHealing;
        arena.allowMagic = trial.allowMagic;
        arena.difficultyMultiplier = trial.difficultyMultiplier;
        
        // Add enemy spawn points based on required bosses
        arena.enemySpawnPoints = new List<Vector3>();
        for (int i = 0; i < trial.requiredBosses.Length; i++)
        {
            float angle = (360f / trial.requiredBosses.Length) * i;
            Vector3 spawnPoint = new Vector3(
                Mathf.Cos(angle * Mathf.Deg2Rad) * 10f,
                0f,
                Mathf.Sin(angle * Mathf.Deg2Rad) * 10f
            );
            arena.enemySpawnPoints.Add(spawnPoint);
        }
        
        return arena;
    }
    
    // Public API
    public static void StartBossRush()
    {
        Instance.StartBossRushInternal();
    }
    
    public static void StartCustomArenaBossRush(string arenaId)
    {
        Instance.StartCustomArenaBossRushInternal(arenaId);
    }
    
    public static void AddCustomArena(ArenaData arena)
    {
        Instance.AddCustomArenaInternal(arena);
    }
    
    public static void EndBossRush()
    {
        Instance.EndBossRushInternal();
    }
    
    public static bool IsBossRushActive()
    {
        return Instance.isBossRushActive;
    }
    
    public static BossRushResult GetLastResult()
    {
        return Instance.currentResult;
    }
    
    void StartBossRushInternal()
    {
        if (isBossRushActive)
        {
            Debug.LogWarning("Boss Rush already active");
            return;
        }
        
        if (arenaQueue.Count == 0)
        {
            Debug.LogWarning("No arenas in queue");
            return;
        }
        
        isBossRushActive = true;
        currentArenaIndex = 0;
        rushStartTime = Time.time;
        
        // Initialize result tracking
        currentResult = new BossRushResult
        {
            totalArenas = arenaQueue.Count,
            arenaResults = new List<ArenaResult>()
        };
        
        // Show Boss Rush UI
        if (bossRushUI != null)
            bossRushUI.SetActive(true);
        
        // Start first arena
        StartCurrentArena();
        
        Debug.Log($"Started Boss Rush with {arenaQueue.Count} arenas");
    }
    
    void StartCustomArenaBossRushInternal(string arenaId)
    {
        var arena = ArenaManager.GetArena(arenaId);
        if (arena == null)
        {
            Debug.LogError($"Arena not found: {arenaId}");
            return;
        }
        
        // Create single-arena queue
        arenaQueue.Clear();
        arenaQueue.Add(arena.arenaData);
        
        StartBossRushInternal();
    }
    
    void AddCustomArenaInternal(ArenaData arena)
    {
        if (arena == null) return;
        
        if (!customArenas.Contains(arena))
        {
            customArenas.Add(arena);
            Debug.Log($"Added custom arena to Boss Rush: {arena.arenaName}");
        }
        
        // Add to queue if custom arenas are enabled
        if (includeCustomArenasToggle != null && includeCustomArenasToggle.isOn)
        {
            if (!arenaQueue.Contains(arena))
            {
                arenaQueue.Add(arena);
                RefreshArenaQueue();
            }
        }
    }
    
    void StartCurrentArena()
    {
        if (currentArenaIndex >= arenaQueue.Count)
        {
            CompleteBossRush();
            return;
        }
        
        var currentArena = arenaQueue[currentArenaIndex];
        
        // Show arena intro
        if (showArenaIntros)
        {
            ShowArenaIntro(currentArena);
        }
        
        // Load arena in trials system
        StartCoroutine(LoadArenaAfterDelay(currentArena, showArenaIntros ? 3f : 0f));
    }
    
    void ShowArenaIntro(ArenaData arena)
    {
        if (currentArenaText != null)
            currentArenaText.text = arena.arenaName;
        
        if (arenaAuthorText != null)
            arenaAuthorText.text = $"Created by {arena.author}";
        
        UpdateProgressUI();
        
        // Show intro animation or effects
        StartCoroutine(ArenaIntroAnimation());
    }
    
    IEnumerator ArenaIntroAnimation()
    {
        // Simple fade-in animation
        var canvasGroup = bossRushUI.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = bossRushUI.AddComponent<CanvasGroup>();
        
        canvasGroup.alpha = 0f;
        
        float duration = 1f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsed / duration);
            yield return null;
        }
        
        canvasGroup.alpha = 1f;
    }
    
    IEnumerator LoadArenaAfterDelay(ArenaData arena, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        // Load arena using ArenaTester
        ArenaTester.TestArena(arena);
        
        // Monitor arena completion
        StartCoroutine(MonitorArenaCompletion());
    }
    
    IEnumerator MonitorArenaCompletion()
    {
        float arenaStartTime = Time.time;
        
        while (ArenaTester.IsTestingActive())
        {
            yield return new WaitForSeconds(0.1f);
        }
        
        // Arena completed, get results
        var testResult = ArenaTester.GetLastTestResult();
        
        var arenaResult = new ArenaResult
        {
            arenaName = arenaQueue[currentArenaIndex].arenaName,
            arenaAuthor = arenaQueue[currentArenaIndex].author,
            completionTime = Time.time - arenaStartTime,
            completed = testResult.status == ArenaTester.TestStatus.Victory,
            enemiesKilled = testResult.enemiesKilled
        };
        
        currentResult.arenaResults.Add(arenaResult);
        currentResult.totalEnemiesKilled += arenaResult.enemiesKilled;
        
        if (arenaResult.completed)
        {
            currentResult.arenasCompleted++;
            
            // Move to next arena
            currentArenaIndex++;
            
            if (currentArenaIndex < arenaQueue.Count)
            {
                yield return new WaitForSeconds(arenaTransitionDelay);
                StartCurrentArena();
            }
            else
            {
                CompleteBossRush();
            }
        }
        else
        {
            // Player failed, end boss rush
            currentResult.playerDied = true;
            EndBossRushInternal();
        }
    }
    
    void CompleteBossRush()
    {
        currentResult.totalTime = Time.time - rushStartTime;
        
        // Show completion UI
        ShowBossRushResults();
        
        // Award rewards
        AwardBossRushRewards();
        
        Debug.Log($"Boss Rush completed! {currentResult.arenasCompleted}/{currentResult.totalArenas} arenas in {currentResult.totalTime:F2} seconds");
    }
    
    void ShowBossRushResults()
    {
        string results = "Boss Rush Results:\n";
        results += $"Arenas Completed: {currentResult.arenasCompleted}/{currentResult.totalArenas}\n";
        results += $"Total Time: {currentResult.totalTime:F2} seconds\n";
        results += $"Total Enemies Killed: {currentResult.totalEnemiesKilled}\n";
        
        if (currentResult.playerDied)
        {
            results += "Status: Failed\n";
        }
        else
        {
            results += "Status: Victory!\n";
        }
        
        Debug.Log(results);
        
        // In a full implementation, this would show a proper results UI
    }
    
    void AwardBossRushRewards()
    {
        // Award rewards based on performance
        if (currentResult.arenasCompleted == currentResult.totalArenas)
        {
            // Perfect completion rewards
            SteamworksIntegration.UnlockAchievement("BOSS_RUSH_PERFECT");
            
            // Unlock special cosmetics
            PlayerPrefs.SetInt("BossRushMaster", 1);
        }
        
        if (currentResult.arenasCompleted >= currentResult.totalArenas / 2)
        {
            // Partial completion rewards
            SteamworksIntegration.UnlockAchievement("BOSS_RUSH_SURVIVOR");
        }
    }
    
    void NextArena()
    {
        if (!isBossRushActive) return;
        
        // Force complete current arena
        ArenaTester.ExitTest();
        
        currentArenaIndex++;
        
        if (currentArenaIndex < arenaQueue.Count)
        {
            StartCurrentArena();
        }
        else
        {
            CompleteBossRush();
        }
    }
    
    void SkipArena()
    {
        if (!isBossRushActive || !allowSkipping) return;
        
        // Skip current arena (counts as failure for that arena)
        var arenaResult = new ArenaResult
        {
            arenaName = arenaQueue[currentArenaIndex].arenaName,
            arenaAuthor = arenaQueue[currentArenaIndex].author,
            completionTime = 0f,
            completed = false,
            enemiesKilled = 0
        };
        
        currentResult.arenaResults.Add(arenaResult);
        
        NextArena();
    }
    
    void EndBossRushInternal()
    {
        isBossRushActive = false;
        
        // Exit current arena test
        if (ArenaTester.IsTestingActive())
        {
            ArenaTester.ExitTest();
        }
        
        // Hide Boss Rush UI
        if (bossRushUI != null)
            bossRushUI.SetActive(false);
        
        // Show final results
        ShowBossRushResults();
        
        Debug.Log("Boss Rush ended");
    }
    
    void UpdateProgressUI()
    {
        if (progressText != null)
            progressText.text = $"Arena {currentArenaIndex + 1} of {arenaQueue.Count}";
        
        if (progressSlider != null)
            progressSlider.value = (float)currentArenaIndex / arenaQueue.Count;
    }
    
    void RefreshArenaQueue()
    {
        if (arenaQueueParent == null) return;
        
        // Clear existing queue items
        foreach (Transform child in arenaQueueParent)
        {
            Destroy(child.gameObject);
        }
        
        // Create queue items
        for (int i = 0; i < arenaQueue.Count; i++)
        {
            CreateQueueItem(arenaQueue[i], i);
        }
    }
    
    void CreateQueueItem(ArenaData arena, int index)
    {
        if (arenaQueueItemPrefab == null || arenaQueueParent == null) return;
        
        GameObject item = Instantiate(arenaQueueItemPrefab, arenaQueueParent);
        
        var nameText = item.GetComponentInChildren<TextMeshProUGUI>();
        if (nameText != null)
            nameText.text = $"{index + 1}. {arena.arenaName}";
        
        var removeButton = item.GetComponentInChildren<Button>();
        if (removeButton != null)
        {
            removeButton.onClick.AddListener(() => RemoveFromQueue(index));
        }
    }
    
    void RemoveFromQueue(int index)
    {
        if (index >= 0 && index < arenaQueue.Count)
        {
            arenaQueue.RemoveAt(index);
            RefreshArenaQueue();
        }
    }
    
    void RandomizeQueue()
    {
        // Shuffle arena queue
        for (int i = 0; i < arenaQueue.Count; i++)
        {
            var temp = arenaQueue[i];
            int randomIndex = Random.Range(i, arenaQueue.Count);
            arenaQueue[i] = arenaQueue[randomIndex];
            arenaQueue[randomIndex] = temp;
        }
        
        RefreshArenaQueue();
    }
    
    void ClearQueue()
    {
        arenaQueue.Clear();
        RefreshArenaQueue();
    }
    
    void OnIncludeCustomArenasChanged(bool include)
    {
        if (include)
        {
            // Add custom arenas to queue
            foreach (var arena in customArenas)
            {
                if (!arenaQueue.Contains(arena))
                {
                    arenaQueue.Add(arena);
                }
            }
        }
        else
        {
            // Remove custom arenas from queue
            arenaQueue.RemoveAll(arena => customArenas.Contains(arena));
        }
        
        RefreshArenaQueue();
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        switch (eventName)
        {
            case "PlayerDeath":
                if (isBossRushActive)
                {
                    currentResult.playerDied = true;
                    EndBossRushInternal();
                }
                break;
        }
    }
}
