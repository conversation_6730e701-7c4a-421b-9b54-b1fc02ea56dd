using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.InputSystem;
using System.Collections;
using System.IO;

namespace CinderOfDarkness.Systems
{
    /// <summary>
    /// Photo Mode System for Cinder of Darkness.
    /// Provides time freeze, free camera, post-processing filters, and screenshot capture.
    /// </summary>
    public class PhotoModeSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Photo Mode Settings")]
        [SerializeField] private KeyCode photoModeKey = KeyCode.P;
        [SerializeField] private string photoModeInputAction = "PhotoMode";
        [SerializeField] private bool enableSteamShare = true;
        [SerializeField] private bool saveToGallery = true;

        [Header("Camera Settings")]
        [SerializeField] private float cameraSpeed = 5f;
        [SerializeField] private float cameraSpeedMultiplier = 3f;
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private float zoomSpeed = 2f;
        [SerializeField] private float minFOV = 10f;
        [SerializeField] private float maxFOV = 120f;

        [Header("UI References")]
        [SerializeField] private GameObject photoModeUI;
        [SerializeField] private GameObject photoModeHUD;
        [SerializeField] private UnityEngine.UI.Slider exposureSlider;
        [SerializeField] private UnityEngine.UI.Slider contrastSlider;
        [SerializeField] private UnityEngine.UI.Slider saturationSlider;
        [SerializeField] private UnityEngine.UI.Slider vignetteSlider;
        [SerializeField] private UnityEngine.UI.Dropdown filterDropdown;
        [SerializeField] private UnityEngine.UI.Button captureButton;
        [SerializeField] private UnityEngine.UI.Button shareButton;

        [Header("Post-Processing")]
        [SerializeField] private Volume postProcessVolume;
        [SerializeField] private VolumeProfile[] filterProfiles;

        [Header("Audio")]
        [SerializeField] private AudioClip shutterSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Public Properties
        public static PhotoModeSystem Instance { get; private set; }
        public bool IsPhotoModeActive { get; private set; }
        public bool IsCapturingScreenshot { get; private set; }
        #endregion

        #region Private Fields
        private Camera mainCamera;
        private Camera photoModeCamera;
        private PlayerController playerController;
        private GameUI gameUI;
        private InputAction photoModeAction;

        // Original states
        private float originalTimeScale;
        private Vector3 originalCameraPosition;
        private Quaternion originalCameraRotation;
        private float originalFOV;
        private VolumeProfile originalVolumeProfile;

        // Photo mode camera control
        private Vector3 photoModeCameraPosition;
        private Vector3 photoModeCameraRotation;
        private float photoModeFOV;

        // Post-processing values
        private ColorAdjustments colorAdjustments;
        private Vignette vignette;
        private Bloom bloom;
        private DepthOfField depthOfField;

        // Screenshot settings
        private string screenshotPath;
        private int screenshotCounter = 0;

        // Input handling
        private bool isMouseLocked = false;
        private Vector2 mouseInput;
        #endregion

        #region Events
        public System.Action OnPhotoModeEntered;
        public System.Action OnPhotoModeExited;
        public System.Action<string> OnScreenshotCaptured;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Photo Mode System singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePhotoMode();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and input.
        /// </summary>
        private void Start()
        {
            SetupComponents();
            SetupInput();
            SetupUI();
        }

        /// <summary>
        /// Handle input and camera updates.
        /// </summary>
        private void Update()
        {
            HandleInput();

            if (IsPhotoModeActive)
            {
                UpdatePhotoModeCamera();
            }
        }

        /// <summary>
        /// Cleanup on destroy.
        /// </summary>
        private void OnDestroy()
        {
            if (photoModeAction != null)
            {
                photoModeAction.performed -= OnPhotoModeToggle;
                photoModeAction.Dispose();
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize photo mode system.
        /// </summary>
        private void InitializePhotoMode()
        {
            screenshotPath = Path.Combine(Application.persistentDataPath, "Screenshots");

            if (!Directory.Exists(screenshotPath))
            {
                Directory.CreateDirectory(screenshotPath);
            }

            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }

        /// <summary>
        /// Setup component references.
        /// </summary>
        private void SetupComponents()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }

            playerController = FindObjectOfType<PlayerController>();
            gameUI = FindObjectOfType<GameUI>();

            // Create photo mode camera
            CreatePhotoModeCamera();

            // Setup post-processing
            SetupPostProcessing();
        }

        /// <summary>
        /// Create dedicated photo mode camera.
        /// </summary>
        private void CreatePhotoModeCamera()
        {
            if (mainCamera == null) return;

            GameObject cameraObject = new GameObject("PhotoModeCamera");
            photoModeCamera = cameraObject.AddComponent<Camera>();

            // Copy main camera settings
            photoModeCamera.CopyFrom(mainCamera);
            photoModeCamera.enabled = false;

            // Add URP camera data
            var cameraData = cameraObject.AddComponent<UniversalAdditionalCameraData>();
            cameraData.renderType = CameraRenderType.Base;
        }

        /// <summary>
        /// Setup post-processing components.
        /// </summary>
        private void SetupPostProcessing()
        {
            if (postProcessVolume == null)
            {
                postProcessVolume = FindObjectOfType<Volume>();
            }

            if (postProcessVolume != null && postProcessVolume.profile != null)
            {
                originalVolumeProfile = postProcessVolume.profile;

                // Get post-processing components
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out vignette);
                postProcessVolume.profile.TryGet(out bloom);
                postProcessVolume.profile.TryGet(out depthOfField);
            }
        }

        /// <summary>
        /// Setup input actions.
        /// </summary>
        private void SetupInput()
        {
            // Setup photo mode toggle input
            var inputActions = FindObjectOfType<PlayerInput>()?.actions;
            if (inputActions != null)
            {
                photoModeAction = inputActions.FindAction(photoModeInputAction);
                if (photoModeAction != null)
                {
                    photoModeAction.performed += OnPhotoModeToggle;
                }
            }
        }

        /// <summary>
        /// Setup UI event listeners.
        /// </summary>
        private void SetupUI()
        {
            if (exposureSlider != null)
                exposureSlider.onValueChanged.AddListener(OnExposureChanged);

            if (contrastSlider != null)
                contrastSlider.onValueChanged.AddListener(OnContrastChanged);

            if (saturationSlider != null)
                saturationSlider.onValueChanged.AddListener(OnSaturationChanged);

            if (vignetteSlider != null)
                vignetteSlider.onValueChanged.AddListener(OnVignetteChanged);

            if (filterDropdown != null)
                filterDropdown.onValueChanged.AddListener(OnFilterChanged);

            if (captureButton != null)
                captureButton.onClick.AddListener(CaptureScreenshot);

            if (shareButton != null)
                shareButton.onClick.AddListener(ShareScreenshot);

            // Hide UI initially
            if (photoModeUI != null)
                photoModeUI.SetActive(false);
        }
        #endregion

        #region Photo Mode Control
        /// <summary>
        /// Toggle photo mode on/off.
        /// </summary>
        public void TogglePhotoMode()
        {
            if (IsPhotoModeActive)
            {
                ExitPhotoMode();
            }
            else
            {
                EnterPhotoMode();
            }
        }

        /// <summary>
        /// Enter photo mode.
        /// </summary>
        public void EnterPhotoMode()
        {
            if (IsPhotoModeActive) return;

            IsPhotoModeActive = true;

            // Store original states
            StoreOriginalStates();

            // Freeze time
            originalTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            // Disable player control
            if (playerController != null)
            {
                playerController.SetMovementEnabled(false);
            }

            // Hide game UI
            if (gameUI != null)
            {
                gameUI.gameObject.SetActive(false);
            }

            // Setup photo mode camera
            SetupPhotoModeCamera();

            // Show photo mode UI
            if (photoModeUI != null)
            {
                photoModeUI.SetActive(true);
            }

            // Lock cursor for camera control
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            isMouseLocked = true;

            OnPhotoModeEntered?.Invoke();
            Debug.Log("Photo Mode Entered");
        }

        /// <summary>
        /// Exit photo mode.
        /// </summary>
        public void ExitPhotoMode()
        {
            if (!IsPhotoModeActive) return;

            IsPhotoModeActive = false;

            // Restore original states
            RestoreOriginalStates();

            // Restore time
            Time.timeScale = originalTimeScale;

            // Re-enable player control
            if (playerController != null)
            {
                playerController.SetMovementEnabled(true);
            }

            // Show game UI
            if (gameUI != null)
            {
                gameUI.gameObject.SetActive(true);
            }

            // Disable photo mode camera
            if (photoModeCamera != null)
            {
                photoModeCamera.enabled = false;
            }

            // Enable main camera
            if (mainCamera != null)
            {
                mainCamera.enabled = true;
            }

            // Hide photo mode UI
            if (photoModeUI != null)
            {
                photoModeUI.SetActive(false);
            }

            // Unlock cursor
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            isMouseLocked = false;

            OnPhotoModeExited?.Invoke();
            Debug.Log("Photo Mode Exited");
        }

        /// <summary>
        /// Store original camera and post-processing states.
        /// </summary>
        private void StoreOriginalStates()
        {
            if (mainCamera != null)
            {
                originalCameraPosition = mainCamera.transform.position;
                originalCameraRotation = mainCamera.transform.rotation;
                originalFOV = mainCamera.fieldOfView;
            }
        }

        /// <summary>
        /// Restore original camera and post-processing states.
        /// </summary>
        private void RestoreOriginalStates()
        {
            if (mainCamera != null)
            {
                mainCamera.transform.position = originalCameraPosition;
                mainCamera.transform.rotation = originalCameraRotation;
                mainCamera.fieldOfView = originalFOV;
            }

            if (postProcessVolume != null && originalVolumeProfile != null)
            {
                postProcessVolume.profile = originalVolumeProfile;
            }
        }

        /// <summary>
        /// Setup photo mode camera position and settings.
        /// </summary>
        private void SetupPhotoModeCamera()
        {
            if (photoModeCamera == null || mainCamera == null) return;

            // Copy current camera state
            photoModeCamera.transform.position = mainCamera.transform.position;
            photoModeCamera.transform.rotation = mainCamera.transform.rotation;
            photoModeCamera.fieldOfView = mainCamera.fieldOfView;

            // Store photo mode camera state
            photoModeCameraPosition = photoModeCamera.transform.position;
            photoModeCameraRotation = photoModeCamera.transform.eulerAngles;
            photoModeFOV = photoModeCamera.fieldOfView;

            // Enable photo mode camera
            photoModeCamera.enabled = true;
            mainCamera.enabled = false;
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle photo mode input.
        /// </summary>
        private void HandleInput()
        {
            // Toggle photo mode with key
            if (Input.GetKeyDown(photoModeKey))
            {
                TogglePhotoMode();
            }

            if (!IsPhotoModeActive) return;

            // Handle UI toggle
            if (Input.GetKeyDown(KeyCode.H))
            {
                TogglePhotoModeUI();
            }

            // Handle screenshot capture
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.Space))
            {
                CaptureScreenshot();
            }

            // Handle mouse lock toggle
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                ToggleMouseLock();
            }

            // Handle exit
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                ExitPhotoMode();
            }
        }

        /// <summary>
        /// Photo mode toggle input callback.
        /// </summary>
        /// <param name="context">Input action context</param>
        private void OnPhotoModeToggle(InputAction.CallbackContext context)
        {
            TogglePhotoMode();
        }

        /// <summary>
        /// Toggle photo mode UI visibility.
        /// </summary>
        private void TogglePhotoModeUI()
        {
            if (photoModeUI != null)
            {
                bool isActive = photoModeUI.activeSelf;
                photoModeUI.SetActive(!isActive);
            }
        }

        /// <summary>
        /// Toggle mouse lock for camera control.
        /// </summary>
        private void ToggleMouseLock()
        {
            isMouseLocked = !isMouseLocked;

            if (isMouseLocked)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
            else
            {
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
            }
        }
        #endregion

        #region Camera Control
        /// <summary>
        /// Update photo mode camera movement and rotation.
        /// </summary>
        private void UpdatePhotoModeCamera()
        {
            if (photoModeCamera == null || !isMouseLocked) return;

            // Get input
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            float upDown = 0f;

            if (Input.GetKey(KeyCode.Q)) upDown = -1f;
            if (Input.GetKey(KeyCode.E)) upDown = 1f;

            // Speed modifier
            float currentSpeed = cameraSpeed;
            if (Input.GetKey(KeyCode.LeftShift))
                currentSpeed *= cameraSpeedMultiplier;

            // Movement (unscaled time for photo mode)
            Vector3 movement = Vector3.zero;
            movement += photoModeCamera.transform.right * horizontal * currentSpeed * Time.unscaledDeltaTime;
            movement += photoModeCamera.transform.forward * vertical * currentSpeed * Time.unscaledDeltaTime;
            movement += photoModeCamera.transform.up * upDown * currentSpeed * Time.unscaledDeltaTime;

            photoModeCamera.transform.position += movement;

            // Mouse look
            mouseInput.x += Input.GetAxis("Mouse X") * mouseSensitivity;
            mouseInput.y -= Input.GetAxis("Mouse Y") * mouseSensitivity;
            mouseInput.y = Mathf.Clamp(mouseInput.y, -90f, 90f);

            photoModeCamera.transform.rotation = Quaternion.Euler(mouseInput.y, mouseInput.x, 0f);

            // Zoom with scroll wheel
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                float newFOV = photoModeCamera.fieldOfView - scroll * zoomSpeed;
                photoModeCamera.fieldOfView = Mathf.Clamp(newFOV, minFOV, maxFOV);
            }
        }
        #endregion

        #region Post-Processing Controls
        /// <summary>
        /// Handle exposure slider change.
        /// </summary>
        /// <param name="value">Exposure value</param>
        private void OnExposureChanged(float value)
        {
            if (colorAdjustments != null)
            {
                colorAdjustments.postExposure.value = value;
            }
        }

        /// <summary>
        /// Handle contrast slider change.
        /// </summary>
        /// <param name="value">Contrast value</param>
        private void OnContrastChanged(float value)
        {
            if (colorAdjustments != null)
            {
                colorAdjustments.contrast.value = value;
            }
        }

        /// <summary>
        /// Handle saturation slider change.
        /// </summary>
        /// <param name="value">Saturation value</param>
        private void OnSaturationChanged(float value)
        {
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = value;
            }
        }

        /// <summary>
        /// Handle vignette slider change.
        /// </summary>
        /// <param name="value">Vignette intensity</param>
        private void OnVignetteChanged(float value)
        {
            if (vignette != null)
            {
                vignette.intensity.value = value;
            }
        }

        /// <summary>
        /// Handle filter dropdown change.
        /// </summary>
        /// <param name="index">Filter index</param>
        private void OnFilterChanged(int index)
        {
            if (postProcessVolume != null && filterProfiles != null && index >= 0 && index < filterProfiles.Length)
            {
                postProcessVolume.profile = filterProfiles[index];

                // Re-get components for new profile
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out vignette);
                postProcessVolume.profile.TryGet(out bloom);
                postProcessVolume.profile.TryGet(out depthOfField);

                // Update UI sliders to match new profile
                UpdateUIFromProfile();
            }
        }

        /// <summary>
        /// Update UI sliders to match current post-processing profile.
        /// </summary>
        private void UpdateUIFromProfile()
        {
            if (colorAdjustments != null)
            {
                if (exposureSlider != null)
                    exposureSlider.value = colorAdjustments.postExposure.value;
                if (contrastSlider != null)
                    contrastSlider.value = colorAdjustments.contrast.value;
                if (saturationSlider != null)
                    saturationSlider.value = colorAdjustments.saturation.value;
            }

            if (vignette != null && vignetteSlider != null)
            {
                vignetteSlider.value = vignette.intensity.value;
            }
        }
        #endregion

        #region Screenshot Capture
        /// <summary>
        /// Capture screenshot with current settings.
        /// </summary>
        public void CaptureScreenshot()
        {
            if (IsCapturingScreenshot) return;

            StartCoroutine(CaptureScreenshotCoroutine());
        }

        /// <summary>
        /// Capture screenshot coroutine.
        /// </summary>
        /// <returns>Screenshot capture coroutine</returns>
        private IEnumerator CaptureScreenshotCoroutine()
        {
            IsCapturingScreenshot = true;

            // Hide UI for clean screenshot
            bool uiWasActive = false;
            if (photoModeUI != null)
            {
                uiWasActive = photoModeUI.activeSelf;
                photoModeUI.SetActive(false);
            }

            // Wait for end of frame
            yield return new WaitForEndOfFrame();

            // Generate filename
            string timestamp = System.DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            string filename = $"CinderOfDarkness_{timestamp}_{screenshotCounter:000}.png";
            string fullPath = Path.Combine(screenshotPath, filename);

            // Capture screenshot
            try
            {
                ScreenCapture.CaptureScreenshot(fullPath);
                screenshotCounter++;

                // Play shutter sound
                if (shutterSound != null && audioSource != null)
                {
                    audioSource.PlayOneShot(shutterSound);
                }

                OnScreenshotCaptured?.Invoke(fullPath);
                Debug.Log($"Screenshot saved: {fullPath}");

                // Show notification (could be implemented with UI system)
                ShowScreenshotNotification(filename);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to capture screenshot: {e.Message}");
            }

            // Restore UI
            if (photoModeUI != null && uiWasActive)
            {
                photoModeUI.SetActive(true);
            }

            IsCapturingScreenshot = false;
        }

        /// <summary>
        /// Share screenshot via Steam overlay.
        /// </summary>
        public void ShareScreenshot()
        {
            if (!enableSteamShare) return;

            // Get latest screenshot
            string latestScreenshot = GetLatestScreenshot();
            if (!string.IsNullOrEmpty(latestScreenshot))
            {
                ShareScreenshotToSteam(latestScreenshot);
            }
        }

        /// <summary>
        /// Get path to latest screenshot.
        /// </summary>
        /// <returns>Latest screenshot path</returns>
        private string GetLatestScreenshot()
        {
            try
            {
                string[] screenshots = Directory.GetFiles(screenshotPath, "*.png");
                if (screenshots.Length > 0)
                {
                    System.Array.Sort(screenshots);
                    return screenshots[screenshots.Length - 1];
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to get latest screenshot: {e.Message}");
            }

            return null;
        }

        /// <summary>
        /// Share screenshot to Steam.
        /// </summary>
        /// <param name="screenshotPath">Path to screenshot</param>
        private void ShareScreenshotToSteam(string screenshotPath)
        {
            try
            {
                // Steam screenshot sharing implementation would go here
                // For now, just log the action
                Debug.Log($"Sharing screenshot to Steam: {screenshotPath}");

                // Could open Steam overlay or use Steam API
                // Application.OpenURL($"steam://screenshot/{screenshotPath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to share screenshot to Steam: {e.Message}");
            }
        }

        /// <summary>
        /// Show screenshot notification.
        /// </summary>
        /// <param name="filename">Screenshot filename</param>
        private void ShowScreenshotNotification(string filename)
        {
            // This could be implemented with a notification system
            Debug.Log($"📸 Screenshot saved: {filename}");
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set photo mode camera speed.
        /// </summary>
        /// <param name="speed">Camera movement speed</param>
        public void SetCameraSpeed(float speed)
        {
            cameraSpeed = Mathf.Max(0.1f, speed);
        }

        /// <summary>
        /// Set mouse sensitivity.
        /// </summary>
        /// <param name="sensitivity">Mouse sensitivity</param>
        public void SetMouseSensitivity(float sensitivity)
        {
            mouseSensitivity = Mathf.Max(0.1f, sensitivity);
        }

        /// <summary>
        /// Get screenshot directory path.
        /// </summary>
        /// <returns>Screenshot directory path</returns>
        public string GetScreenshotPath()
        {
            return screenshotPath;
        }

        /// <summary>
        /// Get number of screenshots taken.
        /// </summary>
        /// <returns>Screenshot count</returns>
        public int GetScreenshotCount()
        {
            try
            {
                return Directory.GetFiles(screenshotPath, "*.png").Length;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Open screenshot folder in file explorer.
        /// </summary>
        public void OpenScreenshotFolder()
        {
            try
            {
                if (Directory.Exists(screenshotPath))
                {
                    Application.OpenURL("file://" + screenshotPath);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to open screenshot folder: {e.Message}");
            }
        }

        /// <summary>
        /// Reset post-processing to defaults.
        /// </summary>
        public void ResetPostProcessing()
        {
            if (postProcessVolume != null && originalVolumeProfile != null)
            {
                postProcessVolume.profile = originalVolumeProfile;
                UpdateUIFromProfile();
            }
        }

        /// <summary>
        /// Set photo mode enabled state.
        /// </summary>
        /// <param name="enabled">True to enable photo mode</param>
        public void SetPhotoModeEnabled(bool enabled)
        {
            gameObject.SetActive(enabled);
        }
        #endregion
    }
}
