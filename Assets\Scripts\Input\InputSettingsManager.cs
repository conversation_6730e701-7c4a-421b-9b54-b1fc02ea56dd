using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using System.IO;

public class InputSettingsManager : MonoBehaviour
{
    [Header("Input Settings")]
    public InputActionAsset inputActions;
    public InputSettings currentSettings;
    public InputSettings defaultSettings;
    
    [Header("Sensitivity Settings")]
    public float mouseSensitivity = 1f;
    public float gamepadSensitivity = 1f;
    public bool invertMouseY = false;
    public bool invertGamepadY = false;
    
    [Header("Deadzone Settings")]
    public float leftStickDeadzone = 0.125f;
    public float rightStickDeadzone = 0.125f;
    public float triggerDeadzone = 0.1f;
    
    [Header("Accessibility")]
    public bool holdToRun = false;
    public bool holdToCrouch = false;
    public float doubleClickTime = 0.3f;
    public bool enableAutoAim = false;
    
    private MultiInputControlSystem inputSystem;
    private string settingsFilePath;
    
    [System.Serializable]
    public class InputSettings
    {
        [Header("Key Bindings")]
        public KeyBinding[] keyboardBindings;
        public ControllerBinding[] gamepadBindings;
        
        [Header("Sensitivity")]
        public float mouseSensitivity = 1f;
        public float gamepadSensitivity = 1f;
        public bool invertMouseY = false;
        public bool invertGamepadY = false;
        
        [Header("Deadzones")]
        public float leftStickDeadzone = 0.125f;
        public float rightStickDeadzone = 0.125f;
        public float triggerDeadzone = 0.1f;
        
        [Header("Accessibility")]
        public bool holdToRun = false;
        public bool holdToCrouch = false;
        public float doubleClickTime = 0.3f;
        public bool enableAutoAim = false;
        public bool vibrationEnabled = true;
        public float vibrationIntensity = 1f;
    }
    
    [System.Serializable]
    public class KeyBinding
    {
        public string actionName;
        public KeyCode primaryKey;
        public KeyCode secondaryKey;
        public bool isMouseButton = false;
        public int mouseButton = 0; // 0 = left, 1 = right, 2 = middle
    }
    
    [System.Serializable]
    public class ControllerBinding
    {
        public string actionName;
        public string primaryButton;
        public string secondaryButton;
        public bool isAxis = false;
        public string axisName;
        public bool invertAxis = false;
    }
    
    void Start()
    {
        inputSystem = GetComponent<MultiInputControlSystem>();
        settingsFilePath = Path.Combine(Application.persistentDataPath, "input_settings.json");
        
        InitializeDefaultSettings();
        LoadInputSettings();
        ApplyInputSettings();
    }
    
    void InitializeDefaultSettings()
    {
        defaultSettings = new InputSettings
        {
            keyboardBindings = CreateDefaultKeyboardBindings(),
            gamepadBindings = CreateDefaultGamepadBindings(),
            mouseSensitivity = 1f,
            gamepadSensitivity = 1f,
            invertMouseY = false,
            invertGamepadY = false,
            leftStickDeadzone = 0.125f,
            rightStickDeadzone = 0.125f,
            triggerDeadzone = 0.1f,
            holdToRun = false,
            holdToCrouch = false,
            doubleClickTime = 0.3f,
            enableAutoAim = false,
            vibrationEnabled = true,
            vibrationIntensity = 1f
        };
        
        // Copy default settings to current settings
        currentSettings = JsonUtility.FromJson<InputSettings>(JsonUtility.ToJson(defaultSettings));
    }
    
    KeyBinding[] CreateDefaultKeyboardBindings()
    {
        return new KeyBinding[]
        {
            new KeyBinding { actionName = "Move Forward", primaryKey = KeyCode.W },
            new KeyBinding { actionName = "Move Backward", primaryKey = KeyCode.S },
            new KeyBinding { actionName = "Move Left", primaryKey = KeyCode.A },
            new KeyBinding { actionName = "Move Right", primaryKey = KeyCode.D },
            new KeyBinding { actionName = "Attack", primaryKey = KeyCode.Mouse0, isMouseButton = true, mouseButton = 0 },
            new KeyBinding { actionName = "Block", primaryKey = KeyCode.Mouse1, isMouseButton = true, mouseButton = 1 },
            new KeyBinding { actionName = "Dodge", primaryKey = KeyCode.Space, secondaryKey = KeyCode.LeftShift },
            new KeyBinding { actionName = "Jump", primaryKey = KeyCode.Space },
            new KeyBinding { actionName = "Run", primaryKey = KeyCode.LeftShift },
            new KeyBinding { actionName = "Crouch", primaryKey = KeyCode.LeftControl, secondaryKey = KeyCode.C },
            new KeyBinding { actionName = "Interact", primaryKey = KeyCode.E, secondaryKey = KeyCode.F },
            new KeyBinding { actionName = "Menu", primaryKey = KeyCode.Escape, secondaryKey = KeyCode.Tab },
            new KeyBinding { actionName = "Inventory", primaryKey = KeyCode.Tab, secondaryKey = KeyCode.I },
            new KeyBinding { actionName = "Map", primaryKey = KeyCode.M },
            new KeyBinding { actionName = "Quick Save", primaryKey = KeyCode.F5 },
            new KeyBinding { actionName = "Quick Load", primaryKey = KeyCode.F9 },
            new KeyBinding { actionName = "Screenshot", primaryKey = KeyCode.F12 },
            new KeyBinding { actionName = "Toggle HUD", primaryKey = KeyCode.H },
            new KeyBinding { actionName = "Pause", primaryKey = KeyCode.P }
        };
    }
    
    ControllerBinding[] CreateDefaultGamepadBindings()
    {
        return new ControllerBinding[]
        {
            new ControllerBinding { actionName = "Move", isAxis = true, axisName = "Left Stick" },
            new ControllerBinding { actionName = "Look", isAxis = true, axisName = "Right Stick" },
            new ControllerBinding { actionName = "Attack", primaryButton = "X" }, // Xbox X, PS Square
            new ControllerBinding { actionName = "Block", primaryButton = "LT", isAxis = true, axisName = "Left Trigger" },
            new ControllerBinding { actionName = "Dodge", primaryButton = "A" }, // Xbox A, PS Cross
            new ControllerBinding { actionName = "Jump", primaryButton = "A" },
            new ControllerBinding { actionName = "Run", primaryButton = "LS" }, // Left stick click
            new ControllerBinding { actionName = "Crouch", primaryButton = "B" }, // Xbox B, PS Circle
            new ControllerBinding { actionName = "Interact", primaryButton = "Y" }, // Xbox Y, PS Triangle
            new ControllerBinding { actionName = "Menu", primaryButton = "Menu" }, // Xbox Menu, PS Options
            new ControllerBinding { actionName = "Inventory", primaryButton = "View" }, // Xbox View, PS Share
            new ControllerBinding { actionName = "Map", primaryButton = "RS" }, // Right stick click
            new ControllerBinding { actionName = "Quick Menu", primaryButton = "LB" },
            new ControllerBinding { actionName = "Target Lock", primaryButton = "RB" }
        };
    }
    
    public void LoadInputSettings()
    {
        if (File.Exists(settingsFilePath))
        {
            try
            {
                string json = File.ReadAllText(settingsFilePath);
                currentSettings = JsonUtility.FromJson<InputSettings>(json);
                Debug.Log("Input settings loaded successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load input settings: {e.Message}");
                ResetToDefaultSettings();
            }
        }
        else
        {
            Debug.Log("No input settings file found, using defaults");
            ResetToDefaultSettings();
        }
    }
    
    public void SaveInputSettings()
    {
        try
        {
            string json = JsonUtility.ToJson(currentSettings, true);
            File.WriteAllText(settingsFilePath, json);
            Debug.Log("Input settings saved successfully");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save input settings: {e.Message}");
        }
    }
    
    public void ApplyInputSettings()
    {
        // Apply sensitivity settings
        mouseSensitivity = currentSettings.mouseSensitivity;
        gamepadSensitivity = currentSettings.gamepadSensitivity;
        invertMouseY = currentSettings.invertMouseY;
        invertGamepadY = currentSettings.invertGamepadY;
        
        // Apply deadzone settings
        leftStickDeadzone = currentSettings.leftStickDeadzone;
        rightStickDeadzone = currentSettings.rightStickDeadzone;
        triggerDeadzone = currentSettings.triggerDeadzone;
        
        // Apply accessibility settings
        holdToRun = currentSettings.holdToRun;
        holdToCrouch = currentSettings.holdToCrouch;
        doubleClickTime = currentSettings.doubleClickTime;
        enableAutoAim = currentSettings.enableAutoAim;
        
        // Apply vibration settings
        if (inputSystem != null)
        {
            inputSystem.SetVibrationEnabled(currentSettings.vibrationEnabled);
            inputSystem.SetVibrationIntensity(currentSettings.vibrationIntensity);
        }
        
        // Apply input bindings
        ApplyKeyboardBindings();
        ApplyGamepadBindings();
        
        Debug.Log("Input settings applied");
    }
    
    void ApplyKeyboardBindings()
    {
        if (inputActions == null) return;
        
        foreach (var binding in currentSettings.keyboardBindings)
        {
            var action = inputActions.FindAction(binding.actionName);
            if (action != null)
            {
                // Remove existing bindings
                for (int i = action.bindings.Count - 1; i >= 0; i--)
                {
                    if (action.bindings[i].isPartOfComposite == false)
                    {
                        action.RemoveBindingOverride(i);
                    }
                }
                
                // Add new binding
                if (binding.isMouseButton)
                {
                    string mousePath = $"<Mouse>/button{binding.mouseButton}";
                    action.AddBinding(mousePath);
                }
                else
                {
                    string keyPath = $"<Keyboard>/{binding.primaryKey.ToString().ToLower()}";
                    action.AddBinding(keyPath);
                    
                    // Add secondary binding if exists
                    if (binding.secondaryKey != KeyCode.None)
                    {
                        string secondaryPath = $"<Keyboard>/{binding.secondaryKey.ToString().ToLower()}";
                        action.AddBinding(secondaryPath);
                    }
                }
            }
        }
    }
    
    void ApplyGamepadBindings()
    {
        if (inputActions == null) return;
        
        foreach (var binding in currentSettings.gamepadBindings)
        {
            var action = inputActions.FindAction(binding.actionName);
            if (action != null)
            {
                string bindingPath = "";
                
                if (binding.isAxis)
                {
                    switch (binding.axisName)
                    {
                        case "Left Stick":
                            bindingPath = "<Gamepad>/leftStick";
                            break;
                        case "Right Stick":
                            bindingPath = "<Gamepad>/rightStick";
                            break;
                        case "Left Trigger":
                            bindingPath = "<Gamepad>/leftTrigger";
                            break;
                        case "Right Trigger":
                            bindingPath = "<Gamepad>/rightTrigger";
                            break;
                    }
                }
                else
                {
                    bindingPath = $"<Gamepad>/{binding.primaryButton.ToLower()}";
                }
                
                if (!string.IsNullOrEmpty(bindingPath))
                {
                    action.AddBinding(bindingPath);
                }
            }
        }
    }
    
    public void ResetToDefaultSettings()
    {
        currentSettings = JsonUtility.FromJson<InputSettings>(JsonUtility.ToJson(defaultSettings));
        ApplyInputSettings();
        SaveInputSettings();
    }
    
    public void RemapKey(string actionName, KeyCode newKey, bool isPrimary = true)
    {
        foreach (var binding in currentSettings.keyboardBindings)
        {
            if (binding.actionName == actionName)
            {
                if (isPrimary)
                    binding.primaryKey = newKey;
                else
                    binding.secondaryKey = newKey;
                break;
            }
        }
        
        ApplyInputSettings();
        SaveInputSettings();
    }
    
    public void RemapControllerButton(string actionName, string newButton, bool isPrimary = true)
    {
        foreach (var binding in currentSettings.gamepadBindings)
        {
            if (binding.actionName == actionName)
            {
                if (isPrimary)
                    binding.primaryButton = newButton;
                else
                    binding.secondaryButton = newButton;
                break;
            }
        }
        
        ApplyInputSettings();
        SaveInputSettings();
    }
    
    public void SetMouseSensitivity(float sensitivity)
    {
        currentSettings.mouseSensitivity = Mathf.Clamp(sensitivity, 0.1f, 5f);
        mouseSensitivity = currentSettings.mouseSensitivity;
        SaveInputSettings();
    }
    
    public void SetGamepadSensitivity(float sensitivity)
    {
        currentSettings.gamepadSensitivity = Mathf.Clamp(sensitivity, 0.1f, 5f);
        gamepadSensitivity = currentSettings.gamepadSensitivity;
        SaveInputSettings();
    }
    
    public void SetInvertMouseY(bool invert)
    {
        currentSettings.invertMouseY = invert;
        invertMouseY = invert;
        SaveInputSettings();
    }
    
    public void SetInvertGamepadY(bool invert)
    {
        currentSettings.invertGamepadY = invert;
        invertGamepadY = invert;
        SaveInputSettings();
    }
    
    public void SetLeftStickDeadzone(float deadzone)
    {
        currentSettings.leftStickDeadzone = Mathf.Clamp01(deadzone);
        leftStickDeadzone = currentSettings.leftStickDeadzone;
        SaveInputSettings();
    }
    
    public void SetRightStickDeadzone(float deadzone)
    {
        currentSettings.rightStickDeadzone = Mathf.Clamp01(deadzone);
        rightStickDeadzone = currentSettings.rightStickDeadzone;
        SaveInputSettings();
    }
    
    public void SetTriggerDeadzone(float deadzone)
    {
        currentSettings.triggerDeadzone = Mathf.Clamp01(deadzone);
        triggerDeadzone = currentSettings.triggerDeadzone;
        SaveInputSettings();
    }
    
    public void SetHoldToRun(bool holdToRun)
    {
        currentSettings.holdToRun = holdToRun;
        this.holdToRun = holdToRun;
        SaveInputSettings();
    }
    
    public void SetHoldToCrouch(bool holdToCrouch)
    {
        currentSettings.holdToCrouch = holdToCrouch;
        this.holdToCrouch = holdToCrouch;
        SaveInputSettings();
    }
    
    public void SetVibrationEnabled(bool enabled)
    {
        currentSettings.vibrationEnabled = enabled;
        if (inputSystem != null)
        {
            inputSystem.SetVibrationEnabled(enabled);
        }
        SaveInputSettings();
    }
    
    public void SetVibrationIntensity(float intensity)
    {
        currentSettings.vibrationIntensity = Mathf.Clamp01(intensity);
        if (inputSystem != null)
        {
            inputSystem.SetVibrationIntensity(currentSettings.vibrationIntensity);
        }
        SaveInputSettings();
    }
    
    public void SetAutoAim(bool enabled)
    {
        currentSettings.enableAutoAim = enabled;
        enableAutoAim = enabled;
        SaveInputSettings();
    }
    
    // Input validation
    public bool IsKeyAlreadyBound(KeyCode key, string excludeAction = "")
    {
        foreach (var binding in currentSettings.keyboardBindings)
        {
            if (binding.actionName != excludeAction)
            {
                if (binding.primaryKey == key || binding.secondaryKey == key)
                    return true;
            }
        }
        return false;
    }
    
    public bool IsControllerButtonAlreadyBound(string button, string excludeAction = "")
    {
        foreach (var binding in currentSettings.gamepadBindings)
        {
            if (binding.actionName != excludeAction)
            {
                if (binding.primaryButton == button || binding.secondaryButton == button)
                    return true;
            }
        }
        return false;
    }
    
    // Getters
    public KeyCode GetKeyBinding(string actionName, bool primary = true)
    {
        foreach (var binding in currentSettings.keyboardBindings)
        {
            if (binding.actionName == actionName)
            {
                return primary ? binding.primaryKey : binding.secondaryKey;
            }
        }
        return KeyCode.None;
    }
    
    public string GetControllerBinding(string actionName, bool primary = true)
    {
        foreach (var binding in currentSettings.gamepadBindings)
        {
            if (binding.actionName == actionName)
            {
                return primary ? binding.primaryButton : binding.secondaryButton;
            }
        }
        return "";
    }
    
    public InputSettings GetCurrentSettings() => currentSettings;
    public InputSettings GetDefaultSettings() => defaultSettings;
}
