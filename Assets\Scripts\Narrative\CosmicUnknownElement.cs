using UnityEngine;
using System.Collections;

public class CosmicUnknownElement : MonoBehaviour
{
    [Header("Cosmic Encounter Configuration")]
    public bool hasTriggered = false;
    public bool isThirdSageDefeated = false;
    public float encounterDuration = 45f;
    
    [Header("Visual Elements")]
    public GameObject massiveShadow;
    public ParticleSystem cosmicEffect;
    public Light cosmicLight;
    public Material skyboxMaterial;
    public Color originalSkyColor;
    public Color cosmicSkyColor;
    
    [Header("Audio")]
    public AudioClip cosmicAmbience;
    public AudioClip unknownVoice;
    public AudioClip realityDistortion;
    public AudioSource cosmicAudioSource;
    
    [Header("Existential Impact")]
    public float existentialDreadLevel = 100f;
    public float cosmicInsignificanceWeight = 75f;
    public float mysteryDepth = 90f;
    
    private TearOfAshSystem tearSystem;
    private PsychologicalSystem psycheSystem;
    private DynamicMusicalSystem musicalSystem;
    private TimeProgressionSystem timeSystem;
    private bool encounterInProgress = false;
    
    void Start()
    {
        tearSystem = GetComponent<TearOfAshSystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        musicalSystem = GetComponent<DynamicMusicalSystem>();
        timeSystem = GetComponent<TimeProgressionSystem>();
        
        // Store original sky color
        if (RenderSettings.skybox != null)
        {
            originalSkyColor = RenderSettings.ambientLight;
        }
        
        // Hide cosmic elements initially
        if (massiveShadow != null)
        {
            massiveShadow.SetActive(false);
        }
        
        Debug.Log("Cosmic Unknown Element initialized - waiting for the third sage's defeat");
    }
    
    void Update()
    {
        CheckForTriggerConditions();
    }
    
    void CheckForTriggerConditions()
    {
        if (hasTriggered || encounterInProgress) return;
        
        // Check if third sage has been defeated
        if (tearSystem != null && tearSystem.GetSagesDefeated() >= 3)
        {
            if (!isThirdSageDefeated)
            {
                isThirdSageDefeated = true;
                // Trigger after a short delay to let the sage defeat settle
                StartCoroutine(DelayedCosmicEncounter());
            }
        }
    }
    
    IEnumerator DelayedCosmicEncounter()
    {
        // Wait for the player to process the sage's defeat
        yield return new WaitForSeconds(10f);
        
        // Trigger the cosmic encounter
        TriggerCosmicEncounter();
    }
    
    void TriggerCosmicEncounter()
    {
        if (hasTriggered) return;
        
        hasTriggered = true;
        encounterInProgress = true;
        
        StartCoroutine(CosmicEncounterSequence());
    }
    
    IEnumerator CosmicEncounterSequence()
    {
        // Phase 1: Subtle Beginning
        yield return StartCoroutine(SubtleBeginningPhase());
        
        // Phase 2: Reality Distortion
        yield return StartCoroutine(RealityDistortionPhase());
        
        // Phase 3: The Presence Manifests
        yield return StartCoroutine(PresenceManifestationPhase());
        
        // Phase 4: The Single Statement
        yield return StartCoroutine(CosmicStatementPhase());
        
        // Phase 5: Vanishing Without Trace
        yield return StartCoroutine(VanishingPhase());
        
        // Phase 6: Aftermath
        yield return StartCoroutine(AftermathPhase());
        
        encounterInProgress = false;
    }
    
    IEnumerator SubtleBeginningPhase()
    {
        ShowCosmicMessage("Something feels... different.");
        yield return new WaitForSeconds(3f);
        
        ShowCosmicMessage("The air grows heavy, as if the world itself is holding its breath.");
        yield return new WaitForSeconds(4f);
        
        // Subtle audio changes
        if (musicalSystem != null)
        {
            musicalSystem.TriggerEmotionalCue(DynamicMusicalSystem.EmotionalCue.EmotionalTrigger.TruthRevealed);
        }
        
        ShowCosmicMessage("The shadows seem deeper, though no clouds have crossed the sun.");
        yield return new WaitForSeconds(3f);
    }
    
    IEnumerator RealityDistortionPhase()
    {
        ShowCosmicMessage("Reality begins to... bend.");
        yield return new WaitForSeconds(2f);
        
        // Start reality distortion audio
        if (realityDistortion != null && cosmicAudioSource != null)
        {
            cosmicAudioSource.clip = realityDistortion;
            cosmicAudioSource.Play();
        }
        
        // Gradual sky color change
        StartCoroutine(GradualSkyColorChange(originalSkyColor, cosmicSkyColor, 8f));
        
        ShowCosmicMessage("The horizon wavers like heat shimmer, but the air is cold.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("Time itself seems to slow, each heartbeat an eternity.");
        yield return new WaitForSeconds(3f);
        
        // Apply psychological impact
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(15f, "Reality distortion - cosmic encounter");
        }
        
        ShowCosmicMessage("You feel watched by something vast and incomprehensible.");
        yield return new WaitForSeconds(4f);
    }
    
    IEnumerator PresenceManifestationPhase()
    {
        ShowCosmicMessage("Then... it appears.");
        yield return new WaitForSeconds(3f);
        
        // Manifest the massive shadow
        if (massiveShadow != null)
        {
            massiveShadow.SetActive(true);
            StartCoroutine(FadeInMassiveShadow());
        }
        
        // Start cosmic effects
        if (cosmicEffect != null)
        {
            cosmicEffect.Play();
        }
        
        if (cosmicLight != null)
        {
            cosmicLight.enabled = true;
            StartCoroutine(PulseCosmicLight());
        }
        
        ShowCosmicMessage("A shadow falls across the sky - not cast by any sun, but existing in defiance of light itself.");
        yield return new WaitForSeconds(5f);
        
        ShowCosmicMessage("It is massive beyond comprehension, featureless beyond description.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("Your mind struggles to process what you are seeing, and fails.");
        yield return new WaitForSeconds(4f);
        
        // Intense psychological impact
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(existentialDreadLevel, "Witnessing the cosmic unknown");
            psycheSystem.AddCosmicInsignificanceRealization(cosmicInsignificanceWeight);
        }
        
        ShowCosmicMessage("You feel smaller than an atom, less significant than a whisper in a hurricane.");
        yield return new WaitForSeconds(5f);
    }
    
    IEnumerator CosmicStatementPhase()
    {
        ShowCosmicMessage("And then... it speaks.");
        yield return new WaitForSeconds(3f);
        
        // Play the unknown voice
        if (unknownVoice != null && cosmicAudioSource != null)
        {
            cosmicAudioSource.clip = unknownVoice;
            cosmicAudioSource.pitch = 0.3f; // Deep, otherworldly
            cosmicAudioSource.Play();
        }
        
        // The single, cryptic statement
        ShowCosmicMessage("\"You are not the first fire... nor the last.\"");
        yield return new WaitForSeconds(8f);
        
        ShowCosmicMessage("The words resonate not in your ears, but in your very essence.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("They carry the weight of eons, the certainty of cosmic truth.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("You understand nothing, yet feel that everything has changed.");
        yield return new WaitForSeconds(5f);
        
        // Apply profound philosophical impact
        if (psycheSystem != null)
        {
            psycheSystem.AddCosmicRevelation("FirstFireLastFire", mysteryDepth);
        }
        
        // Unlock special dialogue option
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            gameManager.UnlockDialogueOption("CosmicEncounterWitness");
            gameManager.UnlockDialogueOption("FirstFireLastFire");
        }
    }
    
    IEnumerator VanishingPhase()
    {
        ShowCosmicMessage("And then... it is gone.");
        yield return new WaitForSeconds(3f);
        
        // Fade out the massive shadow
        if (massiveShadow != null)
        {
            StartCoroutine(FadeOutMassiveShadow());
        }
        
        // Stop cosmic effects
        if (cosmicEffect != null)
        {
            cosmicEffect.Stop();
        }
        
        if (cosmicLight != null)
        {
            cosmicLight.enabled = false;
        }
        
        ShowCosmicMessage("Not fading, not departing - simply ceasing to be there, as if it never was.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("The sky returns to normal. The air lightens. Time resumes its flow.");
        yield return new WaitForSeconds(4f);
        
        // Restore normal sky
        StartCoroutine(GradualSkyColorChange(cosmicSkyColor, originalSkyColor, 5f));
        
        ShowCosmicMessage("But you know you have been changed by what you witnessed.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("No trace remains. No evidence. No proof.");
        yield return new WaitForSeconds(3f);
        
        ShowCosmicMessage("Only the memory, and the terrible certainty that you are not alone in the cosmos.");
        yield return new WaitForSeconds(5f);
    }
    
    IEnumerator AftermathPhase()
    {
        ShowCosmicMessage("You stand in the aftermath of incomprehension.");
        yield return new WaitForSeconds(3f);
        
        ShowCosmicMessage("The world looks the same, but you see it differently now.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("Questions burn in your mind - questions you cannot voice, cannot answer.");
        yield return new WaitForSeconds(4f);
        
        ShowCosmicMessage("What did it mean? What are you? What is your purpose in the vast design?");
        yield return new WaitForSeconds(5f);
        
        ShowCosmicMessage("The silence that follows is more profound than any answer could be.");
        yield return new WaitForSeconds(4f);
        
        // Final psychological impact
        if (psycheSystem != null)
        {
            psycheSystem.AddExistentialQuestioning(50f);
            psycheSystem.AddCosmicPerspective(75f);
        }
        
        // Mark the encounter as complete
        MarkEncounterComplete();
    }
    
    IEnumerator FadeInMassiveShadow()
    {
        if (massiveShadow == null) return;
        
        Renderer shadowRenderer = massiveShadow.GetComponent<Renderer>();
        if (shadowRenderer == null) return;
        
        Material shadowMaterial = shadowRenderer.material;
        Color startColor = shadowMaterial.color;
        startColor.a = 0f;
        shadowMaterial.color = startColor;
        
        float fadeTime = 10f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Lerp(0f, 0.8f, elapsed / fadeTime);
            
            Color currentColor = shadowMaterial.color;
            currentColor.a = alpha;
            shadowMaterial.color = currentColor;
            
            yield return null;
        }
    }
    
    IEnumerator FadeOutMassiveShadow()
    {
        if (massiveShadow == null) return;
        
        Renderer shadowRenderer = massiveShadow.GetComponent<Renderer>();
        if (shadowRenderer == null) return;
        
        Material shadowMaterial = shadowRenderer.material;
        Color startColor = shadowMaterial.color;
        
        float fadeTime = 3f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Lerp(startColor.a, 0f, elapsed / fadeTime);
            
            Color currentColor = shadowMaterial.color;
            currentColor.a = alpha;
            shadowMaterial.color = currentColor;
            
            yield return null;
        }
        
        massiveShadow.SetActive(false);
    }
    
    IEnumerator PulseCosmicLight()
    {
        if (cosmicLight == null) return;
        
        float pulseSpeed = 0.5f;
        float baseIntensity = cosmicLight.intensity;
        
        while (cosmicLight.enabled)
        {
            float pulse = Mathf.Sin(Time.time * pulseSpeed) * 0.3f + 0.7f;
            cosmicLight.intensity = baseIntensity * pulse;
            yield return null;
        }
    }
    
    IEnumerator GradualSkyColorChange(Color fromColor, Color toColor, float duration)
    {
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            Color currentColor = Color.Lerp(fromColor, toColor, progress);
            RenderSettings.ambientLight = currentColor;
            
            yield return null;
        }
        
        RenderSettings.ambientLight = toColor;
    }
    
    void MarkEncounterComplete()
    {
        // Save that the cosmic encounter has occurred
        PlayerPrefs.SetInt("CosmicEncounterTriggered", 1);
        PlayerPrefs.SetFloat("CosmicEncounterTime", Time.time);
        PlayerPrefs.Save();
        
        // Add to game manager's event log
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            gameManager.RecordCosmicEncounter();
        }
        
        Debug.Log("Cosmic encounter completed - the universe has acknowledged The Cinderborn");
    }
    
    void ShowCosmicMessage(string message)
    {
        // Display with minimal UI interference
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowCosmicText(message); // Special cosmic text display
        }
        
        Debug.Log($"Cosmic Unknown: {message}");
    }
    
    // Public methods for external systems
    public bool HasCosmicEncounterOccurred()
    {
        return PlayerPrefs.GetInt("CosmicEncounterTriggered", 0) == 1;
    }
    
    public float GetCosmicEncounterTime()
    {
        return PlayerPrefs.GetFloat("CosmicEncounterTime", 0f);
    }
    
    public void ForceCosmicEncounter()
    {
        if (!hasTriggered)
        {
            TriggerCosmicEncounter();
        }
    }
    
    // Getters
    public bool IsEncounterInProgress() => encounterInProgress;
    public bool HasTriggered() => hasTriggered;
    public float GetExistentialDreadLevel() => existentialDreadLevel;
    public float GetMysteryDepth() => mysteryDepth;
}
