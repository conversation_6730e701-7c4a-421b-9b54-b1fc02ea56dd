using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

namespace CinderOfDarkness.Settings
{
    /// <summary>
    /// Graphics Settings Manager for Cinder of Darkness.
    /// Manages URP quality settings, VFX, shadows, bloom, and other graphics options.
    /// </summary>
    public class GraphicsSettingsManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Graphics Settings")]
        [SerializeField] private string settingsSaveKey = "CinderGraphicsSettings";
        [SerializeField] private bool autoSaveSettings = true;
        [SerializeField] private bool applySettingsOnStart = true;

        [Header("URP Assets")]
        [SerializeField] private UniversalRenderPipelineAsset lowQualityAsset;
        [SerializeField] private UniversalRenderPipelineAsset mediumQualityAsset;
        [SerializeField] private UniversalRenderPipelineAsset highQualityAsset;

        [Header("Post-Processing")]
        [SerializeField] private Volume postProcessVolume;
        [SerializeField] private VolumeProfile lowQualityProfile;
        [SerializeField] private VolumeProfile mediumQualityProfile;
        [SerializeField] private VolumeProfile highQualityProfile;

        [Header("UI References")]
        [SerializeField] private TMP_Dropdown qualityDropdown;
        [SerializeField] private TMP_Dropdown resolutionDropdown;
        [SerializeField] private TMP_Dropdown windowModeDropdown;
        [SerializeField] private Slider renderScaleSlider;
        [SerializeField] private TextMeshProUGUI renderScaleText;
        [SerializeField] private Toggle vsyncToggle;
        [SerializeField] private Toggle shadowsToggle;
        [SerializeField] private Toggle vfxToggle;
        [SerializeField] private Toggle bloomToggle;
        [SerializeField] private Toggle antiAliasingToggle;
        [SerializeField] private Slider fovSlider;
        [SerializeField] private TextMeshProUGUI fovText;
        [SerializeField] private Button applyButton;
        [SerializeField] private Button resetButton;
        #endregion

        #region Public Properties
        public static GraphicsSettingsManager Instance { get; private set; }
        public GraphicsQuality CurrentQuality { get; private set; } = GraphicsQuality.Medium;
        public bool SettingsChanged { get; private set; }
        #endregion

        #region Private Fields
        private GraphicsSettings currentSettings;
        private GraphicsSettings pendingSettings;
        private Resolution[] availableResolutions;
        private Camera mainCamera;
        private VisualEffectsManager vfxManager;

        // Post-processing components
        private Bloom bloom;
        private DepthOfField depthOfField;
        private ColorAdjustments colorAdjustments;
        private Vignette vignette;
        #endregion

        #region Events
        public System.Action<GraphicsSettings> OnSettingsChanged;
        public System.Action<GraphicsQuality> OnQualityChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Graphics Settings Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGraphicsSettings();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and load settings.
        /// </summary>
        private void Start()
        {
            SetupComponents();
            LoadSettings();
            SetupUI();

            if (applySettingsOnStart)
            {
                ApplySettings(currentSettings);
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize graphics settings system.
        /// </summary>
        private void InitializeGraphicsSettings()
        {
            // Get available resolutions
            availableResolutions = Screen.resolutions;

            // Initialize default settings
            currentSettings = GetDefaultSettings();
            pendingSettings = new GraphicsSettings(currentSettings);
        }

        /// <summary>
        /// Setup component references.
        /// </summary>
        private void SetupComponents()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }

            vfxManager = VisualEffectsManager.Instance;

            // Setup post-processing components
            if (postProcessVolume != null && postProcessVolume.profile != null)
            {
                postProcessVolume.profile.TryGet(out bloom);
                postProcessVolume.profile.TryGet(out depthOfField);
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out vignette);
            }
        }

        /// <summary>
        /// Setup UI event listeners and populate dropdowns.
        /// </summary>
        private void SetupUI()
        {
            // Setup quality dropdown
            if (qualityDropdown != null)
            {
                qualityDropdown.ClearOptions();
                qualityDropdown.AddOptions(new List<string> { "Low", "Medium", "High" });
                qualityDropdown.onValueChanged.AddListener(OnQualityDropdownChanged);
            }

            // Setup resolution dropdown
            if (resolutionDropdown != null)
            {
                SetupResolutionDropdown();
                resolutionDropdown.onValueChanged.AddListener(OnResolutionDropdownChanged);
            }

            // Setup window mode dropdown
            if (windowModeDropdown != null)
            {
                windowModeDropdown.ClearOptions();
                windowModeDropdown.AddOptions(new List<string> { "Fullscreen", "Windowed", "Borderless" });
                windowModeDropdown.onValueChanged.AddListener(OnWindowModeDropdownChanged);
            }

            // Setup sliders
            if (renderScaleSlider != null)
            {
                renderScaleSlider.minValue = 0.5f;
                renderScaleSlider.maxValue = 2.0f;
                renderScaleSlider.onValueChanged.AddListener(OnRenderScaleChanged);
            }

            if (fovSlider != null)
            {
                fovSlider.minValue = 60f;
                fovSlider.maxValue = 120f;
                fovSlider.onValueChanged.AddListener(OnFOVChanged);
            }

            // Setup toggles
            if (vsyncToggle != null)
                vsyncToggle.onValueChanged.AddListener(OnVSyncToggleChanged);

            if (shadowsToggle != null)
                shadowsToggle.onValueChanged.AddListener(OnShadowsToggleChanged);

            if (vfxToggle != null)
                vfxToggle.onValueChanged.AddListener(OnVFXToggleChanged);

            if (bloomToggle != null)
                bloomToggle.onValueChanged.AddListener(OnBloomToggleChanged);

            if (antiAliasingToggle != null)
                antiAliasingToggle.onValueChanged.AddListener(OnAntiAliasingToggleChanged);

            // Setup buttons
            if (applyButton != null)
                applyButton.onClick.AddListener(ApplyPendingSettings);

            if (resetButton != null)
                resetButton.onClick.AddListener(ResetToDefaults);

            // Update UI with current settings
            UpdateUIFromSettings(currentSettings);
        }

        /// <summary>
        /// Setup resolution dropdown with available resolutions.
        /// </summary>
        private void SetupResolutionDropdown()
        {
            List<string> resolutionOptions = new List<string>();

            foreach (Resolution resolution in availableResolutions)
            {
                string option = $"{resolution.width} x {resolution.height}";
                if (resolution.refreshRate > 0)
                {
                    option += $" @ {resolution.refreshRate}Hz";
                }
                resolutionOptions.Add(option);
            }

            resolutionDropdown.ClearOptions();
            resolutionDropdown.AddOptions(resolutionOptions);
        }
        #endregion

        #region Settings Management
        /// <summary>
        /// Get default graphics settings.
        /// </summary>
        /// <returns>Default graphics settings</returns>
        private GraphicsSettings GetDefaultSettings()
        {
            return new GraphicsSettings
            {
                quality = GraphicsQuality.Medium,
                resolutionWidth = Screen.currentResolution.width,
                resolutionHeight = Screen.currentResolution.height,
                refreshRate = Screen.currentResolution.refreshRate,
                windowMode = WindowMode.Fullscreen,
                renderScale = 1.0f,
                vsyncEnabled = true,
                shadowsEnabled = true,
                vfxEnabled = true,
                bloomEnabled = true,
                antiAliasingEnabled = true,
                fieldOfView = 75f
            };
        }

        /// <summary>
        /// Apply graphics settings.
        /// </summary>
        /// <param name="settings">Settings to apply</param>
        public void ApplySettings(GraphicsSettings settings)
        {
            currentSettings = new GraphicsSettings(settings);
            CurrentQuality = settings.quality;

            // Apply quality preset
            ApplyQualityPreset(settings.quality);

            // Apply resolution and window mode
            ApplyDisplaySettings(settings);

            // Apply render scale
            ApplyRenderScale(settings.renderScale);

            // Apply VSync
            QualitySettings.vSyncCount = settings.vsyncEnabled ? 1 : 0;

            // Apply shadows
            ApplyShadowSettings(settings.shadowsEnabled);

            // Apply VFX
            ApplyVFXSettings(settings.vfxEnabled);

            // Apply post-processing effects
            ApplyPostProcessingSettings(settings);

            // Apply field of view
            if (mainCamera != null)
            {
                mainCamera.fieldOfView = settings.fieldOfView;
            }

            // Save settings if auto-save is enabled
            if (autoSaveSettings)
            {
                SaveSettings();
            }

            // Update UI
            UpdateUIFromSettings(settings);

            // Reset settings changed flag
            SettingsChanged = false;

            // Trigger events
            OnSettingsChanged?.Invoke(settings);
            OnQualityChanged?.Invoke(settings.quality);

            Debug.Log($"Graphics settings applied: {settings.quality} quality");
        }

        /// <summary>
        /// Apply quality preset.
        /// </summary>
        /// <param name="quality">Quality level</param>
        private void ApplyQualityPreset(GraphicsQuality quality)
        {
            UniversalRenderPipelineAsset urpAsset = null;
            VolumeProfile volumeProfile = null;

            switch (quality)
            {
                case GraphicsQuality.Low:
                    urpAsset = lowQualityAsset;
                    volumeProfile = lowQualityProfile;
                    break;
                case GraphicsQuality.Medium:
                    urpAsset = mediumQualityAsset;
                    volumeProfile = mediumQualityProfile;
                    break;
                case GraphicsQuality.High:
                    urpAsset = highQualityAsset;
                    volumeProfile = highQualityProfile;
                    break;
            }

            // Apply URP asset
            if (urpAsset != null)
            {
                GraphicsSettings.renderPipelineAsset = urpAsset;
                QualitySettings.renderPipeline = urpAsset;
            }

            // Apply post-processing profile
            if (postProcessVolume != null && volumeProfile != null)
            {
                postProcessVolume.profile = volumeProfile;

                // Re-get post-processing components
                postProcessVolume.profile.TryGet(out bloom);
                postProcessVolume.profile.TryGet(out depthOfField);
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out vignette);
            }
        }

        /// <summary>
        /// Apply display settings (resolution, window mode).
        /// </summary>
        /// <param name="settings">Graphics settings</param>
        private void ApplyDisplaySettings(GraphicsSettings settings)
        {
            FullScreenMode fullScreenMode = FullScreenMode.ExclusiveFullScreen;

            switch (settings.windowMode)
            {
                case WindowMode.Fullscreen:
                    fullScreenMode = FullScreenMode.ExclusiveFullScreen;
                    break;
                case WindowMode.Windowed:
                    fullScreenMode = FullScreenMode.Windowed;
                    break;
                case WindowMode.Borderless:
                    fullScreenMode = FullScreenMode.FullScreenWindow;
                    break;
            }

            Screen.SetResolution(settings.resolutionWidth, settings.resolutionHeight, fullScreenMode, settings.refreshRate);
        }

        /// <summary>
        /// Apply render scale setting.
        /// </summary>
        /// <param name="renderScale">Render scale value</param>
        private void ApplyRenderScale(float renderScale)
        {
            var urpAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                // Note: This would require reflection or custom URP asset modification
                // For now, we'll use a simplified approach
                Debug.Log($"Render scale set to: {renderScale}");
            }
        }

        /// <summary>
        /// Apply shadow settings.
        /// </summary>
        /// <param name="shadowsEnabled">Whether shadows are enabled</param>
        private void ApplyShadowSettings(bool shadowsEnabled)
        {
            QualitySettings.shadows = shadowsEnabled ? ShadowQuality.All : ShadowQuality.Disable;
        }

        /// <summary>
        /// Apply VFX settings.
        /// </summary>
        /// <param name="vfxEnabled">Whether VFX are enabled</param>
        private void ApplyVFXSettings(bool vfxEnabled)
        {
            if (vfxManager != null)
            {
                vfxManager.SetVFXEnabled(vfxEnabled);
            }
        }

        /// <summary>
        /// Apply post-processing settings.
        /// </summary>
        /// <param name="settings">Graphics settings</param>
        private void ApplyPostProcessingSettings(GraphicsSettings settings)
        {
            if (bloom != null)
            {
                bloom.active = settings.bloomEnabled;
            }

            // Apply anti-aliasing
            var urpAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                // Note: This would require URP asset modification
                Debug.Log($"Anti-aliasing: {(settings.antiAliasingEnabled ? "Enabled" : "Disabled")}");
            }
        }
        #endregion

        #region UI Event Handlers
        /// <summary>
        /// Handle quality dropdown change.
        /// </summary>
        /// <param name="index">Dropdown index</param>
        private void OnQualityDropdownChanged(int index)
        {
            pendingSettings.quality = (GraphicsQuality)index;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle resolution dropdown change.
        /// </summary>
        /// <param name="index">Dropdown index</param>
        private void OnResolutionDropdownChanged(int index)
        {
            if (index >= 0 && index < availableResolutions.Length)
            {
                Resolution resolution = availableResolutions[index];
                pendingSettings.resolutionWidth = resolution.width;
                pendingSettings.resolutionHeight = resolution.height;
                pendingSettings.refreshRate = resolution.refreshRate;
                MarkSettingsChanged();
            }
        }

        /// <summary>
        /// Handle window mode dropdown change.
        /// </summary>
        /// <param name="index">Dropdown index</param>
        private void OnWindowModeDropdownChanged(int index)
        {
            pendingSettings.windowMode = (WindowMode)index;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle render scale slider change.
        /// </summary>
        /// <param name="value">Slider value</param>
        private void OnRenderScaleChanged(float value)
        {
            pendingSettings.renderScale = value;
            if (renderScaleText != null)
            {
                renderScaleText.text = $"{value:F1}x";
            }
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle FOV slider change.
        /// </summary>
        /// <param name="value">Slider value</param>
        private void OnFOVChanged(float value)
        {
            pendingSettings.fieldOfView = value;
            if (fovText != null)
            {
                fovText.text = $"{value:F0}°";
            }
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle VSync toggle change.
        /// </summary>
        /// <param name="value">Toggle value</param>
        private void OnVSyncToggleChanged(bool value)
        {
            pendingSettings.vsyncEnabled = value;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle shadows toggle change.
        /// </summary>
        /// <param name="value">Toggle value</param>
        private void OnShadowsToggleChanged(bool value)
        {
            pendingSettings.shadowsEnabled = value;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle VFX toggle change.
        /// </summary>
        /// <param name="value">Toggle value</param>
        private void OnVFXToggleChanged(bool value)
        {
            pendingSettings.vfxEnabled = value;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle bloom toggle change.
        /// </summary>
        /// <param name="value">Toggle value</param>
        private void OnBloomToggleChanged(bool value)
        {
            pendingSettings.bloomEnabled = value;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Handle anti-aliasing toggle change.
        /// </summary>
        /// <param name="value">Toggle value</param>
        private void OnAntiAliasingToggleChanged(bool value)
        {
            pendingSettings.antiAliasingEnabled = value;
            MarkSettingsChanged();
        }

        /// <summary>
        /// Mark settings as changed and update apply button.
        /// </summary>
        private void MarkSettingsChanged()
        {
            SettingsChanged = true;
            if (applyButton != null)
            {
                applyButton.interactable = true;
            }
        }

        /// <summary>
        /// Apply pending settings.
        /// </summary>
        private void ApplyPendingSettings()
        {
            ApplySettings(pendingSettings);
        }

        /// <summary>
        /// Reset settings to defaults.
        /// </summary>
        private void ResetToDefaults()
        {
            var defaultSettings = GetDefaultSettings();
            pendingSettings = new GraphicsSettings(defaultSettings);
            UpdateUIFromSettings(pendingSettings);
            MarkSettingsChanged();
        }
        #endregion

        #region UI Updates
        /// <summary>
        /// Update UI elements from settings.
        /// </summary>
        /// <param name="settings">Settings to display</param>
        private void UpdateUIFromSettings(GraphicsSettings settings)
        {
            // Update quality dropdown
            if (qualityDropdown != null)
            {
                qualityDropdown.value = (int)settings.quality;
            }

            // Update resolution dropdown
            if (resolutionDropdown != null)
            {
                int resolutionIndex = FindResolutionIndex(settings.resolutionWidth, settings.resolutionHeight);
                if (resolutionIndex >= 0)
                {
                    resolutionDropdown.value = resolutionIndex;
                }
            }

            // Update window mode dropdown
            if (windowModeDropdown != null)
            {
                windowModeDropdown.value = (int)settings.windowMode;
            }

            // Update render scale slider
            if (renderScaleSlider != null)
            {
                renderScaleSlider.value = settings.renderScale;
            }
            if (renderScaleText != null)
            {
                renderScaleText.text = $"{settings.renderScale:F1}x";
            }

            // Update FOV slider
            if (fovSlider != null)
            {
                fovSlider.value = settings.fieldOfView;
            }
            if (fovText != null)
            {
                fovText.text = $"{settings.fieldOfView:F0}°";
            }

            // Update toggles
            if (vsyncToggle != null)
                vsyncToggle.isOn = settings.vsyncEnabled;

            if (shadowsToggle != null)
                shadowsToggle.isOn = settings.shadowsEnabled;

            if (vfxToggle != null)
                vfxToggle.isOn = settings.vfxEnabled;

            if (bloomToggle != null)
                bloomToggle.isOn = settings.bloomEnabled;

            if (antiAliasingToggle != null)
                antiAliasingToggle.isOn = settings.antiAliasingEnabled;

            // Update apply button
            if (applyButton != null)
            {
                applyButton.interactable = SettingsChanged;
            }
        }

        /// <summary>
        /// Find resolution index in available resolutions.
        /// </summary>
        /// <param name="width">Resolution width</param>
        /// <param name="height">Resolution height</param>
        /// <returns>Resolution index or -1 if not found</returns>
        private int FindResolutionIndex(int width, int height)
        {
            for (int i = 0; i < availableResolutions.Length; i++)
            {
                if (availableResolutions[i].width == width && availableResolutions[i].height == height)
                {
                    return i;
                }
            }
            return -1;
        }
        #endregion

        #region Save/Load
        /// <summary>
        /// Save graphics settings to PlayerPrefs.
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                string json = JsonUtility.ToJson(currentSettings, true);
                PlayerPrefs.SetString(settingsSaveKey, json);
                PlayerPrefs.Save();
                Debug.Log("Graphics settings saved");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save graphics settings: {e.Message}");
            }
        }

        /// <summary>
        /// Load graphics settings from PlayerPrefs.
        /// </summary>
        public void LoadSettings()
        {
            try
            {
                string json = PlayerPrefs.GetString(settingsSaveKey, "");
                if (!string.IsNullOrEmpty(json))
                {
                    currentSettings = JsonUtility.FromJson<GraphicsSettings>(json);
                    pendingSettings = new GraphicsSettings(currentSettings);
                    Debug.Log("Graphics settings loaded");
                }
                else
                {
                    currentSettings = GetDefaultSettings();
                    pendingSettings = new GraphicsSettings(currentSettings);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load graphics settings: {e.Message}");
                currentSettings = GetDefaultSettings();
                pendingSettings = new GraphicsSettings(currentSettings);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set graphics quality preset.
        /// </summary>
        /// <param name="quality">Quality level</param>
        public void SetQuality(GraphicsQuality quality)
        {
            pendingSettings.quality = quality;
            ApplyPendingSettings();
        }

        /// <summary>
        /// Toggle VFX on/off.
        /// </summary>
        /// <param name="enabled">VFX enabled state</param>
        public void SetVFXEnabled(bool enabled)
        {
            pendingSettings.vfxEnabled = enabled;
            ApplyPendingSettings();
        }

        /// <summary>
        /// Toggle shadows on/off.
        /// </summary>
        /// <param name="enabled">Shadows enabled state</param>
        public void SetShadowsEnabled(bool enabled)
        {
            pendingSettings.shadowsEnabled = enabled;
            ApplyPendingSettings();
        }

        /// <summary>
        /// Set render scale.
        /// </summary>
        /// <param name="scale">Render scale value</param>
        public void SetRenderScale(float scale)
        {
            pendingSettings.renderScale = Mathf.Clamp(scale, 0.5f, 2.0f);
            ApplyPendingSettings();
        }

        /// <summary>
        /// Set field of view.
        /// </summary>
        /// <param name="fov">Field of view value</param>
        public void SetFieldOfView(float fov)
        {
            pendingSettings.fieldOfView = Mathf.Clamp(fov, 60f, 120f);
            ApplyPendingSettings();
        }

        /// <summary>
        /// Get current graphics settings.
        /// </summary>
        /// <returns>Current graphics settings</returns>
        public GraphicsSettings GetCurrentSettings()
        {
            return new GraphicsSettings(currentSettings);
        }

        /// <summary>
        /// Check if settings have been changed.
        /// </summary>
        /// <returns>True if settings have been changed</returns>
        public bool HasUnsavedChanges()
        {
            return SettingsChanged;
        }

        /// <summary>
        /// Revert to last applied settings.
        /// </summary>
        public void RevertChanges()
        {
            pendingSettings = new GraphicsSettings(currentSettings);
            UpdateUIFromSettings(pendingSettings);
            SettingsChanged = false;
        }

        /// <summary>
        /// Auto-detect optimal graphics settings.
        /// </summary>
        public void AutoDetectSettings()
        {
            // Simple auto-detection based on system specs
            GraphicsQuality detectedQuality = GraphicsQuality.Medium;

            // Check system memory
            int systemMemoryMB = SystemInfo.systemMemorySize;
            int graphicsMemoryMB = SystemInfo.graphicsMemorySize;

            if (systemMemoryMB >= 16384 && graphicsMemoryMB >= 8192) // 16GB RAM, 8GB VRAM
            {
                detectedQuality = GraphicsQuality.High;
            }
            else if (systemMemoryMB >= 8192 && graphicsMemoryMB >= 4096) // 8GB RAM, 4GB VRAM
            {
                detectedQuality = GraphicsQuality.Medium;
            }
            else
            {
                detectedQuality = GraphicsQuality.Low;
            }

            // Apply detected settings
            var autoSettings = GetDefaultSettings();
            autoSettings.quality = detectedQuality;

            pendingSettings = autoSettings;
            UpdateUIFromSettings(pendingSettings);
            MarkSettingsChanged();

            Debug.Log($"Auto-detected graphics quality: {detectedQuality}");
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Graphics settings data structure.
    /// </summary>
    [System.Serializable]
    public class GraphicsSettings
    {
        public GraphicsQuality quality;
        public int resolutionWidth;
        public int resolutionHeight;
        public int refreshRate;
        public WindowMode windowMode;
        public float renderScale;
        public bool vsyncEnabled;
        public bool shadowsEnabled;
        public bool vfxEnabled;
        public bool bloomEnabled;
        public bool antiAliasingEnabled;
        public float fieldOfView;

        public GraphicsSettings() { }

        public GraphicsSettings(GraphicsSettings other)
        {
            quality = other.quality;
            resolutionWidth = other.resolutionWidth;
            resolutionHeight = other.resolutionHeight;
            refreshRate = other.refreshRate;
            windowMode = other.windowMode;
            renderScale = other.renderScale;
            vsyncEnabled = other.vsyncEnabled;
            shadowsEnabled = other.shadowsEnabled;
            vfxEnabled = other.vfxEnabled;
            bloomEnabled = other.bloomEnabled;
            antiAliasingEnabled = other.antiAliasingEnabled;
            fieldOfView = other.fieldOfView;
        }
    }

    /// <summary>
    /// Graphics quality enumeration.
    /// </summary>
    public enum GraphicsQuality
    {
        Low,
        Medium,
        High
    }

    /// <summary>
    /// Window mode enumeration.
    /// </summary>
    public enum WindowMode
    {
        Fullscreen,
        Windowed,
        Borderless
    }
    #endregion
}
