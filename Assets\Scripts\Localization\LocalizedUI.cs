using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace CinderOfDarkness.Localization
{
    /// <summary>
    /// Localized UI component for automatic UI layout adjustments.
    /// Handles RTL layout changes, image flipping, and UI element repositioning.
    /// </summary>
    public class LocalizedUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Layout Settings")]
        [SerializeField] private bool autoRegister = true;
        [SerializeField] private bool flipHorizontalLayout = true;
        [SerializeField] private bool flipImages = true;
        [SerializeField] private bool adjustAnchors = true;

        [Header("Components to Flip")]
        [SerializeField] private Image[] imagesToFlip;
        [SerializeField] private RectTransform[] elementsToFlip;
        [SerializeField] private HorizontalLayoutGroup[] layoutGroupsToFlip;

        [Header("Custom RTL Positions")]
        [SerializeField] private RTLPositionOverride[] positionOverrides;
        #endregion

        #region Private Fields
        private bool isRegistered = false;
        private bool isCurrentlyRTL = false;
        
        // Store original values for restoration
        private Dictionary<Image, bool> originalImageFlips = new Dictionary<Image, bool>();
        private Dictionary<RectTransform, Vector2> originalAnchors = new Dictionary<RectTransform, Vector2>();
        private Dictionary<RectTransform, Vector2> originalAnchoredPositions = new Dictionary<RectTransform, Vector2>();
        private Dictionary<HorizontalLayoutGroup, bool> originalLayoutReverse = new Dictionary<HorizontalLayoutGroup, bool>();
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize component and store original values.
        /// </summary>
        private void Awake()
        {
            StoreOriginalValues();
        }

        /// <summary>
        /// Register with localization manager.
        /// </summary>
        private void Start()
        {
            if (autoRegister && LocalizationManager.Instance != null)
            {
                RegisterWithManager();
            }
        }

        /// <summary>
        /// Unregister when destroyed.
        /// </summary>
        private void OnDestroy()
        {
            UnregisterFromManager();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Update UI layout based on RTL mode.
        /// </summary>
        /// <param name="isRTL">Whether to use RTL layout</param>
        public void UpdateLayout(bool isRTL)
        {
            if (isCurrentlyRTL == isRTL)
                return; // No change needed

            isCurrentlyRTL = isRTL;

            if (isRTL)
            {
                ApplyRTLLayout();
            }
            else
            {
                RestoreLTRLayout();
            }
        }

        /// <summary>
        /// Register with localization manager.
        /// </summary>
        public void RegisterWithManager()
        {
            if (!isRegistered && LocalizationManager.Instance != null)
            {
                LocalizationManager.Instance.RegisterLocalizedUI(this);
                isRegistered = true;
            }
        }

        /// <summary>
        /// Unregister from localization manager.
        /// </summary>
        public void UnregisterFromManager()
        {
            if (isRegistered && LocalizationManager.Instance != null)
            {
                LocalizationManager.Instance.UnregisterLocalizedUI(this);
                isRegistered = false;
            }
        }

        /// <summary>
        /// Force update layout with current language settings.
        /// </summary>
        public void ForceUpdateLayout()
        {
            if (LocalizationManager.Instance != null)
            {
                UpdateLayout(LocalizationManager.Instance.IsRTLLanguage);
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Store original values for restoration.
        /// </summary>
        private void StoreOriginalValues()
        {
            // Store original image flip states
            if (imagesToFlip != null)
            {
                foreach (Image img in imagesToFlip)
                {
                    if (img != null)
                    {
                        originalImageFlips[img] = img.GetComponent<RectTransform>().localScale.x < 0;
                    }
                }
            }

            // Store original anchor positions
            if (elementsToFlip != null)
            {
                foreach (RectTransform rt in elementsToFlip)
                {
                    if (rt != null)
                    {
                        originalAnchors[rt] = new Vector2(rt.anchorMin.x, rt.anchorMax.x);
                        originalAnchoredPositions[rt] = rt.anchoredPosition;
                    }
                }
            }

            // Store original layout group settings
            if (layoutGroupsToFlip != null)
            {
                foreach (HorizontalLayoutGroup layout in layoutGroupsToFlip)
                {
                    if (layout != null)
                    {
                        originalLayoutReverse[layout] = layout.reverseArrangement;
                    }
                }
            }
        }

        /// <summary>
        /// Apply RTL layout changes.
        /// </summary>
        private void ApplyRTLLayout()
        {
            // Flip images
            if (flipImages && imagesToFlip != null)
            {
                foreach (Image img in imagesToFlip)
                {
                    if (img != null)
                    {
                        FlipImageHorizontally(img, true);
                    }
                }
            }

            // Flip UI elements
            if (flipHorizontalLayout && elementsToFlip != null)
            {
                foreach (RectTransform rt in elementsToFlip)
                {
                    if (rt != null)
                    {
                        FlipElementHorizontally(rt);
                    }
                }
            }

            // Reverse layout groups
            if (layoutGroupsToFlip != null)
            {
                foreach (HorizontalLayoutGroup layout in layoutGroupsToFlip)
                {
                    if (layout != null)
                    {
                        layout.reverseArrangement = true;
                    }
                }
            }

            // Apply position overrides
            ApplyPositionOverrides(true);
        }

        /// <summary>
        /// Restore LTR layout.
        /// </summary>
        private void RestoreLTRLayout()
        {
            // Restore image flips
            if (imagesToFlip != null)
            {
                foreach (Image img in imagesToFlip)
                {
                    if (img != null && originalImageFlips.ContainsKey(img))
                    {
                        FlipImageHorizontally(img, originalImageFlips[img]);
                    }
                }
            }

            // Restore UI elements
            if (elementsToFlip != null)
            {
                foreach (RectTransform rt in elementsToFlip)
                {
                    if (rt != null)
                    {
                        RestoreElementPosition(rt);
                    }
                }
            }

            // Restore layout groups
            if (layoutGroupsToFlip != null)
            {
                foreach (HorizontalLayoutGroup layout in layoutGroupsToFlip)
                {
                    if (layout != null && originalLayoutReverse.ContainsKey(layout))
                    {
                        layout.reverseArrangement = originalLayoutReverse[layout];
                    }
                }
            }

            // Apply position overrides
            ApplyPositionOverrides(false);
        }

        /// <summary>
        /// Flip an image horizontally.
        /// </summary>
        /// <param name="image">Image to flip</param>
        /// <param name="flip">Whether to flip</param>
        private void FlipImageHorizontally(Image image, bool flip)
        {
            RectTransform rt = image.GetComponent<RectTransform>();
            Vector3 scale = rt.localScale;
            scale.x = flip ? -Mathf.Abs(scale.x) : Mathf.Abs(scale.x);
            rt.localScale = scale;
        }

        /// <summary>
        /// Flip a UI element horizontally by adjusting anchors.
        /// </summary>
        /// <param name="element">Element to flip</param>
        private void FlipElementHorizontally(RectTransform element)
        {
            if (!adjustAnchors) return;

            // Flip anchors horizontally
            Vector2 anchorMin = element.anchorMin;
            Vector2 anchorMax = element.anchorMax;

            float tempMinX = 1f - anchorMax.x;
            float tempMaxX = 1f - anchorMin.x;

            element.anchorMin = new Vector2(tempMinX, anchorMin.y);
            element.anchorMax = new Vector2(tempMaxX, anchorMax.y);

            // Flip anchored position
            Vector2 anchoredPos = element.anchoredPosition;
            anchoredPos.x = -anchoredPos.x;
            element.anchoredPosition = anchoredPos;
        }

        /// <summary>
        /// Restore element to original position.
        /// </summary>
        /// <param name="element">Element to restore</param>
        private void RestoreElementPosition(RectTransform element)
        {
            if (originalAnchors.ContainsKey(element))
            {
                Vector2 originalAnchor = originalAnchors[element];
                element.anchorMin = new Vector2(originalAnchor.x, element.anchorMin.y);
                element.anchorMax = new Vector2(originalAnchor.y, element.anchorMax.y);
            }

            if (originalAnchoredPositions.ContainsKey(element))
            {
                element.anchoredPosition = originalAnchoredPositions[element];
            }
        }

        /// <summary>
        /// Apply position overrides for RTL/LTR modes.
        /// </summary>
        /// <param name="isRTL">Whether applying RTL mode</param>
        private void ApplyPositionOverrides(bool isRTL)
        {
            if (positionOverrides == null) return;

            foreach (RTLPositionOverride posOverride in positionOverrides)
            {
                if (posOverride.targetTransform == null) continue;

                if (isRTL)
                {
                    // Apply RTL position
                    if (posOverride.useRTLPosition)
                    {
                        posOverride.targetTransform.anchoredPosition = posOverride.rtlPosition;
                    }
                    if (posOverride.useRTLAnchors)
                    {
                        posOverride.targetTransform.anchorMin = posOverride.rtlAnchorMin;
                        posOverride.targetTransform.anchorMax = posOverride.rtlAnchorMax;
                    }
                }
                else
                {
                    // Apply LTR position
                    if (posOverride.useLTRPosition)
                    {
                        posOverride.targetTransform.anchoredPosition = posOverride.ltrPosition;
                    }
                    if (posOverride.useLTRAnchors)
                    {
                        posOverride.targetTransform.anchorMin = posOverride.ltrAnchorMin;
                        posOverride.targetTransform.anchorMax = posOverride.ltrAnchorMax;
                    }
                }
            }
        }
        #endregion

        #region Editor Support
#if UNITY_EDITOR
        /// <summary>
        /// Context menu for manual layout update.
        /// </summary>
        [ContextMenu("Update Layout (RTL)")]
        private void UpdateLayoutRTLContextMenu()
        {
            UpdateLayout(true);
        }

        /// <summary>
        /// Context menu for manual layout update.
        /// </summary>
        [ContextMenu("Update Layout (LTR)")]
        private void UpdateLayoutLTRContextMenu()
        {
            UpdateLayout(false);
        }

        /// <summary>
        /// Context menu for registering with manager.
        /// </summary>
        [ContextMenu("Register with Localization Manager")]
        private void RegisterContextMenu()
        {
            RegisterWithManager();
        }
#endif
        #endregion

        #region Properties
        /// <summary>
        /// Get whether component is registered with manager.
        /// </summary>
        public bool IsRegistered
        {
            get { return isRegistered; }
        }

        /// <summary>
        /// Get whether currently using RTL layout.
        /// </summary>
        public bool IsCurrentlyRTL
        {
            get { return isCurrentlyRTL; }
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// RTL position override for specific UI elements.
    /// </summary>
    [System.Serializable]
    public class RTLPositionOverride
    {
        [Header("Target")]
        public RectTransform targetTransform;

        [Header("LTR Settings")]
        public bool useLTRPosition = false;
        public Vector2 ltrPosition;
        public bool useLTRAnchors = false;
        public Vector2 ltrAnchorMin;
        public Vector2 ltrAnchorMax;

        [Header("RTL Settings")]
        public bool useRTLPosition = false;
        public Vector2 rtlPosition;
        public bool useRTLAnchors = false;
        public Vector2 rtlAnchorMin;
        public Vector2 rtlAnchorMax;
    }
    #endregion
}
