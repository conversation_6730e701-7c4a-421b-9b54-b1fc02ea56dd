using UnityEngine;
using System.Collections.Generic;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Core AI State Machine for Cinder of Darkness enemies.
    /// Provides modular, scalable state management with performance optimization.
    /// </summary>
    public class AIStateMachine : MonoBehaviour
    {
        #region Serialized Fields
        [Header("AI Configuration")]
        [SerializeField] private AIStateType initialState = AIStateType.Idle;
        [SerializeField] private bool debugMode = false;
        [SerializeField] private float stateUpdateInterval = 0.1f; // Update every 100ms for performance
        
        [Header("AI Stats")]
        [SerializeField] private float detectionRange = 10f;
        [SerializeField] private float attackRange = 2f;
        [SerializeField] private float fleeHealthThreshold = 0.2f;
        [SerializeField] private float patrolRadius = 5f;
        [SerializeField] private float chaseSpeed = 3.5f;
        [SerializeField] private float patrolSpeed = 1.5f;
        #endregion

        #region Public Properties
        public AIStateType CurrentState => currentState?.StateType ?? AIStateType.Idle;
        public Transform Target { get; set; }
        public bool IsPlayerDetected { get; private set; }
        public float DistanceToTarget { get; private set; }
        #endregion

        #region Private Fields
        private Dictionary<AIStateType, IAIState> states = new Dictionary<AIStateType, IAIState>();
        private IAIState currentState;
        private AIBlackboard blackboard;
        private float lastStateUpdate = 0f;
        
        // Cached components for performance
        private UnityEngine.AI.NavMeshAgent navAgent;
        private EnemyHealth enemyHealth;
        private Animator animator;
        private AudioSource audioSource;
        #endregion

        #region Events
        public System.Action<AIStateType, AIStateType> OnStateChanged;
        public System.Action<Transform> OnTargetDetected;
        public System.Action OnTargetLost;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize AI state machine and components.
        /// </summary>
        private void Awake()
        {
            InitializeComponents();
            InitializeBlackboard();
            InitializeStates();
        }

        /// <summary>
        /// Start AI with initial state.
        /// </summary>
        private void Start()
        {
            ChangeState(initialState);
        }

        /// <summary>
        /// Update AI state machine at intervals for performance.
        /// </summary>
        private void Update()
        {
            float currentTime = Time.time;
            if (currentTime - lastStateUpdate >= stateUpdateInterval)
            {
                UpdateAI();
                lastStateUpdate = currentTime;
            }
        }

        /// <summary>
        /// Handle AI state visualization in editor.
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (debugMode)
            {
                DrawDebugGizmos();
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize and cache all required components.
        /// </summary>
        private void InitializeComponents()
        {
            navAgent = GetComponent<UnityEngine.AI.NavMeshAgent>();
            enemyHealth = GetComponent<EnemyHealth>();
            animator = GetComponent<Animator>();
            audioSource = GetComponent<AudioSource>();

            if (navAgent == null)
            {
                Debug.LogError($"NavMeshAgent component missing on {gameObject.name}");
            }

            if (enemyHealth == null)
            {
                Debug.LogError($"EnemyHealth component missing on {gameObject.name}");
            }
        }

        /// <summary>
        /// Initialize AI blackboard for shared data.
        /// </summary>
        private void InitializeBlackboard()
        {
            blackboard = new AIBlackboard
            {
                Agent = navAgent,
                Health = enemyHealth,
                Animator = animator,
                AudioSource = audioSource,
                Transform = transform,
                DetectionRange = detectionRange,
                AttackRange = attackRange,
                FleeHealthThreshold = fleeHealthThreshold,
                PatrolRadius = patrolRadius,
                ChaseSpeed = chaseSpeed,
                PatrolSpeed = patrolSpeed,
                OriginalPosition = transform.position
            };
        }

        /// <summary>
        /// Initialize all AI states.
        /// </summary>
        private void InitializeStates()
        {
            states[AIStateType.Idle] = new IdleState(blackboard);
            states[AIStateType.Patrol] = new PatrolState(blackboard);
            states[AIStateType.Chase] = new ChaseState(blackboard);
            states[AIStateType.Attack] = new AttackState(blackboard);
            states[AIStateType.Flee] = new FleeState(blackboard);
            states[AIStateType.Dead] = new DeadState(blackboard);
            states[AIStateType.Stunned] = new StunnedState(blackboard);
            states[AIStateType.Investigate] = new InvestigateState(blackboard);
        }
        #endregion

        #region State Management
        /// <summary>
        /// Change to a new AI state.
        /// </summary>
        /// <param name="newStateType">Type of state to change to</param>
        public void ChangeState(AIStateType newStateType)
        {
            if (!states.ContainsKey(newStateType))
            {
                Debug.LogError($"State {newStateType} not found in AI state machine");
                return;
            }

            AIStateType previousStateType = currentState?.StateType ?? AIStateType.Idle;
            
            // Exit current state
            currentState?.OnExit();
            
            // Change to new state
            currentState = states[newStateType];
            blackboard.CurrentStateType = newStateType;
            
            // Enter new state
            currentState.OnEnter();
            
            // Notify listeners
            OnStateChanged?.Invoke(previousStateType, newStateType);
            
            if (debugMode)
            {
                Debug.Log($"{gameObject.name}: State changed from {previousStateType} to {newStateType}");
            }
        }

        /// <summary>
        /// Force state change (bypasses normal state transition logic).
        /// </summary>
        /// <param name="newStateType">State to force change to</param>
        public void ForceState(AIStateType newStateType)
        {
            ChangeState(newStateType);
        }
        #endregion

        #region AI Update
        /// <summary>
        /// Main AI update loop.
        /// </summary>
        private void UpdateAI()
        {
            if (currentState == null) return;

            // Update blackboard with current information
            UpdateBlackboard();
            
            // Update current state
            AIStateType? nextState = currentState.OnUpdate();
            
            // Change state if requested
            if (nextState.HasValue && nextState.Value != currentState.StateType)
            {
                ChangeState(nextState.Value);
            }
        }

        /// <summary>
        /// Update shared blackboard data.
        /// </summary>
        private void UpdateBlackboard()
        {
            // Find player target
            if (Target == null)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    Target = player.transform;
                    blackboard.PlayerTransform = Target;
                }
            }

            // Update target information
            if (Target != null)
            {
                DistanceToTarget = Vector3.Distance(transform.position, Target.position);
                blackboard.DistanceToTarget = DistanceToTarget;
                blackboard.PlayerTransform = Target;
                
                // Update detection status
                bool wasDetected = IsPlayerDetected;
                IsPlayerDetected = DistanceToTarget <= detectionRange && CanSeeTarget(Target);
                blackboard.IsPlayerDetected = IsPlayerDetected;
                
                // Trigger events
                if (IsPlayerDetected && !wasDetected)
                {
                    OnTargetDetected?.Invoke(Target);
                }
                else if (!IsPlayerDetected && wasDetected)
                {
                    OnTargetLost?.Invoke();
                }
            }

            // Update health status
            if (enemyHealth != null)
            {
                blackboard.HealthPercentage = enemyHealth.GetHealthPercentage();
                blackboard.IsDead = enemyHealth.IsDead();
            }
        }

        /// <summary>
        /// Check if AI can see the target (line of sight).
        /// </summary>
        /// <param name="target">Target to check visibility for</param>
        /// <returns>True if target is visible</returns>
        private bool CanSeeTarget(Transform target)
        {
            if (target == null) return false;

            Vector3 directionToTarget = (target.position - transform.position).normalized;
            float distanceToTarget = Vector3.Distance(transform.position, target.position);

            // Raycast to check for obstacles
            if (Physics.Raycast(transform.position + Vector3.up * 0.5f, directionToTarget, out RaycastHit hit, distanceToTarget))
            {
                return hit.transform == target;
            }

            return true;
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set AI target manually.
        /// </summary>
        /// <param name="newTarget">New target transform</param>
        public void SetTarget(Transform newTarget)
        {
            Target = newTarget;
            blackboard.PlayerTransform = newTarget;
        }

        /// <summary>
        /// Trigger AI reaction to noise at position.
        /// </summary>
        /// <param name="noisePosition">Position where noise occurred</param>
        /// <param name="noiseLevel">Intensity of the noise (0-1)</param>
        public void OnNoiseDetected(Vector3 noisePosition, float noiseLevel = 1f)
        {
            if (currentState.StateType == AIStateType.Dead) return;

            float distanceToNoise = Vector3.Distance(transform.position, noisePosition);
            float effectiveRange = detectionRange * noiseLevel;

            if (distanceToNoise <= effectiveRange)
            {
                blackboard.LastKnownPlayerPosition = noisePosition;
                blackboard.InvestigationTarget = noisePosition;
                
                if (currentState.StateType == AIStateType.Idle || currentState.StateType == AIStateType.Patrol)
                {
                    ChangeState(AIStateType.Investigate);
                }
            }
        }

        /// <summary>
        /// Stun the AI for a specified duration.
        /// </summary>
        /// <param name="duration">Stun duration in seconds</param>
        public void Stun(float duration)
        {
            blackboard.StunDuration = duration;
            ChangeState(AIStateType.Stunned);
        }

        /// <summary>
        /// Get current AI state information.
        /// </summary>
        /// <returns>AI state info</returns>
        public AIStateInfo GetStateInfo()
        {
            return new AIStateInfo
            {
                CurrentState = CurrentState,
                IsPlayerDetected = IsPlayerDetected,
                DistanceToTarget = DistanceToTarget,
                HealthPercentage = blackboard.HealthPercentage,
                IsMoving = navAgent != null && navAgent.velocity.magnitude > 0.1f
            };
        }
        #endregion

        #region Debug
        /// <summary>
        /// Draw debug gizmos for AI visualization.
        /// </summary>
        private void DrawDebugGizmos()
        {
            // Detection range
            Gizmos.color = IsPlayerDetected ? Color.red : Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // Attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
            
            // Patrol radius
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(blackboard.OriginalPosition, patrolRadius);
            
            // Line to target
            if (Target != null && IsPlayerDetected)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, Target.position);
            }
            
            // Current state label
            if (currentState != null)
            {
                UnityEditor.Handles.Label(transform.position + Vector3.up * 2f, $"State: {currentState.StateType}");
            }
        }
        #endregion
    }

    #region Enums and Data Structures
    /// <summary>
    /// Available AI state types.
    /// </summary>
    public enum AIStateType
    {
        Idle,
        Patrol,
        Chase,
        Attack,
        Flee,
        Dead,
        Stunned,
        Investigate
    }

    /// <summary>
    /// AI state information for external queries.
    /// </summary>
    [System.Serializable]
    public struct AIStateInfo
    {
        public AIStateType CurrentState;
        public bool IsPlayerDetected;
        public float DistanceToTarget;
        public float HealthPercentage;
        public bool IsMoving;
    }
    #endregion
}
