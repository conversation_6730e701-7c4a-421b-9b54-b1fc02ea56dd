using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Sandbox Mode for Cinder of Darkness
/// Provides unlimited creative freedom for testing and experimentation
/// </summary>
public class SandboxMode : MonoBehaviour
{
    [Header("Sandbox Configuration")]
    public bool sandboxEnabled = false;
    public bool infiniteHealth = false;
    public bool infiniteStamina = false;
    public bool infiniteMana = false;
    public bool godMode = false;

    [Header("Spawn Settings")]
    public Transform spawnPoint;
    public float spawnRadius = 5f;
    public LayerMask spawnLayerMask = -1;

    [Header("UI References")]
    public GameObject sandboxUI;
    public GameObject spawnMenu;
    public GameObject settingsPanel;
    public TMPro.TMP_Dropdown categoryDropdown;
    public TMPro.TMP_Dropdown itemDropdown;
    public UnityEngine.UI.Slider quantitySlider;
    public TMPro.TextMeshProUGUI quantityText;

    [Header("Debug Console")]
    public GameObject debugConsole;
    public TMPro.TMP_InputField commandInput;
    public TMPro.TextMeshProUGUI consoleOutput;
    public int maxConsoleLines = 50;

    // Static instance
    private static SandboxMode instance;
    public static SandboxMode Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<SandboxMode>();
                if (instance == null)
                {
                    GameObject go = new GameObject("SandboxMode");
                    instance = go.AddComponent<SandboxMode>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Sandbox data
    private Dictionary<string, SpawnableItem> spawnableItems = new Dictionary<string, SpawnableItem>();
    private List<GameObject> spawnedObjects = new List<GameObject>();
    private SandboxLoadout currentLoadout;
    private List<string> consoleHistory = new List<string>();
    private int consoleHistoryIndex = 0;

    [System.Serializable]
    public class SpawnableItem
    {
        public string id;
        public string name;
        public string category;
        public GameObject prefab;
        public Sprite icon;
        public string description;
        public bool requiresUnlock;
        public string[] tags;
    }

    [System.Serializable]
    public class SandboxLoadout
    {
        public string name;
        public bool infiniteHealth;
        public bool infiniteStamina;
        public bool infiniteMana;
        public bool godMode;
        public Vector3 playerPosition;
        public List<SpawnedObjectData> spawnedObjects;
        public Dictionary<string, float> playerStats;
        public string currentRealm;
        public WeatherSettings weather;
        public HostilitySettings hostility;
    }

    [System.Serializable]
    public class SpawnedObjectData
    {
        public string itemId;
        public Vector3 position;
        public Vector3 rotation;
        public Vector3 scale;
        public Dictionary<string, object> properties;
    }

    [System.Serializable]
    public class WeatherSettings
    {
        public string weatherType;
        public float intensity;
        public Color fogColor;
        public float fogDensity;
    }

    [System.Serializable]
    public class HostilitySettings
    {
        public float globalHostility;
        public Dictionary<string, float> factionHostility;
        public bool alliesAttackPlayer;
        public bool enemiesIgnorePlayer;
    }

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSandbox();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        LoadSpawnableItems();
        SetupUI();
        SetupDebugConsole();
    }

    void InitializeSandbox()
    {
        Debug.Log("Sandbox Mode initialized");
    }

    void LoadSpawnableItems()
    {
        spawnableItems.Clear();

        // Load NPCs
        LoadNPCs();

        // Load Enemies
        LoadEnemies();

        // Load Items
        LoadItems();

        // Load Spells
        LoadSpells();

        // Load Environmental Objects
        LoadEnvironmentalObjects();

        Debug.Log($"Loaded {spawnableItems.Count} spawnable items");
    }

    void LoadNPCs()
    {
        // Load NPC prefabs from Resources
        GameObject[] npcPrefabs = Resources.LoadAll<GameObject>("NPCs");

        foreach (var prefab in npcPrefabs)
        {
            var item = new SpawnableItem
            {
                id = $"npc_{prefab.name.ToLower()}",
                name = prefab.name,
                category = "NPCs",
                prefab = prefab,
                description = $"Spawn {prefab.name} NPC",
                requiresUnlock = false,
                tags = new string[] { "npc", "character" }
            };

            spawnableItems[item.id] = item;
        }
    }

    void LoadEnemies()
    {
        GameObject[] enemyPrefabs = Resources.LoadAll<GameObject>("Enemies");

        foreach (var prefab in enemyPrefabs)
        {
            var item = new SpawnableItem
            {
                id = $"enemy_{prefab.name.ToLower()}",
                name = prefab.name,
                category = "Enemies",
                prefab = prefab,
                description = $"Spawn {prefab.name} enemy",
                requiresUnlock = false,
                tags = new string[] { "enemy", "combat" }
            };

            spawnableItems[item.id] = item;
        }
    }

    void LoadItems()
    {
        GameObject[] itemPrefabs = Resources.LoadAll<GameObject>("Items");

        foreach (var prefab in itemPrefabs)
        {
            var item = new SpawnableItem
            {
                id = $"item_{prefab.name.ToLower()}",
                name = prefab.name,
                category = "Items",
                prefab = prefab,
                description = $"Spawn {prefab.name} item",
                requiresUnlock = false,
                tags = new string[] { "item", "pickup" }
            };

            spawnableItems[item.id] = item;
        }
    }

    void LoadSpells()
    {
        // Create spell effect spawners
        string[] spellEffects = { "Fireball", "Ice Shard", "Lightning Bolt", "Earth Spike", "Healing Light" };

        foreach (string spell in spellEffects)
        {
            var item = new SpawnableItem
            {
                id = $"spell_{spell.ToLower().Replace(" ", "_")}",
                name = spell,
                category = "Spells",
                prefab = null, // Will be handled specially
                description = $"Cast {spell} spell",
                requiresUnlock = false,
                tags = new string[] { "spell", "magic", "effect" }
            };

            spawnableItems[item.id] = item;
        }
    }

    void LoadEnvironmentalObjects()
    {
        GameObject[] envPrefabs = Resources.LoadAll<GameObject>("Environment");

        foreach (var prefab in envPrefabs)
        {
            var item = new SpawnableItem
            {
                id = $"env_{prefab.name.ToLower()}",
                name = prefab.name,
                category = "Environment",
                prefab = prefab,
                description = $"Spawn {prefab.name} object",
                requiresUnlock = false,
                tags = new string[] { "environment", "prop" }
            };

            spawnableItems[item.id] = item;
        }
    }

    void SetupUI()
    {
        if (categoryDropdown != null)
        {
            var categories = spawnableItems.Values.Select(i => i.category).Distinct().ToList();
            categoryDropdown.ClearOptions();
            categoryDropdown.AddOptions(categories);
            categoryDropdown.onValueChanged.AddListener(OnCategoryChanged);
        }

        if (quantitySlider != null)
        {
            quantitySlider.onValueChanged.AddListener(OnQuantityChanged);
        }

        // Initially hide sandbox UI
        if (sandboxUI != null)
            sandboxUI.SetActive(false);
    }

    void SetupDebugConsole()
    {
        if (commandInput != null)
        {
            commandInput.onEndEdit.AddListener(OnCommandEntered);
        }

        if (debugConsole != null)
            debugConsole.SetActive(false);

        // Register console commands
        RegisterConsoleCommands();
    }

    void RegisterConsoleCommands()
    {
        // Commands will be handled in ProcessConsoleCommand method
        LogToConsole("Sandbox Debug Console initialized");
        LogToConsole("Type 'help' for available commands");
    }

    void Update()
    {
        // Toggle sandbox mode with F1
        if (Input.GetKeyDown(KeyCode.F1))
        {
            ToggleSandboxMode();
        }

        // Toggle debug console with F2
        if (Input.GetKeyDown(KeyCode.F2))
        {
            ToggleDebugConsole();
        }

        // Apply sandbox effects
        if (sandboxEnabled)
        {
            ApplySandboxEffects();
        }

        // Handle console navigation
        if (debugConsole != null && debugConsole.activeSelf)
        {
            HandleConsoleNavigation();
        }
    }

    void ApplySandboxEffects()
    {
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player == null) return;

        var playerStats = player.GetComponent<PlayerStats>();
        if (playerStats == null) return;

        // Apply infinite resources
        if (infiniteHealth)
        {
            playerStats.currentHealth = playerStats.maxHealth;
        }

        if (infiniteStamina)
        {
            // Set stamina to max if stamina system exists
            var staminaSystem = player.GetComponent<StaminaSystem>();
            if (staminaSystem != null)
            {
                staminaSystem.SetStamina(staminaSystem.maxStamina);
            }
        }

        if (infiniteMana)
        {
            playerStats.currentMana = playerStats.maxMana;
        }

        if (godMode)
        {
            // Make player invulnerable
            var healthSystem = player.GetComponent<HealthSystem>();
            if (healthSystem != null)
            {
                healthSystem.SetInvulnerable(true);
            }
        }
    }

    // Public API
    public static bool IsSandboxEnabled()
    {
        return Instance.sandboxEnabled;
    }

    public static void EnableSandboxMode(bool enabled)
    {
        Instance.ToggleSandboxModeInternal(enabled);
    }

    public static void SpawnItem(string itemId, Vector3 position, int quantity = 1)
    {
        Instance.SpawnItemInternal(itemId, position, quantity);
    }

    public static void TeleportPlayer(Vector3 position)
    {
        Instance.TeleportPlayerInternal(position);
    }

    public static void TeleportToRealm(string realmName)
    {
        Instance.TeleportToRealmInternal(realmName);
    }

    public static void SetWeather(string weatherType, float intensity)
    {
        Instance.SetWeatherInternal(weatherType, intensity);
    }

    public static void SetHostility(float globalHostility)
    {
        Instance.SetHostilityInternal(globalHostility);
    }

    public static void SaveLoadout(string name)
    {
        Instance.SaveLoadoutInternal(name);
    }

    public static void LoadLoadout(string name)
    {
        Instance.LoadLoadoutInternal(name);
    }

    public static void LoadArenaForTesting(ArenaData arena)
    {
        Instance.LoadArenaForTestingInternal(arena);
    }

    void ToggleSandboxMode()
    {
        ToggleSandboxModeInternal(!sandboxEnabled);
    }

    void ToggleSandboxModeInternal(bool enabled)
    {
        sandboxEnabled = enabled;

        if (sandboxUI != null)
            sandboxUI.SetActive(enabled);

        if (enabled)
        {
            LogToConsole("Sandbox Mode enabled");
        }
        else
        {
            LogToConsole("Sandbox Mode disabled");

            // Reset sandbox effects
            infiniteHealth = false;
            infiniteStamina = false;
            infiniteMana = false;
            godMode = false;

            // Remove god mode
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                var healthSystem = player.GetComponent<HealthSystem>();
                if (healthSystem != null)
                {
                    healthSystem.SetInvulnerable(false);
                }
            }
        }
    }

    void ToggleDebugConsole()
    {
        if (debugConsole != null)
        {
            bool newState = !debugConsole.activeSelf;
            debugConsole.SetActive(newState);

            if (newState && commandInput != null)
            {
                commandInput.Select();
                commandInput.ActivateInputField();
            }
        }
    }

    void SpawnItemInternal(string itemId, Vector3 position, int quantity)
    {
        if (!spawnableItems.ContainsKey(itemId))
        {
            LogToConsole($"Item not found: {itemId}");
            return;
        }

        var item = spawnableItems[itemId];

        for (int i = 0; i < quantity; i++)
        {
            Vector3 spawnPos = position + Random.insideUnitSphere * spawnRadius;

            if (item.category == "Spells")
            {
                CastSpell(item.name, spawnPos);
            }
            else if (item.prefab != null)
            {
                GameObject spawned = Instantiate(item.prefab, spawnPos, Quaternion.identity);
                spawnedObjects.Add(spawned);

                LogToConsole($"Spawned {item.name} at {spawnPos}");
            }
        }
    }

    void CastSpell(string spellName, Vector3 position)
    {
        var magicSystem = FindObjectOfType<ElementalMagicSystem>();
        if (magicSystem != null)
        {
            // Cast spell at position
            switch (spellName.ToLower())
            {
                case "fireball":
                    magicSystem.CastSpellAtPosition("fireball", position);
                    break;
                case "ice shard":
                    magicSystem.CastSpellAtPosition("ice_shard", position);
                    break;
                case "lightning bolt":
                    magicSystem.CastSpellAtPosition("lightning_bolt", position);
                    break;
                case "earth spike":
                    magicSystem.CastSpellAtPosition("earth_spike", position);
                    break;
                case "healing light":
                    magicSystem.CastSpellAtPosition("healing_light", position);
                    break;
            }

            LogToConsole($"Cast {spellName} at {position}");
        }
    }

    void TeleportPlayerInternal(Vector3 position)
    {
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            player.transform.position = position;
            LogToConsole($"Teleported player to {position}");
        }
    }

    void TeleportToRealmInternal(string realmName)
    {
        // Load the specified realm scene
        string sceneName = GetSceneNameFromRealm(realmName);
        if (!string.IsNullOrEmpty(sceneName))
        {
            SceneLoader.LoadSceneAsync(sceneName);
            LogToConsole($"Teleporting to realm: {realmName}");
        }
        else
        {
            LogToConsole($"Realm not found: {realmName}");
        }
    }

    string GetSceneNameFromRealm(string realmName)
    {
        switch (realmName.ToLower())
        {
            case "kingdom": return "KingdomOfSouthernBolt";
            case "forest": return "ForestOfShadows";
            case "ashlands": return "Ashlands";
            case "frozen": return "FrozenNorth";
            case "sky": return "CityOfTheSky";
            default: return null;
        }
    }

    void SetWeatherInternal(string weatherType, float intensity)
    {
        var weatherSystem = FindObjectOfType<WeatherSystem>();
        if (weatherSystem != null)
        {
            weatherSystem.SetWeather(weatherType, intensity);
            LogToConsole($"Weather set to {weatherType} (intensity: {intensity})");
        }
        else
        {
            LogToConsole("Weather system not found");
        }
    }

    void SetHostilityInternal(float globalHostility)
    {
        var hostilitySystem = FindObjectOfType<HostilitySystem>();
        if (hostilitySystem != null)
        {
            hostilitySystem.SetGlobalHostility(globalHostility);
            LogToConsole($"Global hostility set to {globalHostility}");
        }
        else
        {
            LogToConsole("Hostility system not found");
        }
    }

    void SaveLoadoutInternal(string name)
    {
        var loadout = new SandboxLoadout
        {
            name = name,
            infiniteHealth = infiniteHealth,
            infiniteStamina = infiniteStamina,
            infiniteMana = infiniteMana,
            godMode = godMode,
            spawnedObjects = new List<SpawnedObjectData>()
        };

        // Save player position
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            loadout.playerPosition = player.transform.position;
        }

        // Save spawned objects
        foreach (var obj in spawnedObjects)
        {
            if (obj != null)
            {
                var objData = new SpawnedObjectData
                {
                    position = obj.transform.position,
                    rotation = obj.transform.eulerAngles,
                    scale = obj.transform.localScale
                };
                loadout.spawnedObjects.Add(objData);
            }
        }

        // Save to file
        string json = JsonUtility.ToJson(loadout, true);
        string filePath = Path.Combine(Application.persistentDataPath, "Sandbox", $"{name}.loadout");

        Directory.CreateDirectory(Path.GetDirectoryName(filePath));
        System.IO.File.WriteAllText(filePath, json);

        LogToConsole($"Loadout saved: {name}");
    }

    void LoadLoadoutInternal(string name)
    {
        string filePath = Path.Combine(Application.persistentDataPath, "Sandbox", $"{name}.loadout");

        if (!System.IO.File.Exists(filePath))
        {
            LogToConsole($"Loadout not found: {name}");
            return;
        }

        try
        {
            string json = System.IO.File.ReadAllText(filePath);
            var loadout = JsonUtility.FromJson<SandboxLoadout>(json);

            // Apply loadout settings
            infiniteHealth = loadout.infiniteHealth;
            infiniteStamina = loadout.infiniteStamina;
            infiniteMana = loadout.infiniteMana;
            godMode = loadout.godMode;

            // Teleport player
            TeleportPlayerInternal(loadout.playerPosition);

            // Clear existing spawned objects
            ClearSpawnedObjects();

            // Spawn objects from loadout
            foreach (var objData in loadout.spawnedObjects)
            {
                // Spawn object at saved position
                // Implementation would depend on how objects are identified
            }

            LogToConsole($"Loadout loaded: {name}");
        }
        catch (System.Exception e)
        {
            LogToConsole($"Failed to load loadout: {e.Message}");
        }
    }

    void ClearSpawnedObjects()
    {
        foreach (var obj in spawnedObjects)
        {
            if (obj != null)
                Destroy(obj);
        }
        spawnedObjects.Clear();
        LogToConsole("Cleared all spawned objects");
    }

    // UI Event Handlers
    void OnCategoryChanged(int categoryIndex)
    {
        if (categoryDropdown == null || itemDropdown == null) return;

        string selectedCategory = categoryDropdown.options[categoryIndex].text;
        var itemsInCategory = spawnableItems.Values.Where(i => i.category == selectedCategory).ToList();

        itemDropdown.ClearOptions();
        itemDropdown.AddOptions(itemsInCategory.Select(i => i.name).ToList());
    }

    void OnQuantityChanged(float value)
    {
        if (quantityText != null)
        {
            quantityText.text = ((int)value).ToString();
        }
    }

    public void OnSpawnButtonClicked()
    {
        if (categoryDropdown == null || itemDropdown == null) return;

        string category = categoryDropdown.options[categoryDropdown.value].text;
        string itemName = itemDropdown.options[itemDropdown.value].text;
        int quantity = (int)quantitySlider.value;

        var item = spawnableItems.Values.FirstOrDefault(i => i.category == category && i.name == itemName);
        if (item != null)
        {
            Vector3 spawnPos = spawnPoint != null ? spawnPoint.position : Vector3.zero;
            SpawnItemInternal(item.id, spawnPos, quantity);
        }
    }

    public void OnClearAllClicked()
    {
        ClearSpawnedObjects();
    }

    public void OnToggleInfiniteHealth()
    {
        infiniteHealth = !infiniteHealth;
        LogToConsole($"Infinite Health: {infiniteHealth}");
    }

    public void OnToggleInfiniteStamina()
    {
        infiniteStamina = !infiniteStamina;
        LogToConsole($"Infinite Stamina: {infiniteStamina}");
    }

    public void OnToggleInfiniteMana()
    {
        infiniteMana = !infiniteMana;
        LogToConsole($"Infinite Mana: {infiniteMana}");
    }

    public void OnToggleGodMode()
    {
        godMode = !godMode;
        LogToConsole($"God Mode: {godMode}");
    }

    // Console System
    void OnCommandEntered(string command)
    {
        if (string.IsNullOrEmpty(command)) return;

        LogToConsole($"> {command}");
        ProcessConsoleCommand(command);

        // Add to history
        consoleHistory.Add(command);
        consoleHistoryIndex = consoleHistory.Count;

        // Clear input
        if (commandInput != null)
        {
            commandInput.text = "";
            commandInput.ActivateInputField();
        }
    }

    void HandleConsoleNavigation()
    {
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            if (consoleHistoryIndex > 0)
            {
                consoleHistoryIndex--;
                if (commandInput != null)
                    commandInput.text = consoleHistory[consoleHistoryIndex];
            }
        }
        else if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            if (consoleHistoryIndex < consoleHistory.Count - 1)
            {
                consoleHistoryIndex++;
                if (commandInput != null)
                    commandInput.text = consoleHistory[consoleHistoryIndex];
            }
            else
            {
                consoleHistoryIndex = consoleHistory.Count;
                if (commandInput != null)
                    commandInput.text = "";
            }
        }
    }

    void ProcessConsoleCommand(string command)
    {
        string[] parts = command.Split(' ');
        string cmd = parts[0].ToLower();

        switch (cmd)
        {
            case "help":
                ShowHelp();
                break;
            case "spawn":
                if (parts.Length >= 2)
                {
                    string itemId = parts[1];
                    int quantity = parts.Length >= 3 ? int.Parse(parts[2]) : 1;
                    Vector3 pos = spawnPoint != null ? spawnPoint.position : Vector3.zero;
                    SpawnItemInternal(itemId, pos, quantity);
                }
                break;
            case "tp":
            case "teleport":
                if (parts.Length >= 4)
                {
                    float x = float.Parse(parts[1]);
                    float y = float.Parse(parts[2]);
                    float z = float.Parse(parts[3]);
                    TeleportPlayerInternal(new Vector3(x, y, z));
                }
                else if (parts.Length >= 2)
                {
                    TeleportToRealmInternal(parts[1]);
                }
                break;
            case "weather":
                if (parts.Length >= 3)
                {
                    string weatherType = parts[1];
                    float intensity = float.Parse(parts[2]);
                    SetWeatherInternal(weatherType, intensity);
                }
                break;
            case "hostility":
                if (parts.Length >= 2)
                {
                    float hostility = float.Parse(parts[1]);
                    SetHostilityInternal(hostility);
                }
                break;
            case "god":
                OnToggleGodMode();
                break;
            case "health":
                OnToggleInfiniteHealth();
                break;
            case "stamina":
                OnToggleInfiniteStamina();
                break;
            case "mana":
                OnToggleInfiniteMana();
                break;
            case "clear":
                ClearSpawnedObjects();
                break;
            case "save":
                if (parts.Length >= 2)
                    SaveLoadoutInternal(parts[1]);
                break;
            case "load":
                if (parts.Length >= 2)
                    LoadLoadoutInternal(parts[1]);
                break;
            case "list":
                ListSpawnableItems();
                break;
            default:
                LogToConsole($"Unknown command: {cmd}. Type 'help' for available commands.");
                break;
        }
    }

    void ShowHelp()
    {
        LogToConsole("Available Commands:");
        LogToConsole("spawn <item_id> [quantity] - Spawn item");
        LogToConsole("tp <x> <y> <z> - Teleport to coordinates");
        LogToConsole("tp <realm> - Teleport to realm");
        LogToConsole("weather <type> <intensity> - Set weather");
        LogToConsole("hostility <level> - Set global hostility");
        LogToConsole("god - Toggle god mode");
        LogToConsole("health - Toggle infinite health");
        LogToConsole("stamina - Toggle infinite stamina");
        LogToConsole("mana - Toggle infinite mana");
        LogToConsole("clear - Clear all spawned objects");
        LogToConsole("save <name> - Save current loadout");
        LogToConsole("load <name> - Load saved loadout");
        LogToConsole("list - List spawnable items");
    }

    void ListSpawnableItems()
    {
        var categories = spawnableItems.Values.GroupBy(i => i.category);

        foreach (var category in categories)
        {
            LogToConsole($"{category.Key}:");
            foreach (var item in category.Take(10)) // Limit to first 10 items per category
            {
                LogToConsole($"  {item.id} - {item.name}");
            }
            if (category.Count() > 10)
            {
                LogToConsole($"  ... and {category.Count() - 10} more");
            }
        }
    }

    void LogToConsole(string message)
    {
        if (consoleOutput == null) return;

        string timestamp = System.DateTime.Now.ToString("HH:mm:ss");
        string logEntry = $"[{timestamp}] {message}";

        consoleOutput.text += logEntry + "\n";

        // Limit console lines
        string[] lines = consoleOutput.text.Split('\n');
        if (lines.Length > maxConsoleLines)
        {
            consoleOutput.text = string.Join("\n", lines.Skip(lines.Length - maxConsoleLines));
        }

        // Scroll to bottom
        var scrollRect = consoleOutput.GetComponentInParent<UnityEngine.UI.ScrollRect>();
        if (scrollRect != null)
        {
            Canvas.ForceUpdateCanvases();
            scrollRect.verticalNormalizedPosition = 0f;
        }
    }

    void LoadArenaForTestingInternal(ArenaData arena)
    {
        if (arena == null)
        {
            LogToConsole("Cannot load null arena for testing");
            return;
        }

        // Enable sandbox mode
        ToggleSandboxModeInternal(true);

        // Clear existing objects
        ClearSpawnedObjects();

        // Apply arena settings
        ApplyArenaSettings(arena);

        // Spawn arena elements
        SpawnArenaElements(arena);

        LogToConsole($"Loaded arena for testing: {arena.arenaName}");
    }

    void ApplyArenaSettings(ArenaData arena)
    {
        // Apply fog settings
        if (arena.fogSettings.enableFog)
        {
            RenderSettings.fog = true;
            RenderSettings.fogColor = arena.fogSettings.fogColor;
            RenderSettings.fogMode = arena.fogSettings.fogMode;
            RenderSettings.fogDensity = arena.fogSettings.fogDensity;
        }

        // Apply lighting
        RenderSettings.ambientLight = arena.lightingSettings.ambientColor;

        var directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.color = arena.lightingSettings.directionalLightColor;
            directionalLight.intensity = arena.lightingSettings.directionalLightIntensity;
            directionalLight.transform.eulerAngles = arena.lightingSettings.directionalLightRotation;
        }

        // Apply gameplay modifiers
        infiniteHealth = !arena.allowHealing;
        infiniteMana = arena.allowMagic;

        LogToConsole($"Applied arena settings for {arena.biomeType}");
    }

    void SpawnArenaElements(ArenaData arena)
    {
        // Teleport player to arena spawn point
        TeleportPlayerInternal(arena.playerSpawnPoint);

        // Spawn enemies at designated points
        foreach (var spawnPoint in arena.enemySpawnPoints)
        {
            SpawnItemInternal("enemy_shadow_warrior", spawnPoint, 1);
        }

        // Spawn destructible props
        foreach (var prop in arena.destructibleProps)
        {
            SpawnItemInternal($"env_{prop.propName.ToLower()}", prop.position, 1);
        }

        // Spawn static props
        foreach (var prop in arena.staticProps)
        {
            SpawnItemInternal($"env_{prop.propName.ToLower()}", prop.position, 1);
        }

        LogToConsole($"Spawned {arena.enemySpawnPoints.Count} enemies and {arena.destructibleProps.Count + arena.staticProps.Count} props");
    }

    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        if (sandboxEnabled)
        {
            LogToConsole($"Game Event: {eventName}");
        }
    }
}
