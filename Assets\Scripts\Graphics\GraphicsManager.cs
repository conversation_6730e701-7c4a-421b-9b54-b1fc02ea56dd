using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;

/// <summary>
/// Advanced graphics management system for Cinder of Darkness
/// Handles quality settings, post-processing, and visual effects
/// </summary>
public class GraphicsManager : MonoBehaviour
{
    [Header("Quality Settings")]
    public QualityPreset[] qualityPresets;
    public int currentQualityLevel = 2;

    [Header("Post-Processing")]
    public Volume globalVolume;
    public VolumeProfile[] postProcessingProfiles;
    public bool enablePostProcessing = true;

    [Header("Lighting")]
    public Light mainDirectionalLight;
    public bool enableVolumetricFog = true;
    public bool enableGlobalIllumination = true;

    [Header("Effects")]
    public bool enableParticleEffects = true;
    public bool enableScreenSpaceReflections = true;
    public bool enableDepthOfField = false;
    public bool enableChromaticAberration = false;

    [Header("Performance")]
    public bool enableDynamicBatching = true;
    public bool enableGPUInstancing = true;
    public bool enableOcclusionCulling = true;
    public int targetFrameRate = 60;

    // Static instance
    private static GraphicsManager instance;
    public static GraphicsManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<GraphicsManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("GraphicsManager");
                    instance = go.AddComponent<GraphicsManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Quality preset data
    [System.Serializable]
    public class QualityPreset
    {
        public string presetName;
        public int shadowQuality; // 0=Off, 1=Low, 2=Medium, 3=High
        public int textureQuality; // 0=Low, 1=Medium, 2=High, 3=Ultra
        public int antiAliasing; // 0=Off, 2=2x, 4=4x, 8=8x
        public bool enableVSync;
        public bool enablePostProcessing;
        public bool enableParticles;
        public bool enableReflections;
        public float renderScale = 1f;
        public int maxLODLevel = 0;
    }

    // Post-processing components
    private VolumeProfile currentProfile;
    private UnityEngine.Rendering.Universal.ColorAdjustments colorAdjustments;
    private UnityEngine.Rendering.Universal.Bloom bloom;
    private UnityEngine.Rendering.Universal.Vignette vignette;
    private UnityEngine.Rendering.Universal.DepthOfField depthOfField;
    private UnityEngine.Rendering.Universal.ChromaticAberration chromaticAberration;

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeGraphicsManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        SetupQualityPresets();
        SetupPostProcessing();
        ApplyGraphicsSettings();
        LoadGraphicsSettings();
    }

    void InitializeGraphicsManager()
    {
        // Set target frame rate
        Application.targetFrameRate = targetFrameRate;

        // Enable/disable VSync
        QualitySettings.vSyncCount = 1;

        Debug.Log("Graphics Manager initialized");
    }

    void SetupQualityPresets()
    {
        if (qualityPresets == null || qualityPresets.Length == 0)
        {
            // Create default quality presets
            qualityPresets = new QualityPreset[4];

            // Low Quality
            qualityPresets[0] = new QualityPreset
            {
                presetName = "Low",
                shadowQuality = 0,
                textureQuality = 0,
                antiAliasing = 0,
                enableVSync = false,
                enablePostProcessing = false,
                enableParticles = false,
                enableReflections = false,
                renderScale = 0.75f,
                maxLODLevel = 2
            };

            // Medium Quality
            qualityPresets[1] = new QualityPreset
            {
                presetName = "Medium",
                shadowQuality = 1,
                textureQuality = 1,
                antiAliasing = 2,
                enableVSync = true,
                enablePostProcessing = true,
                enableParticles = true,
                enableReflections = false,
                renderScale = 1f,
                maxLODLevel = 1
            };

            // High Quality
            qualityPresets[2] = new QualityPreset
            {
                presetName = "High",
                shadowQuality = 2,
                textureQuality = 2,
                antiAliasing = 4,
                enableVSync = true,
                enablePostProcessing = true,
                enableParticles = true,
                enableReflections = true,
                renderScale = 1f,
                maxLODLevel = 0
            };

            // Ultra Quality
            qualityPresets[3] = new QualityPreset
            {
                presetName = "Ultra",
                shadowQuality = 3,
                textureQuality = 3,
                antiAliasing = 8,
                enableVSync = true,
                enablePostProcessing = true,
                enableParticles = true,
                enableReflections = true,
                renderScale = 1.25f,
                maxLODLevel = 0
            };
        }
    }

    void SetupPostProcessing()
    {
        // Find or create global volume
        if (globalVolume == null)
        {
            GameObject volumeObj = new GameObject("Global Post-Process Volume");
            globalVolume = volumeObj.AddComponent<Volume>();
            globalVolume.isGlobal = true;
            globalVolume.priority = 1;
        }

        // Get or create volume profile
        if (globalVolume.profile == null)
        {
            globalVolume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
        }

        currentProfile = globalVolume.profile;

        // Setup post-processing components
        SetupPostProcessingComponents();
    }

    void SetupPostProcessingComponents()
    {
        // Color Adjustments
        if (!currentProfile.TryGet<UnityEngine.Rendering.Universal.ColorAdjustments>(out colorAdjustments))
        {
            colorAdjustments = currentProfile.Add<UnityEngine.Rendering.Universal.ColorAdjustments>(false);
        }

        // Bloom
        if (!currentProfile.TryGet<UnityEngine.Rendering.Universal.Bloom>(out bloom))
        {
            bloom = currentProfile.Add<UnityEngine.Rendering.Universal.Bloom>(false);
        }

        // Vignette
        if (!currentProfile.TryGet<UnityEngine.Rendering.Universal.Vignette>(out vignette))
        {
            vignette = currentProfile.Add<UnityEngine.Rendering.Universal.Vignette>(false);
        }

        // Depth of Field
        if (!currentProfile.TryGet<UnityEngine.Rendering.Universal.DepthOfField>(out depthOfField))
        {
            depthOfField = currentProfile.Add<UnityEngine.Rendering.Universal.DepthOfField>(false);
        }

        // Chromatic Aberration
        if (!currentProfile.TryGet<UnityEngine.Rendering.Universal.ChromaticAberration>(out chromaticAberration))
        {
            chromaticAberration = currentProfile.Add<UnityEngine.Rendering.Universal.ChromaticAberration>(false);
        }

        // Configure default values
        ConfigurePostProcessingDefaults();
    }

    void ConfigurePostProcessingDefaults()
    {
        // Color Adjustments - Slight desaturation for dark fantasy feel
        colorAdjustments.saturation.value = -0.1f;
        colorAdjustments.contrast.value = 0.1f;

        // Bloom - Subtle glow for magical effects
        bloom.intensity.value = 0.3f;
        bloom.threshold.value = 1.2f;
        bloom.scatter.value = 0.7f;

        // Vignette - Dark edges for atmosphere
        vignette.intensity.value = 0.2f;
        vignette.smoothness.value = 0.4f;
        vignette.color.value = Color.black;

        // Depth of Field - Disabled by default
        depthOfField.active = false;

        // Chromatic Aberration - Disabled by default
        chromaticAberration.active = false;
    }

    void ApplyGraphicsSettings()
    {
        if (currentQualityLevel >= 0 && currentQualityLevel < qualityPresets.Length)
        {
            QualityPreset preset = qualityPresets[currentQualityLevel];
            ApplyQualityPreset(preset);
        }
    }

    void ApplyQualityPreset(QualityPreset preset)
    {
        // Shadow Quality
        switch (preset.shadowQuality)
        {
            case 0:
                QualitySettings.shadows = ShadowQuality.Disable;
                break;
            case 1:
                QualitySettings.shadows = ShadowQuality.HardOnly;
                QualitySettings.shadowResolution = ShadowResolution.Low;
                break;
            case 2:
                QualitySettings.shadows = ShadowQuality.All;
                QualitySettings.shadowResolution = ShadowResolution.Medium;
                break;
            case 3:
                QualitySettings.shadows = ShadowQuality.All;
                QualitySettings.shadowResolution = ShadowResolution.High;
                break;
        }

        // Texture Quality (Updated for Unity 2022.3.62f1)
        QualitySettings.globalTextureMipmapLimit = 3 - preset.textureQuality;

        // Anti-Aliasing
        var cameraData = Camera.main?.GetUniversalAdditionalCameraData();
        if (cameraData != null)
        {
            switch (preset.antiAliasing)
            {
                case 0:
                    cameraData.antialiasing = AntialiasingMode.None;
                    break;
                case 2:
                    cameraData.antialiasing = AntialiasingMode.SubpixelMorphologicalAntiAliasing;
                    break;
                case 4:
                case 8:
                    cameraData.antialiasing = AntialiasingMode.SubpixelMorphologicalAntiAliasing;
                    break;
            }
        }

        // VSync
        QualitySettings.vSyncCount = preset.enableVSync ? 1 : 0;

        // Post-Processing
        enablePostProcessing = preset.enablePostProcessing;
        if (globalVolume != null)
        {
            globalVolume.enabled = enablePostProcessing;
        }

        // Particles
        enableParticleEffects = preset.enableParticles;

        // Reflections
        enableScreenSpaceReflections = preset.enableReflections;

        // Render Scale
        var urpAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
        if (urpAsset != null)
        {
            // Note: Render scale modification requires reflection in URP
            // This is a simplified approach
        }

        // LOD Bias
        QualitySettings.maximumLODLevel = preset.maxLODLevel;

        Debug.Log($"Applied graphics preset: {preset.presetName}");
    }

    // Public API
    public static void SetQualityLevel(int level)
    {
        Instance.SetQualityLevelInternal(level);
    }

    public static void SetResolution(int width, int height, bool fullscreen)
    {
        Screen.SetResolution(width, height, fullscreen);
        Debug.Log($"Resolution set to {width}x{height}, Fullscreen: {fullscreen}");
    }

    public static void SetVSync(bool enabled)
    {
        QualitySettings.vSyncCount = enabled ? 1 : 0;
        Instance.SaveGraphicsSettings();
    }

    public static void SetTargetFrameRate(int frameRate)
    {
        Application.targetFrameRate = frameRate;
        Instance.targetFrameRate = frameRate;
        Instance.SaveGraphicsSettings();
    }

    public static void SetPostProcessing(bool enabled)
    {
        Instance.SetPostProcessingInternal(enabled);
    }

    public static void EnableDepthOfField(bool enabled, float focusDistance = 5f)
    {
        Instance.EnableDepthOfFieldInternal(enabled, focusDistance);
    }

    public static void EnableChromaticAberration(bool enabled, float intensity = 0.5f)
    {
        Instance.EnableChromaticAberrationInternal(enabled, intensity);
    }

    public static void SetBloomIntensity(float intensity)
    {
        Instance.SetBloomIntensityInternal(intensity);
    }

    public static string[] GetQualityPresetNames()
    {
        string[] names = new string[Instance.qualityPresets.Length];
        for (int i = 0; i < Instance.qualityPresets.Length; i++)
        {
            names[i] = Instance.qualityPresets[i].presetName;
        }
        return names;
    }

    public static Resolution[] GetSupportedResolutions()
    {
        return Screen.resolutions;
    }

    void SetQualityLevelInternal(int level)
    {
        if (level >= 0 && level < qualityPresets.Length)
        {
            currentQualityLevel = level;
            ApplyQualityPreset(qualityPresets[level]);
            SaveGraphicsSettings();
        }
    }

    void SetPostProcessingInternal(bool enabled)
    {
        enablePostProcessing = enabled;
        if (globalVolume != null)
        {
            globalVolume.enabled = enabled;
        }
        SaveGraphicsSettings();
    }

    void EnableDepthOfFieldInternal(bool enabled, float focusDistance)
    {
        enableDepthOfField = enabled;
        if (depthOfField != null)
        {
            depthOfField.active = enabled;
            if (enabled)
            {
                depthOfField.focusDistance.value = focusDistance;
                depthOfField.aperture.value = 2f;
                depthOfField.focalLength.value = 50f;
            }
        }
    }

    void EnableChromaticAberrationInternal(bool enabled, float intensity)
    {
        enableChromaticAberration = enabled;
        if (chromaticAberration != null)
        {
            chromaticAberration.active = enabled;
            if (enabled)
            {
                chromaticAberration.intensity.value = intensity;
            }
        }
    }

    void SetBloomIntensityInternal(float intensity)
    {
        if (bloom != null)
        {
            bloom.intensity.value = intensity;
        }
    }

    // Combat-specific visual effects
    public void OnCombatStateChanged(bool inCombat)
    {
        if (inCombat)
        {
            // Enhance visual effects during combat
            EnableChromaticAberration(true, 0.3f);
            SetBloomIntensity(0.5f);

            // Increase contrast
            if (colorAdjustments != null)
            {
                colorAdjustments.contrast.value = 0.2f;
            }
        }
        else
        {
            // Return to normal visual settings
            EnableChromaticAberration(false);
            SetBloomIntensity(0.3f);

            // Reset contrast
            if (colorAdjustments != null)
            {
                colorAdjustments.contrast.value = 0.1f;
            }
        }
    }

    // Boss fight visual effects
    public void OnBossEncounter(bool isBossFight)
    {
        if (isBossFight)
        {
            // Dramatic visual effects for boss fights
            EnableDepthOfField(true, 8f);
            EnableChromaticAberration(true, 0.4f);
            SetBloomIntensity(0.7f);

            // Increase vignette
            if (vignette != null)
            {
                vignette.intensity.value = 0.4f;
            }
        }
        else
        {
            // Return to combat settings
            EnableDepthOfField(false);
            OnCombatStateChanged(false);

            // Reset vignette
            if (vignette != null)
            {
                vignette.intensity.value = 0.2f;
            }
        }
    }

    // Dream sequence visual effects
    public void OnDreamSequence(bool inDream)
    {
        if (inDream)
        {
            // Ethereal visual effects for dreams
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = -0.3f;
                colorAdjustments.hueShift.value = 30f;
            }

            EnableDepthOfField(true, 5f);
            SetBloomIntensity(0.8f);
        }
        else
        {
            // Return to normal
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = -0.1f;
                colorAdjustments.hueShift.value = 0f;
            }

            EnableDepthOfField(false);
            SetBloomIntensity(0.3f);
        }
    }

    // Save/Load settings
    void SaveGraphicsSettings()
    {
        PlayerPrefs.SetInt("GraphicsQuality", currentQualityLevel);
        PlayerPrefs.SetInt("EnablePostProcessing", enablePostProcessing ? 1 : 0);
        PlayerPrefs.SetInt("EnableDepthOfField", enableDepthOfField ? 1 : 0);
        PlayerPrefs.SetInt("EnableChromaticAberration", enableChromaticAberration ? 1 : 0);
        PlayerPrefs.SetInt("TargetFrameRate", targetFrameRate);
        PlayerPrefs.SetInt("VSync", QualitySettings.vSyncCount);
        PlayerPrefs.SetInt("ScreenWidth", Screen.width);
        PlayerPrefs.SetInt("ScreenHeight", Screen.height);
        PlayerPrefs.SetInt("Fullscreen", Screen.fullScreen ? 1 : 0);
        PlayerPrefs.Save();
    }

    void LoadGraphicsSettings()
    {
        currentQualityLevel = PlayerPrefs.GetInt("GraphicsQuality", 2);
        enablePostProcessing = PlayerPrefs.GetInt("EnablePostProcessing", 1) == 1;
        enableDepthOfField = PlayerPrefs.GetInt("EnableDepthOfField", 0) == 1;
        enableChromaticAberration = PlayerPrefs.GetInt("EnableChromaticAberration", 0) == 1;
        targetFrameRate = PlayerPrefs.GetInt("TargetFrameRate", 60);

        int vsync = PlayerPrefs.GetInt("VSync", 1);
        QualitySettings.vSyncCount = vsync;

        int screenWidth = PlayerPrefs.GetInt("ScreenWidth", Screen.currentResolution.width);
        int screenHeight = PlayerPrefs.GetInt("ScreenHeight", Screen.currentResolution.height);
        bool fullscreen = PlayerPrefs.GetInt("Fullscreen", 1) == 1;

        // Apply loaded settings
        ApplyGraphicsSettings();
        SetPostProcessing(enablePostProcessing);
        EnableDepthOfField(enableDepthOfField);
        EnableChromaticAberration(enableChromaticAberration);
        SetTargetFrameRate(targetFrameRate);

        if (screenWidth != Screen.width || screenHeight != Screen.height || fullscreen != Screen.fullScreen)
        {
            SetResolution(screenWidth, screenHeight, fullscreen);
        }

        Debug.Log("Graphics settings loaded");
    }

    // Auto-detect optimal settings
    public static void AutoDetectSettings()
    {
        Instance.AutoDetectSettingsInternal();
    }

    void AutoDetectSettingsInternal()
    {
        // Simple auto-detection based on system specs
        int recommendedQuality = 1; // Default to Medium

        // Check system memory
        if (SystemInfo.systemMemorySize >= 8192) // 8GB+
        {
            recommendedQuality = 2; // High
        }

        if (SystemInfo.systemMemorySize >= 16384) // 16GB+
        {
            recommendedQuality = 3; // Ultra
        }

        // Check graphics memory
        if (SystemInfo.graphicsMemorySize < 2048) // Less than 2GB VRAM
        {
            recommendedQuality = 0; // Low
        }
        else if (SystemInfo.graphicsMemorySize < 4096) // Less than 4GB VRAM
        {
            recommendedQuality = Mathf.Min(recommendedQuality, 1); // Max Medium
        }

        SetQualityLevel(recommendedQuality);
        Debug.Log($"Auto-detected graphics quality: {qualityPresets[recommendedQuality].presetName}");
    }

    // Debug methods
    public void LogSystemInfo()
    {
        Debug.Log($"Graphics Device: {SystemInfo.graphicsDeviceName}");
        Debug.Log($"Graphics Memory: {SystemInfo.graphicsMemorySize} MB");
        Debug.Log($"System Memory: {SystemInfo.systemMemorySize} MB");
        Debug.Log($"Processor: {SystemInfo.processorType}");
        Debug.Log($"Current Quality: {qualityPresets[currentQualityLevel].presetName}");
    }
}
