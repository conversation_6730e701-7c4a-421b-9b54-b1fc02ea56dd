using UnityEngine;
using TMPro;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace CinderOfDarkness.Localization
{
    /// <summary>
    /// Localization Manager for Cinder of Darkness.
    /// Supports full Arabic language with RTL layout and proper text formatting.
    /// </summary>
    public class LocalizationManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Localization Settings")]
        [SerializeField] private SystemLanguage defaultLanguage = SystemLanguage.English;
        [SerializeField] private bool autoDetectLanguage = true;
        [SerializeField] private string localizationSaveKey = "CinderLanguage";

        [Header("Language Data")]
        [SerializeField] private LanguageData[] supportedLanguages;
        [SerializeField] private TextAsset[] localizationFiles;

        [Header("RTL Settings")]
        [SerializeField] private TMP_FontAsset arabicFont;
        [SerializeField] private TMP_FontAsset englishFont;
        [SerializeField] private bool enableRTLSupport = true;

        [Header("UI References")]
        [SerializeField] private TMP_Dropdown languageDropdown;
        #endregion

        #region Public Properties
        public static LocalizationManager Instance { get; private set; }
        public SystemLanguage CurrentLanguage { get; private set; }
        public bool IsRTLLanguage => IsLanguageRTL(CurrentLanguage);
        public CultureInfo CurrentCulture { get; private set; }
        #endregion

        #region Private Fields
        private Dictionary<string, Dictionary<string, string>> localizationData = new Dictionary<string, Dictionary<string, string>>();
        private List<LocalizedText> registeredTexts = new List<LocalizedText>();
        private List<LocalizedUI> registeredUIs = new List<LocalizedUI>();
        
        // RTL text processing
        private ArabicTextProcessor arabicProcessor;
        
        // Language codes mapping
        private Dictionary<SystemLanguage, string> languageCodes = new Dictionary<SystemLanguage, string>
        {
            { SystemLanguage.English, "en" },
            { SystemLanguage.Arabic, "ar" },
            { SystemLanguage.French, "fr" },
            { SystemLanguage.German, "de" },
            { SystemLanguage.Spanish, "es" },
            { SystemLanguage.Italian, "it" },
            { SystemLanguage.Portuguese, "pt" },
            { SystemLanguage.Russian, "ru" },
            { SystemLanguage.Japanese, "ja" },
            { SystemLanguage.Korean, "ko" },
            { SystemLanguage.ChineseSimplified, "zh-CN" },
            { SystemLanguage.ChineseTraditional, "zh-TW" }
        };
        #endregion

        #region Events
        public System.Action<SystemLanguage> OnLanguageChanged;
        public System.Action<bool> OnRTLModeChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Localization Manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeLocalization();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and load language data.
        /// </summary>
        private void Start()
        {
            LoadLocalizationData();
            SetupLanguageDropdown();
            LoadSavedLanguage();
            ApplyLanguage(CurrentLanguage);
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize localization system.
        /// </summary>
        private void InitializeLocalization()
        {
            // Initialize Arabic text processor
            arabicProcessor = new ArabicTextProcessor();
            
            // Set default language
            CurrentLanguage = defaultLanguage;
            
            // Auto-detect system language if enabled
            if (autoDetectLanguage)
            {
                SystemLanguage systemLang = Application.systemLanguage;
                if (IsLanguageSupported(systemLang))
                {
                    CurrentLanguage = systemLang;
                }
            }
            
            // Set culture info
            UpdateCultureInfo();
        }

        /// <summary>
        /// Load localization data from files.
        /// </summary>
        private void LoadLocalizationData()
        {
            foreach (TextAsset locFile in localizationFiles)
            {
                if (locFile == null) continue;
                
                try
                {
                    string fileName = locFile.name;
                    string languageCode = ExtractLanguageCodeFromFileName(fileName);
                    
                    if (!string.IsNullOrEmpty(languageCode))
                    {
                        Dictionary<string, string> langData = ParseLocalizationFile(locFile.text);
                        localizationData[languageCode] = langData;
                        
                        Debug.Log($"Loaded localization data for language: {languageCode} ({langData.Count} entries)");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load localization file {locFile.name}: {e.Message}");
                }
            }
        }

        /// <summary>
        /// Setup language dropdown with supported languages.
        /// </summary>
        private void SetupLanguageDropdown()
        {
            if (languageDropdown == null) return;

            List<string> languageNames = new List<string>();
            
            foreach (LanguageData langData in supportedLanguages)
            {
                languageNames.Add(langData.displayName);
            }

            languageDropdown.ClearOptions();
            languageDropdown.AddOptions(languageNames);
            languageDropdown.onValueChanged.AddListener(OnLanguageDropdownChanged);
            
            // Set current selection
            UpdateLanguageDropdownSelection();
        }

        /// <summary>
        /// Update culture info based on current language.
        /// </summary>
        private void UpdateCultureInfo()
        {
            try
            {
                string cultureCode = GetLanguageCode(CurrentLanguage);
                CurrentCulture = new CultureInfo(cultureCode);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to set culture for language {CurrentLanguage}: {e.Message}");
                CurrentCulture = CultureInfo.InvariantCulture;
            }
        }
        #endregion

        #region Language Management
        /// <summary>
        /// Change the current language.
        /// </summary>
        /// <param name="language">Language to change to</param>
        public void ChangeLanguage(SystemLanguage language)
        {
            if (!IsLanguageSupported(language))
            {
                Debug.LogWarning($"Language not supported: {language}");
                return;
            }

            SystemLanguage previousLanguage = CurrentLanguage;
            CurrentLanguage = language;
            
            UpdateCultureInfo();
            ApplyLanguage(language);
            SaveLanguagePreference();
            
            // Trigger events
            OnLanguageChanged?.Invoke(language);
            
            if (IsLanguageRTL(language) != IsLanguageRTL(previousLanguage))
            {
                OnRTLModeChanged?.Invoke(IsLanguageRTL(language));
            }
            
            Debug.Log($"Language changed to: {language}");
        }

        /// <summary>
        /// Apply language settings to all registered components.
        /// </summary>
        /// <param name="language">Language to apply</param>
        private void ApplyLanguage(SystemLanguage language)
        {
            string languageCode = GetLanguageCode(language);
            bool isRTL = IsLanguageRTL(language);
            
            // Update all registered text components
            foreach (LocalizedText localizedText in registeredTexts.ToList())
            {
                if (localizedText != null)
                {
                    localizedText.UpdateText(languageCode, isRTL);
                }
                else
                {
                    registeredTexts.Remove(localizedText);
                }
            }
            
            // Update all registered UI components
            foreach (LocalizedUI localizedUI in registeredUIs.ToList())
            {
                if (localizedUI != null)
                {
                    localizedUI.UpdateLayout(isRTL);
                }
                else
                {
                    registeredUIs.Remove(localizedUI);
                }
            }
            
            // Update language dropdown selection
            UpdateLanguageDropdownSelection();
        }

        /// <summary>
        /// Get localized text for a key.
        /// </summary>
        /// <param name="key">Localization key</param>
        /// <param name="defaultText">Default text if key not found</param>
        /// <returns>Localized text</returns>
        public string GetLocalizedText(string key, string defaultText = "")
        {
            string languageCode = GetLanguageCode(CurrentLanguage);
            
            if (localizationData.ContainsKey(languageCode) && 
                localizationData[languageCode].ContainsKey(key))
            {
                string text = localizationData[languageCode][key];
                
                // Process Arabic text if needed
                if (IsRTLLanguage && arabicProcessor != null)
                {
                    text = arabicProcessor.ProcessArabicText(text);
                }
                
                return text;
            }
            
            // Fallback to English if available
            if (languageCode != "en" && localizationData.ContainsKey("en") && 
                localizationData["en"].ContainsKey(key))
            {
                return localizationData["en"][key];
            }
            
            // Return default text or key
            return !string.IsNullOrEmpty(defaultText) ? defaultText : key;
        }

        /// <summary>
        /// Get localized text with format parameters.
        /// </summary>
        /// <param name="key">Localization key</param>
        /// <param name="parameters">Format parameters</param>
        /// <returns>Formatted localized text</returns>
        public string GetLocalizedText(string key, params object[] parameters)
        {
            string text = GetLocalizedText(key);
            
            try
            {
                return string.Format(CurrentCulture, text, parameters);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to format localized text for key '{key}': {e.Message}");
                return text;
            }
        }

        /// <summary>
        /// Register a localized text component.
        /// </summary>
        /// <param name="localizedText">Localized text component</param>
        public void RegisterLocalizedText(LocalizedText localizedText)
        {
            if (localizedText != null && !registeredTexts.Contains(localizedText))
            {
                registeredTexts.Add(localizedText);
                
                // Update immediately if localization is already loaded
                if (localizationData.Count > 0)
                {
                    string languageCode = GetLanguageCode(CurrentLanguage);
                    localizedText.UpdateText(languageCode, IsRTLLanguage);
                }
            }
        }

        /// <summary>
        /// Unregister a localized text component.
        /// </summary>
        /// <param name="localizedText">Localized text component</param>
        public void UnregisterLocalizedText(LocalizedText localizedText)
        {
            registeredTexts.Remove(localizedText);
        }

        /// <summary>
        /// Register a localized UI component.
        /// </summary>
        /// <param name="localizedUI">Localized UI component</param>
        public void RegisterLocalizedUI(LocalizedUI localizedUI)
        {
            if (localizedUI != null && !registeredUIs.Contains(localizedUI))
            {
                registeredUIs.Add(localizedUI);
                
                // Update immediately
                localizedUI.UpdateLayout(IsRTLLanguage);
            }
        }

        /// <summary>
        /// Unregister a localized UI component.
        /// </summary>
        /// <param name="localizedUI">Localized UI component</param>
        public void UnregisterLocalizedUI(LocalizedUI localizedUI)
        {
            registeredUIs.Remove(localizedUI);
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Check if a language is supported.
        /// </summary>
        /// <param name="language">Language to check</param>
        /// <returns>True if language is supported</returns>
        public bool IsLanguageSupported(SystemLanguage language)
        {
            return supportedLanguages.Any(lang => lang.language == language);
        }

        /// <summary>
        /// Check if a language uses RTL text direction.
        /// </summary>
        /// <param name="language">Language to check</param>
        /// <returns>True if language uses RTL</returns>
        public bool IsLanguageRTL(SystemLanguage language)
        {
            return language == SystemLanguage.Arabic || 
                   language == SystemLanguage.Hebrew ||
                   language == SystemLanguage.Persian ||
                   language == SystemLanguage.Urdu;
        }

        /// <summary>
        /// Get language code for a system language.
        /// </summary>
        /// <param name="language">System language</param>
        /// <returns>Language code</returns>
        public string GetLanguageCode(SystemLanguage language)
        {
            return languageCodes.ContainsKey(language) ? languageCodes[language] : "en";
        }

        /// <summary>
        /// Get appropriate font for current language.
        /// </summary>
        /// <returns>Font asset for current language</returns>
        public TMP_FontAsset GetCurrentFont()
        {
            if (IsRTLLanguage && arabicFont != null)
            {
                return arabicFont;
            }
            
            return englishFont != null ? englishFont : Resources.GetBuiltinResource<TMP_FontAsset>("LegacyRuntime.fontsettings");
        }

        /// <summary>
        /// Extract language code from localization file name.
        /// </summary>
        /// <param name="fileName">File name</param>
        /// <returns>Language code</returns>
        private string ExtractLanguageCodeFromFileName(string fileName)
        {
            // Expected format: "localization_en.json" or "loc_ar.txt"
            string[] parts = fileName.Split('_', '.');
            if (parts.Length >= 2)
            {
                return parts[1];
            }
            
            return "";
        }

        /// <summary>
        /// Parse localization file content.
        /// </summary>
        /// <param name="content">File content</param>
        /// <returns>Localization dictionary</returns>
        private Dictionary<string, string> ParseLocalizationFile(string content)
        {
            Dictionary<string, string> data = new Dictionary<string, string>();
            
            try
            {
                // Try JSON format first
                LocalizationData locData = JsonUtility.FromJson<LocalizationData>(content);
                if (locData != null && locData.entries != null)
                {
                    foreach (LocalizationEntry entry in locData.entries)
                    {
                        data[entry.key] = entry.value;
                    }
                    return data;
                }
            }
            catch
            {
                // Fall back to simple key=value format
            }
            
            // Parse simple key=value format
            string[] lines = content.Split('\n');
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                    continue;
                
                int equalIndex = trimmedLine.IndexOf('=');
                if (equalIndex > 0)
                {
                    string key = trimmedLine.Substring(0, equalIndex).Trim();
                    string value = trimmedLine.Substring(equalIndex + 1).Trim();
                    
                    // Remove quotes if present
                    if (value.StartsWith("\"") && value.EndsWith("\""))
                    {
                        value = value.Substring(1, value.Length - 2);
                    }
                    
                    data[key] = value;
                }
            }
            
            return data;
        }

        /// <summary>
        /// Handle language dropdown selection change.
        /// </summary>
        /// <param name="index">Selected index</param>
        private void OnLanguageDropdownChanged(int index)
        {
            if (index >= 0 && index < supportedLanguages.Length)
            {
                ChangeLanguage(supportedLanguages[index].language);
            }
        }

        /// <summary>
        /// Update language dropdown selection to match current language.
        /// </summary>
        private void UpdateLanguageDropdownSelection()
        {
            if (languageDropdown == null) return;

            for (int i = 0; i < supportedLanguages.Length; i++)
            {
                if (supportedLanguages[i].language == CurrentLanguage)
                {
                    languageDropdown.value = i;
                    break;
                }
            }
        }

        /// <summary>
        /// Save language preference to PlayerPrefs.
        /// </summary>
        private void SaveLanguagePreference()
        {
            PlayerPrefs.SetString(localizationSaveKey, CurrentLanguage.ToString());
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load saved language preference from PlayerPrefs.
        /// </summary>
        private void LoadSavedLanguage()
        {
            string savedLanguage = PlayerPrefs.GetString(localizationSaveKey, "");
            if (!string.IsNullOrEmpty(savedLanguage))
            {
                if (System.Enum.TryParse(savedLanguage, out SystemLanguage language) && IsLanguageSupported(language))
                {
                    CurrentLanguage = language;
                    UpdateCultureInfo();
                }
            }
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Language data structure.
    /// </summary>
    [System.Serializable]
    public class LanguageData
    {
        public SystemLanguage language;
        public string displayName;
        public string nativeName;
        public bool isRTL;
        public TMP_FontAsset font;
    }

    /// <summary>
    /// Localization data structure for JSON parsing.
    /// </summary>
    [System.Serializable]
    public class LocalizationData
    {
        public LocalizationEntry[] entries;
    }

    /// <summary>
    /// Localization entry structure.
    /// </summary>
    [System.Serializable]
    public class LocalizationEntry
    {
        public string key;
        public string value;
    }
    #endregion
}
