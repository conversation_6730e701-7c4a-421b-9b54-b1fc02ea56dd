using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Main Menu UI for Cinder of Darkness.
    /// Handles game start, settings, and main menu navigation.
    /// </summary>
    public class MainMenuUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Main Menu")]
        [SerializeField] private GameObject mainMenuPanel;
        [SerializeField] private Button newGameButton;
        [SerializeField] private Button continueButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button creditsButton;
        [SerializeField] private But<PERSON> quitButton;

        [Header("Loading Screen")]
        [SerializeField] private GameObject loadingPanel;
        [SerializeField] private Slider loadingProgressBar;
        [SerializeField] private TextMeshProUGUI loadingText;
        [SerializeField] private Image loadingBackground;

        [Header("Credits")]
        [SerializeField] private GameObject creditsPanel;
        [SerializeField] private Button creditsBackButton;
        [SerializeField] private ScrollRect creditsScrollRect;
        [SerializeField] private TextMesh<PERSON>roUGUI creditsText;

        [Header("Save Slot Selection")]
        [SerializeField] private GameObject saveSlotPanel;
        [SerializeField] private Transform saveSlotContainer;
        [SerializeField] private GameObject saveSlotPrefab;
        [SerializeField] private Button saveSlotBackButton;

        [Header("Background")]
        [SerializeField] private Image backgroundImage;
        [SerializeField] private ParticleSystem backgroundParticles;
        [SerializeField] private AnimationCurve fadeInCurve;

        [Header("Audio")]
        [SerializeField] private AudioClip menuMusic;
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip gameStartSound;
        [SerializeField] private AudioSource musicSource;
        [SerializeField] private AudioSource sfxSource;

        [Header("Version Info")]
        [SerializeField] private TextMeshProUGUI versionText;
        [SerializeField] private string gameVersion = "1.0.0";
        #endregion

        #region Private Fields
        private SettingsMenuUI settingsMenu;
        private bool isLoading = false;
        private SaveSystem saveSystem;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            SetupAudioSources();
            SetupSaveSystem();
        }

        private void Start()
        {
            SetupUI();
            SetupEventListeners();
            StartCoroutine(FadeInMenu());
            PlayMenuMusic();
        }

        private void Update()
        {
            HandleInput();
        }
        #endregion

        #region Initialization
        private void SetupAudioSources()
        {
            if (musicSource == null)
            {
                musicSource = gameObject.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.volume = 0.7f;
            }

            if (sfxSource == null)
            {
                sfxSource = gameObject.AddComponent<AudioSource>();
                sfxSource.playOnAwake = false;
            }
        }

        private void SetupSaveSystem()
        {
            saveSystem = FindObjectOfType<SaveSystem>();
            if (saveSystem == null)
            {
                var saveSystemObj = new GameObject("SaveSystem");
                saveSystem = saveSystemObj.AddComponent<SaveSystem>();
                DontDestroyOnLoad(saveSystemObj);
            }
        }

        private void SetupUI()
        {
            // Show main menu, hide others
            if (mainMenuPanel != null)
                mainMenuPanel.SetActive(true);

            if (loadingPanel != null)
                loadingPanel.SetActive(false);

            if (creditsPanel != null)
                creditsPanel.SetActive(false);

            if (saveSlotPanel != null)
                saveSlotPanel.SetActive(false);

            // Setup version text
            if (versionText != null)
                versionText.text = $"v{gameVersion}";

            // Check for existing saves
            UpdateContinueButton();

            // Get settings menu reference
            settingsMenu = FindObjectOfType<SettingsMenuUI>();
        }

        private void SetupEventListeners()
        {
            if (newGameButton != null)
                newGameButton.onClick.AddListener(OnNewGameClicked);

            if (continueButton != null)
                continueButton.onClick.AddListener(OnContinueClicked);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);

            if (creditsButton != null)
                creditsButton.onClick.AddListener(OnCreditsClicked);

            if (quitButton != null)
                quitButton.onClick.AddListener(OnQuitClicked);

            if (creditsBackButton != null)
                creditsBackButton.onClick.AddListener(OnCreditsBackClicked);

            if (saveSlotBackButton != null)
                saveSlotBackButton.onClick.AddListener(OnSaveSlotBackClicked);
        }

        private IEnumerator FadeInMenu()
        {
            if (backgroundImage != null)
            {
                Color startColor = backgroundImage.color;
                startColor.a = 0f;
                backgroundImage.color = startColor;

                float elapsed = 0f;
                float duration = 2f;

                while (elapsed < duration)
                {
                    elapsed += Time.deltaTime;
                    float alpha = fadeInCurve.Evaluate(elapsed / duration);
                    
                    Color color = backgroundImage.color;
                    color.a = alpha;
                    backgroundImage.color = color;

                    yield return null;
                }

                Color finalColor = backgroundImage.color;
                finalColor.a = 1f;
                backgroundImage.color = finalColor;
            }

            // Start background particles
            if (backgroundParticles != null)
                backgroundParticles.Play();
        }
        #endregion

        #region Menu Navigation
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (creditsPanel != null && creditsPanel.activeInHierarchy)
                {
                    OnCreditsBackClicked();
                }
                else if (saveSlotPanel != null && saveSlotPanel.activeInHierarchy)
                {
                    OnSaveSlotBackClicked();
                }
            }

            // Quick start with Enter
            if (Input.GetKeyDown(KeyCode.Return) && !isLoading)
            {
                if (continueButton != null && continueButton.interactable)
                    OnContinueClicked();
                else
                    OnNewGameClicked();
            }
        }

        private void OnNewGameClicked()
        {
            PlayButtonSound();
            ShowSaveSlotSelection(true);
        }

        private void OnContinueClicked()
        {
            PlayButtonSound();
            ShowSaveSlotSelection(false);
        }

        private void OnSettingsClicked()
        {
            PlayButtonSound();
            
            if (settingsMenu != null)
            {
                settingsMenu.OpenSettings();
            }
        }

        private void OnCreditsClicked()
        {
            PlayButtonSound();
            ShowCredits();
        }

        private void OnQuitClicked()
        {
            PlayButtonSound();
            StartCoroutine(QuitGame());
        }

        private void OnCreditsBackClicked()
        {
            PlayButtonSound();
            HideCredits();
        }

        private void OnSaveSlotBackClicked()
        {
            PlayButtonSound();
            HideSaveSlotSelection();
        }
        #endregion

        #region Save Slot Management
        private void ShowSaveSlotSelection(bool isNewGame)
        {
            if (saveSlotPanel != null)
            {
                saveSlotPanel.SetActive(true);
                mainMenuPanel.SetActive(false);
                
                CreateSaveSlots(isNewGame);
            }
        }

        private void HideSaveSlotSelection()
        {
            if (saveSlotPanel != null)
            {
                saveSlotPanel.SetActive(false);
                mainMenuPanel.SetActive(true);
                
                ClearSaveSlots();
            }
        }

        private void CreateSaveSlots(bool isNewGame)
        {
            if (saveSlotPrefab == null || saveSlotContainer == null) return;

            ClearSaveSlots();

            for (int i = 0; i < 3; i++) // 3 save slots
            {
                GameObject slot = Instantiate(saveSlotPrefab, saveSlotContainer);
                var saveSlot = slot.GetComponent<SaveSlotUI>();
                
                if (saveSlot != null)
                {
                    saveSlot.Setup(i, isNewGame, this);
                }
            }
        }

        private void ClearSaveSlots()
        {
            foreach (Transform child in saveSlotContainer)
            {
                Destroy(child.gameObject);
            }
        }

        public void OnSaveSlotSelected(int slotIndex, bool isNewGame)
        {
            PlayButtonSound();
            
            if (isNewGame)
            {
                StartNewGame(slotIndex);
            }
            else
            {
                LoadGame(slotIndex);
            }
        }

        private void UpdateContinueButton()
        {
            if (continueButton == null || saveSystem == null) return;

            bool hasSaves = saveSystem.HasAnySaveFiles();
            continueButton.interactable = hasSaves;
            
            if (!hasSaves && continueButton.GetComponent<Image>() != null)
            {
                var image = continueButton.GetComponent<Image>();
                Color color = image.color;
                color.a = 0.5f;
                image.color = color;
            }
        }
        #endregion

        #region Game Loading
        private void StartNewGame(int slotIndex)
        {
            StartCoroutine(LoadGameScene("GameScene", slotIndex, true));
        }

        private void LoadGame(int slotIndex)
        {
            StartCoroutine(LoadGameScene("GameScene", slotIndex, false));
        }

        private IEnumerator LoadGameScene(string sceneName, int saveSlot, bool isNewGame)
        {
            isLoading = true;
            
            // Show loading screen
            if (loadingPanel != null)
            {
                loadingPanel.SetActive(true);
                mainMenuPanel.SetActive(false);
                saveSlotPanel.SetActive(false);
            }

            // Play game start sound
            PlaySound(gameStartSound);

            // Fade out menu music
            yield return StartCoroutine(FadeOutMusic());

            // Start loading
            AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            asyncLoad.allowSceneActivation = false;

            // Update loading progress
            while (!asyncLoad.isDone)
            {
                float progress = Mathf.Clamp01(asyncLoad.progress / 0.9f);
                
                if (loadingProgressBar != null)
                    loadingProgressBar.value = progress;

                if (loadingText != null)
                    loadingText.text = $"Loading... {progress * 100:F0}%";

                // When loading is almost complete
                if (asyncLoad.progress >= 0.9f)
                {
                    if (loadingText != null)
                        loadingText.text = "Press any key to continue...";

                    if (Input.anyKeyDown)
                    {
                        asyncLoad.allowSceneActivation = true;
                    }
                }

                yield return null;
            }

            // Set save slot for the game
            PlayerPrefs.SetInt("CurrentSaveSlot", saveSlot);
            PlayerPrefs.SetInt("IsNewGame", isNewGame ? 1 : 0);
            PlayerPrefs.Save();
        }

        private IEnumerator FadeOutMusic()
        {
            if (musicSource == null) yield break;

            float startVolume = musicSource.volume;
            float elapsed = 0f;
            float duration = 1f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                musicSource.volume = Mathf.Lerp(startVolume, 0f, elapsed / duration);
                yield return null;
            }

            musicSource.Stop();
        }
        #endregion

        #region Credits
        private void ShowCredits()
        {
            if (creditsPanel != null)
            {
                creditsPanel.SetActive(true);
                mainMenuPanel.SetActive(false);
                
                if (creditsText != null)
                {
                    creditsText.text = GetCreditsText();
                }

                // Auto-scroll credits
                if (creditsScrollRect != null)
                {
                    StartCoroutine(AutoScrollCredits());
                }
            }
        }

        private void HideCredits()
        {
            if (creditsPanel != null)
            {
                creditsPanel.SetActive(false);
                mainMenuPanel.SetActive(true);
            }
        }

        private string GetCreditsText()
        {
            return @"CINDER OF DARKNESS

DEVELOPMENT TEAM
Lead Developer: Augment Agent
Game Design: AI-Driven Development
Programming: Advanced Unity Systems
Art Direction: Soulslike Aesthetic

SPECIAL THANKS
Unity Technologies
The Soulslike Community
Beta Testers
Open Source Contributors

SYSTEMS IMPLEMENTED
• Dynamic Narrative System
• Reactive AI System
• Magic Evolution System
• Economy & Trading System
• Advanced Dialogue System
• Stealth & Assassination System
• Interactive World Map System
• Flashback/Memory System
• Dynamic Time System
• Comprehensive Modding Support

MUSIC & AUDIO
Dark Neoclassical Compositions
Atmospheric Sound Design
Spatial Audio Implementation

LOCALIZATION
English (Primary)
Arabic (RTL Support)
Expandable Framework

TECHNOLOGY
Unity 2022.3 LTS
Universal Render Pipeline
New Input System
Addressable Assets

Thank you for playing
CINDER OF DARKNESS

© 2024 - All Rights Reserved";
        }

        private IEnumerator AutoScrollCredits()
        {
            if (creditsScrollRect == null) yield break;

            float scrollSpeed = 0.1f;
            float currentPosition = 1f;

            while (creditsPanel.activeInHierarchy && currentPosition > 0f)
            {
                currentPosition -= scrollSpeed * Time.deltaTime;
                creditsScrollRect.verticalNormalizedPosition = currentPosition;
                yield return null;
            }
        }
        #endregion

        #region Audio
        private void PlayMenuMusic()
        {
            if (menuMusic != null && musicSource != null)
            {
                musicSource.clip = menuMusic;
                musicSource.Play();
            }
        }

        private void PlayButtonSound()
        {
            PlaySound(buttonClickSound);
        }

        private void PlaySound(AudioClip clip)
        {
            if (clip != null && sfxSource != null)
            {
                sfxSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Quit Game
        private IEnumerator QuitGame()
        {
            // Fade out music
            yield return StartCoroutine(FadeOutMusic());

            // Quit application
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
        #endregion
    }

    #region Supporting Classes
    public class SaveSlotUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI slotNumberText;
        [SerializeField] private TextMeshProUGUI saveInfoText;
        [SerializeField] private TextMeshProUGUI timestampText;
        [SerializeField] private Button selectButton;
        [SerializeField] private Image screenshotImage;

        private int slotIndex;
        private bool isNewGame;
        private MainMenuUI mainMenu;

        public void Setup(int index, bool newGame, MainMenuUI menu)
        {
            slotIndex = index;
            isNewGame = newGame;
            mainMenu = menu;

            if (slotNumberText != null)
                slotNumberText.text = $"Slot {index + 1}";

            if (selectButton != null)
                selectButton.onClick.AddListener(() => mainMenu.OnSaveSlotSelected(slotIndex, isNewGame));

            UpdateSaveInfo();
        }

        private void UpdateSaveInfo()
        {
            // Check if save file exists
            bool saveExists = PlayerPrefs.HasKey($"SaveSlot_{slotIndex}");

            if (saveInfoText != null)
            {
                if (isNewGame)
                {
                    saveInfoText.text = saveExists ? "Overwrite Save" : "New Game";
                }
                else
                {
                    saveInfoText.text = saveExists ? "Continue Game" : "Empty Slot";
                }
            }

            if (timestampText != null)
            {
                if (saveExists)
                {
                    string timestamp = PlayerPrefs.GetString($"SaveSlot_{slotIndex}_Timestamp", "Unknown");
                    timestampText.text = timestamp;
                }
                else
                {
                    timestampText.text = "";
                }
            }

            // Disable button if trying to continue from empty slot
            if (selectButton != null)
            {
                selectButton.interactable = isNewGame || saveExists;
            }
        }
    }
    #endregion
}
