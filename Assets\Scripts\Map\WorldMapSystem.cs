using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Map
{
    /// <summary>
    /// World Map System for Cinder of Darkness.
    /// Manages fog of war, custom markers, and interactive map features.
    /// </summary>
    public class WorldMapSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Map Settings")]
        [SerializeField] private bool enableFogOfWar = true;
        [SerializeField] private float explorationRadius = 10f;
        [SerializeField] private int mapResolution = 512;
        [SerializeField] private LayerMask explorationLayers = -1;

        [Header("UI References")]
        [SerializeField] private GameObject worldMapUI;
        [SerializeField] private RawImage mapImage;
        [SerializeField] private Transform mapContainer;
        [SerializeField] private GameObject markerPrefab;
        [SerializeField] private Transform markerContainer;

        [Header("Mini-map")]
        [SerializeField] private GameObject miniMapUI;
        [SerializeField] private RawImage miniMapImage;
        [SerializeField] private Transform miniMapContainer;
        [SerializeField] private Transform playerIcon;
        [SerializeField] private float miniMapZoom = 1f;
        [SerializeField] private Vector2 miniMapSize = new Vector2(200f, 200f);

        [Header("Fog of War")]
        [SerializeField] private Texture2D fogTexture;
        [SerializeField] private Color exploredColor = Color.white;
        [SerializeField] private Color unexploredColor = Color.black;
        [SerializeField] private float fogUpdateInterval = 0.5f;

        [Header("Map Bounds")]
        [SerializeField] private Vector2 worldBounds = new Vector2(1000f, 1000f);
        [SerializeField] private Vector2 worldOffset = Vector2.zero;

        [Header("Markers")]
        [SerializeField] private MapMarkerType[] markerTypes;
        [SerializeField] private int maxCustomMarkers = 50;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        #endregion

        #region Public Properties
        public static WorldMapSystem Instance { get; private set; }
        public bool IsWorldMapOpen { get; private set; }
        public bool IsMiniMapVisible { get; private set; } = true;
        public Vector2 PlayerMapPosition { get; private set; }
        #endregion

        #region Private Fields
        private Transform playerTransform;
        private Camera playerCamera;

        // Fog of War system
        private bool[,] exploredGrid;
        private Texture2D fogOfWarTexture;
        private float lastFogUpdate;

        // Marker system
        private List<MapMarker> activeMarkers = new List<MapMarker>();
        private List<MapMarker> customMarkers = new List<MapMarker>();
        private Dictionary<string, GameObject> markerObjects = new Dictionary<string, GameObject>();

        // Map navigation
        private Vector2 mapPanOffset = Vector2.zero;
        private float mapZoomLevel = 1f;
        private bool isDragging = false;
        private Vector2 lastMousePosition;

        // Mini-map tracking
        private List<Transform> trackedObjects = new List<Transform>();
        private Dictionary<Transform, GameObject> miniMapIcons = new Dictionary<Transform, GameObject>();
        #endregion

        #region Events
        public System.Action<MapMarker> OnMarkerAdded;
        public System.Action<MapMarker> OnMarkerRemoved;
        public System.Action<Vector2> OnAreaExplored;
        public System.Action<bool> OnMapToggled;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeMapSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupPlayerReferences();
            InitializeFogOfWar();
            SetupUI();
            LoadMapData();
        }

        private void Update()
        {
            UpdatePlayerPosition();
            UpdateFogOfWar();
            UpdateMiniMap();
            HandleMapInput();

            if (IsWorldMapOpen)
            {
                UpdateWorldMapNavigation();
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the map system.
        /// </summary>
        private void InitializeMapSystem()
        {
            // Initialize exploration grid
            int gridWidth = Mathf.RoundToInt(worldBounds.x / explorationRadius);
            int gridHeight = Mathf.RoundToInt(worldBounds.y / explorationRadius);
            exploredGrid = new bool[gridWidth, gridHeight];

            Debug.Log("World Map System initialized");
        }

        /// <summary>
        /// Setup player references.
        /// </summary>
        private void SetupPlayerReferences()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
                playerCamera = Camera.main;
            }
        }

        /// <summary>
        /// Initialize fog of war system.
        /// </summary>
        private void InitializeFogOfWar()
        {
            if (!enableFogOfWar) return;

            // Create fog of war texture
            fogOfWarTexture = new Texture2D(mapResolution, mapResolution, TextureFormat.RGBA32, false);
            fogOfWarTexture.filterMode = FilterMode.Bilinear;

            // Initialize with unexplored color
            Color[] pixels = new Color[mapResolution * mapResolution];
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = unexploredColor;
            }
            fogOfWarTexture.SetPixels(pixels);
            fogOfWarTexture.Apply();

            // Apply to map image
            if (mapImage != null)
            {
                mapImage.texture = fogOfWarTexture;
            }
        }

        /// <summary>
        /// Setup UI components.
        /// </summary>
        private void SetupUI()
        {
            // Hide world map initially
            if (worldMapUI != null)
            {
                worldMapUI.SetActive(false);
            }

            // Setup mini-map
            if (miniMapUI != null)
            {
                miniMapUI.SetActive(IsMiniMapVisible);
            }

            // Setup mini-map size
            if (miniMapContainer != null)
            {
                RectTransform rectTransform = miniMapContainer.GetComponent<RectTransform>();
                if (rectTransform != null)
                {
                    rectTransform.sizeDelta = miniMapSize;
                }
            }
        }
        #endregion

        #region Player Tracking
        /// <summary>
        /// Update player position on map.
        /// </summary>
        private void UpdatePlayerPosition()
        {
            if (playerTransform == null) return;

            Vector3 worldPos = playerTransform.position;
            PlayerMapPosition = WorldToMapPosition(worldPos);

            // Update player icon on mini-map
            if (playerIcon != null && miniMapContainer != null)
            {
                UpdatePlayerIconPosition();
            }
        }

        /// <summary>
        /// Update player icon position on mini-map.
        /// </summary>
        private void UpdatePlayerIconPosition()
        {
            Vector2 normalizedPos = new Vector2(
                (PlayerMapPosition.x + worldBounds.x * 0.5f) / worldBounds.x,
                (PlayerMapPosition.y + worldBounds.y * 0.5f) / worldBounds.y
            );

            RectTransform iconRect = playerIcon.GetComponent<RectTransform>();
            if (iconRect != null)
            {
                Vector2 mapSize = miniMapContainer.GetComponent<RectTransform>().sizeDelta;
                Vector2 iconPos = new Vector2(
                    (normalizedPos.x - 0.5f) * mapSize.x,
                    (normalizedPos.y - 0.5f) * mapSize.y
                );
                iconRect.anchoredPosition = iconPos;
            }

            // Rotate icon based on player rotation
            if (playerTransform != null)
            {
                playerIcon.rotation = Quaternion.Euler(0, 0, -playerTransform.eulerAngles.y);
            }
        }

        /// <summary>
        /// Convert world position to map position.
        /// </summary>
        private Vector2 WorldToMapPosition(Vector3 worldPosition)
        {
            return new Vector2(
                worldPosition.x - worldOffset.x,
                worldPosition.z - worldOffset.y
            );
        }

        /// <summary>
        /// Convert map position to world position.
        /// </summary>
        private Vector3 MapToWorldPosition(Vector2 mapPosition)
        {
            return new Vector3(
                mapPosition.x + worldOffset.x,
                0f,
                mapPosition.y + worldOffset.y
            );
        }
        #endregion

        #region Fog of War
        /// <summary>
        /// Update fog of war based on player exploration.
        /// </summary>
        private void UpdateFogOfWar()
        {
            if (!enableFogOfWar || playerTransform == null) return;
            if (Time.time - lastFogUpdate < fogUpdateInterval) return;

            Vector2 playerMapPos = PlayerMapPosition;
            ExploreArea(playerMapPos, explorationRadius);

            lastFogUpdate = Time.time;
        }

        /// <summary>
        /// Explore area around a position.
        /// </summary>
        public void ExploreArea(Vector2 centerPosition, float radius)
        {
            if (!enableFogOfWar) return;

            // Convert to grid coordinates
            int centerX = Mathf.RoundToInt((centerPosition.x + worldBounds.x * 0.5f) / explorationRadius);
            int centerY = Mathf.RoundToInt((centerPosition.y + worldBounds.y * 0.5f) / explorationRadius);
            int radiusInGrid = Mathf.RoundToInt(radius / explorationRadius);

            bool areaChanged = false;

            // Mark grid cells as explored
            for (int x = centerX - radiusInGrid; x <= centerX + radiusInGrid; x++)
            {
                for (int y = centerY - radiusInGrid; y <= centerY + radiusInGrid; y++)
                {
                    if (x >= 0 && x < exploredGrid.GetLength(0) && y >= 0 && y < exploredGrid.GetLength(1))
                    {
                        float distance = Vector2.Distance(new Vector2(x, y), new Vector2(centerX, centerY));
                        if (distance <= radiusInGrid && !exploredGrid[x, y])
                        {
                            exploredGrid[x, y] = true;
                            areaChanged = true;
                        }
                    }
                }
            }

            if (areaChanged)
            {
                UpdateFogTexture();
                OnAreaExplored?.Invoke(centerPosition);
            }
        }

        /// <summary>
        /// Update fog of war texture.
        /// </summary>
        private void UpdateFogTexture()
        {
            if (fogOfWarTexture == null) return;

            Color[] pixels = fogOfWarTexture.GetPixels();

            for (int x = 0; x < exploredGrid.GetLength(0); x++)
            {
                for (int y = 0; y < exploredGrid.GetLength(1); y++)
                {
                    if (exploredGrid[x, y])
                    {
                        // Convert grid coordinates to texture coordinates
                        int texX = Mathf.RoundToInt((float)x / exploredGrid.GetLength(0) * mapResolution);
                        int texY = Mathf.RoundToInt((float)y / exploredGrid.GetLength(1) * mapResolution);

                        // Update texture pixels in a small area around the grid cell
                        int brushSize = Mathf.Max(1, mapResolution / exploredGrid.GetLength(0));
                        for (int dx = -brushSize; dx <= brushSize; dx++)
                        {
                            for (int dy = -brushSize; dy <= brushSize; dy++)
                            {
                                int pixelX = Mathf.Clamp(texX + dx, 0, mapResolution - 1);
                                int pixelY = Mathf.Clamp(texY + dy, 0, mapResolution - 1);
                                int pixelIndex = pixelY * mapResolution + pixelX;

                                if (pixelIndex >= 0 && pixelIndex < pixels.Length)
                                {
                                    pixels[pixelIndex] = Color.Lerp(pixels[pixelIndex], exploredColor, 0.8f);
                                }
                            }
                        }
                    }
                }
            }

            fogOfWarTexture.SetPixels(pixels);
            fogOfWarTexture.Apply();
        }

        /// <summary>
        /// Check if an area is explored.
        /// </summary>
        public bool IsAreaExplored(Vector2 position)
        {
            if (!enableFogOfWar) return true;

            int gridX = Mathf.RoundToInt((position.x + worldBounds.x * 0.5f) / explorationRadius);
            int gridY = Mathf.RoundToInt((position.y + worldBounds.y * 0.5f) / explorationRadius);

            if (gridX >= 0 && gridX < exploredGrid.GetLength(0) &&
                gridY >= 0 && gridY < exploredGrid.GetLength(1))
            {
                return exploredGrid[gridX, gridY];
            }

            return false;
        }
        #endregion

        #region Marker System
        /// <summary>
        /// Add a custom marker to the map.
        /// </summary>
        public MapMarker AddCustomMarker(Vector3 worldPosition, string title, string description, MapMarkerType markerType)
        {
            if (customMarkers.Count >= maxCustomMarkers)
            {
                Debug.LogWarning("Maximum number of custom markers reached");
                return null;
            }

            var marker = new MapMarker
            {
                id = System.Guid.NewGuid().ToString(),
                worldPosition = worldPosition,
                mapPosition = WorldToMapPosition(worldPosition),
                title = title,
                description = description,
                markerType = markerType,
                isCustom = true,
                isVisible = true
            };

            customMarkers.Add(marker);
            activeMarkers.Add(marker);
            CreateMarkerObject(marker);

            OnMarkerAdded?.Invoke(marker);
            SaveMapData();

            return marker;
        }

        /// <summary>
        /// Remove a custom marker.
        /// </summary>
        public bool RemoveCustomMarker(string markerId)
        {
            var marker = customMarkers.FirstOrDefault(m => m.id == markerId);
            if (marker != null)
            {
                customMarkers.Remove(marker);
                activeMarkers.Remove(marker);
                DestroyMarkerObject(marker);

                OnMarkerRemoved?.Invoke(marker);
                SaveMapData();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Create visual marker object.
        /// </summary>
        private void CreateMarkerObject(MapMarker marker)
        {
            if (markerPrefab == null || markerContainer == null) return;

            GameObject markerObj = Instantiate(markerPrefab, markerContainer);
            markerObjects[marker.id] = markerObj;

            // Position marker on map
            UpdateMarkerPosition(marker, markerObj);

            // Setup marker component
            var markerComponent = markerObj.GetComponent<MapMarkerUI>();
            if (markerComponent != null)
            {
                markerComponent.Setup(marker);
            }
        }

        /// <summary>
        /// Update marker position on map.
        /// </summary>
        private void UpdateMarkerPosition(MapMarker marker, GameObject markerObj)
        {
            if (markerObj == null) return;

            Vector2 normalizedPos = new Vector2(
                (marker.mapPosition.x + worldBounds.x * 0.5f) / worldBounds.x,
                (marker.mapPosition.y + worldBounds.y * 0.5f) / worldBounds.y
            );

            RectTransform rectTransform = markerObj.GetComponent<RectTransform>();
            if (rectTransform != null && mapContainer != null)
            {
                Vector2 mapSize = mapContainer.GetComponent<RectTransform>().sizeDelta;
                Vector2 markerPos = new Vector2(
                    (normalizedPos.x - 0.5f) * mapSize.x,
                    (normalizedPos.y - 0.5f) * mapSize.y
                );
                rectTransform.anchoredPosition = markerPos;
            }
        }

        /// <summary>
        /// Destroy marker object.
        /// </summary>
        private void DestroyMarkerObject(MapMarker marker)
        {
            if (markerObjects.ContainsKey(marker.id))
            {
                Destroy(markerObjects[marker.id]);
                markerObjects.Remove(marker.id);
            }
        }

        /// <summary>
        /// Get all markers of a specific type.
        /// </summary>
        public List<MapMarker> GetMarkersByType(MapMarkerType markerType)
        {
            return activeMarkers.Where(m => m.markerType.Equals(markerType)).ToList();
        }

        /// <summary>
        /// Get marker by ID.
        /// </summary>
        public MapMarker GetMarkerById(string markerId)
        {
            return activeMarkers.FirstOrDefault(m => m.id == markerId);
        }
        #endregion

        #region Mini-Map System
        /// <summary>
        /// Update mini-map display.
        /// </summary>
        private void UpdateMiniMap()
        {
            if (!IsMiniMapVisible || miniMapContainer == null) return;

            UpdateTrackedObjects();
            UpdateMiniMapZoom();
        }

        /// <summary>
        /// Update tracked objects on mini-map.
        /// </summary>
        private void UpdateTrackedObjects()
        {
            foreach (var trackedObj in trackedObjects.ToList())
            {
                if (trackedObj == null)
                {
                    RemoveTrackedObject(trackedObj);
                    continue;
                }

                UpdateTrackedObjectIcon(trackedObj);
            }
        }

        /// <summary>
        /// Add object to mini-map tracking.
        /// </summary>
        public void AddTrackedObject(Transform obj, GameObject iconPrefab)
        {
            if (trackedObjects.Contains(obj)) return;

            trackedObjects.Add(obj);

            if (iconPrefab != null && miniMapContainer != null)
            {
                GameObject icon = Instantiate(iconPrefab, miniMapContainer);
                miniMapIcons[obj] = icon;
            }
        }

        /// <summary>
        /// Remove object from mini-map tracking.
        /// </summary>
        public void RemoveTrackedObject(Transform obj)
        {
            trackedObjects.Remove(obj);

            if (miniMapIcons.ContainsKey(obj))
            {
                Destroy(miniMapIcons[obj]);
                miniMapIcons.Remove(obj);
            }
        }

        /// <summary>
        /// Update tracked object icon position.
        /// </summary>
        private void UpdateTrackedObjectIcon(Transform obj)
        {
            if (!miniMapIcons.ContainsKey(obj)) return;

            GameObject icon = miniMapIcons[obj];
            Vector2 objMapPos = WorldToMapPosition(obj.position);

            // Check if object is within mini-map range
            float distanceFromPlayer = Vector2.Distance(objMapPos, PlayerMapPosition);
            float miniMapRange = miniMapSize.x * 0.5f / miniMapZoom;

            if (distanceFromPlayer <= miniMapRange)
            {
                icon.SetActive(true);

                Vector2 relativePos = objMapPos - PlayerMapPosition;
                Vector2 iconPos = relativePos * miniMapZoom;

                RectTransform iconRect = icon.GetComponent<RectTransform>();
                if (iconRect != null)
                {
                    iconRect.anchoredPosition = iconPos;
                }
            }
            else
            {
                icon.SetActive(false);
            }
        }

        /// <summary>
        /// Update mini-map zoom level.
        /// </summary>
        private void UpdateMiniMapZoom()
        {
            // Could implement dynamic zoom based on player speed or other factors
        }

        /// <summary>
        /// Set mini-map zoom level.
        /// </summary>
        public void SetMiniMapZoom(float zoom)
        {
            miniMapZoom = Mathf.Clamp(zoom, 0.5f, 3f);
        }

        /// <summary>
        /// Toggle mini-map visibility.
        /// </summary>
        public void ToggleMiniMap()
        {
            IsMiniMapVisible = !IsMiniMapVisible;
            if (miniMapUI != null)
            {
                miniMapUI.SetActive(IsMiniMapVisible);
            }
        }
        #endregion

        #region Map Navigation
        /// <summary>
        /// Handle map input for navigation.
        /// </summary>
        private void HandleMapInput()
        {
            // Toggle world map
            if (Input.GetKeyDown(KeyCode.M))
            {
                ToggleWorldMap();
            }

            // Toggle mini-map
            if (Input.GetKeyDown(KeyCode.N))
            {
                ToggleMiniMap();
            }
        }

        /// <summary>
        /// Update world map navigation (pan and zoom).
        /// </summary>
        private void UpdateWorldMapNavigation()
        {
            HandleMapPanning();
            HandleMapZooming();
        }

        /// <summary>
        /// Handle map panning with mouse.
        /// </summary>
        private void HandleMapPanning()
        {
            if (Input.GetMouseButtonDown(0))
            {
                isDragging = true;
                lastMousePosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                isDragging = false;
            }

            if (isDragging)
            {
                Vector2 mouseDelta = (Vector2)Input.mousePosition - lastMousePosition;
                mapPanOffset += mouseDelta * 0.5f;
                lastMousePosition = Input.mousePosition;

                UpdateMapPosition();
            }
        }

        /// <summary>
        /// Handle map zooming with scroll wheel.
        /// </summary>
        private void HandleMapZooming()
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scroll) > 0.01f)
            {
                mapZoomLevel = Mathf.Clamp(mapZoomLevel + scroll * 0.5f, 0.5f, 3f);
                UpdateMapZoom();
            }
        }

        /// <summary>
        /// Update map position based on pan offset.
        /// </summary>
        private void UpdateMapPosition()
        {
            if (mapContainer != null)
            {
                RectTransform rectTransform = mapContainer.GetComponent<RectTransform>();
                if (rectTransform != null)
                {
                    rectTransform.anchoredPosition = mapPanOffset;
                }
            }
        }

        /// <summary>
        /// Update map zoom level.
        /// </summary>
        private void UpdateMapZoom()
        {
            if (mapContainer != null)
            {
                mapContainer.localScale = Vector3.one * mapZoomLevel;
            }
        }

        /// <summary>
        /// Center map on player position.
        /// </summary>
        public void CenterMapOnPlayer()
        {
            if (playerTransform == null) return;

            Vector2 playerScreenPos = WorldToScreenPosition(PlayerMapPosition);
            mapPanOffset = -playerScreenPos;
            UpdateMapPosition();
        }

        /// <summary>
        /// Center map on specific world position.
        /// </summary>
        public void CenterMapOnPosition(Vector3 worldPosition)
        {
            Vector2 mapPos = WorldToMapPosition(worldPosition);
            Vector2 screenPos = WorldToScreenPosition(mapPos);
            mapPanOffset = -screenPos;
            UpdateMapPosition();
        }

        /// <summary>
        /// Convert world position to screen position on map.
        /// </summary>
        private Vector2 WorldToScreenPosition(Vector2 mapPosition)
        {
            Vector2 normalizedPos = new Vector2(
                (mapPosition.x + worldBounds.x * 0.5f) / worldBounds.x,
                (mapPosition.y + worldBounds.y * 0.5f) / worldBounds.y
            );

            if (mapContainer != null)
            {
                Vector2 mapSize = mapContainer.GetComponent<RectTransform>().sizeDelta;
                return new Vector2(
                    (normalizedPos.x - 0.5f) * mapSize.x,
                    (normalizedPos.y - 0.5f) * mapSize.y
                );
            }

            return Vector2.zero;
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Toggle world map visibility.
        /// </summary>
        public void ToggleWorldMap()
        {
            IsWorldMapOpen = !IsWorldMapOpen;

            if (worldMapUI != null)
            {
                worldMapUI.SetActive(IsWorldMapOpen);
            }

            if (IsWorldMapOpen)
            {
                CenterMapOnPlayer();
                RefreshMarkers();
            }

            OnMapToggled?.Invoke(IsWorldMapOpen);
        }

        /// <summary>
        /// Open world map.
        /// </summary>
        public void OpenWorldMap()
        {
            if (!IsWorldMapOpen)
            {
                ToggleWorldMap();
            }
        }

        /// <summary>
        /// Close world map.
        /// </summary>
        public void CloseWorldMap()
        {
            if (IsWorldMapOpen)
            {
                ToggleWorldMap();
            }
        }

        /// <summary>
        /// Refresh all markers on the map.
        /// </summary>
        private void RefreshMarkers()
        {
            foreach (var marker in activeMarkers)
            {
                if (markerObjects.ContainsKey(marker.id))
                {
                    UpdateMarkerPosition(marker, markerObjects[marker.id]);
                }
            }
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save map data to PlayerPrefs.
        /// </summary>
        private void SaveMapData()
        {
            var saveData = new MapSaveData
            {
                exploredAreas = GetExploredAreas(),
                customMarkers = customMarkers.ToArray(),
                miniMapVisible = IsMiniMapVisible,
                miniMapZoom = miniMapZoom
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("WorldMapData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load map data from PlayerPrefs.
        /// </summary>
        private void LoadMapData()
        {
            string json = PlayerPrefs.GetString("WorldMapData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<MapSaveData>(json);

                    // Restore explored areas
                    RestoreExploredAreas(saveData.exploredAreas);

                    // Restore custom markers
                    if (saveData.customMarkers != null)
                    {
                        foreach (var marker in saveData.customMarkers)
                        {
                            customMarkers.Add(marker);
                            activeMarkers.Add(marker);
                            CreateMarkerObject(marker);
                        }
                    }

                    // Restore UI settings
                    IsMiniMapVisible = saveData.miniMapVisible;
                    miniMapZoom = saveData.miniMapZoom;

                    if (miniMapUI != null)
                    {
                        miniMapUI.SetActive(IsMiniMapVisible);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load map data: {e.Message}");
                }
            }
        }

        /// <summary>
        /// Get explored areas as serializable data.
        /// </summary>
        private ExploredArea[] GetExploredAreas()
        {
            var exploredAreas = new List<ExploredArea>();

            for (int x = 0; x < exploredGrid.GetLength(0); x++)
            {
                for (int y = 0; y < exploredGrid.GetLength(1); y++)
                {
                    if (exploredGrid[x, y])
                    {
                        exploredAreas.Add(new ExploredArea { x = x, y = y });
                    }
                }
            }

            return exploredAreas.ToArray();
        }

        /// <summary>
        /// Restore explored areas from save data.
        /// </summary>
        private void RestoreExploredAreas(ExploredArea[] areas)
        {
            if (areas == null) return;

            foreach (var area in areas)
            {
                if (area.x >= 0 && area.x < exploredGrid.GetLength(0) &&
                    area.y >= 0 && area.y < exploredGrid.GetLength(1))
                {
                    exploredGrid[area.x, area.y] = true;
                }
            }

            UpdateFogTexture();
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get all custom markers.
        /// </summary>
        public List<MapMarker> GetCustomMarkers()
        {
            return new List<MapMarker>(customMarkers);
        }

        /// <summary>
        /// Get all active markers.
        /// </summary>
        public List<MapMarker> GetAllMarkers()
        {
            return new List<MapMarker>(activeMarkers);
        }

        /// <summary>
        /// Clear all custom markers.
        /// </summary>
        public void ClearCustomMarkers()
        {
            foreach (var marker in customMarkers.ToList())
            {
                RemoveCustomMarker(marker.id);
            }
        }

        /// <summary>
        /// Set world bounds for the map.
        /// </summary>
        public void SetWorldBounds(Vector2 bounds, Vector2 offset)
        {
            worldBounds = bounds;
            worldOffset = offset;

            // Reinitialize exploration grid
            int gridWidth = Mathf.RoundToInt(worldBounds.x / explorationRadius);
            int gridHeight = Mathf.RoundToInt(worldBounds.y / explorationRadius);
            exploredGrid = new bool[gridWidth, gridHeight];
        }

        /// <summary>
        /// Get exploration percentage.
        /// </summary>
        public float GetExplorationPercentage()
        {
            if (exploredGrid == null) return 0f;

            int totalCells = exploredGrid.GetLength(0) * exploredGrid.GetLength(1);
            int exploredCells = 0;

            for (int x = 0; x < exploredGrid.GetLength(0); x++)
            {
                for (int y = 0; y < exploredGrid.GetLength(1); y++)
                {
                    if (exploredGrid[x, y]) exploredCells++;
                }
            }

            return totalCells > 0 ? (float)exploredCells / totalCells : 0f;
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class MapMarker
    {
        public string id;
        public Vector3 worldPosition;
        public Vector2 mapPosition;
        public string title;
        public string description;
        public MapMarkerType markerType;
        public bool isCustom;
        public bool isVisible;
        public float timestamp;
    }

    [System.Serializable]
    public class MapMarkerType
    {
        public string typeName;
        public Sprite icon;
        public Color color = Color.white;
        public bool showOnMiniMap = true;
        public bool showOnWorldMap = true;
    }

    [System.Serializable]
    public class MapSaveData
    {
        public ExploredArea[] exploredAreas;
        public MapMarker[] customMarkers;
        public bool miniMapVisible;
        public float miniMapZoom;
    }

    [System.Serializable]
    public class ExploredArea
    {
        public int x;
        public int y;
    }
    #endregion
}
