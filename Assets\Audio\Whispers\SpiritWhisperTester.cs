using UnityEngine;

/// <summary>
/// Test script to verify spirit whisper audio system functionality
/// Attach to any GameObject to test spirit whisper playback
/// </summary>
public class SpiritWhisperTester : MonoBehaviour
{
    [Header("Testing Controls")]
    [Tooltip("Press this key to play a random spirit whisper")]
    public KeyCode testKey = KeyCode.T;
    
    [Header("Spirit Whisper System")]
    public SpiritWhisperAsset spiritWhisperAsset;
    
    [Header("Test Settings")]
    [Range(0f, 1f)]
    public float testVolume = 0.5f;
    
    [Range(0.5f, 2f)]
    public float testPitch = 1f;
    
    public bool useRandomPitch = true;
    public bool show3DVisualization = true;
    
    [Header("Debug Info")]
    public bool showDebugInfo = true;
    public int totalWhispersPlayed = 0;
    public float lastWhisperTime = 0f;
    
    private AudioSource testAudioSource;
    private GameObject visualizationSphere;
    
    void Start()
    {
        InitializeTester();
        LoadSpiritWhisperAsset();
    }
    
    void Update()
    {
        HandleInput();
        UpdateVisualization();
        
        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
    }
    
    void InitializeTester()
    {
        // Create audio source for testing
        testAudioSource = gameObject.GetComponent<AudioSource>();
        if (testAudioSource == null)
        {
            testAudioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Configure audio source
        testAudioSource.spatialBlend = 0.7f; // Partially 3D
        testAudioSource.rolloffMode = AudioRolloffMode.Logarithmic;
        testAudioSource.maxDistance = 15f;
        testAudioSource.playOnAwake = false;
        
        // Create visualization sphere
        if (show3DVisualization)
        {
            CreateVisualizationSphere();
        }
        
        Debug.Log("Spirit Whisper Tester initialized. Press '" + testKey + "' to test whispers.");
    }
    
    void LoadSpiritWhisperAsset()
    {
        if (spiritWhisperAsset == null)
        {
            // Try to load from Resources
            spiritWhisperAsset = Resources.Load<SpiritWhisperAsset>("SpiritWhisperAsset");
            
            if (spiritWhisperAsset == null)
            {
                #if UNITY_EDITOR
                // Try to find in project
                string[] guids = UnityEditor.AssetDatabase.FindAssets("t:SpiritWhisperAsset");
                if (guids.Length > 0)
                {
                    string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
                    spiritWhisperAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<SpiritWhisperAsset>(path);
                    Debug.Log($"Loaded SpiritWhisperAsset from: {path}");
                }
                #endif
            }
            else
            {
                Debug.Log("Loaded SpiritWhisperAsset from Resources folder");
            }
        }
        
        if (spiritWhisperAsset == null)
        {
            Debug.LogError("SpiritWhisperAsset not found! Please generate spirit whispers first.");
        }
        else
        {
            Debug.Log($"Spirit Whisper Asset loaded with {spiritWhisperAsset.spiritWhispers.Length} clips");
        }
    }
    
    void HandleInput()
    {
        if (Input.GetKeyDown(testKey))
        {
            PlayTestWhisper();
        }
        
        // Additional test keys
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            PlaySpecificWhisper(0);
        }
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            PlaySpecificWhisper(1);
        }
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            PlaySpecificWhisper(2);
        }
    }
    
    void PlayTestWhisper()
    {
        if (spiritWhisperAsset == null)
        {
            Debug.LogWarning("No SpiritWhisperAsset available for testing!");
            return;
        }
        
        AudioClip whisperClip = spiritWhisperAsset.GetRandomWhisper();
        if (whisperClip == null)
        {
            Debug.LogWarning("No whisper clips available in asset!");
            return;
        }
        
        // Configure audio source
        testAudioSource.clip = whisperClip;
        testAudioSource.volume = testVolume;
        
        if (useRandomPitch)
        {
            testAudioSource.pitch = spiritWhisperAsset.GetRandomPitch();
        }
        else
        {
            testAudioSource.pitch = testPitch;
        }
        
        // Play the whisper
        testAudioSource.Play();
        
        // Update statistics
        totalWhispersPlayed++;
        lastWhisperTime = Time.time;
        
        // Trigger visualization
        if (visualizationSphere != null)
        {
            StartCoroutine(AnimateVisualization(whisperClip.length));
        }
        
        Debug.Log($"Playing spirit whisper: {whisperClip.name} " +
                 $"(Volume: {testAudioSource.volume:F2}, Pitch: {testAudioSource.pitch:F2})");
    }
    
    void PlaySpecificWhisper(int index)
    {
        if (spiritWhisperAsset == null)
        {
            Debug.LogWarning("No SpiritWhisperAsset available for testing!");
            return;
        }
        
        AudioClip whisperClip = spiritWhisperAsset.GetWhisper(index);
        if (whisperClip == null)
        {
            Debug.LogWarning($"Whisper clip at index {index} not found!");
            return;
        }
        
        testAudioSource.clip = whisperClip;
        testAudioSource.volume = testVolume;
        testAudioSource.pitch = useRandomPitch ? spiritWhisperAsset.GetRandomPitch() : testPitch;
        testAudioSource.Play();
        
        totalWhispersPlayed++;
        lastWhisperTime = Time.time;
        
        Debug.Log($"Playing specific whisper {index}: {whisperClip.name}");
    }
    
    void CreateVisualizationSphere()
    {
        visualizationSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        visualizationSphere.name = "SpiritWhisperVisualization";
        visualizationSphere.transform.SetParent(transform);
        visualizationSphere.transform.localPosition = Vector3.up * 2f;
        visualizationSphere.transform.localScale = Vector3.one * 0.5f;
        
        // Make it semi-transparent
        Renderer renderer = visualizationSphere.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.SetFloat("_Mode", 3); // Transparent mode
        mat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        mat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        mat.SetInt("_ZWrite", 0);
        mat.DisableKeyword("_ALPHATEST_ON");
        mat.EnableKeyword("_ALPHABLEND_ON");
        mat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        mat.renderQueue = 3000;
        mat.color = new Color(0.5f, 0.8f, 1f, 0.3f);
        renderer.material = mat;
        
        // Remove collider
        Destroy(visualizationSphere.GetComponent<Collider>());
    }
    
    void UpdateVisualization()
    {
        if (visualizationSphere == null) return;
        
        // Gentle floating animation
        float floatOffset = Mathf.Sin(Time.time * 2f) * 0.2f;
        visualizationSphere.transform.localPosition = Vector3.up * (2f + floatOffset);
        
        // Gentle rotation
        visualizationSphere.transform.Rotate(Vector3.up, 30f * Time.deltaTime);
        
        // Pulse when audio is playing
        if (testAudioSource.isPlaying)
        {
            float pulse = 1f + Mathf.Sin(Time.time * 10f) * 0.2f;
            visualizationSphere.transform.localScale = Vector3.one * (0.5f * pulse);
        }
        else
        {
            visualizationSphere.transform.localScale = Vector3.one * 0.5f;
        }
    }
    
    System.Collections.IEnumerator AnimateVisualization(float duration)
    {
        if (visualizationSphere == null) yield break;
        
        float elapsed = 0f;
        Vector3 originalScale = visualizationSphere.transform.localScale;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            // Pulsing effect
            float pulse = 1f + Mathf.Sin(progress * Mathf.PI * 8f) * 0.3f;
            visualizationSphere.transform.localScale = originalScale * pulse;
            
            // Color shift
            Renderer renderer = visualizationSphere.GetComponent<Renderer>();
            if (renderer != null)
            {
                Color baseColor = new Color(0.5f, 0.8f, 1f, 0.3f);
                Color pulseColor = new Color(1f, 0.8f, 0.5f, 0.5f);
                renderer.material.color = Color.Lerp(baseColor, pulseColor, Mathf.Sin(progress * Mathf.PI * 4f) * 0.5f + 0.5f);
            }
            
            yield return null;
        }
        
        // Reset to original state
        visualizationSphere.transform.localScale = originalScale;
        if (visualizationSphere.GetComponent<Renderer>() != null)
        {
            visualizationSphere.GetComponent<Renderer>().material.color = new Color(0.5f, 0.8f, 1f, 0.3f);
        }
    }
    
    void DisplayDebugInfo()
    {
        // Display debug information on screen (would need UI Text component in real implementation)
        if (Time.time % 1f < Time.deltaTime) // Update once per second
        {
            string debugText = $"Spirit Whisper Tester Debug:\n" +
                             $"Asset Loaded: {(spiritWhisperAsset != null ? "Yes" : "No")}\n" +
                             $"Available Clips: {(spiritWhisperAsset?.spiritWhispers?.Length ?? 0)}\n" +
                             $"Total Played: {totalWhispersPlayed}\n" +
                             $"Last Played: {(Time.time - lastWhisperTime):F1}s ago\n" +
                             $"Currently Playing: {testAudioSource.isPlaying}\n" +
                             $"Test Key: {testKey}\n" +
                             $"Number Keys 1-3: Play specific whispers";
            
            // In a real implementation, this would update a UI Text component
            // For now, we'll just log it occasionally
            if (totalWhispersPlayed > 0 && Time.time % 10f < Time.deltaTime)
            {
                Debug.Log(debugText);
            }
        }
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        // Simple on-screen debug display
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Spirit Whisper Tester", GUI.skin.box);
        GUILayout.Label($"Asset: {(spiritWhisperAsset != null ? "Loaded" : "Missing")}");
        GUILayout.Label($"Clips: {(spiritWhisperAsset?.spiritWhispers?.Length ?? 0)}");
        GUILayout.Label($"Played: {totalWhispersPlayed}");
        GUILayout.Label($"Playing: {testAudioSource.isPlaying}");
        GUILayout.Label($"Press '{testKey}' to test");
        GUILayout.Label("Press 1-3 for specific clips");
        GUILayout.EndArea();
    }
    
    // Public methods for external testing
    public void TestRandomWhisper()
    {
        PlayTestWhisper();
    }
    
    public void TestSpecificWhisper(int index)
    {
        PlaySpecificWhisper(index);
    }
    
    public bool IsWhisperSystemReady()
    {
        return spiritWhisperAsset != null && spiritWhisperAsset.spiritWhispers != null && spiritWhisperAsset.spiritWhispers.Length > 0;
    }
}
