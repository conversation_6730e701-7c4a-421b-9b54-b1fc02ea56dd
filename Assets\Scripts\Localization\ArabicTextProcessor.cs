using UnityEngine;
using System.Text;
using System.Collections.Generic;

namespace CinderOfDarkness.Localization
{
    /// <summary>
    /// Arabic Text Processor for proper RTL text rendering and Arabic character shaping.
    /// Handles Arabic text processing, character joining, and RTL layout.
    /// </summary>
    public class ArabicTextProcessor
    {
        #region Arabic Character Mappings
        // Arabic character forms (isolated, initial, medial, final)
        private static readonly Dictionary<char, char[]> arabicCharacterForms = new Dictionary<char, char[]>
        {
            // Alif
            { '\u0627', new char[] { '\u0627', '\u0627', '\u0627', '\u0627' } },
            // Baa
            { '\u0628', new char[] { '\u0628', '\uFE91', '\uFE92', '\uFE90' } },
            // Taa
            { '\u062A', new char[] { '\u062A', '\uFE97', '\uFE98', '\uFE96' } },
            // Thaa
            { '\u062B', new char[] { '\u062B', '\uFE9B', '\uFE9C', '\uFE9A' } },
            // Jeem
            { '\u062C', new char[] { '\u062C', '\uFE9F', '\uFEA0', '\uFE9E' } },
            // Haa
            { '\u062D', new char[] { '\u062D', '\uFEA3', '\uFEA4', '\uFEA2' } },
            // Khaa
            { '\u062E', new char[] { '\u062E', '\uFEA7', '\uFEA8', '\uFEA6' } },
            // Dal
            { '\u062F', new char[] { '\u062F', '\u062F', '\u062F', '\uFEAA' } },
            // Thal
            { '\u0630', new char[] { '\u0630', '\u0630', '\u0630', '\uFEAC' } },
            // Raa
            { '\u0631', new char[] { '\u0631', '\u0631', '\u0631', '\uFEAE' } },
            // Zay
            { '\u0632', new char[] { '\u0632', '\u0632', '\u0632', '\uFEB0' } },
            // Seen
            { '\u0633', new char[] { '\u0633', '\uFEB3', '\uFEB4', '\uFEB2' } },
            // Sheen
            { '\u0634', new char[] { '\u0634', '\uFEB7', '\uFEB8', '\uFEB6' } },
            // Sad
            { '\u0635', new char[] { '\u0635', '\uFEBB', '\uFEBC', '\uFEBA' } },
            // Dad
            { '\u0636', new char[] { '\u0636', '\uFEBF', '\uFEC0', '\uFEBE' } },
            // Taa
            { '\u0637', new char[] { '\u0637', '\uFEC3', '\uFEC4', '\uFEC2' } },
            // Zaa
            { '\u0638', new char[] { '\u0638', '\uFEC7', '\uFEC8', '\uFEC6' } },
            // Ain
            { '\u0639', new char[] { '\u0639', '\uFECB', '\uFECC', '\uFECA' } },
            // Ghain
            { '\u063A', new char[] { '\u063A', '\uFECF', '\uFED0', '\uFECE' } },
            // Faa
            { '\u0641', new char[] { '\u0641', '\uFED3', '\uFED4', '\uFED2' } },
            // Qaf
            { '\u0642', new char[] { '\u0642', '\uFED7', '\uFED8', '\uFED6' } },
            // Kaf
            { '\u0643', new char[] { '\u0643', '\uFEDB', '\uFEDC', '\uFEDA' } },
            // Lam
            { '\u0644', new char[] { '\u0644', '\uFEDF', '\uFEE0', '\uFEDE' } },
            // Meem
            { '\u0645', new char[] { '\u0645', '\uFEE3', '\uFEE4', '\uFEE2' } },
            // Noon
            { '\u0646', new char[] { '\u0646', '\uFEE7', '\uFEE8', '\uFEE6' } },
            // Haa
            { '\u0647', new char[] { '\u0647', '\uFEEB', '\uFEEC', '\uFEEA' } },
            // Waw
            { '\u0648', new char[] { '\u0648', '\u0648', '\u0648', '\uFEEE' } },
            // Yaa
            { '\u064A', new char[] { '\u064A', '\uFEF3', '\uFEF4', '\uFEF2' } }
        };

        // Characters that don't connect to the following character
        private static readonly HashSet<char> nonConnectingChars = new HashSet<char>
        {
            '\u0627', // Alif
            '\u062F', // Dal
            '\u0630', // Thal
            '\u0631', // Raa
            '\u0632', // Zay
            '\u0648'  // Waw
        };

        // Arabic diacritics (tashkeel)
        private static readonly HashSet<char> arabicDiacritics = new HashSet<char>
        {
            '\u064B', // Fathatan
            '\u064C', // Dammatan
            '\u064D', // Kasratan
            '\u064E', // Fatha
            '\u064F', // Damma
            '\u0650', // Kasra
            '\u0651', // Shadda
            '\u0652', // Sukun
            '\u0653', // Maddah
            '\u0654', // Hamza above
            '\u0655', // Hamza below
            '\u0656', // Subscript alif
            '\u0657', // Inverted damma
            '\u0658', // Mark noon ghunna
            '\u0659', // Zwarakay
            '\u065A', // Vowel sign small v above
            '\u065B', // Vowel sign inverted small v above
            '\u065C', // Vowel sign dot below
            '\u065D', // Reversed damma
            '\u065E', // Fatha with two dots
            '\u065F', // Wavy hamza below
            '\u0670'  // Superscript alif
        };
        #endregion

        #region Public Methods
        /// <summary>
        /// Process Arabic text for proper rendering.
        /// </summary>
        /// <param name="text">Input Arabic text</param>
        /// <returns>Processed text ready for rendering</returns>
        public string ProcessArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // Step 1: Shape Arabic characters
            string shapedText = ShapeArabicCharacters(text);

            // Step 2: Reverse text for RTL display
            string rtlText = ReverseTextForRTL(shapedText);

            return rtlText;
        }

        /// <summary>
        /// Shape Arabic characters based on their position in words.
        /// </summary>
        /// <param name="text">Input text</param>
        /// <returns>Shaped text</returns>
        private string ShapeArabicCharacters(string text)
        {
            StringBuilder result = new StringBuilder();
            
            for (int i = 0; i < text.Length; i++)
            {
                char currentChar = text[i];
                
                // Skip non-Arabic characters and diacritics
                if (!IsArabicCharacter(currentChar))
                {
                    result.Append(currentChar);
                    continue;
                }

                // Skip diacritics
                if (arabicDiacritics.Contains(currentChar))
                {
                    result.Append(currentChar);
                    continue;
                }

                // Determine character form based on context
                CharacterForm form = DetermineCharacterForm(text, i);
                char shapedChar = GetShapedCharacter(currentChar, form);
                
                result.Append(shapedChar);
            }

            return result.ToString();
        }

        /// <summary>
        /// Reverse text for RTL display while preserving numbers and Latin text.
        /// </summary>
        /// <param name="text">Input text</param>
        /// <returns>RTL-ordered text</returns>
        private string ReverseTextForRTL(string text)
        {
            List<TextSegment> segments = SegmentText(text);
            
            // Reverse the order of segments
            segments.Reverse();
            
            StringBuilder result = new StringBuilder();
            foreach (TextSegment segment in segments)
            {
                if (segment.isRTL)
                {
                    // Reverse RTL segments
                    char[] chars = segment.text.ToCharArray();
                    System.Array.Reverse(chars);
                    result.Append(new string(chars));
                }
                else
                {
                    // Keep LTR segments as-is
                    result.Append(segment.text);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Segment text into RTL and LTR parts.
        /// </summary>
        /// <param name="text">Input text</param>
        /// <returns>List of text segments</returns>
        private List<TextSegment> SegmentText(string text)
        {
            List<TextSegment> segments = new List<TextSegment>();
            StringBuilder currentSegment = new StringBuilder();
            bool currentIsRTL = false;
            bool segmentStarted = false;

            for (int i = 0; i < text.Length; i++)
            {
                char c = text[i];
                bool charIsRTL = IsRTLCharacter(c);

                if (!segmentStarted)
                {
                    currentIsRTL = charIsRTL;
                    segmentStarted = true;
                }
                else if (charIsRTL != currentIsRTL)
                {
                    // Direction changed, finish current segment
                    if (currentSegment.Length > 0)
                    {
                        segments.Add(new TextSegment
                        {
                            text = currentSegment.ToString(),
                            isRTL = currentIsRTL
                        });
                        currentSegment.Clear();
                    }
                    currentIsRTL = charIsRTL;
                }

                currentSegment.Append(c);
            }

            // Add final segment
            if (currentSegment.Length > 0)
            {
                segments.Add(new TextSegment
                {
                    text = currentSegment.ToString(),
                    isRTL = currentIsRTL
                });
            }

            return segments;
        }

        /// <summary>
        /// Determine the form of an Arabic character based on its context.
        /// </summary>
        /// <param name="text">Full text</param>
        /// <param name="index">Character index</param>
        /// <returns>Character form</returns>
        private CharacterForm DetermineCharacterForm(string text, int index)
        {
            char currentChar = text[index];
            
            // Check if character can connect
            if (!CanConnect(currentChar))
                return CharacterForm.Isolated;

            bool canConnectToPrevious = false;
            bool canConnectToNext = false;

            // Check previous character
            if (index > 0)
            {
                char prevChar = GetPreviousArabicChar(text, index);
                if (prevChar != '\0' && CanConnect(prevChar) && !nonConnectingChars.Contains(prevChar))
                {
                    canConnectToPrevious = true;
                }
            }

            // Check next character
            if (index < text.Length - 1)
            {
                char nextChar = GetNextArabicChar(text, index);
                if (nextChar != '\0' && CanConnect(nextChar))
                {
                    canConnectToNext = true;
                }
            }

            // Determine form
            if (canConnectToPrevious && canConnectToNext)
                return CharacterForm.Medial;
            else if (canConnectToPrevious)
                return CharacterForm.Final;
            else if (canConnectToNext)
                return CharacterForm.Initial;
            else
                return CharacterForm.Isolated;
        }

        /// <summary>
        /// Get the shaped form of an Arabic character.
        /// </summary>
        /// <param name="character">Original character</param>
        /// <param name="form">Desired form</param>
        /// <returns>Shaped character</returns>
        private char GetShapedCharacter(char character, CharacterForm form)
        {
            if (arabicCharacterForms.ContainsKey(character))
            {
                char[] forms = arabicCharacterForms[character];
                return forms[(int)form];
            }

            return character;
        }

        /// <summary>
        /// Get the previous Arabic character, skipping diacritics.
        /// </summary>
        /// <param name="text">Text</param>
        /// <param name="index">Current index</param>
        /// <returns>Previous Arabic character or null character</returns>
        private char GetPreviousArabicChar(string text, int index)
        {
            for (int i = index - 1; i >= 0; i--)
            {
                char c = text[i];
                if (!arabicDiacritics.Contains(c))
                {
                    return IsArabicCharacter(c) ? c : '\0';
                }
            }
            return '\0';
        }

        /// <summary>
        /// Get the next Arabic character, skipping diacritics.
        /// </summary>
        /// <param name="text">Text</param>
        /// <param name="index">Current index</param>
        /// <returns>Next Arabic character or null character</returns>
        private char GetNextArabicChar(string text, int index)
        {
            for (int i = index + 1; i < text.Length; i++)
            {
                char c = text[i];
                if (!arabicDiacritics.Contains(c))
                {
                    return IsArabicCharacter(c) ? c : '\0';
                }
            }
            return '\0';
        }

        /// <summary>
        /// Check if a character is Arabic.
        /// </summary>
        /// <param name="c">Character to check</param>
        /// <returns>True if character is Arabic</returns>
        private bool IsArabicCharacter(char c)
        {
            return (c >= '\u0600' && c <= '\u06FF') || // Arabic block
                   (c >= '\uFE70' && c <= '\uFEFF') || // Arabic Presentation Forms-B
                   (c >= '\uFB50' && c <= '\uFDFF');   // Arabic Presentation Forms-A
        }

        /// <summary>
        /// Check if a character is RTL.
        /// </summary>
        /// <param name="c">Character to check</param>
        /// <returns>True if character is RTL</returns>
        private bool IsRTLCharacter(char c)
        {
            return IsArabicCharacter(c) || 
                   (c >= '\u0590' && c <= '\u05FF'); // Hebrew block
        }

        /// <summary>
        /// Check if a character can connect to other characters.
        /// </summary>
        /// <param name="c">Character to check</param>
        /// <returns>True if character can connect</returns>
        private bool CanConnect(char c)
        {
            return arabicCharacterForms.ContainsKey(c);
        }
        #endregion

        #region Helper Classes
        /// <summary>
        /// Text segment for RTL processing.
        /// </summary>
        private class TextSegment
        {
            public string text;
            public bool isRTL;
        }

        /// <summary>
        /// Arabic character forms.
        /// </summary>
        private enum CharacterForm
        {
            Isolated = 0,
            Initial = 1,
            Medial = 2,
            Final = 3
        }
        #endregion

        #region Public Utility Methods
        /// <summary>
        /// Check if text contains Arabic characters.
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if text contains Arabic</returns>
        public bool ContainsArabic(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            foreach (char c in text)
            {
                if (IsArabicCharacter(c))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Remove Arabic diacritics from text.
        /// </summary>
        /// <param name="text">Input text</param>
        /// <returns>Text without diacritics</returns>
        public string RemoveDiacritics(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            StringBuilder result = new StringBuilder();
            foreach (char c in text)
            {
                if (!arabicDiacritics.Contains(c))
                {
                    result.Append(c);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Get text direction for mixed content.
        /// </summary>
        /// <param name="text">Text to analyze</param>
        /// <returns>Overall text direction</returns>
        public TextDirection GetTextDirection(string text)
        {
            if (string.IsNullOrEmpty(text))
                return TextDirection.LTR;

            int rtlCount = 0;
            int ltrCount = 0;

            foreach (char c in text)
            {
                if (IsRTLCharacter(c))
                    rtlCount++;
                else if (char.IsLetter(c))
                    ltrCount++;
            }

            if (rtlCount > ltrCount)
                return TextDirection.RTL;
            else if (ltrCount > rtlCount)
                return TextDirection.LTR;
            else
                return TextDirection.Mixed;
        }
        #endregion
    }

    /// <summary>
    /// Text direction enumeration.
    /// </summary>
    public enum TextDirection
    {
        LTR,
        RTL,
        Mixed
    }
}
