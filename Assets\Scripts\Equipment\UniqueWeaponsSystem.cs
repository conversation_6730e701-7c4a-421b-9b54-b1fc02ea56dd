using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class UniqueWeaponsSystem : MonoBehaviour
{
    [Header("Unique Weapons")]
    public UniqueWeapon[] legendaryWeapons;
    public List<string> unlockedWeapons = new List<string>();
    
    [Header("Weapon Effects")]
    public GameObject poisonCloudPrefab;
    public GameObject shockwavePrefab;
    public GameObject bloodAuraPrefab;
    public AudioClip[] harpMelodies;
    
    private PsychologicalSystem psycheSystem;
    private PhilosophicalMoralitySystem moralitySystem;
    private DeathConsequenceSystem deathSystem;
    private PlayerCombat playerCombat;
    
    [System.Serializable]
    public class UniqueWeapon
    {
        [Header("Identity")]
        public string weaponName;
        public string loreDescription;
        public WeaponType type;
        public UnlockCondition unlockCondition;
        
        [Header("Stats")]
        public float baseDamage;
        public float attackSpeed;
        public float range;
        public float weight;
        
        [Header("Special Properties")]
        public SpecialEffect[] effects;
        public string[] requiredMaterials;
        public bool scalesWithSin;
        public bool scalesWithVirtue;
        public bool requiresBloodSacrifice;
        
        [Header("Visual & Audio")]
        public GameObject weaponModel;
        public Material weaponMaterial;
        public AudioClip[] attackSounds;
        public ParticleSystem weaponAura;
        
        public enum WeaponType
        {
            CradleFang,      // Poison dagger
            RuinHarp,        // Musical weapon
            CursedNail,      // Sin-scaling hammer
            FeatherCleaver,  // Anti-gravity axe
            VowOfForsaken    // Blood-fed evil sword
        }
    }
    
    [System.Serializable]
    public class SpecialEffect
    {
        public string effectName;
        public EffectType type;
        public float magnitude;
        public float duration;
        public string description;
        
        public enum EffectType
        {
            PoisonDebuff,
            EmotionalDebuff,
            MusicalShockwave,
            SinScaling,
            AntiGravity,
            BloodFeed,
            MadnessInfliction,
            MemoryErasure
        }
    }
    
    [System.Serializable]
    public class UnlockCondition
    {
        public ConditionType type;
        public float requiredValue;
        public string requiredQuest;
        public PhilosophicalMoralitySystem.MoralPath requiredPath;
        public string requiredLocation;
        
        public enum ConditionType
        {
            SinCount,
            VirtueLevel,
            QuestCompletion,
            LocationDiscovery,
            MoralPath,
            SpecificKill,
            BloodSacrifice
        }
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        playerCombat = GetComponent<PlayerCombat>();
        
        InitializeUniqueWeapons();
    }
    
    void Update()
    {
        CheckWeaponUnlocks();
        UpdateActiveWeaponEffects();
    }
    
    void InitializeUniqueWeapons()
    {
        // Cradle Fang - Poison dagger with emotional debuff
        UniqueWeapon cradleFang = new UniqueWeapon
        {
            weaponName = "Cradle Fang",
            loreDescription = "A dagger carved from the tooth of a dying mother's last breath. It weeps poison and sorrow in equal measure, inflicting not just physical pain but emotional anguish upon its victims.",
            type = UniqueWeapon.WeaponType.CradleFang,
            baseDamage = 45f,
            attackSpeed = 2.5f,
            range = 1.2f,
            weight = 0.8f,
            effects = new SpecialEffect[]
            {
                new SpecialEffect
                {
                    effectName = "Maternal Poison",
                    type = SpecialEffect.EffectType.PoisonDebuff,
                    magnitude = 15f,
                    duration = 8f,
                    description = "Victims suffer poison damage and see visions of disappointed mothers"
                },
                new SpecialEffect
                {
                    effectName = "Guilt of the Cradle",
                    type = SpecialEffect.EffectType.EmotionalDebuff,
                    magnitude = 25f,
                    duration = 12f,
                    description = "Enemies become sluggish as they're overwhelmed by memories of innocence lost"
                }
            },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.QuestCompletion,
                requiredQuest = "A Mother's Desperation"
            }
        };
        
        // Ruin Harp - Musical shockwave weapon
        UniqueWeapon ruinHarp = new UniqueWeapon
        {
            weaponName = "Ruin Harp",
            loreDescription = "Once played to soothe crying children, this harp now sings only songs of destruction. Each string vibrates with the frequency of collapse, turning melody into devastation.",
            type = UniqueWeapon.WeaponType.RuinHarp,
            baseDamage = 35f,
            attackSpeed = 1.8f,
            range = 8f,
            weight = 2.1f,
            effects = new SpecialEffect[]
            {
                new SpecialEffect
                {
                    effectName = "Resonant Destruction",
                    type = SpecialEffect.EffectType.MusicalShockwave,
                    magnitude = 60f,
                    duration = 3f,
                    description = "Creates expanding shockwaves that shatter armor and break spirits"
                }
            },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.QuestCompletion,
                requiredQuest = "The Silent Musician"
            }
        };
        
        // Cursed Nail - Sin-scaling hammer
        UniqueWeapon cursedNail = new UniqueWeapon
        {
            weaponName = "Cursed Nail",
            loreDescription = "Forged from the nails of a crucified saint, this hammer grows heavier and deadlier with each sin committed. The weight of guilt becomes literal, crushing enemies under the burden of wickedness.",
            type = UniqueWeapon.WeaponType.CursedNail,
            baseDamage = 25f, // Base damage, scales with sin
            attackSpeed = 1.2f,
            range = 2.5f,
            weight = 3.5f,
            scalesWithSin = true,
            effects = new SpecialEffect[]
            {
                new SpecialEffect
                {
                    effectName = "Weight of Sin",
                    type = SpecialEffect.EffectType.SinScaling,
                    magnitude = 2f, // Damage multiplier per sin
                    duration = 0f,
                    description = "Damage increases by 200% for each major sin committed"
                }
            },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.SinCount,
                requiredValue = 10f
            }
        };
        
        // Feather Cleaver - Anti-gravity bone axe
        UniqueWeapon featherCleaver = new UniqueWeapon
        {
            weaponName = "Feather Cleaver",
            loreDescription = "Carved from the wing bone of a fallen angel, this axe defies the natural order. Its strikes lift enemies skyward, denying them the comfort of solid ground as they face judgment.",
            type = UniqueWeapon.WeaponType.FeatherCleaver,
            baseDamage = 70f,
            attackSpeed = 1.5f,
            range = 2.8f,
            weight = 1.0f, // Lighter than it appears
            effects = new SpecialEffect[]
            {
                new SpecialEffect
                {
                    effectName = "Heavenly Lift",
                    type = SpecialEffect.EffectType.AntiGravity,
                    magnitude = 15f,
                    duration = 4f,
                    description = "Struck enemies float helplessly, taking fall damage when effect ends"
                }
            },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.LocationDiscovery,
                requiredLocation = "Celestial Spires"
            }
        };
        
        // Vow of the Forsaken - Blood-fed evil sword
        UniqueWeapon vowOfForsaken = new UniqueWeapon
        {
            weaponName = "Vow of the Forsaken",
            loreDescription = "A blade that thirsts eternally for blood, growing stronger with each life it claims. Only those who have fully embraced darkness can wield it without being consumed by its hunger.",
            type = UniqueWeapon.WeaponType.VowOfForsaken,
            baseDamage = 90f,
            attackSpeed = 2.0f,
            range = 3.2f,
            weight = 2.8f,
            requiresBloodSacrifice = true,
            effects = new SpecialEffect[]
            {
                new SpecialEffect
                {
                    effectName = "Blood Hunger",
                    type = SpecialEffect.EffectType.BloodFeed,
                    magnitude = 10f,
                    duration = 0f,
                    description = "Gains permanent damage for each kill, but slowly drains wielder's health"
                }
            },
            unlockCondition = new UnlockCondition
            {
                type = UnlockCondition.ConditionType.MoralPath,
                requiredPath = PhilosophicalMoralitySystem.MoralPath.Evil,
                requiredValue = 75f // Must be deep in evil path
            }
        };
        
        legendaryWeapons = new UniqueWeapon[] { cradleFang, ruinHarp, cursedNail, featherCleaver, vowOfForsaken };
        
        Debug.Log("Unique weapons system initialized - 5 legendary weapons available");
    }
    
    void CheckWeaponUnlocks()
    {
        foreach (UniqueWeapon weapon in legendaryWeapons)
        {
            if (!unlockedWeapons.Contains(weapon.weaponName) && IsWeaponUnlocked(weapon))
            {
                UnlockWeapon(weapon);
            }
        }
    }
    
    bool IsWeaponUnlocked(UniqueWeapon weapon)
    {
        UnlockCondition condition = weapon.unlockCondition;
        
        switch (condition.type)
        {
            case UnlockCondition.ConditionType.SinCount:
                return deathSystem != null && deathSystem.GetInnocentsKilled() >= condition.requiredValue;
            
            case UnlockCondition.ConditionType.QuestCompletion:
                GameManager gameManager = GameManager.Instance;
                return gameManager != null && gameManager.IsQuestCompleted(condition.requiredQuest);
            
            case UnlockCondition.ConditionType.MoralPath:
                if (moralitySystem == null) return false;
                bool correctPath = moralitySystem.GetCurrentPath() == condition.requiredPath;
                bool sufficientProgression = false;
                
                if (condition.requiredPath == PhilosophicalMoralitySystem.MoralPath.Evil)
                {
                    sufficientProgression = moralitySystem.evilProgression >= condition.requiredValue / 100f;
                }
                else if (condition.requiredPath == PhilosophicalMoralitySystem.MoralPath.Good)
                {
                    sufficientProgression = moralitySystem.goodProgression >= condition.requiredValue / 100f;
                }
                
                return correctPath && sufficientProgression;
            
            case UnlockCondition.ConditionType.LocationDiscovery:
                WorldExplorationSystem exploration = GetComponent<WorldExplorationSystem>();
                return exploration != null && exploration.GetDiscoveredRealms().Contains(condition.requiredLocation);
            
            default:
                return false;
        }
    }
    
    void UnlockWeapon(UniqueWeapon weapon)
    {
        unlockedWeapons.Add(weapon.weaponName);
        
        // Add to inventory
        EconomySystem economy = GetComponent<EconomySystem>();
        if (economy != null)
        {
            economy.AddItem(weapon.weaponName, EconomySystem.InventoryItem.ItemType.Weapon, 
                          EconomySystem.InventoryItem.ItemRarity.Legendary, 1, weapon.baseDamage * 50f);
        }
        
        // Show dramatic unlock sequence
        StartCoroutine(WeaponUnlockSequence(weapon));
        
        Debug.Log($"Legendary weapon unlocked: {weapon.weaponName}");
    }
    
    IEnumerator WeaponUnlockSequence(UniqueWeapon weapon)
    {
        // Dramatic pause
        Time.timeScale = 0.1f;
        
        // Show weapon discovery
        ShowWeaponMessage($"LEGENDARY WEAPON DISCOVERED");
        yield return new WaitForSecondsRealtime(2f);
        
        ShowWeaponMessage($"{weapon.weaponName}");
        yield return new WaitForSecondsRealtime(2f);
        
        ShowWeaponMessage(weapon.loreDescription);
        yield return new WaitForSecondsRealtime(4f);
        
        // Return to normal time
        Time.timeScale = 1f;
        
        // Weapon-specific unlock effects
        switch (weapon.type)
        {
            case UniqueWeapon.WeaponType.CradleFang:
                if (psycheSystem != null)
                {
                    psycheSystem.AddTrauma(10f, "The weight of maternal sorrow");
                }
                break;
            
            case UniqueWeapon.WeaponType.RuinHarp:
                // Play haunting melody
                if (harpMelodies.Length > 0)
                {
                    AudioSource.PlayClipAtPoint(harpMelodies[0], transform.position);
                }
                break;
            
            case UniqueWeapon.WeaponType.VowOfForsaken:
                if (psycheSystem != null)
                {
                    psycheSystem.AddTrauma(20f, "The blade whispers of endless hunger");
                }
                ShowWeaponMessage("The blade thirsts for blood... your blood...");
                break;
        }
    }
    
    void UpdateActiveWeaponEffects()
    {
        // Update effects for currently equipped weapon
        string currentWeapon = GetCurrentlyEquippedWeapon();
        if (string.IsNullOrEmpty(currentWeapon)) return;
        
        UniqueWeapon weapon = GetWeaponByName(currentWeapon);
        if (weapon == null) return;
        
        // Apply continuous effects
        foreach (SpecialEffect effect in weapon.effects)
        {
            ApplyContinuousEffect(weapon, effect);
        }
    }
    
    void ApplyContinuousEffect(UniqueWeapon weapon, SpecialEffect effect)
    {
        switch (effect.type)
        {
            case SpecialEffect.EffectType.SinScaling:
                UpdateSinScalingDamage(weapon);
                break;
            
            case SpecialEffect.EffectType.BloodFeed:
                ApplyBloodFeedEffect(weapon);
                break;
        }
    }
    
    void UpdateSinScalingDamage(UniqueWeapon weapon)
    {
        if (weapon.type == UniqueWeapon.WeaponType.CursedNail && deathSystem != null)
        {
            int innocentKills = deathSystem.GetInnocentsKilled();
            float bonusDamage = weapon.baseDamage * (innocentKills * 2f); // 200% per innocent kill
            
            // Apply to player combat system
            if (playerCombat != null)
            {
                playerCombat.SetWeaponDamageBonus(bonusDamage);
            }
        }
    }
    
    void ApplyBloodFeedEffect(UniqueWeapon weapon)
    {
        if (weapon.type == UniqueWeapon.WeaponType.VowOfForsaken)
        {
            // Slowly drain health
            PlayerStats playerStats = GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                playerStats.TakeDamage(1f * Time.deltaTime); // 1 HP per second
            }
            
            // Show blood aura
            if (bloodAuraPrefab != null && Random.Range(0f, 1f) < 0.01f)
            {
                GameObject aura = Instantiate(bloodAuraPrefab, transform.position, Quaternion.identity);
                Destroy(aura, 3f);
            }
        }
    }
    
    public void OnWeaponAttackHit(string weaponName, GameObject target)
    {
        UniqueWeapon weapon = GetWeaponByName(weaponName);
        if (weapon == null) return;
        
        foreach (SpecialEffect effect in weapon.effects)
        {
            ApplyAttackEffect(weapon, effect, target);
        }
    }
    
    void ApplyAttackEffect(UniqueWeapon weapon, SpecialEffect effect, GameObject target)
    {
        switch (effect.type)
        {
            case SpecialEffect.EffectType.PoisonDebuff:
                ApplyPoisonEffect(target, effect);
                break;
            
            case SpecialEffect.EffectType.EmotionalDebuff:
                ApplyEmotionalDebuff(target, effect);
                break;
            
            case SpecialEffect.EffectType.MusicalShockwave:
                CreateShockwave(effect);
                break;
            
            case SpecialEffect.EffectType.AntiGravity:
                ApplyAntiGravityEffect(target, effect);
                break;
        }
    }
    
    void ApplyPoisonEffect(GameObject target, SpecialEffect effect)
    {
        // Create poison cloud
        if (poisonCloudPrefab != null)
        {
            GameObject cloud = Instantiate(poisonCloudPrefab, target.transform.position, Quaternion.identity);
            Destroy(cloud, effect.duration);
        }
        
        // Apply poison damage over time
        EnemyHealth enemyHealth = target.GetComponent<EnemyHealth>();
        if (enemyHealth != null)
        {
            StartCoroutine(PoisonDamageOverTime(enemyHealth, effect));
        }
        
        // Show maternal visions to enemy
        ShowWeaponMessage("The enemy sees visions of disappointed mothers...");
    }
    
    IEnumerator PoisonDamageOverTime(EnemyHealth enemyHealth, SpecialEffect effect)
    {
        float elapsed = 0f;
        float damagePerSecond = effect.magnitude / effect.duration;
        
        while (elapsed < effect.duration && enemyHealth != null)
        {
            enemyHealth.TakeDamage(damagePerSecond * Time.deltaTime);
            elapsed += Time.deltaTime;
            yield return null;
        }
    }
    
    void ApplyEmotionalDebuff(GameObject target, SpecialEffect effect)
    {
        // Slow enemy movement and attacks
        EnemyAI enemyAI = target.GetComponent<EnemyAI>();
        if (enemyAI != null)
        {
            enemyAI.ApplySlowEffect(effect.duration, 0.5f); // 50% speed reduction
        }
        
        ShowWeaponMessage("The enemy is overwhelmed by memories of innocence lost...");
    }
    
    void CreateShockwave(SpecialEffect effect)
    {
        // Create expanding shockwave
        if (shockwavePrefab != null)
        {
            GameObject shockwave = Instantiate(shockwavePrefab, transform.position, Quaternion.identity);
            
            // Scale shockwave over time
            StartCoroutine(ExpandShockwave(shockwave, effect));
        }
        
        // Play harp melody
        if (harpMelodies.Length > 0)
        {
            AudioClip melody = harpMelodies[Random.Range(0, harpMelodies.Length)];
            AudioSource.PlayClipAtPoint(melody, transform.position);
        }
        
        // Damage all enemies in range
        Collider[] enemies = Physics.OverlapSphere(transform.position, effect.magnitude);
        foreach (Collider enemy in enemies)
        {
            EnemyHealth enemyHealth = enemy.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                enemyHealth.TakeDamage(effect.magnitude);
            }
        }
    }
    
    IEnumerator ExpandShockwave(GameObject shockwave, SpecialEffect effect)
    {
        float duration = effect.duration;
        float elapsed = 0f;
        Vector3 startScale = Vector3.one;
        Vector3 endScale = Vector3.one * effect.magnitude;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            shockwave.transform.localScale = Vector3.Lerp(startScale, endScale, progress);
            yield return null;
        }
        
        Destroy(shockwave);
    }
    
    void ApplyAntiGravityEffect(GameObject target, SpecialEffect effect)
    {
        Rigidbody targetRb = target.GetComponent<Rigidbody>();
        if (targetRb != null)
        {
            // Lift enemy into the air
            targetRb.AddForce(Vector3.up * effect.magnitude, ForceMode.Impulse);
            
            // Apply anti-gravity for duration
            StartCoroutine(AntiGravityDuration(targetRb, effect));
        }
        
        ShowWeaponMessage("The enemy floats helplessly, denied the comfort of solid ground...");
    }
    
    IEnumerator AntiGravityDuration(Rigidbody targetRb, SpecialEffect effect)
    {
        float originalGravity = targetRb.drag;
        targetRb.drag = 10f; // High air resistance
        
        yield return new WaitForSeconds(effect.duration);
        
        if (targetRb != null)
        {
            targetRb.drag = originalGravity;
            
            // Apply fall damage
            EnemyHealth enemyHealth = targetRb.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                enemyHealth.TakeDamage(effect.magnitude * 0.5f); // Fall damage
            }
        }
    }
    
    string GetCurrentlyEquippedWeapon()
    {
        // This would interface with the player's equipment system
        return ""; // Placeholder
    }
    
    UniqueWeapon GetWeaponByName(string weaponName)
    {
        foreach (UniqueWeapon weapon in legendaryWeapons)
        {
            if (weapon.weaponName == weaponName)
                return weapon;
        }
        return null;
    }
    
    void ShowWeaponMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Unique Weapon: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public List<string> GetUnlockedWeapons() => unlockedWeapons;
    public UniqueWeapon[] GetAllWeapons() => legendaryWeapons;
    public bool IsWeaponUnlocked(string weaponName) => unlockedWeapons.Contains(weaponName);
}
