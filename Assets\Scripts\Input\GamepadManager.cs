using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections.Generic;

namespace CinderOfDarkness.Input
{
    /// <summary>
    /// Comprehensive gamepad manager for Xbox and PlayStation controllers.
    /// Handles device detection, button mapping, and haptic feedback.
    /// </summary>
    public class GamepadManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Gamepad Settings")]
        [SerializeField] private bool enableHapticFeedback = true;
        [SerializeField] private float defaultRumbleDuration = 0.2f;
        [SerializeField] private float maxRumbleIntensity = 1f;
        [SerializeField] private bool autoDetectControllers = true;
        
        [Header("Button Mapping")]
        [SerializeField] private GamepadButtonMap buttonMap;
        
        [Header("Sensitivity Settings")]
        [SerializeField] private float stickDeadzone = 0.2f;
        [SerializeField] private float triggerDeadzone = 0.1f;
        [SerializeField] private AnimationCurve stickResponseCurve = AnimationCurve.Linear(0, 0, 1, 1);
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        #endregion

        #region Public Properties
        public static GamepadManager Instance { get; private set; }
        public GamepadType CurrentGamepadType { get; private set; } = GamepadType.None;
        public bool IsGamepadConnected { get; private set; }
        public Gamepad CurrentGamepad { get; private set; }
        public string GamepadName { get; private set; } = "";
        #endregion

        #region Private Fields
        private Dictionary<GamepadType, GamepadProfile> gamepadProfiles;
        private List<Gamepad> connectedGamepads = new List<Gamepad>();
        private float lastRumbleTime;
        private bool isRumbling;
        private System.Coroutine rumbleCoroutine;
        #endregion

        #region Events
        public System.Action<GamepadType> OnGamepadConnected;
        public System.Action<GamepadType> OnGamepadDisconnected;
        public System.Action<GamepadType> OnGamepadChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize gamepad manager singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGamepadManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Start gamepad detection.
        /// </summary>
        private void Start()
        {
            if (autoDetectControllers)
            {
                DetectConnectedGamepads();
            }
        }

        /// <summary>
        /// Update gamepad state.
        /// </summary>
        private void Update()
        {
            UpdateGamepadDetection();
            UpdateDebugInfo();
        }

        /// <summary>
        /// Handle application focus for gamepad state.
        /// </summary>
        /// <param name="hasFocus">True if application has focus</param>
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && isRumbling)
            {
                StopRumble();
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize gamepad manager and profiles.
        /// </summary>
        private void InitializeGamepadManager()
        {
            InitializeGamepadProfiles();
            
            // Subscribe to Input System events
            InputSystem.onDeviceChange += OnDeviceChange;
            
            Debug.Log("GamepadManager initialized");
        }

        /// <summary>
        /// Initialize gamepad profiles for different controller types.
        /// </summary>
        private void InitializeGamepadProfiles()
        {
            gamepadProfiles = new Dictionary<GamepadType, GamepadProfile>
            {
                [GamepadType.Xbox] = new GamepadProfile
                {
                    Type = GamepadType.Xbox,
                    Name = "Xbox Controller",
                    ButtonIcons = LoadXboxButtonIcons(),
                    VibrationSupport = true,
                    TriggerSupport = true
                },
                [GamepadType.PlayStation] = new GamepadProfile
                {
                    Type = GamepadType.PlayStation,
                    Name = "PlayStation Controller",
                    ButtonIcons = LoadPlayStationButtonIcons(),
                    VibrationSupport = true,
                    TriggerSupport = true,
                    HasTouchpad = true
                },
                [GamepadType.Generic] = new GamepadProfile
                {
                    Type = GamepadType.Generic,
                    Name = "Generic Controller",
                    ButtonIcons = LoadGenericButtonIcons(),
                    VibrationSupport = false,
                    TriggerSupport = false
                }
            };
        }

        /// <summary>
        /// Load Xbox controller button icons.
        /// </summary>
        /// <returns>Dictionary of button icons</returns>
        private Dictionary<string, Sprite> LoadXboxButtonIcons()
        {
            return new Dictionary<string, Sprite>
            {
                ["A"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_A"),
                ["B"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_B"),
                ["X"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_X"),
                ["Y"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_Y"),
                ["LB"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_LB"),
                ["RB"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_RB"),
                ["LT"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_LT"),
                ["RT"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_RT"),
                ["LS"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_LS"),
                ["RS"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_RS"),
                ["DPad"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_DPad"),
                ["Menu"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_Menu"),
                ["View"] = Resources.Load<Sprite>("UI/ButtonIcons/Xbox/Xbox_View")
            };
        }

        /// <summary>
        /// Load PlayStation controller button icons.
        /// </summary>
        /// <returns>Dictionary of button icons</returns>
        private Dictionary<string, Sprite> LoadPlayStationButtonIcons()
        {
            return new Dictionary<string, Sprite>
            {
                ["Cross"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Cross"),
                ["Circle"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Circle"),
                ["Square"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Square"),
                ["Triangle"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Triangle"),
                ["L1"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_L1"),
                ["R1"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_R1"),
                ["L2"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_L2"),
                ["R2"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_R2"),
                ["L3"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_L3"),
                ["R3"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_R3"),
                ["DPad"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_DPad"),
                ["Options"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Options"),
                ["Share"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Share"),
                ["Touchpad"] = Resources.Load<Sprite>("UI/ButtonIcons/PlayStation/PS_Touchpad")
            };
        }

        /// <summary>
        /// Load generic controller button icons.
        /// </summary>
        /// <returns>Dictionary of button icons</returns>
        private Dictionary<string, Sprite> LoadGenericButtonIcons()
        {
            return new Dictionary<string, Sprite>
            {
                ["Button1"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Button1"),
                ["Button2"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Button2"),
                ["Button3"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Button3"),
                ["Button4"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Button4"),
                ["Shoulder1"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Shoulder1"),
                ["Shoulder2"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Shoulder2"),
                ["Trigger1"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Trigger1"),
                ["Trigger2"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Trigger2"),
                ["Stick1"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Stick1"),
                ["Stick2"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_Stick2"),
                ["DPad"] = Resources.Load<Sprite>("UI/ButtonIcons/Generic/Generic_DPad")
            };
        }
        #endregion

        #region Gamepad Detection
        /// <summary>
        /// Handle Input System device changes.
        /// </summary>
        /// <param name="device">Device that changed</param>
        /// <param name="change">Type of change</param>
        private void OnDeviceChange(InputDevice device, InputDeviceChange change)
        {
            if (device is Gamepad gamepad)
            {
                switch (change)
                {
                    case InputDeviceChange.Added:
                        OnGamepadAdded(gamepad);
                        break;
                    case InputDeviceChange.Removed:
                        OnGamepadRemoved(gamepad);
                        break;
                    case InputDeviceChange.Reconnected:
                        OnGamepadReconnected(gamepad);
                        break;
                    case InputDeviceChange.Disconnected:
                        OnGamepadDisconnected(gamepad);
                        break;
                }
            }
        }

        /// <summary>
        /// Detect currently connected gamepads.
        /// </summary>
        private void DetectConnectedGamepads()
        {
            connectedGamepads.Clear();
            
            foreach (var gamepad in Gamepad.all)
            {
                if (gamepad != null)
                {
                    connectedGamepads.Add(gamepad);
                }
            }

            if (connectedGamepads.Count > 0)
            {
                SetCurrentGamepad(connectedGamepads[0]);
            }
            else
            {
                SetCurrentGamepad(null);
            }
        }

        /// <summary>
        /// Update gamepad detection state.
        /// </summary>
        private void UpdateGamepadDetection()
        {
            // Check if current gamepad is still connected
            if (CurrentGamepad != null && !CurrentGamepad.added)
            {
                SetCurrentGamepad(null);
            }

            // Auto-select first available gamepad if none selected
            if (CurrentGamepad == null && Gamepad.all.Count > 0)
            {
                SetCurrentGamepad(Gamepad.all[0]);
            }
        }

        /// <summary>
        /// Handle gamepad added event.
        /// </summary>
        /// <param name="gamepad">Added gamepad</param>
        private void OnGamepadAdded(Gamepad gamepad)
        {
            if (!connectedGamepads.Contains(gamepad))
            {
                connectedGamepads.Add(gamepad);
            }

            if (CurrentGamepad == null)
            {
                SetCurrentGamepad(gamepad);
            }

            Debug.Log($"Gamepad connected: {gamepad.displayName}");
        }

        /// <summary>
        /// Handle gamepad removed event.
        /// </summary>
        /// <param name="gamepad">Removed gamepad</param>
        private void OnGamepadRemoved(Gamepad gamepad)
        {
            connectedGamepads.Remove(gamepad);

            if (CurrentGamepad == gamepad)
            {
                SetCurrentGamepad(connectedGamepads.Count > 0 ? connectedGamepads[0] : null);
            }

            Debug.Log($"Gamepad disconnected: {gamepad.displayName}");
        }

        /// <summary>
        /// Handle gamepad reconnected event.
        /// </summary>
        /// <param name="gamepad">Reconnected gamepad</param>
        private void OnGamepadReconnected(Gamepad gamepad)
        {
            Debug.Log($"Gamepad reconnected: {gamepad.displayName}");
        }

        /// <summary>
        /// Handle gamepad disconnected event.
        /// </summary>
        /// <param name="gamepad">Disconnected gamepad</param>
        private void OnGamepadDisconnected(Gamepad gamepad)
        {
            Debug.Log($"Gamepad temporarily disconnected: {gamepad.displayName}");
        }

        /// <summary>
        /// Set the current active gamepad.
        /// </summary>
        /// <param name="gamepad">Gamepad to set as current</param>
        private void SetCurrentGamepad(Gamepad gamepad)
        {
            GamepadType previousType = CurrentGamepadType;
            CurrentGamepad = gamepad;
            IsGamepadConnected = gamepad != null;

            if (gamepad != null)
            {
                GamepadName = gamepad.displayName;
                CurrentGamepadType = DetectGamepadType(gamepad);
                OnGamepadConnected?.Invoke(CurrentGamepadType);
            }
            else
            {
                GamepadName = "";
                CurrentGamepadType = GamepadType.None;
                OnGamepadDisconnected?.Invoke(previousType);
            }

            if (previousType != CurrentGamepadType)
            {
                OnGamepadChanged?.Invoke(CurrentGamepadType);
            }
        }

        /// <summary>
        /// Detect the type of gamepad based on its name and properties.
        /// </summary>
        /// <param name="gamepad">Gamepad to detect type for</param>
        /// <returns>Detected gamepad type</returns>
        private GamepadType DetectGamepadType(Gamepad gamepad)
        {
            string name = gamepad.displayName.ToLower();

            if (name.Contains("xbox") || name.Contains("microsoft"))
            {
                return GamepadType.Xbox;
            }
            else if (name.Contains("playstation") || name.Contains("dualshock") || name.Contains("dualsense") || name.Contains("sony"))
            {
                return GamepadType.PlayStation;
            }
            else
            {
                return GamepadType.Generic;
            }
        }
        #endregion

        #region Input Processing
        /// <summary>
        /// Get processed stick input with deadzone and response curve.
        /// </summary>
        /// <param name="rawInput">Raw stick input</param>
        /// <returns>Processed stick input</returns>
        public Vector2 ProcessStickInput(Vector2 rawInput)
        {
            // Apply deadzone
            if (rawInput.magnitude < stickDeadzone)
            {
                return Vector2.zero;
            }

            // Normalize and apply response curve
            Vector2 direction = rawInput.normalized;
            float magnitude = (rawInput.magnitude - stickDeadzone) / (1f - stickDeadzone);
            magnitude = stickResponseCurve.Evaluate(magnitude);

            return direction * magnitude;
        }

        /// <summary>
        /// Get processed trigger input with deadzone.
        /// </summary>
        /// <param name="rawInput">Raw trigger input</param>
        /// <returns>Processed trigger input</returns>
        public float ProcessTriggerInput(float rawInput)
        {
            if (rawInput < triggerDeadzone)
            {
                return 0f;
            }

            return (rawInput - triggerDeadzone) / (1f - triggerDeadzone);
        }
        #endregion

        #region Haptic Feedback
        /// <summary>
        /// Start rumble with specified intensities.
        /// </summary>
        /// <param name="lowFrequency">Low frequency motor intensity (0-1)</param>
        /// <param name="highFrequency">High frequency motor intensity (0-1)</param>
        /// <param name="duration">Duration in seconds</param>
        public void StartRumble(float lowFrequency, float highFrequency, float duration = -1f)
        {
            if (!enableHapticFeedback || CurrentGamepad == null) return;

            if (duration < 0f)
            {
                duration = defaultRumbleDuration;
            }

            // Clamp intensities
            lowFrequency = Mathf.Clamp01(lowFrequency) * maxRumbleIntensity;
            highFrequency = Mathf.Clamp01(highFrequency) * maxRumbleIntensity;

            // Stop existing rumble
            if (rumbleCoroutine != null)
            {
                StopCoroutine(rumbleCoroutine);
            }

            // Start new rumble
            CurrentGamepad.SetMotorSpeeds(lowFrequency, highFrequency);
            isRumbling = true;
            lastRumbleTime = Time.time;

            // Schedule rumble stop
            rumbleCoroutine = StartCoroutine(StopRumbleAfterDelay(duration));
        }

        /// <summary>
        /// Stop rumble immediately.
        /// </summary>
        public void StopRumble()
        {
            if (CurrentGamepad != null)
            {
                CurrentGamepad.SetMotorSpeeds(0f, 0f);
            }

            isRumbling = false;

            if (rumbleCoroutine != null)
            {
                StopCoroutine(rumbleCoroutine);
                rumbleCoroutine = null;
            }
        }

        /// <summary>
        /// Coroutine to stop rumble after delay.
        /// </summary>
        /// <param name="delay">Delay in seconds</param>
        /// <returns>Coroutine</returns>
        private System.Collections.IEnumerator StopRumbleAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            StopRumble();
        }

        /// <summary>
        /// Trigger haptic feedback for specific game events.
        /// </summary>
        /// <param name="eventType">Type of haptic event</param>
        public void TriggerHapticFeedback(HapticEventType eventType)
        {
            if (!enableHapticFeedback) return;

            switch (eventType)
            {
                case HapticEventType.LightHit:
                    StartRumble(0.2f, 0.3f, 0.1f);
                    break;
                case HapticEventType.MediumHit:
                    StartRumble(0.4f, 0.5f, 0.2f);
                    break;
                case HapticEventType.HeavyHit:
                    StartRumble(0.7f, 0.8f, 0.3f);
                    break;
                case HapticEventType.Heartbeat:
                    StartRumble(0.3f, 0.1f, 0.5f);
                    break;
                case HapticEventType.Explosion:
                    StartRumble(1f, 1f, 0.4f);
                    break;
                case HapticEventType.Footstep:
                    StartRumble(0.1f, 0.2f, 0.05f);
                    break;
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get button icon for current gamepad type.
        /// </summary>
        /// <param name="buttonName">Name of button</param>
        /// <returns>Button icon sprite</returns>
        public Sprite GetButtonIcon(string buttonName)
        {
            if (gamepadProfiles.ContainsKey(CurrentGamepadType))
            {
                var profile = gamepadProfiles[CurrentGamepadType];
                if (profile.ButtonIcons.ContainsKey(buttonName))
                {
                    return profile.ButtonIcons[buttonName];
                }
            }

            return null;
        }

        /// <summary>
        /// Get gamepad profile for current gamepad.
        /// </summary>
        /// <returns>Current gamepad profile</returns>
        public GamepadProfile GetCurrentProfile()
        {
            if (gamepadProfiles.ContainsKey(CurrentGamepadType))
            {
                return gamepadProfiles[CurrentGamepadType];
            }

            return null;
        }

        /// <summary>
        /// Set haptic feedback enabled state.
        /// </summary>
        /// <param name="enabled">True to enable haptic feedback</param>
        public void SetHapticFeedbackEnabled(bool enabled)
        {
            enableHapticFeedback = enabled;
            if (!enabled)
            {
                StopRumble();
            }
        }

        /// <summary>
        /// Set stick deadzone value.
        /// </summary>
        /// <param name="deadzone">Deadzone value (0-1)</param>
        public void SetStickDeadzone(float deadzone)
        {
            stickDeadzone = Mathf.Clamp01(deadzone);
        }

        /// <summary>
        /// Set trigger deadzone value.
        /// </summary>
        /// <param name="deadzone">Deadzone value (0-1)</param>
        public void SetTriggerDeadzone(float deadzone)
        {
            triggerDeadzone = Mathf.Clamp01(deadzone);
        }
        #endregion

        #region Debug
        /// <summary>
        /// Update debug information display.
        /// </summary>
        private void UpdateDebugInfo()
        {
            if (!showDebugInfo) return;

            if (CurrentGamepad != null)
            {
                // Debug display would go here
                // Could show current input values, button states, etc.
            }
        }
        #endregion

        #region Cleanup
        /// <summary>
        /// Clean up gamepad manager.
        /// </summary>
        private void OnDestroy()
        {
            InputSystem.onDeviceChange -= OnDeviceChange;
            StopRumble();
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Types of supported gamepads.
    /// </summary>
    public enum GamepadType
    {
        None,
        Xbox,
        PlayStation,
        Generic
    }

    /// <summary>
    /// Types of haptic feedback events.
    /// </summary>
    public enum HapticEventType
    {
        LightHit,
        MediumHit,
        HeavyHit,
        Heartbeat,
        Explosion,
        Footstep
    }

    /// <summary>
    /// Gamepad profile containing controller-specific information.
    /// </summary>
    [System.Serializable]
    public class GamepadProfile
    {
        public GamepadType Type;
        public string Name;
        public Dictionary<string, Sprite> ButtonIcons;
        public bool VibrationSupport;
        public bool TriggerSupport;
        public bool HasTouchpad;
    }

    /// <summary>
    /// Button mapping configuration for different gamepad types.
    /// </summary>
    [System.Serializable]
    public class GamepadButtonMap
    {
        [Header("Face Buttons")]
        public string ConfirmButton = "A"; // Xbox A, PlayStation Cross
        public string CancelButton = "B"; // Xbox B, PlayStation Circle
        public string AlternateButton = "X"; // Xbox X, PlayStation Square
        public string MenuButton = "Y"; // Xbox Y, PlayStation Triangle

        [Header("Shoulder Buttons")]
        public string LeftShoulder = "LB"; // Xbox LB, PlayStation L1
        public string RightShoulder = "RB"; // Xbox RB, PlayStation R1
        public string LeftTrigger = "LT"; // Xbox LT, PlayStation L2
        public string RightTrigger = "RT"; // Xbox RT, PlayStation R2

        [Header("Stick Buttons")]
        public string LeftStickButton = "LS"; // Xbox LS, PlayStation L3
        public string RightStickButton = "RS"; // Xbox RS, PlayStation R3

        [Header("System Buttons")]
        public string StartButton = "Menu"; // Xbox Menu, PlayStation Options
        public string SelectButton = "View"; // Xbox View, PlayStation Share/Touchpad
    }
    #endregion
}
