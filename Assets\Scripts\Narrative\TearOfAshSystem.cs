using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class TearOfAshSystem : MonoBehaviour
{
    [Header("The Tear of Ash")]
    public bool tearOfAshObtained = false;
    public GameObject tearOfAshRelic;
    public TearOfAshData tearData;
    
    [Header("The Four Great Sages")]
    public GreatSage[] fourSages;
    public int sagesDefeated = 0;
    public List<string> defeatedSageNames = new List<string>();
    
    [Header("Summit Access")]
    public bool summitUnlocked = false;
    public GameObject summitGate;
    public Transform summitLocation;
    
    [Header("Philosophical Integration")]
    public float playerPhilosophicalGrowth = 0f;
    public Dictionary<string, float> philosophicalLessons = new Dictionary<string, float>();
    
    private PhilosophicalMoralitySystem moralitySystem;
    private PsychologicalSystem psycheSystem;
    private OrphanedChildCompanion childCompanion;
    private GameManager gameManager;
    
    [System.Serializable]
    public class TearOfAshData
    {
        [Header("Symbolic Meaning")]
        public string relicName = "The Tear of Ash";
        public string symbolicDescription;
        public string loreDescription;
        public PhilosophicalTheme primaryTheme;
        
        [Header("Power")]
        public float relicPower = 1000f;
        public string[] grantedAbilities;
        public string[] unlockedDialogues;
        public string[] revealedTruths;
        
        [Header("Visual")]
        public GameObject relicModel;
        public ParticleSystem ashAura;
        public AudioClip resonanceSound;
        public Color tearColor;
        
        public enum PhilosophicalTheme
        {
            Identity,
            Transformation,
            Sacrifice,
            Redemption,
            Power,
            Sorrow,
            Rebirth
        }
    }
    
    [System.Serializable]
    public class GreatSage
    {
        [Header("Identity")]
        public string sageName;
        public string title;
        public SagePhilosophy philosophy;
        public string philosophicalQuote;
        
        [Header("Location & Encounter")]
        public string regionName;
        public Vector3 sageLocation;
        public GameObject sagePrefab;
        public bool isDefeated = false;
        
        [Header("Boss Encounter")]
        public float sageHealth = 1000f;
        public string[] specialAttacks;
        public BossPhase[] encounterPhases;
        public string defeatDialogue;
        
        [Header("Philosophical Challenge")]
        public PhilosophicalTest philosophicalTest;
        public string[] wisdomQuotes;
        public string lessonTaught;
        public float philosophicalWeight;
        
        [Header("Rewards")]
        public string sageFragment; // Part of the Tear of Ash
        public string[] unlockedWisdom;
        public float powerGranted;
        
        public enum SagePhilosophy
        {
            Sacrifice,   // The Sage of Sacrifice
            Judgment,    // The Sage of Judgment  
            Rage,        // The Sage of Rage
            Silence      // The Sage of Silence
        }
    }
    
    [System.Serializable]
    public class BossPhase
    {
        public string phaseName;
        public float healthThreshold;
        public string phaseDialogue;
        public string[] phaseAttacks;
        public PhilosophicalChallenge challenge;
    }
    
    [System.Serializable]
    public class PhilosophicalTest
    {
        public string testName;
        public string testDescription;
        public TestType type;
        public string[] possibleResponses;
        public float[] responseWeights;
        
        public enum TestType
        {
            MoralDilemma,
            PhilosophicalQuestion,
            ActionTest,
            SacrificeChoice,
            JudgmentCall
        }
    }
    
    [System.Serializable]
    public class PhilosophicalChallenge
    {
        public string challengeText;
        public string[] playerOptions;
        public float[] moralWeights;
        public string correctPhilosophy;
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        childCompanion = GetComponent<OrphanedChildCompanion>();
        gameManager = GameManager.Instance;
        
        InitializeTearOfAshSystem();
        InitializeFourSages();
    }
    
    void Update()
    {
        CheckSageEncounterTriggers();
        UpdatePhilosophicalGrowth();
        CheckTearOfAshUnlock();
    }
    
    void InitializeTearOfAshSystem()
    {
        // Initialize the Tear of Ash data
        tearData = new TearOfAshData
        {
            relicName = "The Tear of Ash",
            symbolicDescription = "The crystallized essence of The Cinderborn's journey - every choice, every sorrow, every moment of growth condensed into a single, perfect tear.",
            loreDescription = "Born from the first flame and tempered by countless sorrows, the Tear of Ash represents the ultimate transformation of pain into power, of loss into wisdom. It is said that only one who has truly understood the weight of their choices can claim it.",
            primaryTheme = TearOfAshData.PhilosophicalTheme.Transformation,
            grantedAbilities = new string[]
            {
                "True Sight - See the moral weight of all choices",
                "Ash Form - Become incorporeal for brief moments", 
                "Memory Echo - Witness the past of any location",
                "Soul Resonance - Understand the true nature of any being"
            },
            unlockedDialogues = new string[]
            {
                "PhilosophicalMastery",
                "SageWisdom", 
                "TrueIdentity",
                "CosmicUnderstanding"
            },
            revealedTruths = new string[]
            {
                "The true nature of The Cinderborn's origin",
                "The connection between all living things",
                "The purpose of suffering in growth",
                "The path to transcendence"
            },
            tearColor = new Color(0.8f, 0.8f, 0.9f, 0.7f)
        };
        
        Debug.Log("The Tear of Ash system initialized - seek the Four Great Sages to claim your destiny");
    }
    
    void InitializeFourSages()
    {
        // Sage of Sacrifice
        GreatSage sageOfSacrifice = new GreatSage
        {
            sageName = "Altruon the Giver",
            title = "The Sage of Sacrifice",
            philosophy = GreatSage.SagePhilosophy.Sacrifice,
            philosophicalQuote = "True strength is found not in what you can take, but in what you are willing to give. The greatest power comes from the greatest sacrifice.",
            regionName = "The Weeping Peaks",
            sageLocation = new Vector3(-200f, 100f, 0f),
            sageHealth = 800f,
            specialAttacks = new string[] { "Sacrificial Flame", "Altruistic Shield", "Giving Aura" },
            defeatDialogue = "You have learned... that to gain everything, one must be willing to lose everything. Take my fragment, and remember this lesson.",
            lessonTaught = "The power of selfless sacrifice",
            philosophicalWeight = 25f,
            sageFragment = "Fragment of Sacrifice",
            unlockedWisdom = new string[] { "True sacrifice requires no recognition", "The giver receives more than the taker" },
            powerGranted = 250f
        };
        
        // Sage of Judgment
        GreatSage sageOfJudgment = new GreatSage
        {
            sageName = "Themis the Weigher",
            title = "The Sage of Judgment", 
            philosophy = GreatSage.SagePhilosophy.Judgment,
            philosophicalQuote = "Every choice carries weight, every action has consequence. To judge others, one must first judge oneself with perfect honesty.",
            regionName = "The Scales of Truth",
            sageLocation = new Vector3(200f, 50f, 100f),
            sageHealth = 900f,
            specialAttacks = new string[] { "Scales of Justice", "Truth Revelation", "Moral Weight" },
            defeatDialogue = "You have faced the mirror of judgment and emerged with understanding. Justice is not punishment, but balance.",
            lessonTaught = "The nature of true judgment",
            philosophicalWeight = 25f,
            sageFragment = "Fragment of Judgment",
            unlockedWisdom = new string[] { "Judge not the action, but the heart behind it", "True justice heals rather than harms" },
            powerGranted = 250f
        };
        
        // Sage of Rage
        GreatSage sageOfRage = new GreatSage
        {
            sageName = "Fury the Untamed",
            title = "The Sage of Rage",
            philosophy = GreatSage.SagePhilosophy.Rage,
            philosophicalQuote = "Rage is not evil - it is passion without direction. Channel your fury, and it becomes the fire that forges greatness.",
            regionName = "The Burning Wastes",
            sageLocation = new Vector3(0f, 0f, -200f),
            sageHealth = 1200f,
            specialAttacks = new string[] { "Righteous Fury", "Berserker's Wisdom", "Controlled Chaos" },
            defeatDialogue = "Your rage burns bright, but it burns clean. You have learned to be angry without being consumed. This is true power.",
            lessonTaught = "The constructive power of controlled rage",
            philosophicalWeight = 25f,
            sageFragment = "Fragment of Rage",
            unlockedWisdom = new string[] { "Anger without purpose is destruction", "Righteous fury can heal the world" },
            powerGranted = 250f
        };
        
        // Sage of Silence
        GreatSage sageOfSilence = new GreatSage
        {
            sageName = "Quietus the Void",
            title = "The Sage of Silence",
            philosophy = GreatSage.SagePhilosophy.Silence,
            philosophicalQuote = "In silence, all truths are heard. In stillness, all movement finds meaning. The deepest wisdom speaks without words.",
            regionName = "The Soundless Valley",
            sageLocation = new Vector3(100f, 0f, 200f),
            sageHealth = 1000f,
            specialAttacks = new string[] { "Void Embrace", "Silent Strike", "Wordless Wisdom" },
            defeatDialogue = "...", // The Sage of Silence speaks only through action
            lessonTaught = "The profound power of silence and stillness",
            philosophicalWeight = 25f,
            sageFragment = "Fragment of Silence",
            unlockedWisdom = new string[] { "The loudest truths are spoken in silence", "In emptiness, all possibilities exist" },
            powerGranted = 250f
        };
        
        fourSages = new GreatSage[] { sageOfSacrifice, sageOfJudgment, sageOfRage, sageOfSilence };
        
        // Initialize philosophical lessons tracking
        philosophicalLessons["Sacrifice"] = 0f;
        philosophicalLessons["Judgment"] = 0f;
        philosophicalLessons["Rage"] = 0f;
        philosophicalLessons["Silence"] = 0f;
        
        Debug.Log("The Four Great Sages await - each guards a fragment of the Tear of Ash");
    }
    
    void CheckSageEncounterTriggers()
    {
        foreach (GreatSage sage in fourSages)
        {
            if (!sage.isDefeated && !defeatedSageNames.Contains(sage.sageName))
            {
                float distance = Vector3.Distance(transform.position, sage.sageLocation);
                if (distance < 10f)
                {
                    TriggerSageEncounter(sage);
                }
            }
        }
    }
    
    void TriggerSageEncounter(GreatSage sage)
    {
        StartCoroutine(SageEncounterSequence(sage));
    }
    
    IEnumerator SageEncounterSequence(GreatSage sage)
    {
        ShowTearMessage($"You approach {sage.sageName}, {sage.title}...");
        yield return new WaitForSeconds(2f);
        
        ShowTearMessage($"{sage.sageName}: \"{sage.philosophicalQuote}\"");
        yield return new WaitForSeconds(4f);
        
        // Present philosophical test before combat
        yield return StartCoroutine(PhilosophicalTestSequence(sage));
        
        // Begin boss encounter
        yield return StartCoroutine(SageBossEncounter(sage));
    }
    
    IEnumerator PhilosophicalTestSequence(GreatSage sage)
    {
        ShowTearMessage($"{sage.sageName} presents you with a philosophical challenge...");
        yield return new WaitForSeconds(2f);
        
        // Present test based on sage's philosophy
        string testQuestion = GeneratePhilosophicalTest(sage);
        ShowTearMessage(testQuestion);
        yield return new WaitForSeconds(5f);
        
        // Player's response affects the upcoming battle and rewards
        float philosophicalAlignment = EvaluatePlayerPhilosophy(sage);
        
        if (philosophicalAlignment > 0.7f)
        {
            ShowTearMessage($"{sage.sageName}: \"You understand. The battle ahead will test your conviction.\"");
            // Easier boss fight, better rewards
        }
        else if (philosophicalAlignment < 0.3f)
        {
            ShowTearMessage($"{sage.sageName}: \"You have much to learn. Let this battle be your teacher.\"");
            // Harder boss fight, but still valuable lessons
        }
        else
        {
            ShowTearMessage($"{sage.sageName}: \"Your understanding grows. Prove your wisdom in combat.\"");
            // Standard boss fight
        }
        
        yield return new WaitForSeconds(2f);
    }
    
    string GeneratePhilosophicalTest(GreatSage sage)
    {
        switch (sage.philosophy)
        {
            case GreatSage.SagePhilosophy.Sacrifice:
                return "Tell me, Cinderborn: What would you sacrifice to save a stranger? Your power? Your memories? Your very identity?";
            
            case GreatSage.SagePhilosophy.Judgment:
                return "A child steals bread to feed their starving family. A merchant loses his livelihood. Who is right? Who is wrong? How do you judge?";
            
            case GreatSage.SagePhilosophy.Rage:
                return "Your anger burns bright when you see injustice. Is this rage a weakness to overcome, or a strength to embrace? Why?";
            
            case GreatSage.SagePhilosophy.Silence:
                return "In a world of noise and chaos, what wisdom can be found in silence? What truths speak without words?";
            
            default:
                return "What defines you, Cinderborn? Your actions, your intentions, or your consequences?";
        }
    }
    
    float EvaluatePlayerPhilosophy(GreatSage sage)
    {
        // Evaluate based on player's past actions and current moral state
        float alignment = 0.5f; // Base alignment
        
        if (moralitySystem != null)
        {
            PhilosophicalMoralitySystem.MoralPath playerPath = moralitySystem.GetCurrentPath();
            
            switch (sage.philosophy)
            {
                case GreatSage.SagePhilosophy.Sacrifice:
                    // Good path aligns with sacrifice
                    if (playerPath == PhilosophicalMoralitySystem.MoralPath.Good)
                        alignment += 0.3f;
                    break;
                
                case GreatSage.SagePhilosophy.Judgment:
                    // Balanced path aligns with judgment
                    if (playerPath == PhilosophicalMoralitySystem.MoralPath.Eclipse)
                        alignment += 0.3f;
                    break;
                
                case GreatSage.SagePhilosophy.Rage:
                    // Any strong conviction aligns with rage
                    if (playerPath != PhilosophicalMoralitySystem.MoralPath.Neutral)
                        alignment += 0.2f;
                    break;
                
                case GreatSage.SagePhilosophy.Silence:
                    // Wisdom and contemplation align with silence
                    if (psycheSystem != null && psycheSystem.enlightenment > 50f)
                        alignment += 0.3f;
                    break;
            }
        }
        
        // Factor in child companion's development
        if (childCompanion != null)
        {
            float bondLevel = childCompanion.GetBondLevel();
            if (bondLevel > 50f) // Good relationship with child shows wisdom
                alignment += 0.2f;
        }
        
        return Mathf.Clamp01(alignment);
    }
    
    IEnumerator SageBossEncounter(GreatSage sage)
    {
        ShowTearMessage($"The battle with {sage.sageName} begins!");
        yield return new WaitForSeconds(1f);
        
        // Simulate boss battle phases
        for (int phase = 0; phase < 3; phase++)
        {
            yield return StartCoroutine(SageBattlePhase(sage, phase));
        }
        
        // Sage defeated
        OnSageDefeated(sage);
    }
    
    IEnumerator SageBattlePhase(GreatSage sage, int phaseNumber)
    {
        string[] phaseDialogues = GetSagePhaseDialogue(sage, phaseNumber);
        
        foreach (string dialogue in phaseDialogues)
        {
            ShowTearMessage($"{sage.sageName}: \"{dialogue}\"");
            yield return new WaitForSeconds(3f);
        }
        
        // Simulate combat phase
        ShowTearMessage($"Phase {phaseNumber + 1} of the battle rages on...");
        yield return new WaitForSeconds(2f);
    }
    
    string[] GetSagePhaseDialogue(GreatSage sage, int phase)
    {
        switch (sage.philosophy)
        {
            case GreatSage.SagePhilosophy.Sacrifice:
                return new string[]
                {
                    "What are you willing to lose to gain wisdom?",
                    "True power comes from what you give, not what you take!",
                    "In your final moment, what will you sacrifice for victory?"
                };
            
            case GreatSage.SagePhilosophy.Judgment:
                return new string[]
                {
                    "Every strike you make is a judgment upon yourself!",
                    "Can you judge your own actions as harshly as you judge others?",
                    "In this moment, who are you truly fighting - me, or yourself?"
                };
            
            case GreatSage.SagePhilosophy.Rage:
                return new string[]
                {
                    "Let your fury burn bright, but let it burn clean!",
                    "Rage without purpose is mere destruction!",
                    "Show me the fire that forges, not the flame that consumes!"
                };
            
            case GreatSage.SagePhilosophy.Silence:
                return new string[]
                {
                    "...", // Silent attacks
                    "Listen to what your heart speaks without words...",
                    "In the silence between strikes, find your truth..."
                };
            
            default:
                return new string[] { "Fight with wisdom, Cinderborn!" };
        }
    }
    
    void OnSageDefeated(GreatSage sage)
    {
        sage.isDefeated = true;
        defeatedSageNames.Add(sage.sageName);
        sagesDefeated++;
        
        // Award sage fragment
        ShowTearMessage($"{sage.sageName}: \"{sage.defeatDialogue}\"");
        
        // Grant philosophical lesson
        philosophicalLessons[sage.philosophy.ToString()] = sage.philosophicalWeight;
        playerPhilosophicalGrowth += sage.philosophicalWeight;
        
        // Grant power
        if (psycheSystem != null)
        {
            psycheSystem.enlightenment += sage.powerGranted / 10f; // Convert to enlightenment points
        }
        
        ShowTearMessage($"You have gained the {sage.sageFragment} and learned: {sage.lessonTaught}");
        
        // Check if all sages defeated
        if (sagesDefeated >= 4)
        {
            UnlockTearOfAsh();
        }
        
        Debug.Log($"Sage {sage.sageName} defeated. Fragments collected: {sagesDefeated}/4");
    }
    
    void UpdatePhilosophicalGrowth()
    {
        // Philosophical growth affects player's understanding and dialogue options
        if (playerPhilosophicalGrowth >= 100f && !tearOfAshObtained)
        {
            // Player has achieved philosophical mastery
            ShowTearMessage("You feel a profound understanding wash over you. The wisdom of the ages flows through your consciousness.");
        }
    }
    
    void CheckTearOfAshUnlock()
    {
        if (sagesDefeated >= 4 && !tearOfAshObtained)
        {
            UnlockTearOfAsh();
        }
    }
    
    void UnlockTearOfAsh()
    {
        tearOfAshObtained = true;
        
        StartCoroutine(TearOfAshUnlockSequence());
    }
    
    IEnumerator TearOfAshUnlockSequence()
    {
        // Dramatic pause
        Time.timeScale = 0.1f;
        
        ShowTearMessage("The four fragments resonate with each other, their combined wisdom creating something greater...");
        yield return new WaitForSecondsRealtime(3f);
        
        ShowTearMessage("THE TEAR OF ASH MANIFESTS");
        yield return new WaitForSecondsRealtime(2f);
        
        ShowTearMessage(tearData.symbolicDescription);
        yield return new WaitForSecondsRealtime(4f);
        
        ShowTearMessage("You feel the weight of every choice, every sorrow, every moment of growth crystallized into perfect understanding.");
        yield return new WaitForSecondsRealtime(3f);
        
        // Return to normal time
        Time.timeScale = 1f;
        
        // Unlock summit
        UnlockSummit();
        
        // Grant abilities
        GrantTearOfAshAbilities();
        
        Debug.Log("The Tear of Ash has been obtained! The summit awaits!");
    }
    
    void UnlockSummit()
    {
        summitUnlocked = true;
        
        if (summitGate != null)
        {
            summitGate.SetActive(false); // Open the gate
        }
        
        ShowTearMessage("The path to the summit is now open. Your final destiny awaits at the peak of the mountain.");
        
        // Notify game manager
        if (gameManager != null)
        {
            gameManager.UnlockLocation("Summit of Convergence");
        }
    }
    
    void GrantTearOfAshAbilities()
    {
        // Grant the abilities listed in tearData
        foreach (string ability in tearData.grantedAbilities)
        {
            ShowTearMessage($"Ability Unlocked: {ability}");
        }
        
        // Unlock special dialogues
        foreach (string dialogue in tearData.unlockedDialogues)
        {
            gameManager?.UnlockDialogueOption(dialogue);
        }
        
        // Reveal cosmic truths
        foreach (string truth in tearData.revealedTruths)
        {
            ShowTearMessage($"Truth Revealed: {truth}");
        }
    }
    
    void ShowTearMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 5f));
        }
        
        Debug.Log($"Tear of Ash: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSecondsRealtime(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public bool IsTearOfAshObtained() => tearOfAshObtained;
    public int GetSagesDefeated() => sagesDefeated;
    public bool IsSummitUnlocked() => summitUnlocked;
    public float GetPhilosophicalGrowth() => playerPhilosophicalGrowth;
    public Dictionary<string, float> GetPhilosophicalLessons() => philosophicalLessons;
    public GreatSage[] GetFourSages() => fourSages;
    public TearOfAshData GetTearData() => tearData;
}
