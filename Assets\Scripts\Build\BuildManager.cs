using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace CinderOfDarkness.Build
{
    /// <summary>
    /// Build Manager for Cinder of Darkness.
    /// Handles automated building, optimization, and deployment preparation.
    /// </summary>
    public class BuildManager : MonoBehaviour
    {
#if UNITY_EDITOR
        #region Build Settings
        private static readonly string[] ScenesToBuild = {
            "Assets/Scenes/MainMenu.unity",
            "Assets/Scenes/GameScene.unity",
            "Assets/Scenes/DemoScene.unity"
        };

        private static readonly string BuildPath = "Builds";
        private static readonly string GameName = "CinderOfDarkness";
        private static readonly string CompanyName = "Augment Code";
        private static readonly string GameVersion = "1.0.0";
        #endregion

        #region Menu Items
        [MenuItem("Cinder of Darkness/Build/Build Windows")]
        public static void BuildWindows()
        {
            BuildGame(BuildTarget.StandaloneWindows64, "Windows");
        }

        [MenuItem("Cinder of Darkness/Build/Build Mac")]
        public static void BuildMac()
        {
            BuildGame(BuildTarget.StandaloneOSX, "Mac");
        }

        [MenuItem("Cinder of Darkness/Build/Build Linux")]
        public static void BuildLinux()
        {
            BuildGame(BuildTarget.StandaloneLinux64, "Linux");
        }

        [MenuItem("Cinder of Darkness/Build/Build All Platforms")]
        public static void BuildAllPlatforms()
        {
            BuildWindows();
            BuildMac();
            BuildLinux();
        }

        [MenuItem("Cinder of Darkness/Build/Prepare for Release")]
        public static void PrepareForRelease()
        {
            Debug.Log("Preparing Cinder of Darkness for release...");
            
            OptimizeProject();
            ValidateProject();
            CleanupProject();
            
            Debug.Log("Release preparation complete!");
        }

        [MenuItem("Cinder of Darkness/Build/Validate Project")]
        public static void ValidateProject()
        {
            Debug.Log("Validating Cinder of Darkness project...");
            
            ValidateScenes();
            ValidateAssets();
            ValidateSystems();
            ValidateSettings();
            
            Debug.Log("Project validation complete!");
        }
        #endregion

        #region Build Process
        private static void BuildGame(BuildTarget target, string platformName)
        {
            Debug.Log($"Building Cinder of Darkness for {platformName}...");
            
            // Setup build settings
            SetupBuildSettings();
            
            // Create build directory
            string buildDir = Path.Combine(BuildPath, platformName);
            if (!Directory.Exists(buildDir))
            {
                Directory.CreateDirectory(buildDir);
            }
            
            // Determine file extension
            string extension = GetExecutableExtension(target);
            string buildPath = Path.Combine(buildDir, $"{GameName}{extension}");
            
            // Configure build options
            BuildPlayerOptions buildOptions = new BuildPlayerOptions
            {
                scenes = ScenesToBuild,
                locationPathName = buildPath,
                target = target,
                options = BuildOptions.None
            };
            
            // Optimize for release
            if (EditorUserBuildSettings.development == false)
            {
                buildOptions.options |= BuildOptions.CompressWithLz4HC;
            }
            
            // Build the game
            var report = BuildPipeline.BuildPlayer(buildOptions);
            
            if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"Build succeeded for {platformName}!");
                Debug.Log($"Build size: {report.summary.totalSize} bytes");
                Debug.Log($"Build time: {report.summary.totalTime}");
                
                // Copy additional files
                CopyAdditionalFiles(buildDir);
                
                // Create readme
                CreateReadmeFile(buildDir);
            }
            else
            {
                Debug.LogError($"Build failed for {platformName}!");
                foreach (var step in report.steps)
                {
                    if (step.messages.Length > 0)
                    {
                        foreach (var message in step.messages)
                        {
                            Debug.LogError($"Build error: {message.content}");
                        }
                    }
                }
            }
        }

        private static void SetupBuildSettings()
        {
            // Set player settings
            PlayerSettings.companyName = CompanyName;
            PlayerSettings.productName = GameName;
            PlayerSettings.bundleVersion = GameVersion;
            
            // Graphics settings
            PlayerSettings.colorSpace = ColorSpace.Linear;
            PlayerSettings.gpuSkinning = true;
            
            // Quality settings
            QualitySettings.vSyncCount = 1;
            QualitySettings.antiAliasing = 4;
            
            // Input settings
            PlayerSettings.defaultInterfaceOrientation = UIOrientation.LandscapeLeft;
            
            Debug.Log("Build settings configured");
        }

        private static string GetExecutableExtension(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return ".exe";
                case BuildTarget.StandaloneOSX:
                    return ".app";
                case BuildTarget.StandaloneLinux64:
                    return "";
                default:
                    return "";
            }
        }

        private static void CopyAdditionalFiles(string buildDir)
        {
            // Copy mod folder structure
            string modsDir = Path.Combine(buildDir, "Mods");
            if (!Directory.Exists(modsDir))
            {
                Directory.CreateDirectory(modsDir);
            }
            
            // Copy localization files
            string locDir = Path.Combine(buildDir, "Localization");
            if (!Directory.Exists(locDir))
            {
                Directory.CreateDirectory(locDir);
            }
            
            // Copy save folder structure
            string savesDir = Path.Combine(buildDir, "Saves");
            if (!Directory.Exists(savesDir))
            {
                Directory.CreateDirectory(savesDir);
            }
            
            Debug.Log("Additional files copied");
        }

        private static void CreateReadmeFile(string buildDir)
        {
            string readmePath = Path.Combine(buildDir, "README.txt");
            
            string readmeContent = $@"CINDER OF DARKNESS v{GameVersion}
========================================

Thank you for playing Cinder of Darkness!

SYSTEM REQUIREMENTS:
- OS: Windows 10 64-bit / macOS 10.14+ / Ubuntu 18.04+
- Processor: Intel Core i5-4590 / AMD FX 8350 equivalent
- Memory: 8 GB RAM
- Graphics: NVIDIA GTX 970 / AMD R9 280 equivalent
- DirectX: Version 11
- Storage: 15 GB available space

CONTROLS:
- WASD: Move
- Mouse: Look around
- Left Click: Attack
- Right Click: Cast Magic
- E: Interact
- I: Inventory
- M: Map
- J: Quest Log
- K: Magic Tree
- Ctrl: Crouch/Stealth
- Shift: Run
- Space: Jump
- ESC: Pause Menu
- F10: Mod Manager
- F11: Sandbox Mode

FEATURES:
✓ Dynamic Narrative System with branching storylines
✓ Reactive AI that adapts to your playstyle
✓ Magic Evolution System with spell progression
✓ Economy & Trading with multiple currencies
✓ Advanced Dialogue with trait-based options
✓ Stealth & Assassination mechanics
✓ Interactive World Map with exploration
✓ Dynamic Time System with day-night cycles
✓ Flashback & Memory sequences
✓ Comprehensive Modding Support

MODDING:
Place mods in the 'Mods' folder and enable them through the in-game Mod Manager (F10).
Sandbox mode (F11) allows safe testing of mods without affecting your main save.

LANGUAGE SUPPORT:
- English (Default)
- Arabic (RTL Support)

TROUBLESHOOTING:
- If the game doesn't start, try running as administrator
- For graphics issues, update your GPU drivers
- For audio problems, check Windows audio settings
- Save files are located in: %USERPROFILE%/AppData/LocalLow/{CompanyName}/{GameName}/Saves

SUPPORT:
For technical support and community discussions, visit our forums.

© 2024 {CompanyName}. All rights reserved.
";

            File.WriteAllText(readmePath, readmeContent);
            Debug.Log("README file created");
        }
        #endregion

        #region Optimization
        private static void OptimizeProject()
        {
            Debug.Log("Optimizing project for release...");
            
            // Optimize textures
            OptimizeTextures();
            
            // Optimize audio
            OptimizeAudio();
            
            // Optimize scripts
            OptimizeScripts();
            
            // Clean up unused assets
            CleanupUnusedAssets();
            
            Debug.Log("Project optimization complete");
        }

        private static void OptimizeTextures()
        {
            string[] textureGuids = AssetDatabase.FindAssets("t:Texture2D");
            
            foreach (string guid in textureGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                
                if (importer != null)
                {
                    // Set compression settings
                    importer.textureCompression = TextureImporterCompression.Compressed;
                    importer.crunchedCompression = true;
                    importer.compressionQuality = 80;
                    
                    // Set max texture size
                    importer.maxTextureSize = 2048;
                    
                    AssetDatabase.ImportAsset(path);
                }
            }
            
            Debug.Log("Textures optimized");
        }

        private static void OptimizeAudio()
        {
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            
            foreach (string guid in audioGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
                
                if (importer != null)
                {
                    // Set compression settings
                    var settings = importer.defaultSampleSettings;
                    settings.compressionFormat = AudioCompressionFormat.Vorbis;
                    settings.quality = 0.7f;
                    importer.defaultSampleSettings = settings;
                    
                    AssetDatabase.ImportAsset(path);
                }
            }
            
            Debug.Log("Audio optimized");
        }

        private static void OptimizeScripts()
        {
            // Remove debug code in release builds
            PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Standalone, "RELEASE_BUILD");
            
            Debug.Log("Scripts optimized");
        }

        private static void CleanupUnusedAssets()
        {
            // This would identify and optionally remove unused assets
            // For safety, we'll just log what could be cleaned up
            
            string[] allAssets = AssetDatabase.GetAllAssetPaths();
            List<string> unusedAssets = new List<string>();
            
            foreach (string asset in allAssets)
            {
                if (asset.StartsWith("Assets/") && !IsAssetReferenced(asset))
                {
                    unusedAssets.Add(asset);
                }
            }
            
            Debug.Log($"Found {unusedAssets.Count} potentially unused assets");
        }

        private static bool IsAssetReferenced(string assetPath)
        {
            // Simple check - in a real implementation, this would be more thorough
            string[] dependencies = AssetDatabase.GetDependencies(assetPath, true);
            return dependencies.Length > 1; // More than just itself
        }
        #endregion

        #region Validation
        private static void ValidateScenes()
        {
            foreach (string scenePath in ScenesToBuild)
            {
                if (!File.Exists(scenePath))
                {
                    Debug.LogError($"Scene not found: {scenePath}");
                }
                else
                {
                    Debug.Log($"Scene validated: {scenePath}");
                }
            }
        }

        private static void ValidateAssets()
        {
            // Check for missing assets
            string[] allAssets = AssetDatabase.GetAllAssetPaths();
            int missingCount = 0;
            
            foreach (string asset in allAssets)
            {
                if (asset.StartsWith("Assets/") && !File.Exists(asset))
                {
                    Debug.LogError($"Missing asset: {asset}");
                    missingCount++;
                }
            }
            
            if (missingCount == 0)
            {
                Debug.Log("All assets validated successfully");
            }
            else
            {
                Debug.LogError($"Found {missingCount} missing assets");
            }
        }

        private static void ValidateSystems()
        {
            // Check if all core systems are present
            string[] requiredSystems = {
                "DynamicNarrativeSystem",
                "ReactiveAISystem",
                "MagicEvolutionSystem",
                "EconomySystem",
                "AdvancedDialogueSystem",
                "StealthSystem",
                "WorldMapSystem",
                "DynamicTimeSystem",
                "FlashbackSystem",
                "ModdingSystem"
            };
            
            foreach (string systemName in requiredSystems)
            {
                var systemType = System.Type.GetType($"CinderOfDarkness.{systemName}");
                if (systemType == null)
                {
                    Debug.LogError($"System not found: {systemName}");
                }
                else
                {
                    Debug.Log($"System validated: {systemName}");
                }
            }
        }

        private static void ValidateSettings()
        {
            // Validate player settings
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                Debug.LogError("Company name not set");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.productName))
            {
                Debug.LogError("Product name not set");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.bundleVersion))
            {
                Debug.LogError("Bundle version not set");
            }
            
            Debug.Log("Settings validated");
        }

        private static void CleanupProject()
        {
            // Remove temporary files
            if (Directory.Exists("Temp"))
            {
                Directory.Delete("Temp", true);
            }
            
            // Clear console
            var logEntries = System.Type.GetType("UnityEditor.LogEntries,UnityEditor.dll");
            var clearMethod = logEntries.GetMethod("Clear", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public);
            clearMethod.Invoke(null, null);
            
            Debug.Log("Project cleanup complete");
        }
        #endregion

        #region Utility
        [MenuItem("Cinder of Darkness/Tools/Open Build Folder")]
        public static void OpenBuildFolder()
        {
            if (Directory.Exists(BuildPath))
            {
                EditorUtility.RevealInFinder(BuildPath);
            }
            else
            {
                Debug.LogWarning("Build folder does not exist. Build the game first.");
            }
        }

        [MenuItem("Cinder of Darkness/Tools/Open Persistent Data")]
        public static void OpenPersistentData()
        {
            EditorUtility.RevealInFinder(Application.persistentDataPath);
        }

        [MenuItem("Cinder of Darkness/Tools/Clear Player Prefs")]
        public static void ClearPlayerPrefs()
        {
            PlayerPrefs.DeleteAll();
            Debug.Log("PlayerPrefs cleared");
        }
        #endregion
#endif
    }
}
