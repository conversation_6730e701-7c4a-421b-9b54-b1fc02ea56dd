using UnityEngine;
using CinderOfDarkness.AI;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// NPC Controller that reacts to player reputation and narrative choices.
    /// Integrates with the Reactive AI System for dynamic behavior.
    /// </summary>
    public class NPCController : MonoBehaviour
    {
        #region Serialized Fields
        [Header("NPC Settings")]
        [SerializeField] private string npcId;
        [SerializeField] private string npcName = "NPC";
        [SerializeField] private string factionId = "";
        [SerializeField] private NPCType npcType = NPCType.Civilian;

        [Header("Reaction Settings")]
        [SerializeField] private bool enableReputationReactions = true;
        [SerializeField] private float reputationSensitivity = 1f;
        [SerializeField] private float reactionUpdateInterval = 2f;

        [Header("Behavior Settings")]
        [SerializeField] private float movementSpeed = 2f;
        [SerializeField] private float interactionRange = 3f;
        [SerializeField] private bool canMove = true;
        [SerializeField] private Transform[] patrolPoints;

        [Header("Dialogue Settings")]
        [SerializeField] private bool hasDialogue = true;
        [SerializeField] private string[] neutralDialogue;
        [SerializeField] private string[] friendlyDialogue;
        [SerializeField] private string[] hostileDialogue;
        [SerializeField] private string[] fearfulDialogue;

        [Header("Visual Feedback")]
        [SerializeField] private GameObject friendlyIndicator;
        [SerializeField] private GameObject hostileIndicator;
        [SerializeField] private GameObject fearfulIndicator;
        [SerializeField] private Renderer npcRenderer;
        [SerializeField] private Color neutralColor = Color.white;
        [SerializeField] private Color friendlyColor = Color.green;
        [SerializeField] private Color hostileColor = Color.red;
        [SerializeField] private Color fearfulColor = Color.yellow;
        #endregion

        #region Public Properties
        public string NPCId => npcId;
        public string NPCName => npcName;
        public string FactionId => factionId;
        public NPCReactionType CurrentReaction { get; private set; } = NPCReactionType.Neutral;
        public bool IsInteractable { get; private set; } = true;
        #endregion

        #region Private Fields
        private ReactiveAISystem reactiveAI;
        private DynamicNarrativeSystem narrativeSystem;
        private Animator animator;
        private Transform player;
        
        // Movement and patrol
        private int currentPatrolIndex = 0;
        private bool isPatrolling = false;
        private Vector3 originalPosition;
        
        // Reaction tracking
        private float lastReactionUpdate;
        private NPCReactionType previousReaction;
        
        // Interaction
        private bool playerInRange = false;
        private float lastInteractionTime;
        #endregion

        #region Events
        public System.Action<NPCController, NPCReactionType> OnReactionChanged;
        public System.Action<NPCController> OnInteractionStarted;
        public System.Action<NPCController> OnInteractionEnded;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (string.IsNullOrEmpty(npcId))
            {
                npcId = $"npc_{GetInstanceID()}";
            }
            
            animator = GetComponent<Animator>();
            originalPosition = transform.position;
        }

        private void Start()
        {
            reactiveAI = ReactiveAISystem.Instance;
            narrativeSystem = DynamicNarrativeSystem.Instance;
            
            if (reactiveAI != null)
            {
                reactiveAI.OnNPCReactionChanged += OnNPCReactionChanged;
            }
            
            SetupPatrol();
            UpdateVisualFeedback();
        }

        private void Update()
        {
            UpdatePlayerDetection();
            UpdateReactionSystem();
            UpdateMovement();
            UpdateAnimations();
        }

        private void OnDestroy()
        {
            if (reactiveAI != null)
            {
                reactiveAI.OnNPCReactionChanged -= OnNPCReactionChanged;
            }
        }
        #endregion

        #region Reaction System
        /// <summary>
        /// Update NPC reaction based on player reputation and narrative state.
        /// </summary>
        private void UpdateReactionSystem()
        {
            if (!enableReputationReactions) return;
            if (Time.time - lastReactionUpdate < reactionUpdateInterval) return;

            NPCReactionType newReaction = CalculateReaction();
            
            if (newReaction != CurrentReaction)
            {
                ChangeReaction(newReaction);
            }
            
            lastReactionUpdate = Time.time;
        }

        /// <summary>
        /// Calculate appropriate reaction based on current game state.
        /// </summary>
        private NPCReactionType CalculateReaction()
        {
            if (narrativeSystem == null) return NPCReactionType.Neutral;

            float reputation = GetRelevantReputation();
            
            // Modify reputation based on NPC type and sensitivity
            reputation *= reputationSensitivity;
            
            // Apply faction-specific modifiers
            if (!string.IsNullOrEmpty(factionId))
            {
                float factionRep = narrativeSystem.GetFactionReputation(factionId);
                reputation = (reputation + factionRep) / 2f; // Average global and faction reputation
            }

            // Determine reaction based on reputation thresholds
            if (reputation >= 60f) return NPCReactionType.Respectful;
            if (reputation >= 30f) return NPCReactionType.Friendly;
            if (reputation >= -30f) return NPCReactionType.Neutral;
            if (reputation >= -60f) return NPCReactionType.Suspicious;
            if (reputation >= -80f) return NPCReactionType.Hostile;
            return NPCReactionType.Fearful;
        }

        /// <summary>
        /// Get relevant reputation for this NPC.
        /// </summary>
        private float GetRelevantReputation()
        {
            if (narrativeSystem == null) return 0f;

            // Use faction reputation if available, otherwise global
            if (!string.IsNullOrEmpty(factionId))
            {
                return narrativeSystem.GetFactionReputation(factionId);
            }
            
            return narrativeSystem.GlobalReputation;
        }

        /// <summary>
        /// Change NPC reaction and update behavior.
        /// </summary>
        private void ChangeReaction(NPCReactionType newReaction)
        {
            previousReaction = CurrentReaction;
            CurrentReaction = newReaction;
            
            ApplyReactionBehavior(newReaction);
            UpdateVisualFeedback();
            
            OnReactionChanged?.Invoke(this, newReaction);
            
            Debug.Log($"NPC {npcName} reaction changed from {previousReaction} to {newReaction}");
        }

        /// <summary>
        /// Apply behavior changes based on reaction.
        /// </summary>
        private void ApplyReactionBehavior(NPCReactionType reaction)
        {
            switch (reaction)
            {
                case NPCReactionType.Friendly:
                case NPCReactionType.Respectful:
                    movementSpeed = 2f;
                    IsInteractable = true;
                    break;
                    
                case NPCReactionType.Neutral:
                    movementSpeed = 2f;
                    IsInteractable = true;
                    break;
                    
                case NPCReactionType.Suspicious:
                    movementSpeed = 1.5f;
                    IsInteractable = true;
                    break;
                    
                case NPCReactionType.Hostile:
                    movementSpeed = 3f;
                    IsInteractable = false;
                    break;
                    
                case NPCReactionType.Fearful:
                    movementSpeed = 4f;
                    IsInteractable = false;
                    break;
            }
        }

        /// <summary>
        /// Handle reaction change events from Reactive AI System.
        /// </summary>
        private void OnNPCReactionChanged(string npcId, NPCReactionType reaction)
        {
            if (npcId == this.npcId)
            {
                ChangeReaction(reaction);
            }
        }
        #endregion

        #region Movement and Patrol
        /// <summary>
        /// Setup patrol system.
        /// </summary>
        private void SetupPatrol()
        {
            if (patrolPoints != null && patrolPoints.Length > 0 && canMove)
            {
                isPatrolling = true;
                currentPatrolIndex = 0;
            }
        }

        /// <summary>
        /// Update NPC movement.
        /// </summary>
        private void UpdateMovement()
        {
            if (!canMove) return;

            // Handle fearful behavior - move away from player
            if (CurrentReaction == NPCReactionType.Fearful && playerInRange)
            {
                MoveAwayFromPlayer();
                return;
            }

            // Handle hostile behavior - move towards player
            if (CurrentReaction == NPCReactionType.Hostile && playerInRange)
            {
                MoveTowardsPlayer();
                return;
            }

            // Normal patrol behavior
            if (isPatrolling && patrolPoints.Length > 0)
            {
                PatrolBehavior();
            }
        }

        /// <summary>
        /// Patrol between waypoints.
        /// </summary>
        private void PatrolBehavior()
        {
            Transform targetPoint = patrolPoints[currentPatrolIndex];
            
            if (Vector3.Distance(transform.position, targetPoint.position) < 1f)
            {
                currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            }
            
            Vector3 direction = (targetPoint.position - transform.position).normalized;
            transform.position += direction * movementSpeed * Time.deltaTime;
            
            // Face movement direction
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
        }

        /// <summary>
        /// Move away from player when fearful.
        /// </summary>
        private void MoveAwayFromPlayer()
        {
            if (player == null) return;
            
            Vector3 direction = (transform.position - player.position).normalized;
            transform.position += direction * movementSpeed * Time.deltaTime;
            transform.rotation = Quaternion.LookRotation(direction);
        }

        /// <summary>
        /// Move towards player when hostile.
        /// </summary>
        private void MoveTowardsPlayer()
        {
            if (player == null) return;
            
            Vector3 direction = (player.position - transform.position).normalized;
            transform.position += direction * movementSpeed * Time.deltaTime;
            transform.rotation = Quaternion.LookRotation(direction);
        }
        #endregion

        #region Player Detection
        /// <summary>
        /// Update player detection and interaction range.
        /// </summary>
        private void UpdatePlayerDetection()
        {
            if (player == null)
            {
                player = GameObject.FindGameObjectWithTag("Player")?.transform;
                return;
            }

            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            bool wasInRange = playerInRange;
            playerInRange = distanceToPlayer <= interactionRange;

            // Trigger interaction events
            if (playerInRange && !wasInRange)
            {
                OnPlayerEnterRange();
            }
            else if (!playerInRange && wasInRange)
            {
                OnPlayerExitRange();
            }
        }

        /// <summary>
        /// Handle player entering interaction range.
        /// </summary>
        private void OnPlayerEnterRange()
        {
            if (IsInteractable)
            {
                OnInteractionStarted?.Invoke(this);
            }
        }

        /// <summary>
        /// Handle player exiting interaction range.
        /// </summary>
        private void OnPlayerExitRange()
        {
            OnInteractionEnded?.Invoke(this);
        }
        #endregion

        #region Dialogue System
        /// <summary>
        /// Get dialogue based on current reaction.
        /// </summary>
        public string GetDialogue()
        {
            if (!hasDialogue) return "";

            string[] dialogueArray = GetDialogueArray();
            
            if (dialogueArray != null && dialogueArray.Length > 0)
            {
                return dialogueArray[Random.Range(0, dialogueArray.Length)];
            }
            
            return $"Hello, I am {npcName}.";
        }

        /// <summary>
        /// Get appropriate dialogue array based on reaction.
        /// </summary>
        private string[] GetDialogueArray()
        {
            switch (CurrentReaction)
            {
                case NPCReactionType.Friendly:
                case NPCReactionType.Respectful:
                    return friendlyDialogue;
                case NPCReactionType.Hostile:
                    return hostileDialogue;
                case NPCReactionType.Fearful:
                    return fearfulDialogue;
                default:
                    return neutralDialogue;
            }
        }

        /// <summary>
        /// Interact with the NPC.
        /// </summary>
        public void Interact()
        {
            if (!IsInteractable) return;
            
            lastInteractionTime = Time.time;
            
            // Get dialogue and display it
            string dialogue = GetDialogue();
            Debug.Log($"{npcName}: {dialogue}");
            
            // Could integrate with dialogue system here
            // DialogueSystem.Instance.StartDialogue(this, dialogue);
        }
        #endregion

        #region Visual Feedback
        /// <summary>
        /// Update visual feedback based on current reaction.
        /// </summary>
        private void UpdateVisualFeedback()
        {
            // Update indicators
            if (friendlyIndicator != null)
                friendlyIndicator.SetActive(CurrentReaction == NPCReactionType.Friendly || CurrentReaction == NPCReactionType.Respectful);
            
            if (hostileIndicator != null)
                hostileIndicator.SetActive(CurrentReaction == NPCReactionType.Hostile);
            
            if (fearfulIndicator != null)
                fearfulIndicator.SetActive(CurrentReaction == NPCReactionType.Fearful);

            // Update color
            if (npcRenderer != null)
            {
                Color targetColor = GetReactionColor();
                npcRenderer.material.color = targetColor;
            }
        }

        /// <summary>
        /// Get color based on current reaction.
        /// </summary>
        private Color GetReactionColor()
        {
            switch (CurrentReaction)
            {
                case NPCReactionType.Friendly:
                case NPCReactionType.Respectful:
                    return friendlyColor;
                case NPCReactionType.Hostile:
                    return hostileColor;
                case NPCReactionType.Fearful:
                    return fearfulColor;
                default:
                    return neutralColor;
            }
        }

        /// <summary>
        /// Update animation parameters.
        /// </summary>
        private void UpdateAnimations()
        {
            if (animator == null) return;

            animator.SetFloat("Speed", canMove ? movementSpeed : 0f);
            animator.SetInteger("Reaction", (int)CurrentReaction);
            animator.SetBool("PlayerInRange", playerInRange);
            animator.SetBool("IsInteractable", IsInteractable);
        }
        #endregion

        #region Public API
        /// <summary>
        /// Force a specific reaction.
        /// </summary>
        public void SetReaction(NPCReactionType reaction)
        {
            ChangeReaction(reaction);
        }

        /// <summary>
        /// Get current reputation influence.
        /// </summary>
        public float GetReputationInfluence()
        {
            return GetRelevantReputation() * reputationSensitivity;
        }

        /// <summary>
        /// Check if player is in interaction range.
        /// </summary>
        public bool IsPlayerInRange()
        {
            return playerInRange;
        }

        /// <summary>
        /// Set NPC faction.
        /// </summary>
        public void SetFaction(string newFactionId)
        {
            factionId = newFactionId;
        }

        /// <summary>
        /// Enable or disable NPC movement.
        /// </summary>
        public void SetMovementEnabled(bool enabled)
        {
            canMove = enabled;
        }

        /// <summary>
        /// Set interaction availability.
        /// </summary>
        public void SetInteractable(bool interactable)
        {
            IsInteractable = interactable;
        }
        #endregion

        #region Debug
        private void OnDrawGizmosSelected()
        {
            // Interaction range
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, interactionRange);

            // Patrol points
            if (patrolPoints != null && patrolPoints.Length > 0)
            {
                Gizmos.color = Color.blue;
                for (int i = 0; i < patrolPoints.Length; i++)
                {
                    if (patrolPoints[i] != null)
                    {
                        Gizmos.DrawSphere(patrolPoints[i].position, 0.3f);
                        
                        // Draw lines between patrol points
                        int nextIndex = (i + 1) % patrolPoints.Length;
                        if (patrolPoints[nextIndex] != null)
                        {
                            Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[nextIndex].position);
                        }
                    }
                }
            }

            // Player connection
            if (Application.isPlaying && player != null)
            {
                Gizmos.color = playerInRange ? Color.green : Color.red;
                Gizmos.DrawLine(transform.position, player.position);
            }
        }
        #endregion
    }

    #region Enums
    public enum NPCType
    {
        Civilian,
        Merchant,
        Guard,
        Noble,
        Beggar,
        Priest,
        Scholar
    }
    #endregion
}
