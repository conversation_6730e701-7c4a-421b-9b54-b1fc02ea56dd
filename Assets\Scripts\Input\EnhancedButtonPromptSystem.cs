using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using CinderOfDarkness.Input;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Enhanced UI Button Prompt System with dynamic device detection and icon support.
    /// Automatically displays appropriate button prompts based on connected input device.
    /// </summary>
    public class EnhancedButtonPromptSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Button Prompt Settings")]
        [SerializeField] private GameObject buttonPromptPrefab;
        [SerializeField] private Transform promptContainer;
        [SerializeField] private float promptDisplayDuration = 3f;
        [SerializeField] private float fadeInDuration = 0.3f;
        [SerializeField] private float fadeOutDuration = 0.3f;
        [SerializeField] private int maxActivePrompts = 5;

        [Header("Input Detection")]
        [SerializeField] private bool autoDetectInputDevice = true;
        [SerializeField] private float inputDetectionDelay = 0.1f;

        [Header("Positioning")]
        [SerializeField] private Vector2 promptSpacing = new Vector2(0, 50);
        [SerializeField] private bool stackVertically = true;
        [SerializeField] private bool autoPosition = true;

        [Header("Animation")]
        [SerializeField] private AnimationCurve fadeInCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private AnimationCurve fadeOutCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);
        [SerializeField] private bool useScaleAnimation = true;
        [SerializeField] private Vector3 scaleAnimationRange = new Vector3(0.8f, 1.2f, 1f);
        #endregion

        #region Public Properties
        public static EnhancedButtonPromptSystem Instance { get; private set; }
        public InputDeviceType CurrentInputDevice { get; private set; } = InputDeviceType.KeyboardMouse;
        public bool IsGamepadConnected => GamepadManager.Instance?.IsGamepadConnected ?? false;
        #endregion

        #region Private Fields
        private Dictionary<string, ButtonPromptData> activePrompts = new Dictionary<string, ButtonPromptData>();
        private Queue<GameObject> promptPool = new Queue<GameObject>();
        private float lastInputTime;
        private GamepadManager gamepadManager;
        private List<string> promptDisplayOrder = new List<string>();
        #endregion

        #region Events
        public System.Action<InputDeviceType> OnInputDeviceChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize button prompt system singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }
        }

        /// <summary>
        /// Initialize prompt system.
        /// </summary>
        private void Start()
        {
            InitializePromptPool();
            InitializeInputDetection();
            DetectCurrentInputDevice();
        }

        /// <summary>
        /// Update input device detection.
        /// </summary>
        private void Update()
        {
            if (autoDetectInputDevice)
            {
                DetectInputDeviceChanges();
            }
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the prompt object pool for performance.
        /// </summary>
        private void InitializePromptPool()
        {
            for (int i = 0; i < maxActivePrompts * 2; i++)
            {
                GameObject prompt = Instantiate(buttonPromptPrefab, promptContainer);
                prompt.SetActive(false);
                promptPool.Enqueue(prompt);
            }
        }

        /// <summary>
        /// Initialize input detection systems.
        /// </summary>
        private void InitializeInputDetection()
        {
            gamepadManager = GamepadManager.Instance;
            
            if (gamepadManager != null)
            {
                gamepadManager.OnGamepadConnected += OnGamepadConnected;
                gamepadManager.OnGamepadDisconnected += OnGamepadDisconnected;
            }

            InputSystem.onDeviceChange += OnInputDeviceChange;
        }
        #endregion

        #region Input Device Detection
        /// <summary>
        /// Handle Input System device changes.
        /// </summary>
        /// <param name="device">Changed device</param>
        /// <param name="change">Type of change</param>
        private void OnInputDeviceChange(InputDevice device, InputDeviceChange change)
        {
            if (change == InputDeviceChange.Added || change == InputDeviceChange.Removed)
            {
                DetectCurrentInputDevice();
            }
        }

        /// <summary>
        /// Handle gamepad connection.
        /// </summary>
        /// <param name="gamepadType">Type of connected gamepad</param>
        private void OnGamepadConnected(GamepadType gamepadType)
        {
            SetInputDevice(InputDeviceType.Gamepad);
        }

        /// <summary>
        /// Handle gamepad disconnection.
        /// </summary>
        /// <param name="gamepadType">Type of disconnected gamepad</param>
        private void OnGamepadDisconnected(GamepadType gamepadType)
        {
            SetInputDevice(InputDeviceType.KeyboardMouse);
        }

        /// <summary>
        /// Detect current input device based on recent input.
        /// </summary>
        private void DetectCurrentInputDevice()
        {
            if (IsGamepadConnected)
            {
                SetInputDevice(InputDeviceType.Gamepad);
            }
            else
            {
                SetInputDevice(InputDeviceType.KeyboardMouse);
            }
        }

        /// <summary>
        /// Detect input device changes based on user input.
        /// </summary>
        private void DetectInputDeviceChanges()
        {
            if (Time.time - lastInputTime < inputDetectionDelay) return;

            bool gamepadInput = false;
            bool keyboardMouseInput = false;

            // Check for gamepad input
            if (Gamepad.current != null)
            {
                gamepadInput = Gamepad.current.wasUpdatedThisFrame;
            }

            // Check for keyboard/mouse input
            if (Keyboard.current != null)
            {
                keyboardMouseInput = Keyboard.current.wasUpdatedThisFrame;
            }

            if (Mouse.current != null)
            {
                keyboardMouseInput |= Mouse.current.wasUpdatedThisFrame;
            }

            if (gamepadInput && CurrentInputDevice != InputDeviceType.Gamepad)
            {
                SetInputDevice(InputDeviceType.Gamepad);
                lastInputTime = Time.time;
            }
            else if (keyboardMouseInput && CurrentInputDevice != InputDeviceType.KeyboardMouse)
            {
                SetInputDevice(InputDeviceType.KeyboardMouse);
                lastInputTime = Time.time;
            }
        }

        /// <summary>
        /// Set the current input device and update all prompts.
        /// </summary>
        /// <param name="deviceType">New input device type</param>
        private void SetInputDevice(InputDeviceType deviceType)
        {
            if (CurrentInputDevice != deviceType)
            {
                CurrentInputDevice = deviceType;
                UpdateAllPrompts();
                OnInputDeviceChanged?.Invoke(deviceType);
            }
        }
        #endregion

        #region Prompt Management
        /// <summary>
        /// Show a button prompt with specified parameters.
        /// </summary>
        /// <param name="promptId">Unique identifier for the prompt</param>
        /// <param name="action">Action description</param>
        /// <param name="keyboardKey">Keyboard key binding</param>
        /// <param name="gamepadButton">Gamepad button binding</param>
        /// <param name="duration">Display duration (-1 for permanent)</param>
        /// <param name="priority">Display priority (higher = more important)</param>
        public void ShowPrompt(string promptId, string action, string keyboardKey, string gamepadButton, 
                              float duration = -1f, int priority = 0)
        {
            if (activePrompts.ContainsKey(promptId))
            {
                UpdatePrompt(promptId, action, keyboardKey, gamepadButton, duration, priority);
                return;
            }

            // Check if we've reached the maximum number of prompts
            if (activePrompts.Count >= maxActivePrompts)
            {
                RemoveLowestPriorityPrompt();
            }

            GameObject promptObject = GetPromptFromPool();
            if (promptObject == null) return;

            ButtonPromptComponent prompt = promptObject.GetComponent<ButtonPromptComponent>();
            if (prompt == null)
            {
                prompt = promptObject.AddComponent<ButtonPromptComponent>();
            }

            // Create prompt data
            ButtonPromptData promptData = new ButtonPromptData
            {
                Id = promptId,
                Action = action,
                KeyboardKey = keyboardKey,
                GamepadButton = gamepadButton,
                Duration = duration > 0 ? duration : promptDisplayDuration,
                Priority = priority,
                PromptObject = promptObject,
                PromptComponent = prompt,
                StartTime = Time.time
            };

            // Setup the prompt
            string buttonText = GetButtonTextForCurrentDevice(keyboardKey, gamepadButton);
            Sprite buttonIcon = GetButtonIconForCurrentDevice(keyboardKey, gamepadButton);
            
            prompt.SetPrompt(action, buttonText, buttonIcon, CurrentInputDevice);
            promptObject.SetActive(true);

            // Add to active prompts
            activePrompts[promptId] = promptData;
            promptDisplayOrder.Add(promptId);

            // Position the prompt
            if (autoPosition)
            {
                PositionPrompt(promptObject);
            }

            // Start fade in animation
            StartCoroutine(AnimatePromptIn(prompt));

            // Auto-hide after duration if specified
            if (duration > 0)
            {
                StartCoroutine(HidePromptAfterDelay(promptId, duration));
            }
        }

        /// <summary>
        /// Hide a specific prompt.
        /// </summary>
        /// <param name="promptId">ID of prompt to hide</param>
        public void HidePrompt(string promptId)
        {
            if (activePrompts.ContainsKey(promptId))
            {
                ButtonPromptData promptData = activePrompts[promptId];
                StartCoroutine(AnimatePromptOut(promptData.PromptComponent, () =>
                {
                    activePrompts.Remove(promptId);
                    promptDisplayOrder.Remove(promptId);
                    ReturnPromptToPool(promptData.PromptObject);
                    RepositionAllPrompts();
                }));
            }
        }

        /// <summary>
        /// Get button text for current input device.
        /// </summary>
        /// <param name="keyboardKey">Keyboard key binding</param>
        /// <param name="gamepadButton">Gamepad button binding</param>
        /// <returns>Appropriate button text</returns>
        private string GetButtonTextForCurrentDevice(string keyboardKey, string gamepadButton)
        {
            return CurrentInputDevice == InputDeviceType.Gamepad ? gamepadButton : keyboardKey;
        }

        /// <summary>
        /// Get button icon for current input device.
        /// </summary>
        /// <param name="keyboardKey">Keyboard key binding</param>
        /// <param name="gamepadButton">Gamepad button binding</param>
        /// <returns>Appropriate button icon</returns>
        private Sprite GetButtonIconForCurrentDevice(string keyboardKey, string gamepadButton)
        {
            if (CurrentInputDevice == InputDeviceType.Gamepad && gamepadManager != null)
            {
                return gamepadManager.GetButtonIcon(gamepadButton);
            }
            else
            {
                // Load keyboard icon from resources
                return Resources.Load<Sprite>($"UI/ButtonIcons/Keyboard/{keyboardKey}");
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Show interaction prompt.
        /// </summary>
        /// <param name="action">Action description</param>
        /// <param name="keyboardKey">Keyboard key</param>
        /// <param name="gamepadButton">Gamepad button</param>
        public void ShowInteractionPrompt(string action, string keyboardKey = "E", string gamepadButton = "A")
        {
            ShowPrompt("interaction", action, keyboardKey, gamepadButton, -1f, 10);
        }

        /// <summary>
        /// Show combat prompt.
        /// </summary>
        /// <param name="action">Action description</param>
        /// <param name="keyboardKey">Keyboard key</param>
        /// <param name="gamepadButton">Gamepad button</param>
        public void ShowCombatPrompt(string action, string keyboardKey, string gamepadButton)
        {
            ShowPrompt("combat", action, keyboardKey, gamepadButton, 2f, 8);
        }

        /// <summary>
        /// Hide all prompts.
        /// </summary>
        public void HideAllPrompts()
        {
            List<string> promptIds = new List<string>(activePrompts.Keys);
            foreach (string promptId in promptIds)
            {
                HidePrompt(promptId);
            }
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Input device types.
    /// </summary>
    public enum InputDeviceType
    {
        KeyboardMouse,
        Gamepad
    }

    /// <summary>
    /// Button prompt data container.
    /// </summary>
    [System.Serializable]
    public class ButtonPromptData
    {
        public string Id;
        public string Action;
        public string KeyboardKey;
        public string GamepadButton;
        public float Duration;
        public int Priority;
        public GameObject PromptObject;
        public ButtonPromptComponent PromptComponent;
        public float StartTime;
    }

    /// <summary>
    /// Button prompt component for individual prompts.
    /// </summary>
    public class ButtonPromptComponent : MonoBehaviour
    {
        [Header("UI Components")]
        public Text actionText;
        public Text buttonText;
        public Image buttonIcon;
        public CanvasGroup canvasGroup;

        /// <summary>
        /// Set prompt display information.
        /// </summary>
        /// <param name="action">Action description</param>
        /// <param name="buttonText">Button text</param>
        /// <param name="buttonIcon">Button icon</param>
        /// <param name="deviceType">Input device type</param>
        public void SetPrompt(string action, string buttonText, Sprite buttonIcon, InputDeviceType deviceType)
        {
            if (actionText != null) actionText.text = action;
            if (this.buttonText != null) this.buttonText.text = buttonText;
            if (this.buttonIcon != null) this.buttonIcon.sprite = buttonIcon;
        }

        /// <summary>
        /// Update input device display.
        /// </summary>
        /// <param name="deviceType">New device type</param>
        /// <param name="buttonText">New button text</param>
        /// <param name="buttonIcon">New button icon</param>
        public void UpdateInputDevice(InputDeviceType deviceType, string buttonText, Sprite buttonIcon)
        {
            if (this.buttonText != null) this.buttonText.text = buttonText;
            if (this.buttonIcon != null) this.buttonIcon.sprite = buttonIcon;
        }
    }
    #endregion
}
