using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class NewRacesSystem : MonoBehaviour
{
    [Header("New Races")]
    public RaceData[] newRaces;
    public List<GameObject> spawnedRaceMembers = new List<GameObject>();
    
    [Header("Race Relationships")]
    public RaceRelationship[] raceRelationships;
    public Dictionary<string, float> playerRaceStandings = new Dictionary<string, float>();
    
    [Header("Cultural Elements")]
    public GameObject[] raceVillagePrefabs;
    public AudioClip[] racialMusic;
    public Material[] racialMaterials;
    
    private PhilosophicalMoralitySystem moralitySystem;
    private HostilitySystem hostilitySystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class RaceData
    {
        [Header("Identity")]
        public string raceName;
        public string culturalName;
        public RaceType type;
        public string philosophy;
        public string greeting;
        
        [Header("Appearance")]
        public GameObject[] maleModels;
        public GameObject[] femaleModels;
        public GameObject[] childModels;
        public GameObject[] elderModels;
        public Color primaryColor;
        public Color secondaryColor;
        
        [Header("Culture")]
        public CommunicationStyle communicationStyle;
        public string[] culturalValues;
        public string[] traditionalNames;
        public string villageDescription;
        public GameObject villageStructurePrefab;
        
        [Header("Abilities")]
        public string[] racialAbilities;
        public float[] abilityPowers;
        public string specialCraft;
        public WeaponType preferredWeapons;
        
        [Header("Relationships")]
        public float defaultPlayerStanding;
        public string[] alliedRaces;
        public string[] enemyRaces;
        public MoralityPreference moralityPreference;
        
        public enum RaceType
        {
            Drenari,     // Shadow-glowing silence worshippers
            Vaelari,     // Thin dwarves with organic weapons
            MyrrhKin,    // Half-spirit singing race
            Thornsouls,  // Bramble-skinned pain-strengthened
            Velokari     // Aquatic signal communicators
        }
        
        public enum CommunicationStyle
        {
            Silence,
            Singing,
            Underwater,
            Telepathic,
            Standard
        }
        
        public enum WeaponType
        {
            Organic,
            Shadow,
            Musical,
            Thorn,
            Aquatic
        }
        
        public enum MoralityPreference
        {
            Neutral,
            Good,
            Evil,
            Balance,
            Chaos
        }
    }
    
    [System.Serializable]
    public class RaceRelationship
    {
        public string race1;
        public string race2;
        public RelationshipType relationship;
        public string reason;
        
        public enum RelationshipType
        {
            Allied,
            Neutral,
            Hostile,
            Ancient_Enemies,
            Symbiotic
        }
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        hostilitySystem = GetComponent<HostilitySystem>();
        gameManager = GameManager.Instance;
        
        InitializeNewRaces();
        InitializeRaceRelationships();
        SpawnRaceVillages();
    }
    
    void Update()
    {
        UpdateRaceStandings();
        HandleRacialInteractions();
    }
    
    void InitializeNewRaces()
    {
        // Drenari - Shadow-glowing silence worshippers
        RaceData drenari = new RaceData
        {
            raceName = "Drenari",
            culturalName = "The Silent Luminous",
            type = RaceData.RaceType.Drenari,
            philosophy = "In silence, truth is found. In shadow, light is born. We glow not to be seen, but to see within.",
            greeting = "*nods respectfully in complete silence, eyes glowing softly*",
            communicationStyle = RaceData.CommunicationStyle.Silence,
            primaryColor = new Color(0.1f, 0.1f, 0.3f), // Dark blue
            secondaryColor = new Color(0.8f, 0.9f, 1f), // Glowing white
            culturalValues = new string[]
            {
                "Silence is sacred",
                "Inner light guides all",
                "Shadows reveal truth",
                "Words corrupt meaning",
                "Meditation over action"
            },
            villageDescription = "A settlement of crystalline structures that glow softly in the darkness, where no voice is ever heard yet all understand each other perfectly.",
            specialCraft = "Shadow-light crystals that store memories",
            preferredWeapons = RaceData.WeaponType.Shadow,
            defaultPlayerStanding = 0f,
            moralityPreference = RaceData.MoralityPreference.Balance
        };
        
        // Vaelari - Thin dwarves with organic weapons
        RaceData vaelari = new RaceData
        {
            raceName = "Vaelari",
            culturalName = "The Living Forge",
            type = RaceData.RaceType.Vaelari,
            philosophy = "Metal is dead, but life grows eternal. We shape not stone, but sinew. We forge not steel, but soul.",
            greeting = "May your roots run deep and your branches bear fruit, stranger.",
            communicationStyle = RaceData.CommunicationStyle.Standard,
            primaryColor = new Color(0.4f, 0.6f, 0.3f), // Forest green
            secondaryColor = new Color(0.8f, 0.7f, 0.5f), // Bone white
            culturalValues = new string[]
            {
                "Life over metal",
                "Growth through symbiosis",
                "Alchemy of flesh and bone",
                "Respect for all living things",
                "Adaptation is survival"
            },
            villageDescription = "A living city where buildings grow from massive trees, and weapons are cultivated in bone gardens tended by master bio-alchemists.",
            specialCraft = "Bio-alchemy and organic weapon cultivation",
            preferredWeapons = RaceData.WeaponType.Organic,
            defaultPlayerStanding = 25f,
            moralityPreference = RaceData.MoralityPreference.Good
        };
        
        // Myrrh-Kin - Half-spirit singing race
        RaceData myrrhKin = new RaceData
        {
            raceName = "Myrrh-Kin",
            culturalName = "The Ethereal Chorus",
            type = RaceData.RaceType.MyrrhKin,
            philosophy = "♪ We are the bridge between flesh and spirit, singing the songs that bind the worlds together ♪",
            greeting = "♪ Greetings, earth-bound soul, may our melodies lift your spirit to new heights ♪",
            communicationStyle = RaceData.CommunicationStyle.Singing,
            primaryColor = new Color(0.9f, 0.8f, 0.9f), // Ethereal pink
            secondaryColor = new Color(0.7f, 0.9f, 1f), // Spirit blue
            culturalValues = new string[]
            {
                "Music is the universal language",
                "Harmony between worlds",
                "Emotion over logic",
                "Beauty in all things",
                "Death is just another song"
            },
            villageDescription = "A floating settlement of crystalline spires that resonate with constant, beautiful music, where the boundary between physical and spiritual is thin.",
            specialCraft = "Harmonic magic and spirit-binding melodies",
            preferredWeapons = RaceData.WeaponType.Musical,
            defaultPlayerStanding = 15f,
            moralityPreference = RaceData.MoralityPreference.Good
        };
        
        // Thornsouls - Bramble-skinned pain-strengthened
        RaceData thornsouls = new RaceData
        {
            raceName = "Thornsouls",
            culturalName = "The Pain-Blessed",
            type = RaceData.RaceType.Thornsouls,
            philosophy = "Pain is the teacher, suffering the lesson. Through thorns we grow strong, through wounds we find wisdom.",
            greeting = "May your pain make you stronger, and your scars tell worthy stories.",
            communicationStyle = RaceData.CommunicationStyle.Standard,
            primaryColor = new Color(0.3f, 0.2f, 0.1f), // Dark brown
            secondaryColor = new Color(0.6f, 0.3f, 0.2f), // Thorn red
            culturalValues = new string[]
            {
                "Strength through suffering",
                "Pain as purification",
                "Endurance above all",
                "Scars are honors",
                "Growth requires sacrifice"
            },
            villageDescription = "A fortress-settlement built into a massive thorn forest, where every structure is designed to test and strengthen those who dwell within.",
            specialCraft = "Thorn-weaving and pain-forged weapons",
            preferredWeapons = RaceData.WeaponType.Thorn,
            defaultPlayerStanding = -10f, // Respect must be earned through pain
            moralityPreference = RaceData.MoralityPreference.Neutral
        };
        
        // Velokari - Aquatic signal communicators
        RaceData velokari = new RaceData
        {
            raceName = "Velokari",
            culturalName = "The Deep Speakers",
            type = RaceData.RaceType.Velokari,
            philosophy = "The depths hold all secrets. In the pressure of the deep, truth is compressed to its purest form.",
            greeting = "*complex series of bioluminescent flashes and water vibrations*",
            communicationStyle = RaceData.CommunicationStyle.Underwater,
            primaryColor = new Color(0.1f, 0.4f, 0.6f), // Deep blue
            secondaryColor = new Color(0.3f, 0.8f, 0.9f), // Bioluminescent cyan
            culturalValues = new string[]
            {
                "Depth over surface",
                "Pressure creates strength",
                "Currents connect all",
                "Silence of the deep",
                "Ancient wisdom flows"
            },
            villageDescription = "An underwater city of coral and bioluminescent structures, accessible only to those who can breathe beneath the waves or earn their trust.",
            specialCraft = "Pressure-forging and bioluminescent technology",
            preferredWeapons = RaceData.WeaponType.Aquatic,
            defaultPlayerStanding = -20f, // Suspicious of surface dwellers
            moralityPreference = RaceData.MoralityPreference.Neutral
        };
        
        newRaces = new RaceData[] { drenari, vaelari, myrrhKin, thornsouls, velokari };
        
        // Initialize player standings
        foreach (RaceData race in newRaces)
        {
            playerRaceStandings[race.raceName] = race.defaultPlayerStanding;
        }
        
        Debug.Log("New races system initialized - 5 unique races with distinct cultures");
    }
    
    void InitializeRaceRelationships()
    {
        raceRelationships = new RaceRelationship[]
        {
            new RaceRelationship
            {
                race1 = "Drenari",
                race2 = "Myrrh-Kin",
                relationship = RaceRelationship.RelationshipType.Allied,
                reason = "Both races value spiritual transcendence over material concerns"
            },
            new RaceRelationship
            {
                race1 = "Vaelari",
                race2 = "Thornsouls",
                relationship = RaceRelationship.RelationshipType.Symbiotic,
                reason = "Vaelari provide healing bio-alchemy, Thornsouls provide protection"
            },
            new RaceRelationship
            {
                race1 = "Drenari",
                race2 = "Thornsouls",
                relationship = RaceRelationship.RelationshipType.Hostile,
                reason = "Silence vs. the screams of pain - fundamentally incompatible philosophies"
            },
            new RaceRelationship
            {
                race1 = "Velokari",
                race2 = "Myrrh-Kin",
                relationship = RaceRelationship.RelationshipType.Ancient_Enemies,
                reason = "Ancient war over whether music or silence rules the depths"
            }
        };
    }
    
    void SpawnRaceVillages()
    {
        // Spawn villages for each race in appropriate locations
        foreach (RaceData race in newRaces)
        {
            SpawnRaceVillage(race);
        }
    }
    
    void SpawnRaceVillage(RaceData race)
    {
        Vector3 villageLocation = GetVillageLocation(race.type);
        
        if (race.villageStructurePrefab != null)
        {
            GameObject village = Instantiate(race.villageStructurePrefab, villageLocation, Quaternion.identity);
            village.name = $"{race.raceName}_Village";
            
            // Add village component
            RaceVillage villageComponent = village.AddComponent<RaceVillage>();
            villageComponent.Initialize(race, this);
            
            // Spawn race members
            SpawnRaceMembers(race, villageLocation);
        }
        
        Debug.Log($"Spawned {race.raceName} village at {villageLocation}");
    }
    
    Vector3 GetVillageLocation(RaceData.RaceType raceType)
    {
        // Return appropriate locations for each race
        switch (raceType)
        {
            case RaceData.RaceType.Drenari:
                return new Vector3(-100f, 0f, -100f); // Dark cave system
            case RaceData.RaceType.Vaelari:
                return new Vector3(50f, 0f, 50f); // Forest clearing
            case RaceData.RaceType.MyrrhKin:
                return new Vector3(0f, 100f, 0f); // Floating in sky
            case RaceData.RaceType.Thornsouls:
                return new Vector3(-50f, 0f, 100f); // Thorn forest
            case RaceData.RaceType.Velokari:
                return new Vector3(100f, -20f, -50f); // Underwater
            default:
                return Vector3.zero;
        }
    }
    
    void SpawnRaceMembers(RaceData race, Vector3 villageCenter)
    {
        // Spawn diverse population: men, women, children, elders
        int populationSize = Random.Range(8, 15);
        
        for (int i = 0; i < populationSize; i++)
        {
            GameObject raceMember = SpawnRandomRaceMember(race, villageCenter);
            if (raceMember != null)
            {
                spawnedRaceMembers.Add(raceMember);
            }
        }
    }
    
    GameObject SpawnRandomRaceMember(RaceData race, Vector3 center)
    {
        GameObject[] modelPool = null;
        string memberType = "";
        
        // Choose random member type
        float random = Random.Range(0f, 1f);
        if (random < 0.4f && race.maleModels.Length > 0)
        {
            modelPool = race.maleModels;
            memberType = "Male";
        }
        else if (random < 0.8f && race.femaleModels.Length > 0)
        {
            modelPool = race.femaleModels;
            memberType = "Female";
        }
        else if (random < 0.9f && race.childModels.Length > 0)
        {
            modelPool = race.childModels;
            memberType = "Child";
        }
        else if (race.elderModels.Length > 0)
        {
            modelPool = race.elderModels;
            memberType = "Elder";
        }
        
        if (modelPool == null || modelPool.Length == 0) return null;
        
        // Spawn at random position around village center
        Vector3 spawnPos = center + Random.insideUnitSphere * 20f;
        spawnPos.y = center.y;
        
        GameObject member = Instantiate(modelPool[Random.Range(0, modelPool.Length)], spawnPos, Quaternion.identity);
        member.name = $"{race.raceName}_{memberType}_{Random.Range(1000, 9999)}";
        
        // Add race member component
        RaceMember raceMemberComponent = member.AddComponent<RaceMember>();
        raceMemberComponent.Initialize(race, memberType, this);
        
        return member;
    }
    
    void UpdateRaceStandings()
    {
        // Update standings based on player actions and moral choices
        if (moralitySystem == null) return;
        
        PhilosophicalMoralitySystem.MoralPath playerPath = moralitySystem.GetCurrentPath();
        
        foreach (RaceData race in newRaces)
        {
            float standingChange = CalculateStandingChange(race, playerPath);
            if (Mathf.Abs(standingChange) > 0.01f)
            {
                ModifyRaceStanding(race.raceName, standingChange);
            }
        }
    }
    
    float CalculateStandingChange(RaceData race, PhilosophicalMoralitySystem.MoralPath playerPath)
    {
        float change = 0f;
        
        // Races react to player's moral alignment
        switch (race.moralityPreference)
        {
            case RaceData.MoralityPreference.Good:
                if (playerPath == PhilosophicalMoralitySystem.MoralPath.Good)
                    change += 0.1f * Time.deltaTime;
                else if (playerPath == PhilosophicalMoralitySystem.MoralPath.Evil)
                    change -= 0.2f * Time.deltaTime;
                break;
            
            case RaceData.MoralityPreference.Balance:
                if (playerPath == PhilosophicalMoralitySystem.MoralPath.Eclipse)
                    change += 0.1f * Time.deltaTime;
                else
                    change -= 0.05f * Time.deltaTime;
                break;
        }
        
        return change;
    }
    
    void HandleRacialInteractions()
    {
        // Handle special interactions based on race communication styles
        foreach (GameObject member in spawnedRaceMembers)
        {
            if (member == null) continue;
            
            RaceMember raceMember = member.GetComponent<RaceMember>();
            if (raceMember == null) continue;
            
            float distance = Vector3.Distance(transform.position, member.transform.position);
            if (distance < 5f)
            {
                HandleRaceSpecificInteraction(raceMember);
            }
        }
    }
    
    void HandleRaceSpecificInteraction(RaceMember raceMember)
    {
        switch (raceMember.raceData.communicationStyle)
        {
            case RaceData.CommunicationStyle.Silence:
                HandleDrenariInteraction(raceMember);
                break;
            case RaceData.CommunicationStyle.Singing:
                HandleMyrrhKinInteraction(raceMember);
                break;
            case RaceData.CommunicationStyle.Underwater:
                HandleVelokariInteraction(raceMember);
                break;
        }
    }
    
    void HandleDrenariInteraction(RaceMember raceMember)
    {
        // Drenari communicate through silence and glowing
        if (Input.GetKeyDown(KeyCode.E))
        {
            ShowRaceMessage("*You stand in respectful silence. The Drenari's eyes glow brighter in acknowledgment.*");
            ModifyRaceStanding("Drenari", 2f);
        }
    }
    
    void HandleMyrrhKinInteraction(RaceMember raceMember)
    {
        // Myrrh-Kin communicate through song
        if (Input.GetKeyDown(KeyCode.E))
        {
            ShowRaceMessage("♪ The Myrrh-Kin sings a greeting melody. You feel your spirit lifted by their ethereal voice. ♪");
            
            // Reduce trauma through beautiful music
            PsychologicalSystem psycheSystem = GetComponent<PsychologicalSystem>();
            if (psycheSystem != null)
            {
                psycheSystem.ReduceTrauma(3f, "Soothed by Myrrh-Kin song");
            }
            
            ModifyRaceStanding("Myrrh-Kin", 1f);
        }
    }
    
    void HandleVelokariInteraction(RaceMember raceMember)
    {
        // Velokari require special conditions to communicate
        if (Input.GetKeyDown(KeyCode.E))
        {
            if (IsPlayerUnderwater())
            {
                ShowRaceMessage("*Complex bioluminescent patterns flash across the Velokari's skin. You sense ancient wisdom in their signals.*");
                ModifyRaceStanding("Velokari", 3f);
            }
            else
            {
                ShowRaceMessage("*The Velokari looks at you with deep, unreadable eyes. They cannot communicate properly above water.*");
            }
        }
    }
    
    bool IsPlayerUnderwater()
    {
        // Check if player is in underwater environment
        return transform.position.y < -10f; // Simplified check
    }
    
    public void ModifyRaceStanding(string raceName, float change)
    {
        if (playerRaceStandings.ContainsKey(raceName))
        {
            playerRaceStandings[raceName] = Mathf.Clamp(playerRaceStandings[raceName] + change, -100f, 100f);
            
            if (Mathf.Abs(change) > 1f)
            {
                Debug.Log($"Standing with {raceName}: {playerRaceStandings[raceName]:F1} ({change:+F1})");
            }
        }
    }
    
    public float GetRaceStanding(string raceName)
    {
        return playerRaceStandings.ContainsKey(raceName) ? playerRaceStandings[raceName] : 0f;
    }
    
    public RaceData GetRaceData(string raceName)
    {
        foreach (RaceData race in newRaces)
        {
            if (race.raceName == raceName)
                return race;
        }
        return null;
    }
    
    void ShowRaceMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Race Interaction: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public RaceData[] GetAllRaces() => newRaces;
    public Dictionary<string, float> GetAllRaceStandings() => playerRaceStandings;
    public List<GameObject> GetSpawnedRaceMembers() => spawnedRaceMembers;
}

public class RaceVillage : MonoBehaviour
{
    public NewRacesSystem.RaceData raceData;
    public NewRacesSystem raceSystem;
    
    public void Initialize(NewRacesSystem.RaceData race, NewRacesSystem system)
    {
        raceData = race;
        raceSystem = system;
    }
}

public class RaceMember : MonoBehaviour
{
    public NewRacesSystem.RaceData raceData;
    public string memberType;
    public NewRacesSystem raceSystem;
    
    public void Initialize(NewRacesSystem.RaceData race, string type, NewRacesSystem system)
    {
        raceData = race;
        memberType = type;
        raceSystem = system;
    }
}
