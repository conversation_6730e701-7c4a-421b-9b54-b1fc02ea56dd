using UnityEngine;
using UnityEngine.Profiling;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Systems
{
    /// <summary>
    /// Performance Monitor for Cinder of Darkness.
    /// Displays real-time FPS, memory usage, and identifies performance bottlenecks.
    /// </summary>
    public class PerformanceMonitor : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Monitor Settings")]
        [SerializeField] private bool enableOnStart = false;
        [SerializeField] private bool showInDeveloperMode = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;
        [SerializeField] private float updateInterval = 0.5f;
        [SerializeField] private int frameHistorySize = 60;

        [Header("UI References")]
        [SerializeField] private GameObject performanceUI;
        [SerializeField] private TextMeshProUGUI fpsText;
        [SerializeField] private TextMeshProUGUI memoryText;
        [SerializeField] private TextMeshProUGUI cpuText;
        [SerializeField] private TextMeshProUGUI gpuText;
        [SerializeField] private TextMeshProUGUI bottleneckText;
        [SerializeField] private RectTransform fpsGraphContainer;
        [SerializeField] private GameObject fpsGraphPointPrefab;

        [Header("Warning Thresholds")]
        [SerializeField] private float lowFPSThreshold = 30f;
        [SerializeField] private float criticalFPSThreshold = 15f;
        [SerializeField] private float highMemoryThreshold = 0.8f; // 80% of available memory
        [SerializeField] private float criticalMemoryThreshold = 0.95f; // 95% of available memory

        [Header("Colors")]
        [SerializeField] private Color normalColor = Color.green;
        [SerializeField] private Color warningColor = Color.yellow;
        [SerializeField] private Color criticalColor = Color.red;
        #endregion

        #region Public Properties
        public static PerformanceMonitor Instance { get; private set; }
        public bool IsVisible { get; private set; }
        public float CurrentFPS { get; private set; }
        public float AverageFPS { get; private set; }
        public float MinFPS { get; private set; }
        public float MaxFPS { get; private set; }
        public long UsedMemoryMB { get; private set; }
        public long TotalMemoryMB { get; private set; }
        public PerformanceBottleneck CurrentBottleneck { get; private set; }
        #endregion

        #region Private Fields
        private float lastUpdateTime;
        private List<float> frameHistory = new List<float>();
        private List<GameObject> fpsGraphPoints = new List<GameObject>();
        private float deltaTime;

        // Performance tracking
        private float cpuFrameTime;
        private float gpuFrameTime;
        private int drawCalls;
        private int triangles;
        private int batches;

        // Memory tracking
        private long lastGCMemory;
        private int gcCollections;
        private float lastGCTime;

        // Bottleneck detection
        private Queue<float> cpuTimes = new Queue<float>();
        private Queue<float> gpuTimes = new Queue<float>();
        private Queue<float> memoryUsage = new Queue<float>();
        private const int bottleneckSampleSize = 30;
        #endregion

        #region Events
        public System.Action<float> OnFPSChanged;
        public System.Action<PerformanceBottleneck> OnBottleneckDetected;
        public System.Action<PerformanceWarning> OnPerformanceWarning;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Performance Monitor singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePerformanceMonitor();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and initial state.
        /// </summary>
        private void Start()
        {
            SetupUI();

            if (enableOnStart || (showInDeveloperMode && Debug.isDebugBuild))
            {
                ShowMonitor();
            }
            else
            {
                HideMonitor();
            }
        }

        /// <summary>
        /// Update performance metrics and UI.
        /// </summary>
        private void Update()
        {
            HandleInput();
            UpdatePerformanceMetrics();

            if (IsVisible && Time.time - lastUpdateTime >= updateInterval)
            {
                UpdateUI();
                lastUpdateTime = Time.time;
            }
        }

        /// <summary>
        /// Update performance metrics every frame.
        /// </summary>
        private void LateUpdate()
        {
            // Calculate frame time
            deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
            CurrentFPS = 1.0f / deltaTime;

            // Update frame history
            UpdateFrameHistory();

            // Track performance metrics
            TrackPerformanceMetrics();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize performance monitor.
        /// </summary>
        private void InitializePerformanceMonitor()
        {
            // Initialize frame history
            frameHistory = new List<float>(frameHistorySize);

            // Reset performance metrics
            MinFPS = float.MaxValue;
            MaxFPS = 0f;

            // Initialize queues for bottleneck detection
            cpuTimes = new Queue<float>();
            gpuTimes = new Queue<float>();
            memoryUsage = new Queue<float>();
        }

        /// <summary>
        /// Setup UI components.
        /// </summary>
        private void SetupUI()
        {
            if (performanceUI == null)
            {
                Debug.LogWarning("Performance UI not assigned to PerformanceMonitor!");
                return;
            }

            // Initialize FPS graph
            if (fpsGraphContainer != null && fpsGraphPointPrefab != null)
            {
                InitializeFPSGraph();
            }
        }

        /// <summary>
        /// Initialize FPS graph visualization.
        /// </summary>
        private void InitializeFPSGraph()
        {
            // Clear existing graph points
            foreach (GameObject point in fpsGraphPoints)
            {
                if (point != null)
                {
                    Destroy(point);
                }
            }
            fpsGraphPoints.Clear();

            // Create graph points
            for (int i = 0; i < frameHistorySize; i++)
            {
                GameObject point = Instantiate(fpsGraphPointPrefab, fpsGraphContainer);
                fpsGraphPoints.Add(point);

                // Position point
                RectTransform pointRect = point.GetComponent<RectTransform>();
                if (pointRect != null)
                {
                    float x = (float)i / (frameHistorySize - 1) * fpsGraphContainer.rect.width;
                    pointRect.anchoredPosition = new Vector2(x, 0);
                }
            }
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle input for toggling monitor.
        /// </summary>
        private void HandleInput()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleMonitor();
            }
        }
        #endregion

        #region Performance Tracking
        /// <summary>
        /// Update frame history for FPS calculations.
        /// </summary>
        private void UpdateFrameHistory()
        {
            frameHistory.Add(CurrentFPS);

            if (frameHistory.Count > frameHistorySize)
            {
                frameHistory.RemoveAt(0);
            }

            // Calculate statistics
            if (frameHistory.Count > 0)
            {
                AverageFPS = frameHistory.Average();
                MinFPS = Mathf.Min(MinFPS, CurrentFPS);
                MaxFPS = Mathf.Max(MaxFPS, CurrentFPS);
            }
        }

        /// <summary>
        /// Track detailed performance metrics.
        /// </summary>
        private void TrackPerformanceMetrics()
        {
            // CPU frame time (simplified)
            cpuFrameTime = Time.unscaledDeltaTime * 1000f; // Convert to milliseconds

            // GPU frame time (would need GPU profiler integration)
            gpuFrameTime = cpuFrameTime * 0.7f; // Simplified estimation

            // Rendering statistics
            drawCalls = UnityEngine.Rendering.DebugUI.instance != null ? 0 : 0; // Placeholder
            triangles = 0; // Would need renderer statistics
            batches = 0; // Would need renderer statistics

            // Memory tracking
            UsedMemoryMB = Profiler.GetTotalAllocatedMemory(false) / (1024 * 1024);
            TotalMemoryMB = SystemInfo.systemMemorySize;

            // GC tracking
            long currentGCMemory = System.GC.GetTotalMemory(false);
            if (currentGCMemory < lastGCMemory)
            {
                gcCollections++;
                lastGCTime = Time.time;
            }
            lastGCMemory = currentGCMemory;

            // Update bottleneck detection queues
            UpdateBottleneckQueues();
        }

        /// <summary>
        /// Update queues for bottleneck detection.
        /// </summary>
        private void UpdateBottleneckQueues()
        {
            // Add current metrics to queues
            cpuTimes.Enqueue(cpuFrameTime);
            gpuTimes.Enqueue(gpuFrameTime);
            memoryUsage.Enqueue((float)UsedMemoryMB / TotalMemoryMB);

            // Maintain queue size
            while (cpuTimes.Count > bottleneckSampleSize)
            {
                cpuTimes.Dequeue();
            }
            while (gpuTimes.Count > bottleneckSampleSize)
            {
                gpuTimes.Dequeue();
            }
            while (memoryUsage.Count > bottleneckSampleSize)
            {
                memoryUsage.Dequeue();
            }
        }

        /// <summary>
        /// Update performance metrics for display.
        /// </summary>
        private void UpdatePerformanceMetrics()
        {
            // Detect performance bottlenecks
            DetectBottlenecks();

            // Check for performance warnings
            CheckPerformanceWarnings();
        }

        /// <summary>
        /// Detect performance bottlenecks.
        /// </summary>
        private void DetectBottlenecks()
        {
            if (cpuTimes.Count < bottleneckSampleSize) return;

            float avgCPU = cpuTimes.Average();
            float avgGPU = gpuTimes.Average();
            float avgMemory = memoryUsage.Average();

            PerformanceBottleneck detectedBottleneck = PerformanceBottleneck.None;

            // Determine primary bottleneck
            if (avgMemory > highMemoryThreshold)
            {
                detectedBottleneck = PerformanceBottleneck.Memory;
            }
            else if (avgCPU > avgGPU * 1.5f)
            {
                detectedBottleneck = PerformanceBottleneck.CPU;
            }
            else if (avgGPU > avgCPU * 1.5f)
            {
                detectedBottleneck = PerformanceBottleneck.GPU;
            }
            else if (CurrentFPS < lowFPSThreshold)
            {
                detectedBottleneck = PerformanceBottleneck.General;
            }

            if (detectedBottleneck != CurrentBottleneck)
            {
                CurrentBottleneck = detectedBottleneck;
                OnBottleneckDetected?.Invoke(detectedBottleneck);
            }
        }

        /// <summary>
        /// Check for performance warnings.
        /// </summary>
        private void CheckPerformanceWarnings()
        {
            // FPS warnings
            if (CurrentFPS < criticalFPSThreshold)
            {
                OnPerformanceWarning?.Invoke(PerformanceWarning.CriticalFPS);
            }
            else if (CurrentFPS < lowFPSThreshold)
            {
                OnPerformanceWarning?.Invoke(PerformanceWarning.LowFPS);
            }

            // Memory warnings
            float memoryRatio = (float)UsedMemoryMB / TotalMemoryMB;
            if (memoryRatio > criticalMemoryThreshold)
            {
                OnPerformanceWarning?.Invoke(PerformanceWarning.CriticalMemory);
            }
            else if (memoryRatio > highMemoryThreshold)
            {
                OnPerformanceWarning?.Invoke(PerformanceWarning.HighMemory);
            }
        }
        #endregion

        #region UI Updates
        /// <summary>
        /// Update UI elements with current performance data.
        /// </summary>
        private void UpdateUI()
        {
            if (!IsVisible) return;

            // Update FPS display
            UpdateFPSDisplay();

            // Update memory display
            UpdateMemoryDisplay();

            // Update CPU display
            UpdateCPUDisplay();

            // Update GPU display
            UpdateGPUDisplay();

            // Update bottleneck display
            UpdateBottleneckDisplay();

            // Update FPS graph
            UpdateFPSGraph();
        }

        /// <summary>
        /// Update FPS display with color coding.
        /// </summary>
        private void UpdateFPSDisplay()
        {
            if (fpsText == null) return;

            string fpsString = $"FPS: {CurrentFPS:F1}\n";
            fpsString += $"Avg: {AverageFPS:F1}\n";
            fpsString += $"Min: {MinFPS:F1} Max: {MaxFPS:F1}";

            fpsText.text = fpsString;

            // Color coding based on FPS
            if (CurrentFPS < criticalFPSThreshold)
            {
                fpsText.color = criticalColor;
            }
            else if (CurrentFPS < lowFPSThreshold)
            {
                fpsText.color = warningColor;
            }
            else
            {
                fpsText.color = normalColor;
            }

            OnFPSChanged?.Invoke(CurrentFPS);
        }

        /// <summary>
        /// Update memory display with usage information.
        /// </summary>
        private void UpdateMemoryDisplay()
        {
            if (memoryText == null) return;

            float memoryRatio = (float)UsedMemoryMB / TotalMemoryMB;
            string memoryString = $"Memory: {UsedMemoryMB}MB / {TotalMemoryMB}MB\n";
            memoryString += $"Usage: {memoryRatio * 100:F1}%\n";
            memoryString += $"GC: {gcCollections} collections";

            memoryText.text = memoryString;

            // Color coding based on memory usage
            if (memoryRatio > criticalMemoryThreshold)
            {
                memoryText.color = criticalColor;
            }
            else if (memoryRatio > highMemoryThreshold)
            {
                memoryText.color = warningColor;
            }
            else
            {
                memoryText.color = normalColor;
            }
        }

        /// <summary>
        /// Update CPU display with frame time information.
        /// </summary>
        private void UpdateCPUDisplay()
        {
            if (cpuText == null) return;

            string cpuString = $"CPU: {cpuFrameTime:F2}ms\n";
            cpuString += $"Draw Calls: {drawCalls}\n";
            cpuString += $"Batches: {batches}";

            cpuText.text = cpuString;
            cpuText.color = normalColor;
        }

        /// <summary>
        /// Update GPU display with rendering information.
        /// </summary>
        private void UpdateGPUDisplay()
        {
            if (gpuText == null) return;

            string gpuString = $"GPU: {gpuFrameTime:F2}ms\n";
            gpuString += $"Triangles: {triangles}\n";
            gpuString += $"VRAM: {SystemInfo.graphicsMemorySize}MB";

            gpuText.text = gpuString;
            gpuText.color = normalColor;
        }

        /// <summary>
        /// Update bottleneck display with current bottleneck information.
        /// </summary>
        private void UpdateBottleneckDisplay()
        {
            if (bottleneckText == null) return;

            string bottleneckString = $"Bottleneck: {GetBottleneckString(CurrentBottleneck)}";
            bottleneckText.text = bottleneckString;

            // Color coding based on bottleneck severity
            if (CurrentBottleneck == PerformanceBottleneck.None)
            {
                bottleneckText.color = normalColor;
            }
            else
            {
                bottleneckText.color = warningColor;
            }
        }

        /// <summary>
        /// Update FPS graph visualization.
        /// </summary>
        private void UpdateFPSGraph()
        {
            if (fpsGraphPoints.Count == 0 || frameHistory.Count == 0) return;

            float maxFPS = 120f; // Graph scale maximum
            float graphHeight = fpsGraphContainer.rect.height;

            for (int i = 0; i < fpsGraphPoints.Count && i < frameHistory.Count; i++)
            {
                GameObject point = fpsGraphPoints[i];
                if (point == null) continue;

                RectTransform pointRect = point.GetComponent<RectTransform>();
                if (pointRect != null)
                {
                    float normalizedFPS = Mathf.Clamp01(frameHistory[i] / maxFPS);
                    float y = normalizedFPS * graphHeight;

                    Vector2 currentPos = pointRect.anchoredPosition;
                    pointRect.anchoredPosition = new Vector2(currentPos.x, y);
                }
            }
        }
        #endregion

        #region Monitor Control
        /// <summary>
        /// Show performance monitor.
        /// </summary>
        public void ShowMonitor()
        {
            IsVisible = true;
            if (performanceUI != null)
            {
                performanceUI.SetActive(true);
            }
            Debug.Log("Performance Monitor enabled");
        }

        /// <summary>
        /// Hide performance monitor.
        /// </summary>
        public void HideMonitor()
        {
            IsVisible = false;
            if (performanceUI != null)
            {
                performanceUI.SetActive(false);
            }
        }

        /// <summary>
        /// Toggle performance monitor visibility.
        /// </summary>
        public void ToggleMonitor()
        {
            if (IsVisible)
            {
                HideMonitor();
            }
            else
            {
                ShowMonitor();
            }
        }

        /// <summary>
        /// Reset performance statistics.
        /// </summary>
        public void ResetStatistics()
        {
            frameHistory.Clear();
            MinFPS = float.MaxValue;
            MaxFPS = 0f;
            gcCollections = 0;
            cpuTimes.Clear();
            gpuTimes.Clear();
            memoryUsage.Clear();
            CurrentBottleneck = PerformanceBottleneck.None;

            Debug.Log("Performance statistics reset");
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Get bottleneck description string.
        /// </summary>
        /// <param name="bottleneck">Bottleneck type</param>
        /// <returns>Bottleneck description</returns>
        private string GetBottleneckString(PerformanceBottleneck bottleneck)
        {
            switch (bottleneck)
            {
                case PerformanceBottleneck.None:
                    return "None";
                case PerformanceBottleneck.CPU:
                    return "CPU Limited";
                case PerformanceBottleneck.GPU:
                    return "GPU Limited";
                case PerformanceBottleneck.Memory:
                    return "Memory Limited";
                case PerformanceBottleneck.General:
                    return "General Performance";
                default:
                    return "Unknown";
            }
        }

        /// <summary>
        /// Get performance recommendations based on current bottleneck.
        /// </summary>
        /// <returns>Performance recommendations</returns>
        public string GetPerformanceRecommendations()
        {
            switch (CurrentBottleneck)
            {
                case PerformanceBottleneck.CPU:
                    return "CPU bottleneck detected. Consider:\n" +
                           "- Reducing AI update frequency\n" +
                           "- Optimizing scripts and logic\n" +
                           "- Using object pooling\n" +
                           "- Reducing physics calculations";

                case PerformanceBottleneck.GPU:
                    return "GPU bottleneck detected. Consider:\n" +
                           "- Lowering graphics quality\n" +
                           "- Reducing resolution or render scale\n" +
                           "- Disabling expensive effects\n" +
                           "- Optimizing shaders";

                case PerformanceBottleneck.Memory:
                    return "Memory bottleneck detected. Consider:\n" +
                           "- Reducing texture quality\n" +
                           "- Unloading unused assets\n" +
                           "- Optimizing audio compression\n" +
                           "- Using streaming for large assets";

                case PerformanceBottleneck.General:
                    return "General performance issues detected. Consider:\n" +
                           "- Lowering overall quality settings\n" +
                           "- Reducing VFX complexity\n" +
                           "- Optimizing scene complexity\n" +
                           "- Checking for memory leaks";

                default:
                    return "Performance is optimal!";
            }
        }

        /// <summary>
        /// Get detailed performance report.
        /// </summary>
        /// <returns>Performance report string</returns>
        public string GetPerformanceReport()
        {
            string report = "=== PERFORMANCE REPORT ===\n\n";

            report += $"FPS Statistics:\n";
            report += $"  Current: {CurrentFPS:F1} FPS\n";
            report += $"  Average: {AverageFPS:F1} FPS\n";
            report += $"  Minimum: {MinFPS:F1} FPS\n";
            report += $"  Maximum: {MaxFPS:F1} FPS\n\n";

            report += $"Memory Usage:\n";
            report += $"  Used: {UsedMemoryMB} MB\n";
            report += $"  Total: {TotalMemoryMB} MB\n";
            report += $"  Usage: {(float)UsedMemoryMB / TotalMemoryMB * 100:F1}%\n";
            report += $"  GC Collections: {gcCollections}\n\n";

            report += $"Performance:\n";
            report += $"  CPU Frame Time: {cpuFrameTime:F2} ms\n";
            report += $"  GPU Frame Time: {gpuFrameTime:F2} ms\n";
            report += $"  Current Bottleneck: {GetBottleneckString(CurrentBottleneck)}\n\n";

            report += $"System Information:\n";
            report += $"  Graphics Memory: {SystemInfo.graphicsMemorySize} MB\n";
            report += $"  Processor: {SystemInfo.processorType}\n";
            report += $"  Graphics Device: {SystemInfo.graphicsDeviceName}\n";

            return report;
        }

        /// <summary>
        /// Export performance data to file.
        /// </summary>
        /// <param name="filePath">File path to export to</param>
        public void ExportPerformanceData(string filePath = null)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                filePath = System.IO.Path.Combine(Application.persistentDataPath,
                    $"performance_report_{System.DateTime.Now:yyyy-MM-dd_HH-mm-ss}.txt");
            }

            try
            {
                string report = GetPerformanceReport();
                System.IO.File.WriteAllText(filePath, report);
                Debug.Log($"Performance report exported to: {filePath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to export performance report: {e.Message}");
            }
        }

        /// <summary>
        /// Force garbage collection and measure impact.
        /// </summary>
        public void ForceGarbageCollection()
        {
            float beforeGC = Time.realtimeSinceStartup;
            long beforeMemory = System.GC.GetTotalMemory(false);

            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            System.GC.Collect();

            float afterGC = Time.realtimeSinceStartup;
            long afterMemory = System.GC.GetTotalMemory(false);

            float gcTime = (afterGC - beforeGC) * 1000f; // Convert to milliseconds
            long memoryFreed = (beforeMemory - afterMemory) / (1024 * 1024); // Convert to MB

            Debug.Log($"Forced GC completed in {gcTime:F2}ms, freed {memoryFreed}MB");
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set performance monitor enabled state.
        /// </summary>
        /// <param name="enabled">True to enable monitor</param>
        public void SetMonitorEnabled(bool enabled)
        {
            if (enabled)
            {
                ShowMonitor();
            }
            else
            {
                HideMonitor();
            }
        }

        /// <summary>
        /// Set update interval for performance monitoring.
        /// </summary>
        /// <param name="interval">Update interval in seconds</param>
        public void SetUpdateInterval(float interval)
        {
            updateInterval = Mathf.Max(0.1f, interval);
        }

        /// <summary>
        /// Set FPS warning thresholds.
        /// </summary>
        /// <param name="lowFPS">Low FPS threshold</param>
        /// <param name="criticalFPS">Critical FPS threshold</param>
        public void SetFPSThresholds(float lowFPS, float criticalFPS)
        {
            lowFPSThreshold = lowFPS;
            criticalFPSThreshold = criticalFPS;
        }

        /// <summary>
        /// Set memory warning thresholds.
        /// </summary>
        /// <param name="highMemory">High memory threshold (0-1)</param>
        /// <param name="criticalMemory">Critical memory threshold (0-1)</param>
        public void SetMemoryThresholds(float highMemory, float criticalMemory)
        {
            highMemoryThreshold = Mathf.Clamp01(highMemory);
            criticalMemoryThreshold = Mathf.Clamp01(criticalMemory);
        }

        /// <summary>
        /// Get current performance metrics.
        /// </summary>
        /// <returns>Performance metrics structure</returns>
        public PerformanceMetrics GetCurrentMetrics()
        {
            return new PerformanceMetrics
            {
                currentFPS = CurrentFPS,
                averageFPS = AverageFPS,
                minFPS = MinFPS,
                maxFPS = MaxFPS,
                usedMemoryMB = UsedMemoryMB,
                totalMemoryMB = TotalMemoryMB,
                cpuFrameTime = cpuFrameTime,
                gpuFrameTime = gpuFrameTime,
                currentBottleneck = CurrentBottleneck,
                gcCollections = gcCollections
            };
        }

        /// <summary>
        /// Check if performance is within acceptable ranges.
        /// </summary>
        /// <returns>True if performance is acceptable</returns>
        public bool IsPerformanceAcceptable()
        {
            bool fpsOK = CurrentFPS >= lowFPSThreshold;
            bool memoryOK = (float)UsedMemoryMB / TotalMemoryMB <= highMemoryThreshold;
            bool noBottleneck = CurrentBottleneck == PerformanceBottleneck.None;

            return fpsOK && memoryOK && noBottleneck;
        }

        /// <summary>
        /// Get performance grade based on current metrics.
        /// </summary>
        /// <returns>Performance grade</returns>
        public PerformanceGrade GetPerformanceGrade()
        {
            if (CurrentFPS >= 60f && (float)UsedMemoryMB / TotalMemoryMB <= 0.5f)
            {
                return PerformanceGrade.Excellent;
            }
            else if (CurrentFPS >= 45f && (float)UsedMemoryMB / TotalMemoryMB <= 0.7f)
            {
                return PerformanceGrade.Good;
            }
            else if (CurrentFPS >= 30f && (float)UsedMemoryMB / TotalMemoryMB <= 0.85f)
            {
                return PerformanceGrade.Fair;
            }
            else
            {
                return PerformanceGrade.Poor;
            }
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Performance metrics structure.
    /// </summary>
    [System.Serializable]
    public struct PerformanceMetrics
    {
        public float currentFPS;
        public float averageFPS;
        public float minFPS;
        public float maxFPS;
        public long usedMemoryMB;
        public long totalMemoryMB;
        public float cpuFrameTime;
        public float gpuFrameTime;
        public PerformanceBottleneck currentBottleneck;
        public int gcCollections;
    }
    #endregion

    #region Enums
    /// <summary>
    /// Performance bottleneck types.
    /// </summary>
    public enum PerformanceBottleneck
    {
        None,
        CPU,
        GPU,
        Memory,
        General
    }

    /// <summary>
    /// Performance warning types.
    /// </summary>
    public enum PerformanceWarning
    {
        LowFPS,
        CriticalFPS,
        HighMemory,
        CriticalMemory
    }

    /// <summary>
    /// Performance grade enumeration.
    /// </summary>
    public enum PerformanceGrade
    {
        Poor,
        Fair,
        Good,
        Excellent
    }
    #endregion
}
