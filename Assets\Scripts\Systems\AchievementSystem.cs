using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Systems
{
    /// <summary>
    /// Achievement System for Cinder of Darkness.
    /// Supports Steam achievements, hidden achievements, and progress tracking.
    /// </summary>
    public class AchievementSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Achievement Settings")]
        [SerializeField] private bool enableSteamAchievements = true;
        [SerializeField] private bool showHiddenAchievements = false;
        [SerializeField] private float notificationDuration = 5f;
        [SerializeField] private string achievementSaveKey = "CinderAchievements";

        [Header("UI References")]
        [SerializeField] private GameObject achievementNotificationPrefab;
        [SerializeField] private Transform notificationParent;
        [SerializeField] private GameObject achievementProgressTracker;
        [SerializeField] private Transform progressTrackerParent;
        [SerializeField] private GameObject achievementListUI;
        [SerializeField] private Transform achievementListContent;
        [SerializeField] private GameObject achievementItemPrefab;

        [Header("Audio")]
        [SerializeField] private AudioClip achievementUnlockedSound;
        [SerializeField] private AudioClip progressUpdateSound;
        [SerializeField] private AudioSource audioSource;

        [Header("Achievement Database")]
        [SerializeField] private AchievementData[] achievements;
        #endregion

        #region Public Properties
        public static AchievementSystem Instance { get; private set; }
        public int TotalAchievements => achievements.Length;
        public int UnlockedAchievements => achievementStates.Count(kvp => kvp.Value.isUnlocked);
        public float CompletionPercentage => TotalAchievements > 0 ? (float)UnlockedAchievements / TotalAchievements * 100f : 0f;
        #endregion

        #region Private Fields
        private Dictionary<string, AchievementState> achievementStates = new Dictionary<string, AchievementState>();
        private List<GameObject> activeNotifications = new List<GameObject>();
        private List<GameObject> activeProgressTrackers = new List<GameObject>();
        private Dictionary<string, GameObject> achievementListItems = new Dictionary<string, GameObject>();

        // Steam integration (placeholder)
        private bool steamInitialized = false;
        #endregion

        #region Events
        public System.Action<AchievementData> OnAchievementUnlocked;
        public System.Action<AchievementData, float> OnAchievementProgress;
        public System.Action<float> OnCompletionPercentageChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize Achievement System singleton.
        /// </summary>
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAchievementSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Setup components and load achievements.
        /// </summary>
        private void Start()
        {
            SetupComponents();
            LoadAchievementStates();
            InitializeSteamAchievements();
            SetupEventListeners();
            RefreshAchievementList();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize achievement system.
        /// </summary>
        private void InitializeAchievementSystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Initialize achievement states
            foreach (var achievement in achievements)
            {
                if (!achievementStates.ContainsKey(achievement.id))
                {
                    achievementStates[achievement.id] = new AchievementState
                    {
                        id = achievement.id,
                        isUnlocked = false,
                        progress = 0f,
                        unlockedDate = System.DateTime.MinValue
                    };
                }
            }
        }

        /// <summary>
        /// Setup component references.
        /// </summary>
        private void SetupComponents()
        {
            // Ensure notification parent exists
            if (notificationParent == null)
            {
                GameObject notificationCanvas = GameObject.Find("NotificationCanvas");
                if (notificationCanvas != null)
                {
                    notificationParent = notificationCanvas.transform;
                }
            }

            // Ensure progress tracker parent exists
            if (progressTrackerParent == null)
            {
                GameObject hudCanvas = GameObject.Find("HUDCanvas");
                if (hudCanvas != null)
                {
                    progressTrackerParent = hudCanvas.transform;
                }
            }
        }

        /// <summary>
        /// Initialize Steam achievements integration.
        /// </summary>
        private void InitializeSteamAchievements()
        {
            if (!enableSteamAchievements) return;

            try
            {
                // Steam achievements initialization would go here
                // For now, simulate Steam availability
                steamInitialized = Application.platform == RuntimePlatform.WindowsPlayer ||
                                 Application.platform == RuntimePlatform.WindowsEditor;

                if (steamInitialized)
                {
                    Debug.Log("Steam achievements initialized successfully");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to initialize Steam achievements: {e.Message}");
                steamInitialized = false;
            }
        }

        /// <summary>
        /// Setup event listeners for game events.
        /// </summary>
        private void SetupEventListeners()
        {
            // Listen for player events
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null)
            {
                playerStats.OnLevelUp += OnPlayerLevelUp;
                playerStats.OnHealthChanged += OnPlayerHealthChanged;
            }

            var playerCombat = FindObjectOfType<PlayerCombat>();
            if (playerCombat != null)
            {
                playerCombat.OnEnemyKilled += OnEnemyKilled;
                playerCombat.OnBossDefeated += OnBossDefeated;
            }

            // Listen for game events
            var gameManager = FindObjectOfType<GameManager>();
            if (gameManager != null)
            {
                gameManager.OnGameCompleted += OnGameCompleted;
                gameManager.OnSecretFound += OnSecretFound;
            }
        }
        #endregion

        #region Achievement Management
        /// <summary>
        /// Unlock an achievement by ID.
        /// </summary>
        /// <param name="achievementId">Achievement ID to unlock</param>
        public void UnlockAchievement(string achievementId)
        {
            var achievement = GetAchievementData(achievementId);
            if (achievement == null)
            {
                Debug.LogWarning($"Achievement not found: {achievementId}");
                return;
            }

            if (IsAchievementUnlocked(achievementId))
            {
                Debug.Log($"Achievement already unlocked: {achievementId}");
                return;
            }

            // Update achievement state
            achievementStates[achievementId].isUnlocked = true;
            achievementStates[achievementId].progress = 1f;
            achievementStates[achievementId].unlockedDate = System.DateTime.Now;

            // Save achievement states
            SaveAchievementStates();

            // Unlock on Steam if available
            if (steamInitialized && enableSteamAchievements)
            {
                UnlockSteamAchievement(achievementId);
            }

            // Show notification
            ShowAchievementNotification(achievement);

            // Play sound
            PlayAchievementSound(achievementUnlockedSound);

            // Update UI
            RefreshAchievementListItem(achievementId);
            UpdateCompletionPercentage();

            // Trigger events
            OnAchievementUnlocked?.Invoke(achievement);

            Debug.Log($"Achievement unlocked: {achievement.title}");
        }

        /// <summary>
        /// Update achievement progress.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        /// <param name="progress">Progress value (0-1)</param>
        public void UpdateAchievementProgress(string achievementId, float progress)
        {
            var achievement = GetAchievementData(achievementId);
            if (achievement == null || IsAchievementUnlocked(achievementId))
                return;

            progress = Mathf.Clamp01(progress);
            float oldProgress = achievementStates[achievementId].progress;
            achievementStates[achievementId].progress = progress;

            // Check if achievement should be unlocked
            if (progress >= 1f)
            {
                UnlockAchievement(achievementId);
                return;
            }

            // Show progress update if significant change
            if (progress - oldProgress >= 0.1f) // 10% progress increment
            {
                ShowProgressUpdate(achievement, progress);
                PlayAchievementSound(progressUpdateSound);
            }

            // Update progress tracker
            UpdateProgressTracker(achievement, progress);

            // Save progress
            SaveAchievementStates();

            // Trigger events
            OnAchievementProgress?.Invoke(achievement, progress);
        }

        /// <summary>
        /// Increment achievement progress by value.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        /// <param name="increment">Progress increment</param>
        public void IncrementAchievementProgress(string achievementId, float increment)
        {
            if (achievementStates.ContainsKey(achievementId))
            {
                float currentProgress = achievementStates[achievementId].progress;
                UpdateAchievementProgress(achievementId, currentProgress + increment);
            }
        }

        /// <summary>
        /// Check if achievement is unlocked.
        /// </summary>
        /// <param name="achievementId">Achievement ID to check</param>
        /// <returns>True if achievement is unlocked</returns>
        public bool IsAchievementUnlocked(string achievementId)
        {
            return achievementStates.ContainsKey(achievementId) && achievementStates[achievementId].isUnlocked;
        }

        /// <summary>
        /// Get achievement progress.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        /// <returns>Progress value (0-1)</returns>
        public float GetAchievementProgress(string achievementId)
        {
            return achievementStates.ContainsKey(achievementId) ? achievementStates[achievementId].progress : 0f;
        }

        /// <summary>
        /// Get achievement data by ID.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        /// <returns>Achievement data or null</returns>
        public AchievementData GetAchievementData(string achievementId)
        {
            return achievements.FirstOrDefault(a => a.id == achievementId);
        }

        /// <summary>
        /// Get all unlocked achievements.
        /// </summary>
        /// <returns>List of unlocked achievements</returns>
        public List<AchievementData> GetUnlockedAchievements()
        {
            return achievements.Where(a => IsAchievementUnlocked(a.id)).ToList();
        }

        /// <summary>
        /// Get all achievements with progress.
        /// </summary>
        /// <returns>List of all achievements</returns>
        public List<AchievementData> GetAllAchievements()
        {
            if (showHiddenAchievements)
            {
                return achievements.ToList();
            }
            else
            {
                return achievements.Where(a => !a.isHidden || IsAchievementUnlocked(a.id)).ToList();
            }
        }
        #endregion

        #region Steam Integration
        /// <summary>
        /// Unlock achievement on Steam.
        /// </summary>
        /// <param name="achievementId">Steam achievement ID</param>
        private void UnlockSteamAchievement(string achievementId)
        {
            try
            {
                // Steam achievement unlock implementation would go here
                // For now, just log the action
                Debug.Log($"Steam achievement unlocked: {achievementId}");

                // Example Steam API call:
                // SteamUserStats.SetAchievement(achievementId);
                // SteamUserStats.StoreStats();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to unlock Steam achievement: {e.Message}");
            }
        }

        /// <summary>
        /// Sync achievements with Steam.
        /// </summary>
        public void SyncWithSteam()
        {
            if (!steamInitialized || !enableSteamAchievements) return;

            try
            {
                // Steam sync implementation would go here
                Debug.Log("Syncing achievements with Steam...");

                // Example Steam API calls:
                // SteamUserStats.RequestCurrentStats();
                // Check Steam achievement states and update local states
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to sync with Steam: {e.Message}");
            }
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Show achievement unlock notification.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        private void ShowAchievementNotification(AchievementData achievement)
        {
            if (achievementNotificationPrefab == null || notificationParent == null) return;

            GameObject notification = Instantiate(achievementNotificationPrefab, notificationParent);
            var notificationUI = notification.GetComponent<AchievementNotificationUI>();

            if (notificationUI != null)
            {
                notificationUI.Setup(achievement, notificationDuration);
            }

            activeNotifications.Add(notification);
            StartCoroutine(RemoveNotificationAfterDelay(notification, notificationDuration));
        }

        /// <summary>
        /// Show progress update notification.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        /// <param name="progress">Current progress</param>
        private void ShowProgressUpdate(AchievementData achievement, float progress)
        {
            // Could show a smaller progress notification
            Debug.Log($"Achievement progress: {achievement.title} - {progress * 100:F0}%");
        }

        /// <summary>
        /// Update progress tracker UI.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        /// <param name="progress">Current progress</param>
        private void UpdateProgressTracker(AchievementData achievement, float progress)
        {
            if (achievementProgressTracker == null || progressTrackerParent == null) return;

            // Find or create progress tracker for this achievement
            string trackerId = $"tracker_{achievement.id}";
            GameObject tracker = activeProgressTrackers.FirstOrDefault(t => t.name == trackerId);

            if (tracker == null && progress > 0f && progress < 1f)
            {
                tracker = Instantiate(achievementProgressTracker, progressTrackerParent);
                tracker.name = trackerId;
                activeProgressTrackers.Add(tracker);
            }

            if (tracker != null)
            {
                var trackerUI = tracker.GetComponent<AchievementProgressTrackerUI>();
                if (trackerUI != null)
                {
                    trackerUI.UpdateProgress(achievement, progress);
                }

                // Remove tracker if achievement is complete
                if (progress >= 1f)
                {
                    activeProgressTrackers.Remove(tracker);
                    Destroy(tracker);
                }
            }
        }

        /// <summary>
        /// Refresh achievement list UI.
        /// </summary>
        private void RefreshAchievementList()
        {
            if (achievementListContent == null || achievementItemPrefab == null) return;

            // Clear existing items
            foreach (Transform child in achievementListContent)
            {
                Destroy(child.gameObject);
            }
            achievementListItems.Clear();

            // Create items for visible achievements
            var visibleAchievements = GetAllAchievements();
            foreach (var achievement in visibleAchievements)
            {
                CreateAchievementListItem(achievement);
            }
        }

        /// <summary>
        /// Create achievement list item.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        private void CreateAchievementListItem(AchievementData achievement)
        {
            GameObject item = Instantiate(achievementItemPrefab, achievementListContent);
            var itemUI = item.GetComponent<AchievementListItemUI>();

            if (itemUI != null)
            {
                bool isUnlocked = IsAchievementUnlocked(achievement.id);
                float progress = GetAchievementProgress(achievement.id);
                itemUI.Setup(achievement, isUnlocked, progress);
            }

            achievementListItems[achievement.id] = item;
        }

        /// <summary>
        /// Refresh specific achievement list item.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        private void RefreshAchievementListItem(string achievementId)
        {
            if (achievementListItems.ContainsKey(achievementId))
            {
                var item = achievementListItems[achievementId];
                var itemUI = item.GetComponent<AchievementListItemUI>();
                var achievement = GetAchievementData(achievementId);

                if (itemUI != null && achievement != null)
                {
                    bool isUnlocked = IsAchievementUnlocked(achievementId);
                    float progress = GetAchievementProgress(achievementId);
                    itemUI.Setup(achievement, isUnlocked, progress);
                }
            }
        }

        /// <summary>
        /// Remove notification after delay.
        /// </summary>
        /// <param name="notification">Notification GameObject</param>
        /// <param name="delay">Delay in seconds</param>
        /// <returns>Removal coroutine</returns>
        private System.Collections.IEnumerator RemoveNotificationAfterDelay(GameObject notification, float delay)
        {
            yield return new WaitForSeconds(delay);

            if (notification != null)
            {
                activeNotifications.Remove(notification);
                Destroy(notification);
            }
        }

        /// <summary>
        /// Update completion percentage and trigger event.
        /// </summary>
        private void UpdateCompletionPercentage()
        {
            OnCompletionPercentageChanged?.Invoke(CompletionPercentage);
        }
        #endregion

        #region Save/Load
        /// <summary>
        /// Save achievement states to PlayerPrefs.
        /// </summary>
        private void SaveAchievementStates()
        {
            try
            {
                var saveData = new AchievementSaveData
                {
                    states = achievementStates.Values.ToArray()
                };

                string json = JsonUtility.ToJson(saveData, true);
                PlayerPrefs.SetString(achievementSaveKey, json);
                PlayerPrefs.Save();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save achievement states: {e.Message}");
            }
        }

        /// <summary>
        /// Load achievement states from PlayerPrefs.
        /// </summary>
        private void LoadAchievementStates()
        {
            try
            {
                string json = PlayerPrefs.GetString(achievementSaveKey, "");
                if (!string.IsNullOrEmpty(json))
                {
                    var saveData = JsonUtility.FromJson<AchievementSaveData>(json);

                    foreach (var state in saveData.states)
                    {
                        achievementStates[state.id] = state;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load achievement states: {e.Message}");
            }
        }

        /// <summary>
        /// Reset all achievement progress.
        /// </summary>
        public void ResetAllAchievements()
        {
            foreach (var achievement in achievements)
            {
                achievementStates[achievement.id] = new AchievementState
                {
                    id = achievement.id,
                    isUnlocked = false,
                    progress = 0f,
                    unlockedDate = System.DateTime.MinValue
                };
            }

            SaveAchievementStates();
            RefreshAchievementList();
            UpdateCompletionPercentage();

            Debug.Log("All achievements reset");
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handle player level up event.
        /// </summary>
        /// <param name="newLevel">New player level</param>
        private void OnPlayerLevelUp(int newLevel)
        {
            // Check level-based achievements
            CheckAchievement("reach_level_10", newLevel >= 10);
            CheckAchievement("reach_level_25", newLevel >= 25);
            CheckAchievement("reach_level_50", newLevel >= 50);
            CheckAchievement("max_level", newLevel >= 100);
        }

        /// <summary>
        /// Handle player health change event.
        /// </summary>
        /// <param name="currentHealth">Current health</param>
        /// <param name="maxHealth">Maximum health</param>
        private void OnPlayerHealthChanged(float currentHealth, float maxHealth)
        {
            // Check health-based achievements
            if (currentHealth <= 1f && maxHealth > 1f)
            {
                CheckAchievement("close_call", true);
            }
        }

        /// <summary>
        /// Handle enemy killed event.
        /// </summary>
        /// <param name="enemyType">Type of enemy killed</param>
        private void OnEnemyKilled(string enemyType)
        {
            // Increment kill count achievements
            IncrementAchievementProgress("kill_100_enemies", 0.01f); // 1/100
            IncrementAchievementProgress("kill_1000_enemies", 0.001f); // 1/1000

            // Check specific enemy type achievements
            if (enemyType == "Boss")
            {
                IncrementAchievementProgress("defeat_all_bosses", 0.1f); // Assuming 10 bosses
            }
        }

        /// <summary>
        /// Handle boss defeated event.
        /// </summary>
        /// <param name="bossName">Name of defeated boss</param>
        private void OnBossDefeated(string bossName)
        {
            CheckAchievement($"defeat_{bossName.ToLower()}", true);
            IncrementAchievementProgress("defeat_all_bosses", 0.1f);
        }

        /// <summary>
        /// Handle game completed event.
        /// </summary>
        private void OnGameCompleted()
        {
            CheckAchievement("complete_game", true);
        }

        /// <summary>
        /// Handle secret found event.
        /// </summary>
        /// <param name="secretId">Secret identifier</param>
        private void OnSecretFound(string secretId)
        {
            IncrementAchievementProgress("find_all_secrets", 0.05f); // Assuming 20 secrets
        }

        /// <summary>
        /// Check and unlock achievement if condition is met.
        /// </summary>
        /// <param name="achievementId">Achievement ID</param>
        /// <param name="condition">Condition to check</param>
        private void CheckAchievement(string achievementId, bool condition)
        {
            if (condition && !IsAchievementUnlocked(achievementId))
            {
                UnlockAchievement(achievementId);
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play achievement sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlayAchievementSound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Show achievement list UI.
        /// </summary>
        public void ShowAchievementList()
        {
            if (achievementListUI != null)
            {
                achievementListUI.SetActive(true);
                RefreshAchievementList();
            }
        }

        /// <summary>
        /// Hide achievement list UI.
        /// </summary>
        public void HideAchievementList()
        {
            if (achievementListUI != null)
            {
                achievementListUI.SetActive(false);
            }
        }

        /// <summary>
        /// Toggle hidden achievements visibility.
        /// </summary>
        /// <param name="show">True to show hidden achievements</param>
        public void SetShowHiddenAchievements(bool show)
        {
            showHiddenAchievements = show;
            RefreshAchievementList();
        }

        /// <summary>
        /// Get achievement statistics.
        /// </summary>
        /// <returns>Achievement statistics</returns>
        public AchievementStats GetAchievementStats()
        {
            return new AchievementStats
            {
                totalAchievements = TotalAchievements,
                unlockedAchievements = UnlockedAchievements,
                completionPercentage = CompletionPercentage,
                hiddenAchievementsUnlocked = achievements.Count(a => a.isHidden && IsAchievementUnlocked(a.id))
            };
        }
        #endregion
    }

    #region Data Structures
    /// <summary>
    /// Achievement data structure.
    /// </summary>
    [System.Serializable]
    public class AchievementData
    {
        public string id;
        public string title;
        public string description;
        public string hiddenDescription;
        public Sprite icon;
        public bool isHidden;
        public AchievementType type;
        public int targetValue;
        public string steamId;
    }

    /// <summary>
    /// Achievement state for tracking progress.
    /// </summary>
    [System.Serializable]
    public class AchievementState
    {
        public string id;
        public bool isUnlocked;
        public float progress;
        public System.DateTime unlockedDate;
    }

    /// <summary>
    /// Achievement types.
    /// </summary>
    public enum AchievementType
    {
        Story,
        Combat,
        Exploration,
        Collection,
        Challenge,
        Hidden,
        Progression
    }

    /// <summary>
    /// Achievement save data structure.
    /// </summary>
    [System.Serializable]
    public class AchievementSaveData
    {
        public AchievementState[] states;
    }

    /// <summary>
    /// Achievement statistics structure.
    /// </summary>
    [System.Serializable]
    public class AchievementStats
    {
        public int totalAchievements;
        public int unlockedAchievements;
        public float completionPercentage;
        public int hiddenAchievementsUnlocked;
    }

    /// <summary>
    /// Achievement notification UI component.
    /// </summary>
    public class AchievementNotificationUI : MonoBehaviour
    {
        [Header("UI Components")]
        public Image achievementIcon;
        public TextMeshProUGUI achievementTitle;
        public TextMeshProUGUI achievementDescription;
        public CanvasGroup canvasGroup;

        /// <summary>
        /// Setup notification with achievement data.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        /// <param name="duration">Display duration</param>
        public void Setup(AchievementData achievement, float duration)
        {
            if (achievementIcon != null && achievement.icon != null)
                achievementIcon.sprite = achievement.icon;

            if (achievementTitle != null)
                achievementTitle.text = achievement.title;

            if (achievementDescription != null)
                achievementDescription.text = achievement.description;

            // Animate in
            StartCoroutine(AnimateNotification(duration));
        }

        /// <summary>
        /// Animate notification appearance and disappearance.
        /// </summary>
        /// <param name="duration">Display duration</param>
        /// <returns>Animation coroutine</returns>
        private System.Collections.IEnumerator AnimateNotification(float duration)
        {
            // Fade in
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 0f;
                float fadeTime = 0.5f;
                float elapsedTime = 0f;

                while (elapsedTime < fadeTime)
                {
                    elapsedTime += Time.deltaTime;
                    canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / fadeTime);
                    yield return null;
                }

                canvasGroup.alpha = 1f;
            }

            // Wait for display duration
            yield return new WaitForSeconds(duration - 1f); // Account for fade times

            // Fade out
            if (canvasGroup != null)
            {
                float fadeTime = 0.5f;
                float elapsedTime = 0f;

                while (elapsedTime < fadeTime)
                {
                    elapsedTime += Time.deltaTime;
                    canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsedTime / fadeTime);
                    yield return null;
                }

                canvasGroup.alpha = 0f;
            }
        }
    }

    /// <summary>
    /// Achievement progress tracker UI component.
    /// </summary>
    public class AchievementProgressTrackerUI : MonoBehaviour
    {
        [Header("UI Components")]
        public Image achievementIcon;
        public TextMeshProUGUI achievementTitle;
        public Slider progressBar;
        public TextMeshProUGUI progressText;

        /// <summary>
        /// Update progress tracker with achievement data.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        /// <param name="progress">Current progress (0-1)</param>
        public void UpdateProgress(AchievementData achievement, float progress)
        {
            if (achievementIcon != null && achievement.icon != null)
                achievementIcon.sprite = achievement.icon;

            if (achievementTitle != null)
                achievementTitle.text = achievement.title;

            if (progressBar != null)
                progressBar.value = progress;

            if (progressText != null)
                progressText.text = $"{progress * 100:F0}%";
        }
    }

    /// <summary>
    /// Achievement list item UI component.
    /// </summary>
    public class AchievementListItemUI : MonoBehaviour
    {
        [Header("UI Components")]
        public Image achievementIcon;
        public TextMeshProUGUI achievementTitle;
        public TextMeshProUGUI achievementDescription;
        public Slider progressBar;
        public TextMeshProUGUI progressText;
        public GameObject unlockedIndicator;
        public GameObject lockedOverlay;

        /// <summary>
        /// Setup list item with achievement data.
        /// </summary>
        /// <param name="achievement">Achievement data</param>
        /// <param name="isUnlocked">Whether achievement is unlocked</param>
        /// <param name="progress">Current progress (0-1)</param>
        public void Setup(AchievementData achievement, bool isUnlocked, float progress)
        {
            if (achievementIcon != null && achievement.icon != null)
                achievementIcon.sprite = achievement.icon;

            if (achievementTitle != null)
            {
                achievementTitle.text = achievement.isHidden && !isUnlocked ? "???" : achievement.title;
            }

            if (achievementDescription != null)
            {
                string description = achievement.isHidden && !isUnlocked ?
                    achievement.hiddenDescription : achievement.description;
                achievementDescription.text = description;
            }

            if (progressBar != null)
            {
                progressBar.gameObject.SetActive(!isUnlocked);
                progressBar.value = progress;
            }

            if (progressText != null)
            {
                progressText.gameObject.SetActive(!isUnlocked);
                progressText.text = $"{progress * 100:F0}%";
            }

            if (unlockedIndicator != null)
                unlockedIndicator.SetActive(isUnlocked);

            if (lockedOverlay != null)
                lockedOverlay.SetActive(!isUnlocked);
        }
    }
    #endregion
}
