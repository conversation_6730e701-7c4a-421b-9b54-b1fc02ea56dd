# Spirit Whisper Audio System - Cinder of Darkness

## Overview
The Spirit Whisper Audio System provides eerie, spectral audio effects for the RegretAfterKillingSystem in Cinder of Darkness. This system generates and manages 10 unique spirit whisper audio clips that play during regret and haunting moments.

## Generated Assets

### Audio Clips (10 total)
- **spirit_whisper_01** (3.2s) - Sorrowful whisper with low frequency tones
- **spirit_whisper_02** (4.1s) - Regretful whisper with moderate intensity
- **spirit_whisper_03** (5.3s) - Haunting whisper with ethereal harmonics
- **spirit_whisper_04** (3.8s) - Peaceful whisper with gentle modulation
- **spirit_whisper_05** (6.2s) - Ominous whisper with deep reverb
- **spirit_whisper_06** (4.7s) - Melancholic whisper with breath-like texture
- **spirit_whisper_07** (3.5s) - Ethereal whisper with spectral harmonics
- **spirit_whisper_08** (5.8s) - Distant whisper with heavy reverb
- **spirit_whisper_09** (4.4s) - Comforting whisper with warm tones
- **spirit_whisper_10** (6.7s) - Mysterious whisper with complex harmonics

### Asset Organization
```
Assets/Audio/Whispers/
├── spirit_whisper_01.asset
├── spirit_whisper_02.asset
├── ...
├── spirit_whisper_10.asset
├── SpiritWhisperAsset.asset
├── SpiritWhisperGenerator.cs
├── AutoGenerateSpiritWhispers.cs
├── SpiritWhisperTester.cs
└── README_SpiritWhispers.md

Assets/Resources/
└── SpiritWhisperAsset.asset (Runtime copy)
```

## Technical Specifications

### Audio Properties
- **Sample Rate**: 44,100 Hz
- **Channels**: Mono (1 channel)
- **Format**: Unity AudioClip assets
- **Duration Range**: 3.2 - 6.7 seconds
- **Volume Range**: 0.5 - 0.9 intensity
- **Frequency Range**: 140 - 230 Hz base frequency

### Whisper Types
Each whisper has unique characteristics:

1. **Sorrowful** - Lower frequency, enhanced harmonics, extended reverb
2. **Regretful** - Balanced frequency, moderate noise, standard reverb
3. **Haunting** - Higher frequency, strong harmonics, heavy reverb
4. **Peaceful** - Gentle frequency, reduced noise, light reverb
5. **Ominous** - High frequency, intense harmonics, deep reverb
6. **Melancholic** - Slightly low frequency, moderate harmonics, medium reverb
7. **Ethereal** - Elevated frequency, maximum harmonics, extensive reverb
8. **Distant** - Very low frequency, reduced harmonics, maximum reverb
9. **Comforting** - Warm frequency, gentle harmonics, light reverb
10. **Mysterious** - High frequency, strong harmonics, heavy reverb

## Integration with RegretAfterKillingSystem

### Automatic Loading
The RegretAfterKillingSystem automatically loads spirit whispers in this order:
1. Assigned SpiritWhisperAsset in inspector
2. Resources folder: `Resources/SpiritWhisperAsset.asset`
3. Project search for any SpiritWhisperAsset (Editor only)
4. Fallback to legacy spiritWhispers array

### Usage in Code
```csharp
// Play a random spirit whisper
PlaySpiritWhisper();

// Access specific whisper
AudioClip whisper = spiritWhisperAsset.GetWhisper(index);

// Get random whisper with pitch variation
AudioClip randomWhisper = spiritWhisperAsset.GetRandomWhisper();
float randomPitch = spiritWhisperAsset.GetRandomPitch();
```

### Audio Settings
- **Default Volume**: 0.3 (30%)
- **Pitch Variation**: ±0.2 (80% - 120%)
- **Cooldown Time**: 2 seconds between whispers
- **3D Audio**: 70% spatial blend
- **Max Distance**: 15 units
- **Rolloff**: Logarithmic

## Auto-Generation System

### Automatic Creation
The `AutoGenerateSpiritWhispers` class automatically generates all spirit whisper assets when:
- The project loads and no SpiritWhisperAsset exists
- The existing asset is empty or corrupted
- Manual generation is triggered

### Manual Generation
Use the Unity menu: `Cinder of Darkness > Generate Spirit Whispers`

### Generation Process
1. Creates `/Assets/Audio/Whispers/` directory
2. Generates 10 unique AudioClip assets
3. Creates SpiritWhisperAsset ScriptableObject
4. Copies asset to Resources folder for runtime loading
5. Refreshes AssetDatabase

## Testing System

### SpiritWhisperTester Component
Attach `SpiritWhisperTester` to any GameObject to test the system:

**Controls:**
- **T Key**: Play random spirit whisper
- **1-3 Keys**: Play specific whispers (indices 0-2)

**Features:**
- Real-time debug information
- 3D visualization sphere
- Volume and pitch testing
- Statistics tracking

**Inspector Settings:**
- Test volume control
- Pitch variation toggle
- Debug display options
- Visualization controls

## Audio Generation Algorithm

### Core Components
1. **Whisper Tone**: Low-frequency sine waves with modulation
2. **Spectral Harmonics**: Ethereal overtones at 1.5x, 2.3x, 3.7x base frequency
3. **Breath Modulation**: Amplitude variation simulating breathing
4. **Eerie Reverb**: Multiple delayed echoes with decay
5. **Envelope**: Smooth fade-in/fade-out curves

### Type-Specific Modifiers
Each whisper type applies unique modifiers to:
- Base frequency (0.6x - 1.4x)
- Harmonic intensity (0.5x - 2.0x)
- Noise level (0.1x - 0.9x)
- Reverb depth (1.0x - 3.0x)

## Performance Considerations

### Memory Usage
- Total asset size: ~2-3 MB
- Individual clips: 200-400 KB each
- Runtime memory: Minimal (clips loaded on-demand)

### CPU Usage
- Audio generation: Editor-only, no runtime cost
- Playback: Standard Unity AudioSource overhead
- 3D positioning: Logarithmic rolloff for efficiency

### Optimization Features
- Cooldown system prevents audio spam
- Temporary AudioSource cleanup
- Automatic pitch variation reduces repetition
- Spatial audio for immersion without performance cost

## Troubleshooting

### Common Issues

**"SpiritWhisperAsset not found!"**
- Solution: Use menu `Cinder of Darkness > Generate Spirit Whispers`
- Or manually assign the asset in RegretAfterKillingSystem inspector

**"No whisper clips available!"**
- Check if SpiritWhisperAsset.spiritWhispers array is populated
- Regenerate assets if array is empty

**Audio not playing**
- Verify AudioSource component exists
- Check volume levels (default 0.3)
- Ensure cooldown period has passed (2 seconds)

**Poor audio quality**
- Regenerate assets for fresh audio clips
- Adjust pitch variation in SpiritWhisperAsset
- Check Unity audio quality settings

### Debug Tools
- Use SpiritWhisperTester for comprehensive testing
- Enable debug info in RegretAfterKillingSystem
- Check Unity Console for detailed logging
- Monitor audio statistics in tester component

## Future Enhancements

### Planned Features
- Dynamic whisper generation based on player actions
- Emotional intensity scaling
- Localization support for different languages
- Advanced 3D audio positioning
- Integration with other narrative systems

### Customization Options
- Additional whisper types
- Configurable generation parameters
- Custom audio processing effects
- Player preference settings
- Accessibility options

## Credits
Generated automatically by Cinder of Darkness Audio Asset Generator
Part of the immersive narrative structure for enhanced player emotional engagement
Designed for dark fantasy atmosphere with respectful handling of death and regret themes
