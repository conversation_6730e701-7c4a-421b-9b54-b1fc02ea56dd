using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Base class for all UI panels in Cinder of Darkness.
    /// Provides common functionality for panel management, navigation, and animation.
    /// </summary>
    public abstract class UIPanel : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Panel Settings")]
        [SerializeField] protected string panelId;
        [SerializeField] protected bool canGoBack = true;
        [SerializeField] protected bool hasConfirmAction = true;
        [SerializeField] protected bool pauseGameWhenOpen = false;

        [Header("Navigation")]
        [SerializeField] protected Selectable[] selectableElements;
        [SerializeField] protected Selectable defaultSelectedElement;
        [SerializeField] protected bool enableWrapNavigation = true;

        [Header("Animation")]
        [SerializeField] protected CanvasGroup canvasGroup;
        [SerializeField] protected RectTransform panelTransform;
        [SerializeField] protected AnimationType animationType = AnimationType.Fade;
        [SerializeField] protected float animationDuration = 0.3f;
        [SerializeField] protected AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Audio")]
        [SerializeField] protected AudioClip openSound;
        [SerializeField] protected AudioClip closeSound;
        #endregion

        #region Public Properties
        public string PanelId => panelId;
        public bool CanGoBack => canGoBack;
        public bool HasConfirmAction => hasConfirmAction;
        public bool IsVisible { get; protected set; }
        public Selectable CurrentSelectedElement { get; protected set; }
        #endregion

        #region Protected Fields
        protected List<TextMeshProUGUI> textElements = new List<TextMeshProUGUI>();
        protected List<Image> imageElements = new List<Image>();
        protected List<Button> buttonElements = new List<Button>();
        protected int currentSelectionIndex = 0;
        protected bool isAnimating = false;
        #endregion

        #region Events
        public System.Action<UIPanel> OnPanelOpened;
        public System.Action<UIPanel> OnPanelClosed;
        public System.Action<Selectable> OnSelectionChanged;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize panel components.
        /// </summary>
        protected virtual void Awake()
        {
            if (canvasGroup == null)
                canvasGroup = GetComponent<CanvasGroup>();

            if (panelTransform == null)
                panelTransform = GetComponent<RectTransform>();

            CacheUIElements();
        }

        /// <summary>
        /// Setup panel after initialization.
        /// </summary>
        protected virtual void Start()
        {
            SetupNavigation();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the panel.
        /// </summary>
        public virtual void Initialize()
        {
            CacheUIElements();
            SetupNavigation();
            SetupEventListeners();
        }

        /// <summary>
        /// Cache all UI elements for performance and accessibility.
        /// </summary>
        protected virtual void CacheUIElements()
        {
            textElements.Clear();
            imageElements.Clear();
            buttonElements.Clear();

            // Cache text elements
            TextMeshProUGUI[] texts = GetComponentsInChildren<TextMeshProUGUI>(true);
            textElements.AddRange(texts);

            // Cache image elements
            Image[] images = GetComponentsInChildren<Image>(true);
            imageElements.AddRange(images);

            // Cache button elements
            Button[] buttons = GetComponentsInChildren<Button>(true);
            buttonElements.AddRange(buttons);

            // Cache selectable elements if not manually assigned
            if (selectableElements == null || selectableElements.Length == 0)
            {
                selectableElements = GetComponentsInChildren<Selectable>(true);
            }
        }

        /// <summary>
        /// Setup navigation between UI elements.
        /// </summary>
        protected virtual void SetupNavigation()
        {
            if (selectableElements == null || selectableElements.Length == 0) return;

            for (int i = 0; i < selectableElements.Length; i++)
            {
                Selectable current = selectableElements[i];
                if (current == null) continue;

                Navigation nav = current.navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up navigation links
                if (enableWrapNavigation)
                {
                    nav.selectOnUp = selectableElements[(i - 1 + selectableElements.Length) % selectableElements.Length];
                    nav.selectOnDown = selectableElements[(i + 1) % selectableElements.Length];
                }
                else
                {
                    nav.selectOnUp = i > 0 ? selectableElements[i - 1] : null;
                    nav.selectOnDown = i < selectableElements.Length - 1 ? selectableElements[i + 1] : null;
                }

                current.navigation = nav;
            }

            // Set default selection
            if (defaultSelectedElement == null && selectableElements.Length > 0)
            {
                defaultSelectedElement = selectableElements[0];
            }
        }

        /// <summary>
        /// Setup event listeners for UI elements.
        /// </summary>
        protected virtual void SetupEventListeners()
        {
            foreach (Button button in buttonElements)
            {
                if (button != null)
                {
                    button.onClick.AddListener(() => OnButtonClicked(button));
                }
            }
        }
        #endregion

        #region Panel Visibility
        /// <summary>
        /// Show the panel.
        /// </summary>
        /// <param name="animate">Whether to animate the transition</param>
        public virtual void Show(bool animate = true)
        {
            gameObject.SetActive(true);
            IsVisible = true;

            if (animate && !isAnimating)
            {
                StartCoroutine(ShowAnimated(animationDuration));
            }
            else
            {
                SetVisibility(true);
                OnPanelShown();
            }
        }

        /// <summary>
        /// Hide the panel.
        /// </summary>
        /// <param name="animate">Whether to animate the transition</param>
        public virtual void Hide(bool animate = true)
        {
            if (animate && !isAnimating)
            {
                StartCoroutine(HideAnimated(animationDuration));
            }
            else
            {
                SetVisibility(false);
                OnPanelHidden();
            }
        }

        /// <summary>
        /// Show panel with animation.
        /// </summary>
        /// <param name="duration">Animation duration</param>
        /// <returns>Animation coroutine</returns>
        public virtual System.Collections.IEnumerator ShowAnimated(float duration)
        {
            isAnimating = true;
            gameObject.SetActive(true);

            yield return StartCoroutine(AnimatePanel(true, duration));

            OnPanelShown();
            isAnimating = false;
        }

        /// <summary>
        /// Hide panel with animation.
        /// </summary>
        /// <param name="duration">Animation duration</param>
        /// <returns>Animation coroutine</returns>
        public virtual System.Collections.IEnumerator HideAnimated(float duration)
        {
            isAnimating = true;

            yield return StartCoroutine(AnimatePanel(false, duration));

            OnPanelHidden();
            isAnimating = false;
        }

        /// <summary>
        /// Animate panel visibility.
        /// </summary>
        /// <param name="show">True to show, false to hide</param>
        /// <param name="duration">Animation duration</param>
        /// <returns>Animation coroutine</returns>
        protected virtual System.Collections.IEnumerator AnimatePanel(bool show, float duration)
        {
            float startValue = show ? 0f : 1f;
            float endValue = show ? 1f : 0f;
            float elapsedTime = 0f;

            Vector3 startScale = show ? Vector3.zero : Vector3.one;
            Vector3 endScale = show ? Vector3.one : Vector3.zero;

            while (elapsedTime < duration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float t = animationCurve.Evaluate(elapsedTime / duration);

                switch (animationType)
                {
                    case AnimationType.Fade:
                        if (canvasGroup != null)
                        {
                            canvasGroup.alpha = Mathf.Lerp(startValue, endValue, t);
                        }
                        break;

                    case AnimationType.Scale:
                        if (panelTransform != null)
                        {
                            panelTransform.localScale = Vector3.Lerp(startScale, endScale, t);
                        }
                        break;

                    case AnimationType.FadeAndScale:
                        if (canvasGroup != null)
                        {
                            canvasGroup.alpha = Mathf.Lerp(startValue, endValue, t);
                        }
                        if (panelTransform != null)
                        {
                            panelTransform.localScale = Vector3.Lerp(startScale, endScale, t);
                        }
                        break;
                }

                yield return null;
            }

            // Ensure final values
            switch (animationType)
            {
                case AnimationType.Fade:
                    if (canvasGroup != null)
                    {
                        canvasGroup.alpha = endValue;
                    }
                    break;

                case AnimationType.Scale:
                    if (panelTransform != null)
                    {
                        panelTransform.localScale = endScale;
                    }
                    break;

                case AnimationType.FadeAndScale:
                    if (canvasGroup != null)
                    {
                        canvasGroup.alpha = endValue;
                    }
                    if (panelTransform != null)
                    {
                        panelTransform.localScale = endScale;
                    }
                    break;
            }
        }

        /// <summary>
        /// Set panel visibility immediately.
        /// </summary>
        /// <param name="visible">True to show, false to hide</param>
        protected virtual void SetVisibility(bool visible)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = visible ? 1f : 0f;
                canvasGroup.interactable = visible;
                canvasGroup.blocksRaycasts = visible;
            }

            if (panelTransform != null)
            {
                panelTransform.localScale = visible ? Vector3.one : Vector3.zero;
            }

            IsVisible = visible;

            if (!visible)
            {
                gameObject.SetActive(false);
            }
        }
        #endregion

        #region Navigation
        /// <summary>
        /// Navigate up in the UI.
        /// </summary>
        public virtual void NavigateUp()
        {
            NavigateToElement(-1);
        }

        /// <summary>
        /// Navigate down in the UI.
        /// </summary>
        public virtual void NavigateDown()
        {
            NavigateToElement(1);
        }

        /// <summary>
        /// Navigate left in the UI.
        /// </summary>
        public virtual void NavigateLeft()
        {
            // Override in derived classes for horizontal navigation
        }

        /// <summary>
        /// Navigate right in the UI.
        /// </summary>
        public virtual void NavigateRight()
        {
            // Override in derived classes for horizontal navigation
        }

        /// <summary>
        /// Navigate to a specific element by index offset.
        /// </summary>
        /// <param name="indexOffset">Index offset (-1 for up, 1 for down)</param>
        protected virtual void NavigateToElement(int indexOffset)
        {
            if (selectableElements == null || selectableElements.Length == 0) return;

            int newIndex = currentSelectionIndex + indexOffset;

            if (enableWrapNavigation)
            {
                newIndex = (newIndex + selectableElements.Length) % selectableElements.Length;
            }
            else
            {
                newIndex = Mathf.Clamp(newIndex, 0, selectableElements.Length - 1);
            }

            SelectElement(newIndex);
        }

        /// <summary>
        /// Select a UI element by index.
        /// </summary>
        /// <param name="index">Element index</param>
        protected virtual void SelectElement(int index)
        {
            if (selectableElements == null || index < 0 || index >= selectableElements.Length) return;

            currentSelectionIndex = index;
            CurrentSelectedElement = selectableElements[index];

            if (CurrentSelectedElement != null)
            {
                CurrentSelectedElement.Select();
                OnSelectionChanged?.Invoke(CurrentSelectedElement);
            }
        }

        /// <summary>
        /// Select the default element.
        /// </summary>
        public virtual void SelectDefaultElement()
        {
            if (defaultSelectedElement != null)
            {
                defaultSelectedElement.Select();
                CurrentSelectedElement = defaultSelectedElement;

                // Find index of default element
                for (int i = 0; i < selectableElements.Length; i++)
                {
                    if (selectableElements[i] == defaultSelectedElement)
                    {
                        currentSelectionIndex = i;
                        break;
                    }
                }
            }
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Called when panel is shown.
        /// </summary>
        protected virtual void OnPanelShown()
        {
            SelectDefaultElement();
            
            if (pauseGameWhenOpen)
            {
                Time.timeScale = 0f;
            }

            OnPanelOpened?.Invoke(this);
        }

        /// <summary>
        /// Called when panel is hidden.
        /// </summary>
        protected virtual void OnPanelHidden()
        {
            IsVisible = false;
            
            if (pauseGameWhenOpen)
            {
                Time.timeScale = 1f;
            }

            OnPanelClosed?.Invoke(this);
        }

        /// <summary>
        /// Handle button click events.
        /// </summary>
        /// <param name="button">Clicked button</param>
        protected virtual void OnButtonClicked(Button button)
        {
            // Override in derived classes
        }
        #endregion

        #region Accessibility
        /// <summary>
        /// Apply accessibility settings to the panel.
        /// </summary>
        /// <param name="highContrast">Enable high contrast</param>
        /// <param name="textScale">Text scale multiplier</param>
        /// <param name="colorBlindType">Color blind support type</param>
        public virtual void ApplyAccessibilitySettings(bool highContrast, float textScale, ColorBlindType colorBlindType)
        {
            // Apply text scaling
            foreach (TextMeshProUGUI text in textElements)
            {
                if (text != null)
                {
                    text.fontSize *= textScale;
                }
            }

            // Apply high contrast if enabled
            if (highContrast)
            {
                ApplyHighContrastColors();
            }

            // Apply color blind support
            if (colorBlindType != ColorBlindType.None)
            {
                ApplyColorBlindSupport(colorBlindType);
            }
        }

        /// <summary>
        /// Apply high contrast colors.
        /// </summary>
        protected virtual void ApplyHighContrastColors()
        {
            foreach (TextMeshProUGUI text in textElements)
            {
                if (text != null)
                {
                    text.color = Color.white;
                }
            }

            foreach (Image image in imageElements)
            {
                if (image != null && image.type == Image.Type.Simple)
                {
                    image.color = Color.black;
                }
            }
        }

        /// <summary>
        /// Apply color blind support.
        /// </summary>
        /// <param name="colorBlindType">Color blind type</param>
        protected virtual void ApplyColorBlindSupport(ColorBlindType colorBlindType)
        {
            // Override in derived classes for specific color adjustments
        }
        #endregion

        #region Abstract Methods
        /// <summary>
        /// Handle confirm action (Enter/A button).
        /// </summary>
        public abstract void OnConfirm();

        /// <summary>
        /// Handle cancel action (Escape/B button).
        /// </summary>
        public abstract void OnCancel();
        #endregion
    }

    #region Enums
    /// <summary>
    /// Panel animation types.
    /// </summary>
    public enum AnimationType
    {
        None,
        Fade,
        Scale,
        FadeAndScale,
        Slide
    }
    #endregion
}
