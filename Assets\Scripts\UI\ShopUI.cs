using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using CinderOfDarkness.Economy;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Shop UI for Cinder of Darkness.
    /// Handles buying, selling, and trading with merchants.
    /// </summary>
    public class ShopUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject shopPanel;
        [SerializeField] private TextMeshProUGUI merchantNameText;
        [SerializeField] private TextMeshProUGUI playerCurrencyText;
        [SerializeField] private Button closeButton;

        [Header("Shop Tabs")]
        [SerializeField] private Button buyTabButton;
        [SerializeField] private Button sellTabButton;
        [SerializeField] private Image tabIndicator;

        [Header("Item Lists")]
        [SerializeField] private Transform buyItemsContainer;
        [SerializeField] private Transform sellItemsContainer;
        [SerializeField] private GameObject shopItemPrefab;
        [SerializeField] private ScrollRect buyScrollRect;
        [SerializeField] private ScrollRect sellScrollRect;

        [Header("Transaction")]
        [SerializeField] private GameObject transactionPanel;
        [SerializeField] private Image transactionItemIcon;
        [SerializeField] private TextMeshProUGUI transactionItemName;
        [SerializeField] private TextMeshProUGUI transactionPriceText;
        [SerializeField] private Slider quantitySlider;
        [SerializeField] private TextMeshProUGUI quantityText;
        [SerializeField] private Button confirmButton;
        [SerializeField] private Button cancelButton;

        [Header("Audio")]
        [SerializeField] private AudioClip shopOpenSound;
        [SerializeField] private AudioClip shopCloseSound;
        [SerializeField] private AudioClip buySound;
        [SerializeField] private AudioClip sellSound;
        [SerializeField] private AudioClip tabSwitchSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private EconomySystem economySystem;
        private PlayerInventory playerInventory;
        private Shop currentShop;
        private List<GameObject> buyItems = new List<GameObject>();
        private List<GameObject> sellItems = new List<GameObject>();
        private ShopTab currentTab = ShopTab.Buy;
        private ShopItem selectedItem;
        private bool isVisible = false;
        private bool isBuying = true;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
            UpdateCurrencyDisplay();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            economySystem = EconomySystem.Instance;
            playerInventory = FindObjectOfType<PlayerInventory>();
        }

        private void SetupUI()
        {
            if (shopPanel != null)
                shopPanel.SetActive(false);

            if (transactionPanel != null)
                transactionPanel.SetActive(false);

            UpdateTabButtons();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseShop);

            if (buyTabButton != null)
                buyTabButton.onClick.AddListener(() => SetTab(ShopTab.Buy));

            if (sellTabButton != null)
                sellTabButton.onClick.AddListener(() => SetTab(ShopTab.Sell));

            if (confirmButton != null)
                confirmButton.onClick.AddListener(ConfirmTransaction);

            if (cancelButton != null)
                cancelButton.onClick.AddListener(CancelTransaction);

            if (quantitySlider != null)
                quantitySlider.onValueChanged.AddListener(OnQuantityChanged);

            // Subscribe to economy events
            if (economySystem != null)
            {
                economySystem.OnTransactionCompleted += OnTransactionCompleted;
                economySystem.OnCurrencyChanged += OnCurrencyChanged;
            }
        }
        #endregion

        #region Shop Management
        public void OpenShop(Shop shop)
        {
            currentShop = shop;
            
            if (shopPanel != null)
            {
                shopPanel.SetActive(true);
                isVisible = true;
                
                if (merchantNameText != null)
                    merchantNameText.text = shop.shopName;

                RefreshShopItems();
                PlaySound(shopOpenSound);
            }
        }

        public void CloseShop()
        {
            if (shopPanel != null)
            {
                shopPanel.SetActive(false);
                isVisible = false;
                PlaySound(shopCloseSound);
            }

            if (transactionPanel != null)
                transactionPanel.SetActive(false);

            currentShop = null;
        }

        private void HandleInput()
        {
            if (!isVisible) return;

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                CloseShop();
            }

            if (Input.GetKeyDown(KeyCode.Tab))
            {
                SetTab(currentTab == ShopTab.Buy ? ShopTab.Sell : ShopTab.Buy);
            }
        }

        private void SetTab(ShopTab tab)
        {
            currentTab = tab;
            UpdateTabButtons();
            UpdateItemDisplay();
            PlaySound(tabSwitchSound);
        }

        private void UpdateTabButtons()
        {
            if (buyTabButton != null)
                buyTabButton.interactable = currentTab != ShopTab.Buy;

            if (sellTabButton != null)
                sellTabButton.interactable = currentTab != ShopTab.Sell;

            if (tabIndicator != null)
            {
                tabIndicator.color = currentTab == ShopTab.Buy ? Color.green : Color.yellow;
            }

            UpdateItemDisplay();
        }

        private void UpdateItemDisplay()
        {
            if (buyScrollRect != null)
                buyScrollRect.gameObject.SetActive(currentTab == ShopTab.Buy);

            if (sellScrollRect != null)
                sellScrollRect.gameObject.SetActive(currentTab == ShopTab.Sell);
        }
        #endregion

        #region Item Display
        private void RefreshShopItems()
        {
            ClearItemLists();

            if (currentShop == null) return;

            // Populate buy items
            foreach (var item in currentShop.inventory)
            {
                CreateBuyItem(item);
            }

            // Populate sell items (player inventory)
            if (playerInventory != null)
            {
                foreach (var item in playerInventory.GetAllItems())
                {
                    CreateSellItem(item);
                }
            }
        }

        private void CreateBuyItem(ShopItem item)
        {
            if (shopItemPrefab == null || buyItemsContainer == null) return;

            GameObject itemObj = Instantiate(shopItemPrefab, buyItemsContainer);
            var shopItemUI = itemObj.GetComponent<ShopItemUI>();

            if (shopItemUI != null)
            {
                float adjustedPrice = economySystem?.GetAdjustedPrice(item.basePrice, currentShop.region) ?? item.basePrice;
                shopItemUI.Setup(item, adjustedPrice, true, this);
                buyItems.Add(itemObj);
            }
        }

        private void CreateSellItem(InventoryItem item)
        {
            if (shopItemPrefab == null || sellItemsContainer == null) return;

            GameObject itemObj = Instantiate(shopItemPrefab, sellItemsContainer);
            var shopItemUI = itemObj.GetComponent<ShopItemUI>();

            if (shopItemUI != null)
            {
                // Convert inventory item to shop item for display
                var shopItem = new ShopItem
                {
                    itemId = item.itemId,
                    itemName = item.itemName,
                    description = item.description,
                    icon = item.icon,
                    basePrice = item.value,
                    stock = item.quantity
                };

                float sellPrice = item.value * 0.6f; // 60% of base value
                shopItemUI.Setup(shopItem, sellPrice, false, this);
                sellItems.Add(itemObj);
            }
        }

        private void ClearItemLists()
        {
            foreach (var item in buyItems)
            {
                if (item != null)
                    Destroy(item);
            }
            buyItems.Clear();

            foreach (var item in sellItems)
            {
                if (item != null)
                    Destroy(item);
            }
            sellItems.Clear();
        }

        public void SelectItem(ShopItem item, float price, bool buying)
        {
            selectedItem = item;
            isBuying = buying;
            ShowTransactionPanel(item, price);
        }

        private void ShowTransactionPanel(ShopItem item, float price)
        {
            if (transactionPanel != null)
            {
                transactionPanel.SetActive(true);

                if (transactionItemIcon != null)
                    transactionItemIcon.sprite = item.icon;

                if (transactionItemName != null)
                    transactionItemName.text = item.itemName;

                if (transactionPriceText != null)
                    transactionPriceText.text = $"{(isBuying ? "Buy" : "Sell")} Price: {price:F0}";

                if (quantitySlider != null)
                {
                    quantitySlider.minValue = 1;
                    quantitySlider.maxValue = isBuying ? item.stock : GetPlayerItemQuantity(item.itemId);
                    quantitySlider.value = 1;
                }

                UpdateTransactionDisplay();
            }
        }

        private void UpdateTransactionDisplay()
        {
            if (quantitySlider != null && quantityText != null)
            {
                int quantity = Mathf.RoundToInt(quantitySlider.value);
                quantityText.text = quantity.ToString();
            }
        }

        private void OnQuantityChanged(float value)
        {
            UpdateTransactionDisplay();
        }

        private int GetPlayerItemQuantity(string itemId)
        {
            if (playerInventory == null) return 0;
            return playerInventory.GetItemCount(itemId);
        }

        private void UpdateCurrencyDisplay()
        {
            if (economySystem == null || playerCurrencyText == null) return;

            int gold = economySystem.GetCurrency("gold");
            int silver = economySystem.GetCurrency("silver");
            
            playerCurrencyText.text = $"Gold: {gold} | Silver: {silver}";
        }
        #endregion

        #region Transactions
        private void ConfirmTransaction()
        {
            if (selectedItem == null || economySystem == null) return;

            int quantity = Mathf.RoundToInt(quantitySlider.value);
            
            if (isBuying)
            {
                PerformBuy(selectedItem, quantity);
            }
            else
            {
                PerformSell(selectedItem, quantity);
            }

            CancelTransaction();
        }

        private void CancelTransaction()
        {
            if (transactionPanel != null)
                transactionPanel.SetActive(false);

            selectedItem = null;
        }

        private void PerformBuy(ShopItem item, int quantity)
        {
            float totalPrice = item.basePrice * quantity;
            
            if (economySystem.GetCurrency("gold") >= totalPrice)
            {
                // Deduct currency
                economySystem.RemoveCurrency("gold", Mathf.RoundToInt(totalPrice));
                
                // Add item to inventory
                if (playerInventory != null)
                {
                    var inventoryItem = CreateInventoryItem(item);
                    inventoryItem.quantity = quantity;
                    playerInventory.AddItem(inventoryItem);
                }

                // Update shop stock
                item.stock -= quantity;
                
                PlaySound(buySound);
                RefreshShopItems();
            }
            else
            {
                Debug.Log("Not enough gold!");
            }
        }

        private void PerformSell(ShopItem item, int quantity)
        {
            if (playerInventory != null && playerInventory.GetItemCount(item.itemId) >= quantity)
            {
                // Remove item from inventory
                playerInventory.RemoveItem(item.itemId, quantity);
                
                // Add currency
                float sellPrice = item.basePrice * 0.6f * quantity;
                economySystem.AddCurrency("gold", Mathf.RoundToInt(sellPrice));
                
                PlaySound(sellSound);
                RefreshShopItems();
            }
            else
            {
                Debug.Log("Not enough items to sell!");
            }
        }

        private InventoryItem CreateInventoryItem(ShopItem shopItem)
        {
            return new InventoryItem
            {
                itemId = shopItem.itemId,
                itemName = shopItem.itemName,
                description = shopItem.description,
                icon = shopItem.icon,
                value = Mathf.RoundToInt(shopItem.basePrice),
                quantity = 1
            };
        }
        #endregion

        #region Event Handlers
        private void OnTransactionCompleted(string transactionType, string itemId, int amount)
        {
            if (isVisible)
            {
                RefreshShopItems();
            }
        }

        private void OnCurrencyChanged(string currencyType, int newAmount)
        {
            // Currency display is updated in Update()
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class ShopItemUI : MonoBehaviour
    {
        [SerializeField] private Image itemIcon;
        [SerializeField] private TextMeshProUGUI itemNameText;
        [SerializeField] private TextMeshProUGUI priceText;
        [SerializeField] private TextMeshProUGUI stockText;
        [SerializeField] private Button selectButton;

        private ShopItem item;
        private float price;
        private bool isBuyItem;
        private ShopUI shopUI;

        public void Setup(ShopItem shopItem, float itemPrice, bool buying, ShopUI shop)
        {
            item = shopItem;
            price = itemPrice;
            isBuyItem = buying;
            shopUI = shop;

            if (itemIcon != null)
                itemIcon.sprite = item.icon;

            if (itemNameText != null)
                itemNameText.text = item.itemName;

            if (priceText != null)
                priceText.text = $"{price:F0}g";

            if (stockText != null)
                stockText.text = $"Stock: {item.stock}";

            if (selectButton != null)
                selectButton.onClick.AddListener(() => shopUI.SelectItem(item, price, isBuyItem));
        }
    }

    public enum ShopTab
    {
        Buy,
        Sell
    }
    #endregion
}
