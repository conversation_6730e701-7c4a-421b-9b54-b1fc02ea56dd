using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 3f;
    public float runSpeed = 6f;
    public float jumpForce = 8f;
    public float mouseSensitivity = 2f;

    [Header("Camera Settings")]
    public Camera playerCamera;
    public Transform cameraHolder;
    public bool isFirstPerson = false;
    public Vector3 thirdPersonOffset = new Vector3(0, 2, -5);
    public Vector3 firstPersonOffset = new Vector3(0, 1.8f, 0);

    [Header("Combat Settings")]
    public Transform weaponHolder;
    public LayerMask enemyLayer;
    public float attackRange = 2f;

    private Rigidbody rb;
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private bool isGrounded;
    private float xRotation = 0f;
    private bool isRunning;

    // Input variables
    private Vector2 moveInput;
    private Vector2 lookInput;
    private bool jumpInput;
    private bool attackInput;
    private bool magicInput;
    private bool perspectiveToggle;

    // Input Actions
    private PlayerInput playerInput;
    private InputAction moveAction;
    private InputAction lookAction;
    private InputAction jumpAction;
    private InputAction attackAction;
    private InputAction magicAction;
    private InputAction perspectiveAction;
    private InputAction runAction;

    void Start()
    {
        rb = GetComponent<Rigidbody>();
        playerStats = GetComponent<PlayerStats>();
        playerCombat = GetComponent<PlayerCombat>();

        // Setup Input System
        SetupInputActions();

        // Lock cursor to center of screen
        Cursor.lockState = CursorLockMode.Locked;

        SetupCamera();
    }

    void SetupInputActions()
    {
        playerInput = GetComponent<PlayerInput>();
        if (playerInput != null)
        {
            var actionMap = playerInput.actions.FindActionMap("Gameplay");
            if (actionMap != null)
            {
                moveAction = actionMap.FindAction("Move");
                lookAction = actionMap.FindAction("Look");
                jumpAction = actionMap.FindAction("Jump");
                attackAction = actionMap.FindAction("Attack");
                magicAction = actionMap.FindAction("Block"); // Using Block as magic for now
                runAction = actionMap.FindAction("Run");

                // Subscribe to input events
                if (jumpAction != null) jumpAction.performed += OnJump;
                if (attackAction != null) attackAction.performed += OnAttack;
                if (magicAction != null) magicAction.performed += OnMagic;
            }
        }
    }

    void Update()
    {
        HandleInput();
        HandleMouseLook();
        HandlePerspectiveToggle();
    }

    void FixedUpdate()
    {
        HandleMovement();
    }

    void HandleInput()
    {
        // Read input from new Input System
        if (moveAction != null)
            moveInput = moveAction.ReadValue<Vector2>();

        if (lookAction != null)
            lookInput = lookAction.ReadValue<Vector2>();

        if (runAction != null)
            isRunning = runAction.IsPressed();

        // Fallback to old input system if new system not available
        if (moveAction == null)
        {
            moveInput = new Vector2(Input.GetAxis("Horizontal"), Input.GetAxis("Vertical"));
        }

        if (lookAction == null)
        {
            lookInput = new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));
        }

        // Perspective toggle (fallback)
        perspectiveToggle = Input.GetKeyDown(KeyCode.V);

        if (runAction == null)
        {
            isRunning = Input.GetKey(KeyCode.LeftShift);
        }
    }

    void HandleMovement()
    {
        // Check if grounded
        isGrounded = Physics.Raycast(transform.position, Vector3.down, 1.1f);

        // Calculate movement using new input
        Vector3 direction = transform.right * moveInput.x + transform.forward * moveInput.y;
        float currentSpeed = isRunning ? runSpeed : walkSpeed;

        // Apply movement
        Vector3 moveVelocity = direction * currentSpeed;
        rb.velocity = new Vector3(moveVelocity.x, rb.velocity.y, moveVelocity.z);

        // Jumping handled by input events now

        // Consume stamina when running
        if (isRunning && direction.magnitude > 0)
        {
            if (playerStats != null)
                playerStats.ConsumeStamina(Time.fixedDeltaTime * 10f);
        }
    }

    void HandleMouseLook()
    {
        float mouseX = lookInput.x * mouseSensitivity;
        float mouseY = lookInput.y * mouseSensitivity;

        // Rotate the player body horizontally
        transform.Rotate(Vector3.up * mouseX);

        // Rotate the camera vertically
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -90f, 90f);
        if (cameraHolder != null)
            cameraHolder.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }

    void HandlePerspectiveToggle()
    {
        if (perspectiveToggle)
        {
            isFirstPerson = !isFirstPerson;
            SetupCamera();
        }
    }

    void SetupCamera()
    {
        if (isFirstPerson)
        {
            cameraHolder.localPosition = firstPersonOffset;
            playerCamera.fieldOfView = 75f;
        }
        else
        {
            cameraHolder.localPosition = thirdPersonOffset;
            playerCamera.fieldOfView = 60f;
        }
    }

    public bool IsFirstPerson()
    {
        return isFirstPerson;
    }

    public Vector3 GetCameraForward()
    {
        return playerCamera != null ? playerCamera.transform.forward : transform.forward;
    }

    // Input Event Handlers
    void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed && isGrounded)
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
    }

    void OnAttack(InputAction.CallbackContext context)
    {
        if (context.performed && playerStats != null && playerStats.CanAttack())
        {
            if (playerCombat != null)
                playerCombat.PerformAttack();
        }
    }

    void OnMagic(InputAction.CallbackContext context)
    {
        if (context.performed && playerStats != null && playerStats.CanCastMagic())
        {
            if (playerCombat != null)
                playerCombat.CastMagic();
        }
    }

    void OnDestroy()
    {
        // Unsubscribe from input events
        if (jumpAction != null) jumpAction.performed -= OnJump;
        if (attackAction != null) attackAction.performed -= OnAttack;
        if (magicAction != null) magicAction.performed -= OnMagic;
    }
}
