using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Handles player movement, camera control, and input processing for The Cinderborn character.
/// Supports both keyboard/mouse and gamepad input with smooth movement and camera controls.
/// </summary>
public class PlayerController : MonoBehaviour
{
    #region Serialized Fields
    [Header("Movement Settings")]
    [SerializeField] private float walkSpeed = 3f;
    [SerializeField] private float runSpeed = 6f;
    [SerializeField] private float jumpForce = 8f;
    [SerializeField] private float mouseSensitivity = 2f;

    [Header("Camera Settings")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private Transform cameraHolder;
    [SerializeField] private bool isFirstPerson;
    [SerializeField] private Vector3 thirdPersonOffset = new Vector3(0, 2, -5);
    [SerializeField] private Vector3 firstPersonOffset = new Vector3(0, 1.8f, 0);

    [Head<PERSON>("Combat Settings")]
    [SerializeField] private Transform weaponHolder;
    [SerializeField] private LayerMask enemyLayer;
    [SerializeField] private float attackRange = 2f;
    #endregion

    #region Private Fields
    // Cached components
    private Rigidbody rigidBody;
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;

    // Movement state
    private bool isGrounded;
    private float xRotation;
    private bool isRunning;
    private bool movementEnabled = true;

    // Input state
    private Vector2 moveInput;
    private Vector2 lookInput;
    private bool perspectiveToggle;

    // Input Actions
    private PlayerInput playerInput;
    private InputAction moveAction;
    private InputAction lookAction;
    private InputAction jumpAction;
    private InputAction attackAction;
    private InputAction magicAction;
    private InputAction runAction;
    #endregion

    #region Constants
    private const float GroundCheckDistance = 1.1f;
    private const float StaminaConsumptionRate = 10f;
    private const float FirstPersonFOV = 75f;
    private const float ThirdPersonFOV = 60f;
    private const float VerticalLookLimit = 90f;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize player controller components and input system.
    /// </summary>
    private void Start()
    {
        CacheComponents();
        SetupInputActions();
        InitializeCamera();
    }

    /// <summary>
    /// Handle input processing and camera updates.
    /// </summary>
    private void Update()
    {
        if (!movementEnabled) return;

        HandleInput();
        HandleMouseLook();
        HandlePerspectiveToggle();
    }

    /// <summary>
    /// Handle physics-based movement in FixedUpdate for consistent behavior.
    /// </summary>
    private void FixedUpdate()
    {
        if (!movementEnabled) return;

        HandleMovement();
    }

    /// <summary>
    /// Clean up input event subscriptions.
    /// </summary>
    private void OnDestroy()
    {
        UnsubscribeFromInputEvents();
    }
    #endregion

    #region Initialization
    /// <summary>
    /// Cache frequently used components for performance.
    /// </summary>
    private void CacheComponents()
    {
        rigidBody = GetComponent<Rigidbody>();
        playerStats = GetComponent<PlayerStats>();
        playerCombat = GetComponent<PlayerCombat>();
    }

    /// <summary>
    /// Setup input action bindings and event subscriptions.
    /// </summary>
    private void SetupInputActions()
    {
        playerInput = GetComponent<PlayerInput>();
        if (playerInput?.actions == null) return;

        var actionMap = playerInput.actions.FindActionMap("Gameplay");
        if (actionMap == null) return;

        // Cache input actions
        moveAction = actionMap.FindAction("Move");
        lookAction = actionMap.FindAction("Look");
        jumpAction = actionMap.FindAction("Jump");
        attackAction = actionMap.FindAction("Attack");
        magicAction = actionMap.FindAction("Block"); // Using Block as magic for now
        runAction = actionMap.FindAction("Run");

        // Subscribe to input events
        SubscribeToInputEvents();
    }

    /// <summary>
    /// Initialize camera settings and cursor lock.
    /// </summary>
    private void InitializeCamera()
    {
        Cursor.lockState = CursorLockMode.Locked;
        SetupCamera();
    }
    #endregion

    #region Input Handling
    /// <summary>
    /// Process input from both new Input System and legacy fallback.
    /// </summary>
    private void HandleInput()
    {
        // Read input from new Input System
        moveInput = moveAction?.ReadValue<Vector2>() ?? new Vector2(Input.GetAxis("Horizontal"), Input.GetAxis("Vertical"));
        lookInput = lookAction?.ReadValue<Vector2>() ?? new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));
        isRunning = runAction?.IsPressed() ?? Input.GetKey(KeyCode.LeftShift);

        // Perspective toggle (fallback to legacy input)
        perspectiveToggle = Input.GetKeyDown(KeyCode.V);
    }

    /// <summary>
    /// Subscribe to input action events.
    /// </summary>
    private void SubscribeToInputEvents()
    {
        if (jumpAction != null) jumpAction.performed += OnJump;
        if (attackAction != null) attackAction.performed += OnAttack;
        if (magicAction != null) magicAction.performed += OnMagic;
    }

    /// <summary>
    /// Unsubscribe from input action events to prevent memory leaks.
    /// </summary>
    private void UnsubscribeFromInputEvents()
    {
        if (jumpAction != null) jumpAction.performed -= OnJump;
        if (attackAction != null) attackAction.performed -= OnAttack;
        if (magicAction != null) magicAction.performed -= OnMagic;
    }
    #endregion

    #region Movement and Camera
    /// <summary>
    /// Handle physics-based player movement and stamina consumption.
    /// </summary>
    private void HandleMovement()
    {
        // Ground check using raycast
        isGrounded = Physics.Raycast(transform.position, Vector3.down, GroundCheckDistance);

        // Calculate movement direction relative to player orientation
        Vector3 direction = (transform.right * moveInput.x + transform.forward * moveInput.y).normalized;
        float currentSpeed = isRunning ? runSpeed : walkSpeed;

        // Apply movement while preserving vertical velocity
        Vector3 moveVelocity = direction * currentSpeed;
        rigidBody.velocity = new Vector3(moveVelocity.x, rigidBody.velocity.y, moveVelocity.z);

        // Consume stamina when running
        if (isRunning && direction.magnitude > 0 && playerStats != null)
        {
            playerStats.ConsumeStamina(Time.fixedDeltaTime * StaminaConsumptionRate);
        }
    }

    /// <summary>
    /// Handle mouse look for camera rotation with sensitivity and clamping.
    /// </summary>
    private void HandleMouseLook()
    {
        float mouseX = lookInput.x * mouseSensitivity;
        float mouseY = lookInput.y * mouseSensitivity;

        // Rotate player body horizontally
        transform.Rotate(Vector3.up * mouseX);

        // Rotate camera vertically with clamping
        xRotation = Mathf.Clamp(xRotation - mouseY, -VerticalLookLimit, VerticalLookLimit);

        if (cameraHolder != null)
        {
            cameraHolder.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
    }

    /// <summary>
    /// Handle perspective switching between first and third person.
    /// </summary>
    private void HandlePerspectiveToggle()
    {
        if (perspectiveToggle)
        {
            isFirstPerson = !isFirstPerson;
            SetupCamera();
        }
    }

    /// <summary>
    /// Configure camera position and field of view based on perspective mode.
    /// </summary>
    private void SetupCamera()
    {
        if (cameraHolder == null || playerCamera == null) return;

        if (isFirstPerson)
        {
            cameraHolder.localPosition = firstPersonOffset;
            playerCamera.fieldOfView = FirstPersonFOV;
        }
        else
        {
            cameraHolder.localPosition = thirdPersonOffset;
            playerCamera.fieldOfView = ThirdPersonFOV;
        }
    }
    #endregion

    #region Input Event Handlers
    /// <summary>
    /// Handle jump input when grounded.
    /// </summary>
    /// <param name="context">Input action callback context</param>
    private void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed && isGrounded && rigidBody != null)
        {
            rigidBody.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
    }

    /// <summary>
    /// Handle attack input when player can attack.
    /// </summary>
    /// <param name="context">Input action callback context</param>
    private void OnAttack(InputAction.CallbackContext context)
    {
        if (context.performed && playerStats?.CanAttack() == true && playerCombat != null)
        {
            playerCombat.PerformAttack();
        }
    }

    /// <summary>
    /// Handle magic casting input when player can cast magic.
    /// </summary>
    /// <param name="context">Input action callback context</param>
    private void OnMagic(InputAction.CallbackContext context)
    {
        if (context.performed && playerStats?.CanCastMagic() == true && playerCombat != null)
        {
            playerCombat.CastMagic();
        }
    }
    #endregion

    #region Public API
    /// <summary>
    /// Check if the player is currently in first person mode.
    /// </summary>
    /// <returns>True if in first person, false if in third person</returns>
    public bool IsFirstPerson() => isFirstPerson;

    /// <summary>
    /// Get the forward direction of the player camera.
    /// </summary>
    /// <returns>Camera forward vector, or player forward if camera is null</returns>
    public Vector3 GetCameraForward() => playerCamera?.transform.forward ?? transform.forward;

    /// <summary>
    /// Enable or disable player movement.
    /// </summary>
    /// <param name="enabled">Whether movement should be enabled</param>
    public void SetMovementEnabled(bool enabled)
    {
        movementEnabled = enabled;
    }

    /// <summary>
    /// Check if player movement is currently enabled.
    /// </summary>
    /// <returns>True if movement is enabled</returns>
    public bool IsMovementEnabled() => movementEnabled;
    #endregion
}
