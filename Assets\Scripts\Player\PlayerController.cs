using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 3f;
    public float runSpeed = 6f;
    public float jumpForce = 8f;
    public float mouseSensitivity = 2f;
    
    [Header("Camera Settings")]
    public Camera playerCamera;
    public Transform cameraHolder;
    public bool isFirstPerson = false;
    public Vector3 thirdPersonOffset = new Vector3(0, 2, -5);
    public Vector3 firstPersonOffset = new Vector3(0, 1.8f, 0);
    
    [Header("Combat Settings")]
    public Transform weaponHolder;
    public LayerMask enemyLayer;
    public float attackRange = 2f;
    
    private Rigidbody rb;
    private PlayerStats playerStats;
    private PlayerCombat playerCombat;
    private bool isGrounded;
    private float xRotation = 0f;
    private bool isRunning;
    
    // Input variables
    private float horizontal;
    private float vertical;
    private bool jumpInput;
    private bool attackInput;
    private bool magicInput;
    private bool perspectiveToggle;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        playerStats = GetComponent<PlayerStats>();
        playerCombat = GetComponent<PlayerCombat>();
        
        // Lock cursor to center of screen
        Cursor.lockState = CursorLockMode.Locked;
        
        SetupCamera();
    }
    
    void Update()
    {
        HandleInput();
        HandleMouseLook();
        HandlePerspectiveToggle();
    }
    
    void FixedUpdate()
    {
        HandleMovement();
    }
    
    void HandleInput()
    {
        horizontal = Input.GetAxis("Horizontal");
        vertical = Input.GetAxis("Vertical");
        jumpInput = Input.GetButtonDown("Jump");
        attackInput = Input.GetMouseButtonDown(0);
        magicInput = Input.GetMouseButtonDown(1);
        perspectiveToggle = Input.GetKeyDown(KeyCode.V);
        isRunning = Input.GetKey(KeyCode.LeftShift);
        
        // Combat inputs
        if (attackInput && playerStats.CanAttack())
        {
            playerCombat.PerformAttack();
        }
        
        if (magicInput && playerStats.CanCastMagic())
        {
            playerCombat.CastMagic();
        }
    }
    
    void HandleMovement()
    {
        // Check if grounded
        isGrounded = Physics.Raycast(transform.position, Vector3.down, 1.1f);
        
        // Calculate movement
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;
        float currentSpeed = isRunning ? runSpeed : walkSpeed;
        
        // Apply movement
        Vector3 moveVelocity = direction * currentSpeed;
        rb.velocity = new Vector3(moveVelocity.x, rb.velocity.y, moveVelocity.z);
        
        // Jumping
        if (jumpInput && isGrounded)
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
        
        // Consume stamina when running
        if (isRunning && direction.magnitude > 0)
        {
            playerStats.ConsumeStamina(Time.fixedDeltaTime * 10f);
        }
    }
    
    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        // Rotate the player body horizontally
        transform.Rotate(Vector3.up * mouseX);
        
        // Rotate the camera vertically
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -90f, 90f);
        cameraHolder.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }
    
    void HandlePerspectiveToggle()
    {
        if (perspectiveToggle)
        {
            isFirstPerson = !isFirstPerson;
            SetupCamera();
        }
    }
    
    void SetupCamera()
    {
        if (isFirstPerson)
        {
            cameraHolder.localPosition = firstPersonOffset;
            playerCamera.fieldOfView = 75f;
        }
        else
        {
            cameraHolder.localPosition = thirdPersonOffset;
            playerCamera.fieldOfView = 60f;
        }
    }
    
    public bool IsFirstPerson()
    {
        return isFirstPerson;
    }
    
    public Vector3 GetCameraForward()
    {
        return playerCamera.transform.forward;
    }
}
