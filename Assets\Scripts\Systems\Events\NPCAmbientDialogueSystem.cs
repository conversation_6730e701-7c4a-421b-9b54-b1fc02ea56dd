using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

/// <summary>
/// NPC Ambient Dialogue System for Dynamic World Events in Cinder of Darkness
/// Handles context-aware NPC dialogue that changes based on active world events
/// </summary>
public class NPCAmbientDialogueSystem : MonoBehaviour
{
    [Header("Dialogue Configuration")]
    public EventDialogueDatabase dialogueDatabase;
    public float dialogueFrequency = 30f; // Seconds between ambient dialogue
    public float dialogueVariance = 10f;
    public int maxSimultaneousDialogue = 3;
    
    [Header("Audio Settings")]
    public AudioSource dialogueAudioSource;
    public float dialogueVolume = 0.7f;
    public float dialogueRange = 15f;
    
    [Header("Visual Feedback")]
    public GameObject dialogueBubblePrefab;
    public float bubbleDuration = 3f;
    public bool showDialogueBubbles = true;
    
    // Static instance
    private static NPCAmbientDialogueSystem instance;
    public static NPCAmbientDialogueSystem Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<NPCAmbientDialogueSystem>();
                if (instance == null)
                {
                    GameObject go = new GameObject("NPCAmbientDialogueSystem");
                    instance = go.AddComponent<NPCAmbientDialogueSystem>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // System state
    private List<NPCController> registeredNPCs = new List<NPCController>();
    private Dictionary<string, List<string>> activeEventDialogue = new Dictionary<string, List<string>>();
    private List<NPCController> currentlySpeaking = new List<NPCController>();
    private float nextDialogueTime;
    
    /// <summary>
    /// Event dialogue database
    /// </summary>
    [System.Serializable]
    public class EventDialogueDatabase
    {
        public EventDialogueSet[] eventDialogueSets;
        
        public List<string> GetDialogueForEvent(string eventId, WorldEvent.EventType eventType, NPCType npcType)
        {
            var dialogueSet = eventDialogueSets.FirstOrDefault(set => 
                set.eventId == eventId || set.eventType == eventType);
            
            if (dialogueSet != null)
            {
                return dialogueSet.GetDialogueForNPCType(npcType);
            }
            
            return new List<string>();
        }
        
        public List<string> GetGeneralDialogueForEventType(WorldEvent.EventType eventType, NPCType npcType)
        {
            var dialogueSet = eventDialogueSets.FirstOrDefault(set => 
                set.eventType == eventType && string.IsNullOrEmpty(set.eventId));
            
            if (dialogueSet != null)
            {
                return dialogueSet.GetDialogueForNPCType(npcType);
            }
            
            return new List<string>();
        }
    }
    
    /// <summary>
    /// Dialogue set for specific events
    /// </summary>
    [System.Serializable]
    public class EventDialogueSet
    {
        public string eventId; // Specific event ID, or empty for general event type
        public WorldEvent.EventType eventType;
        public NPCDialogueGroup[] npcDialogueGroups;
        
        public List<string> GetDialogueForNPCType(NPCType npcType)
        {
            var group = npcDialogueGroups.FirstOrDefault(g => g.npcType == npcType);
            return group?.dialogueLines?.ToList() ?? new List<string>();
        }
    }
    
    /// <summary>
    /// NPC dialogue group by type
    /// </summary>
    [System.Serializable]
    public class NPCDialogueGroup
    {
        public NPCType npcType;
        public string[] dialogueLines;
    }
    
    /// <summary>
    /// NPC types for dialogue categorization
    /// </summary>
    public enum NPCType
    {
        Civilian,
        Merchant,
        Guard,
        Noble,
        Priest,
        Soldier,
        Child,
        Elder,
        Traveler,
        Scholar
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeDialogueSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        StartCoroutine(AmbientDialogueLoop());
        SubscribeToEventSystem();
    }
    
    void InitializeDialogueSystem()
    {
        // Setup audio source
        if (dialogueAudioSource == null)
        {
            dialogueAudioSource = gameObject.AddComponent<AudioSource>();
            dialogueAudioSource.volume = dialogueVolume;
            dialogueAudioSource.spatialBlend = 1f; // 3D sound
            dialogueAudioSource.maxDistance = dialogueRange;
        }
        
        nextDialogueTime = Time.time + Random.Range(dialogueFrequency - dialogueVariance, dialogueFrequency + dialogueVariance);
        
        Debug.Log("NPC Ambient Dialogue System initialized");
    }
    
    void SubscribeToEventSystem()
    {
        // Subscribe to event manager notifications
        // EventManager.OnEventStarted += OnEventStarted;
        // EventManager.OnEventCompleted += OnEventCompleted;
    }
    
    /// <summary>
    /// Ambient dialogue loop
    /// </summary>
    IEnumerator AmbientDialogueLoop()
    {
        while (true)
        {
            if (Time.time >= nextDialogueTime && currentlySpeaking.Count < maxSimultaneousDialogue)
            {
                TriggerRandomAmbientDialogue();
                nextDialogueTime = Time.time + Random.Range(dialogueFrequency - dialogueVariance, dialogueFrequency + dialogueVariance);
            }
            
            yield return new WaitForSeconds(1f);
        }
    }
    
    /// <summary>
    /// Register NPC for ambient dialogue
    /// </summary>
    public void RegisterNPC(NPCController npc)
    {
        if (!registeredNPCs.Contains(npc))
        {
            registeredNPCs.Add(npc);
        }
    }
    
    /// <summary>
    /// Unregister NPC from ambient dialogue
    /// </summary>
    public void UnregisterNPC(NPCController npc)
    {
        registeredNPCs.Remove(npc);
        currentlySpeaking.Remove(npc);
    }
    
    /// <summary>
    /// Handle event started
    /// </summary>
    void OnEventStarted(EventManager.ActiveEvent activeEvent)
    {
        // Update dialogue options for this event
        UpdateEventDialogue(activeEvent.eventId, activeEvent.eventData.eventType);
        
        // Trigger immediate event-related dialogue
        TriggerEventDialogue(activeEvent);
    }
    
    /// <summary>
    /// Handle event completed
    /// </summary>
    void OnEventCompleted(EventManager.CompletedEvent completedEvent)
    {
        // Remove event-specific dialogue
        if (activeEventDialogue.ContainsKey(completedEvent.eventId))
        {
            activeEventDialogue.Remove(completedEvent.eventId);
        }
        
        // Trigger event completion dialogue
        TriggerEventCompletionDialogue(completedEvent);
    }
    
    /// <summary>
    /// Update dialogue options for active event
    /// </summary>
    void UpdateEventDialogue(string eventId, WorldEvent.EventType eventType)
    {
        if (dialogueDatabase == null) return;
        
        // Get all dialogue for this event across all NPC types
        var allDialogue = new List<string>();
        
        foreach (NPCType npcType in System.Enum.GetValues(typeof(NPCType)))
        {
            var dialogue = dialogueDatabase.GetDialogueForEvent(eventId, eventType, npcType);
            allDialogue.AddRange(dialogue);
        }
        
        if (allDialogue.Count > 0)
        {
            activeEventDialogue[eventId] = allDialogue;
        }
    }
    
    /// <summary>
    /// Trigger random ambient dialogue
    /// </summary>
    void TriggerRandomAmbientDialogue()
    {
        // Get available NPCs (not currently speaking and near player)
        var availableNPCs = GetAvailableNPCs();
        
        if (availableNPCs.Count == 0) return;
        
        // Select random NPC
        var selectedNPC = availableNPCs[Random.Range(0, availableNPCs.Count)];
        
        // Get appropriate dialogue
        string dialogue = GetContextualDialogue(selectedNPC);
        
        if (!string.IsNullOrEmpty(dialogue))
        {
            TriggerNPCDialogue(selectedNPC, dialogue);
        }
    }
    
    /// <summary>
    /// Get available NPCs for dialogue
    /// </summary>
    List<NPCController> GetAvailableNPCs()
    {
        var player = GameObject.FindGameObjectWithTag("Player");
        if (player == null) return new List<NPCController>();
        
        return registeredNPCs.Where(npc => 
            npc != null && 
            !currentlySpeaking.Contains(npc) &&
            Vector3.Distance(npc.transform.position, player.transform.position) <= dialogueRange &&
            npc.CanSpeak()
        ).ToList();
    }
    
    /// <summary>
    /// Get contextual dialogue for NPC
    /// </summary>
    string GetContextualDialogue(NPCController npc)
    {
        if (dialogueDatabase == null) return "";
        
        // Priority 1: Event-specific dialogue
        var activeEvents = EventManager.GetActiveEvents();
        foreach (var activeEvent in activeEvents)
        {
            var eventDialogue = dialogueDatabase.GetDialogueForEvent(
                activeEvent.eventId, 
                activeEvent.eventData.eventType, 
                npc.GetNPCType()
            );
            
            if (eventDialogue.Count > 0)
            {
                return eventDialogue[Random.Range(0, eventDialogue.Count)];
            }
        }
        
        // Priority 2: General event type dialogue
        foreach (var activeEvent in activeEvents)
        {
            var generalDialogue = dialogueDatabase.GetGeneralDialogueForEventType(
                activeEvent.eventData.eventType, 
                npc.GetNPCType()
            );
            
            if (generalDialogue.Count > 0)
            {
                return generalDialogue[Random.Range(0, generalDialogue.Count)];
            }
        }
        
        // Priority 3: Default NPC dialogue
        return npc.GetDefaultAmbientDialogue();
    }
    
    /// <summary>
    /// Trigger NPC dialogue
    /// </summary>
    void TriggerNPCDialogue(NPCController npc, string dialogue)
    {
        currentlySpeaking.Add(npc);
        
        // Show dialogue bubble
        if (showDialogueBubbles && dialogueBubblePrefab != null)
        {
            ShowDialogueBubble(npc, dialogue);
        }
        
        // Play dialogue audio (if available)
        PlayDialogueAudio(npc, dialogue);
        
        // Notify NPC of dialogue
        npc.OnAmbientDialogueTriggered(dialogue);
        
        // Remove from speaking list after duration
        StartCoroutine(RemoveFromSpeakingAfterDelay(npc, bubbleDuration));
    }
    
    /// <summary>
    /// Show dialogue bubble
    /// </summary>
    void ShowDialogueBubble(NPCController npc, string dialogue)
    {
        GameObject bubble = Instantiate(dialogueBubblePrefab, npc.transform);
        
        // Position bubble above NPC
        bubble.transform.localPosition = Vector3.up * 2.5f;
        
        // Setup bubble text
        var textComponent = bubble.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        if (textComponent != null)
        {
            textComponent.text = dialogue;
        }
        
        // Make bubble face camera
        var camera = Camera.main;
        if (camera != null)
        {
            bubble.transform.LookAt(camera.transform);
            bubble.transform.Rotate(0, 180, 0); // Face the camera properly
        }
        
        // Auto-destroy bubble
        Destroy(bubble, bubbleDuration);
    }
    
    /// <summary>
    /// Play dialogue audio
    /// </summary>
    void PlayDialogueAudio(NPCController npc, string dialogue)
    {
        // Move audio source to NPC position
        dialogueAudioSource.transform.position = npc.transform.position;
        
        // Get dialogue audio clip (would be implemented based on dialogue system)
        AudioClip dialogueClip = npc.GetDialogueAudioClip(dialogue);
        
        if (dialogueClip != null)
        {
            dialogueAudioSource.PlayOneShot(dialogueClip);
        }
    }
    
    /// <summary>
    /// Remove NPC from speaking list after delay
    /// </summary>
    IEnumerator RemoveFromSpeakingAfterDelay(NPCController npc, float delay)
    {
        yield return new WaitForSeconds(delay);
        currentlySpeaking.Remove(npc);
    }
    
    /// <summary>
    /// Trigger event-specific dialogue
    /// </summary>
    void TriggerEventDialogue(EventManager.ActiveEvent activeEvent)
    {
        // Get NPCs in affected areas
        var affectedNPCs = GetNPCsInAffectedAreas(activeEvent.eventData.affectedRealms);
        
        // Trigger dialogue for a few random NPCs
        int dialogueCount = Mathf.Min(3, affectedNPCs.Count);
        var selectedNPCs = affectedNPCs.OrderBy(x => Random.value).Take(dialogueCount);
        
        foreach (var npc in selectedNPCs)
        {
            string dialogue = GetContextualDialogue(npc);
            if (!string.IsNullOrEmpty(dialogue))
            {
                TriggerNPCDialogue(npc, dialogue);
            }
        }
    }
    
    /// <summary>
    /// Trigger event completion dialogue
    /// </summary>
    void TriggerEventCompletionDialogue(EventManager.CompletedEvent completedEvent)
    {
        // Get completion-specific dialogue
        var completionDialogue = GetEventCompletionDialogue(completedEvent);
        
        if (!string.IsNullOrEmpty(completionDialogue))
        {
            // Find nearby NPCs to deliver completion dialogue
            var nearbyNPCs = GetAvailableNPCs().Take(2);
            
            foreach (var npc in nearbyNPCs)
            {
                TriggerNPCDialogue(npc, completionDialogue);
            }
        }
    }
    
    /// <summary>
    /// Get NPCs in affected areas
    /// </summary>
    List<NPCController> GetNPCsInAffectedAreas(string[] affectedAreas)
    {
        return registeredNPCs.Where(npc => 
            npc != null && 
            affectedAreas.Any(area => npc.IsInArea(area))
        ).ToList();
    }
    
    /// <summary>
    /// Get event completion dialogue
    /// </summary>
    string GetEventCompletionDialogue(EventManager.CompletedEvent completedEvent)
    {
        // Generate completion dialogue based on event outcome
        switch (completedEvent.finalState)
        {
            case EventManager.EventState.Completed:
                return $"Thank the gods, the {completedEvent.eventName} is finally over!";
            case EventManager.EventState.Failed:
                return $"The {completedEvent.eventName} has ended badly... we must be cautious.";
            case EventManager.EventState.Cancelled:
                return $"Strange... the {completedEvent.eventName} just... stopped.";
            default:
                return "";
        }
    }
    
    /// <summary>
    /// Create default dialogue database
    /// </summary>
    [ContextMenu("Create Default Dialogue Database")]
    void CreateDefaultDialogueDatabase()
    {
        dialogueDatabase = new EventDialogueDatabase();
        
        var invasionDialogue = new EventDialogueSet
        {
            eventType = WorldEvent.EventType.Invasion,
            npcDialogueGroups = new NPCDialogueGroup[]
            {
                new NPCDialogueGroup
                {
                    npcType = NPCType.Civilian,
                    dialogueLines = new string[]
                    {
                        "I heard orcs are marching from the south...",
                        "My family is hiding in the cellar until this passes.",
                        "The roads aren't safe anymore.",
                        "When will this madness end?"
                    }
                },
                new NPCDialogueGroup
                {
                    npcType = NPCType.Guard,
                    dialogueLines = new string[]
                    {
                        "All units to defensive positions!",
                        "Keep the gates secured at all costs.",
                        "I've never seen orcs this organized before.",
                        "We need more men on the walls."
                    }
                },
                new NPCDialogueGroup
                {
                    npcType = NPCType.Merchant,
                    dialogueLines = new string[]
                    {
                        "Trade routes are completely blocked.",
                        "I'm not opening my shop until this is over.",
                        "Weapon prices are going through the roof.",
                        "This war is ruining business."
                    }
                }
            }
        };
        
        var disasterDialogue = new EventDialogueSet
        {
            eventType = WorldEvent.EventType.NaturalDisaster,
            npcDialogueGroups = new NPCDialogueGroup[]
            {
                new NPCDialogueGroup
                {
                    npcType = NPCType.Civilian,
                    dialogueLines = new string[]
                    {
                        "This ash is getting into everything...",
                        "I can barely see through this storm.",
                        "The crops will be ruined if this continues.",
                        "Is this the end of days?"
                    }
                },
                new NPCDialogueGroup
                {
                    npcType = NPCType.Elder,
                    dialogueLines = new string[]
                    {
                        "I've seen storms before, but nothing like this.",
                        "The old prophecies spoke of such darkness.",
                        "We must weather this together.",
                        "This too shall pass, child."
                    }
                }
            }
        };
        
        dialogueDatabase.eventDialogueSets = new EventDialogueSet[] { invasionDialogue, disasterDialogue };
        
        Debug.Log("Created default dialogue database with invasion and disaster dialogue");
    }
    
    // Public API
    public static void RegisterNPCStatic(NPCController npc)
    {
        Instance.RegisterNPC(npc);
    }
    
    public static void UnregisterNPCStatic(NPCController npc)
    {
        Instance.UnregisterNPC(npc);
    }
    
    public static void TriggerEventDialogueStatic(EventManager.ActiveEvent activeEvent)
    {
        Instance.TriggerEventDialogue(activeEvent);
    }
}
