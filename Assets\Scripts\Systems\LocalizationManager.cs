using UnityEngine;
using System.Collections.Generic;
using System.IO;
using TMPro;
using UnityEngine.UI;

/// <summary>
/// Localization system for Cinder of Darkness
/// Supports Arabic and English with automatic text direction handling
/// </summary>
public class LocalizationManager : MonoBehaviour
{
    public enum Language
    {
        English,
        Arabic
    }
    
    [Header("Localization Settings")]
    public Language currentLanguage = Language.English;
    public bool autoDetectSystemLanguage = true;
    public string localizationFolder = "Localization";
    
    [Header("Font Assets")]
    public TMP_FontAsset englishFont;
    public TMP_FontAsset arabicFont;
    
    // Static instance
    private static LocalizationManager instance;
    public static LocalizationManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<LocalizationManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("LocalizationManager");
                    instance = go.AddComponent<LocalizationManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Localization data
    private Dictionary<string, Dictionary<Language, string>> localizedStrings = new Dictionary<string, Dictionary<Language, string>>();
    private List<LocalizedText> registeredTexts = new List<LocalizedText>();
    
    // Events
    public System.Action<Language> OnLanguageChanged;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeLocalization();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        LoadLocalizationData();
        
        if (autoDetectSystemLanguage)
        {
            DetectSystemLanguage();
        }
        
        ApplyLanguage(currentLanguage);
    }
    
    void InitializeLocalization()
    {
        // Create default localization entries
        CreateDefaultEntries();
        
        Debug.Log("LocalizationManager initialized");
    }
    
    void CreateDefaultEntries()
    {
        // Main Menu
        AddLocalizationEntry("menu_new_game", "New Game", "لعبة جديدة");
        AddLocalizationEntry("menu_continue", "Continue", "متابعة");
        AddLocalizationEntry("menu_settings", "Settings", "الإعدادات");
        AddLocalizationEntry("menu_credits", "Credits", "الشكر والتقدير");
        AddLocalizationEntry("menu_exit", "Exit", "خروج");
        
        // Settings
        AddLocalizationEntry("settings_audio", "Audio", "الصوت");
        AddLocalizationEntry("settings_graphics", "Graphics", "الرسوميات");
        AddLocalizationEntry("settings_controls", "Controls", "التحكم");
        AddLocalizationEntry("settings_language", "Language", "اللغة");
        AddLocalizationEntry("settings_master_volume", "Master Volume", "مستوى الصوت الرئيسي");
        AddLocalizationEntry("settings_music_volume", "Music Volume", "مستوى الموسيقى");
        AddLocalizationEntry("settings_sfx_volume", "SFX Volume", "مستوى المؤثرات الصوتية");
        AddLocalizationEntry("settings_resolution", "Resolution", "الدقة");
        AddLocalizationEntry("settings_fullscreen", "Fullscreen", "ملء الشاشة");
        AddLocalizationEntry("settings_vsync", "VSync", "المزامنة العمودية");
        AddLocalizationEntry("settings_apply", "Apply", "تطبيق");
        AddLocalizationEntry("settings_reset", "Reset", "إعادة تعيين");
        AddLocalizationEntry("settings_back", "Back", "رجوع");
        
        // Gameplay UI
        AddLocalizationEntry("ui_health", "Health", "الصحة");
        AddLocalizationEntry("ui_mana", "Mana", "المانا");
        AddLocalizationEntry("ui_level", "Level", "المستوى");
        AddLocalizationEntry("ui_experience", "Experience", "الخبرة");
        AddLocalizationEntry("ui_gold", "Gold", "الذهب");
        AddLocalizationEntry("ui_inventory", "Inventory", "المخزون");
        AddLocalizationEntry("ui_quests", "Quests", "المهام");
        AddLocalizationEntry("ui_map", "Map", "الخريطة");
        AddLocalizationEntry("ui_pause", "Pause", "إيقاف مؤقت");
        
        // Combat
        AddLocalizationEntry("combat_attack", "Attack", "هجوم");
        AddLocalizationEntry("combat_heavy_attack", "Heavy Attack", "هجوم قوي");
        AddLocalizationEntry("combat_block", "Block", "حجب");
        AddLocalizationEntry("combat_dodge", "Dodge", "تفادي");
        AddLocalizationEntry("combat_magic", "Magic", "سحر");
        
        // Magic Elements
        AddLocalizationEntry("magic_fire", "Fire", "النار");
        AddLocalizationEntry("magic_water", "Water", "الماء");
        AddLocalizationEntry("magic_wind", "Wind", "الرياح");
        AddLocalizationEntry("magic_earth", "Earth", "الأرض");
        
        // Morality System
        AddLocalizationEntry("morality_good", "Good", "خير");
        AddLocalizationEntry("morality_evil", "Evil", "شر");
        AddLocalizationEntry("morality_neutral", "Neutral", "محايد");
        AddLocalizationEntry("morality_path_of_light", "Path of Light", "طريق النور");
        AddLocalizationEntry("morality_path_of_darkness", "Path of Darkness", "طريق الظلام");
        
        // Loading Tips
        AddLocalizationEntry("tip_choices", "Every choice echoes through the realms...", "كل خيار يتردد صداه عبر العوالم...");
        AddLocalizationEntry("tip_magic", "Ancient magic flows through those who seek it...", "السحر القديم يتدفق عبر أولئك الذين يسعون إليه...");
        AddLocalizationEntry("tip_honor", "Honor and brutality walk hand in hand...", "الشرف والوحشية يسيران جنباً إلى جنب...");
        AddLocalizationEntry("tip_destiny", "The Cinderborn's destiny awaits...", "مصير المولود من الرماد في انتظاره...");
        AddLocalizationEntry("tip_wisdom", "Cultural wisdom guides the worthy...", "الحكمة الثقافية ترشد المستحقين...");
        AddLocalizationEntry("tip_death", "Death teaches the greatest lessons...", "الموت يعلم أعظم الدروس...");
        AddLocalizationEntry("tip_contemplation", "Contemplation brings inner strength...", "التأمل يجلب القوة الداخلية...");
        
        // Common UI
        AddLocalizationEntry("ui_yes", "Yes", "نعم");
        AddLocalizationEntry("ui_no", "No", "لا");
        AddLocalizationEntry("ui_ok", "OK", "موافق");
        AddLocalizationEntry("ui_cancel", "Cancel", "إلغاء");
        AddLocalizationEntry("ui_confirm", "Confirm", "تأكيد");
        AddLocalizationEntry("ui_loading", "Loading...", "جاري التحميل...");
        AddLocalizationEntry("ui_saving", "Saving...", "جاري الحفظ...");
        AddLocalizationEntry("ui_save_complete", "Save Complete", "تم الحفظ");
        AddLocalizationEntry("ui_load_complete", "Load Complete", "تم التحميل");
        
        // Error Messages
        AddLocalizationEntry("error_save_failed", "Save Failed", "فشل الحفظ");
        AddLocalizationEntry("error_load_failed", "Load Failed", "فشل التحميل");
        AddLocalizationEntry("error_no_save_found", "No Save Found", "لم يتم العثور على حفظ");
        AddLocalizationEntry("error_connection_failed", "Connection Failed", "فشل الاتصال");
    }
    
    void AddLocalizationEntry(string key, string english, string arabic)
    {
        if (!localizedStrings.ContainsKey(key))
        {
            localizedStrings[key] = new Dictionary<Language, string>();
        }
        
        localizedStrings[key][Language.English] = english;
        localizedStrings[key][Language.Arabic] = arabic;
    }
    
    void LoadLocalizationData()
    {
        // Try to load from CSV file
        string csvPath = Path.Combine(Application.streamingAssetsPath, localizationFolder, "localization.csv");
        
        if (File.Exists(csvPath))
        {
            LoadFromCSV(csvPath);
        }
        else
        {
            // Try to load from Resources
            TextAsset csvAsset = Resources.Load<TextAsset>($"{localizationFolder}/localization");
            if (csvAsset != null)
            {
                ParseCSVData(csvAsset.text);
            }
        }
    }
    
    void LoadFromCSV(string filePath)
    {
        try
        {
            string csvData = File.ReadAllText(filePath);
            ParseCSVData(csvData);
            Debug.Log("Localization data loaded from CSV file");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load localization CSV: {e.Message}");
        }
    }
    
    void ParseCSVData(string csvData)
    {
        string[] lines = csvData.Split('\n');
        
        for (int i = 1; i < lines.Length; i++) // Skip header
        {
            string line = lines[i].Trim();
            if (string.IsNullOrEmpty(line)) continue;
            
            string[] values = ParseCSVLine(line);
            if (values.Length >= 3)
            {
                string key = values[0];
                string english = values[1];
                string arabic = values[2];
                
                AddLocalizationEntry(key, english, arabic);
            }
        }
    }
    
    string[] ParseCSVLine(string line)
    {
        List<string> values = new List<string>();
        bool inQuotes = false;
        string currentValue = "";
        
        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];
            
            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                values.Add(currentValue);
                currentValue = "";
            }
            else
            {
                currentValue += c;
            }
        }
        
        values.Add(currentValue);
        return values.ToArray();
    }
    
    void DetectSystemLanguage()
    {
        SystemLanguage systemLang = Application.systemLanguage;
        
        switch (systemLang)
        {
            case SystemLanguage.Arabic:
                currentLanguage = Language.Arabic;
                break;
            default:
                currentLanguage = Language.English;
                break;
        }
        
        Debug.Log($"Detected system language: {systemLang}, using: {currentLanguage}");
    }
    
    // Public API
    public static string GetLocalizedString(string key)
    {
        return Instance.GetString(key);
    }
    
    public string GetString(string key)
    {
        if (localizedStrings.ContainsKey(key) && localizedStrings[key].ContainsKey(currentLanguage))
        {
            return localizedStrings[key][currentLanguage];
        }
        
        // Fallback to English
        if (localizedStrings.ContainsKey(key) && localizedStrings[key].ContainsKey(Language.English))
        {
            return localizedStrings[key][Language.English];
        }
        
        // Return key if not found
        Debug.LogWarning($"Localization key not found: {key}");
        return key;
    }
    
    public static void SetLanguage(Language language)
    {
        Instance.ApplyLanguage(language);
    }
    
    public void ApplyLanguage(Language language)
    {
        currentLanguage = language;
        
        // Update all registered texts
        foreach (LocalizedText localizedText in registeredTexts)
        {
            if (localizedText != null)
            {
                localizedText.UpdateText();
            }
        }
        
        // Apply font changes
        ApplyFontChanges();
        
        // Apply text direction
        ApplyTextDirection();
        
        // Save language preference
        PlayerPrefs.SetString("Language", language.ToString());
        PlayerPrefs.Save();
        
        OnLanguageChanged?.Invoke(language);
        
        Debug.Log($"Language changed to: {language}");
    }
    
    void ApplyFontChanges()
    {
        TMP_FontAsset targetFont = currentLanguage == Language.Arabic ? arabicFont : englishFont;
        
        if (targetFont == null) return;
        
        // Update all TextMeshPro components
        TextMeshProUGUI[] allTexts = FindObjectsOfType<TextMeshProUGUI>(true);
        foreach (TextMeshProUGUI text in allTexts)
        {
            LocalizedText localizedText = text.GetComponent<LocalizedText>();
            if (localizedText != null)
            {
                text.font = targetFont;
            }
        }
    }
    
    void ApplyTextDirection()
    {
        bool isRTL = currentLanguage == Language.Arabic;
        
        // Update all localized texts with direction
        foreach (LocalizedText localizedText in registeredTexts)
        {
            if (localizedText != null)
            {
                localizedText.SetTextDirection(isRTL);
            }
        }
    }
    
    public static void RegisterLocalizedText(LocalizedText localizedText)
    {
        if (Instance.registeredTexts.Contains(localizedText)) return;
        
        Instance.registeredTexts.Add(localizedText);
        localizedText.UpdateText();
    }
    
    public static void UnregisterLocalizedText(LocalizedText localizedText)
    {
        Instance.registeredTexts.Remove(localizedText);
    }
    
    public static Language GetCurrentLanguage()
    {
        return Instance.currentLanguage;
    }
    
    public static bool IsRightToLeft()
    {
        return Instance.currentLanguage == Language.Arabic;
    }
    
    public static TMP_FontAsset GetCurrentFont()
    {
        return Instance.currentLanguage == Language.Arabic ? Instance.arabicFont : Instance.englishFont;
    }
    
    // CSV Export for translators
    public void ExportToCSV()
    {
        string csvContent = "Key,English,Arabic\n";
        
        foreach (var entry in localizedStrings)
        {
            string key = entry.Key;
            string english = entry.Value.ContainsKey(Language.English) ? entry.Value[Language.English] : "";
            string arabic = entry.Value.ContainsKey(Language.Arabic) ? entry.Value[Language.Arabic] : "";
            
            // Escape quotes and commas
            english = EscapeCSVValue(english);
            arabic = EscapeCSVValue(arabic);
            
            csvContent += $"{key},{english},{arabic}\n";
        }
        
        string filePath = Path.Combine(Application.persistentDataPath, "localization_export.csv");
        File.WriteAllText(filePath, csvContent);
        
        Debug.Log($"Localization data exported to: {filePath}");
    }
    
    string EscapeCSVValue(string value)
    {
        if (value.Contains(",") || value.Contains("\"") || value.Contains("\n"))
        {
            value = value.Replace("\"", "\"\"");
            value = $"\"{value}\"";
        }
        return value;
    }
}

/// <summary>
/// Component for automatically localizing text elements
/// </summary>
public class LocalizedText : MonoBehaviour
{
    [Header("Localization")]
    public string localizationKey;
    public bool autoRegister = true;
    public bool updateFontAutomatically = true;
    
    private TextMeshProUGUI textComponent;
    private Text legacyTextComponent;
    private RectTransform rectTransform;
    
    void Awake()
    {
        textComponent = GetComponent<TextMeshProUGUI>();
        legacyTextComponent = GetComponent<Text>();
        rectTransform = GetComponent<RectTransform>();
    }
    
    void Start()
    {
        if (autoRegister)
        {
            LocalizationManager.RegisterLocalizedText(this);
        }
    }
    
    void OnDestroy()
    {
        LocalizationManager.UnregisterLocalizedText(this);
    }
    
    public void UpdateText()
    {
        if (string.IsNullOrEmpty(localizationKey)) return;
        
        string localizedString = LocalizationManager.GetLocalizedString(localizationKey);
        
        if (textComponent != null)
        {
            textComponent.text = localizedString;
            
            if (updateFontAutomatically)
            {
                TMP_FontAsset currentFont = LocalizationManager.GetCurrentFont();
                if (currentFont != null)
                {
                    textComponent.font = currentFont;
                }
            }
        }
        else if (legacyTextComponent != null)
        {
            legacyTextComponent.text = localizedString;
        }
    }
    
    public void SetTextDirection(bool isRightToLeft)
    {
        if (textComponent != null)
        {
            if (isRightToLeft)
            {
                textComponent.alignment = TextAlignmentOptions.TopRight;
                textComponent.isRightToLeftText = true;
            }
            else
            {
                textComponent.alignment = TextAlignmentOptions.TopLeft;
                textComponent.isRightToLeftText = false;
            }
        }
        
        // Adjust layout for RTL
        if (rectTransform != null && isRightToLeft)
        {
            // Flip horizontal alignment
            Vector2 anchorMin = rectTransform.anchorMin;
            Vector2 anchorMax = rectTransform.anchorMax;
            
            float tempMin = 1f - anchorMax.x;
            float tempMax = 1f - anchorMin.x;
            
            anchorMin.x = tempMin;
            anchorMax.x = tempMax;
            
            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
        }
    }
    
    public void SetLocalizationKey(string key)
    {
        localizationKey = key;
        UpdateText();
    }
}
