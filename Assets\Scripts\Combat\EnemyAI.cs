using UnityEngine;
using UnityEngine.AI;

public class EnemyAI : MonoBehaviour
{
    [Header("AI Settings")]
    public EnemyType enemyType = EnemyType.Orc;
    public AIState currentState = AIState.Patrol;
    public float detectionRange = 10f;
    public float attackRange = 2f;
    public float loseTargetRange = 15f;
    
    [Header("Combat Settings")]
    public float attackDamage = 20f;
    public float attackCooldown = 2f;
    public float attackWindup = 0.5f;
    
    [Header("Movement Settings")]
    public float patrolRadius = 5f;
    public float patrolWaitTime = 3f;
    public float chaseSpeed = 6f;
    public float patrolSpeed = 2f;
    
    [Header("Patrol Points")]
    public Transform[] patrolPoints;
    
    private NavMeshAgent agent;
    private Transform player;
    private PlayerStats playerStats;
    private EnemyHealth enemyHealth;
    private Animator animator;
    
    private Vector3 startPosition;
    private Vector3 currentPatrolTarget;
    private int currentPatrolIndex = 0;
    private float lastAttackTime;
    private float patrolWaitTimer;
    private bool isAttacking = false;
    
    public enum EnemyType
    {
        Or<PERSON>,
        Goblin,
        Skeleton,
        Demon,
        DarkKnight
    }
    
    public enum AIState
    {
        Patrol,
        Chase,
        Attack,
        Stunned,
        Dead
    }
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        enemyHealth = GetComponent<EnemyHealth>();
        animator = GetComponent<Animator>();
        
        // Find player
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            player = playerController.transform;
            playerStats = playerController.GetComponent<PlayerStats>();
        }
        
        startPosition = transform.position;
        
        // Set initial patrol target
        if (patrolPoints != null && patrolPoints.Length > 0)
        {
            currentPatrolTarget = patrolPoints[0].position;
        }
        else
        {
            SetRandomPatrolTarget();
        }
        
        // Subscribe to health events
        if (enemyHealth != null)
        {
            enemyHealth.OnDeath += OnDeath;
        }
        
        SetupEnemyType();
    }
    
    void Update()
    {
        if (currentState == AIState.Dead) return;
        
        UpdateAI();
        UpdateAnimations();
    }
    
    void UpdateAI()
    {
        float distanceToPlayer = player != null ? Vector3.Distance(transform.position, player.position) : float.MaxValue;
        
        switch (currentState)
        {
            case AIState.Patrol:
                HandlePatrol();
                
                // Check for player detection
                if (distanceToPlayer <= detectionRange)
                {
                    ChangeState(AIState.Chase);
                }
                break;
                
            case AIState.Chase:
                HandleChase();
                
                // Check if player is in attack range
                if (distanceToPlayer <= attackRange)
                {
                    ChangeState(AIState.Attack);
                }
                // Check if player is too far away
                else if (distanceToPlayer > loseTargetRange)
                {
                    ChangeState(AIState.Patrol);
                }
                break;
                
            case AIState.Attack:
                HandleAttack();
                
                // Check if player moved out of range
                if (distanceToPlayer > attackRange)
                {
                    ChangeState(AIState.Chase);
                }
                break;
                
            case AIState.Stunned:
                // Wait for stun to end
                break;
        }
    }
    
    void HandlePatrol()
    {
        agent.speed = patrolSpeed;
        
        // Check if we've reached the patrol target
        if (!agent.pathPending && agent.remainingDistance < 0.5f)
        {
            patrolWaitTimer += Time.deltaTime;
            
            if (patrolWaitTimer >= patrolWaitTime)
            {
                patrolWaitTimer = 0f;
                SetNextPatrolTarget();
            }
        }
        else
        {
            agent.SetDestination(currentPatrolTarget);
        }
    }
    
    void HandleChase()
    {
        if (player == null) return;
        
        agent.speed = chaseSpeed;
        agent.SetDestination(player.position);
    }
    
    void HandleAttack()
    {
        if (player == null) return;
        
        // Stop moving and face the player
        agent.ResetPath();
        Vector3 direction = (player.position - transform.position).normalized;
        direction.y = 0;
        transform.rotation = Quaternion.LookRotation(direction);
        
        // Attack if cooldown is ready
        if (Time.time >= lastAttackTime + attackCooldown && !isAttacking)
        {
            StartCoroutine(PerformAttack());
        }
    }
    
    System.Collections.IEnumerator PerformAttack()
    {
        isAttacking = true;
        lastAttackTime = Time.time;
        
        // Play attack animation
        if (animator != null)
        {
            animator.SetTrigger("Attack");
        }
        
        // Wait for attack windup
        yield return new WaitForSeconds(attackWindup);
        
        // Check if player is still in range
        float distanceToPlayer = player != null ? Vector3.Distance(transform.position, player.position) : float.MaxValue;
        if (distanceToPlayer <= attackRange && playerStats != null)
        {
            playerStats.TakeDamage(attackDamage);
            Debug.Log($"{gameObject.name} attacks player for {attackDamage} damage!");
        }
        
        // Wait for attack to finish
        yield return new WaitForSeconds(attackCooldown - attackWindup);
        
        isAttacking = false;
    }
    
    void SetNextPatrolTarget()
    {
        if (patrolPoints != null && patrolPoints.Length > 0)
        {
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            currentPatrolTarget = patrolPoints[currentPatrolIndex].position;
        }
        else
        {
            SetRandomPatrolTarget();
        }
    }
    
    void SetRandomPatrolTarget()
    {
        Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
        randomDirection += startPosition;
        randomDirection.y = startPosition.y;
        
        NavMeshHit hit;
        if (NavMesh.SamplePosition(randomDirection, out hit, patrolRadius, 1))
        {
            currentPatrolTarget = hit.position;
        }
        else
        {
            currentPatrolTarget = startPosition;
        }
    }
    
    void ChangeState(AIState newState)
    {
        currentState = newState;
        
        switch (newState)
        {
            case AIState.Patrol:
                SetRandomPatrolTarget();
                break;
            case AIState.Chase:
                // Alert nearby enemies
                AlertNearbyEnemies();
                break;
        }
    }
    
    void AlertNearbyEnemies()
    {
        Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, detectionRange * 2f);
        foreach (Collider col in nearbyEnemies)
        {
            EnemyAI otherEnemy = col.GetComponent<EnemyAI>();
            if (otherEnemy != null && otherEnemy != this && otherEnemy.currentState == AIState.Patrol)
            {
                otherEnemy.ChangeState(AIState.Chase);
            }
        }
    }
    
    void SetupEnemyType()
    {
        // Adjust stats based on enemy type
        switch (enemyType)
        {
            case EnemyType.Goblin:
                attackDamage *= 0.7f;
                detectionRange *= 1.2f;
                chaseSpeed *= 1.3f;
                break;
            case EnemyType.Orc:
                attackDamage *= 1.2f;
                attackRange *= 1.1f;
                break;
            case EnemyType.Skeleton:
                attackDamage *= 0.9f;
                chaseSpeed *= 0.8f;
                break;
            case EnemyType.Demon:
                attackDamage *= 1.5f;
                detectionRange *= 1.5f;
                break;
            case EnemyType.DarkKnight:
                attackDamage *= 2f;
                attackRange *= 1.3f;
                chaseSpeed *= 0.7f;
                break;
        }
    }
    
    public void OnTakeDamage(float damage)
    {
        // React to taking damage
        if (currentState == AIState.Patrol)
        {
            ChangeState(AIState.Chase);
        }
        
        // Chance to be stunned by heavy attacks
        if (damage > attackDamage)
        {
            StartCoroutine(Stun(1f));
        }
    }
    
    System.Collections.IEnumerator Stun(float duration)
    {
        AIState previousState = currentState;
        currentState = AIState.Stunned;
        agent.ResetPath();
        
        yield return new WaitForSeconds(duration);
        
        currentState = previousState;
    }
    
    void OnDeath()
    {
        currentState = AIState.Dead;
        agent.enabled = false;
    }
    
    void UpdateAnimations()
    {
        if (animator == null) return;
        
        // Set animation parameters
        animator.SetFloat("Speed", agent.velocity.magnitude);
        animator.SetBool("IsChasing", currentState == AIState.Chase);
        animator.SetBool("IsAttacking", isAttacking);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // Draw patrol radius
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(startPosition, patrolRadius);
    }
}
