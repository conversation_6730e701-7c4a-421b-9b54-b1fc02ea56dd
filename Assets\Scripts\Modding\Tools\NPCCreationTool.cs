using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;
using TMPro;

/// <summary>
/// In-game NPC Creation Tool for Cinder of Darkness
/// Allows players to create fully functional NPCs with dialogue, AI, and faction settings
/// </summary>
public class NPCCreationTool : MonoBehaviour
{
    [Header("UI References")]
    public GameObject npcCreatorUI;
    public TMP_InputField npcNameField;
    public TMP_Dropdown raceDropdown;
    public TMP_InputField titleField;
    public TMP_Dropdown personalityDropdown;
    public TMP_Dropdown factionDropdown;
    
    [<PERSON>er("Dialogue Editor")]
    public GameObject dialogueEditorPanel;
    public Transform dialogueNodeParent;
    public GameObject dialogueNodePrefab;
    public TMP_InputField currentNodeText;
    public Transform choiceListParent;
    public GameObject choicePrefab;
    
    [Header("Combat AI Settings")]
    public TMP_Dropdown aiTypeDropdown;
    public Slider aggressionSlider;
    public Slider detectionRangeSlider;
    public Slider healthSlider;
    public Slider damageSlider;
    public Toggle[] abilityToggles;
    
    [Header("Appearance Settings")]
    public TMP_Dropdown modelDropdown;
    public Slider scaleSlider;
    public Button colorPickerButton;
    public Image colorPreview;
    
    [Header("Voice Settings")]
    public TMP_Dropdown voiceTypeDropdown;
    public Slider pitchSlider;
    public Button recordVoiceButton;
    public Button importAudioButton;
    
    [Header("Preview")]
    public Transform previewArea;
    public Camera previewCamera;
    
    // Static instance
    private static NPCCreationTool instance;
    public static NPCCreationTool Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<NPCCreationTool>();
                if (instance == null)
                {
                    GameObject go = new GameObject("NPCCreationTool");
                    instance = go.AddComponent<NPCCreationTool>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // NPC Creation data
    private NPCCreationData currentNPC;
    private GameObject previewNPC;
    private DialogueNode currentDialogueNode;
    private List<DialogueNode> dialogueNodes = new List<DialogueNode>();
    private int nextNodeId = 1;
    
    [System.Serializable]
    public class NPCCreationData
    {
        public string npcId;
        public string name;
        public string race;
        public string title;
        public string personality;
        public string faction;
        public CombatAIData combatAI;
        public AppearanceData appearance;
        public VoiceData voice;
        public DialogueTreeData dialogue;
        public Vector3 spawnPosition;
        public bool isHostile;
        public string[] tags;
    }
    
    [System.Serializable]
    public class CombatAIData
    {
        public string aiType;
        public float aggressionLevel;
        public float detectionRange;
        public float health;
        public float damage;
        public List<string> abilities;
    }
    
    [System.Serializable]
    public class AppearanceData
    {
        public string modelId;
        public Vector3 scale;
        public Color primaryColor;
        public Color secondaryColor;
        public string[] accessories;
    }
    
    [System.Serializable]
    public class VoiceData
    {
        public string voiceType;
        public float pitch;
        public List<string> customAudioFiles;
        public bool useTextToSpeech;
    }
    
    [System.Serializable]
    public class DialogueTreeData
    {
        public string rootNodeId;
        public List<DialogueNodeData> nodes;
    }
    
    [System.Serializable]
    public class DialogueNodeData
    {
        public string nodeId;
        public string text;
        public string speakerId;
        public List<DialogueChoiceData> choices;
        public List<string> conditions;
        public List<string> actions;
        public string emotionalTone;
        public bool isEndNode;
    }
    
    [System.Serializable]
    public class DialogueChoiceData
    {
        public string text;
        public string targetNodeId;
        public List<string> requirements;
        public List<string> consequences;
        public string moralAlignment;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeNPCCreator();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        CreateNewNPC();
    }
    
    void InitializeNPCCreator()
    {
        Debug.Log("NPC Creation Tool initialized");
    }
    
    void SetupUI()
    {
        // Setup dropdowns
        SetupRaceDropdown();
        SetupPersonalityDropdown();
        SetupFactionDropdown();
        SetupAITypeDropdown();
        SetupModelDropdown();
        SetupVoiceTypeDropdown();
        
        // Setup sliders
        if (aggressionSlider != null)
            aggressionSlider.onValueChanged.AddListener(OnAggressionChanged);
        
        if (detectionRangeSlider != null)
            detectionRangeSlider.onValueChanged.AddListener(OnDetectionRangeChanged);
        
        if (healthSlider != null)
            healthSlider.onValueChanged.AddListener(OnHealthChanged);
        
        if (damageSlider != null)
            damageSlider.onValueChanged.AddListener(OnDamageChanged);
        
        if (scaleSlider != null)
            scaleSlider.onValueChanged.AddListener(OnScaleChanged);
        
        if (pitchSlider != null)
            pitchSlider.onValueChanged.AddListener(OnPitchChanged);
        
        // Setup buttons
        if (colorPickerButton != null)
            colorPickerButton.onClick.AddListener(OpenColorPicker);
        
        if (recordVoiceButton != null)
            recordVoiceButton.onClick.AddListener(StartVoiceRecording);
        
        if (importAudioButton != null)
            importAudioButton.onClick.AddListener(ImportAudioFile);
        
        // Initially hide UI
        if (npcCreatorUI != null)
            npcCreatorUI.SetActive(false);
    }
    
    void SetupRaceDropdown()
    {
        if (raceDropdown == null) return;
        
        List<string> races = new List<string>
        {
            "Human", "Drenari", "Vaelari", "Ashborn", "Frostkin", "Skyborn", "Shadowkin", "Emberfolk"
        };
        
        raceDropdown.ClearOptions();
        raceDropdown.AddOptions(races);
        raceDropdown.onValueChanged.AddListener(OnRaceChanged);
    }
    
    void SetupPersonalityDropdown()
    {
        if (personalityDropdown == null) return;
        
        List<string> personalities = new List<string>
        {
            "Friendly", "Hostile", "Neutral", "Wise", "Aggressive", "Fearful", "Curious", "Stoic", "Cheerful", "Melancholic"
        };
        
        personalityDropdown.ClearOptions();
        personalityDropdown.AddOptions(personalities);
        personalityDropdown.onValueChanged.AddListener(OnPersonalityChanged);
    }
    
    void SetupFactionDropdown()
    {
        if (factionDropdown == null) return;
        
        List<string> factions = new List<string>
        {
            "Kingdom of Light", "Shadow Realm", "Independent", "Ash Cult", "Frost Clan", "Sky Guardians", "Neutral"
        };
        
        factionDropdown.ClearOptions();
        factionDropdown.AddOptions(factions);
        factionDropdown.onValueChanged.AddListener(OnFactionChanged);
    }
    
    void SetupAITypeDropdown()
    {
        if (aiTypeDropdown == null) return;
        
        List<string> aiTypes = new List<string>
        {
            "Passive", "Defensive", "Aggressive", "Patrol", "Guard", "Merchant", "Quest Giver", "Companion"
        };
        
        aiTypeDropdown.ClearOptions();
        aiTypeDropdown.AddOptions(aiTypes);
        aiTypeDropdown.onValueChanged.AddListener(OnAITypeChanged);
    }
    
    void SetupModelDropdown()
    {
        if (modelDropdown == null) return;
        
        List<string> models = new List<string>
        {
            "Human Male", "Human Female", "Warrior", "Mage", "Rogue", "Elder", "Child", "Merchant", "Guard", "Noble"
        };
        
        modelDropdown.ClearOptions();
        modelDropdown.AddOptions(models);
        modelDropdown.onValueChanged.AddListener(OnModelChanged);
    }
    
    void SetupVoiceTypeDropdown()
    {
        if (voiceTypeDropdown == null) return;
        
        List<string> voiceTypes = new List<string>
        {
            "Deep Male", "High Male", "Deep Female", "High Female", "Child", "Elder", "Robotic", "Ethereal"
        };
        
        voiceTypeDropdown.ClearOptions();
        voiceTypeDropdown.AddOptions(voiceTypes);
        voiceTypeDropdown.onValueChanged.AddListener(OnVoiceTypeChanged);
    }
    
    // Public API
    public static bool IsNPCCreatorOpen()
    {
        return Instance.npcCreatorUI != null && Instance.npcCreatorUI.activeSelf;
    }
    
    public static void OpenNPCCreator()
    {
        Instance.OpenNPCCreatorInternal();
    }
    
    public static void CloseNPCCreator()
    {
        Instance.CloseNPCCreatorInternal();
    }
    
    public static void CreateNPCFromData(NPCCreationData data)
    {
        Instance.CreateNPCFromDataInternal(data);
    }
    
    void OpenNPCCreatorInternal()
    {
        if (npcCreatorUI != null)
            npcCreatorUI.SetActive(true);
        
        CreateNewNPC();
        UpdatePreview();
    }
    
    void CloseNPCCreatorInternal()
    {
        if (npcCreatorUI != null)
            npcCreatorUI.SetActive(false);
        
        CleanupPreview();
    }
    
    void CreateNewNPC()
    {
        currentNPC = new NPCCreationData
        {
            npcId = System.Guid.NewGuid().ToString(),
            name = "New NPC",
            race = "Human",
            title = "",
            personality = "Neutral",
            faction = "Independent",
            combatAI = new CombatAIData
            {
                aiType = "Passive",
                aggressionLevel = 0.5f,
                detectionRange = 10f,
                health = 100f,
                damage = 20f,
                abilities = new List<string>()
            },
            appearance = new AppearanceData
            {
                modelId = "Human Male",
                scale = Vector3.one,
                primaryColor = Color.white,
                secondaryColor = Color.gray,
                accessories = new string[0]
            },
            voice = new VoiceData
            {
                voiceType = "Deep Male",
                pitch = 1f,
                customAudioFiles = new List<string>(),
                useTextToSpeech = true
            },
            dialogue = new DialogueTreeData
            {
                rootNodeId = "node_1",
                nodes = new List<DialogueNodeData>()
            },
            spawnPosition = Vector3.zero,
            isHostile = false,
            tags = new string[0]
        };
        
        // Create initial dialogue node
        CreateInitialDialogueNode();
        
        // Update UI
        UpdateUIFromNPC();
    }
    
    void CreateInitialDialogueNode()
    {
        var initialNode = new DialogueNodeData
        {
            nodeId = "node_1",
            text = "Hello, traveler.",
            speakerId = currentNPC.npcId,
            choices = new List<DialogueChoiceData>(),
            conditions = new List<string>(),
            actions = new List<string>(),
            emotionalTone = "Neutral",
            isEndNode = false
        };
        
        // Add default choice
        initialNode.choices.Add(new DialogueChoiceData
        {
            text = "Goodbye.",
            targetNodeId = "end",
            requirements = new List<string>(),
            consequences = new List<string>(),
            moralAlignment = "Neutral"
        });
        
        currentNPC.dialogue.nodes.Add(initialNode);
        dialogueNodes.Add(ConvertToDialogueNode(initialNode));
    }
    
    DialogueNode ConvertToDialogueNode(DialogueNodeData data)
    {
        var node = new DialogueNode
        {
            nodeId = data.nodeId,
            text = data.text,
            speakerId = data.speakerId,
            choices = data.choices.Select(c => new DialogueChoice
            {
                text = c.text,
                targetNodeId = c.targetNodeId,
                requirements = c.requirements.ToArray()
            }).ToList(),
            conditions = data.conditions.ToArray(),
            actions = data.actions.ToArray()
        };
        
        return node;
    }
    
    void UpdateUIFromNPC()
    {
        if (npcNameField != null)
            npcNameField.text = currentNPC.name;
        
        if (titleField != null)
            titleField.text = currentNPC.title;
        
        // Update dropdowns
        if (raceDropdown != null)
            raceDropdown.value = raceDropdown.options.FindIndex(o => o.text == currentNPC.race);
        
        if (personalityDropdown != null)
            personalityDropdown.value = personalityDropdown.options.FindIndex(o => o.text == currentNPC.personality);
        
        if (factionDropdown != null)
            factionDropdown.value = factionDropdown.options.FindIndex(o => o.text == currentNPC.faction);
        
        if (aiTypeDropdown != null)
            aiTypeDropdown.value = aiTypeDropdown.options.FindIndex(o => o.text == currentNPC.combatAI.aiType);
        
        if (modelDropdown != null)
            modelDropdown.value = modelDropdown.options.FindIndex(o => o.text == currentNPC.appearance.modelId);
        
        if (voiceTypeDropdown != null)
            voiceTypeDropdown.value = voiceTypeDropdown.options.FindIndex(o => o.text == currentNPC.voice.voiceType);
        
        // Update sliders
        if (aggressionSlider != null)
            aggressionSlider.value = currentNPC.combatAI.aggressionLevel;
        
        if (detectionRangeSlider != null)
            detectionRangeSlider.value = currentNPC.combatAI.detectionRange;
        
        if (healthSlider != null)
            healthSlider.value = currentNPC.combatAI.health;
        
        if (damageSlider != null)
            damageSlider.value = currentNPC.combatAI.damage;
        
        if (scaleSlider != null)
            scaleSlider.value = currentNPC.appearance.scale.x;
        
        if (pitchSlider != null)
            pitchSlider.value = currentNPC.voice.pitch;
        
        // Update color preview
        if (colorPreview != null)
            colorPreview.color = currentNPC.appearance.primaryColor;
    }
    
    void UpdatePreview()
    {
        CleanupPreview();
        
        if (previewArea == null) return;
        
        // Load model prefab
        GameObject modelPrefab = LoadModelPrefab(currentNPC.appearance.modelId);
        if (modelPrefab != null)
        {
            previewNPC = Instantiate(modelPrefab, previewArea.position, previewArea.rotation);
            previewNPC.transform.localScale = currentNPC.appearance.scale;
            
            // Apply color
            ApplyColorToModel(previewNPC, currentNPC.appearance.primaryColor);
            
            // Position camera
            if (previewCamera != null)
            {
                previewCamera.transform.LookAt(previewNPC.transform);
            }
        }
    }
    
    GameObject LoadModelPrefab(string modelId)
    {
        // Load from Resources based on model ID
        string resourcePath = $"NPCModels/{modelId.Replace(" ", "")}";
        return Resources.Load<GameObject>(resourcePath);
    }
    
    void ApplyColorToModel(GameObject model, Color color)
    {
        var renderers = model.GetComponentsInChildren<Renderer>();
        foreach (var renderer in renderers)
        {
            foreach (var material in renderer.materials)
            {
                if (material.HasProperty("_Color"))
                {
                    material.color = color;
                }
            }
        }
    }
    
    void CleanupPreview()
    {
        if (previewNPC != null)
        {
            DestroyImmediate(previewNPC);
            previewNPC = null;
        }
    }
    
    // Event Handlers
    void OnRaceChanged(int index)
    {
        if (raceDropdown != null)
        {
            currentNPC.race = raceDropdown.options[index].text;
            UpdatePreview();
        }
    }
    
    void OnPersonalityChanged(int index)
    {
        if (personalityDropdown != null)
        {
            currentNPC.personality = personalityDropdown.options[index].text;
        }
    }
    
    void OnFactionChanged(int index)
    {
        if (factionDropdown != null)
        {
            currentNPC.faction = factionDropdown.options[index].text;
        }
    }
    
    void OnAITypeChanged(int index)
    {
        if (aiTypeDropdown != null)
        {
            currentNPC.combatAI.aiType = aiTypeDropdown.options[index].text;
        }
    }
    
    void OnModelChanged(int index)
    {
        if (modelDropdown != null)
        {
            currentNPC.appearance.modelId = modelDropdown.options[index].text;
            UpdatePreview();
        }
    }
    
    void OnVoiceTypeChanged(int index)
    {
        if (voiceTypeDropdown != null)
        {
            currentNPC.voice.voiceType = voiceTypeDropdown.options[index].text;
        }
    }
    
    void OnAggressionChanged(float value)
    {
        currentNPC.combatAI.aggressionLevel = value;
    }
    
    void OnDetectionRangeChanged(float value)
    {
        currentNPC.combatAI.detectionRange = value;
    }
    
    void OnHealthChanged(float value)
    {
        currentNPC.combatAI.health = value;
    }
    
    void OnDamageChanged(float value)
    {
        currentNPC.combatAI.damage = value;
    }
    
    void OnScaleChanged(float value)
    {
        currentNPC.appearance.scale = Vector3.one * value;
        UpdatePreview();
    }
    
    void OnPitchChanged(float value)
    {
        currentNPC.voice.pitch = value;
    }
    
    void OpenColorPicker()
    {
        // Implementation would open a color picker UI
        // For now, cycle through predefined colors
        Color[] colors = { Color.white, Color.red, Color.blue, Color.green, Color.yellow, Color.magenta, Color.cyan };
        int currentIndex = System.Array.IndexOf(colors, currentNPC.appearance.primaryColor);
        int nextIndex = (currentIndex + 1) % colors.Length;
        
        currentNPC.appearance.primaryColor = colors[nextIndex];
        
        if (colorPreview != null)
            colorPreview.color = currentNPC.appearance.primaryColor;
        
        UpdatePreview();
    }
    
    void StartVoiceRecording()
    {
        // Implementation would start microphone recording
        Debug.Log("Voice recording started (placeholder)");
    }
    
    void ImportAudioFile()
    {
        // Implementation would open file browser
        Debug.Log("Audio file import (placeholder)");
    }
    
    // Dialogue Editor
    public void OpenDialogueEditor()
    {
        if (dialogueEditorPanel != null)
            dialogueEditorPanel.SetActive(true);
        
        RefreshDialogueEditor();
    }
    
    public void CloseDialogueEditor()
    {
        if (dialogueEditorPanel != null)
            dialogueEditorPanel.SetActive(false);
    }
    
    void RefreshDialogueEditor()
    {
        // Clear existing nodes
        if (dialogueNodeParent != null)
        {
            foreach (Transform child in dialogueNodeParent)
            {
                Destroy(child.gameObject);
            }
        }
        
        // Create node UI elements
        foreach (var nodeData in currentNPC.dialogue.nodes)
        {
            CreateDialogueNodeUI(nodeData);
        }
    }
    
    void CreateDialogueNodeUI(DialogueNodeData nodeData)
    {
        if (dialogueNodePrefab == null || dialogueNodeParent == null) return;
        
        GameObject nodeUI = Instantiate(dialogueNodePrefab, dialogueNodeParent);
        
        // Setup node UI components
        var nodeText = nodeUI.GetComponentInChildren<TMP_InputField>();
        if (nodeText != null)
        {
            nodeText.text = nodeData.text;
            nodeText.onEndEdit.AddListener((text) => nodeData.text = text);
        }
        
        var nodeButton = nodeUI.GetComponentInChildren<Button>();
        if (nodeButton != null)
        {
            nodeButton.onClick.AddListener(() => SelectDialogueNode(nodeData));
        }
    }
    
    void SelectDialogueNode(DialogueNodeData nodeData)
    {
        currentDialogueNode = ConvertToDialogueNode(nodeData);
        
        if (currentNodeText != null)
            currentNodeText.text = nodeData.text;
        
        RefreshChoiceList(nodeData);
    }
    
    void RefreshChoiceList(DialogueNodeData nodeData)
    {
        // Clear existing choices
        if (choiceListParent != null)
        {
            foreach (Transform child in choiceListParent)
            {
                Destroy(child.gameObject);
            }
        }
        
        // Create choice UI elements
        foreach (var choice in nodeData.choices)
        {
            CreateChoiceUI(choice, nodeData);
        }
    }
    
    void CreateChoiceUI(DialogueChoiceData choice, DialogueNodeData parentNode)
    {
        if (choicePrefab == null || choiceListParent == null) return;
        
        GameObject choiceUI = Instantiate(choicePrefab, choiceListParent);
        
        var choiceText = choiceUI.GetComponentInChildren<TMP_InputField>();
        if (choiceText != null)
        {
            choiceText.text = choice.text;
            choiceText.onEndEdit.AddListener((text) => choice.text = text);
        }
        
        var deleteButton = choiceUI.GetComponentInChildren<Button>();
        if (deleteButton != null)
        {
            deleteButton.onClick.AddListener(() => {
                parentNode.choices.Remove(choice);
                RefreshChoiceList(parentNode);
            });
        }
    }
    
    public void AddNewDialogueChoice()
    {
        if (currentDialogueNode == null) return;
        
        var currentNodeData = currentNPC.dialogue.nodes.Find(n => n.nodeId == currentDialogueNode.nodeId);
        if (currentNodeData != null)
        {
            var newChoice = new DialogueChoiceData
            {
                text = "New choice",
                targetNodeId = "end",
                requirements = new List<string>(),
                consequences = new List<string>(),
                moralAlignment = "Neutral"
            };
            
            currentNodeData.choices.Add(newChoice);
            RefreshChoiceList(currentNodeData);
        }
    }
    
    public void AddNewDialogueNode()
    {
        var newNode = new DialogueNodeData
        {
            nodeId = $"node_{nextNodeId++}",
            text = "New dialogue text",
            speakerId = currentNPC.npcId,
            choices = new List<DialogueChoiceData>(),
            conditions = new List<string>(),
            actions = new List<string>(),
            emotionalTone = "Neutral",
            isEndNode = false
        };
        
        currentNPC.dialogue.nodes.Add(newNode);
        RefreshDialogueEditor();
    }
    
    // Save/Load NPC
    public void SaveNPC()
    {
        // Update NPC data from UI
        if (npcNameField != null)
            currentNPC.name = npcNameField.text;
        
        if (titleField != null)
            currentNPC.title = titleField.text;
        
        // Save as mod file
        var npcMod = ConvertToNPCMod(currentNPC);
        ModdingSystem.CreateNPCMod(npcMod);
        
        Debug.Log($"NPC saved: {currentNPC.name}");
    }
    
    NPCMod ConvertToNPCMod(NPCCreationData data)
    {
        return new NPCMod
        {
            npcId = data.npcId,
            name = data.name,
            race = data.race,
            title = data.title,
            personality = data.personality,
            faction = data.faction,
            dialogue = ConvertToDialogueTree(data.dialogue),
            combatAI = new CombatAI
            {
                aiType = data.combatAI.aiType,
                aggressionLevel = data.combatAI.aggressionLevel,
                detectionRange = data.combatAI.detectionRange,
                health = data.combatAI.health,
                damage = data.combatAI.damage,
                abilities = data.combatAI.abilities.ToArray()
            },
            spawnLocation = data.spawnPosition,
            modelPath = $"NPCModels/{data.appearance.modelId}",
            voiceLines = data.voice.customAudioFiles.ToArray()
        };
    }
    
    DialogueTree ConvertToDialogueTree(DialogueTreeData data)
    {
        return new DialogueTree
        {
            rootNodeId = data.rootNodeId,
            nodes = data.nodes.Select(ConvertToDialogueNode).ToList()
        };
    }
    
    void CreateNPCFromDataInternal(NPCCreationData data)
    {
        currentNPC = data;
        UpdateUIFromNPC();
        UpdatePreview();
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Handle game events if needed
    }
    
    void Update()
    {
        // Toggle NPC Creator with F3 (if unlocked)
        if (Input.GetKeyDown(KeyCode.F3))
        {
            if (ModdingSystem.IsToolUnlocked("npc_creator"))
            {
                if (IsNPCCreatorOpen())
                    CloseNPCCreatorInternal();
                else
                    OpenNPCCreatorInternal();
            }
        }
    }
}
