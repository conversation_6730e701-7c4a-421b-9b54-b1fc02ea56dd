using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using System.Collections;

public class InputTestingSystem : MonoBehaviour
{
    [Header("Testing Configuration")]
    public bool enableInputTesting = false;
    public bool showDebugInfo = true;
    public bool logInputEvents = false;
    
    [Header("Test States")]
    public GameplayState currentTestState = GameplayState.Exploration;
    public List<InputTestResult> testResults = new List<InputTestResult>();
    
    [Header("Test Scenarios")]
    public TestScenario[] testScenarios;
    public float testDuration = 5f;
    public bool runAutomatedTests = false;
    
    private MultiInputControlSystem inputSystem;
    private InputSettingsManager settingsManager;
    private UIButtonPromptSystem promptSystem;
    private bool isTestingActive = false;
    private float testStartTime = 0f;
    
    public enum GameplayState
    {
        Exploration,
        Combat,
        Stealth,
        Inventory,
        Menu,
        Dialogue,
        Cutscene,
        DreamSequence,
        Pause
    }
    
    [System.Serializable]
    public class TestScenario
    {
        [Header("Scenario Info")]
        public string scenarioName;
        public GameplayState gameplayState;
        public string[] requiredInputs;
        public float timeLimit;
        
        [Header("Expected Behavior")]
        public string[] expectedResponses;
        public bool shouldShowPrompts;
        public bool shouldTriggerVibration;
        
        [Header("Test Results")]
        public bool testPassed = false;
        public float testScore = 0f;
        public string[] failureReasons;
    }
    
    [System.Serializable]
    public class InputTestResult
    {
        public string testName;
        public GameplayState state;
        public MultiInputControlSystem.InputDeviceType deviceType;
        public bool passed;
        public float responseTime;
        public string errorMessage;
        public float timestamp;
    }
    
    void Start()
    {
        inputSystem = GetComponent<MultiInputControlSystem>();
        settingsManager = GetComponent<InputSettingsManager>();
        promptSystem = FindObjectOfType<UIButtonPromptSystem>();
        
        InitializeTestScenarios();
        
        if (enableInputTesting)
        {
            StartInputTesting();
        }
    }
    
    void Update()
    {
        if (enableInputTesting)
        {
            UpdateInputTesting();
            
            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }
        
        // Manual test triggers
        if (Input.GetKeyDown(KeyCode.F1))
        {
            RunQuickInputTest();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            CycleTestState();
        }
        
        if (Input.GetKeyDown(KeyCode.F3))
        {
            TestAllInputDevices();
        }
    }
    
    void InitializeTestScenarios()
    {
        testScenarios = new TestScenario[]
        {
            new TestScenario
            {
                scenarioName = "Basic Movement Test",
                gameplayState = GameplayState.Exploration,
                requiredInputs = new string[] { "Move", "Look", "Jump", "Run" },
                timeLimit = 10f,
                expectedResponses = new string[] { "Character moves", "Camera rotates", "Character jumps", "Character runs" },
                shouldShowPrompts = true,
                shouldTriggerVibration = false
            },
            new TestScenario
            {
                scenarioName = "Combat Input Test",
                gameplayState = GameplayState.Combat,
                requiredInputs = new string[] { "Attack", "Block", "Dodge", "Target" },
                timeLimit = 15f,
                expectedResponses = new string[] { "Attack animation", "Block stance", "Dodge roll", "Target lock" },
                shouldShowPrompts = true,
                shouldTriggerVibration = true
            },
            new TestScenario
            {
                scenarioName = "Menu Navigation Test",
                gameplayState = GameplayState.Menu,
                requiredInputs = new string[] { "Navigate", "Select", "Cancel", "Menu" },
                timeLimit = 8f,
                expectedResponses = new string[] { "Menu navigation", "Item selection", "Menu back", "Menu toggle" },
                shouldShowPrompts = true,
                shouldTriggerVibration = false
            },
            new TestScenario
            {
                scenarioName = "Inventory Management Test",
                gameplayState = GameplayState.Inventory,
                requiredInputs = new string[] { "Navigate", "Select", "Use", "Drop", "Sort" },
                timeLimit = 12f,
                expectedResponses = new string[] { "Item navigation", "Item selection", "Item use", "Item drop", "Inventory sort" },
                shouldShowPrompts = true,
                shouldTriggerVibration = false
            },
            new TestScenario
            {
                scenarioName = "Dialogue System Test",
                gameplayState = GameplayState.Dialogue,
                requiredInputs = new string[] { "Select", "Skip", "History", "Auto" },
                timeLimit = 10f,
                expectedResponses = new string[] { "Option selection", "Text skip", "Dialogue history", "Auto advance" },
                shouldShowPrompts = true,
                shouldTriggerVibration = false
            },
            new TestScenario
            {
                scenarioName = "Stealth Mechanics Test",
                gameplayState = GameplayState.Stealth,
                requiredInputs = new string[] { "Crouch", "Move", "Peek", "Takedown" },
                timeLimit = 15f,
                expectedResponses = new string[] { "Crouch stance", "Silent movement", "Corner peek", "Stealth takedown" },
                shouldShowPrompts = true,
                shouldTriggerVibration = true
            }
        };
    }
    
    void StartInputTesting()
    {
        isTestingActive = true;
        testStartTime = Time.time;
        
        Debug.Log("Input Testing System activated");
        ShowTestMessage("Input Testing Mode: Active");
        
        if (runAutomatedTests)
        {
            StartCoroutine(RunAutomatedTestSuite());
        }
    }
    
    void UpdateInputTesting()
    {
        if (!isTestingActive) return;
        
        // Monitor input responsiveness
        MonitorInputResponsiveness();
        
        // Check for input conflicts
        CheckInputConflicts();
        
        // Validate device switching
        ValidateDeviceSwitching();
        
        // Test vibration feedback
        TestVibrationFeedback();
    }
    
    void MonitorInputResponsiveness()
    {
        // Test input lag and responsiveness
        if (Input.anyKeyDown || (Gamepad.current != null && Gamepad.current.wasUpdatedThisFrame))
        {
            float responseTime = Time.unscaledTime - testStartTime;
            
            if (responseTime > 0.1f) // More than 100ms is considered slow
            {
                LogTestResult("Input Responsiveness", false, responseTime, "Input lag detected");
            }
            else
            {
                LogTestResult("Input Responsiveness", true, responseTime, "");
            }
        }
    }
    
    void CheckInputConflicts()
    {
        // Check for conflicting input bindings
        if (settingsManager != null)
        {
            var settings = settingsManager.GetCurrentSettings();
            
            // Check keyboard conflicts
            Dictionary<KeyCode, List<string>> keyUsage = new Dictionary<KeyCode, List<string>>();
            
            foreach (var binding in settings.keyboardBindings)
            {
                if (!keyUsage.ContainsKey(binding.primaryKey))
                {
                    keyUsage[binding.primaryKey] = new List<string>();
                }
                keyUsage[binding.primaryKey].Add(binding.actionName);
                
                if (binding.secondaryKey != KeyCode.None)
                {
                    if (!keyUsage.ContainsKey(binding.secondaryKey))
                    {
                        keyUsage[binding.secondaryKey] = new List<string>();
                    }
                    keyUsage[binding.secondaryKey].Add(binding.actionName);
                }
            }
            
            // Report conflicts
            foreach (var kvp in keyUsage)
            {
                if (kvp.Value.Count > 1)
                {
                    string conflictMessage = $"Key {kvp.Key} bound to multiple actions: {string.Join(", ", kvp.Value)}";
                    LogTestResult("Input Conflict Check", false, 0f, conflictMessage);
                }
            }
        }
    }
    
    void ValidateDeviceSwitching()
    {
        if (inputSystem == null) return;
        
        // Test seamless device switching
        var currentDevice = inputSystem.GetActiveDeviceType();
        
        // Simulate device switching scenarios
        if (Input.GetKeyDown(KeyCode.F4))
        {
            TestDeviceSwitch(MultiInputControlSystem.InputDeviceType.KeyboardMouse);
        }
        else if (Input.GetKeyDown(KeyCode.F5))
        {
            TestDeviceSwitch(MultiInputControlSystem.InputDeviceType.PlayStation);
        }
        else if (Input.GetKeyDown(KeyCode.F6))
        {
            TestDeviceSwitch(MultiInputControlSystem.InputDeviceType.Xbox);
        }
    }
    
    void TestDeviceSwitch(MultiInputControlSystem.InputDeviceType targetDevice)
    {
        float switchStartTime = Time.time;
        
        // Simulate device switch
        StartCoroutine(ValidateDeviceSwitchCoroutine(targetDevice, switchStartTime));
    }
    
    IEnumerator ValidateDeviceSwitchCoroutine(MultiInputControlSystem.InputDeviceType targetDevice, float startTime)
    {
        yield return new WaitForSeconds(0.1f);
        
        float switchTime = Time.time - startTime;
        bool switchSuccessful = inputSystem.GetActiveDeviceType() == targetDevice;
        
        string testName = $"Device Switch to {targetDevice}";
        LogTestResult(testName, switchSuccessful, switchTime, 
                     switchSuccessful ? "" : "Device switch failed or took too long");
    }
    
    void TestVibrationFeedback()
    {
        if (inputSystem == null || !inputSystem.IsControllerConnected()) return;
        
        // Test vibration on specific inputs
        if (Input.GetKeyDown(KeyCode.F7))
        {
            inputSystem.TriggerVibration(MultiInputControlSystem.VibrationProfile.VibrationTrigger.WeaponHit);
            LogTestResult("Vibration Test - Weapon Hit", true, 0f, "Vibration triggered");
        }
        
        if (Input.GetKeyDown(KeyCode.F8))
        {
            inputSystem.TriggerEmotionalVibration();
            LogTestResult("Vibration Test - Emotional", true, 0f, "Emotional vibration triggered");
        }
    }
    
    void RunQuickInputTest()
    {
        StartCoroutine(QuickInputTestCoroutine());
    }
    
    IEnumerator QuickInputTestCoroutine()
    {
        ShowTestMessage("Running Quick Input Test...");
        
        // Test basic inputs
        yield return TestBasicInputs();
        
        // Test UI navigation
        yield return TestUINavigation();
        
        // Test device detection
        yield return TestDeviceDetection();
        
        ShowTestMessage("Quick Input Test Complete");
        DisplayTestResults();
    }
    
    IEnumerator TestBasicInputs()
    {
        ShowTestMessage("Testing basic inputs...");
        
        string[] basicInputs = { "Move", "Look", "Attack", "Jump", "Interact" };
        
        foreach (string input in basicInputs)
        {
            ShowTestMessage($"Test input: {input}");
            yield return new WaitForSeconds(1f);
            
            // Simulate input test
            bool testPassed = Random.Range(0f, 1f) > 0.1f; // 90% success rate for simulation
            LogTestResult($"Basic Input - {input}", testPassed, Random.Range(0.01f, 0.05f), 
                         testPassed ? "" : "Input not responding");
        }
    }
    
    IEnumerator TestUINavigation()
    {
        ShowTestMessage("Testing UI navigation...");
        
        // Test menu navigation
        yield return new WaitForSeconds(0.5f);
        LogTestResult("UI Navigation", true, 0.02f, "");
        
        // Test button prompts
        if (promptSystem != null)
        {
            promptSystem.ShowInteractionPrompt("Test Prompt");
            yield return new WaitForSeconds(1f);
            promptSystem.HideInteractionPrompt();
            LogTestResult("Button Prompts", true, 0.01f, "");
        }
    }
    
    IEnumerator TestDeviceDetection()
    {
        ShowTestMessage("Testing device detection...");
        
        if (inputSystem != null)
        {
            bool keyboardDetected = inputSystem.IsKeyboardMouse();
            bool controllerDetected = inputSystem.IsControllerConnected();
            
            LogTestResult("Keyboard Detection", keyboardDetected, 0f, 
                         keyboardDetected ? "" : "Keyboard not detected");
            LogTestResult("Controller Detection", controllerDetected, 0f, 
                         controllerDetected ? "" : "No controller detected");
        }
        
        yield return new WaitForSeconds(0.5f);
    }
    
    IEnumerator RunAutomatedTestSuite()
    {
        ShowTestMessage("Running Automated Test Suite...");
        
        foreach (var scenario in testScenarios)
        {
            yield return RunTestScenario(scenario);
        }
        
        ShowTestMessage("Automated Test Suite Complete");
        GenerateTestReport();
    }
    
    IEnumerator RunTestScenario(TestScenario scenario)
    {
        ShowTestMessage($"Testing: {scenario.scenarioName}");
        
        float scenarioStartTime = Time.time;
        currentTestState = scenario.gameplayState;
        
        // Show appropriate prompts
        if (scenario.shouldShowPrompts && promptSystem != null)
        {
            foreach (string input in scenario.requiredInputs)
            {
                promptSystem.ShowPrompt($"test_{input}", input, $"Test {input}", Vector3.zero);
            }
        }
        
        // Wait for test duration
        yield return new WaitForSeconds(scenario.timeLimit);
        
        // Evaluate test results
        float testTime = Time.time - scenarioStartTime;
        bool testPassed = EvaluateScenario(scenario);
        
        scenario.testPassed = testPassed;
        scenario.testScore = testPassed ? 100f : Random.Range(30f, 70f);
        
        LogTestResult(scenario.scenarioName, testPassed, testTime, 
                     testPassed ? "" : "Scenario requirements not met");
        
        // Hide prompts
        if (promptSystem != null)
        {
            promptSystem.HideAllPrompts();
        }
        
        yield return new WaitForSeconds(1f);
    }
    
    bool EvaluateScenario(TestScenario scenario)
    {
        // Simulate scenario evaluation
        // In a real implementation, this would check actual input responses
        return Random.Range(0f, 1f) > 0.2f; // 80% success rate for simulation
    }
    
    void CycleTestState()
    {
        int currentIndex = (int)currentTestState;
        currentIndex = (currentIndex + 1) % System.Enum.GetValues(typeof(GameplayState)).Length;
        currentTestState = (GameplayState)currentIndex;
        
        ShowTestMessage($"Test State: {currentTestState}");
    }
    
    void TestAllInputDevices()
    {
        StartCoroutine(TestAllInputDevicesCoroutine());
    }
    
    IEnumerator TestAllInputDevicesCoroutine()
    {
        ShowTestMessage("Testing all input devices...");
        
        // Test keyboard/mouse
        yield return TestSpecificDevice(MultiInputControlSystem.InputDeviceType.KeyboardMouse);
        
        // Test PlayStation controller
        if (inputSystem != null && inputSystem.IsControllerConnected())
        {
            yield return TestSpecificDevice(MultiInputControlSystem.InputDeviceType.PlayStation);
            yield return TestSpecificDevice(MultiInputControlSystem.InputDeviceType.Xbox);
        }
        
        ShowTestMessage("All device testing complete");
    }
    
    IEnumerator TestSpecificDevice(MultiInputControlSystem.InputDeviceType deviceType)
    {
        ShowTestMessage($"Testing {deviceType}...");
        
        // Simulate device-specific tests
        yield return new WaitForSeconds(2f);
        
        bool deviceWorking = Random.Range(0f, 1f) > 0.1f; // 90% success rate
        LogTestResult($"Device Test - {deviceType}", deviceWorking, Random.Range(0.5f, 2f), 
                     deviceWorking ? "" : "Device not responding properly");
    }
    
    void LogTestResult(string testName, bool passed, float responseTime, string errorMessage)
    {
        InputTestResult result = new InputTestResult
        {
            testName = testName,
            state = currentTestState,
            deviceType = inputSystem?.GetActiveDeviceType() ?? MultiInputControlSystem.InputDeviceType.KeyboardMouse,
            passed = passed,
            responseTime = responseTime,
            errorMessage = errorMessage,
            timestamp = Time.time
        };
        
        testResults.Add(result);
        
        if (logInputEvents)
        {
            string status = passed ? "PASS" : "FAIL";
            Debug.Log($"[INPUT TEST] {status}: {testName} ({responseTime:F3}s) - {errorMessage}");
        }
    }
    
    void DisplayDebugInfo()
    {
        // Display debug information on screen
        if (inputSystem != null)
        {
            string debugText = $"Input Device: {inputSystem.GetActiveDeviceType()}\n";
            debugText += $"Test State: {currentTestState}\n";
            debugText += $"Tests Run: {testResults.Count}\n";
            debugText += $"Tests Passed: {testResults.FindAll(r => r.passed).Count}\n";
            
            // This would be displayed on a debug UI panel
            // For now, we'll just log it periodically
            if (Time.time % 5f < Time.deltaTime)
            {
                Debug.Log($"[INPUT DEBUG] {debugText}");
            }
        }
    }
    
    void DisplayTestResults()
    {
        int totalTests = testResults.Count;
        int passedTests = testResults.FindAll(r => r.passed).Count;
        float successRate = totalTests > 0 ? (float)passedTests / totalTests * 100f : 0f;
        
        ShowTestMessage($"Test Results: {passedTests}/{totalTests} ({successRate:F1}% success rate)");
    }
    
    void GenerateTestReport()
    {
        string report = "=== INPUT TESTING REPORT ===\n";
        report += $"Total Tests: {testResults.Count}\n";
        report += $"Passed: {testResults.FindAll(r => r.passed).Count}\n";
        report += $"Failed: {testResults.FindAll(r => !r.passed).Count}\n\n";
        
        report += "FAILED TESTS:\n";
        foreach (var result in testResults)
        {
            if (!result.passed)
            {
                report += $"- {result.testName}: {result.errorMessage}\n";
            }
        }
        
        Debug.Log(report);
    }
    
    void ShowTestMessage(string message)
    {
        Debug.Log($"[INPUT TEST] {message}");
        
        // Show on UI if available
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"[TEST] {message}");
        }
    }
    
    // Public interface
    public void EnableTesting(bool enable)
    {
        enableInputTesting = enable;
        if (enable)
        {
            StartInputTesting();
        }
        else
        {
            isTestingActive = false;
        }
    }
    
    public void RunSpecificTest(string testName)
    {
        var scenario = System.Array.Find(testScenarios, s => s.scenarioName == testName);
        if (scenario != null)
        {
            StartCoroutine(RunTestScenario(scenario));
        }
    }
    
    public List<InputTestResult> GetTestResults() => testResults;
    public void ClearTestResults() => testResults.Clear();
}
