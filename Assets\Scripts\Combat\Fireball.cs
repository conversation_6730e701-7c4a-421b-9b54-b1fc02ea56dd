using UnityEngine;

public class Fireball : MonoBehaviour
{
    [Header("Fireball Settings")]
    public float damage = 30f;
    public float lifetime = 5f;
    public float explosionRadius = 3f;
    public GameObject explosionEffect;
    public LayerMask enemyLayer;
    
    [Header("Visual Effects")]
    public ParticleSystem trailEffect;
    public Light fireLight;
    
    private Rigidbody rb;
    private bool hasExploded = false;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        
        // Destroy fireball after lifetime
        Destroy(gameObject, lifetime);
        
        // Setup visual effects
        if (fireLight == null)
        {
            fireLight = GetComponent<Light>();
        }
        
        if (fireLight != null)
        {
            fireLight.color = Color.red;
            fireLight.intensity = 2f;
            fireLight.range = 5f;
        }
    }
    
    void Update()
    {
        // Add some rotation for visual effect
        transform.Rotate(Vector3.forward * 360f * Time.deltaTime);
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (hasExploded) return;
        
        // Don't explode on the player who cast it
        if (other.CompareTag("Player")) return;
        
        // Explode on contact with enemies or environment
        if (other.CompareTag("Enemy") || other.CompareTag("Environment") || other.CompareTag("Ground"))
        {
            Explode();
        }
    }
    
    void OnCollisionEnter(Collision collision)
    {
        if (hasExploded) return;
        
        // Don't explode on the player who cast it
        if (collision.gameObject.CompareTag("Player")) return;
        
        Explode();
    }
    
    void Explode()
    {
        if (hasExploded) return;
        hasExploded = true;
        
        // Create explosion effect
        if (explosionEffect != null)
        {
            Instantiate(explosionEffect, transform.position, Quaternion.identity);
        }
        else
        {
            // Create simple explosion effect
            CreateSimpleExplosion();
        }
        
        // Deal damage to nearby enemies
        DealExplosionDamage();
        
        // Destroy the fireball
        Destroy(gameObject);
    }
    
    void CreateSimpleExplosion()
    {
        // Create a simple explosion effect using primitives
        GameObject explosion = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        explosion.transform.position = transform.position;
        explosion.transform.localScale = Vector3.one * explosionRadius;
        
        // Make it red and transparent
        Renderer explosionRenderer = explosion.GetComponent<Renderer>();
        Material explosionMat = new Material(Shader.Find("Standard"));
        explosionMat.color = new Color(1f, 0.3f, 0f, 0.5f);
        explosionMat.SetFloat("_Mode", 3); // Transparent mode
        explosionMat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        explosionMat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        explosionMat.SetInt("_ZWrite", 0);
        explosionMat.DisableKeyword("_ALPHATEST_ON");
        explosionMat.EnableKeyword("_ALPHABLEND_ON");
        explosionMat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        explosionMat.renderQueue = 3000;
        explosionRenderer.material = explosionMat;
        
        // Remove collider
        Destroy(explosion.GetComponent<Collider>());
        
        // Animate the explosion
        StartCoroutine(AnimateExplosion(explosion));
    }
    
    System.Collections.IEnumerator AnimateExplosion(GameObject explosion)
    {
        float duration = 0.5f;
        float elapsed = 0f;
        Vector3 startScale = Vector3.zero;
        Vector3 endScale = Vector3.one * explosionRadius;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            // Scale up then fade out
            explosion.transform.localScale = Vector3.Lerp(startScale, endScale, progress);
            
            // Fade out
            Renderer renderer = explosion.GetComponent<Renderer>();
            if (renderer != null)
            {
                Color color = renderer.material.color;
                color.a = 1f - progress;
                renderer.material.color = color;
            }
            
            yield return null;
        }
        
        Destroy(explosion);
    }
    
    void DealExplosionDamage()
    {
        // Find all colliders in explosion radius
        Collider[] hitColliders = Physics.OverlapSphere(transform.position, explosionRadius, enemyLayer);
        
        foreach (Collider hitCollider in hitColliders)
        {
            // Calculate distance-based damage
            float distance = Vector3.Distance(transform.position, hitCollider.transform.position);
            float damageMultiplier = 1f - (distance / explosionRadius);
            float finalDamage = damage * damageMultiplier;
            
            // Apply damage to enemy
            EnemyHealth enemyHealth = hitCollider.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                enemyHealth.TakeDamage(finalDamage);
                Debug.Log($"Fireball explosion hit {hitCollider.name} for {finalDamage} damage!");
            }
            
            // Apply knockback
            Rigidbody enemyRb = hitCollider.GetComponent<Rigidbody>();
            if (enemyRb != null)
            {
                Vector3 knockbackDirection = (hitCollider.transform.position - transform.position).normalized;
                float knockbackForce = 10f * damageMultiplier;
                enemyRb.AddForce(knockbackDirection * knockbackForce, ForceMode.Impulse);
            }
        }
    }
    
    public void SetDamage(float newDamage)
    {
        damage = newDamage;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw explosion radius in editor
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, explosionRadius);
    }
}
