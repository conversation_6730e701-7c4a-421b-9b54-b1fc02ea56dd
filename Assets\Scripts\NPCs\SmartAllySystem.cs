using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class SmartAllySystem : MonoBehaviour
{
    [Header("Ally Management")]
    public List<SmartAlly> activeAllies = new List<SmartAlly>();
    public List<SmartAlly> availableAllies = new List<SmartAlly>();
    public int maxActiveAllies = 2;
    
    [Header("Loyalty Tracking")]
    public float globalLoyaltyModifier = 1f;
    public Dictionary<string, float> allyLoyaltyHistory = new Dictionary<string, float>();
    
    private PhilosophicalMoralitySystem moralitySystem;
    private DeathConsequenceSystem deathSystem;
    private DynamicTitleSystem titleSystem;
    private EconomySystem economySystem;
    private GameManager gameManager;
    
    [System.Serializable]
    public class SmartAlly
    {
        [Header("Identity")]
        public string allyName;
        public string backstory;
        public AllyType type;
        public AllyPersonality personality;
        
        [Header("Loyalty System")]
        public float currentLoyalty = 50f; // 0-100
        public float maxLoyalty = 100f;
        public LoyaltyCriteria[] loyaltyCriteria;
        public float loyaltyDecayRate = 1f; // Per day
        
        [Header("Combat & Skills")]
        public float combatEffectiveness = 75f;
        public string[] specialAbilities;
        public WeaponPreference weaponPreference;
        public float healingAbility = 0f;
        
        [Header("Moral Alignment")]
        public PhilosophicalMoralitySystem.MoralPath preferredPath;
        public float moralFlexibility = 25f; // How much they tolerate opposing choices
        public string[] dealBreakers; // Actions that cause immediate abandonment
        
        [Header("Economic Needs")]
        public bool requiresPayment = false;
        public float paymentAmount = 50f;
        public int daysSinceLastPayment = 0;
        public int maxDaysWithoutPayment = 7;
        
        [Header("Dialogue & Reactions")]
        public string[] loyalDialogue;
        public string[] neutralDialogue;
        public string[] displeasedDialogue;
        public string[] leavingDialogue;
        public string[] returningDialogue;
        
        [Header("Visual & Audio")]
        public GameObject allyModel;
        public AudioClip[] voiceLines;
        public Color loyaltyIndicatorColor;
        
        public enum AllyType
        {
            HonorBound,     // Leaves if player acts dishonorably
            Mercenary,      // Requires payment, flexible morals
            Idealist,       // Strict moral requirements
            Pragmatist,     // Adapts to player's choices
            Survivor,       // Stays as long as it's beneficial
            Fanatic,        // Extreme loyalty once earned
            Mentor,         // Guides and judges player
            Student         // Learns from player's example
        }
        
        public enum AllyPersonality
        {
            Stoic,
            Cheerful,
            Cynical,
            Wise,
            Naive,
            Aggressive,
            Protective,
            Mysterious
        }
        
        public enum WeaponPreference
        {
            Sword,
            Bow,
            Magic,
            Hammer,
            Daggers,
            Staff,
            Fists
        }
    }
    
    [System.Serializable]
    public class LoyaltyCriteria
    {
        public CriteriaType type;
        public float loyaltyChange;
        public string description;
        public bool isPositive;
        
        public enum CriteriaType
        {
            InnocentKilled,
            VillainKilled,
            ChildHelped,
            ElderRespected,
            PaymentReceived,
            PaymentMissed,
            MoralChoiceAligned,
            MoralChoiceOpposed,
            PlayerInjured,
            AllyInjured,
            QuestCompleted,
            LieDetected,
            TruthTold,
            PromiseKept,
            PromiseBroken
        }
    }
    
    void Start()
    {
        moralitySystem = GetComponent<PhilosophicalMoralitySystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        titleSystem = GetComponent<DynamicTitleSystem>();
        economySystem = GetComponent<EconomySystem>();
        gameManager = GameManager.Instance;
        
        InitializeAllySystem();
        CreateAvailableAllies();
    }
    
    void Update()
    {
        UpdateAllyLoyalty();
        CheckAllyReactions();
        HandleAllyDecisions();
    }
    
    void InitializeAllySystem()
    {
        Debug.Log("Smart Ally System initialized - allies will react dynamically to player choices");
    }
    
    void CreateAvailableAllies()
    {
        // Sir Marcus - Honor-bound knight
        SmartAlly marcus = new SmartAlly
        {
            allyName = "Sir Marcus Ironheart",
            backstory = "A fallen knight seeking redemption. He believes in honor above all else and will not tolerate cruelty to innocents.",
            type = SmartAlly.AllyType.HonorBound,
            personality = SmartAlly.AllyPersonality.Stoic,
            currentLoyalty = 60f,
            preferredPath = PhilosophicalMoralitySystem.MoralPath.Good,
            moralFlexibility = 10f, // Very strict
            dealBreakers = new string[] { "InnocentMassacre", "ChildKilling", "ElderAbuse" },
            combatEffectiveness = 85f,
            weaponPreference = SmartAlly.WeaponPreference.Sword,
            loyaltyCriteria = new LoyaltyCriteria[]
            {
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.InnocentKilled, loyaltyChange = -25f, isPositive = false },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.VillainKilled, loyaltyChange = 10f, isPositive = true },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.ChildHelped, loyaltyChange = 15f, isPositive = true }
            },
            loyalDialogue = new string[]
            {
                "Your honor shines bright, my friend. I am proud to stand beside you.",
                "Together we shall bring justice to this dark world.",
                "Your noble heart gives me hope for redemption."
            },
            displeasedDialogue = new string[]
            {
                "I question your methods, Cinderborn. This is not the way of honor.",
                "Your actions trouble me greatly. We must speak of this.",
                "I fear you are losing your way. Remember who you wish to be."
            },
            leavingDialogue = new string[]
            {
                "I cannot... I will not be party to such cruelty. Farewell, Cinderborn.",
                "You have become everything I swore to fight against. Our paths diverge here.",
                "May the gods forgive you, for I cannot. I must leave."
            }
        };
        
        // Vera the Coin - Mercenary
        SmartAlly vera = new SmartAlly
        {
            allyName = "Vera the Coin",
            backstory = "A pragmatic mercenary who values gold above morals. She'll stick around as long as the pay is good and the work isn't suicidal.",
            type = SmartAlly.AllyType.Mercenary,
            personality = SmartAlly.AllyPersonality.Cynical,
            currentLoyalty = 40f,
            preferredPath = PhilosophicalMoralitySystem.MoralPath.Neutral,
            moralFlexibility = 80f, // Very flexible
            requiresPayment = true,
            paymentAmount = 75f,
            maxDaysWithoutPayment = 5,
            combatEffectiveness = 70f,
            weaponPreference = SmartAlly.WeaponPreference.Bow,
            loyaltyCriteria = new LoyaltyCriteria[]
            {
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.PaymentReceived, loyaltyChange = 20f, isPositive = true },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.PaymentMissed, loyaltyChange = -15f, isPositive = false },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.QuestCompleted, loyaltyChange = 5f, isPositive = true }
            },
            loyalDialogue = new string[]
            {
                "You pay well and fight better. I like our arrangement.",
                "Gold talks, and yours speaks my language perfectly.",
                "As long as the coin flows, my blade is yours."
            },
            displeasedDialogue = new string[]
            {
                "My purse is getting light, Cinderborn. Time to remedy that.",
                "I'm not running a charity here. Where's my payment?",
                "You're testing my patience... and my loyalty."
            },
            leavingDialogue = new string[]
            {
                "No pay, no loyalty. Find yourself another fool to work for free.",
                "I've got bills to pay and you're not helping. Goodbye.",
                "When you remember how to value good help, you know where to find me."
            }
        };
        
        // Kira Dawnseeker - Idealist
        SmartAlly kira = new SmartAlly
        {
            allyName = "Kira Dawnseeker",
            backstory = "A young idealist who believes in the power of redemption and second chances. She sees potential for good in everyone.",
            type = SmartAlly.AllyType.Idealist,
            personality = SmartAlly.AllyPersonality.Cheerful,
            currentLoyalty = 70f,
            preferredPath = PhilosophicalMoralitySystem.MoralPath.Good,
            moralFlexibility = 30f,
            combatEffectiveness = 60f,
            healingAbility = 40f,
            weaponPreference = SmartAlly.WeaponPreference.Staff,
            loyaltyCriteria = new LoyaltyCriteria[]
            {
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.ChildHelped, loyaltyChange = 20f, isPositive = true },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.InnocentKilled, loyaltyChange = -30f, isPositive = false },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.MoralChoiceAligned, loyaltyChange = 10f, isPositive = true }
            },
            loyalDialogue = new string[]
            {
                "Your kindness gives me hope, Cinderborn. The world needs more souls like yours.",
                "Together we can bring light to even the darkest places!",
                "I believe in you, and I believe in the good you can do."
            },
            displeasedDialogue = new string[]
            {
                "I... I don't understand why you did that. There had to be another way.",
                "Please, Cinderborn, remember the person you want to be.",
                "This isn't you. I know there's goodness in your heart."
            },
            leavingDialogue = new string[]
            {
                "I can't watch you destroy yourself like this. Maybe... maybe someday you'll find your way back.",
                "I still believe in you, but I can't be part of this darkness. Farewell.",
                "When you're ready to choose light over shadow, I'll be waiting."
            }
        };
        
        // Grimm Shadowbane - Fanatic
        SmartAlly grimm = new SmartAlly
        {
            allyName = "Grimm Shadowbane",
            backstory = "A dark warrior who respects only strength and ruthlessness. Once you earn his loyalty, he'll follow you into hell itself.",
            type = SmartAlly.AllyType.Fanatic,
            personality = SmartAlly.AllyPersonality.Aggressive,
            currentLoyalty = 20f, // Starts low, but can become extremely high
            preferredPath = PhilosophicalMoralitySystem.MoralPath.Evil,
            moralFlexibility = 60f,
            combatEffectiveness = 95f,
            weaponPreference = SmartAlly.WeaponPreference.Hammer,
            loyaltyCriteria = new LoyaltyCriteria[]
            {
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.VillainKilled, loyaltyChange = 15f, isPositive = true },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.InnocentKilled, loyaltyChange = 5f, isPositive = true },
                new LoyaltyCriteria { type = LoyaltyCriteria.CriteriaType.MoralChoiceAligned, loyaltyChange = 20f, isPositive = true }
            },
            loyalDialogue = new string[]
            {
                "You have proven yourself worthy, Cinderborn. I would die for you.",
                "Your strength commands my absolute loyalty. Lead, and I follow.",
                "Together we are unstoppable. The weak shall tremble before us."
            },
            displeasedDialogue = new string[]
            {
                "Weakness disgusts me. Show me the strength I know you possess.",
                "You disappoint me, Cinderborn. Where is your killer instinct?",
                "Mercy is for the weak. I expected better from you."
            }
        };
        
        availableAllies.Add(marcus);
        availableAllies.Add(vera);
        availableAllies.Add(kira);
        availableAllies.Add(grimm);
        
        Debug.Log($"Created {availableAllies.Count} potential allies with unique loyalty systems");
    }
    
    void UpdateAllyLoyalty()
    {
        foreach (SmartAlly ally in activeAllies)
        {
            // Daily loyalty decay
            if (gameManager != null)
            {
                float daysPassed = gameManager.GetDaysPassed();
                if (daysPassed > 0)
                {
                    ally.currentLoyalty = Mathf.Max(0f, ally.currentLoyalty - (ally.loyaltyDecayRate * daysPassed));
                }
            }
            
            // Payment tracking for mercenaries
            if (ally.requiresPayment)
            {
                ally.daysSinceLastPayment++;
                if (ally.daysSinceLastPayment >= ally.maxDaysWithoutPayment)
                {
                    ApplyLoyaltyChange(ally, LoyaltyCriteria.CriteriaType.PaymentMissed);
                }
            }
            
            // Update loyalty history
            allyLoyaltyHistory[ally.allyName] = ally.currentLoyalty;
        }
    }
    
    void CheckAllyReactions()
    {
        // Check for recent player actions that affect loyalty
        foreach (SmartAlly ally in activeAllies)
        {
            CheckMoralAlignmentReaction(ally);
            CheckTitleReaction(ally);
            CheckRecentActions(ally);
        }
    }
    
    void CheckMoralAlignmentReaction(SmartAlly ally)
    {
        if (moralitySystem == null) return;
        
        PhilosophicalMoralitySystem.MoralPath playerPath = moralitySystem.GetCurrentPath();
        
        if (playerPath == ally.preferredPath)
        {
            ApplyLoyaltyChange(ally, LoyaltyCriteria.CriteriaType.MoralChoiceAligned);
        }
        else if (ally.moralFlexibility < 50f) // Strict allies
        {
            ApplyLoyaltyChange(ally, LoyaltyCriteria.CriteriaType.MoralChoiceOpposed);
        }
    }
    
    void CheckTitleReaction(SmartAlly ally)
    {
        if (titleSystem == null) return;
        
        string currentTitle = titleSystem.GetCurrentTitleName();
        
        // Allies react to player's reputation
        switch (ally.type)
        {
            case SmartAlly.AllyType.HonorBound:
                if (currentTitle.Contains("Butcher") || currentTitle.Contains("Reaper"))
                {
                    ally.currentLoyalty -= 5f * Time.deltaTime;
                }
                else if (currentTitle.Contains("Redeemed") || currentTitle.Contains("Ashspeaker"))
                {
                    ally.currentLoyalty += 2f * Time.deltaTime;
                }
                break;
            
            case SmartAlly.AllyType.Fanatic:
                if (currentTitle.Contains("Reaper") || currentTitle.Contains("Butcher"))
                {
                    ally.currentLoyalty += 3f * Time.deltaTime;
                }
                break;
        }
    }
    
    void CheckRecentActions(SmartAlly ally)
    {
        // Check for deal-breaker actions
        if (ally.dealBreakers != null)
        {
            foreach (string dealBreaker in ally.dealBreakers)
            {
                if (gameManager != null && gameManager.HasRecentlyPerformed(dealBreaker))
                {
                    TriggerAllyLeaving(ally, $"I cannot forgive {dealBreaker}. We are done.");
                    return;
                }
            }
        }
    }
    
    void HandleAllyDecisions()
    {
        List<SmartAlly> alliesToRemove = new List<SmartAlly>();
        
        foreach (SmartAlly ally in activeAllies)
        {
            if (ally.currentLoyalty <= 0f)
            {
                TriggerAllyLeaving(ally, GetLeavingDialogue(ally));
                alliesToRemove.Add(ally);
            }
            else if (ally.currentLoyalty <= 25f && Random.Range(0f, 1f) < 0.01f)
            {
                TriggerAllyWarning(ally);
            }
        }
        
        // Remove allies who left
        foreach (SmartAlly ally in alliesToRemove)
        {
            activeAllies.Remove(ally);
        }
    }
    
    void TriggerAllyLeaving(SmartAlly ally, string reason)
    {
        ShowAllyMessage($"{ally.allyName}: {reason}");
        
        // Play leaving sequence
        StartCoroutine(AllyLeavingSequence(ally));
        
        Debug.Log($"Ally {ally.allyName} is leaving due to: {reason}");
    }
    
    IEnumerator AllyLeavingSequence(SmartAlly ally)
    {
        // Show dramatic leaving sequence
        ShowAllyMessage($"{ally.allyName} turns away, disappointment clear in their eyes.");
        yield return new WaitForSeconds(2f);
        
        ShowAllyMessage("They walk away without looking back.");
        yield return new WaitForSeconds(2f);
        
        // Remove ally from active list
        activeAllies.Remove(ally);
        
        // Add back to available allies with reduced starting loyalty
        ally.currentLoyalty = Mathf.Max(10f, ally.currentLoyalty * 0.5f);
        if (!availableAllies.Contains(ally))
        {
            availableAllies.Add(ally);
        }
    }
    
    void TriggerAllyWarning(SmartAlly ally)
    {
        string warning = GetDispleasedDialogue(ally);
        ShowAllyMessage($"{ally.allyName}: {warning}");
    }
    
    void ApplyLoyaltyChange(SmartAlly ally, LoyaltyCriteria.CriteriaType criteriaType)
    {
        foreach (LoyaltyCriteria criteria in ally.loyaltyCriteria)
        {
            if (criteria.type == criteriaType)
            {
                float oldLoyalty = ally.currentLoyalty;
                ally.currentLoyalty = Mathf.Clamp(ally.currentLoyalty + criteria.loyaltyChange, 0f, ally.maxLoyalty);
                
                if (Mathf.Abs(criteria.loyaltyChange) > 5f)
                {
                    string reaction = criteria.isPositive ? "approves" : "disapproves";
                    ShowAllyMessage($"{ally.allyName} {reaction} of your actions. (Loyalty: {oldLoyalty:F0} → {ally.currentLoyalty:F0})");
                }
                
                break;
            }
        }
    }
    
    public bool RecruitAlly(string allyName)
    {
        if (activeAllies.Count >= maxActiveAllies)
        {
            ShowAllyMessage("You cannot recruit more allies at this time.");
            return false;
        }
        
        SmartAlly ally = availableAllies.Find(a => a.allyName == allyName);
        if (ally == null)
        {
            ShowAllyMessage($"{allyName} is not available for recruitment.");
            return false;
        }
        
        // Check recruitment requirements
        if (!CanRecruitAlly(ally))
        {
            return false;
        }
        
        // Move to active allies
        availableAllies.Remove(ally);
        activeAllies.Add(ally);
        
        ShowAllyMessage($"{ally.allyName} joins your cause.");
        
        return true;
    }
    
    bool CanRecruitAlly(SmartAlly ally)
    {
        // Check if player's current reputation/title allows recruitment
        if (titleSystem != null)
        {
            string currentTitle = titleSystem.GetCurrentTitleName();
            
            switch (ally.type)
            {
                case SmartAlly.AllyType.HonorBound:
                    if (currentTitle.Contains("Butcher") || currentTitle.Contains("Reaper"))
                    {
                        ShowAllyMessage($"{ally.allyName}: I cannot serve one with such a dark reputation.");
                        return false;
                    }
                    break;
                
                case SmartAlly.AllyType.Idealist:
                    if (deathSystem != null && deathSystem.GetInnocentsKilled() > 5)
                    {
                        ShowAllyMessage($"{ally.allyName}: Your hands are stained with innocent blood. I cannot join you.");
                        return false;
                    }
                    break;
            }
        }
        
        return true;
    }
    
    public void PayAlly(string allyName, float amount)
    {
        SmartAlly ally = activeAllies.Find(a => a.allyName == allyName);
        if (ally == null || !ally.requiresPayment) return;
        
        if (economySystem != null && economySystem.SpendMoney(amount))
        {
            ally.daysSinceLastPayment = 0;
            ApplyLoyaltyChange(ally, LoyaltyCriteria.CriteriaType.PaymentReceived);
            
            ShowAllyMessage($"{ally.allyName}: Payment received. Our contract continues.");
        }
        else
        {
            ShowAllyMessage("You don't have enough gold to pay your ally.");
        }
    }
    
    string GetLoyalDialogue(SmartAlly ally)
    {
        if (ally.loyalDialogue.Length > 0)
            return ally.loyalDialogue[Random.Range(0, ally.loyalDialogue.Length)];
        return "I am honored to serve alongside you.";
    }
    
    string GetDispleasedDialogue(SmartAlly ally)
    {
        if (ally.displeasedDialogue.Length > 0)
            return ally.displeasedDialogue[Random.Range(0, ally.displeasedDialogue.Length)];
        return "I am not pleased with your recent actions.";
    }
    
    string GetLeavingDialogue(SmartAlly ally)
    {
        if (ally.leavingDialogue.Length > 0)
            return ally.leavingDialogue[Random.Range(0, ally.leavingDialogue.Length)];
        return "I can no longer follow you. Farewell.";
    }
    
    void ShowAllyMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Ally System: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public List<SmartAlly> GetActiveAllies() => activeAllies;
    public List<SmartAlly> GetAvailableAllies() => availableAllies;
    public float GetAllyLoyalty(string allyName)
    {
        SmartAlly ally = activeAllies.Find(a => a.allyName == allyName);
        return ally != null ? ally.currentLoyalty : 0f;
    }
}
