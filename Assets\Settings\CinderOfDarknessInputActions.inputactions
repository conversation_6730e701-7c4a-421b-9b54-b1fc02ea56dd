{"name": "CinderOfDarknessInputActions", "maps": [{"name": "Gameplay", "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "actions": [{"name": "Move", "type": "Value", "id": "b2c3d4e5-f678-9012-3456-7890abcdef12", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "c3d4e5f6-7890-1234-5678-90abcdef1234", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Attack", "type": "<PERSON><PERSON>", "id": "d4e5f678-9012-3456-7890-abcdef123456", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Block", "type": "<PERSON><PERSON>", "id": "e5f67890-1234-5678-90ab-cdef12345678", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Dodge", "type": "<PERSON><PERSON>", "id": "f6789012-3456-7890-abcd-ef1234567890", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "67890123-4567-890a-bcde-f12345678901", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "78901234-5678-90ab-cdef-123456789012", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Run", "type": "<PERSON><PERSON>", "id": "89012345-6789-0abc-def1-234567890123", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Pause", "type": "<PERSON><PERSON>", "id": "90123456-789a-bcde-f123-456789012345", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "WASD", "id": "01234567-89ab-cdef-1234-567890abcdef", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "12345678-9abc-def1-2345-67890abcdef1", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "23456789-abcd-ef12-3456-7890abcdef12", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "3456789a-bcde-f123-4567-890abcdef123", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "456789ab-cdef-1234-5678-90abcdef1234", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "56789abc-def1-2345-6789-0abcdef12345", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6789abcd-ef12-3456-789a-bcdef1234567", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "789abcde-f123-4567-89ab-cdef12345678", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "89abcdef-1234-5678-9abc-def123456789", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9abcdef1-2345-6789-abcd-ef1234567890", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "abcdef12-3456-789a-bcde-f12345678901", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Block", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bcdef123-4567-89ab-cdef-123456789012", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Block", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "cdef1234-5678-9abc-def1-234567890123", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Dodge", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "def12345-6789-abcd-ef12-345678901234", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Dodge", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ef123456-789a-bcde-f123-456789012345", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f1234567-89ab-cdef-1234-567890123456", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "12345678-9abc-def1-2345-678901234567", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "23456789-abcd-ef12-3456-789012345678", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3456789a-bcde-f123-4567-890123456789", "path": "<Keyboard>/leftCtrl", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "456789ab-cdef-1234-5678-90123456789a", "path": "<Gamepad>/leftStickPress", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "56789abc-def1-2345-6789-0123456789ab", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Pause", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6789abcd-ef12-3456-789a-123456789abc", "path": "<Gamepad>/start", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Pause", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "789abcde-f123-4567-89ab-23456789abcd", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "89abcdef-1234-5678-9abc-3456789abcde", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "9abcdef1-2345-6789-abcd-456789abcdef", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "abcdef12-3456-789a-bcde-56789abcdef1", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Keyboard", "id": "bcdef123-4567-89ab-cdef-6789abcdef12", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "cdef1234-5678-9abc-def1-789abcdef123", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "def12345-6789-abcd-ef12-89abcdef1234", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "ef123456-789a-bcde-f123-9abcdef12345", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "f1234567-89ab-cdef-1234-abcdef123456", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "12345678-9abc-def1-2345-bcdef1234567", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "23456789-abcd-ef12-3456-cdef12345678", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "3456789a-bcde-f123-4567-def123456789", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "456789ab-cdef-1234-5678-ef12345678901", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "56789abc-def1-2345-6789-f123456789012", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6789abcd-ef12-3456-789a-1234567890123", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "789abcde-f123-4567-89ab-2345678901234", "path": "<Keyboard>/enter", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "89abcdef-1234-5678-9abc-3456789012345", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9abcdef1-2345-6789-abcd-4567890123456", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "abcdef12-3456-789a-bcde-5678901234567", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}]}