using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Profiling;

/// <summary>
/// Comprehensive performance validation test to verify all optimizations are working correctly.
/// Monitors CPU usage, memory allocations, frame rate, and garbage collection.
/// </summary>
public class PerformanceValidationTest : MonoBehaviour
{
    #region Serialized Fields
    [Header("Performance Monitoring")]
    [SerializeField] private bool enableContinuousMonitoring = true;
    [SerializeField] private bool showPerformanceUI = true;
    [SerializeField] private float monitoringInterval = 1f;
    
    [Header("Performance Metrics")]
    [SerializeField] private float currentFPS;
    [SerializeField] private float averageFPS;
    [SerializeField] private long totalMemoryUsage;
    [SerializeField] private long gcMemoryUsage;
    [SerializeField] private int gcCollectionCount;
    [SerializeField] private float cpuUsagePercentage;
    
    [Header("Optimization Validation")]
    [SerializeField] private List<string> optimizationResults = new List<string>();
    [SerializeField] private int optimizationsValidated;
    [SerializeField] private float performanceScore;
    #endregion

    #region Private Fields
    private float[] fpsHistory = new float[60]; // Store 60 frames of FPS data
    private int fpsHistoryIndex = 0;
    private float lastMonitorTime = 0f;
    private int lastGCCount = 0;
    private long lastMemoryUsage = 0;
    
    // Performance thresholds
    private const float TARGET_FPS = 60f;
    private const long MAX_MEMORY_MB = 1200;
    private const int MAX_GC_PER_MINUTE = 8;
    
    // UI elements
    private GUIStyle performanceStyle;
    private bool showDetailedMetrics = false;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize performance monitoring.
    /// </summary>
    private void Start()
    {
        InitializePerformanceMonitoring();
        
        if (enableContinuousMonitoring)
        {
            StartCoroutine(ContinuousPerformanceMonitoring());
        }
        
        StartCoroutine(ValidateOptimizations());
    }

    /// <summary>
    /// Update FPS tracking every frame.
    /// </summary>
    private void Update()
    {
        // Track FPS every frame for accurate measurement
        UpdateFPSTracking();
    }

    /// <summary>
    /// Display performance UI if enabled.
    /// </summary>
    private void OnGUI()
    {
        if (showPerformanceUI)
        {
            DrawPerformanceUI();
        }
    }
    #endregion

    #region Performance Monitoring
    /// <summary>
    /// Initialize performance monitoring systems.
    /// </summary>
    private void InitializePerformanceMonitoring()
    {
        // Initialize FPS history array
        for (int i = 0; i < fpsHistory.Length; i++)
        {
            fpsHistory[i] = 60f; // Start with target FPS
        }
        
        // Initialize GUI style
        performanceStyle = new GUIStyle();
        performanceStyle.fontSize = 14;
        performanceStyle.normal.textColor = Color.white;
        
        // Get initial memory baseline
        lastMemoryUsage = Profiler.GetTotalAllocatedMemory(false);
        lastGCCount = System.GC.CollectionCount(0);
        
        Debug.Log("🚀 Performance Validation Test initialized");
    }

    /// <summary>
    /// Continuous performance monitoring coroutine.
    /// </summary>
    /// <returns>Monitoring coroutine</returns>
    private IEnumerator ContinuousPerformanceMonitoring()
    {
        while (enableContinuousMonitoring)
        {
            yield return new WaitForSeconds(monitoringInterval);
            
            UpdatePerformanceMetrics();
            ValidatePerformanceThresholds();
        }
    }

    /// <summary>
    /// Update FPS tracking every frame.
    /// </summary>
    private void UpdateFPSTracking()
    {
        // Calculate current FPS
        currentFPS = 1f / Time.unscaledDeltaTime;
        
        // Store in history array
        fpsHistory[fpsHistoryIndex] = currentFPS;
        fpsHistoryIndex = (fpsHistoryIndex + 1) % fpsHistory.Length;
        
        // Calculate average FPS
        float totalFPS = 0f;
        for (int i = 0; i < fpsHistory.Length; i++)
        {
            totalFPS += fpsHistory[i];
        }
        averageFPS = totalFPS / fpsHistory.Length;
    }

    /// <summary>
    /// Update comprehensive performance metrics.
    /// </summary>
    private void UpdatePerformanceMetrics()
    {
        // Memory metrics
        totalMemoryUsage = Profiler.GetTotalAllocatedMemory(false);
        gcMemoryUsage = System.GC.GetTotalMemory(false);
        
        // Garbage collection metrics
        int currentGCCount = System.GC.CollectionCount(0);
        gcCollectionCount = currentGCCount - lastGCCount;
        lastGCCount = currentGCCount;
        
        // CPU usage estimation (simplified)
        cpuUsagePercentage = Mathf.Clamp((TARGET_FPS - averageFPS) / TARGET_FPS * 100f, 0f, 100f);
    }

    /// <summary>
    /// Validate performance against target thresholds.
    /// </summary>
    private void ValidatePerformanceThresholds()
    {
        float currentTime = Time.time;
        if (currentTime - lastMonitorTime >= 60f) // Check every minute
        {
            lastMonitorTime = currentTime;
            
            // Check FPS threshold
            if (averageFPS >= TARGET_FPS * 0.9f) // 90% of target FPS
            {
                Debug.Log($"✅ FPS Performance: {averageFPS:F1} FPS (Target: {TARGET_FPS} FPS)");
            }
            else
            {
                Debug.LogWarning($"⚠️ FPS Performance: {averageFPS:F1} FPS (Below target: {TARGET_FPS} FPS)");
            }
            
            // Check memory threshold
            long memoryMB = totalMemoryUsage / (1024 * 1024);
            if (memoryMB <= MAX_MEMORY_MB)
            {
                Debug.Log($"✅ Memory Usage: {memoryMB} MB (Target: <{MAX_MEMORY_MB} MB)");
            }
            else
            {
                Debug.LogWarning($"⚠️ Memory Usage: {memoryMB} MB (Above target: {MAX_MEMORY_MB} MB)");
            }
            
            // Check GC frequency
            if (gcCollectionCount <= MAX_GC_PER_MINUTE)
            {
                Debug.Log($"✅ GC Collections: {gcCollectionCount}/min (Target: <{MAX_GC_PER_MINUTE}/min)");
            }
            else
            {
                Debug.LogWarning($"⚠️ GC Collections: {gcCollectionCount}/min (Above target: {MAX_GC_PER_MINUTE}/min)");
            }
        }
    }
    #endregion

    #region Optimization Validation
    /// <summary>
    /// Validate that all performance optimizations are working correctly.
    /// </summary>
    /// <returns>Validation coroutine</returns>
    private IEnumerator ValidateOptimizations()
    {
        Debug.Log("🔍 Starting optimization validation...");
        
        yield return new WaitForSeconds(2f); // Wait for systems to initialize
        
        optimizationResults.Clear();
        optimizationsValidated = 0;
        
        // Validate PsychologicalSystem optimizations
        yield return StartCoroutine(ValidatePsychologicalSystemOptimizations());
        
        // Validate TimeProgressionSystem optimizations
        yield return StartCoroutine(ValidateTimeProgressionOptimizations());
        
        // Validate general performance optimizations
        yield return StartCoroutine(ValidateGeneralOptimizations());
        
        // Calculate performance score
        CalculatePerformanceScore();
        
        // Display results
        DisplayOptimizationResults();
    }

    /// <summary>
    /// Validate PsychologicalSystem performance optimizations.
    /// </summary>
    /// <returns>Validation coroutine</returns>
    private IEnumerator ValidatePsychologicalSystemOptimizations()
    {
        PsychologicalSystem psychSystem = FindObjectOfType<PsychologicalSystem>();
        
        if (psychSystem != null)
        {
            optimizationResults.Add("✅ PsychologicalSystem: Found and accessible");
            optimizationsValidated++;
            
            // Test interval-based updates by monitoring frame rate stability
            float fpsBeforeTest = averageFPS;
            yield return new WaitForSeconds(5f); // Let system run for 5 seconds
            float fpsAfterTest = averageFPS;
            
            if (Mathf.Abs(fpsAfterTest - fpsBeforeTest) < 5f) // Stable FPS indicates good optimization
            {
                optimizationResults.Add("✅ PsychologicalSystem: Interval-based updates working (stable FPS)");
                optimizationsValidated++;
            }
            else
            {
                optimizationResults.Add("⚠️ PsychologicalSystem: FPS instability detected");
            }
        }
        else
        {
            optimizationResults.Add("⚠️ PsychologicalSystem: Not found in scene");
        }
    }

    /// <summary>
    /// Validate TimeProgressionSystem performance optimizations.
    /// </summary>
    /// <returns>Validation coroutine</returns>
    private IEnumerator ValidateTimeProgressionOptimizations()
    {
        TimeProgressionSystem timeSystem = FindObjectOfType<TimeProgressionSystem>();
        
        if (timeSystem != null)
        {
            optimizationResults.Add("✅ TimeProgressionSystem: Found and accessible");
            optimizationsValidated++;
            
            // Monitor memory allocations during time progression
            long memoryBefore = Profiler.GetTotalAllocatedMemory(false);
            yield return new WaitForSeconds(10f); // Let time system run
            long memoryAfter = Profiler.GetTotalAllocatedMemory(false);
            
            long memoryIncrease = memoryAfter - memoryBefore;
            if (memoryIncrease < 1024 * 1024) // Less than 1MB increase is good
            {
                optimizationResults.Add("✅ TimeProgressionSystem: Low memory allocation detected");
                optimizationsValidated++;
            }
            else
            {
                optimizationResults.Add($"⚠️ TimeProgressionSystem: High memory allocation: {memoryIncrease / 1024} KB");
            }
        }
        else
        {
            optimizationResults.Add("⚠️ TimeProgressionSystem: Not found in scene");
        }
    }

    /// <summary>
    /// Validate general performance optimizations.
    /// </summary>
    /// <returns>Validation coroutine</returns>
    private IEnumerator ValidateGeneralOptimizations()
    {
        // Test overall system performance
        yield return new WaitForSeconds(1f);
        
        // Validate FPS performance
        if (averageFPS >= TARGET_FPS * 0.8f) // 80% of target is acceptable
        {
            optimizationResults.Add($"✅ Overall FPS: {averageFPS:F1} FPS (Good performance)");
            optimizationsValidated++;
        }
        else
        {
            optimizationResults.Add($"⚠️ Overall FPS: {averageFPS:F1} FPS (Below expectations)");
        }
        
        // Validate memory usage
        long memoryMB = totalMemoryUsage / (1024 * 1024);
        if (memoryMB <= MAX_MEMORY_MB)
        {
            optimizationResults.Add($"✅ Memory Usage: {memoryMB} MB (Within limits)");
            optimizationsValidated++;
        }
        else
        {
            optimizationResults.Add($"⚠️ Memory Usage: {memoryMB} MB (Above recommended)");
        }
        
        // Validate GC frequency
        if (gcCollectionCount <= MAX_GC_PER_MINUTE)
        {
            optimizationResults.Add($"✅ GC Collections: {gcCollectionCount}/min (Optimized)");
            optimizationsValidated++;
        }
        else
        {
            optimizationResults.Add($"⚠️ GC Collections: {gcCollectionCount}/min (High frequency)");
        }
    }

    /// <summary>
    /// Calculate overall performance score.
    /// </summary>
    private void CalculatePerformanceScore()
    {
        int totalOptimizations = 6; // Expected number of optimizations
        performanceScore = ((float)optimizationsValidated / totalOptimizations) * 100f;
        
        // Bonus points for exceeding targets
        if (averageFPS > TARGET_FPS)
            performanceScore += 10f;
        
        if (totalMemoryUsage < MAX_MEMORY_MB * 0.8f * 1024 * 1024)
            performanceScore += 10f;
        
        performanceScore = Mathf.Clamp(performanceScore, 0f, 120f);
    }

    /// <summary>
    /// Display comprehensive optimization results.
    /// </summary>
    private void DisplayOptimizationResults()
    {
        Debug.Log("📊 PERFORMANCE OPTIMIZATION VALIDATION RESULTS:");
        Debug.Log($"   Optimizations Validated: {optimizationsValidated}");
        Debug.Log($"   Performance Score: {performanceScore:F1}%");
        Debug.Log($"   Average FPS: {averageFPS:F1}");
        Debug.Log($"   Memory Usage: {totalMemoryUsage / (1024 * 1024)} MB");
        Debug.Log($"   GC Collections: {gcCollectionCount}/min");
        
        Debug.Log("\n🔍 DETAILED VALIDATION RESULTS:");
        foreach (string result in optimizationResults)
        {
            Debug.Log($"   {result}");
        }
        
        if (performanceScore >= 90f)
        {
            Debug.Log("\n🎉 EXCELLENT PERFORMANCE! ALL OPTIMIZATIONS WORKING! 🎉");
        }
        else if (performanceScore >= 75f)
        {
            Debug.Log("\n👍 GOOD PERFORMANCE - OPTIMIZATIONS EFFECTIVE");
        }
        else
        {
            Debug.Log("\n⚠️ PERFORMANCE ISSUES DETECTED - REVIEW NEEDED");
        }
    }
    #endregion

    #region UI Display
    /// <summary>
    /// Draw performance monitoring UI.
    /// </summary>
    private void DrawPerformanceUI()
    {
        // Performance metrics display
        GUI.Box(new Rect(10, 10, 300, showDetailedMetrics ? 200 : 120), "");
        
        GUI.Label(new Rect(20, 20, 280, 20), $"FPS: {currentFPS:F1} (Avg: {averageFPS:F1})", performanceStyle);
        GUI.Label(new Rect(20, 40, 280, 20), $"Memory: {totalMemoryUsage / (1024 * 1024)} MB", performanceStyle);
        GUI.Label(new Rect(20, 60, 280, 20), $"GC/min: {gcCollectionCount}", performanceStyle);
        GUI.Label(new Rect(20, 80, 280, 20), $"Performance Score: {performanceScore:F1}%", performanceStyle);
        
        if (GUI.Button(new Rect(20, 100, 100, 20), showDetailedMetrics ? "Hide Details" : "Show Details"))
        {
            showDetailedMetrics = !showDetailedMetrics;
        }
        
        if (showDetailedMetrics)
        {
            GUI.Label(new Rect(20, 130, 280, 20), $"CPU Usage: {cpuUsagePercentage:F1}%", performanceStyle);
            GUI.Label(new Rect(20, 150, 280, 20), $"GC Memory: {gcMemoryUsage / (1024 * 1024)} MB", performanceStyle);
            GUI.Label(new Rect(20, 170, 280, 20), $"Optimizations: {optimizationsValidated}", performanceStyle);
        }
    }
    #endregion

    #region Public API
    /// <summary>
    /// Manually trigger performance validation.
    /// </summary>
    [ContextMenu("Run Performance Validation")]
    public void RunPerformanceValidation()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(ValidateOptimizations());
        }
        else
        {
            Debug.LogWarning("Performance validation can only be run in Play Mode");
        }
    }

    /// <summary>
    /// Get current performance score.
    /// </summary>
    /// <returns>Performance score as percentage</returns>
    public float GetPerformanceScore() => performanceScore;

    /// <summary>
    /// Get current average FPS.
    /// </summary>
    /// <returns>Average FPS over last 60 frames</returns>
    public float GetAverageFPS() => averageFPS;

    /// <summary>
    /// Get current memory usage in MB.
    /// </summary>
    /// <returns>Memory usage in megabytes</returns>
    public long GetMemoryUsageMB() => totalMemoryUsage / (1024 * 1024);
    #endregion
}
