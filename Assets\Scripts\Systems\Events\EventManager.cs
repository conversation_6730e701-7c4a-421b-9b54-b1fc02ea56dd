using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

/// <summary>
/// Core Event Manager for Dynamic World Events in Cinder of Darkness
/// Handles event scheduling, triggering, propagation, and state management
/// </summary>
public class EventManager : MonoBehaviour
{
    [Header("Event Registry")]
    public EventRegistry eventRegistry;

    [Header("Event Settings")]
    public bool enableEvents = true;
    public float eventCheckInterval = 1f; // Check for new events every hour (game time)
    public float eventUpdateInterval = 0.1f; // Update active events every 6 minutes (game time)
    public bool debugMode = false;

    [Header("Integration References")]
    public TimeSystem timeSystem;
    public MoralitySystem moralitySystem;
    public QuestSystem questSystem;
    public WorldStateManager worldStateManager;

    // Static instance
    private static EventManager instance;
    public static EventManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<EventManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("EventManager");
                    instance = go.AddComponent<EventManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Event state management
    private List<ActiveEvent> activeEvents = new List<ActiveEvent>();
    private Dictionary<string, float> eventCooldowns = new Dictionary<string, float>();
    private EventTriggerContext currentContext;
    private float lastEventCheck = 0f;
    private float lastEventUpdate = 0f;

    // Event history and statistics
    private List<CompletedEvent> eventHistory = new List<CompletedEvent>();
    private Dictionary<string, int> eventTriggerCounts = new Dictionary<string, int>();

    /// <summary>
    /// Active event instance with runtime data
    /// </summary>
    [System.Serializable]
    public class ActiveEvent
    {
        public string eventId;
        public WorldEvent eventData;
        public float startTime;
        public float duration;
        public float progress;
        public EventState state;
        public bool playerInteracted;
        public string playerChoice;
        public Dictionary<string, object> eventVariables;
        public List<string> affectedNPCs;
        public List<string> triggeredEffects;

        public ActiveEvent(WorldEvent eventData)
        {
            this.eventId = eventData.eventId;
            this.eventData = eventData;
            this.startTime = Time.time;
            this.duration = eventData.GetRandomDuration();
            this.progress = 0f;
            this.state = EventState.Active;
            this.playerInteracted = false;
            this.eventVariables = new Dictionary<string, object>();
            this.affectedNPCs = new List<string>();
            this.triggeredEffects = new List<string>();
        }

        public float GetRemainingTime()
        {
            return Mathf.Max(0f, duration - (Time.time - startTime));
        }

        public bool IsExpired()
        {
            return Time.time >= startTime + duration;
        }

        public void UpdateProgress()
        {
            progress = Mathf.Clamp01((Time.time - startTime) / duration);
        }
    }

    /// <summary>
    /// Event states for tracking lifecycle
    /// </summary>
    public enum EventState
    {
        Scheduled,
        Active,
        PlayerInteracting,
        Resolving,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// Completed event record for history tracking
    /// </summary>
    [System.Serializable]
    public class CompletedEvent
    {
        public string eventId;
        public string eventName;
        public float startTime;
        public float endTime;
        public EventState finalState;
        public bool playerParticipated;
        public string playerChoice;
        public float moralityImpact;
        public Dictionary<string, float> reputationImpacts;
        public List<string> consequences;
    }

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeEventManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        StartCoroutine(EventManagementLoop());
    }

    void InitializeEventManager()
    {
        // Find required systems if not assigned
        if (timeSystem == null)
            timeSystem = FindObjectOfType<TimeSystem>();

        if (moralitySystem == null)
            moralitySystem = FindObjectOfType<MoralitySystem>();

        if (questSystem == null)
            questSystem = FindObjectOfType<QuestSystem>();

        if (worldStateManager == null)
            worldStateManager = FindObjectOfType<WorldStateManager>();

        Debug.Log("Event Manager initialized");
    }

    /// <summary>
    /// Main event management loop
    /// </summary>
    IEnumerator EventManagementLoop()
    {
        while (enableEvents)
        {
            float currentTime = Time.time;

            // Check for new events
            if (currentTime - lastEventCheck >= eventCheckInterval)
            {
                CheckForNewEvents();
                lastEventCheck = currentTime;
            }

            // Update active events
            if (currentTime - lastEventUpdate >= eventUpdateInterval)
            {
                UpdateActiveEvents();
                lastEventUpdate = currentTime;
            }

            // Update event cooldowns
            UpdateEventCooldowns();

            yield return new WaitForSeconds(0.1f);
        }
    }

    /// <summary>
    /// Check for and potentially trigger new events
    /// </summary>
    void CheckForNewEvents()
    {
        if (eventRegistry == null) return;

        // Update current context
        UpdateEventContext();

        // Check if we can start new events
        if (activeEvents.Count >= eventRegistry.maxConcurrentEvents) return;

        // Get triggerable events
        var activeEventIds = activeEvents.Select(e => e.eventId).ToList();
        var triggerableEvents = eventRegistry.GetTriggerableEvents(currentContext, activeEventIds);

        // Filter out events on cooldown
        triggerableEvents = triggerableEvents.Where(e => !IsEventOnCooldown(e.eventId)).ToList();

        // Filter by concurrent type limits
        triggerableEvents = triggerableEvents.Where(e =>
            eventRegistry.CanStartEventOfType(e.eventType, activeEvents.Select(ae => ae.eventData).ToList())
        ).ToList();

        if (triggerableEvents.Count == 0) return;

        // Try to trigger a random event
        var selectedEvent = eventRegistry.GetRandomEvent(currentContext, activeEventIds);
        if (selectedEvent != null)
        {
            TriggerEvent(selectedEvent);
        }
    }

    /// <summary>
    /// Update current event trigger context
    /// </summary>
    void UpdateEventContext()
    {
        currentContext = new EventTriggerContext();

        // Time-based context
        if (timeSystem != null)
        {
            currentContext.daysPassed = timeSystem.GetDaysPassed();
            currentContext.currentSeason = timeSystem.GetCurrentSeason();
        }

        // Player context
        if (moralitySystem != null)
        {
            currentContext.playerMorality = moralitySystem.GetMoralityValue();
            currentContext.playerAlignment = moralitySystem.GetCurrentAlignment();
        }

        // Quest context
        if (questSystem != null)
        {
            currentContext.completedQuests = questSystem.GetCompletedQuests();
            currentContext.activeQuests = questSystem.GetActiveQuests();
            currentContext.defeatedBosses = questSystem.GetDefeatedBosses();
        }

        // World state context
        if (worldStateManager != null)
        {
            currentContext.worldFlags = worldStateManager.GetActiveFlags();
            currentContext.dialogueChoices = worldStateManager.GetDialogueChoices();
            currentContext.factionStandings = worldStateManager.GetFactionStandings();
        }

        // Active events context
        currentContext.activeEvents = activeEvents.Select(e => e.eventId).ToList();

        // Player level (would come from player stats system)
        var playerStats = FindObjectOfType<PlayerStats>();
        if (playerStats != null)
        {
            currentContext.playerLevel = playerStats.level;
        }
    }

    /// <summary>
    /// Trigger a new event
    /// </summary>
    public void TriggerEvent(WorldEvent eventData)
    {
        if (eventData == null) return;

        // Check if event is already active
        if (activeEvents.Any(e => e.eventId == eventData.eventId))
        {
            if (debugMode) Debug.LogWarning($"Event {eventData.eventId} is already active");
            return;
        }

        // Create active event
        var activeEvent = new ActiveEvent(eventData);
        activeEvents.Add(activeEvent);

        // Set cooldown
        if (eventData.canRecur)
        {
            eventCooldowns[eventData.eventId] = Time.time + eventData.cooldownDays * 24f; // Convert days to hours
        }

        // Track trigger count
        if (!eventTriggerCounts.ContainsKey(eventData.eventId))
        {
            eventTriggerCounts[eventData.eventId] = 0;
        }
        eventTriggerCounts[eventData.eventId]++;

        // Apply event effects
        ApplyEventEffects(activeEvent);

        // Notify other systems
        NotifyEventStarted(activeEvent);

        if (debugMode) Debug.Log($"Triggered event: {eventData.eventName}");
    }

    /// <summary>
    /// Update all active events
    /// </summary>
    void UpdateActiveEvents()
    {
        for (int i = activeEvents.Count - 1; i >= 0; i--)
        {
            var activeEvent = activeEvents[i];
            activeEvent.UpdateProgress();

            // Check if event has expired
            if (activeEvent.IsExpired())
            {
                CompleteEvent(activeEvent, EventState.Completed);
            }
            else
            {
                // Update event effects based on progress
                UpdateEventEffects(activeEvent);
            }
        }
    }

    /// <summary>
    /// Apply initial event effects
    /// </summary>
    void ApplyEventEffects(ActiveEvent activeEvent)
    {
        var eventData = activeEvent.eventData;

        // Apply NPC behavior overrides
        ApplyNPCOverrides(activeEvent);

        // Apply environmental effects
        ApplyEnvironmentalEffects(activeEvent);

        // Apply economic impacts
        ApplyEconomicImpacts(activeEvent);

        // Apply visual changes
        ApplyVisualChanges(activeEvent);
    }

    /// <summary>
    /// Apply NPC behavior overrides
    /// </summary>
    void ApplyNPCOverrides(ActiveEvent activeEvent)
    {
        var npcSystem = FindObjectOfType<NPCSystem>();
        if (npcSystem == null) return;

        foreach (var npcOverride in activeEvent.eventData.npcOverrides)
        {
            if (!string.IsNullOrEmpty(npcOverride.npcId))
            {
                npcSystem.ApplyBehaviorOverride(npcOverride.npcId, npcOverride);
                activeEvent.affectedNPCs.Add(npcOverride.npcId);
            }
            else if (!string.IsNullOrEmpty(npcOverride.npcTag))
            {
                var npcsWithTag = npcSystem.GetNPCsByTag(npcOverride.npcTag);
                foreach (var npc in npcsWithTag)
                {
                    npcSystem.ApplyBehaviorOverride(npc.npcId, npcOverride);
                    activeEvent.affectedNPCs.Add(npc.npcId);
                }
            }
        }
    }

    /// <summary>
    /// Apply environmental effects
    /// </summary>
    void ApplyEnvironmentalEffects(ActiveEvent activeEvent)
    {
        var environmentSystem = FindObjectOfType<EnvironmentSystem>();
        if (environmentSystem == null) return;

        foreach (var envEffect in activeEvent.eventData.environmentalEffects)
        {
            string effectId = environmentSystem.ApplyEnvironmentalEffect(envEffect, activeEvent.eventData.affectedRealms);
            if (!string.IsNullOrEmpty(effectId))
            {
                activeEvent.triggeredEffects.Add(effectId);
            }
        }
    }

    /// <summary>
    /// Apply economic impacts
    /// </summary>
    void ApplyEconomicImpacts(ActiveEvent activeEvent)
    {
        var economySystem = FindObjectOfType<EconomySystem>();
        if (economySystem == null) return;

        var economicImpact = activeEvent.eventData.economicImpact;
        if (economicImpact != null)
        {
            economySystem.ApplyEventImpact(activeEvent.eventId, economicImpact);
        }
    }

    /// <summary>
    /// Apply visual changes
    /// </summary>
    void ApplyVisualChanges(ActiveEvent activeEvent)
    {
        var visualSystem = FindObjectOfType<DynamicVisualSystem>();
        if (visualSystem == null) return;

        var visualChanges = activeEvent.eventData.visualChanges;
        if (visualChanges != null)
        {
            visualSystem.ApplyEventVisuals(activeEvent.eventId, visualChanges);
        }
    }

    /// <summary>
    /// Update ongoing event effects
    /// </summary>
    void UpdateEventEffects(ActiveEvent activeEvent)
    {
        // Update effects based on event progress
        // This could include escalating effects, changing intensities, etc.

        // Check for escalation conditions
        if (activeEvent.eventData.canEscalate && !activeEvent.playerInteracted)
        {
            if (activeEvent.progress >= activeEvent.eventData.escalationThreshold)
            {
                TryEscalateEvent(activeEvent);
            }
        }
    }

    /// <summary>
    /// Try to escalate an event
    /// </summary>
    void TryEscalateEvent(ActiveEvent activeEvent)
    {
        if (activeEvent.eventData.escalatedEvent != null)
        {
            // Complete current event and trigger escalated version
            CompleteEvent(activeEvent, EventState.Failed);
            TriggerEvent(activeEvent.eventData.escalatedEvent);
        }
    }

    /// <summary>
    /// Complete an event
    /// </summary>
    void CompleteEvent(ActiveEvent activeEvent, EventState finalState)
    {
        // Remove event effects
        RemoveEventEffects(activeEvent);

        // Create completion record
        var completedEvent = new CompletedEvent
        {
            eventId = activeEvent.eventId,
            eventName = activeEvent.eventData.eventName,
            startTime = activeEvent.startTime,
            endTime = Time.time,
            finalState = finalState,
            playerParticipated = activeEvent.playerInteracted,
            playerChoice = activeEvent.playerChoice,
            consequences = new List<string>()
        };

        eventHistory.Add(completedEvent);

        // Apply completion consequences
        ApplyEventConsequences(activeEvent, finalState);

        // Check for chained events
        if (eventRegistry.enableChainedEvents)
        {
            var chainedEvents = eventRegistry.GetChainableEvents(activeEvent.eventId, currentContext);
            foreach (var chainedEvent in chainedEvents)
            {
                TriggerEvent(chainedEvent);
            }
        }

        // Remove from active events
        activeEvents.Remove(activeEvent);

        // Notify other systems
        NotifyEventCompleted(activeEvent, finalState);

        if (debugMode) Debug.Log($"Completed event: {activeEvent.eventData.eventName} ({finalState})");
    }

    /// <summary>
    /// Remove event effects when event ends
    /// </summary>
    void RemoveEventEffects(ActiveEvent activeEvent)
    {
        // Remove NPC overrides
        var npcSystem = FindObjectOfType<NPCSystem>();
        if (npcSystem != null)
        {
            foreach (var npcId in activeEvent.affectedNPCs)
            {
                npcSystem.RemoveBehaviorOverride(npcId, activeEvent.eventId);
            }
        }

        // Remove environmental effects
        var environmentSystem = FindObjectOfType<EnvironmentSystem>();
        if (environmentSystem != null)
        {
            foreach (var effectId in activeEvent.triggeredEffects)
            {
                environmentSystem.RemoveEnvironmentalEffect(effectId);
            }
        }

        // Remove economic impacts
        var economySystem = FindObjectOfType<EconomySystem>();
        if (economySystem != null)
        {
            economySystem.RemoveEventImpact(activeEvent.eventId);
        }

        // Remove visual changes
        var visualSystem = FindObjectOfType<DynamicVisualSystem>();
        if (visualSystem != null)
        {
            visualSystem.RemoveEventVisuals(activeEvent.eventId);
        }
    }

    /// <summary>
    /// Apply event consequences based on completion state
    /// </summary>
    void ApplyEventConsequences(ActiveEvent activeEvent, EventState finalState)
    {
        var eventData = activeEvent.eventData;

        if (finalState == EventState.Completed && activeEvent.playerInteracted)
        {
            // Apply completion rewards
            foreach (var reward in eventData.completionRewards)
            {
                ApplyEventReward(reward);
            }
        }
        else if (finalState == EventState.Failed)
        {
            // Apply failure consequences
            foreach (var consequence in eventData.failureConsequences)
            {
                ApplyEventConsequence(consequence);
            }
        }

        // Apply morality shifts
        if (activeEvent.playerInteracted)
        {
            foreach (var moralityShift in eventData.moralityConsequences)
            {
                if (moralitySystem != null)
                {
                    moralitySystem.ShiftMorality(moralityShift.alignment, moralityShift.shiftAmount);
                }
            }
        }

        // Apply reputation changes
        if (activeEvent.playerInteracted)
        {
            foreach (var repChange in eventData.reputationChanges)
            {
                if (worldStateManager != null)
                {
                    worldStateManager.ModifyFactionReputation(repChange.factionId, repChange.reputationDelta);
                }
            }
        }
    }

    /// <summary>
    /// Apply event reward
    /// </summary>
    void ApplyEventReward(WorldEvent.EventReward reward)
    {
        // Implementation would depend on specific reward systems
        Debug.Log($"Applied event reward: {reward.rewardType} - {reward.rewardId}");
    }

    /// <summary>
    /// Apply event consequence
    /// </summary>
    void ApplyEventConsequence(WorldEvent.EventConsequence consequence)
    {
        // Implementation would depend on specific consequence systems
        Debug.Log($"Applied event consequence: {consequence.consequenceType} - {consequence.consequenceId}");
    }

    /// <summary>
    /// Update event cooldowns
    /// </summary>
    void UpdateEventCooldowns()
    {
        var expiredCooldowns = new List<string>();

        foreach (var kvp in eventCooldowns)
        {
            if (Time.time >= kvp.Value)
            {
                expiredCooldowns.Add(kvp.Key);
            }
        }

        foreach (var eventId in expiredCooldowns)
        {
            eventCooldowns.Remove(eventId);
        }
    }

    /// <summary>
    /// Check if event is on cooldown
    /// </summary>
    bool IsEventOnCooldown(string eventId)
    {
        return eventCooldowns.ContainsKey(eventId) && Time.time < eventCooldowns[eventId];
    }

    /// <summary>
    /// Notify other systems that an event started
    /// </summary>
    void NotifyEventStarted(ActiveEvent activeEvent)
    {
        // Send event notifications to other systems
        var eventUI = FindObjectOfType<EventUI>();
        if (eventUI != null)
        {
            eventUI.ShowEventNotification(activeEvent);
        }

        // Notify World Event UI Manager
        WorldEventUIManager.ShowEventStarted(activeEvent);

        // Notify NPC Ambient Dialogue System
        NPCAmbientDialogueSystem.TriggerEventDialogueStatic(activeEvent);

        // Show flash briefing for major events
        if (activeEvent.eventData.severity >= WorldEvent.EventSeverity.Major)
        {
            string briefingTitle = $"event_briefing_{activeEvent.eventData.eventType.ToString().ToLower()}";
            string briefingDesc = $"event_briefing_desc_{activeEvent.eventId}";
            WorldEventUIManager.ShowFlashBriefing(briefingTitle, briefingDesc, activeEvent.eventData.eventType);
        }

        // Notify quest system
        if (questSystem != null)
        {
            questSystem.OnEventStarted(activeEvent.eventId);
        }

        // Notify world state manager
        if (worldStateManager != null)
        {
            worldStateManager.SetWorldFlag($"event_{activeEvent.eventId}_active", true);
        }
    }

    /// <summary>
    /// Notify other systems that an event completed
    /// </summary>
    void NotifyEventCompleted(ActiveEvent activeEvent, EventState finalState)
    {
        // Create completed event record
        var completedEvent = new CompletedEvent
        {
            eventId = activeEvent.eventId,
            eventName = activeEvent.eventData.eventName,
            startTime = activeEvent.startTime,
            endTime = Time.time,
            finalState = finalState,
            playerParticipated = activeEvent.playerInteracted,
            playerChoice = activeEvent.playerChoice
        };

        // Notify World Event UI Manager
        WorldEventUIManager.ShowEventCompleted(completedEvent);

        // Notify quest system
        if (questSystem != null)
        {
            questSystem.OnEventCompleted(activeEvent.eventId, finalState);
        }

        // Notify world state manager
        if (worldStateManager != null)
        {
            worldStateManager.SetWorldFlag($"event_{activeEvent.eventId}_active", false);
            worldStateManager.SetWorldFlag($"event_{activeEvent.eventId}_completed", true);
        }
    }

    // Public API
    public static List<ActiveEvent> GetActiveEvents()
    {
        return Instance.activeEvents.ToList();
    }

    public static ActiveEvent GetActiveEvent(string eventId)
    {
        return Instance.activeEvents.FirstOrDefault(e => e.eventId == eventId);
    }

    public static void PlayerInteractWithEvent(string eventId, string choiceId)
    {
        Instance.PlayerInteractWithEventInternal(eventId, choiceId);
    }

    public static bool IsEventActive(string eventId)
    {
        return Instance.activeEvents.Any(e => e.eventId == eventId);
    }

    public static List<CompletedEvent> GetEventHistory()
    {
        return Instance.eventHistory.ToList();
    }

    void PlayerInteractWithEventInternal(string eventId, string choiceId)
    {
        var activeEvent = activeEvents.FirstOrDefault(e => e.eventId == eventId);
        if (activeEvent == null) return;

        activeEvent.playerInteracted = true;
        activeEvent.playerChoice = choiceId;

        // Find the interaction option
        var interactionOption = activeEvent.eventData.interventionOptions.FirstOrDefault(o => o.optionId == choiceId);
        if (interactionOption != null)
        {
            // Apply interaction outcome
            ApplyInteractionOutcome(activeEvent, interactionOption);
        }
    }

    void ApplyInteractionOutcome(ActiveEvent activeEvent, WorldEvent.PlayerInteractionOption interaction)
    {
        var outcome = interaction.outcome;

        if (outcome.resolveEvent)
        {
            CompleteEvent(activeEvent, EventState.Completed);
        }
        else if (outcome.escalateEvent)
        {
            TryEscalateEvent(activeEvent);
        }
        else if (outcome.triggerNewEvent && !string.IsNullOrEmpty(outcome.newEventId))
        {
            var newEvent = eventRegistry.GetEvent(outcome.newEventId);
            if (newEvent != null)
            {
                TriggerEvent(newEvent);
            }
        }

        // Modify event duration
        if (outcome.eventDurationModifier != 1f)
        {
            activeEvent.duration *= outcome.eventDurationModifier;
        }

        // Set world flags
        if (worldStateManager != null)
        {
            foreach (var flag in outcome.worldFlagsToSet)
            {
                worldStateManager.SetWorldFlag(flag, true);
            }

            foreach (var flag in outcome.worldFlagsToRemove)
            {
                worldStateManager.SetWorldFlag(flag, false);
            }
        }
    }

    // Save/Load support
    [System.Serializable]
    public class EventManagerSaveData
    {
        public List<ActiveEvent> activeEvents;
        public Dictionary<string, float> eventCooldowns;
        public List<CompletedEvent> eventHistory;
        public Dictionary<string, int> eventTriggerCounts;
    }

    public EventManagerSaveData GetSaveData()
    {
        return new EventManagerSaveData
        {
            activeEvents = activeEvents,
            eventCooldowns = eventCooldowns,
            eventHistory = eventHistory,
            eventTriggerCounts = eventTriggerCounts
        };
    }

    public void LoadSaveData(EventManagerSaveData saveData)
    {
        if (saveData == null) return;

        activeEvents = saveData.activeEvents ?? new List<ActiveEvent>();
        eventCooldowns = saveData.eventCooldowns ?? new Dictionary<string, float>();
        eventHistory = saveData.eventHistory ?? new List<CompletedEvent>();
        eventTriggerCounts = saveData.eventTriggerCounts ?? new Dictionary<string, int>();

        // Reapply effects for loaded active events
        foreach (var activeEvent in activeEvents)
        {
            ApplyEventEffects(activeEvent);
        }
    }
}
