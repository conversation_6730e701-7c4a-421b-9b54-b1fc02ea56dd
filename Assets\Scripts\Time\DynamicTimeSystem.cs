using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace CinderOfDarkness.Time
{
    /// <summary>
    /// Dynamic Time System for Cinder of Darkness.
    /// Manages day-night cycle, time-based events, and temporal mechanics.
    /// </summary>
    public class DynamicTimeSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Time Settings")]
        [SerializeField] private bool enableTimeSystem = true;
        [SerializeField] private float dayDurationMinutes = 20f;
        [SerializeField] private float timeScale = 1f;
        [SerializeField] private bool pauseTimeInMenus = true;

        [Header("Day-Night Cycle")]
        [SerializeField] private Light sunLight;
        [SerializeField] private Light moonLight;
        [SerializeField] private Gradient sunColor;
        [SerializeField] private Gradient moonColor;
        [SerializeField] private AnimationCurve sunIntensityCurve;
        [SerializeField] private AnimationCurve moonIntensityCurve;

        [Header("Sky and Atmosphere")]
        [SerializeField] private Material skyboxMaterial;
        [SerializeField] private Gradient skyTint;
        [SerializeField] private Gradient fogColor;
        [SerializeField] private AnimationCurve fogDensityCurve;

        [Header("Time Events")]
        [SerializeField] private TimeEvent[] timeEvents;
        [SerializeField] private int maxActiveEvents = 20;

        [Header("Sleep System")]
        [SerializeField] private bool enableSleepSystem = true;
        [SerializeField] private float sleepDuration = 8f; // Hours
        [SerializeField] private Transform[] sleepLocations;

        [Header("UI References")]
        [SerializeField] private TMPro.TextMeshProUGUI timeDisplay;
        [SerializeField] private GameObject sleepUI;
        [SerializeField] private UnityEngine.UI.Slider timeSkipSlider;

        [Header("Audio")]
        [SerializeField] private AudioClip[] dayAmbientSounds;
        [SerializeField] private AudioClip[] nightAmbientSounds;
        [SerializeField] private AudioSource ambientAudioSource;
        #endregion

        #region Public Properties
        public static DynamicTimeSystem Instance { get; private set; }
        public GameTime CurrentTime { get; private set; }
        public TimeOfDay CurrentTimeOfDay { get; private set; }
        public bool IsNight => CurrentTimeOfDay == TimeOfDay.Night;
        public bool IsDay => CurrentTimeOfDay == TimeOfDay.Day;
        public float DayProgress => CurrentTime.GetDayProgress();
        #endregion

        #region Private Fields
        private float realTimeElapsed;
        private List<ActiveTimeEvent> activeEvents = new List<ActiveTimeEvent>();
        private Dictionary<string, bool> triggeredEvents = new Dictionary<string, bool>();

        // Lighting system
        private float baseSunIntensity;
        private float baseMoonIntensity;
        private Color baseSkyTint;
        private Color baseFogColor;

        // Sleep system
        private bool isSleeping = false;
        private Coroutine sleepCoroutine;
        private Transform nearestSleepLocation;

        // Audio system
        private AudioClip currentAmbientClip;
        private Coroutine ambientTransitionCoroutine;

        // Performance optimization
        private float lastEventCheck;
        private float lastLightingUpdate;
        private const float eventCheckInterval = 1f;
        private const float lightingUpdateInterval = 0.1f;
        #endregion

        #region Events
        public System.Action<GameTime> OnTimeChanged;
        public System.Action<TimeOfDay> OnTimeOfDayChanged;
        public System.Action<TimeEvent> OnTimeEventTriggered;
        public System.Action OnSleepStarted;
        public System.Action OnSleepEnded;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeTimeSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupLightingSystem();
            SetupAudioSystem();
            LoadTimeData();
            UpdateTimeDisplay();
        }

        private void Update()
        {
            if (!enableTimeSystem || isSleeping) return;

            UpdateTime();
            UpdateLighting();
            UpdateTimeEvents();
            UpdateAmbientAudio();
            UpdateUI();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the time system.
        /// </summary>
        private void InitializeTimeSystem()
        {
            // Initialize with dawn time
            CurrentTime = new GameTime(6, 0, 0); // 6:00 AM
            CurrentTimeOfDay = GetTimeOfDay(CurrentTime);

            // Setup audio source
            if (ambientAudioSource == null)
            {
                ambientAudioSource = gameObject.AddComponent<AudioSource>();
                ambientAudioSource.loop = true;
                ambientAudioSource.playOnAwake = false;
            }

            Debug.Log("Dynamic Time System initialized");
        }

        /// <summary>
        /// Setup lighting system references.
        /// </summary>
        private void SetupLightingSystem()
        {
            if (sunLight != null)
            {
                baseSunIntensity = sunLight.intensity;
            }

            if (moonLight != null)
            {
                baseMoonIntensity = moonLight.intensity;
            }

            if (skyboxMaterial != null)
            {
                baseSkyTint = skyboxMaterial.GetColor("_Tint");
            }

            baseFogColor = RenderSettings.fogColor;
        }

        /// <summary>
        /// Setup audio system.
        /// </summary>
        private void SetupAudioSystem()
        {
            if (ambientAudioSource != null)
            {
                ambientAudioSource.volume = 0.3f;
                UpdateAmbientSound();
            }
        }
        #endregion

        #region Time Management
        /// <summary>
        /// Update game time based on real time.
        /// </summary>
        private void UpdateTime()
        {
            if (pauseTimeInMenus && IsGamePaused()) return;

            float deltaTime = UnityEngine.Time.deltaTime * timeScale;
            realTimeElapsed += deltaTime;

            // Convert real time to game time
            float gameSecondsPerRealSecond = (24f * 60f * 60f) / (dayDurationMinutes * 60f);
            float gameTimeIncrement = deltaTime * gameSecondsPerRealSecond;

            CurrentTime.AddSeconds(gameTimeIncrement);

            // Check for time of day changes
            TimeOfDay newTimeOfDay = GetTimeOfDay(CurrentTime);
            if (newTimeOfDay != CurrentTimeOfDay)
            {
                CurrentTimeOfDay = newTimeOfDay;
                OnTimeOfDayChanged?.Invoke(CurrentTimeOfDay);
            }

            OnTimeChanged?.Invoke(CurrentTime);
        }

        /// <summary>
        /// Get time of day based on current time.
        /// </summary>
        /// <param name="time">Game time</param>
        /// <returns>Time of day</returns>
        private TimeOfDay GetTimeOfDay(GameTime time)
        {
            int hour = time.Hour;

            if (hour >= 6 && hour < 12) return TimeOfDay.Morning;
            if (hour >= 12 && hour < 18) return TimeOfDay.Afternoon;
            if (hour >= 18 && hour < 22) return TimeOfDay.Evening;
            return TimeOfDay.Night;
        }

        /// <summary>
        /// Set game time manually.
        /// </summary>
        /// <param name="hour">Hour (0-23)</param>
        /// <param name="minute">Minute (0-59)</param>
        /// <param name="second">Second (0-59)</param>
        public void SetTime(int hour, int minute, int second = 0)
        {
            CurrentTime = new GameTime(hour, minute, second);
            CurrentTimeOfDay = GetTimeOfDay(CurrentTime);

            UpdateLighting();
            UpdateAmbientSound();
            OnTimeChanged?.Invoke(CurrentTime);
            OnTimeOfDayChanged?.Invoke(CurrentTimeOfDay);
        }

        /// <summary>
        /// Skip time by specified hours.
        /// </summary>
        /// <param name="hours">Hours to skip</param>
        public void SkipTime(float hours)
        {
            CurrentTime.AddHours(hours);
            CurrentTimeOfDay = GetTimeOfDay(CurrentTime);

            UpdateLighting();
            UpdateAmbientSound();
            OnTimeChanged?.Invoke(CurrentTime);
            OnTimeOfDayChanged?.Invoke(CurrentTimeOfDay);
        }

        /// <summary>
        /// Check if game is paused.
        /// </summary>
        /// <returns>True if game is paused</returns>
        private bool IsGamePaused()
        {
            return UnityEngine.Time.timeScale == 0f;
        }
        #endregion

        #region Lighting System
        /// <summary>
        /// Update lighting based on time of day.
        /// </summary>
        private void UpdateLighting()
        {
            if (UnityEngine.Time.time - lastLightingUpdate < lightingUpdateInterval) return;

            float dayProgress = CurrentTime.GetDayProgress();

            UpdateSunLighting(dayProgress);
            UpdateMoonLighting(dayProgress);
            UpdateSkybox(dayProgress);
            UpdateFog(dayProgress);

            lastLightingUpdate = UnityEngine.Time.time;
        }

        /// <summary>
        /// Update sun lighting.
        /// </summary>
        /// <param name="dayProgress">Day progress (0-1)</param>
        private void UpdateSunLighting(float dayProgress)
        {
            if (sunLight == null) return;

            // Calculate sun angle (sunrise at 0.25, sunset at 0.75)
            float sunAngle = Mathf.Lerp(-90f, 270f, dayProgress);
            sunLight.transform.rotation = Quaternion.Euler(sunAngle, 30f, 0f);

            // Update sun intensity
            float sunIntensity = sunIntensityCurve.Evaluate(dayProgress) * baseSunIntensity;
            sunLight.intensity = sunIntensity;

            // Update sun color
            sunLight.color = sunColor.Evaluate(dayProgress);

            // Enable/disable sun based on intensity
            sunLight.enabled = sunIntensity > 0.01f;
        }

        /// <summary>
        /// Update moon lighting.
        /// </summary>
        /// <param name="dayProgress">Day progress (0-1)</param>
        private void UpdateMoonLighting(float dayProgress)
        {
            if (moonLight == null) return;

            // Moon is opposite to sun
            float moonAngle = Mathf.Lerp(90f, 450f, dayProgress);
            moonLight.transform.rotation = Quaternion.Euler(moonAngle, 210f, 0f);

            // Update moon intensity
            float moonIntensity = moonIntensityCurve.Evaluate(dayProgress) * baseMoonIntensity;
            moonLight.intensity = moonIntensity;

            // Update moon color
            moonLight.color = moonColor.Evaluate(dayProgress);

            // Enable/disable moon based on intensity
            moonLight.enabled = moonIntensity > 0.01f;
        }

        /// <summary>
        /// Update skybox material.
        /// </summary>
        /// <param name="dayProgress">Day progress (0-1)</param>
        private void UpdateSkybox(float dayProgress)
        {
            if (skyboxMaterial == null) return;

            Color skyColor = skyTint.Evaluate(dayProgress);
            skyboxMaterial.SetColor("_Tint", skyColor);
        }

        /// <summary>
        /// Update fog settings.
        /// </summary>
        /// <param name="dayProgress">Day progress (0-1)</param>
        private void UpdateFog(float dayProgress)
        {
            RenderSettings.fogColor = fogColor.Evaluate(dayProgress);
            RenderSettings.fogDensity = fogDensityCurve.Evaluate(dayProgress) * 0.01f;
        }
        #endregion

        #region Time Events
        /// <summary>
        /// Update and process time-based events.
        /// </summary>
        private void UpdateTimeEvents()
        {
            if (UnityEngine.Time.time - lastEventCheck < eventCheckInterval) return;

            CheckTimeEvents();
            UpdateActiveEvents();

            lastEventCheck = UnityEngine.Time.time;
        }

        /// <summary>
        /// Check for time events that should trigger.
        /// </summary>
        private void CheckTimeEvents()
        {
            foreach (var timeEvent in timeEvents)
            {
                if (ShouldTriggerEvent(timeEvent))
                {
                    TriggerTimeEvent(timeEvent);
                }
            }
        }

        /// <summary>
        /// Check if a time event should trigger.
        /// </summary>
        /// <param name="timeEvent">Time event to check</param>
        /// <returns>True if event should trigger</returns>
        private bool ShouldTriggerEvent(TimeEvent timeEvent)
        {
            // Check if already triggered (for one-time events)
            if (timeEvent.isOneTime && triggeredEvents.ContainsKey(timeEvent.eventId))
            {
                return false;
            }

            // Check time conditions
            switch (timeEvent.triggerType)
            {
                case TimeTriggerType.SpecificTime:
                    return CheckSpecificTimeTrigger(timeEvent);
                case TimeTriggerType.TimeOfDay:
                    return CheckTimeOfDayTrigger(timeEvent);
                case TimeTriggerType.DayOfWeek:
                    return CheckDayOfWeekTrigger(timeEvent);
                case TimeTriggerType.Interval:
                    return CheckIntervalTrigger(timeEvent);
                default:
                    return false;
            }
        }

        /// <summary>
        /// Check specific time trigger.
        /// </summary>
        private bool CheckSpecificTimeTrigger(TimeEvent timeEvent)
        {
            return CurrentTime.Hour == timeEvent.triggerHour &&
                   CurrentTime.Minute == timeEvent.triggerMinute;
        }

        /// <summary>
        /// Check time of day trigger.
        /// </summary>
        private bool CheckTimeOfDayTrigger(TimeEvent timeEvent)
        {
            return CurrentTimeOfDay == timeEvent.triggerTimeOfDay;
        }

        /// <summary>
        /// Check day of week trigger.
        /// </summary>
        private bool CheckDayOfWeekTrigger(TimeEvent timeEvent)
        {
            // Would need to implement day of week tracking
            return false; // Placeholder
        }

        /// <summary>
        /// Check interval trigger.
        /// </summary>
        private bool CheckIntervalTrigger(TimeEvent timeEvent)
        {
            // Would check if enough time has passed since last trigger
            return false; // Placeholder
        }

        /// <summary>
        /// Trigger a time event.
        /// </summary>
        /// <param name="timeEvent">Time event to trigger</param>
        private void TriggerTimeEvent(TimeEvent timeEvent)
        {
            if (activeEvents.Count >= maxActiveEvents) return;

            var activeEvent = new ActiveTimeEvent
            {
                timeEvent = timeEvent,
                startTime = UnityEngine.Time.time,
                isActive = true
            };

            activeEvents.Add(activeEvent);
            ExecuteTimeEvent(timeEvent);

            if (timeEvent.isOneTime)
            {
                triggeredEvents[timeEvent.eventId] = true;
            }

            OnTimeEventTriggered?.Invoke(timeEvent);
            Debug.Log($"Time event triggered: {timeEvent.eventId}");
        }

        /// <summary>
        /// Execute time event effects.
        /// </summary>
        /// <param name="timeEvent">Time event to execute</param>
        private void ExecuteTimeEvent(TimeEvent timeEvent)
        {
            switch (timeEvent.eventType)
            {
                case TimeEventType.SpawnEnemies:
                    SpawnEventEnemies(timeEvent);
                    break;
                case TimeEventType.NPCSchedule:
                    UpdateNPCSchedule(timeEvent);
                    break;
                case TimeEventType.ShopAvailability:
                    UpdateShopAvailability(timeEvent);
                    break;
                case TimeEventType.QuestAvailability:
                    UpdateQuestAvailability(timeEvent);
                    break;
                case TimeEventType.WeatherChange:
                    ChangeWeather(timeEvent);
                    break;
            }
        }

        /// <summary>
        /// Update active time events.
        /// </summary>
        private void UpdateActiveEvents()
        {
            for (int i = activeEvents.Count - 1; i >= 0; i--)
            {
                var activeEvent = activeEvents[i];

                if (UnityEngine.Time.time - activeEvent.startTime > activeEvent.timeEvent.duration)
                {
                    activeEvents.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Spawn enemies for time event.
        /// </summary>
        private void SpawnEventEnemies(TimeEvent timeEvent)
        {
            // Would integrate with enemy spawning system
            Debug.Log($"Spawning enemies for event: {timeEvent.eventId}");
        }

        /// <summary>
        /// Update NPC schedule.
        /// </summary>
        private void UpdateNPCSchedule(TimeEvent timeEvent)
        {
            // Would integrate with NPC system
            Debug.Log($"Updating NPC schedule for event: {timeEvent.eventId}");
        }

        /// <summary>
        /// Update shop availability.
        /// </summary>
        private void UpdateShopAvailability(TimeEvent timeEvent)
        {
            // Would integrate with shop system
            Debug.Log($"Updating shop availability for event: {timeEvent.eventId}");
        }

        /// <summary>
        /// Update quest availability.
        /// </summary>
        private void UpdateQuestAvailability(TimeEvent timeEvent)
        {
            // Would integrate with quest system
            Debug.Log($"Updating quest availability for event: {timeEvent.eventId}");
        }

        /// <summary>
        /// Change weather.
        /// </summary>
        private void ChangeWeather(TimeEvent timeEvent)
        {
            // Would integrate with weather system
            Debug.Log($"Changing weather for event: {timeEvent.eventId}");
        }
        #endregion

        #region Sleep System
        /// <summary>
        /// Start sleep sequence.
        /// </summary>
        public void StartSleep()
        {
            if (isSleeping || !enableSleepSystem) return;

            nearestSleepLocation = FindNearestSleepLocation();
            if (nearestSleepLocation == null)
            {
                Debug.LogWarning("No sleep location found nearby");
                return;
            }

            sleepCoroutine = StartCoroutine(SleepSequence());
        }

        /// <summary>
        /// Sleep sequence coroutine.
        /// </summary>
        /// <returns>Sleep coroutine</returns>
        private IEnumerator SleepSequence()
        {
            isSleeping = true;
            OnSleepStarted?.Invoke();

            // Show sleep UI
            if (sleepUI != null)
            {
                sleepUI.SetActive(true);
            }

            // Disable player controls
            var playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
            {
                playerController.enabled = false;
            }

            // Skip time
            SkipTime(sleepDuration);

            // Sleep animation/fade
            yield return new WaitForSeconds(2f);

            // Hide sleep UI
            if (sleepUI != null)
            {
                sleepUI.SetActive(false);
            }

            // Re-enable player controls
            if (playerController != null)
            {
                playerController.enabled = true;
            }

            isSleeping = false;
            OnSleepEnded?.Invoke();
        }

        /// <summary>
        /// Find nearest sleep location.
        /// </summary>
        /// <returns>Nearest sleep location transform</returns>
        private Transform FindNearestSleepLocation()
        {
            if (sleepLocations == null || sleepLocations.Length == 0) return null;

            Transform player = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (player == null) return null;

            Transform nearest = null;
            float nearestDistance = float.MaxValue;

            foreach (var location in sleepLocations)
            {
                if (location == null) continue;

                float distance = Vector3.Distance(player.position, location.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearest = location;
                }
            }

            return nearestDistance <= 5f ? nearest : null; // Within 5 units
        }

        /// <summary>
        /// Check if player can sleep at current location.
        /// </summary>
        /// <returns>True if player can sleep</returns>
        public bool CanSleep()
        {
            return enableSleepSystem && !isSleeping && FindNearestSleepLocation() != null;
        }
        #endregion

        #region Audio System
        /// <summary>
        /// Update ambient audio based on time of day.
        /// </summary>
        private void UpdateAmbientAudio()
        {
            AudioClip targetClip = GetAmbientClipForTime();

            if (targetClip != currentAmbientClip)
            {
                if (ambientTransitionCoroutine != null)
                {
                    StopCoroutine(ambientTransitionCoroutine);
                }
                ambientTransitionCoroutine = StartCoroutine(TransitionAmbientAudio(targetClip));
            }
        }

        /// <summary>
        /// Get appropriate ambient clip for current time.
        /// </summary>
        /// <returns>Ambient audio clip</returns>
        private AudioClip GetAmbientClipForTime()
        {
            if (IsNight && nightAmbientSounds != null && nightAmbientSounds.Length > 0)
            {
                return nightAmbientSounds[Random.Range(0, nightAmbientSounds.Length)];
            }
            else if (IsDay && dayAmbientSounds != null && dayAmbientSounds.Length > 0)
            {
                return dayAmbientSounds[Random.Range(0, dayAmbientSounds.Length)];
            }

            return null;
        }

        /// <summary>
        /// Transition ambient audio smoothly.
        /// </summary>
        /// <param name="newClip">New ambient clip</param>
        /// <returns>Transition coroutine</returns>
        private IEnumerator TransitionAmbientAudio(AudioClip newClip)
        {
            float transitionDuration = 3f;
            float originalVolume = ambientAudioSource.volume;

            // Fade out current audio
            float elapsedTime = 0f;
            while (elapsedTime < transitionDuration * 0.5f)
            {
                elapsedTime += UnityEngine.Time.deltaTime;
                ambientAudioSource.volume = Mathf.Lerp(originalVolume, 0f, elapsedTime / (transitionDuration * 0.5f));
                yield return null;
            }

            // Change clip
            currentAmbientClip = newClip;
            if (newClip != null)
            {
                ambientAudioSource.clip = newClip;
                ambientAudioSource.Play();
            }
            else
            {
                ambientAudioSource.Stop();
            }

            // Fade in new audio
            elapsedTime = 0f;
            while (elapsedTime < transitionDuration * 0.5f)
            {
                elapsedTime += UnityEngine.Time.deltaTime;
                ambientAudioSource.volume = Mathf.Lerp(0f, originalVolume, elapsedTime / (transitionDuration * 0.5f));
                yield return null;
            }

            ambientAudioSource.volume = originalVolume;
        }

        /// <summary>
        /// Update ambient sound immediately.
        /// </summary>
        private void UpdateAmbientSound()
        {
            AudioClip targetClip = GetAmbientClipForTime();

            if (targetClip != null)
            {
                currentAmbientClip = targetClip;
                ambientAudioSource.clip = targetClip;
                if (!ambientAudioSource.isPlaying)
                {
                    ambientAudioSource.Play();
                }
            }
            else
            {
                ambientAudioSource.Stop();
                currentAmbientClip = null;
            }
        }
        #endregion

        #region UI System
        /// <summary>
        /// Update UI elements.
        /// </summary>
        private void UpdateUI()
        {
            UpdateTimeDisplay();
            UpdateTimeSkipSlider();
        }

        /// <summary>
        /// Update time display.
        /// </summary>
        private void UpdateTimeDisplay()
        {
            if (timeDisplay != null)
            {
                string timeString = CurrentTime.ToString();
                string timeOfDayString = CurrentTimeOfDay.ToString();
                timeDisplay.text = $"{timeString}\n{timeOfDayString}";
            }
        }

        /// <summary>
        /// Update time skip slider.
        /// </summary>
        private void UpdateTimeSkipSlider()
        {
            if (timeSkipSlider != null)
            {
                timeSkipSlider.value = DayProgress;
            }
        }

        /// <summary>
        /// Handle time skip slider change.
        /// </summary>
        /// <param name="value">Slider value (0-1)</param>
        public void OnTimeSkipSliderChanged(float value)
        {
            float targetHour = value * 24f;
            SetTime(Mathf.FloorToInt(targetHour), Mathf.FloorToInt((targetHour % 1f) * 60f));
        }
        #endregion

        #region Save/Load System
        /// <summary>
        /// Save time data.
        /// </summary>
        private void SaveTimeData()
        {
            var saveData = new TimeSaveData
            {
                currentHour = CurrentTime.Hour,
                currentMinute = CurrentTime.Minute,
                currentSecond = CurrentTime.Second,
                timeScale = timeScale,
                triggeredEvents = triggeredEvents
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("DynamicTimeData", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load time data.
        /// </summary>
        private void LoadTimeData()
        {
            string json = PlayerPrefs.GetString("DynamicTimeData", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<TimeSaveData>(json);

                    CurrentTime = new GameTime(saveData.currentHour, saveData.currentMinute, saveData.currentSecond);
                    timeScale = saveData.timeScale;

                    if (saveData.triggeredEvents != null)
                    {
                        triggeredEvents = saveData.triggeredEvents;
                    }

                    CurrentTimeOfDay = GetTimeOfDay(CurrentTime);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load time data: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set time scale.
        /// </summary>
        /// <param name="scale">Time scale multiplier</param>
        public void SetTimeScale(float scale)
        {
            timeScale = Mathf.Max(0f, scale);
        }

        /// <summary>
        /// Pause time system.
        /// </summary>
        public void PauseTime()
        {
            SetTimeScale(0f);
        }

        /// <summary>
        /// Resume time system.
        /// </summary>
        public void ResumeTime()
        {
            SetTimeScale(1f);
        }

        /// <summary>
        /// Get time until specific hour.
        /// </summary>
        /// <param name="targetHour">Target hour</param>
        /// <returns>Hours until target</returns>
        public float GetTimeUntilHour(int targetHour)
        {
            int currentHour = CurrentTime.Hour;

            if (targetHour > currentHour)
            {
                return targetHour - currentHour;
            }
            else
            {
                return (24 - currentHour) + targetHour;
            }
        }

        /// <summary>
        /// Check if it's within time range.
        /// </summary>
        /// <param name="startHour">Start hour</param>
        /// <param name="endHour">End hour</param>
        /// <returns>True if within range</returns>
        public bool IsWithinTimeRange(int startHour, int endHour)
        {
            int currentHour = CurrentTime.Hour;

            if (startHour <= endHour)
            {
                return currentHour >= startHour && currentHour < endHour;
            }
            else
            {
                return currentHour >= startHour || currentHour < endHour;
            }
        }

        /// <summary>
        /// Add time event at runtime.
        /// </summary>
        /// <param name="timeEvent">Time event to add</param>
        public void AddTimeEvent(TimeEvent timeEvent)
        {
            var eventList = timeEvents.ToList();
            eventList.Add(timeEvent);
            timeEvents = eventList.ToArray();
        }

        /// <summary>
        /// Remove time event.
        /// </summary>
        /// <param name="eventId">Event ID to remove</param>
        public void RemoveTimeEvent(string eventId)
        {
            var eventList = timeEvents.ToList();
            eventList.RemoveAll(e => e.eventId == eventId);
            timeEvents = eventList.ToArray();
        }

        /// <summary>
        /// Reset time system.
        /// </summary>
        public void ResetTimeSystem()
        {
            CurrentTime = new GameTime(6, 0, 0);
            CurrentTimeOfDay = GetTimeOfDay(CurrentTime);
            timeScale = 1f;
            triggeredEvents.Clear();
            activeEvents.Clear();

            UpdateLighting();
            UpdateAmbientSound();
            SaveTimeData();

            Debug.Log("Time system reset");
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class TimeSaveData
    {
        public int currentHour;
        public int currentMinute;
        public int currentSecond;
        public float timeScale;
        public Dictionary<string, bool> triggeredEvents;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class GameTime
    {
        public int Hour { get; private set; }
        public int Minute { get; private set; }
        public int Second { get; private set; }

        public GameTime(int hour, int minute, int second)
        {
            Hour = hour;
            Minute = minute;
            Second = second;
            NormalizeTime();
        }

        public void AddSeconds(float seconds)
        {
            Second += Mathf.FloorToInt(seconds);
            NormalizeTime();
        }

        public void AddHours(float hours)
        {
            Hour += Mathf.FloorToInt(hours);
            float remainingMinutes = (hours - Mathf.FloorToInt(hours)) * 60f;
            Minute += Mathf.FloorToInt(remainingMinutes);
            NormalizeTime();
        }

        public float GetDayProgress()
        {
            return (Hour * 3600f + Minute * 60f + Second) / (24f * 3600f);
        }

        private void NormalizeTime()
        {
            while (Second >= 60)
            {
                Second -= 60;
                Minute++;
            }
            while (Minute >= 60)
            {
                Minute -= 60;
                Hour++;
            }
            while (Hour >= 24)
            {
                Hour -= 24;
            }
        }

        public override string ToString()
        {
            return $"{Hour:D2}:{Minute:D2}:{Second:D2}";
        }
    }

    [System.Serializable]
    public class TimeEvent
    {
        public string eventId;
        public TimeTriggerType triggerType;
        public TimeEventType eventType;
        public int triggerHour;
        public int triggerMinute;
        public TimeOfDay triggerTimeOfDay;
        public float duration = 60f;
        public bool isOneTime = false;
        public string[] affectedObjects;
    }

    [System.Serializable]
    public class ActiveTimeEvent
    {
        public TimeEvent timeEvent;
        public float startTime;
        public bool isActive;
    }

    public enum TimeOfDay
    {
        Morning,
        Afternoon,
        Evening,
        Night
    }

    public enum TimeTriggerType
    {
        SpecificTime,
        TimeOfDay,
        DayOfWeek,
        Interval
    }

    public enum TimeEventType
    {
        SpawnEnemies,
        NPCSchedule,
        ShopAvailability,
        QuestAvailability,
        WeatherChange
    }
    #endregion
}
