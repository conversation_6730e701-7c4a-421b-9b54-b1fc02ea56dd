using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace CinderOfDarkness.World
{
    /// <summary>
    /// Expanded Kingdom System for Cinder of Darkness.
    /// Adds detailed kingdom management, NPC routines, and architectural systems.
    /// </summary>
    public class ExpandedKingdomSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Kingdom Management")]
        [SerializeField] private Kingdom[] kingdoms;
        [SerializeField] private Village[] villages;
        [SerializeField] private string currentLocation;
        [SerializeField] private float reputationUpdateInterval = 30f;

        [Header("Architecture System")]
        [SerializeField] private ArchitecturalPrefab[] architecturalPrefabs;
        [SerializeField] private Material[] kingdomMaterials;
        [SerializeField] private GameObject[] decorativeElements;

        [Header("NPC Routine System")]
        [SerializeField] private NPCRoutineManager routineManager;
        [SerializeField] private float routineUpdateInterval = 60f;
        [SerializeField] private bool enableDynamicRoutines = true;

        [Header("Biome Integration")]
        [SerializeField] private BiomeData[] biomes;
        [SerializeField] private WeatherSystem weatherSystem;
        [SerializeField] private LightingController lightingController;
        #endregion

        #region Private Fields
        private Dictionary<string, Kingdom> kingdomLookup = new Dictionary<string, Kingdom>();
        private Dictionary<string, Village> villageLookup = new Dictionary<string, Village>();
        private List<NPCRoutine> activeRoutines = new List<NPCRoutine>();
        private float lastReputationUpdate;
        private float lastRoutineUpdate;
        #endregion

        #region Public Properties
        public static ExpandedKingdomSystem Instance { get; private set; }
        public Kingdom CurrentKingdom { get; private set; }
        public Village CurrentVillage { get; private set; }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeKingdomSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupKingdoms();
            SetupVillages();
            InitializeNPCRoutines();
        }

        private void Update()
        {
            UpdateReputationSystems();
            UpdateNPCRoutines();
            UpdateArchitecturalEffects();
        }
        #endregion

        #region Initialization
        private void InitializeKingdomSystem()
        {
            // Initialize kingdom lookup
            kingdomLookup.Clear();
            villageLookup.Clear();

            Debug.Log("Expanded Kingdom System initialized");
        }

        private void SetupKingdoms()
        {
            kingdoms = new Kingdom[]
            {
                CreateKingdomOfEternalFlame(),
                CreateKingdomOfWhisperingStones(),
                CreateKingdomOfCrystalTears(),
                CreateKingdomOfIronWill(),
                CreateKingdomOfSilentWaters()
            };

            foreach (var kingdom in kingdoms)
            {
                kingdomLookup[kingdom.kingdomName] = kingdom;
            }

            Debug.Log($"Initialized {kingdoms.Length} kingdoms");
        }

        private Kingdom CreateKingdomOfEternalFlame()
        {
            return new Kingdom
            {
                kingdomName = "Kingdom of Eternal Flame",
                culturalName = "مملكة اللهب الأبدي", // Arabic name
                kingdomType = Kingdom.KingdomType.FireWorshippers,
                architecturalStyle = Kingdom.ArchitecturalStyle.BurningBrass,

                // Cultural Identity
                corePhilosophy = "Fire is the source of all life and purification",
                socialStructure = Kingdom.SocialStructure.PyramidHierarchy,
                leadershipType = Kingdom.LeadershipType.FlameKeeper,

                // Relationship with Cinderborn
                cinderbornPerception = Kingdom.CinderbornPerception.DivineBeing,
                defaultReputation = 75f,

                // Cultural Rules
                culturalTaboos = new string[]
                {
                    "Never extinguish a flame intentionally",
                    "Water magic is forbidden in sacred areas",
                    "Ash must be treated with reverence"
                },

                culturalVirtues = new string[]
                {
                    "Maintaining eternal flames",
                    "Showing courage in battle",
                    "Protecting the weak from cold"
                },

                // Architecture
                primaryMaterial = "Burning Brass and Obsidian",
                illuminationType = Kingdom.IlluminationType.EternalFlames,
                architecturalFeatures = new string[]
                {
                    "Towering flame spires",
                    "Brass-lined fire channels",
                    "Obsidian flame altars",
                    "Heat-conducting floor systems"
                },

                // NPCs and Routines
                npcTypes = new Kingdom.NPCType[]
                {
                    Kingdom.NPCType.FlameKeeper,
                    Kingdom.NPCType.BrassSmith,
                    Kingdom.NPCType.FireDancer,
                    Kingdom.NPCType.AshCollector,
                    Kingdom.NPCType.HeatEngineer
                },

                // Biome Integration
                biome = BiomeData.BiomeType.VolcanicPlains,
                weatherPattern = WeatherSystem.WeatherType.HotAndDry,
                ambientTemperature = 35f,

                // Unique Features
                specialBuildings = new string[]
                {
                    "The Great Forge of Souls",
                    "Temple of Eternal Combustion",
                    "Academy of Flame Arts",
                    "The Brass Observatory"
                },

                uniqueTraditions = new string[]
                {
                    "Daily flame blessing ceremonies",
                    "Annual Festival of Burning Memories",
                    "Flame-walking initiation rites",
                    "Sacred ash painting rituals"
                }
            };
        }

        private Kingdom CreateKingdomOfWhisperingStones()
        {
            return new Kingdom
            {
                kingdomName = "Kingdom of Whispering Stones",
                culturalName = "مملكة الحجارة الهامسة",
                kingdomType = Kingdom.KingdomType.EarthBound,
                architecturalStyle = Kingdom.ArchitecturalStyle.LivingStone,

                corePhilosophy = "The earth remembers all secrets and speaks to those who listen",
                socialStructure = Kingdom.SocialStructure.CircularCouncil,
                leadershipType = Kingdom.LeadershipType.StoneListener,

                cinderbornPerception = Kingdom.CinderbornPerception.EarthDisruptor,
                defaultReputation = 25f,

                culturalTaboos = new string[]
                {
                    "Never break stone without permission",
                    "Fire magic disturbs the earth's peace",
                    "Loud noises anger the stone spirits"
                },

                culturalVirtues = new string[]
                {
                    "Listening to stone whispers",
                    "Maintaining ancient structures",
                    "Preserving geological harmony"
                },

                primaryMaterial = "Living Stone and Crystal Veins",
                illuminationType = Kingdom.IlluminationType.CrystalResonance,
                architecturalFeatures = new string[]
                {
                    "Self-repairing stone walls",
                    "Crystal communication networks",
                    "Underground tunnel systems",
                    "Resonating stone chambers"
                },

                npcTypes = new Kingdom.NPCType[]
                {
                    Kingdom.NPCType.StoneListener,
                    Kingdom.NPCType.CrystalTuner,
                    Kingdom.NPCType.EarthShaper,
                    Kingdom.NPCType.TunnelKeeper,
                    Kingdom.NPCType.GeologicalScribe
                },

                biome = BiomeData.BiomeType.CrystalCaverns,
                weatherPattern = WeatherSystem.WeatherType.StableAndCool,
                ambientTemperature = 18f,

                specialBuildings = new string[]
                {
                    "The Great Listening Chamber",
                    "Crystal Harmony Spires",
                    "Underground Archive of Echoes",
                    "The Stone Speaker's Throne"
                },

                uniqueTraditions = new string[]
                {
                    "Silent meditation with stones",
                    "Crystal tuning ceremonies",
                    "Underground pilgrimage routes",
                    "Stone memory preservation rituals"
                }
            };
        }

        private Kingdom CreateKingdomOfCrystalTears()
        {
            return new Kingdom
            {
                kingdomName = "Kingdom of Crystal Tears",
                culturalName = "مملكة الدموع البلورية",
                kingdomType = Kingdom.KingdomType.SorrowKeepers,
                architecturalStyle = Kingdom.ArchitecturalStyle.CrystalizedGrief,

                corePhilosophy = "Sorrow crystallizes into beauty, and tears become eternal gems",
                socialStructure = Kingdom.SocialStructure.EmotionalHierarchy,
                leadershipType = Kingdom.LeadershipType.TearKeeper,

                cinderbornPerception = Kingdom.CinderbornPerception.KindredSpirit,
                defaultReputation = 60f,

                culturalTaboos = new string[]
                {
                    "Never waste tears or ignore grief",
                    "Joy must be balanced with remembrance",
                    "Breaking crystal tears is sacrilege"
                },

                culturalVirtues = new string[]
                {
                    "Honoring lost memories",
                    "Transforming pain into beauty",
                    "Preserving emotional truth"
                },

                primaryMaterial = "Crystallized Tears and Moonstone",
                illuminationType = Kingdom.IlluminationType.TearLight,
                architecturalFeatures = new string[]
                {
                    "Tear-crystal chandeliers",
                    "Grief gardens with crystal flowers",
                    "Memory pools of liquid crystal",
                    "Sorrow spires reaching skyward"
                },

                npcTypes = new Kingdom.NPCType[]
                {
                    Kingdom.NPCType.TearKeeper,
                    Kingdom.NPCType.GriefCounselor,
                    Kingdom.NPCType.CrystalGardener,
                    Kingdom.NPCType.MemoryPreserver,
                    Kingdom.NPCType.EmotionalHealer
                },

                biome = BiomeData.BiomeType.MournfulMeadows,
                weatherPattern = WeatherSystem.WeatherType.GentleRain,
                ambientTemperature = 22f,

                specialBuildings = new string[]
                {
                    "The Cathedral of Crystallized Sorrow",
                    "Gardens of Eternal Remembrance",
                    "The Tear Collection Sanctuary",
                    "Palace of Bittersweet Memories"
                },

                uniqueTraditions = new string[]
                {
                    "Annual Day of Tears ceremony",
                    "Crystal tear crafting rituals",
                    "Memory sharing circles",
                    "Grief transformation workshops"
                }
            };
        }

        private void SetupVillages()
        {
            villages = new Village[]
            {
                CreateVillageOfForgottenNames(),
                CreateVillageOfEndlessHarvest(),
                CreateVillageOfSilentBells(),
                CreateVillageOfWanderingLights(),
                CreateVillageOfBrokenMirrors()
            };

            foreach (var village in villages)
            {
                villageLookup[village.villageName] = village;
            }

            Debug.Log($"Initialized {villages.Length} villages");
        }

        private Village CreateVillageOfForgottenNames()
        {
            return new Village
            {
                villageName = "Village of Forgotten Names",
                culturalName = "قرية الأسماء المنسية",
                villageType = Village.VillageType.MemoryKeepers,

                uniqueBelief = "Names hold power, and forgotten names become dangerous",
                corePhilosophy = "Every name tells a story, every story deserves remembrance",

                // Relationship with Cinderborn
                cinderbornPerception = Village.CinderbornPerception.NamelessWanderer,
                strangerPolicy = Village.StrangerPolicy.NameFirst,
                suspicionLevel = 0.7f,

                // Cultural Practices
                culturalTaboos = new string[]
                {
                    "Never speak a name without permission",
                    "Forgetting someone's name is deeply offensive",
                    "Anonymous actions are considered cowardly"
                },

                culturalVirtues = new string[]
                {
                    "Remembering the names of the dead",
                    "Honoring family lineages",
                    "Preserving oral histories"
                },

                // Architecture and Environment
                architecturalStyle = Village.ArchitecturalStyle.NameStones,
                primaryMaterial = "Carved memory stones with inscribed names",

                specialBuildings = new string[]
                {
                    "The Hall of Remembered Names",
                    "Memorial Garden of the Nameless",
                    "The Name Keeper's Library",
                    "Shrine of Lost Identities"
                },

                // NPCs and Routines
                npcTypes = new Village.NPCType[]
                {
                    Village.NPCType.NameKeeper,
                    Village.NPCType.MemoryScribe,
                    Village.NPCType.GenealogyExpert,
                    Village.NPCType.StoryTeller,
                    Village.NPCType.IdentityGuardian
                },

                // Daily Routines
                dailyRoutines = new Village.DailyRoutine[]
                {
                    new Village.DailyRoutine
                    {
                        routineName = "Morning Name Blessing",
                        startTime = 6f,
                        duration = 1f,
                        participants = new string[] { "NameKeeper", "Villagers" },
                        description = "Daily recitation of community member names"
                    },
                    new Village.DailyRoutine
                    {
                        routineName = "Memory Circle",
                        startTime = 19f,
                        duration = 2f,
                        participants = new string[] { "StoryTeller", "Elders", "Children" },
                        description = "Evening sharing of family stories and names"
                    }
                },

                // Unique Features
                uniqueTraditions = new string[]
                {
                    "Name Day celebrations for each villager",
                    "Annual Festival of Remembered Dead",
                    "Name-carving ceremonies for newborns",
                    "Pilgrimage to forgotten graveyards"
                },

                // Economic and Social
                primaryTrade = "Memory preservation services and genealogy research",
                socialStructure = Village.SocialStructure.ElderCouncil,
                populationSize = 150,

                // Environmental
                biome = BiomeData.BiomeType.QuietHills,
                weatherPattern = WeatherSystem.WeatherType.MistyMornings,
                ambientTemperature = 20f
            };
        }
        #endregion

        #region Kingdom Management
        public void EnterKingdom(string kingdomName)
        {
            if (kingdomLookup.ContainsKey(kingdomName))
            {
                CurrentKingdom = kingdomLookup[kingdomName];
                CurrentVillage = null;
                currentLocation = kingdomName;

                ApplyKingdomEffects(CurrentKingdom);
                StartKingdomRoutines(CurrentKingdom);

                Debug.Log($"Entered {kingdomName}");
            }
        }

        public void EnterVillage(string villageName)
        {
            if (villageLookup.ContainsKey(villageName))
            {
                CurrentVillage = villageLookup[villageName];
                CurrentKingdom = null;
                currentLocation = villageName;

                ApplyVillageEffects(CurrentVillage);
                StartVillageRoutines(CurrentVillage);

                Debug.Log($"Entered {villageName}");
            }
        }

        private void ApplyKingdomEffects(Kingdom kingdom)
        {
            // Apply architectural changes
            ApplyArchitecturalStyle(kingdom.architecturalStyle);

            // Apply lighting and weather
            if (lightingController != null)
                lightingController.SetKingdomLighting(kingdom.illuminationType);

            if (weatherSystem != null)
                weatherSystem.SetWeatherPattern(kingdom.weatherPattern);

            // Apply ambient temperature
            SetAmbientTemperature(kingdom.ambientTemperature);
        }

        private void ApplyVillageEffects(Village village)
        {
            // Apply village-specific effects
            ApplyArchitecturalStyle(village.architecturalStyle);

            if (weatherSystem != null)
                weatherSystem.SetWeatherPattern(village.weatherPattern);

            SetAmbientTemperature(village.ambientTemperature);
        }
        #endregion

        #region NPC Routine System
        private void InitializeNPCRoutines()
        {
            if (routineManager == null)
            {
                var routineObj = new GameObject("NPCRoutineManager");
                routineManager = routineObj.AddComponent<NPCRoutineManager>();
                routineObj.transform.SetParent(transform);
            }

            routineManager.Initialize(this);
        }

        private void UpdateNPCRoutines()
        {
            if (Time.time - lastRoutineUpdate < routineUpdateInterval) return;

            lastRoutineUpdate = Time.time;

            if (enableDynamicRoutines && routineManager != null)
            {
                routineManager.UpdateRoutines();
            }
        }

        private void StartKingdomRoutines(Kingdom kingdom)
        {
            if (routineManager != null)
            {
                routineManager.StartKingdomRoutines(kingdom);
            }
        }

        private void StartVillageRoutines(Village village)
        {
            if (routineManager != null)
            {
                routineManager.StartVillageRoutines(village);
            }
        }
        #endregion

        #region Architectural System
        private void ApplyArchitecturalStyle(Kingdom.ArchitecturalStyle style)
        {
            // Apply architectural changes based on style
            foreach (var prefab in architecturalPrefabs)
            {
                if (prefab.style == style)
                {
                    prefab.ApplyToScene();
                }
            }
        }

        private void ApplyArchitecturalStyle(Village.ArchitecturalStyle style)
        {
            // Apply village architectural changes
            foreach (var prefab in architecturalPrefabs)
            {
                if (prefab.villageStyle == style)
                {
                    prefab.ApplyToScene();
                }
            }
        }

        private void UpdateArchitecturalEffects()
        {
            // Update dynamic architectural elements
            if (CurrentKingdom != null)
            {
                UpdateKingdomArchitecture(CurrentKingdom);
            }
            else if (CurrentVillage != null)
            {
                UpdateVillageArchitecture(CurrentVillage);
            }
        }

        private void UpdateKingdomArchitecture(Kingdom kingdom)
        {
            // Update kingdom-specific architectural effects
            switch (kingdom.illuminationType)
            {
                case Kingdom.IlluminationType.EternalFlames:
                    UpdateFlameEffects();
                    break;
                case Kingdom.IlluminationType.CrystalResonance:
                    UpdateCrystalEffects();
                    break;
                case Kingdom.IlluminationType.TearLight:
                    UpdateTearLightEffects();
                    break;
            }
        }

        private void UpdateVillageArchitecture(Village village)
        {
            // Update village-specific effects
            // Implementation depends on village type
        }
        #endregion

        #region Reputation System
        private void UpdateReputationSystems()
        {
            if (Time.time - lastReputationUpdate < reputationUpdateInterval) return;

            lastReputationUpdate = Time.time;

            // Update reputation based on player actions
            UpdateLocationReputation();
        }

        private void UpdateLocationReputation()
        {
            if (CurrentKingdom != null)
            {
                UpdateKingdomReputation(CurrentKingdom);
            }
            else if (CurrentVillage != null)
            {
                UpdateVillageReputation(CurrentVillage);
            }
        }

        private void UpdateKingdomReputation(Kingdom kingdom)
        {
            // Update reputation based on player actions and kingdom values
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null)
            {
                // Reputation changes based on moral alignment and actions
                float reputationChange = CalculateKingdomReputationChange(kingdom, playerStats);
                ModifyKingdomReputation(kingdom.kingdomName, reputationChange);
            }
        }

        private void UpdateVillageReputation(Village village)
        {
            // Update village reputation
            var playerStats = FindObjectOfType<PlayerStats>();
            if (playerStats != null)
            {
                float reputationChange = CalculateVillageReputationChange(village, playerStats);
                ModifyVillageReputation(village.villageName, reputationChange);
            }
        }
        #endregion

        #region Utility Methods
        private void SetAmbientTemperature(float temperature)
        {
            // Set ambient temperature effects
            // This could affect player stamina, NPC behavior, etc.
        }

        private void UpdateFlameEffects()
        {
            // Update eternal flame visual effects
        }

        private void UpdateCrystalEffects()
        {
            // Update crystal resonance effects
        }

        private void UpdateTearLightEffects()
        {
            // Update tear light illumination effects
        }

        private float CalculateKingdomReputationChange(Kingdom kingdom, PlayerStats playerStats)
        {
            // Calculate reputation change based on kingdom values and player actions
            return 0f; // Placeholder
        }

        private float CalculateVillageReputationChange(Village village, PlayerStats playerStats)
        {
            // Calculate village reputation change
            return 0f; // Placeholder
        }

        private void ModifyKingdomReputation(string kingdomName, float change)
        {
            // Modify kingdom reputation
        }

        private void ModifyVillageReputation(string villageName, float change)
        {
            // Modify village reputation
        }
        #endregion

        #region Public API
        public Kingdom GetKingdomByName(string name)
        {
            return kingdomLookup.ContainsKey(name) ? kingdomLookup[name] : null;
        }

        public Village GetVillageByName(string name)
        {
            return villageLookup.ContainsKey(name) ? villageLookup[name] : null;
        }

        public string GetCurrentLocationName()
        {
            return currentLocation;
        }

        public bool IsInKingdom()
        {
            return CurrentKingdom != null;
        }

        public bool IsInVillage()
        {
            return CurrentVillage != null;
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class Kingdom
    {
        [Header("Basic Information")]
        public string kingdomName;
        public string culturalName; // Name in native language
        public KingdomType kingdomType;
        public ArchitecturalStyle architecturalStyle;

        [Header("Cultural Identity")]
        public string corePhilosophy;
        public SocialStructure socialStructure;
        public LeadershipType leadershipType;

        [Header("Cinderborn Relations")]
        public CinderbornPerception cinderbornPerception;
        public float defaultReputation;

        [Header("Cultural Rules")]
        public string[] culturalTaboos;
        public string[] culturalVirtues;

        [Header("Architecture")]
        public string primaryMaterial;
        public IlluminationType illuminationType;
        public string[] architecturalFeatures;

        [Header("Population")]
        public NPCType[] npcTypes;
        public int estimatedPopulation;

        [Header("Environment")]
        public BiomeData.BiomeType biome;
        public WeatherSystem.WeatherType weatherPattern;
        public float ambientTemperature;

        [Header("Unique Features")]
        public string[] specialBuildings;
        public string[] uniqueTraditions;

        public enum KingdomType
        {
            FireWorshippers,
            EarthBound,
            SorrowKeepers,
            IronForged,
            WaterBound,
            SkyReachers,
            ShadowDwellers
        }

        public enum ArchitecturalStyle
        {
            BurningBrass,
            LivingStone,
            CrystalizedGrief,
            ForgedIron,
            FlowingWater,
            FloatingCrystal,
            ShadowWeave
        }

        public enum SocialStructure
        {
            PyramidHierarchy,
            CircularCouncil,
            EmotionalHierarchy,
            MeritocracyForge,
            FlowingDemocracy,
            CloudCouncil,
            ShadowNetwork
        }

        public enum LeadershipType
        {
            FlameKeeper,
            StoneListener,
            TearKeeper,
            IronMaster,
            WaveRider,
            SkyWatcher,
            ShadowWeaver
        }

        public enum CinderbornPerception
        {
            DivineBeing,
            EarthDisruptor,
            KindredSpirit,
            ForgeWorthy,
            TidalForce,
            SkyBringer,
            ShadowKin
        }

        public enum IlluminationType
        {
            EternalFlames,
            CrystalResonance,
            TearLight,
            ForgeGlow,
            BioluminescentWater,
            StarCrystals,
            LivingShadows
        }

        public enum NPCType
        {
            FlameKeeper,
            BrassSmith,
            FireDancer,
            AshCollector,
            HeatEngineer,
            StoneListener,
            CrystalTuner,
            EarthShaper,
            TunnelKeeper,
            GeologicalScribe,
            TearKeeper,
            GriefCounselor,
            CrystalGardener,
            MemoryPreserver,
            EmotionalHealer
        }
    }

    [System.Serializable]
    public class Village
    {
        [Header("Basic Information")]
        public string villageName;
        public string culturalName;
        public VillageType villageType;

        [Header("Beliefs and Philosophy")]
        public string uniqueBelief;
        public string corePhilosophy;

        [Header("Cinderborn Relations")]
        public CinderbornPerception cinderbornPerception;
        public StrangerPolicy strangerPolicy;
        public float suspicionLevel;

        [Header("Cultural Practices")]
        public string[] culturalTaboos;
        public string[] culturalVirtues;

        [Header("Architecture")]
        public ArchitecturalStyle architecturalStyle;
        public string primaryMaterial;
        public string[] specialBuildings;

        [Header("Population")]
        public NPCType[] npcTypes;
        public int populationSize;

        [Header("Daily Life")]
        public DailyRoutine[] dailyRoutines;
        public string[] uniqueTraditions;

        [Header("Economy and Society")]
        public string primaryTrade;
        public SocialStructure socialStructure;

        [Header("Environment")]
        public BiomeData.BiomeType biome;
        public WeatherSystem.WeatherType weatherPattern;
        public float ambientTemperature;

        public enum VillageType
        {
            MemoryKeepers,
            HarvestBound,
            SilentWatchers,
            LightChasers,
            ReflectionBreakers,
            TimeKeepers,
            DreamWeavers
        }

        public enum ArchitecturalStyle
        {
            NameStones,
            GrowingWood,
            SilentBells,
            LightCatchers,
            ShatteredGlass,
            TimeSpirals,
            DreamCatchers
        }

        public enum CinderbornPerception
        {
            NamelessWanderer,
            SeasonBringer,
            SilentThreat,
            LightBearer,
            TruthRevealer,
            TimeDisruptor,
            DreamWalker
        }

        public enum StrangerPolicy
        {
            NameFirst,
            SeasonalWelcome,
            SilentObservation,
            LightTest,
            MirrorTrial,
            TimeQuestion,
            DreamShare
        }

        public enum SocialStructure
        {
            ElderCouncil,
            SeasonalRotation,
            SilentHierarchy,
            LightCircle,
            ShardDemocracy,
            TimeKeepers,
            DreamCouncil
        }

        public enum NPCType
        {
            NameKeeper,
            MemoryScribe,
            GenealogyExpert,
            StoryTeller,
            IdentityGuardian,
            HarvestMaster,
            SeasonKeeper,
            GrowthTender,
            SilentWatcher,
            BellKeeper,
            LightChaser,
            ReflectionBreaker
        }

        [System.Serializable]
        public class DailyRoutine
        {
            public string routineName;
            public float startTime; // Hour of day (0-24)
            public float duration; // Duration in hours
            public string[] participants;
            public string description;
            public bool isWeatherDependent;
            public bool isSeasonDependent;
        }
    }

    [System.Serializable]
    public class ArchitecturalPrefab
    {
        public string prefabName;
        public GameObject prefab;
        public Kingdom.ArchitecturalStyle style;
        public Village.ArchitecturalStyle villageStyle;
        public Vector3 defaultScale = Vector3.one;
        public bool isDynamic;

        public void ApplyToScene()
        {
            // Apply this architectural prefab to the current scene
            if (prefab != null)
            {
                // Implementation for applying architectural changes
                Debug.Log($"Applied architectural style: {prefabName}");
            }
        }
    }

    [System.Serializable]
    public class BiomeData
    {
        public BiomeType biomeType;
        public string biomeName;
        public Color ambientColor;
        public float fogDensity;
        public Material terrainMaterial;
        public GameObject[] environmentPrefabs;

        public enum BiomeType
        {
            VolcanicPlains,
            CrystalCaverns,
            MournfulMeadows,
            IronMountains,
            FlowingRivers,
            FloatingIslands,
            ShadowValleys,
            QuietHills,
            EndlessFields,
            SilentForests,
            LightMeadows,
            BrokenLands,
            TimelessDeserts,
            DreamScapes
        }
    }

    [System.Serializable]
    public class NPCRoutine
    {
        public string npcId;
        public string routineName;
        public Vector3 startPosition;
        public Vector3 endPosition;
        public float startTime;
        public float duration;
        public NPCAction[] actions;
        public bool isActive;

        [System.Serializable]
        public class NPCAction
        {
            public string actionName;
            public float actionTime;
            public Vector3 actionPosition;
            public string actionDescription;
        }
    }
    #endregion
}
