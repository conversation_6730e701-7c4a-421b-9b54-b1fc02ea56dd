using UnityEngine;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Noise Generator for creating audio stimuli that AI can detect.
    /// Handles footsteps, combat sounds, and environmental noise.
    /// </summary>
    public class NoiseGenerator : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Noise Settings")]
        [SerializeField] private float baseNoiseLevel = 0.5f;
        [SerializeField] private float maxNoiseRange = 20f;
        [SerializeField] private bool generateFootstepNoise = true;
        [SerializeField] private float footstepNoiseLevel = 0.3f;
        [SerializeField] private float footstepInterval = 0.5f;
        
        [Header("Movement Noise")]
        [SerializeField] private float walkingNoiseMultiplier = 1f;
        [SerializeField] private float runningNoiseMultiplier = 2f;
        [SerializeField] private float crouchingNoiseMultiplier = 0.3f;
        [SerializeField] private float jumpingNoiseLevel = 0.8f;
        [SerializeField] private float landingNoiseLevel = 1f;
        
        [Header("Combat Noise")]
        [SerializeField] private float attackNoiseLevel = 0.7f;
        [SerializeField] private float magicNoiseLevel = 0.9f;
        [SerializeField] private float weaponClashNoiseLevel = 0.8f;
        
        [Header("Environmental Noise")]
        [SerializeField] private float doorNoiseLevel = 0.4f;
        [SerializeField] private float objectBreakNoiseLevel = 1f;
        [SerializeField] private float objectThrowNoiseLevel = 0.6f;
        #endregion

        #region Private Fields
        private float lastFootstepTime;
        private Vector3 lastPosition;
        private bool wasMoving;
        private PlayerController playerController;
        private PlayerCombat playerCombat;
        private Rigidbody playerRigidbody;
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Initialize noise generator.
        /// </summary>
        private void Start()
        {
            playerController = GetComponent<PlayerController>();
            playerCombat = GetComponent<PlayerCombat>();
            playerRigidbody = GetComponent<Rigidbody>();
            lastPosition = transform.position;
            
            // Subscribe to combat events if available
            if (playerCombat != null)
            {
                playerCombat.OnAttackHit += OnAttackPerformed;
            }
        }

        /// <summary>
        /// Update noise generation.
        /// </summary>
        private void Update()
        {
            if (generateFootstepNoise)
            {
                HandleMovementNoise();
            }
        }

        /// <summary>
        /// Clean up event subscriptions.
        /// </summary>
        private void OnDestroy()
        {
            if (playerCombat != null)
            {
                playerCombat.OnAttackHit -= OnAttackPerformed;
            }
        }
        #endregion

        #region Movement Noise
        /// <summary>
        /// Handle noise generation from player movement.
        /// </summary>
        private void HandleMovementNoise()
        {
            Vector3 currentPosition = transform.position;
            float distanceMoved = Vector3.Distance(currentPosition, lastPosition);
            bool isMoving = distanceMoved > 0.01f;

            if (isMoving)
            {
                // Generate footstep noise at intervals
                if (Time.time - lastFootstepTime >= footstepInterval)
                {
                    GenerateFootstepNoise();
                    lastFootstepTime = Time.time;
                }
            }

            // Detect landing from jump/fall
            if (wasMoving && !isMoving && playerRigidbody != null)
            {
                float verticalVelocity = Mathf.Abs(playerRigidbody.velocity.y);
                if (verticalVelocity > 2f) // Landed from significant height
                {
                    GenerateLandingNoise(verticalVelocity);
                }
            }

            lastPosition = currentPosition;
            wasMoving = isMoving;
        }

        /// <summary>
        /// Generate footstep noise based on movement type.
        /// </summary>
        private void GenerateFootstepNoise()
        {
            float noiseLevel = footstepNoiseLevel * baseNoiseLevel;
            
            // Modify noise based on movement type
            if (playerController != null)
            {
                // Check movement speed to determine noise level
                float speed = playerRigidbody != null ? playerRigidbody.velocity.magnitude : 1f;
                
                if (speed > 6f) // Running
                {
                    noiseLevel *= runningNoiseMultiplier;
                }
                else if (speed < 2f) // Crouching/slow movement
                {
                    noiseLevel *= crouchingNoiseMultiplier;
                }
                else // Walking
                {
                    noiseLevel *= walkingNoiseMultiplier;
                }
            }

            GenerateNoise(transform.position, noiseLevel);
        }

        /// <summary>
        /// Generate landing noise from jumps or falls.
        /// </summary>
        /// <param name="impactVelocity">Velocity of impact</param>
        private void GenerateLandingNoise(float impactVelocity)
        {
            float noiseLevel = landingNoiseLevel * baseNoiseLevel;
            noiseLevel *= Mathf.Clamp(impactVelocity / 10f, 0.5f, 2f); // Scale with impact
            
            GenerateNoise(transform.position, noiseLevel);
        }
        #endregion

        #region Combat Noise
        /// <summary>
        /// Handle attack noise generation.
        /// </summary>
        /// <param name="hitPosition">Position where attack hit</param>
        /// <param name="damage">Damage dealt</param>
        /// <param name="target">Target that was hit</param>
        private void OnAttackPerformed(Vector3 hitPosition, float damage, GameObject target)
        {
            float noiseLevel = attackNoiseLevel * baseNoiseLevel;
            
            // Increase noise for more powerful attacks
            noiseLevel *= Mathf.Clamp(damage / 25f, 0.5f, 2f);
            
            GenerateNoise(hitPosition, noiseLevel);
        }

        /// <summary>
        /// Generate noise from magic casting.
        /// </summary>
        /// <param name="castPosition">Position where magic was cast</param>
        /// <param name="magicType">Type of magic cast</param>
        public void OnMagicCast(Vector3 castPosition, string magicType = "")
        {
            float noiseLevel = magicNoiseLevel * baseNoiseLevel;
            
            // Different magic types could have different noise levels
            switch (magicType.ToLower())
            {
                case "fire":
                    noiseLevel *= 1.2f;
                    break;
                case "lightning":
                    noiseLevel *= 1.5f;
                    break;
                case "healing":
                    noiseLevel *= 0.5f;
                    break;
            }
            
            GenerateNoise(castPosition, noiseLevel);
        }

        /// <summary>
        /// Generate noise from weapon clashing.
        /// </summary>
        /// <param name="clashPosition">Position where weapons clashed</param>
        public void OnWeaponClash(Vector3 clashPosition)
        {
            float noiseLevel = weaponClashNoiseLevel * baseNoiseLevel;
            GenerateNoise(clashPosition, noiseLevel);
        }
        #endregion

        #region Environmental Noise
        /// <summary>
        /// Generate noise from opening/closing doors.
        /// </summary>
        /// <param name="doorPosition">Position of the door</param>
        /// <param name="isOpening">True if opening, false if closing</param>
        public void OnDoorInteraction(Vector3 doorPosition, bool isOpening)
        {
            float noiseLevel = doorNoiseLevel * baseNoiseLevel;
            if (!isOpening) noiseLevel *= 0.7f; // Closing is slightly quieter
            
            GenerateNoise(doorPosition, noiseLevel);
        }

        /// <summary>
        /// Generate noise from breaking objects.
        /// </summary>
        /// <param name="breakPosition">Position where object broke</param>
        /// <param name="objectSize">Size/mass of broken object (0-1)</param>
        public void OnObjectBreak(Vector3 breakPosition, float objectSize = 1f)
        {
            float noiseLevel = objectBreakNoiseLevel * baseNoiseLevel * objectSize;
            GenerateNoise(breakPosition, noiseLevel);
        }

        /// <summary>
        /// Generate noise from throwing objects.
        /// </summary>
        /// <param name="throwPosition">Position where object was thrown</param>
        /// <param name="throwForce">Force of the throw</param>
        public void OnObjectThrow(Vector3 throwPosition, float throwForce)
        {
            float noiseLevel = objectThrowNoiseLevel * baseNoiseLevel;
            noiseLevel *= Mathf.Clamp(throwForce / 10f, 0.3f, 1.5f);
            
            GenerateNoise(throwPosition, noiseLevel);
        }

        /// <summary>
        /// Generate noise from object impact/landing.
        /// </summary>
        /// <param name="impactPosition">Position where object impacted</param>
        /// <param name="impactForce">Force of impact</param>
        public void OnObjectImpact(Vector3 impactPosition, float impactForce)
        {
            float noiseLevel = (objectThrowNoiseLevel * 0.8f) * baseNoiseLevel;
            noiseLevel *= Mathf.Clamp(impactForce / 15f, 0.2f, 1.2f);
            
            GenerateNoise(impactPosition, noiseLevel);
        }
        #endregion

        #region Core Noise Generation
        /// <summary>
        /// Generate noise at specified position with given intensity.
        /// </summary>
        /// <param name="position">Position where noise occurs</param>
        /// <param name="intensity">Intensity of noise (0-1)</param>
        public void GenerateNoise(Vector3 position, float intensity)
        {
            // Clamp intensity
            intensity = Mathf.Clamp01(intensity);
            
            // Broadcast noise to all AI sensors
            AISensorSystem.BroadcastNoise(position, intensity, maxNoiseRange);
            
            // Debug visualization
            if (Application.isEditor)
            {
                Debug.DrawWireSphere(position, intensity * 5f, Color.red, 1f);
            }
        }

        /// <summary>
        /// Generate custom noise with specific parameters.
        /// </summary>
        /// <param name="position">Position where noise occurs</param>
        /// <param name="intensity">Intensity of noise (0-1)</param>
        /// <param name="range">Maximum range for noise propagation</param>
        public void GenerateCustomNoise(Vector3 position, float intensity, float range)
        {
            intensity = Mathf.Clamp01(intensity);
            AISensorSystem.BroadcastNoise(position, intensity, range);
        }
        #endregion

        #region Public API
        /// <summary>
        /// Set base noise level multiplier.
        /// </summary>
        /// <param name="multiplier">Noise level multiplier</param>
        public void SetNoiseMultiplier(float multiplier)
        {
            baseNoiseLevel = Mathf.Clamp(multiplier, 0f, 2f);
        }

        /// <summary>
        /// Enable or disable footstep noise generation.
        /// </summary>
        /// <param name="enabled">True to enable footstep noise</param>
        public void SetFootstepNoiseEnabled(bool enabled)
        {
            generateFootstepNoise = enabled;
        }

        /// <summary>
        /// Set footstep noise parameters.
        /// </summary>
        /// <param name="noiseLevel">Base footstep noise level</param>
        /// <param name="interval">Interval between footsteps</param>
        public void SetFootstepParameters(float noiseLevel, float interval)
        {
            footstepNoiseLevel = Mathf.Clamp01(noiseLevel);
            footstepInterval = Mathf.Max(0.1f, interval);
        }

        /// <summary>
        /// Trigger jump noise manually.
        /// </summary>
        public void TriggerJumpNoise()
        {
            float noiseLevel = jumpingNoiseLevel * baseNoiseLevel;
            GenerateNoise(transform.position, noiseLevel);
        }
        #endregion
    }
}
