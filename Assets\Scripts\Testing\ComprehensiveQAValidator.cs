using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CinderOfDarkness.Narrative;
using CinderOfDarkness.AI;
using CinderOfDarkness.Magic;
using CinderOfDarkness.Stealth;

namespace CinderOfDarkness.Testing
{
    /// <summary>
    /// Comprehensive QA Validator for Cinder of Darkness.
    /// Validates all 10 core systems for functionality, integration, and performance.
    /// </summary>
    public class ComprehensiveQAValidator : MonoBehaviour
    {
        #region Serialized Fields
        [Header("QA Settings")]
        [SerializeField] private bool runOnStart = false;
        [SerializeField] private bool verboseLogging = true;
        [SerializeField] private bool performanceTest = true;
        [SerializeField] private bool integrationTest = true;
        #endregion

        #region Private Fields
        private List<string> validationResults = new List<string>();
        private List<string> criticalIssues = new List<string>();
        private List<string> warnings = new List<string>();
        private List<string> performanceIssues = new List<string>();
        
        // System references
        private DynamicNarrativeSystem narrativeSystem;
        private ReactiveAISystem reactiveAI;
        private MagicEvolutionSystem magicSystem;
        private EconomySystem economySystem;
        private AdvancedDialogueSystem dialogueSystem;
        private StealthSystem stealthSystem;
        private WorldMapSystem mapSystem;
        private DynamicTimeSystem timeSystem;
        private FlashbackSystem flashbackSystem;
        private ModdingSystem moddingSystem;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (runOnStart)
            {
                StartCoroutine(RunComprehensiveQA());
            }
        }
        #endregion

        #region QA Validation
        /// <summary>
        /// Run comprehensive QA validation.
        /// </summary>
        public System.Collections.IEnumerator RunComprehensiveQA()
        {
            Debug.Log("=== STARTING COMPREHENSIVE QA VALIDATION ===");
            
            validationResults.Clear();
            criticalIssues.Clear();
            warnings.Clear();
            performanceIssues.Clear();

            // 1. System Discovery
            yield return StartCoroutine(DiscoverSystems());
            
            // 2. Compilation Validation
            yield return StartCoroutine(ValidateCompilation());
            
            // 3. System Functionality
            yield return StartCoroutine(ValidateSystemFunctionality());
            
            // 4. Integration Testing
            if (integrationTest)
            {
                yield return StartCoroutine(ValidateSystemIntegration());
            }
            
            // 5. Performance Testing
            if (performanceTest)
            {
                yield return StartCoroutine(ValidatePerformance());
            }
            
            // 6. Save/Load Testing
            yield return StartCoroutine(ValidateSaveLoadSystems());
            
            // 7. Event System Testing
            yield return StartCoroutine(ValidateEventSystems());
            
            // 8. Localization Testing
            yield return StartCoroutine(ValidateLocalization());
            
            // 9. Input System Testing
            yield return StartCoroutine(ValidateInputSystems());
            
            // 10. Final Report
            GenerateFinalReport();
        }

        /// <summary>
        /// Discover all core systems.
        /// </summary>
        private System.Collections.IEnumerator DiscoverSystems()
        {
            Debug.Log("1. Discovering Systems...");
            
            narrativeSystem = DynamicNarrativeSystem.Instance;
            reactiveAI = ReactiveAISystem.Instance;
            magicSystem = MagicEvolutionSystem.Instance;
            economySystem = EconomySystem.Instance;
            dialogueSystem = AdvancedDialogueSystem.Instance;
            stealthSystem = StealthSystem.Instance;
            mapSystem = WorldMapSystem.Instance;
            timeSystem = DynamicTimeSystem.Instance;
            flashbackSystem = FlashbackSystem.Instance;
            moddingSystem = ModdingSystem.Instance;

            int foundSystems = 0;
            if (narrativeSystem != null) foundSystems++;
            if (reactiveAI != null) foundSystems++;
            if (magicSystem != null) foundSystems++;
            if (economySystem != null) foundSystems++;
            if (dialogueSystem != null) foundSystems++;
            if (stealthSystem != null) foundSystems++;
            if (mapSystem != null) foundSystems++;
            if (timeSystem != null) foundSystems++;
            if (flashbackSystem != null) foundSystems++;
            if (moddingSystem != null) foundSystems++;

            validationResults.Add($"✅ System Discovery: {foundSystems}/10 core systems found");
            
            if (foundSystems < 10)
            {
                warnings.Add($"Only {foundSystems}/10 core systems found in scene");
            }

            yield return null;
        }

        /// <summary>
        /// Validate compilation status.
        /// </summary>
        private System.Collections.IEnumerator ValidateCompilation()
        {
            Debug.Log("2. Validating Compilation...");
            
            // Check for compilation errors (Unity would prevent this script from running if there were errors)
            validationResults.Add("✅ Compilation: No compilation errors detected");
            
            yield return null;
        }

        /// <summary>
        /// Validate individual system functionality.
        /// </summary>
        private System.Collections.IEnumerator ValidateSystemFunctionality()
        {
            Debug.Log("3. Validating System Functionality...");
            
            // Test Narrative System
            if (narrativeSystem != null)
            {
                try
                {
                    narrativeSystem.MakeChoice("test_choice", "Test Choice", ChoiceType.Minor);
                    validationResults.Add("✅ Narrative System: Choice system functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Narrative System Error: {e.Message}");
                }
            }

            // Test Reactive AI
            if (reactiveAI != null)
            {
                try
                {
                    reactiveAI.RecordPlayerAction(ActionType.MeleeAttack, Vector3.zero);
                    validationResults.Add("✅ Reactive AI: Action recording functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Reactive AI Error: {e.Message}");
                }
            }

            // Test Magic System
            if (magicSystem != null)
            {
                try
                {
                    bool learned = magicSystem.LearnSpell("test_spell");
                    validationResults.Add("✅ Magic System: Spell learning functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Magic System Error: {e.Message}");
                }
            }

            // Test Economy System
            if (economySystem != null)
            {
                try
                {
                    economySystem.AddCurrency("gold", 100);
                    validationResults.Add("✅ Economy System: Currency management functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Economy System Error: {e.Message}");
                }
            }

            // Test Dialogue System
            if (dialogueSystem != null)
            {
                try
                {
                    dialogueSystem.SetCharacterTrait("test_character", "charisma", 50f);
                    validationResults.Add("✅ Dialogue System: Character traits functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Dialogue System Error: {e.Message}");
                }
            }

            // Test Stealth System
            if (stealthSystem != null)
            {
                try
                {
                    stealthSystem.EnterStealth();
                    stealthSystem.ExitStealth();
                    validationResults.Add("✅ Stealth System: Stealth state management functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Stealth System Error: {e.Message}");
                }
            }

            // Test Map System
            if (mapSystem != null)
            {
                try
                {
                    mapSystem.AddMarker("test_marker", Vector3.zero, MarkerType.Custom);
                    validationResults.Add("✅ Map System: Marker management functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Map System Error: {e.Message}");
                }
            }

            // Test Time System
            if (timeSystem != null)
            {
                try
                {
                    timeSystem.SetTimeScale(1.5f);
                    validationResults.Add("✅ Time System: Time manipulation functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Time System Error: {e.Message}");
                }
            }

            // Test Flashback System
            if (flashbackSystem != null)
            {
                try
                {
                    flashbackSystem.AddMemoryTrigger("test_trigger", Vector3.zero, 5f);
                    validationResults.Add("✅ Flashback System: Memory trigger functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Flashback System Error: {e.Message}");
                }
            }

            // Test Modding System
            if (moddingSystem != null)
            {
                try
                {
                    moddingSystem.RefreshAvailableMods();
                    validationResults.Add("✅ Modding System: Mod discovery functional");
                }
                catch (System.Exception e)
                {
                    criticalIssues.Add($"Modding System Error: {e.Message}");
                }
            }

            yield return null;
        }

        /// <summary>
        /// Validate system integration.
        /// </summary>
        private System.Collections.IEnumerator ValidateSystemIntegration()
        {
            Debug.Log("4. Validating System Integration...");
            
            // Test Narrative-AI Integration
            if (narrativeSystem != null && reactiveAI != null)
            {
                try
                {
                    // Test reputation affecting AI
                    narrativeSystem.ModifyFactionReputation("test_faction", 50f);
                    validationResults.Add("✅ Integration: Narrative-AI integration functional");
                }
                catch (System.Exception e)
                {
                    warnings.Add($"Narrative-AI Integration Warning: {e.Message}");
                }
            }

            // Test Magic-Economy Integration
            if (magicSystem != null && economySystem != null)
            {
                try
                {
                    // Test magic points as currency
                    int magicPoints = magicSystem.MagicPoints;
                    validationResults.Add("✅ Integration: Magic-Economy integration functional");
                }
                catch (System.Exception e)
                {
                    warnings.Add($"Magic-Economy Integration Warning: {e.Message}");
                }
            }

            yield return null;
        }

        /// <summary>
        /// Validate performance characteristics.
        /// </summary>
        private System.Collections.IEnumerator ValidatePerformance()
        {
            Debug.Log("5. Validating Performance...");
            
            float startTime = Time.realtimeSinceStartup;
            
            // Simulate heavy system usage
            for (int i = 0; i < 100; i++)
            {
                if (narrativeSystem != null)
                    narrativeSystem.MakeChoice($"perf_test_{i}", "Performance Test", ChoiceType.Minor);
                
                if (reactiveAI != null)
                    reactiveAI.RecordPlayerAction(ActionType.MeleeAttack, Vector3.zero);
                
                if (i % 10 == 0) yield return null; // Yield periodically
            }
            
            float endTime = Time.realtimeSinceStartup;
            float duration = endTime - startTime;
            
            if (duration < 1f)
            {
                validationResults.Add($"✅ Performance: Systems handle stress test in {duration:F3}s");
            }
            else
            {
                performanceIssues.Add($"Performance concern: Stress test took {duration:F3}s");
            }
            
            yield return null;
        }

        /// <summary>
        /// Validate save/load functionality.
        /// </summary>
        private System.Collections.IEnumerator ValidateSaveLoadSystems()
        {
            Debug.Log("6. Validating Save/Load Systems...");
            
            try
            {
                if (narrativeSystem != null)
                {
                    narrativeSystem.SaveNarrativeState();
                    validationResults.Add("✅ Save/Load: Narrative system save functional");
                }
                
                if (reactiveAI != null)
                {
                    reactiveAI.SaveAdaptationData();
                    validationResults.Add("✅ Save/Load: AI system save functional");
                }
            }
            catch (System.Exception e)
            {
                criticalIssues.Add($"Save/Load Error: {e.Message}");
            }
            
            yield return null;
        }

        /// <summary>
        /// Validate event systems.
        /// </summary>
        private System.Collections.IEnumerator ValidateEventSystems()
        {
            Debug.Log("7. Validating Event Systems...");
            
            int eventCount = 0;
            
            // Test narrative events
            if (narrativeSystem != null)
            {
                narrativeSystem.OnChoiceMade += (choice) => eventCount++;
                narrativeSystem.MakeChoice("event_test", "Event Test", ChoiceType.Minor);
            }
            
            yield return new WaitForSeconds(0.1f);
            
            if (eventCount > 0)
            {
                validationResults.Add("✅ Events: Event system functional");
            }
            else
            {
                warnings.Add("Event system may not be properly connected");
            }
            
            yield return null;
        }

        /// <summary>
        /// Validate localization support.
        /// </summary>
        private System.Collections.IEnumerator ValidateLocalization()
        {
            Debug.Log("8. Validating Localization...");
            
            var localizationManager = FindObjectOfType<LocalizationManager>();
            if (localizationManager != null)
            {
                validationResults.Add("✅ Localization: LocalizationManager found");
            }
            else
            {
                warnings.Add("LocalizationManager not found - localization may not be available");
            }
            
            yield return null;
        }

        /// <summary>
        /// Validate input systems.
        /// </summary>
        private System.Collections.IEnumerator ValidateInputSystems()
        {
            Debug.Log("9. Validating Input Systems...");
            
            var inputManager = FindObjectOfType<MultiInputControlSystem>();
            if (inputManager != null)
            {
                validationResults.Add("✅ Input: MultiInputControlSystem found");
            }
            else
            {
                warnings.Add("MultiInputControlSystem not found - input may not be properly configured");
            }
            
            yield return null;
        }

        /// <summary>
        /// Generate final QA report.
        /// </summary>
        private void GenerateFinalReport()
        {
            Debug.Log("10. Generating Final Report...");
            
            var report = new StringBuilder();
            report.AppendLine("=== COMPREHENSIVE QA VALIDATION REPORT ===");
            report.AppendLine($"Validation completed at: {System.DateTime.Now}");
            report.AppendLine();
            
            report.AppendLine("VALIDATION RESULTS:");
            foreach (var result in validationResults)
            {
                report.AppendLine(result);
            }
            report.AppendLine();
            
            if (criticalIssues.Count > 0)
            {
                report.AppendLine("CRITICAL ISSUES:");
                foreach (var issue in criticalIssues)
                {
                    report.AppendLine($"❌ {issue}");
                }
                report.AppendLine();
            }
            
            if (warnings.Count > 0)
            {
                report.AppendLine("WARNINGS:");
                foreach (var warning in warnings)
                {
                    report.AppendLine($"⚠️ {warning}");
                }
                report.AppendLine();
            }
            
            if (performanceIssues.Count > 0)
            {
                report.AppendLine("PERFORMANCE ISSUES:");
                foreach (var issue in performanceIssues)
                {
                    report.AppendLine($"🐌 {issue}");
                }
                report.AppendLine();
            }
            
            // Overall assessment
            if (criticalIssues.Count == 0)
            {
                report.AppendLine("🎉 OVERALL ASSESSMENT: PASSED");
                report.AppendLine("All core systems are functional and ready for production.");
            }
            else
            {
                report.AppendLine("❌ OVERALL ASSESSMENT: FAILED");
                report.AppendLine("Critical issues must be resolved before production.");
            }
            
            Debug.Log(report.ToString());
            
            // Save report to file
            System.IO.File.WriteAllText(
                System.IO.Path.Combine(Application.persistentDataPath, "QA_Report.txt"),
                report.ToString()
            );
        }
        #endregion

        #region Public API
        /// <summary>
        /// Run QA validation manually.
        /// </summary>
        [ContextMenu("Run QA Validation")]
        public void RunQAValidation()
        {
            StartCoroutine(RunComprehensiveQA());
        }
        #endregion
    }
}
