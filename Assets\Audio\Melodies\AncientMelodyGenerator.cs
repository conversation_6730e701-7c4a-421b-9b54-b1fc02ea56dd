using UnityEngine;
using UnityEditor;
using System.IO;

#if UNITY_EDITOR
/// <summary>
/// Generates ancient melody audio clips for Cinder of Darkness
/// Creates atmospheric musical tracks for contemplative and narrative systems
/// </summary>
public class AncientMelodyGenerator : EditorWindow
{
    [MenuItem("Cinder of Darkness/Generate Ancient Melodies")]
    public static void ShowWindow()
    {
        GetWindow<AncientMelodyGenerator>("Ancient Melody Generator");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Ancient Melody Audio Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("Generates 12 atmospheric ancient melody clips for contemplative systems", EditorStyles.helpBox);
        GUILayout.Space(10);
        
        if (GUILayout.Button("Generate All Ancient Melodies"))
        {
            GenerateAllAncientMelodies();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Assign to Contemplative Systems"))
        {
            AssignMelodiesToSystems();
        }
    }
    
    void GenerateAllAncientMelodies()
    {
        Debug.Log("Generating ancient melody audio clips...");
        
        // Create directory if it doesn't exist
        string melodyDir = "Assets/Audio/Melodies";
        if (!Directory.Exists(melodyDir))
        {
            Directory.CreateDirectory(melodyDir);
        }
        
        // Generate 12 ancient melody variations with different cultural themes
        AncientMelody[] melodies = new AncientMelody[]
        {
            new AncientMelody("lament_of_the_fallen", "Lament of the Fallen", 45f, CulturalOrigin.Ashlands, EmotionalTag.Loss, InstrumentType.Lyre),
            new AncientMelody("whispers_of_dawn", "Whispers of Dawn", 62f, CulturalOrigin.LightKingdom, EmotionalTag.Hope, InstrumentType.Flute),
            new AncientMelody("shadows_dance", "Shadows Dance", 38f, CulturalOrigin.ShadowKingdom, EmotionalTag.Mystery, InstrumentType.Oud),
            new AncientMelody("river_of_memories", "River of Memories", 71f, CulturalOrigin.Independent, EmotionalTag.Reflection, InstrumentType.Dulcimer),
            new AncientMelody("forgotten_homeland", "Forgotten Homeland", 55f, CulturalOrigin.Drenari, EmotionalTag.Longing, InstrumentType.VocalDrone),
            new AncientMelody("stars_lullaby", "Star's Lullaby", 83f, CulturalOrigin.Vaelari, EmotionalTag.Peace, InstrumentType.Harp),
            new AncientMelody("echoes_of_eternity", "Echoes of Eternity", 49f, CulturalOrigin.Ancient, EmotionalTag.Wonder, InstrumentType.Bells),
            new AncientMelody("mourning_winds", "Mourning Winds", 67f, CulturalOrigin.Ashlands, EmotionalTag.Sorrow, InstrumentType.Flute),
            new AncientMelody("temple_silence", "Temple Silence", 91f, CulturalOrigin.Monks, EmotionalTag.Serenity, InstrumentType.VocalDrone),
            new AncientMelody("lost_children_song", "Lost Children's Song", 34f, CulturalOrigin.Independent, EmotionalTag.Innocence, InstrumentType.Dulcimer),
            new AncientMelody("ember_heart", "Ember Heart", 58f, CulturalOrigin.Cinderborn, EmotionalTag.Determination, InstrumentType.Lyre),
            new AncientMelody("void_between_worlds", "Void Between Worlds", 76f, CulturalOrigin.Cosmic, EmotionalTag.Transcendence, InstrumentType.Ethereal)
        };
        
        // Generate each melody
        foreach (AncientMelody melody in melodies)
        {
            GenerateAncientMelodyClip(melody);
        }
        
        // Create the ancient melody asset
        CreateAncientMelodyAsset(melodies);
        
        AssetDatabase.Refresh();
        Debug.Log("All ancient melody clips generated successfully!");
    }
    
    void GenerateAncientMelodyClip(AncientMelody melody)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * melody.duration);
        AudioClip clip = AudioClip.Create(melody.fileName, samples, 2, sampleRate, false); // Stereo for spatial depth
        
        float[] data = new float[samples * 2]; // Stereo data
        
        // Generate melody based on instrument type and cultural origin
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            float normalizedTime = time / melody.duration;
            
            // Generate base melody
            Vector2 stereoSample = GenerateMelodyForInstrument(time, melody);
            
            // Apply cultural modulation
            stereoSample = ApplyCulturalCharacteristics(stereoSample, time, melody);
            
            // Apply emotional coloring
            stereoSample = ApplyEmotionalColoring(stereoSample, time, normalizedTime, melody);
            
            // Apply envelope
            float envelope = CalculateMelodyEnvelope(normalizedTime, melody);
            stereoSample *= envelope;
            
            // Store stereo data
            data[i * 2] = Mathf.Clamp(stereoSample.x, -1f, 1f);     // Left channel
            data[i * 2 + 1] = Mathf.Clamp(stereoSample.y, -1f, 1f); // Right channel
        }
        
        clip.SetData(data, 0);
        
        // Save the clip
        string path = $"Assets/Audio/Melodies/{melody.fileName}.asset";
        AssetDatabase.CreateAsset(clip, path);
        
        Debug.Log($"Generated ancient melody: {melody.title} ({melody.duration:F1}s, {melody.culturalOrigin}, {melody.emotionalTag})");
    }
    
    Vector2 GenerateMelodyForInstrument(float time, AncientMelody melody)
    {
        Vector2 sample = Vector2.zero;
        
        switch (melody.instrumentType)
        {
            case InstrumentType.Lyre:
                sample = GenerateLyreSound(time, melody);
                break;
            case InstrumentType.Oud:
                sample = GenerateOudSound(time, melody);
                break;
            case InstrumentType.Dulcimer:
                sample = GenerateDulcimerSound(time, melody);
                break;
            case InstrumentType.Flute:
                sample = GenerateFluteSound(time, melody);
                break;
            case InstrumentType.VocalDrone:
                sample = GenerateVocalDroneSound(time, melody);
                break;
            case InstrumentType.Harp:
                sample = GenerateHarpSound(time, melody);
                break;
            case InstrumentType.Bells:
                sample = GenerateBellSound(time, melody);
                break;
            case InstrumentType.Ethereal:
                sample = GenerateEtherealSound(time, melody);
                break;
        }
        
        return sample;
    }
    
    Vector2 GenerateLyreSound(float time, AncientMelody melody)
    {
        // Ancient lyre with plucked string harmonics
        float[] notes = GetScaleForCulture(melody.culturalOrigin);
        int noteIndex = Mathf.FloorToInt(time * 0.5f) % notes.Length;
        float frequency = notes[noteIndex];
        
        // Plucked string with decay
        float pluck = Mathf.Sin(time * frequency * 2f * Mathf.PI) * Mathf.Exp(-(time % 2f) * 1.5f);
        
        // Add harmonics
        float harmonic1 = Mathf.Sin(time * frequency * 3f * 2f * Mathf.PI) * 0.3f * Mathf.Exp(-(time % 2f) * 2f);
        float harmonic2 = Mathf.Sin(time * frequency * 5f * 2f * Mathf.PI) * 0.15f * Mathf.Exp(-(time % 2f) * 3f);
        
        float mono = (pluck + harmonic1 + harmonic2) * 0.4f;
        
        // Stereo spread for spatial depth
        return new Vector2(mono * 0.8f, mono * 1.2f);
    }
    
    Vector2 GenerateOudSound(float time, AncientMelody melody)
    {
        // Middle Eastern oud with microtonal bending
        float[] notes = GetScaleForCulture(melody.culturalOrigin);
        float baseFreq = notes[Mathf.FloorToInt(time * 0.3f) % notes.Length];
        
        // Add microtonal bending
        float bend = Mathf.Sin(time * 0.7f) * 0.05f;
        float frequency = baseFreq * (1f + bend);
        
        // Oud-like timbre with rich harmonics
        float fundamental = Mathf.Sin(time * frequency * 2f * Mathf.PI);
        float harmonic2 = Mathf.Sin(time * frequency * 2f * 2f * Mathf.PI) * 0.6f;
        float harmonic3 = Mathf.Sin(time * frequency * 3f * 2f * Mathf.PI) * 0.4f;
        float harmonic4 = Mathf.Sin(time * frequency * 4f * 2f * Mathf.PI) * 0.2f;
        
        float mono = (fundamental + harmonic2 + harmonic3 + harmonic4) * 0.3f;
        
        // Slight stereo delay for depth
        float leftDelay = 0.01f;
        float rightSample = mono;
        float leftSample = mono * 0.9f; // Slightly quieter left for natural feel
        
        return new Vector2(leftSample, rightSample);
    }
    
    Vector2 GenerateDulcimerSound(float time, AncientMelody melody)
    {
        // Hammered dulcimer with metallic resonance
        float[] notes = GetScaleForCulture(melody.culturalOrigin);
        
        float sample = 0f;
        
        // Multiple strings playing together
        for (int i = 0; i < 3; i++)
        {
            int noteIndex = (Mathf.FloorToInt(time * 0.4f) + i) % notes.Length;
            float frequency = notes[noteIndex];
            
            // Struck string with metallic decay
            float strike = Mathf.Sin(time * frequency * 2f * Mathf.PI) * Mathf.Exp(-(time % 2.5f) * 0.8f);
            
            // Metallic resonance
            float resonance = Mathf.Sin(time * frequency * 1.5f * 2f * Mathf.PI) * 0.2f * Mathf.Exp(-(time % 2.5f) * 0.5f);
            
            sample += (strike + resonance) * (0.3f / (i + 1));
        }
        
        // Wide stereo spread
        return new Vector2(sample * 0.7f, sample * 1.3f);
    }
    
    Vector2 GenerateFluteSound(float time, AncientMelody melody)
    {
        // Wooden flute with breath texture
        float[] notes = GetScaleForCulture(melody.culturalOrigin);
        int noteIndex = Mathf.FloorToInt(time * 0.6f) % notes.Length;
        float frequency = notes[noteIndex];
        
        // Flute fundamental with slight vibrato
        float vibrato = 1f + 0.02f * Mathf.Sin(time * 5f);
        float flute = Mathf.Sin(time * frequency * vibrato * 2f * Mathf.PI);
        
        // Add breath noise
        float breath = (Mathf.PerlinNoise(time * 20f, 0f) - 0.5f) * 0.1f;
        
        // Soft attack and release
        float envelope = Mathf.Sin((time % 3f) / 3f * Mathf.PI);
        
        float mono = (flute + breath) * envelope * 0.35f;
        
        // Natural stereo positioning
        return new Vector2(mono * 0.9f, mono * 1.1f);
    }
    
    Vector2 GenerateVocalDroneSound(float time, AncientMelody melody)
    {
        // Human vocal drone with formants
        float baseFreq = GetDroneFrequencyForCulture(melody.culturalOrigin);
        
        // Fundamental frequency
        float fundamental = Mathf.Sin(time * baseFreq * 2f * Mathf.PI);
        
        // Vocal formants (simplified)
        float formant1 = Mathf.Sin(time * baseFreq * 2f * 2f * Mathf.PI) * 0.4f;
        float formant2 = Mathf.Sin(time * baseFreq * 3f * 2f * Mathf.PI) * 0.3f;
        float formant3 = Mathf.Sin(time * baseFreq * 4f * 2f * Mathf.PI) * 0.2f;
        
        // Breathing modulation
        float breathing = 1f + 0.1f * Mathf.Sin(time * 0.3f);
        
        float mono = (fundamental + formant1 + formant2 + formant3) * breathing * 0.25f;
        
        // Center the vocal drone
        return new Vector2(mono, mono);
    }
    
    Vector2 GenerateHarpSound(float time, AncientMelody melody)
    {
        // Celtic/ancient harp with string resonance
        float[] notes = GetScaleForCulture(melody.culturalOrigin);
        
        float sample = 0f;
        
        // Arpeggiated pattern
        for (int i = 0; i < 4; i++)
        {
            float noteTime = time - (i * 0.2f);
            if (noteTime > 0)
            {
                int noteIndex = (Mathf.FloorToInt(noteTime * 0.8f) + i) % notes.Length;
                float frequency = notes[noteIndex];
                
                // Plucked harp string
                float pluck = Mathf.Sin(noteTime * frequency * 2f * Mathf.PI) * Mathf.Exp(-noteTime * 0.7f);
                
                sample += pluck * (0.4f / (i + 1));
            }
        }
        
        // Rich stereo spread
        return new Vector2(sample * 0.6f, sample * 1.4f);
    }
    
    Vector2 GenerateBellSound(float time, AncientMelody melody)
    {
        // Temple bells with long resonance
        float[] frequencies = { 220f, 330f, 440f, 660f }; // Bell harmonics
        
        float sample = 0f;
        
        foreach (float freq in frequencies)
        {
            // Bell strike with long decay
            float bell = Mathf.Sin(time * freq * 2f * Mathf.PI) * Mathf.Exp(-time * 0.2f);
            
            // Add metallic overtones
            float overtone = Mathf.Sin(time * freq * 2.7f * 2f * Mathf.PI) * 0.3f * Mathf.Exp(-time * 0.3f);
            
            sample += (bell + overtone) * 0.2f;
        }
        
        // Wide stereo field for temple ambience
        return new Vector2(sample * 0.5f, sample * 1.5f);
    }
    
    Vector2 GenerateEtherealSound(float time, AncientMelody melody)
    {
        // Otherworldly, cosmic sounds
        float baseFreq = 110f; // Deep cosmic frequency
        
        // Multiple sine waves with cosmic ratios
        float wave1 = Mathf.Sin(time * baseFreq * 2f * Mathf.PI);
        float wave2 = Mathf.Sin(time * baseFreq * 1.618f * 2f * Mathf.PI) * 0.7f; // Golden ratio
        float wave3 = Mathf.Sin(time * baseFreq * 2.414f * 2f * Mathf.PI) * 0.5f; // Silver ratio
        
        // Cosmic modulation
        float modulation = 1f + 0.3f * Mathf.Sin(time * 0.1f);
        
        // Add ethereal noise
        float noise = (Mathf.PerlinNoise(time * 5f, 0f) - 0.5f) * 0.2f;
        
        float mono = (wave1 + wave2 + wave3 + noise) * modulation * 0.3f;
        
        // Phase-shifted stereo for otherworldly effect
        float phase = Mathf.PI * 0.25f;
        return new Vector2(mono, mono * Mathf.Cos(phase));
    }
    
    Vector2 ApplyCulturalCharacteristics(Vector2 sample, float time, AncientMelody melody)
    {
        switch (melody.culturalOrigin)
        {
            case CulturalOrigin.Ashlands:
                // Add ash-like texture
                sample *= 1f + 0.1f * (Mathf.PerlinNoise(time * 10f, 0f) - 0.5f);
                break;
            case CulturalOrigin.LightKingdom:
                // Brighter, more resonant
                sample *= 1f + 0.05f * Mathf.Sin(time * 3f);
                break;
            case CulturalOrigin.ShadowKingdom:
                // Darker, more mysterious
                sample *= 0.9f + 0.1f * Mathf.Sin(time * 0.7f);
                break;
            case CulturalOrigin.Drenari:
                // Exotic scales and timbres
                sample *= 1f + 0.15f * Mathf.Sin(time * 1.3f);
                break;
            case CulturalOrigin.Vaelari:
                // Ethereal and flowing
                sample *= 1f + 0.2f * Mathf.Sin(time * 0.5f);
                break;
        }
        
        return sample;
    }
    
    Vector2 ApplyEmotionalColoring(Vector2 sample, float time, float normalizedTime, AncientMelody melody)
    {
        switch (melody.emotionalTag)
        {
            case EmotionalTag.Loss:
                // Add melancholic tremolo
                sample *= 1f + 0.1f * Mathf.Sin(time * 4f);
                break;
            case EmotionalTag.Hope:
                // Gentle upward modulation
                sample *= 1f + 0.05f * normalizedTime;
                break;
            case EmotionalTag.Mystery:
                // Unpredictable modulation
                sample *= 1f + 0.1f * Mathf.PerlinNoise(time * 2f, 0f);
                break;
            case EmotionalTag.Peace:
                // Stable, gentle
                sample *= 1f + 0.03f * Mathf.Sin(time * 1f);
                break;
            case EmotionalTag.Sorrow:
                // Descending modulation
                sample *= 1f - 0.05f * normalizedTime;
                break;
        }
        
        return sample;
    }
    
    float CalculateMelodyEnvelope(float normalizedTime, AncientMelody melody)
    {
        // Gentle fade in and out
        float fadeInTime = 0.1f;
        float fadeOutTime = 0.2f;
        
        float envelope = 1f;
        
        if (normalizedTime < fadeInTime)
        {
            envelope = normalizedTime / fadeInTime;
        }
        else if (normalizedTime > (1f - fadeOutTime))
        {
            float fadeOutProgress = (normalizedTime - (1f - fadeOutTime)) / fadeOutTime;
            envelope = 1f - fadeOutProgress;
        }
        
        // Apply smooth curve
        envelope = Mathf.SmoothStep(0f, 1f, envelope);
        
        return envelope;
    }
    
    float[] GetScaleForCulture(CulturalOrigin culture)
    {
        // Different musical scales for different cultures
        switch (culture)
        {
            case CulturalOrigin.Ashlands:
                return new float[] { 220f, 247f, 277f, 311f, 330f, 370f, 415f }; // Minor pentatonic
            case CulturalOrigin.LightKingdom:
                return new float[] { 261f, 294f, 330f, 349f, 392f, 440f, 494f }; // Major scale
            case CulturalOrigin.ShadowKingdom:
                return new float[] { 220f, 233f, 262f, 277f, 294f, 311f, 349f }; // Natural minor
            case CulturalOrigin.Drenari:
                return new float[] { 220f, 246f, 277f, 293f, 330f, 369f, 415f }; // Exotic scale
            case CulturalOrigin.Vaelari:
                return new float[] { 174f, 196f, 220f, 246f, 277f, 311f, 349f }; // Ethereal scale
            case CulturalOrigin.Ancient:
                return new float[] { 110f, 123f, 138f, 155f, 174f, 196f, 220f }; // Ancient modes
            case CulturalOrigin.Monks:
                return new float[] { 196f, 220f, 246f, 262f, 294f, 330f, 370f }; // Gregorian-inspired
            case CulturalOrigin.Cosmic:
                return new float[] { 110f, 138f, 174f, 220f, 277f, 349f, 440f }; // Cosmic ratios
            default:
                return new float[] { 220f, 247f, 277f, 311f, 330f, 370f, 415f };
        }
    }
    
    float GetDroneFrequencyForCulture(CulturalOrigin culture)
    {
        switch (culture)
        {
            case CulturalOrigin.Ashlands: return 110f;
            case CulturalOrigin.LightKingdom: return 130f;
            case CulturalOrigin.ShadowKingdom: return 98f;
            case CulturalOrigin.Drenari: return 123f;
            case CulturalOrigin.Vaelari: return 146f;
            case CulturalOrigin.Ancient: return 87f;
            case CulturalOrigin.Monks: return 116f;
            case CulturalOrigin.Cosmic: return 73f;
            default: return 110f;
        }
    }
    
    void CreateAncientMelodyAsset(AncientMelody[] melodies)
    {
        // Create a ScriptableObject asset to hold the melody clips
        AncientMelodyAsset asset = ScriptableObject.CreateInstance<AncientMelodyAsset>();
        
        // Load the generated clips
        asset.ancientMelodies = new AudioClip[melodies.Length];
        asset.melodyMetadata = new AncientMelodyMetadata[melodies.Length];
        
        for (int i = 0; i < melodies.Length; i++)
        {
            string clipPath = $"Assets/Audio/Melodies/{melodies[i].fileName}.asset";
            asset.ancientMelodies[i] = AssetDatabase.LoadAssetAtPath<AudioClip>(clipPath);
            
            asset.melodyMetadata[i] = new AncientMelodyMetadata
            {
                title = melodies[i].title,
                culturalOrigin = melodies[i].culturalOrigin,
                emotionalTag = melodies[i].emotionalTag,
                instrumentType = melodies[i].instrumentType,
                duration = melodies[i].duration
            };
        }
        
        // Set default settings
        asset.defaultVolume = 0.4f;
        asset.spatialBlend = 0.6f;
        asset.fadeInDuration = 3f;
        asset.fadeOutDuration = 5f;
        
        string assetPath = "Assets/Audio/Melodies/AncientMelodyAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);
        
        // Also create a copy in Resources folder for runtime loading
        string resourcesDir = "Assets/Resources";
        if (!Directory.Exists(resourcesDir))
        {
            Directory.CreateDirectory(resourcesDir);
        }
        
        string resourcesPath = "Assets/Resources/AncientMelodyAsset.asset";
        AssetDatabase.CopyAsset(assetPath, resourcesPath);
        
        AssetDatabase.SaveAssets();
        
        Debug.Log($"Created AncientMelodyAsset with {melodies.Length} clips at {assetPath}");
        Debug.Log($"Also created Resources copy at {resourcesPath} for runtime loading");
    }
    
    void AssignMelodiesToSystems()
    {
        Debug.Log("Assigning ancient melodies to contemplative systems...");
        
        // This would be implemented to find and assign to ContemplativeContentSystem
        // and other systems that use ancient melodies
        
        Debug.Log("Ancient melodies are ready for assignment to contemplative systems!");
    }
}

// Data structures for ancient melodies
[System.Serializable]
public class AncientMelody
{
    public string fileName;
    public string title;
    public float duration;
    public CulturalOrigin culturalOrigin;
    public EmotionalTag emotionalTag;
    public InstrumentType instrumentType;
    
    public AncientMelody(string fileName, string title, float duration, CulturalOrigin origin, EmotionalTag emotion, InstrumentType instrument)
    {
        this.fileName = fileName;
        this.title = title;
        this.duration = duration;
        this.culturalOrigin = origin;
        this.emotionalTag = emotion;
        this.instrumentType = instrument;
    }
}

public enum CulturalOrigin
{
    Ashlands,
    LightKingdom,
    ShadowKingdom,
    Independent,
    Drenari,
    Vaelari,
    Ancient,
    Monks,
    Cinderborn,
    Cosmic
}

public enum EmotionalTag
{
    Loss,
    Hope,
    Mystery,
    Reflection,
    Longing,
    Peace,
    Wonder,
    Sorrow,
    Serenity,
    Innocence,
    Determination,
    Transcendence
}

public enum InstrumentType
{
    Lyre,
    Oud,
    Dulcimer,
    Flute,
    VocalDrone,
    Harp,
    Bells,
    Ethereal
}
#endif
