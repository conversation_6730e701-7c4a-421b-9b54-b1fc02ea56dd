using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using CinderOfDarkness.Modding;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Mod Manager UI for Cinder of Darkness.
    /// Manages mod loading, configuration, and sandbox testing.
    /// </summary>
    public class ModManagerUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject modManagerPanel;
        [SerializeField] private Transform modListContainer;
        [SerializeField] private GameObject modEntryPrefab;
        [SerializeField] private Button closeButton;
        [SerializeField] private Button refreshButton;
        [SerializeField] private TextMeshProUGUI modCountText;

        [Header("Mod Details")]
        [SerializeField] private GameObject modDetailsPanel;
        [SerializeField] private TextMeshProUGUI modNameText;
        [SerializeField] private TextMeshProUG<PERSON> modAuthorText;
        [SerializeField] private TextMeshProUGUI modVersionText;
        [SerializeField] private TextMeshProUGUI modDescriptionText;
        [SerializeField] private Button loadModButton;
        [SerializeField] private Button unloadModButton;
        [SerializeField] private Button configureModButton;

        [Header("Sandbox Mode")]
        [SerializeField] private Toggle sandboxToggle;
        [SerializeField] private TextMeshProUGUI sandboxStatusText;
        [SerializeField] private Button enterSandboxButton;
        [SerializeField] private Button exitSandboxButton;

        [Header("Mod Categories")]
        [SerializeField] private Button allModsButton;
        [SerializeField] private Button contentModsButton;
        [SerializeField] private Button scriptModsButton;
        [SerializeField] private Button assetModsButton;
        [SerializeField] private Image categoryIndicator;

        [Header("Performance")]
        [SerializeField] private Transform performanceContainer;
        [SerializeField] private TextMeshProUGUI loadedModsText;
        [SerializeField] private TextMeshProUGUI memoryUsageText;
        [SerializeField] private Slider performanceSlider;

        [Header("Audio")]
        [SerializeField] private AudioClip openSound;
        [SerializeField] private AudioClip closeSound;
        [SerializeField] private AudioClip modLoadSound;
        [SerializeField] private AudioClip modUnloadSound;
        [SerializeField] private AudioClip sandboxToggleSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private ModdingSystem moddingSystem;
        private List<GameObject> modEntries = new List<GameObject>();
        private ModType currentCategory = ModType.Total; // All mods
        private ModInfo selectedMod;
        private bool isVisible = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
            UpdatePerformanceDisplay();
            UpdateSandboxStatus();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            moddingSystem = ModdingSystem.Instance;
        }

        private void SetupUI()
        {
            if (modManagerPanel != null)
                modManagerPanel.SetActive(false);

            if (modDetailsPanel != null)
                modDetailsPanel.SetActive(false);

            UpdateCategoryButtons();
            RefreshModList();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseModManager);

            if (refreshButton != null)
                refreshButton.onClick.AddListener(RefreshMods);

            if (loadModButton != null)
                loadModButton.onClick.AddListener(LoadSelectedMod);

            if (unloadModButton != null)
                unloadModButton.onClick.AddListener(UnloadSelectedMod);

            if (configureModButton != null)
                configureModButton.onClick.AddListener(ConfigureSelectedMod);

            if (sandboxToggle != null)
                sandboxToggle.onValueChanged.AddListener(OnSandboxToggled);

            if (enterSandboxButton != null)
                enterSandboxButton.onClick.AddListener(EnterSandbox);

            if (exitSandboxButton != null)
                exitSandboxButton.onClick.AddListener(ExitSandbox);

            // Category buttons
            if (allModsButton != null)
                allModsButton.onClick.AddListener(() => SetCategory(ModType.Total));

            if (contentModsButton != null)
                contentModsButton.onClick.AddListener(() => SetCategory(ModType.Content));

            if (scriptModsButton != null)
                scriptModsButton.onClick.AddListener(() => SetCategory(ModType.Script));

            if (assetModsButton != null)
                assetModsButton.onClick.AddListener(() => SetCategory(ModType.Asset));

            // Subscribe to modding system events
            if (moddingSystem != null)
            {
                moddingSystem.OnModLoaded += OnModLoaded;
                moddingSystem.OnModUnloaded += OnModUnloaded;
                moddingSystem.OnModError += OnModError;
                moddingSystem.OnSandboxModeChanged += OnSandboxModeChanged;
                moddingSystem.OnModsRefreshed += OnModsRefreshed;
            }
        }
        #endregion

        #region UI Management
        public void ToggleModManager()
        {
            if (isVisible)
                CloseModManager();
            else
                OpenModManager();
        }

        public void OpenModManager()
        {
            if (modManagerPanel != null)
            {
                modManagerPanel.SetActive(true);
                isVisible = true;
                RefreshModList();
                PlaySound(openSound);
            }
        }

        public void CloseModManager()
        {
            if (modManagerPanel != null)
            {
                modManagerPanel.SetActive(false);
                isVisible = false;
                PlaySound(closeSound);
            }

            if (modDetailsPanel != null)
                modDetailsPanel.SetActive(false);
        }

        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.F10))
            {
                ToggleModManager();
            }

            if (Input.GetKeyDown(KeyCode.F11))
            {
                if (sandboxToggle != null)
                    sandboxToggle.isOn = !sandboxToggle.isOn;
            }

            if (Input.GetKeyDown(KeyCode.Escape) && isVisible)
            {
                CloseModManager();
            }
        }

        private void SetCategory(ModType category)
        {
            currentCategory = category;
            UpdateCategoryButtons();
            RefreshModList();
        }

        private void UpdateCategoryButtons()
        {
            // Update button states
            if (allModsButton != null)
                allModsButton.interactable = currentCategory != ModType.Total;

            if (contentModsButton != null)
                contentModsButton.interactable = currentCategory != ModType.Content;

            if (scriptModsButton != null)
                scriptModsButton.interactable = currentCategory != ModType.Script;

            if (assetModsButton != null)
                assetModsButton.interactable = currentCategory != ModType.Asset;

            // Update category indicator
            if (categoryIndicator != null)
            {
                switch (currentCategory)
                {
                    case ModType.Total:
                        categoryIndicator.color = Color.white;
                        break;
                    case ModType.Content:
                        categoryIndicator.color = Color.green;
                        break;
                    case ModType.Script:
                        categoryIndicator.color = Color.blue;
                        break;
                    case ModType.Asset:
                        categoryIndicator.color = Color.yellow;
                        break;
                }
            }
        }
        #endregion

        #region Mod List Management
        private void RefreshModList()
        {
            ClearModEntries();

            if (moddingSystem == null) return;

            var mods = GetModsForCategory(currentCategory);
            foreach (var mod in mods)
            {
                CreateModEntry(mod);
            }

            UpdateModCount();
        }

        private List<ModInfo> GetModsForCategory(ModType category)
        {
            if (moddingSystem == null) return new List<ModInfo>();

            var allMods = moddingSystem.AvailableMods;

            if (category == ModType.Total)
                return allMods;

            return allMods.FindAll(m => m.modType == category);
        }

        private void CreateModEntry(ModInfo mod)
        {
            if (modEntryPrefab == null || modListContainer == null) return;

            GameObject entry = Instantiate(modEntryPrefab, modListContainer);
            var modEntry = entry.GetComponent<ModEntryUI>();

            if (modEntry != null)
            {
                modEntry.Setup(mod, this);
                modEntries.Add(entry);
            }
        }

        private void ClearModEntries()
        {
            foreach (var entry in modEntries)
            {
                if (entry != null)
                    Destroy(entry);
            }
            modEntries.Clear();
        }

        public void SelectMod(ModInfo mod)
        {
            selectedMod = mod;
            UpdateModDetails();
        }

        private void UpdateModDetails()
        {
            if (selectedMod == null)
            {
                if (modDetailsPanel != null)
                    modDetailsPanel.SetActive(false);
                return;
            }

            if (modDetailsPanel != null)
                modDetailsPanel.SetActive(true);

            if (modNameText != null)
                modNameText.text = selectedMod.modName;

            if (modAuthorText != null)
                modAuthorText.text = $"By: {selectedMod.author}";

            if (modVersionText != null)
                modVersionText.text = $"Version: {selectedMod.version}";

            if (modDescriptionText != null)
                modDescriptionText.text = selectedMod.description;

            UpdateActionButtons();
        }

        private void UpdateActionButtons()
        {
            if (selectedMod == null) return;

            bool isLoaded = moddingSystem.IsModLoaded(selectedMod.modId);

            if (loadModButton != null)
                loadModButton.gameObject.SetActive(!isLoaded);

            if (unloadModButton != null)
                unloadModButton.gameObject.SetActive(isLoaded);

            if (configureModButton != null)
                configureModButton.gameObject.SetActive(isLoaded);
        }

        private void UpdateModCount()
        {
            if (modCountText != null && moddingSystem != null)
            {
                int loaded = moddingSystem.LoadedMods.Count;
                int available = moddingSystem.AvailableMods.Count;
                modCountText.text = $"Loaded: {loaded}/{available}";
            }
        }

        private void RefreshMods()
        {
            if (moddingSystem != null)
            {
                moddingSystem.RefreshAvailableMods();
            }
        }
        #endregion

        #region Mod Actions
        private void LoadSelectedMod()
        {
            if (selectedMod == null || moddingSystem == null) return;

            bool success = moddingSystem.LoadMod(selectedMod.modId);
            if (success)
            {
                PlaySound(modLoadSound);
                UpdateModDetails();
                RefreshModList();
            }
        }

        private void UnloadSelectedMod()
        {
            if (selectedMod == null || moddingSystem == null) return;

            bool success = moddingSystem.UnloadMod(selectedMod.modId);
            if (success)
            {
                PlaySound(modUnloadSound);
                UpdateModDetails();
                RefreshModList();
            }
        }

        private void ConfigureSelectedMod()
        {
            if (selectedMod == null) return;

            // Open mod configuration panel
            Debug.Log($"Configuring mod: {selectedMod.modName}");
        }
        #endregion

        #region Sandbox Management
        private void OnSandboxToggled(bool enabled)
        {
            if (moddingSystem != null)
            {
                moddingSystem.ToggleSandboxMode(enabled);
                PlaySound(sandboxToggleSound);
            }
        }

        private void EnterSandbox()
        {
            if (moddingSystem != null)
            {
                moddingSystem.ToggleSandboxMode(true);
            }
        }

        private void ExitSandbox()
        {
            if (moddingSystem != null)
            {
                moddingSystem.ToggleSandboxMode(false);
            }
        }

        private void UpdateSandboxStatus()
        {
            if (moddingSystem == null) return;

            bool inSandbox = moddingSystem.IsSandboxMode;

            if (sandboxStatusText != null)
            {
                sandboxStatusText.text = inSandbox ? "SANDBOX MODE" : "NORMAL MODE";
                sandboxStatusText.color = inSandbox ? Color.yellow : Color.white;
            }

            if (enterSandboxButton != null)
                enterSandboxButton.gameObject.SetActive(!inSandbox);

            if (exitSandboxButton != null)
                exitSandboxButton.gameObject.SetActive(inSandbox);
        }
        #endregion

        #region Performance Display
        private void UpdatePerformanceDisplay()
        {
            if (moddingSystem == null) return;

            var stats = moddingSystem.GetModdingStats();

            if (loadedModsText != null)
                loadedModsText.text = $"Loaded Mods: {stats.totalLoadedMods}";

            if (memoryUsageText != null)
                memoryUsageText.text = $"Memory: {stats.totalMemoryUsage:F1}MB";

            if (performanceSlider != null)
            {
                // Performance indicator (0-1, where 1 is optimal)
                float performance = Mathf.Clamp01(1f - (stats.totalMemoryUsage / 1000f));
                performanceSlider.value = performance;
                
                var fillImage = performanceSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (performance > 0.7f)
                        fillImage.color = Color.green;
                    else if (performance > 0.4f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }
        #endregion

        #region Event Handlers
        private void OnModLoaded(ModInfo mod)
        {
            if (isVisible)
            {
                RefreshModList();
                if (selectedMod != null && selectedMod.modId == mod.modId)
                {
                    UpdateModDetails();
                }
            }
        }

        private void OnModUnloaded(ModInfo mod)
        {
            if (isVisible)
            {
                RefreshModList();
                if (selectedMod != null && selectedMod.modId == mod.modId)
                {
                    UpdateModDetails();
                }
            }
        }

        private void OnModError(ModInfo mod, string error)
        {
            Debug.LogError($"Mod Error [{mod.modName}]: {error}");
        }

        private void OnSandboxModeChanged(bool enabled)
        {
            // Sandbox status is updated in Update()
        }

        private void OnModsRefreshed()
        {
            if (isVisible)
            {
                RefreshModList();
            }
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class ModEntryUI : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI modNameText;
        [SerializeField] private TextMeshProUGUI modVersionText;
        [SerializeField] private Image modTypeIcon;
        [SerializeField] private Toggle enabledToggle;
        [SerializeField] private Button selectButton;

        private ModInfo mod;
        private ModManagerUI modManagerUI;

        public void Setup(ModInfo modInfo, ModManagerUI managerUI)
        {
            mod = modInfo;
            modManagerUI = managerUI;

            if (modNameText != null)
                modNameText.text = mod.modName;

            if (modVersionText != null)
                modVersionText.text = mod.version;

            if (enabledToggle != null)
            {
                enabledToggle.isOn = mod.isEnabled;
                enabledToggle.onValueChanged.AddListener(OnEnabledToggled);
            }

            if (selectButton != null)
                selectButton.onClick.AddListener(() => modManagerUI.SelectMod(mod));

            // Set icon color based on mod type
            if (modTypeIcon != null)
            {
                switch (mod.modType)
                {
                    case ModType.Content:
                        modTypeIcon.color = Color.green;
                        break;
                    case ModType.Script:
                        modTypeIcon.color = Color.blue;
                        break;
                    case ModType.Asset:
                        modTypeIcon.color = Color.yellow;
                        break;
                    case ModType.UI:
                        modTypeIcon.color = Color.cyan;
                        break;
                    case ModType.Audio:
                        modTypeIcon.color = Color.magenta;
                        break;
                }
            }
        }

        private void OnEnabledToggled(bool enabled)
        {
            var moddingSystem = ModdingSystem.Instance;
            if (moddingSystem == null) return;

            if (enabled)
                moddingSystem.LoadMod(mod.modId);
            else
                moddingSystem.UnloadMod(mod.modId);
        }
    }
    #endregion
}
