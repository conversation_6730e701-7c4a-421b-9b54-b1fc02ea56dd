using UnityEngine;

/// <summary>
/// Component attached to objects placed in the arena editor
/// Provides identification and data for arena objects
/// </summary>
public class ArenaObject : MonoBehaviour
{
    [Header("Arena Object Data")]
    public ArenaEditorManager.PlacementMode objectType;
    public string objectId;
    public ArenaData arenaData;
    
    [Header("Visual Settings")]
    public bool showGizmos = true;
    public Color gizmoColor = Color.white;
    public float gizmoSize = 1f;
    
    [Header("Object Properties")]
    public bool isDestructible = false;
    public float health = 100f;
    public float damage = 0f;
    public float triggerRadius = 2f;
    public string[] tags = new string[0];
    
    void Start()
    {
        if (string.IsNullOrEmpty(objectId))
        {
            objectId = System.Guid.NewGuid().ToString();
        }
        
        SetupObjectProperties();
    }
    
    void SetupObjectProperties()
    {
        switch (objectType)
        {
            case ArenaEditorManager.PlacementMode.EnemySpawn:
                SetupEnemySpawn();
                break;
            case ArenaEditorManager.PlacementMode.DestructibleProp:
                SetupDestructibleProp();
                break;
            case ArenaEditorManager.PlacementMode.StaticProp:
                SetupStaticProp();
                break;
            case ArenaEditorManager.PlacementMode.Trap:
                SetupTrap();
                break;
            case ArenaEditorManager.PlacementMode.EnvironmentalTrigger:
                SetupEnvironmentalTrigger();
                break;
            case ArenaEditorManager.PlacementMode.PlayerSpawn:
                SetupPlayerSpawn();
                break;
        }
    }
    
    void SetupEnemySpawn()
    {
        gizmoColor = Color.red;
        gizmoSize = 2f;
        
        // Add spawn point marker
        if (GetComponent<Collider>() == null)
        {
            var collider = gameObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = 1f;
        }
    }
    
    void SetupDestructibleProp()
    {
        gizmoColor = Color.yellow;
        isDestructible = true;
        health = 100f;
        
        // Ensure it has a collider
        if (GetComponent<Collider>() == null)
        {
            gameObject.AddComponent<BoxCollider>();
        }
        
        // Add destructible component if needed
        if (GetComponent<DestructibleObject>() == null)
        {
            var destructible = gameObject.AddComponent<DestructibleObject>();
            destructible.maxHealth = health;
            destructible.currentHealth = health;
        }
    }
    
    void SetupStaticProp()
    {
        gizmoColor = Color.gray;
        isDestructible = false;
        
        // Ensure it has a collider
        if (GetComponent<Collider>() == null)
        {
            gameObject.AddComponent<BoxCollider>();
        }
    }
    
    void SetupTrap()
    {
        gizmoColor = Color.orange;
        damage = 50f;
        triggerRadius = 2f;
        
        // Add trigger collider
        if (GetComponent<Collider>() == null)
        {
            var collider = gameObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = triggerRadius;
        }
        
        // Add trap component
        if (GetComponent<TrapTrigger>() == null)
        {
            var trap = gameObject.AddComponent<TrapTrigger>();
            trap.damage = damage;
            trap.triggerRadius = triggerRadius;
        }
    }
    
    void SetupEnvironmentalTrigger()
    {
        gizmoColor = Color.green;
        triggerRadius = 3f;
        
        // Add trigger collider
        if (GetComponent<Collider>() == null)
        {
            var collider = gameObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = triggerRadius;
        }
        
        // Add environmental effect component
        if (GetComponent<EnvironmentalEffect>() == null)
        {
            gameObject.AddComponent<EnvironmentalEffect>();
        }
    }
    
    void SetupPlayerSpawn()
    {
        gizmoColor = Color.blue;
        gizmoSize = 2f;
        
        // Add spawn point marker
        if (GetComponent<Collider>() == null)
        {
            var collider = gameObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = 1f;
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        Gizmos.color = gizmoColor;
        
        switch (objectType)
        {
            case ArenaEditorManager.PlacementMode.EnemySpawn:
                DrawEnemySpawnGizmo();
                break;
            case ArenaEditorManager.PlacementMode.DestructibleProp:
                DrawPropGizmo();
                break;
            case ArenaEditorManager.PlacementMode.StaticProp:
                DrawPropGizmo();
                break;
            case ArenaEditorManager.PlacementMode.Trap:
                DrawTrapGizmo();
                break;
            case ArenaEditorManager.PlacementMode.EnvironmentalTrigger:
                DrawEnvironmentalGizmo();
                break;
            case ArenaEditorManager.PlacementMode.PlayerSpawn:
                DrawPlayerSpawnGizmo();
                break;
        }
    }
    
    void DrawEnemySpawnGizmo()
    {
        // Draw enemy spawn marker
        Gizmos.DrawWireSphere(transform.position, gizmoSize);
        Gizmos.DrawLine(transform.position, transform.position + Vector3.up * gizmoSize * 2);
        
        // Draw facing direction
        Gizmos.color = Color.white;
        Gizmos.DrawRay(transform.position + Vector3.up, transform.forward * gizmoSize);
    }
    
    void DrawPropGizmo()
    {
        // Draw prop outline
        var bounds = GetComponent<Collider>()?.bounds ?? new Bounds(transform.position, Vector3.one);
        Gizmos.DrawWireCube(bounds.center, bounds.size);
        
        if (isDestructible)
        {
            // Draw health indicator
            Gizmos.color = Color.Lerp(Color.red, Color.green, health / 100f);
            Gizmos.DrawCube(transform.position + Vector3.up * (bounds.size.y + 0.5f), new Vector3(0.5f, 0.1f, 0.5f));
        }
    }
    
    void DrawTrapGizmo()
    {
        // Draw trap trigger area
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 0.3f);
        Gizmos.DrawSphere(transform.position, triggerRadius);
        
        // Draw trap outline
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireSphere(transform.position, triggerRadius);
        
        // Draw danger indicator
        Gizmos.color = Color.red;
        Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
    }
    
    void DrawEnvironmentalGizmo()
    {
        // Draw environmental effect area
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 0.2f);
        Gizmos.DrawSphere(transform.position, triggerRadius);
        
        // Draw effect outline
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireSphere(transform.position, triggerRadius);
        
        // Draw effect type indicator
        switch (name.ToLower())
        {
            case "fog":
                Gizmos.color = Color.gray;
                break;
            case "healing":
                Gizmos.color = Color.green;
                break;
            case "lava":
                Gizmos.color = Color.red;
                break;
            case "wind":
                Gizmos.color = Color.cyan;
                break;
        }
        
        Gizmos.DrawCube(transform.position + Vector3.up, Vector3.one * 0.3f);
    }
    
    void DrawPlayerSpawnGizmo()
    {
        // Draw player spawn marker
        Gizmos.DrawWireSphere(transform.position, gizmoSize);
        
        // Draw player indicator
        Gizmos.color = Color.blue;
        Gizmos.DrawCube(transform.position + Vector3.up * 0.5f, new Vector3(0.5f, 1f, 0.5f));
        
        // Draw facing direction
        Gizmos.color = Color.white;
        Gizmos.DrawRay(transform.position + Vector3.up, transform.forward * gizmoSize);
    }
    
    void OnDrawGizmosSelected()
    {
        if (!showGizmos) return;
        
        // Draw selection highlight
        Gizmos.color = Color.yellow;
        
        var bounds = GetComponent<Collider>()?.bounds ?? new Bounds(transform.position, Vector3.one);
        Gizmos.DrawWireCube(bounds.center, bounds.size * 1.1f);
        
        // Draw object info
        DrawObjectInfo();
    }
    
    void DrawObjectInfo()
    {
        #if UNITY_EDITOR
        var style = new GUIStyle();
        style.normal.textColor = Color.white;
        style.fontSize = 12;
        
        Vector3 screenPos = Camera.current.WorldToScreenPoint(transform.position + Vector3.up * 2);
        screenPos.y = Screen.height - screenPos.y;
        
        string info = $"{objectType}\n";
        if (isDestructible)
            info += $"Health: {health}\n";
        if (damage > 0)
            info += $"Damage: {damage}\n";
        if (triggerRadius > 0)
            info += $"Radius: {triggerRadius}m\n";
        
        UnityEditor.Handles.Label(transform.position + Vector3.up * 2, info);
        #endif
    }
    
    // Public methods for arena testing
    public void ActivateForTesting()
    {
        switch (objectType)
        {
            case ArenaEditorManager.PlacementMode.Trap:
                var trap = GetComponent<TrapTrigger>();
                if (trap != null)
                    trap.enabled = true;
                break;
            case ArenaEditorManager.PlacementMode.EnvironmentalTrigger:
                var envEffect = GetComponent<EnvironmentalEffect>();
                if (envEffect != null)
                    envEffect.enabled = true;
                break;
        }
    }
    
    public void DeactivateForEditing()
    {
        switch (objectType)
        {
            case ArenaEditorManager.PlacementMode.Trap:
                var trap = GetComponent<TrapTrigger>();
                if (trap != null)
                    trap.enabled = false;
                break;
            case ArenaEditorManager.PlacementMode.EnvironmentalTrigger:
                var envEffect = GetComponent<EnvironmentalEffect>();
                if (envEffect != null)
                    envEffect.enabled = false;
                break;
        }
    }
    
    public ArenaData.PropPlacement GetPropPlacementData()
    {
        return new ArenaData.PropPlacement
        {
            propId = objectId,
            propName = gameObject.name,
            position = transform.position,
            rotation = transform.eulerAngles,
            scale = transform.localScale,
            isDestructible = isDestructible,
            health = health,
            tags = tags
        };
    }
    
    public ArenaData.TrapPlacement GetTrapPlacementData()
    {
        return new ArenaData.TrapPlacement
        {
            trapId = objectId,
            trapType = gameObject.name,
            position = transform.position,
            rotation = transform.eulerAngles,
            triggerRadius = triggerRadius,
            damage = damage,
            cooldown = 5f,
            isReusable = true,
            triggerTags = new string[] { "Player" }
        };
    }
}

/// <summary>
/// Simple destructible object component for arena props
/// </summary>
public class DestructibleObject : MonoBehaviour
{
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    public GameObject destroyEffect;
    public AudioClip destroySound;
    
    void Start()
    {
        currentHealth = maxHealth;
    }
    
    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        
        if (currentHealth <= 0)
        {
            DestroyObject();
        }
    }
    
    void DestroyObject()
    {
        if (destroyEffect != null)
        {
            Instantiate(destroyEffect, transform.position, transform.rotation);
        }
        
        if (destroySound != null)
        {
            AudioSource.PlayClipAtPoint(destroySound, transform.position);
        }
        
        Destroy(gameObject);
    }
}

/// <summary>
/// Simple trap trigger component for arena traps
/// </summary>
public class TrapTrigger : MonoBehaviour
{
    public float damage = 50f;
    public float triggerRadius = 2f;
    public float cooldown = 5f;
    public bool isReusable = true;
    public GameObject triggerEffect;
    public AudioClip triggerSound;
    
    private float lastTriggerTime = 0f;
    private bool hasTriggered = false;
    
    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") && CanTrigger())
        {
            TriggerTrap(other.gameObject);
        }
    }
    
    bool CanTrigger()
    {
        if (!isReusable && hasTriggered)
            return false;
        
        return Time.time - lastTriggerTime >= cooldown;
    }
    
    void TriggerTrap(GameObject target)
    {
        lastTriggerTime = Time.time;
        hasTriggered = true;
        
        // Deal damage
        var playerStats = target.GetComponent<PlayerStats>();
        if (playerStats != null)
        {
            playerStats.TakeDamage(damage);
        }
        
        // Play effects
        if (triggerEffect != null)
        {
            Instantiate(triggerEffect, transform.position, transform.rotation);
        }
        
        if (triggerSound != null)
        {
            AudioSource.PlayClipAtPoint(triggerSound, transform.position);
        }
        
        Debug.Log($"Trap triggered! Dealt {damage} damage to {target.name}");
    }
}

/// <summary>
/// Simple environmental effect component for arena environmental triggers
/// </summary>
public class EnvironmentalEffect : MonoBehaviour
{
    public EnvironmentalEffectType effectType = EnvironmentalEffectType.Fog;
    public float effectStrength = 1f;
    public float effectRadius = 3f;
    public bool continuousEffect = true;
    
    public enum EnvironmentalEffectType
    {
        Fog,
        Healing,
        Damage,
        Wind,
        Slow,
        Speed
    }
    
    void OnTriggerStay(Collider other)
    {
        if (other.CompareTag("Player") && continuousEffect)
        {
            ApplyEffect(other.gameObject);
        }
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") && !continuousEffect)
        {
            ApplyEffect(other.gameObject);
        }
    }
    
    void ApplyEffect(GameObject target)
    {
        var playerStats = target.GetComponent<PlayerStats>();
        if (playerStats == null) return;
        
        switch (effectType)
        {
            case EnvironmentalEffectType.Healing:
                playerStats.Heal(effectStrength * Time.deltaTime);
                break;
            case EnvironmentalEffectType.Damage:
                playerStats.TakeDamage(effectStrength * Time.deltaTime);
                break;
            case EnvironmentalEffectType.Fog:
                // Reduce visibility (would need camera effects)
                break;
            case EnvironmentalEffectType.Wind:
                // Apply force to player (would need rigidbody)
                var rb = target.GetComponent<Rigidbody>();
                if (rb != null)
                {
                    rb.AddForce(transform.forward * effectStrength);
                }
                break;
        }
    }
}
