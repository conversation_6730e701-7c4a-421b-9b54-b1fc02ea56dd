# Cinder of Darkness - Steam Build Guide

## Overview
This guide covers building and preparing **Cinder of Darkness** for Steam release. The game is designed as a AAA-quality soulslike experience with deep narrative, brutal combat, and psychological elements.

## Prerequisites

### Unity Setup
- **Unity Version**: 2022.3 LTS or newer
- **Render Pipeline**: Universal Render Pipeline (URP)
- **Platform**: PC (Windows, Mac, Linux)
- **Target Resolution**: 1920x1080 minimum, 4K support

### Required Unity Packages
```
- Universal RP
- Post Processing Stack v2
- Cinemachine
- Input System
- Addressables
- Unity Analytics
- Cloud Build (optional)
```

### Steam Integration
- **Steamworks SDK**: Latest version
- **Steam Audio**: For 3D spatial audio
- **Steam Workshop**: For mod support (future)
- **Steam Achievements**: Integrated with moral choices

## Build Configuration

### Graphics Settings
```csharp
// Quality Settings for Steam Release
QualitySettings.shadows = ShadowQuality.All;
QualitySettings.shadowResolution = ShadowResolution.VeryHigh;
QualitySettings.shadowDistance = 150f;
QualitySettings.shadowCascades = 4;
QualitySettings.antiAliasing = 4;
QualitySettings.masterTextureLimit = 0;
QualitySettings.anisotropicFiltering = AnisotropicFiltering.ForceEnable;
QualitySettings.realtimeReflectionProbes = true;
```

### Performance Targets
- **Minimum FPS**: 30 FPS on GTX 1060 / RX 580
- **Recommended FPS**: 60 FPS on RTX 3070 / RX 6700 XT
- **Maximum Settings**: 4K 60 FPS on RTX 4080+

### Build Settings
```
Platform: PC, Mac & Linux Standalone
Architecture: x86_64
Scripting Backend: IL2CPP
Api Compatibility Level: .NET Standard 2.1
Compression Method: LZ4HC
```

## Steam Features Integration

### Achievements System
```csharp
// Example Achievement Integration
public class SteamAchievements : MonoBehaviour
{
    private Achievement[] achievements = new Achievement[]
    {
        new Achievement("FIRST_BLOOD", "First Blood", "Defeat your first enemy"),
        new Achievement("MENTOR_SLAYER", "Kinslayer", "Defeat the Fallen Mentor"),
        new Achievement("CULTURAL_BRIDGE", "Bridge Builder", "Gain respect from 5 different cultures"),
        new Achievement("PSYCHOLOGICAL_MASTER", "Mind Over Matter", "Achieve perfect mental stability"),
        new Achievement("WEAPON_MASTER", "Legendary Arsenal", "Unlock all legendary weapons"),
        new Achievement("HIDDEN_REALM", "Realm Walker", "Discover all hidden realms"),
        new Achievement("PHILOSOPHICAL_JOURNEY", "The Examined Life", "Hear all philosophical quotes"),
        new Achievement("PERFECT_BALANCE", "The Eclipse Path", "Complete the game on Eclipse path"),
        new Achievement("BRUTAL_WARRIOR", "Blood and Ash", "Perform 100 brutal finishers"),
        new Achievement("CULTURAL_SCHOLAR", "Student of the World", "Learn all martial arts schools")
    };
}
```

### Steam Cloud Save
```csharp
// Save data structure for Steam Cloud
[System.Serializable]
public class CloudSaveData
{
    public float storyProgress;
    public PlayerStats.MoralPath currentPath;
    public float sunAlignment;
    public float moonAlignment;
    public List<string> unlockedWeapons;
    public List<string> learnedMartialArts;
    public List<string> discoveredRealms;
    public float mentalStability;
    public float trauma;
    public Dictionary<string, float> factionStandings;
    public List<string> completedEvents;
    public int characterAge;
    public CharacterProgression.FacialHairStage facialHair;
}
```

## Content Warnings & Rating

### ESRB Rating: M (Mature 17+)
- **Intense Violence**: Brutal combat with dismemberment
- **Blood and Gore**: Realistic blood physics and effects
- **Strong Language**: Occasional strong language in dialogue
- **Suggestive Themes**: Mature philosophical themes
- **Simulated Gambling**: None

### Content Warnings
```
- Graphic Violence and Gore
- Psychological Themes (trauma, mental health)
- Cultural and Religious References
- Themes of War and Conflict
- Death and Mortality
- Moral Ambiguity
```

## Localization Support

### Supported Languages
- **Full Localization**: English, Spanish, French, German, Japanese, Chinese (Simplified)
- **Subtitles Only**: Arabic, Russian, Portuguese, Italian, Korean

### Cultural Sensitivity
- All cultural representations reviewed by cultural consultants
- Authentic language phrases with proper pronunciation guides
- Respectful portrayal of religious and philosophical concepts
- Historical accuracy in weapon and martial arts descriptions

## Performance Optimization

### Memory Management
```csharp
// Addressable Asset System for large assets
public class AssetManager : MonoBehaviour
{
    public async void LoadRegionAssets(string regionName)
    {
        var handle = Addressables.LoadAssetAsync<GameObject>($"Region_{regionName}");
        await handle.Task;
        // Use asset
        Addressables.Release(handle);
    }
}
```

### Graphics Optimization
- **LOD System**: 4 levels for all character models
- **Occlusion Culling**: Enabled for all scenes
- **Texture Streaming**: Enabled for large textures
- **Shader Variants**: Optimized for different quality levels

## Testing Checklist

### Core Functionality
- [ ] All combat systems working (melee, magic, finishers)
- [ ] Psychological system affects gameplay correctly
- [ ] Hostility system responds to player actions
- [ ] All martial arts schools functional
- [ ] Weapon lore system complete
- [ ] Hidden boss reveals trigger properly
- [ ] World exploration and realm travel working

### Steam Integration
- [ ] Steam achievements unlock correctly
- [ ] Cloud saves sync properly
- [ ] Steam overlay functional
- [ ] Controller support (Xbox, PlayStation, Steam Controller)
- [ ] Steam Input API integration
- [ ] Big Picture Mode compatibility

### Performance
- [ ] Maintains target FPS on minimum specs
- [ ] No memory leaks during extended play
- [ ] Loading times under 30 seconds
- [ ] Stable framerate during intense combat
- [ ] No crashes during 4+ hour sessions

### Accessibility
- [ ] Colorblind-friendly UI
- [ ] Subtitle options
- [ ] Remappable controls
- [ ] Audio cues for important events
- [ ] Difficulty options (gore level, combat assistance)

## Build Process

### 1. Pre-Build Setup
```bash
# Clean previous builds
rm -rf Builds/
mkdir Builds

# Update version number
echo "1.0.0" > version.txt

# Generate build timestamp
date > build_timestamp.txt
```

### 2. Unity Build
```csharp
// Build script
public static void BuildGame()
{
    string[] scenes = {
        "Assets/Scenes/MainMenu.unity",
        "Assets/Scenes/StartingVillage.unity",
        "Assets/Scenes/QadeshWastes.unity",
        "Assets/Scenes/JotunheimReaches.unity",
        "Assets/Scenes/CeruleanArchipelago.unity",
        "Assets/Scenes/BurningDepths.unity",
        "Assets/Scenes/CelestialSpires.unity",
        "Assets/Scenes/SunkenAtlas.unity"
    };
    
    BuildPlayerOptions buildOptions = new BuildPlayerOptions();
    buildOptions.scenes = scenes;
    buildOptions.locationPathName = "Builds/CinderOfDarkness.exe";
    buildOptions.target = BuildTarget.StandaloneWindows64;
    buildOptions.options = BuildOptions.None;
    
    BuildPipeline.BuildPlayer(buildOptions);
}
```

### 3. Post-Build Processing
```bash
# Copy additional files
cp README.md Builds/
cp CREDITS.md Builds/
cp LICENSE.txt Builds/

# Create Steam depot structure
mkdir SteamDepot
cp -r Builds/* SteamDepot/
```

### 4. Steam Upload
```bash
# Use SteamCMD for automated uploads
steamcmd +login username +run_app_build ../scripts/app_build_script.vdf +quit
```

## Marketing Assets

### Steam Store Page
- **Header Image**: 1920x1080 featuring The Cinderborn
- **Screenshots**: 10 high-quality gameplay screenshots
- **Trailer**: 2-minute gameplay trailer with philosophical narration
- **Description**: Emphasize cultural diversity, brutal combat, psychological depth

### Key Features for Store Page
```
🗡️ BRUTAL SOULS-LIKE COMBAT
Experience visceral combat with dismemberment, blood physics, and brutal finishers

🌍 CULTURAL AUTHENTICITY  
Meet warriors from diverse cultures: Samurai, Saracens, Crusaders, Vikings, and more

🧠 PSYCHOLOGICAL JOURNEY
Your mental state affects gameplay, dialogue, and world perception

⚔️ LEGENDARY WEAPONS & MARTIAL ARTS
Master real fighting styles and wield weapons with deep cultural lore

🌟 DEEP NARRATIVE LAYERS
Uncover hidden boss identities through cryptic foreshadowing

🌎 DIVERSE REALMS
Explore Hell, Cloud City, Atlantis, and other mystical dimensions

🎭 PHILOSOPHICAL DEPTH
Navigate complex moral choices in a world without clear heroes or villains
```

## Post-Launch Support

### Update Schedule
- **Week 1**: Hotfixes for critical bugs
- **Month 1**: Balance updates and quality of life improvements
- **Month 3**: New Game+ mode with additional content
- **Month 6**: DLC expansion with new realms and cultures

### Community Features
- **Steam Workshop**: Mod support for custom weapons and armor
- **Steam Forums**: Active community engagement
- **Discord Server**: Real-time player support and feedback
- **Developer Blogs**: Regular updates on development process

## Legal Considerations

### Cultural Sensitivity
- All cultural consultants credited
- Proper attribution for historical references
- Respectful use of cultural symbols and practices
- Clear disclaimers about fictional elements

### Music & Audio
- All music composed in-house or properly licensed
- Cultural music samples used with permission
- Voice acting contracts include commercial rights
- Sound effects library properly licensed

### Third-Party Assets
- All Unity Asset Store purchases documented
- Proper attribution in credits
- Commercial licenses verified
- No GPL or copyleft licensed content

## Success Metrics

### Launch Targets
- **First Week Sales**: 10,000 copies
- **First Month**: 50,000 copies
- **Steam Reviews**: 85%+ positive
- **Metacritic Score**: 80+
- **Twitch/YouTube**: 1M+ views in first month

### Long-term Goals
- **Total Sales**: 500,000 copies in first year
- **DLC Adoption**: 30% of base game owners
- **Community Size**: 100,000+ Discord members
- **Mod Support**: 1,000+ Steam Workshop items

---

**Cinder of Darkness** represents a new standard in culturally diverse, psychologically complex gaming experiences. This build guide ensures the game meets AAA quality standards while respecting the diverse cultures and philosophies it represents.
