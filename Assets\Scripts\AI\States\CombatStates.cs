using UnityEngine;

namespace CinderOfDarkness.AI
{
    /// <summary>
    /// Attack state for AI enemies.
    /// AI performs attacks when in range of target.
    /// </summary>
    public class AttackState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Attack;
        #endregion

        #region Private Fields
        private float attackStartTime;
        private float attackDuration = 1.5f;
        private bool hasDealtDamage;
        private float damageDelay = 0.5f; // Delay before dealing damage in attack animation
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize attack state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public AttackState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter attack state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            attackStartTime = Time.time;
            hasDealtDamage = false;
            
            // Stop movement during attack
            Blackboard.StopMovement();
            
            // Face target
            if (Blackboard.PlayerTransform != null)
            {
                LookAtTarget(Blackboard.PlayerTransform.position, 10f);
            }
            
            // Trigger attack animation
            Blackboard.TriggerAnimation(AIBlackboard.AttackHash);
            
            // Update last attack time
            Blackboard.LastAttackTime = Time.time;
            
            // Set combat state
            Blackboard.IsInCombat = true;
        }

        /// <summary>
        /// Update attack state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check common transitions first
            AIStateType? commonTransition = CheckCommonTransitions();
            if (commonTransition.HasValue)
            {
                return commonTransition;
            }

            float timeSinceAttackStart = Time.time - attackStartTime;

            // Deal damage at the right moment in animation
            if (!hasDealtDamage && timeSinceAttackStart >= damageDelay)
            {
                DealDamage();
                hasDealtDamage = true;
            }

            // Continue facing target during attack
            if (Blackboard.PlayerTransform != null)
            {
                LookAtTarget(Blackboard.PlayerTransform.position, 5f);
            }

            // Check if attack is complete
            if (timeSinceAttackStart >= attackDuration)
            {
                // Decide next action based on target position
                if (Blackboard.IsPlayerDetected)
                {
                    if (Blackboard.IsTargetInAttackRange() && Blackboard.CanAttack)
                    {
                        return AIStateType.Attack; // Attack again
                    }
                    else
                    {
                        return AIStateType.Chase; // Chase if target moved away
                    }
                }
                else
                {
                    return AIStateType.Investigate; // Investigate last known position
                }
            }

            return null; // Continue attacking
        }

        /// <summary>
        /// Exit attack state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Deal damage to the target.
        /// </summary>
        private void DealDamage()
        {
            if (Blackboard.PlayerTransform == null) return;

            // Check if target is still in range
            if (!Blackboard.IsTargetInAttackRange()) return;

            // Get player stats component
            PlayerStats playerStats = Blackboard.PlayerTransform.GetComponent<PlayerStats>();
            if (playerStats != null)
            {
                // Calculate damage (could be configurable)
                float damage = 25f;
                playerStats.TakeDamage(damage);
                
                Debug.Log($"AI dealt {damage} damage to player");
            }

            // Play attack sound effect
            // Note: AudioClip would be assigned in inspector or loaded from resources
            // Blackboard.PlayAudio(attackSoundClip);
        }
        #endregion
    }

    /// <summary>
    /// Flee state for AI enemies.
    /// AI runs away when health is low or overwhelmed.
    /// </summary>
    public class FleeState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Flee;
        #endregion

        #region Private Fields
        private Vector3 fleeTarget;
        private float fleeStartTime;
        private float maxFleeTime = 10f;
        private float fleeDistance = 15f;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize flee state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public FleeState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter flee state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            fleeStartTime = Time.time;
            
            // Set flee speed (faster than chase)
            Blackboard.SetAgentSpeed(Blackboard.ChaseSpeed * 1.2f);
            
            // Set flee animation
            Blackboard.SetAnimationBool(AIBlackboard.AlertHash, true);
            
            // Calculate flee target
            CalculateFleeTarget();
            
            // Exit combat mode
            Blackboard.IsInCombat = false;
        }

        /// <summary>
        /// Update flee state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check if dead
            if (Blackboard.IsDead)
            {
                return AIStateType.Dead;
            }

            // Check if stunned
            if (Blackboard.IsStunned)
            {
                return AIStateType.Stunned;
            }

            // Handle flee movement
            HandleFleeMovement();

            // Check if should stop fleeing
            if (ShouldStopFleeing())
            {
                return AIStateType.Idle;
            }

            // Update movement animation
            UpdateMovementAnimation();

            return null; // Continue fleeing
        }

        /// <summary>
        /// Exit flee state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Calculate where to flee to.
        /// </summary>
        private void CalculateFleeTarget()
        {
            if (Blackboard.Transform == null) return;

            Vector3 fleeDirection;
            
            if (Blackboard.PlayerTransform != null)
            {
                // Flee away from player
                fleeDirection = (Blackboard.Transform.position - Blackboard.PlayerTransform.position).normalized;
            }
            else
            {
                // Flee in random direction
                fleeDirection = Random.insideUnitSphere.normalized;
                fleeDirection.y = 0;
            }

            fleeTarget = Blackboard.Transform.position + fleeDirection * fleeDistance;

            // Sample NavMesh to ensure valid position
            if (UnityEngine.AI.NavMesh.SamplePosition(fleeTarget, out UnityEngine.AI.NavMeshHit hit, fleeDistance, UnityEngine.AI.NavMesh.AllAreas))
            {
                fleeTarget = hit.position;
            }

            Blackboard.SetDestination(fleeTarget);
        }

        /// <summary>
        /// Handle flee movement logic.
        /// </summary>
        private void HandleFleeMovement()
        {
            // If reached flee target, find new one
            if (Blackboard.HasReachedDestination())
            {
                CalculateFleeTarget();
            }

            // Keep looking away from player while fleeing
            if (Blackboard.PlayerTransform != null)
            {
                Vector3 awayFromPlayer = Blackboard.Transform.position - Blackboard.PlayerTransform.position;
                awayFromPlayer.y = 0;
                if (awayFromPlayer != Vector3.zero)
                {
                    LookAtTarget(Blackboard.Transform.position + awayFromPlayer, 3f);
                }
            }
        }

        /// <summary>
        /// Check if AI should stop fleeing.
        /// </summary>
        /// <returns>True if should stop fleeing</returns>
        private bool ShouldStopFleeing()
        {
            // Stop fleeing if health recovered above threshold
            if (Blackboard.HealthPercentage > Blackboard.FleeHealthThreshold + 0.1f)
            {
                return true;
            }

            // Stop fleeing if been fleeing for too long
            if (Time.time - fleeStartTime > maxFleeTime)
            {
                return true;
            }

            // Stop fleeing if far enough from player
            if (Blackboard.DistanceToTarget > Blackboard.DetectionRange * 2f)
            {
                return true;
            }

            return false;
        }
        #endregion
    }

    /// <summary>
    /// Dead state for AI enemies.
    /// AI is dead and inactive.
    /// </summary>
    public class DeadState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Dead;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize dead state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public DeadState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter dead state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            // Stop all movement
            Blackboard.StopMovement();
            
            // Disable NavMesh agent
            if (Blackboard.Agent != null)
            {
                Blackboard.Agent.enabled = false;
            }
            
            // Trigger death animation
            Blackboard.TriggerAnimation(AIBlackboard.DeathHash);
            
            // Exit combat mode
            Blackboard.IsInCombat = false;
            
            // Play death sound
            // Blackboard.PlayAudio(deathSoundClip);
        }

        /// <summary>
        /// Update dead state.
        /// </summary>
        /// <returns>Always null - dead enemies don't transition</returns>
        public override AIStateType? OnUpdate()
        {
            // Dead enemies don't transition to other states
            return null;
        }

        /// <summary>
        /// Exit dead state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
            // Dead enemies typically don't exit this state
        }
        #endregion
    }

    /// <summary>
    /// Stunned state for AI enemies.
    /// AI is temporarily incapacitated.
    /// </summary>
    public class StunnedState : BaseAIState
    {
        #region Properties
        public override AIStateType StateType => AIStateType.Stunned;
        #endregion

        #region Constructor
        /// <summary>
        /// Initialize stunned state.
        /// </summary>
        /// <param name="blackboard">AI blackboard reference</param>
        public StunnedState(AIBlackboard blackboard) : base(blackboard)
        {
        }
        #endregion

        #region State Implementation
        /// <summary>
        /// Enter stunned state.
        /// </summary>
        public override void OnEnter()
        {
            base.OnEnter();
            
            // Stop movement
            Blackboard.StopMovement();
            
            // Set stunned animation
            Blackboard.SetAnimationBool(AIBlackboard.StunnedHash, true);
            
            // Record stun start time
            Blackboard.StunStartTime = Time.time;
            Blackboard.IsStunned = true;
        }

        /// <summary>
        /// Update stunned state.
        /// </summary>
        /// <returns>Next state or null to continue</returns>
        public override AIStateType? OnUpdate()
        {
            // Check if dead
            if (Blackboard.IsDead)
            {
                return AIStateType.Dead;
            }

            // Check if stun duration is over
            if (Time.time - Blackboard.StunStartTime >= Blackboard.StunDuration)
            {
                Blackboard.IsStunned = false;
                
                // Return to appropriate state based on situation
                if (Blackboard.IsPlayerDetected)
                {
                    if (Blackboard.IsTargetInAttackRange())
                    {
                        return AIStateType.Attack;
                    }
                    else
                    {
                        return AIStateType.Chase;
                    }
                }
                else
                {
                    return AIStateType.Idle;
                }
            }

            return null; // Continue being stunned
        }

        /// <summary>
        /// Exit stunned state.
        /// </summary>
        public override void OnExit()
        {
            base.OnExit();
            
            // Clear stunned animation
            Blackboard.SetAnimationBool(AIBlackboard.StunnedHash, false);
            Blackboard.IsStunned = false;
        }
        #endregion
    }
}
