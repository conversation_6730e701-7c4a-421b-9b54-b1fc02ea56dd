using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace CinderOfDarkness.Combat
{
    /// <summary>
    /// Enhanced Legendary Weapons System for Cinder of Darkness.
    /// Features unique weapons with deep lore, special mechanics, and visual effects.
    /// </summary>
    public class EnhancedLegendaryWeapons : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Weapon Management")]
        [SerializeField] private LegendaryWeapon[] legendaryWeapons;
        [SerializeField] private Transform weaponDisplayArea;
        [SerializeField] private bool enableWeaponEvolution = true;

        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem weaponAuraEffect;
        [SerializeField] private Light weaponLight;
        [SerializeField] private Material[] weaponMaterials;

        [Header("Audio")]
        [SerializeField] private AudioSource weaponAudioSource;
        [SerializeField] private AudioClip weaponUnlockSound;
        [SerializeField] private AudioClip weaponEquipSound;
        [SerializeField] private AudioClip weaponEvolutionSound;
        #endregion

        #region Private Fields
        private Dictionary<string, LegendaryWeapon> weaponDatabase = new Dictionary<string, LegendaryWeapon>();
        private List<string> unlockedWeapons = new List<string>();
        private LegendaryWeapon currentlyEquipped;
        private PlayerCombat playerCombat;
        private WeaponLoreSystem loreSystem;
        #endregion

        #region Public Properties
        public static EnhancedLegendaryWeapons Instance { get; private set; }
        public LegendaryWeapon CurrentWeapon => currentlyEquipped;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeWeaponSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupLegendaryWeapons();
            LoadWeaponProgress();
        }
        #endregion

        #region Initialization
        private void InitializeWeaponSystem()
        {
            playerCombat = FindObjectOfType<PlayerCombat>();
            loreSystem = FindObjectOfType<WeaponLoreSystem>();

            if (weaponAudioSource == null)
                weaponAudioSource = gameObject.AddComponent<AudioSource>();
        }

        private void SetupLegendaryWeapons()
        {
            legendaryWeapons = new LegendaryWeapon[]
            {
                CreateSoulrenderBlade(),
                CreateEchoesOfSorrow(),
                CreateVoidwhisperDagger(),
                CreateHeartbreakerMace(),
                CreateMemoryThiefBow(),
                CreateDreamweaverStaff(),
                CreateBonecrusherHammer(),
                CreateShadowdancerRapier(),
                CreateFlameheartAxe(),
                CreateStormcallerSpear()
            };

            foreach (var weapon in legendaryWeapons)
            {
                weaponDatabase[weapon.weaponName] = weapon;
            }

            Debug.Log($"Initialized {legendaryWeapons.Length} legendary weapons");
        }

        private LegendaryWeapon CreateSoulrenderBlade()
        {
            return new LegendaryWeapon
            {
                weaponName = "Soulrender",
                culturalName = "روح القاطع", // Arabic: Soul Cutter
                weaponType = WeaponType.Greatsword,
                rarity = WeaponRarity.Mythic,

                // Lore and Background
                forgeHistory = "Forged in the depths of the Crying Mountains by the last of the Soul Smiths, using tears of the grieving and metal from fallen stars.",
                previousOwners = new string[]
                {
                    "Kael the Mourning King - Used it to end a war by cutting through the souls of hatred",
                    "Sister Meredith - A healer who wielded it to sever disease from souls",
                    "The Nameless Executioner - Who used it for mercy killings of the cursed"
                },
                legendaryDeeds = new string[]
                {
                    "Severed the connection between a lich and its phylactery",
                    "Cut through the bonds of a demon possession without harming the host",
                    "Ended a plague by cutting the spiritual corruption at its source"
                },
                curseOrBlessing = "Blessing: Can cut spiritual bonds and curses. Curse: Wielder feels the pain of every soul it touches.",

                // Combat Stats
                baseDamage = 85f,
                criticalChance = 0.25f,
                attackSpeed = 0.8f,
                reach = 2.5f,
                elementalAffinity = ElementalType.Spirit,

                // Special Abilities
                uniqueAbilities = new WeaponAbility[]
                {
                    new WeaponAbility
                    {
                        abilityName = "Soul Sever",
                        description = "Cuts through magical bonds and spiritual connections",
                        cooldown = 30f,
                        manaCost = 40f,
                        effectType = AbilityEffectType.Dispel
                    },
                    new WeaponAbility
                    {
                        abilityName = "Empathic Strike",
                        description = "Damage scales with target's emotional state",
                        cooldown = 0f,
                        manaCost = 0f,
                        effectType = AbilityEffectType.Passive
                    },
                    new WeaponAbility
                    {
                        abilityName = "Mercy's Edge",
                        description = "Can choose to heal instead of harm with each strike",
                        cooldown = 0f,
                        manaCost = 20f,
                        effectType = AbilityEffectType.Toggle
                    }
                },

                // Unlock Requirements
                unlockConditions = new UnlockCondition[]
                {
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.QuestCompletion,
                        requiredQuest = "The Crying Mountains Pilgrimage",
                        description = "Complete the pilgrimage to the Soul Smith's forge"
                    },
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.MoralAlignment,
                        requiredAlignment = MoralAlignment.Compassionate,
                        description = "Must have shown mercy to at least 10 enemies"
                    }
                },

                // Visual and Audio
                weaponModel = null, // Would be assigned in inspector
                weaponMaterial = null, // Would be assigned in inspector
                glowColor = new Color(0.7f, 0.9f, 1f, 0.8f), // Soft blue glow
                particleEffect = null, // Would be assigned in inspector
                equipSound = null, // Would be assigned in inspector
                attackSounds = new AudioClip[0], // Would be assigned in inspector

                // Evolution
                canEvolve = true,
                evolutionRequirements = new EvolutionRequirement[]
                {
                    new EvolutionRequirement
                    {
                        requirementType = EvolutionRequirement.RequirementType.SoulsHealed,
                        requiredAmount = 100,
                        description = "Heal 100 souls with Mercy's Edge"
                    }
                },
                evolvedForm = "Soulrender Ascendant"
            };
        }

        private LegendaryWeapon CreateEchoesOfSorrow()
        {
            return new LegendaryWeapon
            {
                weaponName = "Echoes of Sorrow",
                culturalName = "أصداء الحزن", // Arabic: Echoes of Sorrow
                weaponType = WeaponType.Harp,
                rarity = WeaponRarity.Legendary,

                forgeHistory = "Crafted from the heartwood of a tree that grew over a mass grave, its strings made from the hair of mourning mothers.",
                previousOwners = new string[]
                {
                    "Lyralei the Grief Singer - Used it to comfort the dying in plague times",
                    "The Orphan Bard - Who played it to remember the names of the forgotten dead",
                    "Silence the Mute - A warrior who spoke only through its melodies"
                },
                legendaryDeeds = new string[]
                {
                    "Its song once stopped a war by making all combatants weep for their losses",
                    "Played a lullaby that put an ancient dragon to eternal sleep",
                    "Its mournful tune guided lost souls to their final rest"
                },
                curseOrBlessing = "Blessing: Can manipulate emotions and memories through music. Curse: Wielder gradually loses the ability to feel joy.",

                baseDamage = 45f,
                criticalChance = 0.15f,
                attackSpeed = 1.2f,
                reach = 3f,
                elementalAffinity = ElementalType.Sound,

                uniqueAbilities = new WeaponAbility[]
                {
                    new WeaponAbility
                    {
                        abilityName = "Lament of the Lost",
                        description = "Area attack that causes enemies to become overwhelmed with grief",
                        cooldown = 45f,
                        manaCost = 60f,
                        effectType = AbilityEffectType.AreaDebuff
                    },
                    new WeaponAbility
                    {
                        abilityName = "Memory's Melody",
                        description = "Can replay the last moments of the dead",
                        cooldown = 120f,
                        manaCost = 80f,
                        effectType = AbilityEffectType.Divination
                    },
                    new WeaponAbility
                    {
                        abilityName = "Resonant Destruction",
                        description = "Sound waves that shatter armor and barriers",
                        cooldown = 20f,
                        manaCost = 30f,
                        effectType = AbilityEffectType.ArmorBreak
                    }
                },

                unlockConditions = new UnlockCondition[]
                {
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.LocationDiscovery,
                        requiredLocation = "The Weeping Grove",
                        description = "Discover the grove where the heartwood tree grows"
                    },
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.EmotionalResonance,
                        requiredEmotion = EmotionalState.Profound_Sorrow,
                        description = "Experience profound sorrow and channel it into understanding"
                    }
                },

                weaponModel = null,
                weaponMaterial = null,
                glowColor = new Color(0.6f, 0.4f, 0.8f, 0.7f), // Purple-blue glow
                particleEffect = null,
                equipSound = null,
                attackSounds = new AudioClip[0],

                canEvolve = true,
                evolutionRequirements = new EvolutionRequirement[]
                {
                    new EvolutionRequirement
                    {
                        requirementType = EvolutionRequirement.RequirementType.SongsPlayed,
                        requiredAmount = 1000,
                        description = "Play 1000 melodies of remembrance"
                    }
                },
                evolvedForm = "Symphony of Eternal Sorrow"
            };
        }

        private LegendaryWeapon CreateVoidwhisperDagger()
        {
            return new LegendaryWeapon
            {
                weaponName = "Voidwhisper",
                culturalName = "همسة الفراغ", // Arabic: Whisper of Void
                weaponType = WeaponType.Dagger,
                rarity = WeaponRarity.Mythic,

                forgeHistory = "Forged in the space between worlds, where reality grows thin and whispers of the void can be heard.",
                previousOwners = new string[]
                {
                    "The Void Walker - An assassin who could step between dimensions",
                    "Nihil the Philosopher - Who used it to cut away illusions and reveal truth",
                    "The Last Oracle - Who sacrificed herself to seal a breach in reality"
                },
                legendaryDeeds = new string[]
                {
                    "Cut a hole in reality to banish an elder god",
                    "Severed the connection between a tyrant king and his source of immortality",
                    "Used to perform the ritual that created the Barrier of Worlds"
                },
                curseOrBlessing = "Blessing: Can cut through any material or magical barrier. Curse: Each use risks opening small rifts to the void.",

                baseDamage = 95f,
                criticalChance = 0.35f,
                attackSpeed = 1.8f,
                reach = 1.2f,
                elementalAffinity = ElementalType.Void,

                uniqueAbilities = new WeaponAbility[]
                {
                    new WeaponAbility
                    {
                        abilityName = "Reality Rend",
                        description = "Cuts through any defense, ignoring armor and magical protection",
                        cooldown = 60f,
                        manaCost = 50f,
                        effectType = AbilityEffectType.TrueStrike
                    },
                    new WeaponAbility
                    {
                        abilityName = "Void Step",
                        description = "Briefly step into the void to teleport behind enemies",
                        cooldown = 30f,
                        manaCost = 40f,
                        effectType = AbilityEffectType.Teleport
                    },
                    new WeaponAbility
                    {
                        abilityName = "Whispers of Madness",
                        description = "Target hears void whispers, causing confusion and fear",
                        cooldown = 45f,
                        manaCost = 35f,
                        effectType = AbilityEffectType.MindEffect
                    }
                },

                unlockConditions = new UnlockCondition[]
                {
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.BossDefeat,
                        requiredBoss = "The Void Touched",
                        description = "Defeat the entity that guards the space between worlds"
                    },
                    new UnlockCondition
                    {
                        conditionType = UnlockCondition.ConditionType.SanityThreshold,
                        requiredSanity = 25f, // Low sanity required
                        description = "Must have gazed into the void and retained some sanity"
                    }
                ],

                weaponModel = null,
                weaponMaterial = null,
                glowColor = new Color(0.1f, 0.1f, 0.1f, 0.9f), // Dark, almost black glow
                particleEffect = null,
                equipSound = null,
                attackSounds = new AudioClip[0],

                canEvolve = true,
                evolutionRequirements = new EvolutionRequirement[]
                {
                    new EvolutionRequirement
                    {
                        requirementType = EvolutionRequirement.RequirementType.VoidRiftsOpened,
                        requiredAmount = 50,
                        description = "Open 50 rifts to the void (and survive)"
                    }
                },
                evolvedForm = "Voidwhisper Eternal"
            };
        }
        #endregion

        #region Weapon Management
        public bool UnlockWeapon(string weaponName)
        {
            if (weaponDatabase.ContainsKey(weaponName) && !unlockedWeapons.Contains(weaponName))
            {
                var weapon = weaponDatabase[weaponName];

                if (CanUnlockWeapon(weapon))
                {
                    unlockedWeapons.Add(weaponName);
                    PlayWeaponUnlockSequence(weapon);
                    SaveWeaponProgress();
                    return true;
                }
            }
            return false;
        }

        public bool EquipWeapon(string weaponName)
        {
            if (unlockedWeapons.Contains(weaponName) && weaponDatabase.ContainsKey(weaponName))
            {
                var weapon = weaponDatabase[weaponName];
                currentlyEquipped = weapon;

                ApplyWeaponEffects(weapon);
                PlayWeaponEquipSequence(weapon);

                return true;
            }
            return false;
        }

        public bool EvolveWeapon(string weaponName)
        {
            if (weaponDatabase.ContainsKey(weaponName))
            {
                var weapon = weaponDatabase[weaponName];

                if (weapon.canEvolve && CanEvolveWeapon(weapon))
                {
                    var evolvedWeapon = CreateEvolvedWeapon(weapon);
                    weaponDatabase[evolvedWeapon.weaponName] = evolvedWeapon;
                    unlockedWeapons.Add(evolvedWeapon.weaponName);

                    PlayWeaponEvolutionSequence(weapon, evolvedWeapon);
                    SaveWeaponProgress();

                    return true;
                }
            }
            return false;
        }

        private bool CanUnlockWeapon(LegendaryWeapon weapon)
        {
            foreach (var condition in weapon.unlockConditions)
            {
                if (!IsConditionMet(condition))
                    return false;
            }
            return true;
        }

        private bool CanEvolveWeapon(LegendaryWeapon weapon)
        {
            foreach (var requirement in weapon.evolutionRequirements)
            {
                if (!IsEvolutionRequirementMet(requirement))
                    return false;
            }
            return true;
        }

        private bool IsConditionMet(UnlockCondition condition)
        {
            // Check if unlock condition is met
            switch (condition.conditionType)
            {
                case UnlockCondition.ConditionType.QuestCompletion:
                    var questSystem = FindObjectOfType<QuestSystem>();
                    return questSystem != null && questSystem.IsQuestCompleted(condition.requiredQuest);

                case UnlockCondition.ConditionType.BossDefeat:
                    // Check if boss has been defeated
                    return PlayerPrefs.GetInt($"Boss_{condition.requiredBoss}_Defeated", 0) == 1;

                case UnlockCondition.ConditionType.LocationDiscovery:
                    var explorationSystem = FindObjectOfType<WorldExplorationSystem>();
                    return explorationSystem != null && explorationSystem.IsLocationDiscovered(condition.requiredLocation);

                // Add more condition checks
                default:
                    return false;
            }
        }

        private bool IsEvolutionRequirementMet(EvolutionRequirement requirement)
        {
            // Check if evolution requirement is met
            switch (requirement.requirementType)
            {
                case EvolutionRequirement.RequirementType.SoulsHealed:
                    return PlayerPrefs.GetInt("SoulsHealed", 0) >= requirement.requiredAmount;

                case EvolutionRequirement.RequirementType.SongsPlayed:
                    return PlayerPrefs.GetInt("SongsPlayed", 0) >= requirement.requiredAmount;

                case EvolutionRequirement.RequirementType.VoidRiftsOpened:
                    return PlayerPrefs.GetInt("VoidRiftsOpened", 0) >= requirement.requiredAmount;

                // Add more requirement checks
                default:
                    return false;
            }
        }

        private void ApplyWeaponEffects(LegendaryWeapon weapon)
        {
            // Apply weapon effects to player combat system
            if (playerCombat != null)
            {
                playerCombat.SetWeaponStats(weapon.baseDamage, weapon.criticalChance, weapon.attackSpeed);
                playerCombat.SetWeaponReach(weapon.reach);
                playerCombat.SetElementalAffinity(weapon.elementalAffinity);
            }

            // Apply visual effects
            ApplyWeaponVisuals(weapon);
        }

        private void ApplyWeaponVisuals(LegendaryWeapon weapon)
        {
            if (weaponLight != null)
            {
                weaponLight.color = weapon.glowColor;
                weaponLight.enabled = true;
            }

            if (weaponAuraEffect != null)
            {
                var main = weaponAuraEffect.main;
                main.startColor = weapon.glowColor;
                weaponAuraEffect.Play();
            }
        }

        private void PlayWeaponUnlockSequence(LegendaryWeapon weapon)
        {
            StartCoroutine(WeaponUnlockSequence(weapon));
        }

        private void PlayWeaponEquipSequence(LegendaryWeapon weapon)
        {
            if (weaponEquipSound != null && weaponAudioSource != null)
            {
                weaponAudioSource.PlayOneShot(weaponEquipSound);
            }
        }

        private void PlayWeaponEvolutionSequence(LegendaryWeapon original, LegendaryWeapon evolved)
        {
            StartCoroutine(WeaponEvolutionSequence(original, evolved));
        }

        private IEnumerator WeaponUnlockSequence(LegendaryWeapon weapon)
        {
            // Play dramatic unlock sequence
            if (weaponUnlockSound != null && weaponAudioSource != null)
            {
                weaponAudioSource.PlayOneShot(weaponUnlockSound);
            }

            // Show weapon lore
            var gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"Legendary Weapon Unlocked: {weapon.weaponName}\n{weapon.forgeHistory}");
                yield return new WaitForSeconds(5f);
                gameUI.HideInteractionPrompt();
            }
        }

        private IEnumerator WeaponEvolutionSequence(LegendaryWeapon original, LegendaryWeapon evolved)
        {
            // Play evolution sequence
            if (weaponEvolutionSound != null && weaponAudioSource != null)
            {
                weaponAudioSource.PlayOneShot(weaponEvolutionSound);
            }

            // Show evolution effects
            yield return new WaitForSeconds(3f);

            var gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"{original.weaponName} has evolved into {evolved.weaponName}!");
                yield return new WaitForSeconds(3f);
                gameUI.HideInteractionPrompt();
            }
        }

        private LegendaryWeapon CreateEvolvedWeapon(LegendaryWeapon original)
        {
            // Create evolved version of weapon with enhanced stats
            var evolved = new LegendaryWeapon
            {
                weaponName = original.evolvedForm,
                culturalName = original.culturalName + " المتطور", // Add "evolved" in Arabic
                weaponType = original.weaponType,
                rarity = WeaponRarity.Transcendent,

                baseDamage = original.baseDamage * 1.5f,
                criticalChance = original.criticalChance * 1.3f,
                attackSpeed = original.attackSpeed * 1.2f,
                reach = original.reach * 1.1f,
                elementalAffinity = original.elementalAffinity,

                // Enhanced abilities
                uniqueAbilities = EnhanceAbilities(original.uniqueAbilities),

                // Visual upgrades
                glowColor = EnhanceGlowColor(original.glowColor),

                canEvolve = false // Evolved weapons don't evolve further
            };

            return evolved;
        }

        private WeaponAbility[] EnhanceAbilities(WeaponAbility[] originalAbilities)
        {
            var enhanced = new WeaponAbility[originalAbilities.Length];
            for (int i = 0; i < originalAbilities.Length; i++)
            {
                enhanced[i] = new WeaponAbility
                {
                    abilityName = originalAbilities[i].abilityName + " Enhanced",
                    description = originalAbilities[i].description + " (Enhanced effect)",
                    cooldown = originalAbilities[i].cooldown * 0.7f, // Reduced cooldown
                    manaCost = originalAbilities[i].manaCost * 0.8f, // Reduced mana cost
                    effectType = originalAbilities[i].effectType
                };
            }
            return enhanced;
        }

        private Color EnhanceGlowColor(Color original)
        {
            return new Color(original.r * 1.2f, original.g * 1.2f, original.b * 1.2f, original.a);
        }

        private void SaveWeaponProgress()
        {
            // Save unlocked weapons to PlayerPrefs
            string unlockedList = string.Join(",", unlockedWeapons);
            PlayerPrefs.SetString("UnlockedLegendaryWeapons", unlockedList);
            PlayerPrefs.Save();
        }

        private void LoadWeaponProgress()
        {
            // Load unlocked weapons from PlayerPrefs
            string unlockedList = PlayerPrefs.GetString("UnlockedLegendaryWeapons", "");
            if (!string.IsNullOrEmpty(unlockedList))
            {
                unlockedWeapons = new List<string>(unlockedList.Split(','));
            }
        }
        #endregion

        #region Public API
        public LegendaryWeapon GetWeaponByName(string name)
        {
            return weaponDatabase.ContainsKey(name) ? weaponDatabase[name] : null;
        }

        public List<string> GetUnlockedWeapons()
        {
            return new List<string>(unlockedWeapons);
        }

        public List<LegendaryWeapon> GetAllWeapons()
        {
            return new List<LegendaryWeapon>(legendaryWeapons);
        }

        public bool IsWeaponUnlocked(string weaponName)
        {
            return unlockedWeapons.Contains(weaponName);
        }
        #endregion
    }

    #region Data Structures
    [System.Serializable]
    public class LegendaryWeapon
    {
        [Header("Basic Information")]
        public string weaponName;
        public string culturalName;
        public WeaponType weaponType;
        public WeaponRarity rarity;

        [Header("Lore and History")]
        [TextArea(3, 5)]
        public string forgeHistory;
        public string[] previousOwners;
        public string[] legendaryDeeds;
        [TextArea(2, 3)]
        public string curseOrBlessing;

        [Header("Combat Statistics")]
        public float baseDamage;
        public float criticalChance;
        public float attackSpeed;
        public float reach;
        public ElementalType elementalAffinity;

        [Header("Special Abilities")]
        public WeaponAbility[] uniqueAbilities;

        [Header("Unlock Requirements")]
        public UnlockCondition[] unlockConditions;

        [Header("Visual and Audio")]
        public GameObject weaponModel;
        public Material weaponMaterial;
        public Color glowColor;
        public ParticleSystem particleEffect;
        public AudioClip equipSound;
        public AudioClip[] attackSounds;

        [Header("Evolution")]
        public bool canEvolve;
        public EvolutionRequirement[] evolutionRequirements;
        public string evolvedForm;
    }

    [System.Serializable]
    public class WeaponAbility
    {
        public string abilityName;
        [TextArea(2, 3)]
        public string description;
        public float cooldown;
        public float manaCost;
        public AbilityEffectType effectType;
        public float effectMagnitude;
        public float effectDuration;
    }

    [System.Serializable]
    public class UnlockCondition
    {
        public ConditionType conditionType;
        public string requiredQuest;
        public string requiredBoss;
        public string requiredLocation;
        public MoralAlignment requiredAlignment;
        public EmotionalState requiredEmotion;
        public float requiredSanity;
        [TextArea(1, 2)]
        public string description;

        public enum ConditionType
        {
            QuestCompletion,
            BossDefeat,
            LocationDiscovery,
            MoralAlignment,
            EmotionalResonance,
            SanityThreshold,
            ItemCollection,
            SkillMastery,
            TimeSpent,
            DeathCount
        }
    }

    [System.Serializable]
    public class EvolutionRequirement
    {
        public RequirementType requirementType;
        public int requiredAmount;
        [TextArea(1, 2)]
        public string description;

        public enum RequirementType
        {
            SoulsHealed,
            SongsPlayed,
            VoidRiftsOpened,
            EnemiesDefeated,
            DamageDealt,
            SpellsCast,
            SecretsDiscovered,
            AlliesProtected,
            TruthsRevealed,
            MemoriesRestored
        }
    }

    public enum WeaponType
    {
        Sword,
        Greatsword,
        Dagger,
        Mace,
        Hammer,
        Axe,
        Spear,
        Bow,
        Staff,
        Wand,
        Harp,
        Rapier,
        Scythe,
        Whip,
        Gauntlets
    }

    public enum WeaponRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary,
        Mythic,
        Transcendent,
        Artifact
    }

    public enum ElementalType
    {
        None,
        Fire,
        Water,
        Earth,
        Air,
        Light,
        Shadow,
        Spirit,
        Void,
        Sound,
        Time,
        Memory,
        Dream,
        Sorrow,
        Joy
    }

    public enum AbilityEffectType
    {
        Passive,
        Active,
        Toggle,
        Dispel,
        AreaDebuff,
        Divination,
        ArmorBreak,
        TrueStrike,
        Teleport,
        MindEffect,
        Healing,
        Summoning,
        Transformation,
        TimeManipulation
    }

    public enum MoralAlignment
    {
        Neutral,
        Compassionate,
        Ruthless,
        Wise,
        Chaotic,
        Lawful,
        Selfless,
        Selfish
    }

    public enum EmotionalState
    {
        Neutral,
        Joy,
        Sorrow,
        Anger,
        Fear,
        Love,
        Hatred,
        Hope,
        Despair,
        Profound_Sorrow,
        Transcendent_Joy,
        Righteous_Fury,
        Peaceful_Acceptance
    }
    #endregion
}
