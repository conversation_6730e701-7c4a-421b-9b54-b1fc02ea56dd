using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections;

public class AtmosphericSystem : MonoBehaviour
{
    [Header("Weather System")]
    public WeatherType currentWeather = WeatherType.Clear;
    public float weatherTransitionTime = 30f;
    public bool dynamicWeatherEnabled = true;
    public float weatherChangeChance = 0.1f;
    
    [Header("Fog System")]
    public bool fogEnabled = true;
    public Color fogColor = Color.gray;
    public float fogDensity = 0.02f;
    public float fogStart = 10f;
    public float fogEnd = 100f;
    
    [Header("Lighting")]
    public Light sunLight;
    public Light moonLight;
    public Gradient sunColorGradient;
    public Gradient moonColorGradient;
    public AnimationCurve lightIntensityCurve;
    
    [Header("Post-Processing")]
    public Volume postProcessVolume;
    public VolumeProfile clearWeatherProfile;
    public VolumeProfile stormyWeatherProfile;
    public VolumeProfile foggyWeatherProfile;
    public VolumeProfile bloodMoonProfile;
    
    [Header("Particle Effects")]
    public ParticleSystem rainParticles;
    public ParticleSystem snowParticles;
    public ParticleSystem fogParticles;
    public ParticleSystem dustParticles;
    public ParticleSystem ashParticles;
    
    [Header("Audio")]
    public AudioSource weatherAudioSource;
    public AudioClip[] rainSounds;
    public AudioClip[] windSounds;
    public AudioClip[] thunderSounds;
    public AudioClip[] ambientSounds;
    
    [Header("Sekiro-Style Effects")]
    public bool motionBlurEnabled = true;
    public bool depthOfFieldEnabled = true;
    public float cinematicIntensity = 1f;
    public Color atmosphericTint = Color.white;
    
    private float timeOfDay = 12f; // 0-24 hours
    private float dayDuration = 600f; // 10 minutes per day
    private WeatherType targetWeather;
    private float weatherTransitionProgress = 0f;
    private PlayerStats playerStats;
    
    public enum WeatherType
    {
        Clear,
        Cloudy,
        Rain,
        Storm,
        Snow,
        Fog,
        Dust,
        BloodMoon,
        Eclipse
    }
    
    void Start()
    {
        playerStats = FindObjectOfType<PlayerStats>();
        targetWeather = currentWeather;
        
        InitializeGraphicsSettings();
        SetupPostProcessing();
        InitializeWeatherEffects();
        
        // Subscribe to player events for atmospheric changes
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnPlayerPathChanged;
        }
        
        StartCoroutine(DayNightCycle());
        StartCoroutine(WeatherSystem());
    }
    
    void Update()
    {
        UpdateTimeOfDay();
        UpdateLighting();
        UpdateFog();
        UpdateWeatherTransition();
        UpdateAtmosphericEffects();
    }
    
    void InitializeGraphicsSettings()
    {
        // Enable high-quality rendering features
        QualitySettings.shadows = ShadowQuality.All;
        QualitySettings.shadowResolution = ShadowResolution.VeryHigh;
        QualitySettings.shadowDistance = 150f;
        QualitySettings.shadowCascades = 4;
        
        // Enable anti-aliasing
        QualitySettings.antiAliasing = 4;
        
        // Set texture quality
        QualitySettings.masterTextureLimit = 0;
        QualitySettings.anisotropicFiltering = AnisotropicFiltering.ForceEnable;
        
        // Enable real-time reflections
        QualitySettings.realtimeReflectionProbes = true;
        
        Debug.Log("Graphics settings initialized for Sekiro-quality visuals");
    }
    
    void SetupPostProcessing()
    {
        if (postProcessVolume == null)
        {
            // Create post-process volume if not assigned
            GameObject ppGO = new GameObject("Post Process Volume");
            postProcessVolume = ppGO.AddComponent<Volume>();
            postProcessVolume.isGlobal = true;
        }
        
        // Apply initial profile
        ApplyWeatherProfile(currentWeather);
    }
    
    void InitializeWeatherEffects()
    {
        // Initialize all particle systems
        if (rainParticles != null) rainParticles.Stop();
        if (snowParticles != null) snowParticles.Stop();
        if (fogParticles != null) fogParticles.Stop();
        if (dustParticles != null) dustParticles.Stop();
        if (ashParticles != null) ashParticles.Stop();
        
        // Setup fog
        RenderSettings.fog = fogEnabled;
        RenderSettings.fogColor = fogColor;
        RenderSettings.fogMode = FogMode.ExponentialSquared;
        RenderSettings.fogDensity = fogDensity;
    }
    
    IEnumerator DayNightCycle()
    {
        while (true)
        {
            yield return new WaitForSeconds(1f);
            
            timeOfDay += (24f / dayDuration);
            if (timeOfDay >= 24f)
            {
                timeOfDay = 0f;
            }
        }
    }
    
    IEnumerator WeatherSystem()
    {
        while (true)
        {
            yield return new WaitForSeconds(Random.Range(60f, 180f)); // 1-3 minutes
            
            if (dynamicWeatherEnabled && Random.Range(0f, 1f) < weatherChangeChance)
            {
                ChangeWeather();
            }
        }
    }
    
    void UpdateTimeOfDay()
    {
        // Update sun and moon positions
        if (sunLight != null)
        {
            float sunAngle = (timeOfDay - 6f) * 15f; // Sun rises at 6 AM
            sunLight.transform.rotation = Quaternion.Euler(sunAngle, 30f, 0f);
        }
        
        if (moonLight != null)
        {
            float moonAngle = (timeOfDay - 18f) * 15f; // Moon rises at 6 PM
            moonLight.transform.rotation = Quaternion.Euler(moonAngle, 210f, 0f);
        }
    }
    
    void UpdateLighting()
    {
        float dayProgress = timeOfDay / 24f;
        
        if (sunLight != null)
        {
            // Sun is active during day (6 AM to 6 PM)
            bool isDaytime = timeOfDay >= 6f && timeOfDay <= 18f;
            sunLight.enabled = isDaytime;
            
            if (isDaytime)
            {
                float dayTime = (timeOfDay - 6f) / 12f; // 0 to 1 for day
                sunLight.intensity = lightIntensityCurve.Evaluate(dayTime) * GetWeatherLightMultiplier();
                sunLight.color = sunColorGradient.Evaluate(dayTime);
            }
        }
        
        if (moonLight != null)
        {
            // Moon is active during night
            bool isNighttime = timeOfDay < 6f || timeOfDay > 18f;
            moonLight.enabled = isNighttime;
            
            if (isNighttime)
            {
                float nightTime = timeOfDay > 18f ? (timeOfDay - 18f) / 6f : (timeOfDay + 6f) / 6f;
                moonLight.intensity = lightIntensityCurve.Evaluate(nightTime) * 0.3f * GetWeatherLightMultiplier();
                moonLight.color = moonColorGradient.Evaluate(nightTime);
            }
        }
        
        // Update ambient lighting
        UpdateAmbientLighting();
    }
    
    void UpdateAmbientLighting()
    {
        bool isDaytime = timeOfDay >= 6f && timeOfDay <= 18f;
        
        if (isDaytime)
        {
            RenderSettings.ambientSkyColor = Color.Lerp(new Color(0.2f, 0.3f, 0.5f), new Color(0.5f, 0.7f, 1f), GetDayProgress());
            RenderSettings.ambientEquatorColor = Color.Lerp(new Color(0.3f, 0.3f, 0.4f), new Color(0.4f, 0.4f, 0.4f), GetDayProgress());
            RenderSettings.ambientGroundColor = Color.Lerp(new Color(0.1f, 0.1f, 0.2f), new Color(0.2f, 0.3f, 0.1f), GetDayProgress());
        }
        else
        {
            RenderSettings.ambientSkyColor = new Color(0.1f, 0.1f, 0.3f);
            RenderSettings.ambientEquatorColor = new Color(0.2f, 0.2f, 0.3f);
            RenderSettings.ambientGroundColor = new Color(0.05f, 0.05f, 0.1f);
        }
        
        // Apply weather tinting
        ApplyWeatherTinting();
    }
    
    float GetDayProgress()
    {
        if (timeOfDay >= 6f && timeOfDay <= 18f)
        {
            return (timeOfDay - 6f) / 12f;
        }
        return 0f;
    }
    
    float GetWeatherLightMultiplier()
    {
        switch (currentWeather)
        {
            case WeatherType.Storm: return 0.3f;
            case WeatherType.Rain: return 0.6f;
            case WeatherType.Fog: return 0.4f;
            case WeatherType.Cloudy: return 0.7f;
            case WeatherType.Dust: return 0.5f;
            case WeatherType.BloodMoon: return 0.8f;
            case WeatherType.Eclipse: return 0.1f;
            default: return 1f;
        }
    }
    
    void UpdateFog()
    {
        if (!fogEnabled) return;
        
        switch (currentWeather)
        {
            case WeatherType.Fog:
                RenderSettings.fogDensity = Mathf.Lerp(RenderSettings.fogDensity, 0.08f, Time.deltaTime);
                RenderSettings.fogColor = Color.Lerp(RenderSettings.fogColor, Color.gray, Time.deltaTime);
                break;
            case WeatherType.Rain:
            case WeatherType.Storm:
                RenderSettings.fogDensity = Mathf.Lerp(RenderSettings.fogDensity, 0.04f, Time.deltaTime);
                RenderSettings.fogColor = Color.Lerp(RenderSettings.fogColor, new Color(0.4f, 0.4f, 0.5f), Time.deltaTime);
                break;
            case WeatherType.BloodMoon:
                RenderSettings.fogDensity = Mathf.Lerp(RenderSettings.fogDensity, 0.03f, Time.deltaTime);
                RenderSettings.fogColor = Color.Lerp(RenderSettings.fogColor, new Color(0.6f, 0.2f, 0.2f), Time.deltaTime);
                break;
            default:
                RenderSettings.fogDensity = Mathf.Lerp(RenderSettings.fogDensity, fogDensity, Time.deltaTime);
                RenderSettings.fogColor = Color.Lerp(RenderSettings.fogColor, fogColor, Time.deltaTime);
                break;
        }
    }
    
    void UpdateWeatherTransition()
    {
        if (currentWeather != targetWeather)
        {
            weatherTransitionProgress += Time.deltaTime / weatherTransitionTime;
            
            if (weatherTransitionProgress >= 1f)
            {
                currentWeather = targetWeather;
                weatherTransitionProgress = 0f;
                ApplyWeatherEffects(currentWeather);
            }
        }
    }
    
    void UpdateAtmosphericEffects()
    {
        // Update particle effects based on weather
        UpdateWeatherParticles();
        
        // Update atmospheric tinting
        Camera.main.backgroundColor = Color.Lerp(Camera.main.backgroundColor, GetWeatherSkyColor(), Time.deltaTime * 0.5f);
    }
    
    void UpdateWeatherParticles()
    {
        switch (currentWeather)
        {
            case WeatherType.Rain:
                if (rainParticles != null && !rainParticles.isPlaying)
                {
                    rainParticles.Play();
                }
                break;
            case WeatherType.Snow:
                if (snowParticles != null && !snowParticles.isPlaying)
                {
                    snowParticles.Play();
                }
                break;
            case WeatherType.Fog:
                if (fogParticles != null && !fogParticles.isPlaying)
                {
                    fogParticles.Play();
                }
                break;
            case WeatherType.Dust:
                if (dustParticles != null && !dustParticles.isPlaying)
                {
                    dustParticles.Play();
                }
                break;
            default:
                StopAllWeatherParticles();
                break;
        }
    }
    
    void StopAllWeatherParticles()
    {
        if (rainParticles != null) rainParticles.Stop();
        if (snowParticles != null) snowParticles.Stop();
        if (fogParticles != null) fogParticles.Stop();
        if (dustParticles != null) dustParticles.Stop();
        if (ashParticles != null) ashParticles.Stop();
    }
    
    Color GetWeatherSkyColor()
    {
        switch (currentWeather)
        {
            case WeatherType.Clear: return new Color(0.5f, 0.7f, 1f);
            case WeatherType.Cloudy: return new Color(0.6f, 0.6f, 0.7f);
            case WeatherType.Rain: return new Color(0.4f, 0.4f, 0.5f);
            case WeatherType.Storm: return new Color(0.2f, 0.2f, 0.3f);
            case WeatherType.Snow: return new Color(0.8f, 0.8f, 0.9f);
            case WeatherType.Fog: return new Color(0.7f, 0.7f, 0.7f);
            case WeatherType.Dust: return new Color(0.8f, 0.7f, 0.5f);
            case WeatherType.BloodMoon: return new Color(0.6f, 0.2f, 0.2f);
            case WeatherType.Eclipse: return new Color(0.1f, 0.1f, 0.2f);
            default: return Color.white;
        }
    }
    
    void ChangeWeather()
    {
        WeatherType[] possibleWeathers = { WeatherType.Clear, WeatherType.Cloudy, WeatherType.Rain, WeatherType.Fog, WeatherType.Dust };
        
        // Special weather based on player's moral path
        if (playerStats != null)
        {
            PlayerStats.MoralPath currentPath = playerStats.GetCurrentPath();
            
            switch (currentPath)
            {
                case PlayerStats.MoralPath.Moon:
                    if (Random.Range(0f, 1f) < 0.3f)
                    {
                        targetWeather = WeatherType.BloodMoon;
                        return;
                    }
                    break;
                case PlayerStats.MoralPath.Eclipse:
                    if (Random.Range(0f, 1f) < 0.2f)
                    {
                        targetWeather = WeatherType.Eclipse;
                        return;
                    }
                    break;
            }
        }
        
        // Normal weather change
        WeatherType newWeather;
        do
        {
            newWeather = possibleWeathers[Random.Range(0, possibleWeathers.Length)];
        } while (newWeather == currentWeather);
        
        targetWeather = newWeather;
        Debug.Log($"Weather changing to: {targetWeather}");
    }
    
    void ApplyWeatherEffects(WeatherType weather)
    {
        ApplyWeatherProfile(weather);
        PlayWeatherAudio(weather);
        
        // Special effects for dramatic weather
        if (weather == WeatherType.BloodMoon || weather == WeatherType.Eclipse)
        {
            StartCoroutine(DramaticWeatherEffect());
        }
    }
    
    void ApplyWeatherProfile(WeatherType weather)
    {
        if (postProcessVolume == null) return;
        
        VolumeProfile targetProfile = clearWeatherProfile;
        
        switch (weather)
        {
            case WeatherType.Storm:
            case WeatherType.Rain:
                targetProfile = stormyWeatherProfile;
                break;
            case WeatherType.Fog:
                targetProfile = foggyWeatherProfile;
                break;
            case WeatherType.BloodMoon:
            case WeatherType.Eclipse:
                targetProfile = bloodMoonProfile;
                break;
        }
        
        if (targetProfile != null)
        {
            postProcessVolume.profile = targetProfile;
        }
    }
    
    void ApplyWeatherTinting()
    {
        Color tint = GetWeatherSkyColor();
        RenderSettings.ambientSkyColor = Color.Lerp(RenderSettings.ambientSkyColor, tint, Time.deltaTime * 0.5f);
    }
    
    void PlayWeatherAudio(WeatherType weather)
    {
        if (weatherAudioSource == null) return;
        
        AudioClip[] soundsToPlay = null;
        
        switch (weather)
        {
            case WeatherType.Rain:
            case WeatherType.Storm:
                soundsToPlay = rainSounds;
                break;
            case WeatherType.Dust:
            case WeatherType.Fog:
                soundsToPlay = windSounds;
                break;
        }
        
        if (soundsToPlay != null && soundsToPlay.Length > 0)
        {
            AudioClip clipToPlay = soundsToPlay[Random.Range(0, soundsToPlay.Length)];
            weatherAudioSource.clip = clipToPlay;
            weatherAudioSource.loop = true;
            weatherAudioSource.Play();
        }
        else
        {
            weatherAudioSource.Stop();
        }
    }
    
    IEnumerator DramaticWeatherEffect()
    {
        // Screen flash for dramatic weather
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            GameObject flashOverlay = new GameObject("WeatherFlash");
            Canvas canvas = flashOverlay.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 1000;
            
            UnityEngine.UI.Image flashImage = flashOverlay.AddComponent<UnityEngine.UI.Image>();
            flashImage.color = new Color(1f, 0f, 0f, 0f);
            
            // Flash effect
            float flashDuration = 2f;
            float elapsed = 0f;
            
            while (elapsed < flashDuration)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.Sin(elapsed / flashDuration * Mathf.PI) * 0.3f;
                flashImage.color = new Color(1f, 0f, 0f, alpha);
                yield return null;
            }
            
            Destroy(flashOverlay);
        }
    }
    
    void OnPlayerPathChanged(PlayerStats.MoralPath newPath)
    {
        // Trigger special weather based on player's moral choice
        switch (newPath)
        {
            case PlayerStats.MoralPath.Moon:
                if (Random.Range(0f, 1f) < 0.5f)
                {
                    targetWeather = WeatherType.BloodMoon;
                }
                break;
            case PlayerStats.MoralPath.Eclipse:
                if (Random.Range(0f, 1f) < 0.3f)
                {
                    targetWeather = WeatherType.Eclipse;
                }
                break;
            case PlayerStats.MoralPath.Sun:
                targetWeather = WeatherType.Clear;
                break;
        }
    }
    
    public void ForceWeather(WeatherType weather)
    {
        targetWeather = weather;
        weatherTransitionProgress = 0f;
    }
    
    public WeatherType GetCurrentWeather()
    {
        return currentWeather;
    }
    
    public float GetTimeOfDay()
    {
        return timeOfDay;
    }
    
    public bool IsNightTime()
    {
        return timeOfDay < 6f || timeOfDay > 18f;
    }
}
