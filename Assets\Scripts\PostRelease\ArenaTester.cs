using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using TMPro;

/// <summary>
/// Arena Tester for Cinder of Darkness
/// Handles testing of custom arenas in a temporary sandbox environment
/// </summary>
public class ArenaTester : MonoBehaviour
{
    [Header("Testing UI")]
    public GameObject testingUI;
    public TextMeshProUGUI arenaNameText;
    public TextMeshProUGUI timerText;
    public TextMeshProUGUI objectiveText;
    public TextMeshProUGUI statusText;
    public Button exitTestButton;
    public Slider progressSlider;
    
    [Header("Testing Environment")]
    public Transform testingArea;
    public Camera testingCamera;
    public GameObject testingFloor;
    public LayerMask testingLayerMask = -1;
    
    [Header("Player Testing")]
    public GameObject testPlayerPrefab;
    public Transform playerSpawnPoint;
    
    // Static instance
    private static ArenaTester instance;
    public static ArenaTester Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaTester>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaTester");
                    instance = go.AddComponent<ArenaTester>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Testing state
    private ArenaData currentTestArena;
    private GameObject testPlayer;
    private List<GameObject> spawnedTestObjects = new List<GameObject>();
    private bool isTestingActive = false;
    private float testStartTime;
    private int enemiesKilled = 0;
    private int totalEnemies = 0;
    private TestResult currentTestResult;
    
    public enum TestStatus
    {
        NotStarted,
        InProgress,
        Victory,
        Defeat,
        Timeout,
        Cancelled
    }
    
    [System.Serializable]
    public class TestResult
    {
        public string arenaId;
        public string arenaName;
        public TestStatus status;
        public float completionTime;
        public int enemiesKilled;
        public int totalEnemies;
        public bool playerDied;
        public string failureReason;
        public Dictionary<string, object> statistics;
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeArenaTester();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
        
        if (testingUI != null)
            testingUI.SetActive(false);
    }
    
    void InitializeArenaTester()
    {
        Debug.Log("Arena Tester initialized");
    }
    
    void SetupUI()
    {
        if (exitTestButton != null)
            exitTestButton.onClick.AddListener(ExitTest);
    }
    
    // Public API
    public static void TestArena(ArenaData arena)
    {
        Instance.TestArenaInternal(arena);
    }
    
    public static void ExitTest()
    {
        Instance.ExitTestInternal();
    }
    
    public static bool IsTestingActive()
    {
        return Instance.isTestingActive;
    }
    
    public static TestResult GetLastTestResult()
    {
        return Instance.currentTestResult;
    }
    
    void TestArenaInternal(ArenaData arena)
    {
        if (arena == null)
        {
            Debug.LogError("Cannot test null arena");
            return;
        }
        
        if (isTestingActive)
        {
            Debug.LogWarning("Test already in progress");
            return;
        }
        
        currentTestArena = arena;
        StartArenaTest();
    }
    
    void StartArenaTest()
    {
        isTestingActive = true;
        testStartTime = Time.time;
        enemiesKilled = 0;
        totalEnemies = currentTestArena.enemySpawnPoints.Count;
        
        // Initialize test result
        currentTestResult = new TestResult
        {
            arenaId = currentTestArena.arenaId,
            arenaName = currentTestArena.arenaName,
            status = TestStatus.InProgress,
            statistics = new Dictionary<string, object>()
        };
        
        // Setup testing environment
        SetupTestingEnvironment();
        
        // Spawn test player
        SpawnTestPlayer();
        
        // Spawn arena objects
        SpawnArenaObjects();
        
        // Show testing UI
        ShowTestingUI();
        
        // Start test monitoring
        StartCoroutine(MonitorTest());
        
        Debug.Log($"Started testing arena: {currentTestArena.arenaName}");
    }
    
    void SetupTestingEnvironment()
    {
        // Clear existing test objects
        ClearTestObjects();
        
        // Setup testing area
        if (testingArea != null)
        {
            testingArea.position = Vector3.zero;
            testingArea.rotation = Quaternion.identity;
        }
        
        // Create arena floor
        CreateTestingFloor();
        
        // Setup camera
        if (testingCamera != null)
        {
            testingCamera.enabled = true;
            PositionTestingCamera();
        }
        
        // Apply arena environmental settings
        ApplyArenaEnvironment();
    }
    
    void CreateTestingFloor()
    {
        if (testingFloor != null)
        {
            DestroyImmediate(testingFloor);
        }
        
        testingFloor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        testingFloor.name = "TestingFloor";
        testingFloor.transform.SetParent(testingArea, false);
        testingFloor.transform.localScale = new Vector3(
            currentTestArena.arenaSize.x / 10f,
            1f,
            currentTestArena.arenaSize.z / 10f
        );
        testingFloor.transform.position = currentTestArena.playerSpawnPoint;
        
        // Set layer for raycasting
        testingFloor.layer = LayerMask.NameToLayer("Default");
    }
    
    void PositionTestingCamera()
    {
        if (testingCamera == null) return;
        
        Vector3 arenaCenter = currentTestArena.playerSpawnPoint;
        Vector3 cameraOffset = new Vector3(0, currentTestArena.arenaSize.y * 0.8f, -currentTestArena.arenaSize.z * 0.4f);
        
        testingCamera.transform.position = arenaCenter + cameraOffset;
        testingCamera.transform.LookAt(arenaCenter);
    }
    
    void ApplyArenaEnvironment()
    {
        // Apply fog settings
        if (currentTestArena.fogSettings.enableFog)
        {
            RenderSettings.fog = true;
            RenderSettings.fogColor = currentTestArena.fogSettings.fogColor;
            RenderSettings.fogMode = currentTestArena.fogSettings.fogMode;
            RenderSettings.fogDensity = currentTestArena.fogSettings.fogDensity;
        }
        
        // Apply lighting
        RenderSettings.ambientLight = currentTestArena.lightingSettings.ambientColor;
        
        var directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.color = currentTestArena.lightingSettings.directionalLightColor;
            directionalLight.intensity = currentTestArena.lightingSettings.directionalLightIntensity;
            directionalLight.transform.eulerAngles = currentTestArena.lightingSettings.directionalLightRotation;
        }
    }
    
    void SpawnTestPlayer()
    {
        if (testPlayerPrefab == null)
        {
            // Use existing player if available
            testPlayer = GameObject.FindGameObjectWithTag("Player");
            if (testPlayer != null)
            {
                testPlayer.transform.position = currentTestArena.playerSpawnPoint;
                testPlayer.transform.rotation = Quaternion.identity;
                return;
            }
        }
        
        // Spawn test player prefab
        if (testPlayerPrefab != null)
        {
            testPlayer = Instantiate(testPlayerPrefab, currentTestArena.playerSpawnPoint, Quaternion.identity, testingArea);
            spawnedTestObjects.Add(testPlayer);
        }
        
        // Setup player for testing
        if (testPlayer != null)
        {
            SetupTestPlayer();
        }
    }
    
    void SetupTestPlayer()
    {
        // Apply arena modifiers to player
        var playerStats = testPlayer.GetComponent<PlayerStats>();
        if (playerStats != null)
        {
            // Apply difficulty multiplier
            playerStats.maxHealth *= (2f - currentTestArena.difficultyMultiplier);
            playerStats.currentHealth = playerStats.maxHealth;
            
            // Apply arena restrictions
            if (!currentTestArena.allowMagic)
            {
                var magicSystem = testPlayer.GetComponent<ElementalMagicSystem>();
                if (magicSystem != null)
                    magicSystem.SetMagicEnabled(false);
            }
            
            if (!currentTestArena.allowHealing)
            {
                var healingSystem = testPlayer.GetComponent<HealingSystem>();
                if (healingSystem != null)
                    healingSystem.SetHealingEnabled(false);
            }
        }
        
        // Setup player death detection
        var healthSystem = testPlayer.GetComponent<HealthSystem>();
        if (healthSystem != null)
        {
            healthSystem.OnPlayerDeath += OnPlayerDeath;
        }
    }
    
    void SpawnArenaObjects()
    {
        // Spawn enemies
        foreach (var spawnPoint in currentTestArena.enemySpawnPoints)
        {
            SpawnTestEnemy(spawnPoint);
        }
        
        // Spawn destructible props
        foreach (var prop in currentTestArena.destructibleProps)
        {
            SpawnTestProp(prop, true);
        }
        
        // Spawn static props
        foreach (var prop in currentTestArena.staticProps)
        {
            SpawnTestProp(prop, false);
        }
        
        // Spawn traps
        foreach (var trap in currentTestArena.traps)
        {
            SpawnTestTrap(trap);
        }
        
        // Spawn environmental triggers
        // Implementation would depend on environmental effect system
    }
    
    void SpawnTestEnemy(Vector3 position)
    {
        // Load random enemy prefab
        GameObject enemyPrefab = LoadRandomEnemyPrefab();
        if (enemyPrefab == null) return;
        
        GameObject enemy = Instantiate(enemyPrefab, position, Quaternion.identity, testingArea);
        spawnedTestObjects.Add(enemy);
        
        // Apply difficulty multiplier
        var enemyStats = enemy.GetComponent<EnemyStats>();
        if (enemyStats != null)
        {
            enemyStats.health *= currentTestArena.difficultyMultiplier;
            enemyStats.damage *= currentTestArena.difficultyMultiplier;
        }
        
        // Setup enemy death detection
        var healthSystem = enemy.GetComponent<HealthSystem>();
        if (healthSystem != null)
        {
            healthSystem.OnEnemyDeath += OnEnemyDeath;
        }
    }
    
    GameObject LoadRandomEnemyPrefab()
    {
        // Load enemy prefabs from Resources
        GameObject[] enemyPrefabs = Resources.LoadAll<GameObject>("Enemies");
        if (enemyPrefabs.Length > 0)
        {
            return enemyPrefabs[Random.Range(0, enemyPrefabs.Length)];
        }
        
        // Fallback: create simple enemy
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "TestEnemy";
        enemy.GetComponent<Renderer>().material.color = Color.red;
        return enemy;
    }
    
    void SpawnTestProp(ArenaData.PropPlacement prop, bool isDestructible)
    {
        // Load prop prefab or create simple prop
        GameObject propPrefab = LoadPropPrefab(prop.propName);
        if (propPrefab == null)
        {
            propPrefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
            propPrefab.name = prop.propName;
        }
        
        GameObject propObject = Instantiate(propPrefab, prop.position, Quaternion.Euler(prop.rotation), testingArea);
        propObject.transform.localScale = prop.scale;
        spawnedTestObjects.Add(propObject);
        
        if (isDestructible)
        {
            var destructible = propObject.GetComponent<DestructibleObject>();
            if (destructible == null)
            {
                destructible = propObject.AddComponent<DestructibleObject>();
            }
            destructible.maxHealth = prop.health;
            destructible.currentHealth = prop.health;
        }
    }
    
    GameObject LoadPropPrefab(string propName)
    {
        return Resources.Load<GameObject>($"Props/{propName}");
    }
    
    void SpawnTestTrap(ArenaData.TrapPlacement trap)
    {
        // Create simple trap object
        GameObject trapObject = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        trapObject.name = trap.trapType;
        trapObject.transform.position = trap.position;
        trapObject.transform.rotation = Quaternion.Euler(trap.rotation);
        trapObject.transform.SetParent(testingArea, true);
        trapObject.GetComponent<Renderer>().material.color = Color.orange;
        
        spawnedTestObjects.Add(trapObject);
        
        // Add trap functionality
        var trapTrigger = trapObject.GetComponent<TrapTrigger>();
        if (trapTrigger == null)
        {
            trapTrigger = trapObject.AddComponent<TrapTrigger>();
        }
        
        trapTrigger.damage = trap.damage;
        trapTrigger.triggerRadius = trap.triggerRadius;
        trapTrigger.cooldown = trap.cooldown;
        trapTrigger.isReusable = trap.isReusable;
        
        // Setup trigger collider
        var collider = trapObject.GetComponent<Collider>();
        if (collider != null)
        {
            collider.isTrigger = true;
        }
    }
    
    void ShowTestingUI()
    {
        if (testingUI != null)
            testingUI.SetActive(true);
        
        if (arenaNameText != null)
            arenaNameText.text = currentTestArena.arenaName;
        
        UpdateObjectiveText();
        UpdateStatusText("Test in progress...");
    }
    
    void UpdateObjectiveText()
    {
        if (objectiveText == null) return;
        
        string objective = "";
        switch (currentTestArena.winCondition)
        {
            case ArenaData.WinConditionType.KillAllEnemies:
                objective = $"Defeat all enemies ({enemiesKilled}/{totalEnemies})";
                break;
            case ArenaData.WinConditionType.SurviveTimeLimit:
                float remainingTime = currentTestArena.timeLimitSeconds - (Time.time - testStartTime);
                objective = $"Survive for {remainingTime:F1} seconds";
                break;
            case ArenaData.WinConditionType.ReachLocation:
                objective = "Reach the target location";
                break;
            default:
                objective = "Complete the arena challenge";
                break;
        }
        
        objectiveText.text = objective;
    }
    
    void UpdateStatusText(string status)
    {
        if (statusText != null)
            statusText.text = status;
    }
    
    void UpdateProgressSlider()
    {
        if (progressSlider == null) return;
        
        float progress = 0f;
        switch (currentTestArena.winCondition)
        {
            case ArenaData.WinConditionType.KillAllEnemies:
                progress = totalEnemies > 0 ? (float)enemiesKilled / totalEnemies : 0f;
                break;
            case ArenaData.WinConditionType.SurviveTimeLimit:
                float elapsedTime = Time.time - testStartTime;
                progress = Mathf.Clamp01(elapsedTime / currentTestArena.timeLimitSeconds);
                break;
        }
        
        progressSlider.value = progress;
    }
    
    IEnumerator MonitorTest()
    {
        while (isTestingActive)
        {
            UpdateTestUI();
            CheckWinConditions();
            CheckFailConditions();
            
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    void UpdateTestUI()
    {
        if (timerText != null)
        {
            float elapsedTime = Time.time - testStartTime;
            timerText.text = $"Time: {elapsedTime:F1}s";
        }
        
        UpdateObjectiveText();
        UpdateProgressSlider();
    }
    
    void CheckWinConditions()
    {
        switch (currentTestArena.winCondition)
        {
            case ArenaData.WinConditionType.KillAllEnemies:
                if (enemiesKilled >= totalEnemies)
                {
                    CompleteTest(TestStatus.Victory);
                }
                break;
            case ArenaData.WinConditionType.SurviveTimeLimit:
                float elapsedTime = Time.time - testStartTime;
                if (elapsedTime >= currentTestArena.timeLimitSeconds)
                {
                    CompleteTest(TestStatus.Victory);
                }
                break;
        }
    }
    
    void CheckFailConditions()
    {
        // Check timeout for non-survival modes
        if (currentTestArena.winCondition != ArenaData.WinConditionType.SurviveTimeLimit)
        {
            float elapsedTime = Time.time - testStartTime;
            if (currentTestArena.timeLimitSeconds > 0 && elapsedTime >= currentTestArena.timeLimitSeconds)
            {
                CompleteTest(TestStatus.Timeout);
            }
        }
    }
    
    void OnEnemyDeath()
    {
        enemiesKilled++;
        Debug.Log($"Enemy defeated! ({enemiesKilled}/{totalEnemies})");
    }
    
    void OnPlayerDeath()
    {
        CompleteTest(TestStatus.Defeat);
    }
    
    void CompleteTest(TestStatus status)
    {
        if (!isTestingActive) return;
        
        isTestingActive = false;
        
        // Update test result
        currentTestResult.status = status;
        currentTestResult.completionTime = Time.time - testStartTime;
        currentTestResult.enemiesKilled = enemiesKilled;
        currentTestResult.totalEnemies = totalEnemies;
        currentTestResult.playerDied = (status == TestStatus.Defeat);
        
        // Update UI
        string statusMessage = "";
        switch (status)
        {
            case TestStatus.Victory:
                statusMessage = "Arena Test Completed Successfully!";
                break;
            case TestStatus.Defeat:
                statusMessage = "Arena Test Failed - Player Defeated";
                break;
            case TestStatus.Timeout:
                statusMessage = "Arena Test Failed - Time Limit Exceeded";
                break;
        }
        
        UpdateStatusText(statusMessage);
        
        Debug.Log($"Arena test completed: {status} in {currentTestResult.completionTime:F2} seconds");
        
        // Auto-exit after a delay
        StartCoroutine(AutoExitAfterDelay(3f));
    }
    
    IEnumerator AutoExitAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        ExitTestInternal();
    }
    
    void ExitTestInternal()
    {
        if (isTestingActive)
        {
            CompleteTest(TestStatus.Cancelled);
        }
        
        // Clean up test environment
        CleanupTest();
        
        // Return to arena editor
        ReturnToArenaEditor();
        
        Debug.Log("Exited arena test");
    }
    
    void CleanupTest()
    {
        // Clear test objects
        ClearTestObjects();
        
        // Reset environment
        ResetEnvironment();
        
        // Hide testing UI
        if (testingUI != null)
            testingUI.SetActive(false);
        
        // Disable testing camera
        if (testingCamera != null)
            testingCamera.enabled = false;
    }
    
    void ClearTestObjects()
    {
        foreach (var obj in spawnedTestObjects)
        {
            if (obj != null)
                DestroyImmediate(obj);
        }
        spawnedTestObjects.Clear();
        
        if (testingFloor != null)
        {
            DestroyImmediate(testingFloor);
            testingFloor = null;
        }
    }
    
    void ResetEnvironment()
    {
        // Reset render settings
        RenderSettings.fog = false;
        RenderSettings.ambientLight = Color.gray;
        
        // Reset directional light
        var directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.color = Color.white;
            directionalLight.intensity = 1f;
            directionalLight.transform.eulerAngles = new Vector3(50f, -30f, 0f);
        }
    }
    
    void ReturnToArenaEditor()
    {
        // Re-enable arena editor
        var arenaEditor = ArenaEditorManager.Instance;
        if (arenaEditor != null)
        {
            arenaEditor.ExitEditingMode();
            arenaEditor.EnterEditingMode();
        }
    }
    
    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Handle game events during testing
    }
}
