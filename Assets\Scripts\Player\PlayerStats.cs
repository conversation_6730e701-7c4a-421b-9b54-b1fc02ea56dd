using System;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Manages player statistics including health, mana, stamina, and moral alignment.
/// Handles stat regeneration, moral path calculation, and UI updates.
/// </summary>
public class PlayerStats : MonoBehaviour
{
    #region Serialized Fields
    [Header("Core Stats")]
    [SerializeField] private float maxHealth = 100f;
    [SerializeField] private float maxMana = 50f;
    [SerializeField] private float maxStamina = 100f;

    [Header("Regeneration Rates")]
    [SerializeField] private float healthRegenRate = 1f;
    [SerializeField] private float manaRegenRate = 2f;
    [SerializeField] private float staminaRegenRate = 15f;

    [Header("Combat Stats")]
    [SerializeField] private float attackDamage = 25f;
    [SerializeField] private float magicDamage = 30f;
    [SerializeField] private float attackStaminaCost = 20f;
    [SerializeField] private float magicManaCost = 15f;

    [Header("Moral Alignment")]
    [SerializeField] private float sunAlignment;    // -100 to 100
    [SerializeField] private float moonAlignment;   // -100 to 100
    [SerializeField] private MoralPath currentPath = MoralPath.Neutral;

    [Header("Level and Experience")]
    [SerializeField] private int level = 1;
    [SerializeField] private float experience = 0f;
    [SerializeField] private float experienceToNextLevel = 100f;

    [Header("UI References")]
    [SerializeField] private Slider healthBar;
    [SerializeField] private Slider manaBar;
    [SerializeField] private Slider staminaBar;
    [SerializeField] private Text pathText;
    #endregion

    #region Private Fields
    // Current stat values
    private float currentHealth;
    private float currentMana;
    private float currentStamina;

    // Cached for performance
    private bool hasUIElements;
    #endregion

    #region Constants
    private const float AlignmentMin = -100f;
    private const float AlignmentMax = 100f;
    private const float PathThreshold = 50f;
    private const float EclipseThreshold = 30f;
    #endregion

    #region Events
    /// <summary>
    /// Invoked when the player dies.
    /// </summary>
    public event Action OnPlayerDeath;

    /// <summary>
    /// Invoked when the player's moral path changes.
    /// </summary>
    public event Action<MoralPath> OnPathChanged;

    /// <summary>
    /// Invoked when the player levels up.
    /// </summary>
    public event Action<int> OnLevelUp;
    #endregion

    #region Enums
    /// <summary>
    /// Represents the player's moral alignment path.
    /// </summary>
    public enum MoralPath
    {
        Neutral,
        Sun,      // Path of Light
        Moon,     // Path of Darkness
        Eclipse   // Path of Balance
    }
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize player stats and UI elements.
    /// </summary>
    private void Start()
    {
        InitializeStats();
        CacheUIElements();
        UpdateUI();
    }

    /// <summary>
    /// Update stats regeneration, moral path, and UI.
    /// </summary>
    private void Update()
    {
        RegenerateStats();
        UpdateMoralPath();

        if (hasUIElements)
        {
            UpdateUI();
        }
    }
    #endregion

    #region Initialization
    /// <summary>
    /// Initialize current stat values to maximum.
    /// </summary>
    private void InitializeStats()
    {
        currentHealth = maxHealth;
        currentMana = maxMana;
        currentStamina = maxStamina;
    }

    /// <summary>
    /// Cache UI element references for performance.
    /// </summary>
    private void CacheUIElements()
    {
        hasUIElements = healthBar != null || manaBar != null || staminaBar != null || pathText != null;
    }
    #endregion

    #region Stat Management
    /// <summary>
    /// Regenerate health, mana, and stamina over time.
    /// </summary>
    private void RegenerateStats()
    {
        float deltaTime = Time.deltaTime;

        // Regenerate stats if below maximum
        if (currentHealth < maxHealth)
        {
            currentHealth = Mathf.Min(maxHealth, currentHealth + healthRegenRate * deltaTime);
        }

        if (currentMana < maxMana)
        {
            currentMana = Mathf.Min(maxMana, currentMana + manaRegenRate * deltaTime);
        }

        if (currentStamina < maxStamina)
        {
            currentStamina = Mathf.Min(maxStamina, currentStamina + staminaRegenRate * deltaTime);
        }
    }

    /// <summary>
    /// Update moral path based on current alignment values.
    /// </summary>
    private void UpdateMoralPath()
    {
        MoralPath previousPath = currentPath;
        currentPath = CalculateMoralPath();

        // Trigger event if path changed
        if (previousPath != currentPath)
        {
            OnPathChanged?.Invoke(currentPath);
            Debug.Log($"Moral path changed to: {currentPath}");
        }
    }

    /// <summary>
    /// Calculate the current moral path based on alignment values.
    /// </summary>
    /// <returns>The calculated moral path</returns>
    private MoralPath CalculateMoralPath()
    {
        float sunAbs = Mathf.Abs(sunAlignment);
        float moonAbs = Mathf.Abs(moonAlignment);

        if (sunAbs > PathThreshold && sunAlignment > moonAlignment)
        {
            return MoralPath.Sun;
        }

        if (moonAbs > PathThreshold && moonAlignment > sunAlignment)
        {
            return MoralPath.Moon;
        }

        if (sunAbs > EclipseThreshold && moonAbs > EclipseThreshold)
        {
            return MoralPath.Eclipse;
        }

        return MoralPath.Neutral;
    }
    #endregion

    #region Health and Resource Management
    /// <summary>
    /// Apply damage to the player and handle death if health reaches zero.
    /// </summary>
    /// <param name="damage">Amount of damage to apply</param>
    public void TakeDamage(float damage)
    {
        currentHealth = Mathf.Max(0, currentHealth - damage);

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    /// <summary>
    /// Heal the player by the specified amount.
    /// </summary>
    /// <param name="amount">Amount of health to restore</param>
    public void Heal(float amount)
    {
        currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
    }

    /// <summary>
    /// Restore health by the specified amount (alias for Heal).
    /// </summary>
    /// <param name="amount">Amount of health to restore</param>
    public void RestoreHealth(float amount)
    {
        Heal(amount);
        Debug.Log($"Health restored by {amount}. Current: {currentHealth}/{maxHealth}");
    }

    /// <summary>
    /// Modify the maximum health value.
    /// </summary>
    /// <param name="amount">Amount to modify max health by (can be negative)</param>
    public void ModifyMaxHealth(float amount)
    {
        maxHealth = Mathf.Max(1f, maxHealth + amount); // Ensure max health never goes below 1

        // If current health exceeds new max, clamp it
        if (currentHealth > maxHealth)
        {
            currentHealth = maxHealth;
        }

        Debug.Log($"Max health modified by {amount}. New max: {maxHealth}");
    }

    /// <summary>
    /// Consume mana for magic casting.
    /// </summary>
    /// <param name="amount">Amount of mana to consume</param>
    public void ConsumeMana(float amount)
    {
        currentMana = Mathf.Max(0, currentMana - amount);
    }

    /// <summary>
    /// Consume stamina for actions.
    /// </summary>
    /// <param name="amount">Amount of stamina to consume</param>
    public void ConsumeStamina(float amount)
    {
        currentStamina = Mathf.Max(0, currentStamina - amount);
    }

    /// <summary>
    /// Check if the player has enough stamina to attack.
    /// </summary>
    /// <returns>True if player can attack</returns>
    public bool CanAttack() => currentStamina >= attackStaminaCost;

    /// <summary>
    /// Check if the player has enough mana to cast magic.
    /// </summary>
    /// <returns>True if player can cast magic</returns>
    public bool CanCastMagic() => currentMana >= magicManaCost;

    /// <summary>
    /// Handle player death and respawn logic.
    /// </summary>
    private void Die()
    {
        Debug.Log("Player has died!");
        OnPlayerDeath?.Invoke();

        // Prototype respawn logic - reset to spawn point with full health
        currentHealth = maxHealth;
        transform.position = Vector3.zero;
    }
    #endregion

    #region Moral Alignment
    /// <summary>
    /// Add moral choice values to the player's alignment.
    /// </summary>
    /// <param name="sunValue">Sun alignment change</param>
    /// <param name="moonValue">Moon alignment change</param>
    public void AddMoralChoice(float sunValue, float moonValue)
    {
        sunAlignment = Mathf.Clamp(sunAlignment + sunValue, AlignmentMin, AlignmentMax);
        moonAlignment = Mathf.Clamp(moonAlignment + moonValue, AlignmentMin, AlignmentMax);

        Debug.Log($"Moral choice made. Sun: {sunAlignment}, Moon: {moonAlignment}");
    }

    /// <summary>
    /// Add temporary stat bonuses to the player.
    /// </summary>
    /// <param name="bonusName">Name of the bonus for logging</param>
    /// <param name="healthBonus">Health bonus amount</param>
    /// <param name="manaBonus">Mana bonus amount</param>
    /// <param name="staminaBonus">Stamina bonus amount</param>
    public void AddTemporaryStatBonus(string bonusName, float healthBonus, float manaBonus, float staminaBonus)
    {
        maxHealth += healthBonus;
        maxMana += manaBonus;
        maxStamina += staminaBonus;

        Debug.Log($"Temporary stat bonus '{bonusName}' applied: +{healthBonus} health, +{manaBonus} mana, +{staminaBonus} stamina");
    }
    #endregion

    #region UI Management
    /// <summary>
    /// Update UI elements with current stat values.
    /// </summary>
    private void UpdateUI()
    {
        if (healthBar != null)
            healthBar.value = currentHealth / maxHealth;

        if (manaBar != null)
            manaBar.value = currentMana / maxMana;

        if (staminaBar != null)
            staminaBar.value = currentStamina / maxStamina;

        if (pathText != null)
            pathText.text = $"Path: {currentPath}";
    }
    #endregion

    #region Public API - Getters
    /// <summary>
    /// Get current health as a percentage (0-1).
    /// </summary>
    /// <returns>Health percentage</returns>
    public float GetHealthPercentage() => currentHealth / maxHealth;

    /// <summary>
    /// Get current mana as a percentage (0-1).
    /// </summary>
    /// <returns>Mana percentage</returns>
    public float GetManaPercentage() => currentMana / maxMana;

    /// <summary>
    /// Get current stamina as a percentage (0-1).
    /// </summary>
    /// <returns>Stamina percentage</returns>
    public float GetStaminaPercentage() => currentStamina / maxStamina;

    /// <summary>
    /// Get current health value.
    /// </summary>
    /// <returns>Current health</returns>
    public float GetCurrentHealth() => currentHealth;

    /// <summary>
    /// Get current mana value.
    /// </summary>
    /// <returns>Current mana</returns>
    public float GetCurrentMana() => currentMana;

    /// <summary>
    /// Get current stamina value.
    /// </summary>
    /// <returns>Current stamina</returns>
    public float GetCurrentStamina() => currentStamina;

    /// <summary>
    /// Get the player's current moral path.
    /// </summary>
    /// <returns>Current moral path</returns>
    public MoralPath GetCurrentPath() => currentPath;

    /// <summary>
    /// Get the player's sun alignment value.
    /// </summary>
    /// <returns>Sun alignment (-100 to 100)</returns>
    public float GetSunAlignment() => sunAlignment;

    /// <summary>
    /// Get the player's moon alignment value.
    /// </summary>
    /// <returns>Moon alignment (-100 to 100)</returns>
    public float GetMoonAlignment() => moonAlignment;

    /// <summary>
    /// Get maximum health value.
    /// </summary>
    /// <returns>Maximum health</returns>
    public float GetMaxHealth() => maxHealth;

    /// <summary>
    /// Get maximum mana value.
    /// </summary>
    /// <returns>Maximum mana</returns>
    public float GetMaxMana() => maxMana;

    /// <summary>
    /// Get maximum stamina value.
    /// </summary>
    /// <returns>Maximum stamina</returns>
    public float GetMaxStamina() => maxStamina;

    /// <summary>
    /// Get attack damage value.
    /// </summary>
    /// <returns>Attack damage</returns>
    public float GetAttackDamage() => attackDamage;

    /// <summary>
    /// Get current level.
    /// </summary>
    /// <returns>Current level</returns>
    public int Level => level;

    /// <summary>
    /// Get current experience.
    /// </summary>
    /// <returns>Current experience</returns>
    public float Experience => experience;

    /// <summary>
    /// Get current health.
    /// </summary>
    /// <returns>Current health</returns>
    public float CurrentHealth => currentHealth;

    /// <summary>
    /// Get current mana.
    /// </summary>
    /// <returns>Current mana</returns>
    public float CurrentMana => currentMana;

    /// <summary>
    /// Add experience and handle level up.
    /// </summary>
    /// <param name="amount">Amount of experience to add</param>
    public void AddExperience(float amount)
    {
        experience += amount;

        while (experience >= experienceToNextLevel)
        {
            LevelUp();
        }
    }

    /// <summary>
    /// Set level directly (for save/load).
    /// </summary>
    /// <param name="newLevel">New level</param>
    public void SetLevel(int newLevel)
    {
        level = newLevel;
        experienceToNextLevel = CalculateExperienceForLevel(level + 1);
    }

    /// <summary>
    /// Set experience directly (for save/load).
    /// </summary>
    /// <param name="newExperience">New experience</param>
    public void SetExperience(float newExperience)
    {
        experience = newExperience;
    }

    /// <summary>
    /// Set health directly (for save/load).
    /// </summary>
    /// <param name="health">New health value</param>
    public void SetHealth(float health)
    {
        currentHealth = Mathf.Clamp(health, 0f, maxHealth);
    }

    /// <summary>
    /// Set mana directly (for save/load).
    /// </summary>
    /// <param name="mana">New mana value</param>
    public void SetMana(float mana)
    {
        currentMana = Mathf.Clamp(mana, 0f, maxMana);
    }

    /// <summary>
    /// Handle level up logic.
    /// </summary>
    private void LevelUp()
    {
        experience -= experienceToNextLevel;
        level++;
        experienceToNextLevel = CalculateExperienceForLevel(level + 1);

        // Increase stats on level up
        maxHealth += 10f;
        maxMana += 5f;
        maxStamina += 5f;

        // Restore health and mana on level up
        currentHealth = maxHealth;
        currentMana = maxMana;

        OnLevelUp?.Invoke(level);
        Debug.Log($"Level up! New level: {level}");
    }

    /// <summary>
    /// Calculate experience required for a specific level.
    /// </summary>
    /// <param name="targetLevel">Target level</param>
    /// <returns>Experience required</returns>
    private float CalculateExperienceForLevel(int targetLevel)
    {
        return 100f * targetLevel * 1.2f; // Exponential growth
    }

    /// <summary>
    /// Get magic damage value.
    /// </summary>
    /// <returns>Magic damage</returns>
    public float GetMagicDamage() => magicDamage;

    /// <summary>
    /// Get health percentage (0-1).
    /// </summary>
    /// <returns>Health percentage</returns>
    public float GetHealthPercentage() => currentHealth / maxHealth;

    /// <summary>
    /// Get mana percentage (0-1).
    /// </summary>
    /// <returns>Mana percentage</returns>
    public float GetManaPercentage() => currentMana / maxMana;

    /// <summary>
    /// Get stamina percentage (0-1).
    /// </summary>
    /// <returns>Stamina percentage</returns>
    public float GetStaminaPercentage() => currentStamina / maxStamina;
    #endregion
}
