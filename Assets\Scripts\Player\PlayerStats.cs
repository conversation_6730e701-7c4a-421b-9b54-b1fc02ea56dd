using UnityEngine;
using UnityEngine.UI;

public class PlayerStats : MonoBehaviour
{
    [Header("Core Stats")]
    public float maxHealth = 100f;
    public float maxMana = 50f;
    public float maxStamina = 100f;

    [Header("Regeneration")]
    public float healthRegenRate = 1f;
    public float manaRegenRate = 2f;
    public float staminaRegenRate = 15f;

    [Header("Combat Stats")]
    public float attackDamage = 25f;
    public float magicDamage = 30f;
    public float attackStaminaCost = 20f;
    public float magicManaCost = 15f;

    [Header("Moral Alignment")]
    public float sunAlignment = 0f;    // -100 to 100
    public float moonAlignment = 0f;   // -100 to 100
    public MoralPath currentPath = MoralPath.Neutral;

    [Header("UI References")]
    public Slider healthBar;
    public Slider manaBar;
    public Slider staminaBar;
    public Text pathText;

    // Current values
    [HideInInspector] public float currentHealth;
    [HideInInspector] public float currentMana;
    [HideInInspector] public float currentStamina;

    // Events
    public System.Action OnPlayerDeath;
    public System.Action<MoralPath> OnPathChanged;

    public enum MoralPath
    {
        Neutral,
        Sun,      // Path of Light
        Moon,     // Path of Darkness
        Eclipse   // Path of Balance
    }

    void Start()
    {
        // Initialize stats
        currentHealth = maxHealth;
        currentMana = maxMana;
        currentStamina = maxStamina;

        UpdateUI();
    }

    void Update()
    {
        RegenerateStats();
        UpdateMoralPath();
        UpdateUI();
    }

    void RegenerateStats()
    {
        // Health regeneration (slower)
        if (currentHealth < maxHealth)
        {
            currentHealth = Mathf.Min(maxHealth, currentHealth + healthRegenRate * Time.deltaTime);
        }

        // Mana regeneration
        if (currentMana < maxMana)
        {
            currentMana = Mathf.Min(maxMana, currentMana + manaRegenRate * Time.deltaTime);
        }

        // Stamina regeneration (fastest)
        if (currentStamina < maxStamina)
        {
            currentStamina = Mathf.Min(maxStamina, currentStamina + staminaRegenRate * Time.deltaTime);
        }
    }

    void UpdateMoralPath()
    {
        MoralPath previousPath = currentPath;

        // Determine current path based on alignment values
        float sunAbs = Mathf.Abs(sunAlignment);
        float moonAbs = Mathf.Abs(moonAlignment);

        if (sunAbs > 50 && sunAlignment > moonAlignment)
        {
            currentPath = MoralPath.Sun;
        }
        else if (moonAbs > 50 && moonAlignment > sunAlignment)
        {
            currentPath = MoralPath.Moon;
        }
        else if (sunAbs > 30 && moonAbs > 30)
        {
            currentPath = MoralPath.Eclipse;
        }
        else
        {
            currentPath = MoralPath.Neutral;
        }

        // Trigger event if path changed
        if (previousPath != currentPath)
        {
            OnPathChanged?.Invoke(currentPath);
            Debug.Log($"Moral path changed to: {currentPath}");
        }
    }

    public void TakeDamage(float damage)
    {
        currentHealth = Mathf.Max(0, currentHealth - damage);

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    public void Heal(float amount)
    {
        currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
    }

    public void ConsumeMana(float amount)
    {
        currentMana = Mathf.Max(0, currentMana - amount);
    }

    public void ConsumeStamina(float amount)
    {
        currentStamina = Mathf.Max(0, currentStamina - amount);
    }

    public bool CanAttack()
    {
        return currentStamina >= attackStaminaCost;
    }

    public bool CanCastMagic()
    {
        return currentMana >= magicManaCost;
    }

    public void AddMoralChoice(float sunValue, float moonValue)
    {
        sunAlignment = Mathf.Clamp(sunAlignment + sunValue, -100f, 100f);
        moonAlignment = Mathf.Clamp(moonAlignment + moonValue, -100f, 100f);

        Debug.Log($"Moral choice made. Sun: {sunAlignment}, Moon: {moonAlignment}");
    }

    void Die()
    {
        Debug.Log("Player has died!");
        OnPlayerDeath?.Invoke();

        // Respawn logic or game over screen
        // For prototype, just respawn with full health
        currentHealth = maxHealth;
        transform.position = Vector3.zero; // Reset to spawn point
    }

    void UpdateUI()
    {
        if (healthBar != null)
            healthBar.value = currentHealth / maxHealth;

        if (manaBar != null)
            manaBar.value = currentMana / maxMana;

        if (staminaBar != null)
            staminaBar.value = currentStamina / maxStamina;

        if (pathText != null)
            pathText.text = $"Path: {currentPath}";
    }

    // Getters
    public float GetHealthPercentage() => currentHealth / maxHealth;
    public float GetManaPercentage() => currentMana / maxMana;
    public float GetStaminaPercentage() => currentStamina / maxStamina;
    public float GetCurrentHealth() => currentHealth;
    public float GetCurrentMana() => currentMana;
    public float GetCurrentStamina() => currentStamina;
    public MoralPath GetCurrentPath() => currentPath;
    public float GetSunAlignment() => sunAlignment;
    public float GetMoonAlignment() => moonAlignment;

    public void AddTemporaryStatBonus(string bonusName, float healthBonus, float manaBonus, float staminaBonus)
    {
        // Add temporary stat bonuses (simplified implementation)
        maxHealth += healthBonus;
        maxMana += manaBonus;
        maxStamina += staminaBonus;
        Debug.Log($"Temporary stat bonus '{bonusName}' applied: +{healthBonus} health, +{manaBonus} mana, +{staminaBonus} stamina");
    }

    public void RestoreHealth(float amount)
    {
        currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
        Debug.Log($"Health restored by {amount}. Current: {currentHealth}/{maxHealth}");
    }
}
