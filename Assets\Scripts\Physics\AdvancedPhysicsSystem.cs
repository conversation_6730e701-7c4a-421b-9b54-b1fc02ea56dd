using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class AdvancedPhysicsSystem : MonoBehaviour
{
    [Header("Cloth Physics")]
    public bool clothPhysicsEnabled = true;
    public Cloth[] characterClothes;
    public float clothStiffness = 0.8f;
    public float clothDamping = 0.5f;
    public Vector3 windForce = Vector3.zero;
    
    [Header("Hair Physics")]
    public bool hairPhysicsEnabled = true;
    public Transform[] hairBones;
    public float hairStiffness = 0.6f;
    public float hairGravity = 9.81f;
    public float hairDamping = 0.3f;
    
    [Header("Destruction System")]
    public bool destructionEnabled = true;
    public GameObject[] destructibleObjects;
    public float destructionForceThreshold = 50f;
    public GameObject debrisPrefab;
    public int maxDebrisCount = 100;
    
    [Header("Weapon Physics")]
    public bool weaponPhysicsEnabled = true;
    public float weaponWeight = 1f;
    public float weaponSwingForce = 10f;
    public AnimationCurve weaponWeightCurve;
    
    [Header("Ragdoll System")]
    public bool ragdollEnabled = true;
    public float ragdollActivationForce = 30f;
    public float ragdollRecoveryTime = 3f;
    public LayerMask ragdollLayers;
    
    [Header("Environmental Physics")]
    public WindZone[] windZones;
    public float globalWindStrength = 1f;
    public Vector3 globalWindDirection = Vector3.right;
    
    private List<GameObject> activeDebris = new List<GameObject>();
    private Dictionary<Transform, Vector3> hairBonePositions = new Dictionary<Transform, Vector3>();
    private Dictionary<Transform, Vector3> hairBoneVelocities = new Dictionary<Transform, Vector3>();
    private PlayerController playerController;
    private CharacterProgression characterProgression;
    
    void Start()
    {
        playerController = GetComponent<PlayerController>();
        characterProgression = GetComponent<CharacterProgression>();
        
        InitializeClothPhysics();
        InitializeHairPhysics();
        InitializeDestructionSystem();
        SetupWeaponPhysics();
        
        Debug.Log("Advanced Physics System initialized");
    }
    
    void Update()
    {
        if (clothPhysicsEnabled)
            UpdateClothPhysics();
        
        if (hairPhysicsEnabled)
            UpdateHairPhysics();
        
        if (weaponPhysicsEnabled)
            UpdateWeaponPhysics();
        
        UpdateEnvironmentalPhysics();
        CleanupDebris();
    }
    
    void FixedUpdate()
    {
        if (hairPhysicsEnabled)
            FixedUpdateHairPhysics();
    }
    
    void InitializeClothPhysics()
    {
        foreach (Cloth cloth in characterClothes)
        {
            if (cloth != null)
            {
                // Configure cloth properties
                cloth.stretchingStiffness = clothStiffness;
                cloth.bendingStiffness = clothStiffness * 0.5f;
                cloth.damping = clothDamping;
                
                // Set up cloth constraints
                ClothSkinningCoefficient[] coefficients = cloth.coefficients;
                for (int i = 0; i < coefficients.Length; i++)
                {
                    // Adjust coefficients based on cloth type
                    coefficients[i].maxDistance = 0.1f;
                    coefficients[i].collisionSphereDistance = 0.05f;
                }
                cloth.coefficients = coefficients;
                
                // Add wind influence
                cloth.externalAcceleration = windForce;
                cloth.randomAcceleration = windForce * 0.1f;
            }
        }
    }
    
    void InitializeHairPhysics()
    {
        foreach (Transform hairBone in hairBones)
        {
            if (hairBone != null)
            {
                hairBonePositions[hairBone] = hairBone.position;
                hairBoneVelocities[hairBone] = Vector3.zero;
                
                // Add hair bone physics component if not present
                if (hairBone.GetComponent<Rigidbody>() == null)
                {
                    Rigidbody hairRb = hairBone.gameObject.AddComponent<Rigidbody>();
                    hairRb.mass = 0.01f;
                    hairRb.drag = hairDamping;
                    hairRb.useGravity = true;
                    
                    // Add joint to parent if exists
                    if (hairBone.parent != null)
                    {
                        ConfigurableJoint joint = hairBone.gameObject.AddComponent<ConfigurableJoint>();
                        joint.connectedBody = hairBone.parent.GetComponent<Rigidbody>();
                        
                        // Configure joint limits
                        joint.xMotion = ConfigurableJointMotion.Limited;
                        joint.yMotion = ConfigurableJointMotion.Limited;
                        joint.zMotion = ConfigurableJointMotion.Limited;
                        
                        SoftJointLimit limit = new SoftJointLimit();
                        limit.limit = 30f; // 30 degree movement limit
                        joint.linearLimit = limit;
                        
                        // Set spring properties
                        JointDrive drive = new JointDrive();
                        drive.positionSpring = hairStiffness * 1000f;
                        drive.positionDamper = hairDamping * 100f;
                        joint.xDrive = drive;
                        joint.yDrive = drive;
                        joint.zDrive = drive;
                    }
                }
            }
        }
    }
    
    void InitializeDestructionSystem()
    {
        foreach (GameObject destructible in destructibleObjects)
        {
            if (destructible != null)
            {
                // Add destruction component if not present
                DestructibleObject destructionComponent = destructible.GetComponent<DestructibleObject>();
                if (destructionComponent == null)
                {
                    destructionComponent = destructible.AddComponent<DestructibleObject>();
                }
                
                destructionComponent.forceThreshold = destructionForceThreshold;
                destructionComponent.debrisPrefab = debrisPrefab;
                destructionComponent.physicsSystem = this;
            }
        }
    }
    
    void SetupWeaponPhysics()
    {
        if (playerController != null)
        {
            PlayerCombat playerCombat = playerController.GetComponent<PlayerCombat>();
            if (playerCombat != null)
            {
                // Subscribe to weapon events
                playerCombat.OnWeaponSwing += OnWeaponSwing;
                playerCombat.OnWeaponHit += OnWeaponHit;
            }
        }
    }
    
    void UpdateClothPhysics()
    {
        // Update wind effects on cloth
        Vector3 currentWind = CalculateCurrentWind();
        
        foreach (Cloth cloth in characterClothes)
        {
            if (cloth != null)
            {
                cloth.externalAcceleration = currentWind;
                
                // Add movement-based wind
                if (playerController != null)
                {
                    Vector3 movementWind = -playerController.GetComponent<Rigidbody>().velocity * 0.5f;
                    cloth.externalAcceleration += movementWind;
                }
            }
        }
    }
    
    void UpdateHairPhysics()
    {
        // Update hair based on character movement and wind
        Vector3 currentWind = CalculateCurrentWind();
        
        foreach (Transform hairBone in hairBones)
        {
            if (hairBone != null)
            {
                Rigidbody hairRb = hairBone.GetComponent<Rigidbody>();
                if (hairRb != null)
                {
                    // Apply wind force
                    hairRb.AddForce(currentWind, ForceMode.Acceleration);
                    
                    // Apply movement-based forces
                    if (playerController != null)
                    {
                        Vector3 movementForce = -playerController.GetComponent<Rigidbody>().velocity * 2f;
                        hairRb.AddForce(movementForce, ForceMode.Acceleration);
                    }
                    
                    // Limit velocity to prevent excessive movement
                    if (hairRb.velocity.magnitude > 10f)
                    {
                        hairRb.velocity = hairRb.velocity.normalized * 10f;
                    }
                }
            }
        }
    }
    
    void FixedUpdateHairPhysics()
    {
        // Additional hair physics calculations in FixedUpdate for stability
        foreach (Transform hairBone in hairBones)
        {
            if (hairBone != null && hairBonePositions.ContainsKey(hairBone))
            {
                Vector3 currentPos = hairBone.position;
                Vector3 previousPos = hairBonePositions[hairBone];
                Vector3 velocity = (currentPos - previousPos) / Time.fixedDeltaTime;
                
                hairBoneVelocities[hairBone] = velocity;
                hairBonePositions[hairBone] = currentPos;
            }
        }
    }
    
    void UpdateWeaponPhysics()
    {
        // Update weapon weight effects based on current weapon
        if (playerController != null)
        {
            PlayerCombat playerCombat = playerController.GetComponent<PlayerCombat>();
            if (playerCombat != null)
            {
                float currentWeaponWeight = GetWeaponWeight(playerCombat.GetCurrentWeapon());
                ApplyWeaponWeightEffects(currentWeaponWeight);
            }
        }
    }
    
    void UpdateEnvironmentalPhysics()
    {
        // Update global wind effects
        UpdateWindZones();
        
        // Apply physics to loose objects
        ApplyEnvironmentalForces();
    }
    
    Vector3 CalculateCurrentWind()
    {
        Vector3 totalWind = globalWindDirection * globalWindStrength;
        
        // Add wind zone influences
        foreach (WindZone windZone in windZones)
        {
            if (windZone != null && windZone.enabled)
            {
                float distance = Vector3.Distance(transform.position, windZone.transform.position);
                if (distance < windZone.radius)
                {
                    float influence = 1f - (distance / windZone.radius);
                    Vector3 windDirection = windZone.transform.forward;
                    totalWind += windDirection * windZone.windMain * influence;
                }
            }
        }
        
        // Add atmospheric effects
        AtmosphericSystem atmosphere = FindObjectOfType<AtmosphericSystem>();
        if (atmosphere != null)
        {
            switch (atmosphere.GetCurrentWeather())
            {
                case AtmosphericSystem.WeatherType.Storm:
                    totalWind += Random.insideUnitSphere * 5f;
                    break;
                case AtmosphericSystem.WeatherType.Rain:
                    totalWind += Vector3.down * 2f;
                    break;
                case AtmosphericSystem.WeatherType.Dust:
                    totalWind += Random.insideUnitSphere * 3f;
                    break;
            }
        }
        
        return totalWind;
    }
    
    void UpdateWindZones()
    {
        foreach (WindZone windZone in windZones)
        {
            if (windZone != null)
            {
                // Add some variation to wind
                windZone.windMain = globalWindStrength + Mathf.Sin(Time.time * 0.5f) * 0.5f;
                windZone.windTurbulence = globalWindStrength * 0.3f + Mathf.Sin(Time.time * 2f) * 0.2f;
            }
        }
    }
    
    void ApplyEnvironmentalForces()
    {
        // Apply forces to loose objects in the environment
        Rigidbody[] allRigidbodies = FindObjectsOfType<Rigidbody>();
        Vector3 currentWind = CalculateCurrentWind();
        
        foreach (Rigidbody rb in allRigidbodies)
        {
            if (rb != null && !rb.isKinematic && rb.mass < 10f) // Only affect light objects
            {
                // Apply wind force
                rb.AddForce(currentWind * rb.mass * 0.1f, ForceMode.Force);
            }
        }
    }
    
    float GetWeaponWeight(PlayerCombat.WeaponType weaponType)
    {
        switch (weaponType)
        {
            case PlayerCombat.WeaponType.LightSword:
                return 1f;
            case PlayerCombat.WeaponType.HeavyMace:
                return 3f;
            default:
                return 1f;
        }
    }
    
    void ApplyWeaponWeightEffects(float weight)
    {
        if (playerController != null)
        {
            // Adjust movement speed based on weapon weight
            float speedMultiplier = weaponWeightCurve.Evaluate(weight);
            // This would be applied to the player's movement system
        }
    }
    
    void OnWeaponSwing(Vector3 swingDirection, float force)
    {
        // Apply physics effects during weapon swing
        if (hairPhysicsEnabled)
        {
            foreach (Transform hairBone in hairBones)
            {
                if (hairBone != null)
                {
                    Rigidbody hairRb = hairBone.GetComponent<Rigidbody>();
                    if (hairRb != null)
                    {
                        Vector3 swingForce = swingDirection * force * 0.1f;
                        hairRb.AddForce(swingForce, ForceMode.Impulse);
                    }
                }
            }
        }
        
        // Apply cloth movement
        if (clothPhysicsEnabled)
        {
            foreach (Cloth cloth in characterClothes)
            {
                if (cloth != null)
                {
                    cloth.externalAcceleration += swingDirection * force * 0.05f;
                }
            }
        }
    }
    
    void OnWeaponHit(Vector3 hitPoint, float force, GameObject target)
    {
        // Apply destruction physics on weapon hit
        if (destructionEnabled)
        {
            DestructibleObject destructible = target.GetComponent<DestructibleObject>();
            if (destructible != null)
            {
                destructible.ApplyDamage(force, hitPoint);
            }
        }
        
        // Apply ragdoll physics if hitting character
        if (ragdollEnabled && force > ragdollActivationForce)
        {
            EnemyHealth enemyHealth = target.GetComponent<EnemyHealth>();
            if (enemyHealth != null && enemyHealth.GetHealthPercentage() < 0.3f)
            {
                ActivateRagdoll(target);
            }
        }
    }
    
    void ActivateRagdoll(GameObject target)
    {
        Animator targetAnimator = target.GetComponent<Animator>();
        if (targetAnimator != null)
        {
            targetAnimator.enabled = false;
        }
        
        Rigidbody[] rigidbodies = target.GetComponentsInChildren<Rigidbody>();
        foreach (Rigidbody rb in rigidbodies)
        {
            rb.isKinematic = false;
            rb.AddForce(Random.insideUnitSphere * 5f, ForceMode.Impulse);
        }
        
        // Schedule ragdoll recovery
        StartCoroutine(RecoverFromRagdoll(target, ragdollRecoveryTime));
    }
    
    IEnumerator RecoverFromRagdoll(GameObject target, float recoveryTime)
    {
        yield return new WaitForSeconds(recoveryTime);
        
        EnemyHealth enemyHealth = target.GetComponent<EnemyHealth>();
        if (enemyHealth != null && !enemyHealth.IsDead())
        {
            // Recover from ragdoll
            Animator targetAnimator = target.GetComponent<Animator>();
            if (targetAnimator != null)
            {
                targetAnimator.enabled = true;
            }
            
            Rigidbody[] rigidbodies = target.GetComponentsInChildren<Rigidbody>();
            foreach (Rigidbody rb in rigidbodies)
            {
                rb.isKinematic = true;
            }
        }
    }
    
    public void CreateDebris(Vector3 position, Vector3 force, int count = 5)
    {
        if (!destructionEnabled || debrisPrefab == null) return;
        
        for (int i = 0; i < count; i++)
        {
            GameObject debris = Instantiate(debrisPrefab, position + Random.insideUnitSphere * 0.5f, Random.rotation);
            
            Rigidbody debrisRb = debris.GetComponent<Rigidbody>();
            if (debrisRb != null)
            {
                Vector3 randomForce = force + Random.insideUnitSphere * 5f;
                debrisRb.AddForce(randomForce, ForceMode.Impulse);
                debrisRb.AddTorque(Random.insideUnitSphere * 10f, ForceMode.Impulse);
            }
            
            activeDebris.Add(debris);
            
            // Schedule cleanup
            Destroy(debris, Random.Range(10f, 30f));
        }
    }
    
    void CleanupDebris()
    {
        // Remove null references
        activeDebris.RemoveAll(debris => debris == null);
        
        // Limit debris count
        while (activeDebris.Count > maxDebrisCount)
        {
            GameObject oldestDebris = activeDebris[0];
            activeDebris.RemoveAt(0);
            if (oldestDebris != null)
            {
                Destroy(oldestDebris);
            }
        }
    }
    
    public void SetGlobalWind(Vector3 direction, float strength)
    {
        globalWindDirection = direction.normalized;
        globalWindStrength = strength;
    }
    
    public void AddWindZone(WindZone windZone)
    {
        List<WindZone> windList = new List<WindZone>(windZones);
        windList.Add(windZone);
        windZones = windList.ToArray();
    }
    
    public void SetClothStiffness(float stiffness)
    {
        clothStiffness = stiffness;
        foreach (Cloth cloth in characterClothes)
        {
            if (cloth != null)
            {
                cloth.stretchingStiffness = stiffness;
                cloth.bendingStiffness = stiffness * 0.5f;
            }
        }
    }
    
    public void SetHairStiffness(float stiffness)
    {
        hairStiffness = stiffness;
        foreach (Transform hairBone in hairBones)
        {
            if (hairBone != null)
            {
                ConfigurableJoint joint = hairBone.GetComponent<ConfigurableJoint>();
                if (joint != null)
                {
                    JointDrive drive = joint.xDrive;
                    drive.positionSpring = stiffness * 1000f;
                    joint.xDrive = drive;
                    joint.yDrive = drive;
                    joint.zDrive = drive;
                }
            }
        }
    }
}
