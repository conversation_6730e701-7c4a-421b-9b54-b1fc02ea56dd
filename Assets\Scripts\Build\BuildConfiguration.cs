using UnityEngine;

/// <summary>
/// Build configuration and deployment settings for Cinder of Darkness
/// Centralizes all build-related settings and platform configurations
/// </summary>
[CreateAssetMenu(fileName = "BuildConfiguration", menuName = "Cinder of Darkness/Build Configuration")]
public class BuildConfiguration : ScriptableObject
{
    [Header("Game Information")]
    public string gameName = "Cinder of Darkness";
    public string gameVersion = "1.0.0";
    public string companyName = "Augment Code";
    public string productName = "Cinder of Darkness";

    [Header("Build Settings")]
    public bool developmentBuild = false;
    public bool allowDebugging = false;
    public bool autoConnectProfiler = false;
    public bool deepProfilingSupport = false;
    public bool scriptDebugging = false;

    [Header("Platform Settings")]
    public bool buildForWindows = true;
    public bool buildForMac = false;
    public bool buildForLinux = false;
    public bool buildForConsoles = false;

    [Header("Quality Settings")]
    public int defaultQualityLevel = 2; // Medium
    public bool enableVSync = true;
    public int targetFrameRate = 60;
    public bool enableHDR = true;

    [Header("Audio Settings")]
    public bool enableAudioCompression = true;
    public AudioCompressionFormat audioCompressionFormat = AudioCompressionFormat.Vorbis;
    public float audioQuality = 0.7f;

    [Header("Graphics Settings")]
    public bool enableGraphicsJobs = true;
    public bool enableGPUSkinning = true;
    public bool enableInstancing = true;
    public bool enableBatching = true;

    [Header("Input Settings")]
    public bool enableNewInputSystem = true;
    public bool enableLegacyInputManager = false;
    public bool enableInputSystemBackend = true;

    [Header("Localization")]
    public bool enableLocalization = true;
    public string[] supportedLanguages = { "English", "Arabic" };
    public string defaultLanguage = "English";

    [Header("Steam Integration")]
    public bool enableSteamIntegration = false;
    public uint steamAppId = 0;
    public bool enableSteamAchievements = true;
    public bool enableSteamCloudSave = true;
    public bool enableSteamOverlay = true;

    [Header("Scene Configuration")]
    public string[] buildScenes = {
        "Assets/Scenes/MainMenu.unity",
        "Assets/Scenes/Realms/KingdomOfSouthernBolt.unity",
        "Assets/Scenes/Realms/ForestOfShadows.unity",
        "Assets/Scenes/Realms/Ashlands.unity",
        "Assets/Scenes/Realms/FrozenNorth.unity",
        "Assets/Scenes/Realms/CityOfTheSky.unity"
    };

    [Header("Asset Bundles")]
    public bool enableAssetBundles = false;
    public string assetBundleOutputPath = "AssetBundles";
    public bool compressAssetBundles = true;

    [Header("Security")]
    public bool enableCodeStripping = true;
    public bool enableManagedStripping = true;
    public bool obfuscateCode = false;
    public bool enableAntiCheat = false;

    [Header("Performance")]
    public bool enableMultithreading = true;
    public int workerThreadCount = 4;
    public bool enableJobSystem = true;
    public bool enableBurstCompiler = true;

    [Header("Debugging")]
    public bool includeDebugSymbols = false;
    public bool enableConsoleLogging = true;
    public bool enableFileLogging = false;
    public string logFilePath = "Logs/game.log";

    [Header("Distribution")]
    public string buildOutputPath = "Builds";
    public bool createInstaller = false;
    public bool signExecutable = false;
    public string certificatePath = "";

    // Build validation
    public bool ValidateBuildSettings()
    {
        bool isValid = true;

        // Check required scenes
        foreach (string scenePath in buildScenes)
        {
            if (!System.IO.File.Exists(scenePath))
            {
                #if UNITY_EDITOR
                Debug.LogError($"Build scene not found: {scenePath}");
                #endif
                isValid = false;
            }
        }

        // Check Steam settings
        if (enableSteamIntegration && steamAppId == 0)
        {
            #if UNITY_EDITOR
            Debug.LogError("Steam integration enabled but App ID not set!");
            #endif
            isValid = false;
        }

        // Check localization
        if (enableLocalization && supportedLanguages.Length == 0)
        {
            #if UNITY_EDITOR
            Debug.LogError("Localization enabled but no languages specified!");
            #endif
            isValid = false;
        }

        // Check output path
        if (string.IsNullOrEmpty(buildOutputPath))
        {
            #if UNITY_EDITOR
            Debug.LogError("Build output path not specified!");
            #endif
            isValid = false;
        }

        return isValid;
    }

    // Apply settings to Unity
    public void ApplyBuildSettings()
    {
        // Player settings
        PlayerSettings.productName = productName;
        PlayerSettings.companyName = companyName;
        PlayerSettings.bundleVersion = gameVersion;

        // Quality settings
        QualitySettings.SetQualityLevel(defaultQualityLevel);
        QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        Application.targetFrameRate = targetFrameRate;

        // Graphics settings
        PlayerSettings.graphicsJobs = enableGraphicsJobs;
        PlayerSettings.gpuSkinning = enableGPUSkinning;

        // Input settings
        #if UNITY_EDITOR
        var inputSettings = UnityEditor.EditorBuildSettings.scenes;
        #endif

        #if UNITY_EDITOR
        Debug.Log("Build settings applied successfully");
        #endif
    }

    // Get platform-specific settings
    public PlatformBuildSettings GetPlatformSettings(BuildTarget target)
    {
        PlatformBuildSettings settings = new PlatformBuildSettings();

        switch (target)
        {
            case BuildTarget.StandaloneWindows64:
                settings.enableDirectX12 = true;
                settings.enableVulkan = false;
                settings.enableOpenGL = false;
                settings.architecture = "x64";
                break;

            case BuildTarget.StandaloneOSX:
                settings.enableMetal = true;
                settings.enableOpenGL = false;
                settings.architecture = "Universal";
                break;

            case BuildTarget.StandaloneLinux64:
                settings.enableVulkan = true;
                settings.enableOpenGL = true;
                settings.architecture = "x64";
                break;
        }

        return settings;
    }

    // Generate build report
    public BuildReport GenerateBuildReport()
    {
        BuildReport report = new BuildReport();

        report.gameName = gameName;
        report.version = gameVersion;
        report.buildTime = System.DateTime.Now;
        report.isDevelopmentBuild = developmentBuild;
        report.targetPlatforms = GetTargetPlatforms();
        report.enabledFeatures = GetEnabledFeatures();
        report.sceneCount = buildScenes.Length;
        report.estimatedSize = EstimateBuildSize();

        return report;
    }

    string[] GetTargetPlatforms()
    {
        System.Collections.Generic.List<string> platforms = new System.Collections.Generic.List<string>();

        if (buildForWindows) platforms.Add("Windows");
        if (buildForMac) platforms.Add("macOS");
        if (buildForLinux) platforms.Add("Linux");
        if (buildForConsoles) platforms.Add("Consoles");

        return platforms.ToArray();
    }

    string[] GetEnabledFeatures()
    {
        System.Collections.Generic.List<string> features = new System.Collections.Generic.List<string>();

        if (enableSteamIntegration) features.Add("Steam Integration");
        if (enableLocalization) features.Add("Localization");
        if (enableNewInputSystem) features.Add("New Input System");
        if (enableAssetBundles) features.Add("Asset Bundles");
        if (enableAntiCheat) features.Add("Anti-Cheat");
        if (enableBurstCompiler) features.Add("Burst Compiler");

        return features.ToArray();
    }

    long EstimateBuildSize()
    {
        // Rough estimation based on assets and settings
        long baseSize = 500 * 1024 * 1024; // 500 MB base

        if (enableSteamIntegration) baseSize += 50 * 1024 * 1024; // +50 MB
        if (enableLocalization) baseSize += supportedLanguages.Length * 10 * 1024 * 1024; // +10 MB per language
        if (!enableCodeStripping) baseSize += 100 * 1024 * 1024; // +100 MB without stripping

        return baseSize;
    }
}

[System.Serializable]
public class PlatformBuildSettings
{
    public bool enableDirectX12;
    public bool enableVulkan;
    public bool enableMetal;
    public bool enableOpenGL;
    public string architecture;
    public bool enableIL2CPP;
    public string scriptingBackend;
}

[System.Serializable]
public class BuildReport
{
    public string gameName;
    public string version;
    public System.DateTime buildTime;
    public bool isDevelopmentBuild;
    public string[] targetPlatforms;
    public string[] enabledFeatures;
    public int sceneCount;
    public long estimatedSize;

    public override string ToString()
    {
        return $"Build Report for {gameName} v{version}\n" +
               $"Build Time: {buildTime}\n" +
               $"Development Build: {isDevelopmentBuild}\n" +
               $"Target Platforms: {string.Join(", ", targetPlatforms)}\n" +
               $"Enabled Features: {string.Join(", ", enabledFeatures)}\n" +
               $"Scene Count: {sceneCount}\n" +
               $"Estimated Size: {estimatedSize / (1024 * 1024)} MB";
    }
}

// Build target enumeration for cross-platform compatibility
public enum BuildTarget
{
    StandaloneWindows64,
    StandaloneOSX,
    StandaloneLinux64,
    PS4,
    PS5,
    XboxOne,
    XboxSeriesX,
    Switch
}
