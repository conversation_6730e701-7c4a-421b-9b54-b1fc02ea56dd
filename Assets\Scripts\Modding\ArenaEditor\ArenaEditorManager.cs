using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using TMPro;
using Newtonsoft.Json;

/// <summary>
/// Arena Editor Manager for Cinder of Darkness
/// Handles the creation, editing, and management of custom arenas
/// Integrates with the modding system for save/load functionality
/// </summary>
public class ArenaEditorManager : MonoBehaviour
{
    [Header("UI References")]
    public GameObject arenaEditorUI;
    public GameObject mainMenuPanel;
    public GameObject editorPanel;
    public GameObject previewPanel;

    [Header("Main Menu UI")]
    public Button createNewArenaButton;
    public Button loadExistingArenaButton;
    public Button saveArenaButton;
    public Button testArenaButton;
    public Button exitEditorButton;
    public TMP_Dropdown existingArenasDropdown;

    [Header("Editor UI")]
    public TMP_InputField arenaNameField;
    public TMP_InputField arenaDescriptionField;
    public TMP_Dropdown biomeDropdown;
    public TMP_Dropdown winConditionDropdown;
    public TMP_InputField timeLimitField;
    public Slider difficultySlider;
    public Toggle allowMagicToggle;
    public Toggle allowHealingToggle;

    [Header("Arena Editing")]
    public GameObject arenaEditingPanel;
    public Transform arenaEditingArea;
    public Camera arenaEditingCamera;
    public LayerMask arenaLayerMask = -1;
    public Material previewMaterial;
    public Material selectedMaterial;

    [Header("Object Placement")]
    public ArenaObjectPalette objectPalette;
    public Transform placementIndicator;
    public float gridSnapSize = 1f;
    public bool enableGridSnap = true;

    [Header("Preview Settings")]
    public Camera previewCamera;
    public Transform previewArea;
    public GameObject[] biomePrefabs;

    [Header("File Management")]
    public string arenasFolder = "Mods/Arenas";
    public string arenaFileExtension = ".arenamod";

    // Static instance
    private static ArenaEditorManager instance;
    public static ArenaEditorManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<ArenaEditorManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("ArenaEditorManager");
                    instance = go.AddComponent<ArenaEditorManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // Arena editor state
    private ArenaData currentArena;
    private GameObject currentPreview;
    private List<string> availableArenas = new List<string>();
    private bool isEditorOpen = false;
    private bool hasUnsavedChanges = false;

    // Editing state
    private bool isEditingMode = false;
    private PlacementMode currentPlacementMode = PlacementMode.None;
    private GameObject selectedObject;
    private List<GameObject> placedObjects = new List<GameObject>();
    private Vector3 lastMousePosition;
    private bool isDragging = false;

    // Editor modes
    private EditorMode currentMode = EditorMode.Overview;

    public enum EditorMode
    {
        Overview,
        Layout,
        Environment,
        Gameplay,
        Testing
    }

    public enum PlacementMode
    {
        None,
        EnemySpawn,
        DestructibleProp,
        StaticProp,
        Trap,
        EnvironmentalTrigger,
        PlayerSpawn
    }

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeArenaEditor();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        SetupUI();
        CreateArenasDirectory();
        RefreshAvailableArenas();
    }

    void InitializeArenaEditor()
    {
        Debug.Log("Arena Editor Manager initialized");
    }

    void SetupUI()
    {
        // Setup main menu buttons
        if (createNewArenaButton != null)
            createNewArenaButton.onClick.AddListener(CreateNewArena);

        if (loadExistingArenaButton != null)
            loadExistingArenaButton.onClick.AddListener(LoadExistingArena);

        if (saveArenaButton != null)
            saveArenaButton.onClick.AddListener(SaveCurrentArena);

        if (testArenaButton != null)
            testArenaButton.onClick.AddListener(TestCurrentArena);

        if (exitEditorButton != null)
            exitEditorButton.onClick.AddListener(ExitArenaEditor);

        // Setup editor UI
        if (arenaNameField != null)
            arenaNameField.onValueChanged.AddListener(OnArenaNameChanged);

        if (arenaDescriptionField != null)
            arenaDescriptionField.onValueChanged.AddListener(OnArenaDescriptionChanged);

        if (biomeDropdown != null)
        {
            SetupBiomeDropdown();
            biomeDropdown.onValueChanged.AddListener(OnBiomeChanged);
        }

        if (winConditionDropdown != null)
        {
            SetupWinConditionDropdown();
            winConditionDropdown.onValueChanged.AddListener(OnWinConditionChanged);
        }

        if (timeLimitField != null)
            timeLimitField.onValueChanged.AddListener(OnTimeLimitChanged);

        if (difficultySlider != null)
            difficultySlider.onValueChanged.AddListener(OnDifficultyChanged);

        if (allowMagicToggle != null)
            allowMagicToggle.onValueChanged.AddListener(OnAllowMagicChanged);

        if (allowHealingToggle != null)
            allowHealingToggle.onValueChanged.AddListener(OnAllowHealingChanged);

        // Initially hide editor UI
        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(false);
    }

    void SetupBiomeDropdown()
    {
        if (biomeDropdown == null) return;

        var biomeNames = System.Enum.GetNames(typeof(ArenaData.BiomeType)).ToList();
        biomeDropdown.ClearOptions();
        biomeDropdown.AddOptions(biomeNames);
    }

    void SetupWinConditionDropdown()
    {
        if (winConditionDropdown == null) return;

        var winConditionNames = System.Enum.GetNames(typeof(ArenaData.WinConditionType)).ToList();
        winConditionDropdown.ClearOptions();
        winConditionDropdown.AddOptions(winConditionNames);
    }

    void CreateArenasDirectory()
    {
        string fullPath = Path.Combine(Application.persistentDataPath, arenasFolder);

        if (!Directory.Exists(fullPath))
        {
            Directory.CreateDirectory(fullPath);
            Debug.Log($"Created arenas directory: {fullPath}");
        }
    }

    void RefreshAvailableArenas()
    {
        availableArenas.Clear();
        string fullPath = Path.Combine(Application.persistentDataPath, arenasFolder);

        if (Directory.Exists(fullPath))
        {
            string[] arenaFiles = Directory.GetFiles(fullPath, $"*{arenaFileExtension}");

            foreach (string file in arenaFiles)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                availableArenas.Add(fileName);
            }
        }

        // Update dropdown
        if (existingArenasDropdown != null)
        {
            existingArenasDropdown.ClearOptions();
            existingArenasDropdown.AddOptions(availableArenas);
        }

        Debug.Log($"Found {availableArenas.Count} existing arenas");
    }

    // Public API
    public static bool IsArenaEditorOpen()
    {
        return Instance.isEditorOpen;
    }

    public static void OpenArenaEditor()
    {
        Instance.OpenArenaEditorInternal();
    }

    public static void CloseArenaEditor()
    {
        Instance.CloseArenaEditorInternal();
    }

    public static ArenaData GetCurrentArena()
    {
        return Instance.currentArena;
    }

    public static void LoadArenaForEditing(string arenaName)
    {
        Instance.LoadArenaForEditingInternal(arenaName);
    }

    void OpenArenaEditorInternal()
    {
        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(true);

        if (mainMenuPanel != null)
            mainMenuPanel.SetActive(true);

        if (editorPanel != null)
            editorPanel.SetActive(false);

        isEditorOpen = true;
        RefreshAvailableArenas();

        Debug.Log("Arena Editor opened");
    }

    void CloseArenaEditorInternal()
    {
        if (hasUnsavedChanges)
        {
            // Show save confirmation dialog
            if (ShowSaveConfirmationDialog())
            {
                SaveCurrentArena();
            }
        }

        if (arenaEditorUI != null)
            arenaEditorUI.SetActive(false);

        CleanupPreview();
        isEditorOpen = false;
        hasUnsavedChanges = false;

        Debug.Log("Arena Editor closed");
    }

    bool ShowSaveConfirmationDialog()
    {
        // In a full implementation, this would show a proper dialog
        // For now, return true to auto-save
        return true;
    }

    // Arena Management
    void CreateNewArena()
    {
        currentArena = ScriptableObject.CreateInstance<ArenaData>();
        currentArena.arenaName = "New Arena";
        currentArena.arenaId = System.Guid.NewGuid().ToString();
        currentArena.author = "Player";
        currentArena.ApplyBiomeDefaults();

        SwitchToEditorMode();
        UpdateUIFromArena();
        UpdatePreview();

        hasUnsavedChanges = false;
        Debug.Log("Created new arena");
    }

    void LoadExistingArena()
    {
        if (existingArenasDropdown == null || availableArenas.Count == 0) return;

        string selectedArena = availableArenas[existingArenasDropdown.value];
        LoadArenaForEditingInternal(selectedArena);
    }

    void LoadArenaForEditingInternal(string arenaName)
    {
        string filePath = Path.Combine(Application.persistentDataPath, arenasFolder, arenaName + arenaFileExtension);

        if (!File.Exists(filePath))
        {
            Debug.LogError($"Arena file not found: {filePath}");
            return;
        }

        try
        {
            string json = File.ReadAllText(filePath);
            var arenaJson = JsonConvert.DeserializeObject<ArenaDataJson>(json);

            currentArena = ConvertFromJson(arenaJson);

            SwitchToEditorMode();
            UpdateUIFromArena();
            UpdatePreview();

            hasUnsavedChanges = false;
            Debug.Log($"Loaded arena: {arenaName}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load arena {arenaName}: {e.Message}");
        }
    }

    void SaveCurrentArena()
    {
        if (currentArena == null)
        {
            Debug.LogWarning("No arena to save");
            return;
        }

        if (!currentArena.ValidateArenaData())
        {
            Debug.LogError("Arena validation failed");
            return;
        }

        string fileName = SanitizeFileName(currentArena.arenaName);
        string filePath = Path.Combine(Application.persistentDataPath, arenasFolder, fileName + arenaFileExtension);

        try
        {
            var arenaJson = ConvertToJson(currentArena);
            string json = JsonConvert.SerializeObject(arenaJson, Formatting.Indented);

            File.WriteAllText(filePath, json);

            hasUnsavedChanges = false;
            RefreshAvailableArenas();

            Debug.Log($"Arena saved: {fileName}");

            // Notify modding system
            var moddingSystem = ModdingSystem.Instance;
            if (moddingSystem != null)
            {
                moddingSystem.ScanForMods();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save arena: {e.Message}");
        }
    }

    void TestCurrentArena()
    {
        if (currentArena == null)
        {
            Debug.LogWarning("No arena to test");
            return;
        }

        if (!currentArena.ValidateArenaData())
        {
            Debug.LogError("Arena validation failed - cannot test");
            return;
        }

        // Save arena first
        SaveCurrentArena();

        // Exit editing mode
        ExitEditingMode();

        // Start arena test
        ArenaTester.TestArena(currentArena);

        Debug.Log($"Testing arena: {currentArena.arenaName}");
    }

    public void OnArenaTestCompleted(ArenaTester.TestResult result)
    {
        // Handle test completion
        Debug.Log($"Arena test completed: {result.status}");

        // Show test results
        ShowTestResults(result);

        // Return to editing mode
        EnterEditingMode();
    }

    void ShowTestResults(ArenaTester.TestResult result)
    {
        string message = $"Arena Test Results:\n";
        message += $"Status: {result.status}\n";
        message += $"Time: {result.completionTime:F2} seconds\n";
        message += $"Enemies Defeated: {result.enemiesKilled}/{result.totalEnemies}\n";

        if (result.playerDied)
        {
            message += "Player was defeated\n";
        }

        Debug.Log(message);

        // In a full implementation, this would show a proper results UI
    }

    void ExitArenaEditor()
    {
        CloseArenaEditorInternal();
    }

    void SwitchToEditorMode()
    {
        if (mainMenuPanel != null)
            mainMenuPanel.SetActive(false);

        if (editorPanel != null)
            editorPanel.SetActive(true);

        currentMode = EditorMode.Overview;
    }

    void UpdateUIFromArena()
    {
        if (currentArena == null) return;

        if (arenaNameField != null)
            arenaNameField.text = currentArena.arenaName;

        if (arenaDescriptionField != null)
            arenaDescriptionField.text = currentArena.description;

        if (biomeDropdown != null)
            biomeDropdown.value = (int)currentArena.biomeType;

        if (winConditionDropdown != null)
            winConditionDropdown.value = (int)currentArena.winCondition;

        if (timeLimitField != null)
            timeLimitField.text = currentArena.timeLimitSeconds.ToString();

        if (difficultySlider != null)
            difficultySlider.value = currentArena.difficultyMultiplier;

        if (allowMagicToggle != null)
            allowMagicToggle.isOn = currentArena.allowMagic;

        if (allowHealingToggle != null)
            allowHealingToggle.isOn = currentArena.allowHealing;
    }

    void UpdatePreview()
    {
        CleanupPreview();

        if (currentArena == null || previewArea == null) return;

        // Load biome prefab for preview
        GameObject biomePrefab = GetBiomePrefab(currentArena.biomeType);
        if (biomePrefab != null)
        {
            currentPreview = Instantiate(biomePrefab, previewArea.position, previewArea.rotation);

            // Apply arena settings to preview
            ApplyArenaSettingsToPreview();

            // Position preview camera
            if (previewCamera != null)
            {
                PositionPreviewCamera();
            }
        }
    }

    GameObject GetBiomePrefab(ArenaData.BiomeType biomeType)
    {
        if (biomePrefabs == null || biomePrefabs.Length == 0) return null;

        int index = (int)biomeType;
        if (index >= 0 && index < biomePrefabs.Length)
        {
            return biomePrefabs[index];
        }

        return null;
    }

    void ApplyArenaSettingsToPreview()
    {
        if (currentPreview == null || currentArena == null) return;

        // Apply fog settings
        if (currentArena.fogSettings.enableFog)
        {
            RenderSettings.fog = true;
            RenderSettings.fogColor = currentArena.fogSettings.fogColor;
            RenderSettings.fogMode = currentArena.fogSettings.fogMode;
            RenderSettings.fogDensity = currentArena.fogSettings.fogDensity;
        }

        // Apply lighting settings
        RenderSettings.ambientLight = currentArena.lightingSettings.ambientColor;

        var directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.color = currentArena.lightingSettings.directionalLightColor;
            directionalLight.intensity = currentArena.lightingSettings.directionalLightIntensity;
            directionalLight.transform.eulerAngles = currentArena.lightingSettings.directionalLightRotation;
        }
    }

    void PositionPreviewCamera()
    {
        if (previewCamera == null || currentArena == null) return;

        Vector3 arenaCenter = currentArena.playerSpawnPoint;
        Vector3 cameraOffset = new Vector3(0, currentArena.arenaSize.y * 0.5f, -currentArena.arenaSize.z * 0.3f);

        previewCamera.transform.position = arenaCenter + cameraOffset;
        previewCamera.transform.LookAt(arenaCenter);
    }

    void CleanupPreview()
    {
        if (currentPreview != null)
        {
            DestroyImmediate(currentPreview);
            currentPreview = null;
        }

        // Reset render settings
        RenderSettings.fog = false;
    }

    // UI Event Handlers
    void OnArenaNameChanged(string newName)
    {
        if (currentArena != null)
        {
            currentArena.arenaName = newName;
            hasUnsavedChanges = true;
        }
    }

    void OnArenaDescriptionChanged(string newDescription)
    {
        if (currentArena != null)
        {
            currentArena.description = newDescription;
            hasUnsavedChanges = true;
        }
    }

    void OnBiomeChanged(int biomeIndex)
    {
        if (currentArena != null)
        {
            currentArena.biomeType = (ArenaData.BiomeType)biomeIndex;
            currentArena.ApplyBiomeDefaults();
            UpdatePreview();
            hasUnsavedChanges = true;
        }
    }

    void OnWinConditionChanged(int conditionIndex)
    {
        if (currentArena != null)
        {
            currentArena.winCondition = (ArenaData.WinConditionType)conditionIndex;
            hasUnsavedChanges = true;
        }
    }

    void OnTimeLimitChanged(string newTimeLimit)
    {
        if (currentArena != null && float.TryParse(newTimeLimit, out float timeLimit))
        {
            currentArena.timeLimitSeconds = timeLimit;
            hasUnsavedChanges = true;
        }
    }

    void OnDifficultyChanged(float newDifficulty)
    {
        if (currentArena != null)
        {
            currentArena.difficultyMultiplier = newDifficulty;
            hasUnsavedChanges = true;
        }
    }

    void OnAllowMagicChanged(bool allowMagic)
    {
        if (currentArena != null)
        {
            currentArena.allowMagic = allowMagic;
            hasUnsavedChanges = true;
        }
    }

    void OnAllowHealingChanged(bool allowHealing)
    {
        if (currentArena != null)
        {
            currentArena.allowHealing = allowHealing;
            hasUnsavedChanges = true;
        }
    }

    // JSON Conversion (for file serialization)
    ArenaDataJson ConvertToJson(ArenaData arena)
    {
        return new ArenaDataJson
        {
            modId = arena.arenaId,
            name = arena.arenaName,
            author = arena.author,
            version = arena.version,
            description = arena.description,
            arenaData = arena
        };
    }

    ArenaData ConvertFromJson(ArenaDataJson arenaJson)
    {
        return arenaJson.arenaData;
    }

    string SanitizeFileName(string fileName)
    {
        string sanitized = fileName;
        char[] invalidChars = Path.GetInvalidFileNameChars();

        foreach (char c in invalidChars)
        {
            sanitized = sanitized.Replace(c, '_');
        }

        return sanitized;
    }

    // Integration with other systems
    public void OnGameEvent(string eventName, Dictionary<string, object> parameters = null)
    {
        // Handle game events if needed
    }

    void Update()
    {
        // Toggle Arena Editor with F4 (if unlocked)
        if (Input.GetKeyDown(KeyCode.F4))
        {
            if (ModdingSystem.IsToolUnlocked("arena_creator"))
            {
                if (IsArenaEditorOpen())
                    CloseArenaEditorInternal();
                else
                    OpenArenaEditorInternal();
            }
        }

        // Handle arena editing input
        if (isEditingMode)
        {
            HandleEditingInput();
        }
    }

    void HandleEditingInput()
    {
        if (arenaEditingCamera == null) return;

        // Handle mouse input for object placement
        if (Input.GetMouseButtonDown(0))
        {
            HandleMouseClick();
        }

        if (Input.GetMouseButton(0) && isDragging)
        {
            HandleMouseDrag();
        }

        if (Input.GetMouseButtonUp(0))
        {
            HandleMouseRelease();
        }

        // Handle keyboard shortcuts
        if (Input.GetKeyDown(KeyCode.Delete) && selectedObject != null)
        {
            DeleteSelectedObject();
        }

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            ClearSelection();
            SetPlacementMode(PlacementMode.None);
        }

        // Update placement indicator
        UpdatePlacementIndicator();
    }

    void HandleMouseClick()
    {
        Vector3 mousePosition = Input.mousePosition;
        Ray ray = arenaEditingCamera.ScreenPointToRay(mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, arenaLayerMask))
        {
            Vector3 hitPoint = hit.point;

            if (enableGridSnap)
            {
                hitPoint = SnapToGrid(hitPoint);
            }

            if (currentPlacementMode != PlacementMode.None)
            {
                PlaceObjectAtPosition(hitPoint);
            }
            else
            {
                // Try to select existing object
                SelectObjectAtPosition(hit.collider.gameObject);
            }
        }
    }

    void HandleMouseDrag()
    {
        if (selectedObject != null)
        {
            Vector3 mousePosition = Input.mousePosition;
            Ray ray = arenaEditingCamera.ScreenPointToRay(mousePosition);

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, arenaLayerMask))
            {
                Vector3 newPosition = hit.point;

                if (enableGridSnap)
                {
                    newPosition = SnapToGrid(newPosition);
                }

                selectedObject.transform.position = newPosition;
                hasUnsavedChanges = true;
            }
        }
    }

    void HandleMouseRelease()
    {
        isDragging = false;
    }

    Vector3 SnapToGrid(Vector3 position)
    {
        float snappedX = Mathf.Round(position.x / gridSnapSize) * gridSnapSize;
        float snappedZ = Mathf.Round(position.z / gridSnapSize) * gridSnapSize;
        return new Vector3(snappedX, position.y, snappedZ);
    }

    void UpdatePlacementIndicator()
    {
        if (placementIndicator == null || currentPlacementMode == PlacementMode.None) return;

        Vector3 mousePosition = Input.mousePosition;
        Ray ray = arenaEditingCamera.ScreenPointToRay(mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, arenaLayerMask))
        {
            Vector3 indicatorPosition = hit.point;

            if (enableGridSnap)
            {
                indicatorPosition = SnapToGrid(indicatorPosition);
            }

            placementIndicator.position = indicatorPosition;
            placementIndicator.gameObject.SetActive(true);
        }
        else
        {
            placementIndicator.gameObject.SetActive(false);
        }
    }

    void PlaceObjectAtPosition(Vector3 position)
    {
        GameObject prefab = GetPrefabForPlacementMode(currentPlacementMode);
        if (prefab == null) return;

        GameObject placedObject = Instantiate(prefab, position, Quaternion.identity, arenaEditingArea);
        placedObjects.Add(placedObject);

        // Add arena object component for identification
        var arenaObject = placedObject.GetComponent<ArenaObject>();
        if (arenaObject == null)
        {
            arenaObject = placedObject.AddComponent<ArenaObject>();
        }

        arenaObject.objectType = currentPlacementMode;
        arenaObject.arenaData = currentArena;

        // Update arena data
        UpdateArenaDataFromPlacement(placedObject, currentPlacementMode);

        hasUnsavedChanges = true;

        Debug.Log($"Placed {currentPlacementMode} at {position}");
    }

    GameObject GetPrefabForPlacementMode(PlacementMode mode)
    {
        if (objectPalette == null) return null;

        return objectPalette.GetPrefabForMode(mode);
    }

    void UpdateArenaDataFromPlacement(GameObject placedObject, PlacementMode mode)
    {
        if (currentArena == null) return;

        Vector3 position = placedObject.transform.position;

        switch (mode)
        {
            case PlacementMode.EnemySpawn:
                currentArena.enemySpawnPoints.Add(position);
                break;
            case PlacementMode.DestructibleProp:
                currentArena.destructibleProps.Add(new ArenaData.PropPlacement
                {
                    propId = System.Guid.NewGuid().ToString(),
                    propName = placedObject.name,
                    position = position,
                    rotation = placedObject.transform.eulerAngles,
                    scale = placedObject.transform.localScale,
                    isDestructible = true,
                    health = 100f
                });
                break;
            case PlacementMode.StaticProp:
                currentArena.staticProps.Add(new ArenaData.PropPlacement
                {
                    propId = System.Guid.NewGuid().ToString(),
                    propName = placedObject.name,
                    position = position,
                    rotation = placedObject.transform.eulerAngles,
                    scale = placedObject.transform.localScale,
                    isDestructible = false
                });
                break;
            case PlacementMode.Trap:
                currentArena.traps.Add(new ArenaData.TrapPlacement
                {
                    trapId = System.Guid.NewGuid().ToString(),
                    trapType = "basic_trap",
                    position = position,
                    rotation = placedObject.transform.eulerAngles,
                    triggerRadius = 2f,
                    damage = 50f,
                    cooldown = 5f,
                    isReusable = true
                });
                break;
            case PlacementMode.PlayerSpawn:
                currentArena.playerSpawnPoint = position;
                break;
        }
    }

    void SelectObjectAtPosition(GameObject obj)
    {
        ClearSelection();

        var arenaObject = obj.GetComponent<ArenaObject>();
        if (arenaObject != null)
        {
            selectedObject = obj;
            HighlightSelectedObject();
            isDragging = true;
        }
    }

    void ClearSelection()
    {
        if (selectedObject != null)
        {
            RemoveHighlight(selectedObject);
            selectedObject = null;
        }
    }

    void HighlightSelectedObject()
    {
        if (selectedObject == null || selectedMaterial == null) return;

        var renderer = selectedObject.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material = selectedMaterial;
        }
    }

    void RemoveHighlight(GameObject obj)
    {
        if (obj == null || previewMaterial == null) return;

        var renderer = obj.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material = previewMaterial;
        }
    }

    void DeleteSelectedObject()
    {
        if (selectedObject == null) return;

        var arenaObject = selectedObject.GetComponent<ArenaObject>();
        if (arenaObject != null)
        {
            RemoveFromArenaData(selectedObject, arenaObject.objectType);
        }

        placedObjects.Remove(selectedObject);
        DestroyImmediate(selectedObject);
        selectedObject = null;
        hasUnsavedChanges = true;
    }

    void RemoveFromArenaData(GameObject obj, PlacementMode mode)
    {
        if (currentArena == null) return;

        Vector3 position = obj.transform.position;

        switch (mode)
        {
            case PlacementMode.EnemySpawn:
                currentArena.enemySpawnPoints.RemoveAll(p => Vector3.Distance(p, position) < 0.1f);
                break;
            case PlacementMode.DestructibleProp:
                currentArena.destructibleProps.RemoveAll(p => Vector3.Distance(p.position, position) < 0.1f);
                break;
            case PlacementMode.StaticProp:
                currentArena.staticProps.RemoveAll(p => Vector3.Distance(p.position, position) < 0.1f);
                break;
            case PlacementMode.Trap:
                currentArena.traps.RemoveAll(t => Vector3.Distance(t.position, position) < 0.1f);
                break;
        }
    }

    // Public methods for UI
    public void SetPlacementMode(PlacementMode mode)
    {
        currentPlacementMode = mode;
        ClearSelection();

        if (placementIndicator != null)
        {
            placementIndicator.gameObject.SetActive(mode != PlacementMode.None);
        }

        Debug.Log($"Placement mode set to: {mode}");
    }

    public void EnterEditingMode()
    {
        isEditingMode = true;
        currentMode = EditorMode.Layout;

        if (arenaEditingPanel != null)
            arenaEditingPanel.SetActive(true);

        if (arenaEditingCamera != null)
            arenaEditingCamera.enabled = true;

        if (previewCamera != null)
            previewCamera.enabled = false;

        SetupEditingEnvironment();

        Debug.Log("Entered arena editing mode");
    }

    public void ExitEditingMode()
    {
        isEditingMode = false;
        ClearSelection();
        SetPlacementMode(PlacementMode.None);

        if (arenaEditingPanel != null)
            arenaEditingPanel.SetActive(false);

        if (arenaEditingCamera != null)
            arenaEditingCamera.enabled = false;

        if (previewCamera != null)
            previewCamera.enabled = true;

        Debug.Log("Exited arena editing mode");
    }

    void SetupEditingEnvironment()
    {
        if (currentArena == null) return;

        // Clear existing placed objects
        ClearPlacedObjects();

        // Create arena floor/bounds
        CreateArenaFloor();

        // Load existing objects from arena data
        LoadExistingObjects();
    }

    void ClearPlacedObjects()
    {
        foreach (var obj in placedObjects)
        {
            if (obj != null)
                DestroyImmediate(obj);
        }
        placedObjects.Clear();
    }

    void CreateArenaFloor()
    {
        if (arenaEditingArea == null) return;

        // Create a simple floor plane for the arena
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "ArenaFloor";
        floor.transform.SetParent(arenaEditingArea, false);
        floor.transform.localScale = new Vector3(currentArena.arenaSize.x / 10f, 1f, currentArena.arenaSize.z / 10f);
        floor.transform.position = currentArena.playerSpawnPoint;

        // Set up floor material
        var renderer = floor.GetComponent<Renderer>();
        if (renderer != null && previewMaterial != null)
        {
            renderer.material = previewMaterial;
        }

        // Add collider for raycasting
        var collider = floor.GetComponent<Collider>();
        if (collider != null)
        {
            collider.gameObject.layer = LayerMask.NameToLayer("Default");
        }
    }

    void LoadExistingObjects()
    {
        if (currentArena == null) return;

        // Load enemy spawn points
        foreach (var spawnPoint in currentArena.enemySpawnPoints)
        {
            CreateVisualMarker(spawnPoint, PlacementMode.EnemySpawn);
        }

        // Load destructible props
        foreach (var prop in currentArena.destructibleProps)
        {
            CreateVisualMarker(prop.position, PlacementMode.DestructibleProp);
        }

        // Load static props
        foreach (var prop in currentArena.staticProps)
        {
            CreateVisualMarker(prop.position, PlacementMode.StaticProp);
        }

        // Load traps
        foreach (var trap in currentArena.traps)
        {
            CreateVisualMarker(trap.position, PlacementMode.Trap);
        }

        // Load player spawn point
        if (currentArena.playerSpawnPoint != Vector3.zero)
        {
            CreateVisualMarker(currentArena.playerSpawnPoint, PlacementMode.PlayerSpawn);
        }
    }

    void CreateVisualMarker(Vector3 position, PlacementMode mode)
    {
        GameObject prefab = GetPrefabForPlacementMode(mode);
        if (prefab == null)
        {
            // Create a simple marker if no prefab is available
            prefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
            prefab.name = $"Marker_{mode}";
        }

        GameObject marker = Instantiate(prefab, position, Quaternion.identity, arenaEditingArea);
        placedObjects.Add(marker);

        var arenaObject = marker.GetComponent<ArenaObject>();
        if (arenaObject == null)
        {
            arenaObject = marker.AddComponent<ArenaObject>();
        }

        arenaObject.objectType = mode;
        arenaObject.arenaData = currentArena;

        // Apply preview material
        var renderer = marker.GetComponent<Renderer>();
        if (renderer != null && previewMaterial != null)
        {
            renderer.material = previewMaterial;
        }
    }

    // JSON serialization structure
    [System.Serializable]
    public class ArenaDataJson
    {
        public string modId;
        public string name;
        public string author;
        public string version;
        public string description;
        public ArenaData arenaData;
    }
}
