using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class MainMenu : MonoBehaviour
{
    [Header("UI Panels")]
    public GameObject mainMenuPanel;
    public GameObject settingsPanel;
    public GameObject creditsPanel;
    public GameObject loadGamePanel;
    
    [Header("Main Menu Buttons")]
    public Button newGameButton;
    public Button loadGameButton;
    public Button settingsButton;
    public Button creditsButton;
    public Button quitButton;
    
    [Head<PERSON>("Settings")]
    public Slider volumeSlider;
    public Slider mouseSensitivitySlider;
    public Dropdown qualityDropdown;
    public Dropdown resolutionDropdown;
    public Toggle fullscreenToggle;
    
    [Header("Audio")]
    public AudioSource menuMusic;
    public AudioClip buttonClickSound;
    
    [Header("Background")]
    public RawImage backgroundImage;
    public float backgroundScrollSpeed = 0.1f;
    
    private AudioSource audioSource;
    
    void Start()
    {
        InitializeMenu();
        LoadSettings();
        SetupAudio();
    }
    
    void Update()
    {
        AnimateBackground();
    }
    
    void InitializeMenu()
    {
        // Show main menu, hide others
        if (mainMenuPanel != null) mainMenuPanel.SetActive(true);
        if (settingsPanel != null) settingsPanel.SetActive(false);
        if (creditsPanel != null) creditsPanel.SetActive(false);
        if (loadGamePanel != null) loadGamePanel.SetActive(false);
        
        // Setup button listeners
        if (newGameButton != null) newGameButton.onClick.AddListener(StartNewGame);
        if (loadGameButton != null) loadGameButton.onClick.AddListener(ShowLoadGame);
        if (settingsButton != null) settingsButton.onClick.AddListener(ShowSettings);
        if (creditsButton != null) creditsButton.onClick.AddListener(ShowCredits);
        if (quitButton != null) quitButton.onClick.AddListener(QuitGame);
        
        // Setup settings listeners
        if (volumeSlider != null) volumeSlider.onValueChanged.AddListener(OnVolumeChanged);
        if (mouseSensitivitySlider != null) mouseSensitivitySlider.onValueChanged.AddListener(OnMouseSensitivityChanged);
        if (qualityDropdown != null) qualityDropdown.onValueChanged.AddListener(OnQualityChanged);
        if (resolutionDropdown != null) resolutionDropdown.onValueChanged.AddListener(OnResolutionChanged);
        if (fullscreenToggle != null) fullscreenToggle.onValueChanged.AddListener(OnFullscreenChanged);
        
        // Setup resolution dropdown
        SetupResolutionDropdown();
        
        // Enable/disable load game based on save existence
        if (loadGameButton != null)
        {
            loadGameButton.interactable = PlayerPrefs.HasKey("GameSave");
        }
    }
    
    void SetupAudio()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        if (menuMusic != null && menuMusic.clip != null)
        {
            menuMusic.Play();
        }
    }
    
    void LoadSettings()
    {
        // Load volume
        float volume = PlayerPrefs.GetFloat("Volume", 1f);
        if (volumeSlider != null) volumeSlider.value = volume;
        AudioListener.volume = volume;
        
        // Load mouse sensitivity
        float mouseSensitivity = PlayerPrefs.GetFloat("MouseSensitivity", 2f);
        if (mouseSensitivitySlider != null) mouseSensitivitySlider.value = mouseSensitivity;
        
        // Load quality
        int quality = PlayerPrefs.GetInt("Quality", QualitySettings.GetQualityLevel());
        if (qualityDropdown != null) qualityDropdown.value = quality;
        QualitySettings.SetQualityLevel(quality);
        
        // Load fullscreen
        bool fullscreen = PlayerPrefs.GetInt("Fullscreen", Screen.fullScreen ? 1 : 0) == 1;
        if (fullscreenToggle != null) fullscreenToggle.isOn = fullscreen;
        Screen.fullScreen = fullscreen;
    }
    
    void SetupResolutionDropdown()
    {
        if (resolutionDropdown == null) return;
        
        resolutionDropdown.ClearOptions();
        
        Resolution[] resolutions = Screen.resolutions;
        System.Collections.Generic.List<string> options = new System.Collections.Generic.List<string>();
        
        int currentResolutionIndex = 0;
        for (int i = 0; i < resolutions.Length; i++)
        {
            string option = resolutions[i].width + " x " + resolutions[i].height;
            options.Add(option);
            
            if (resolutions[i].width == Screen.currentResolution.width &&
                resolutions[i].height == Screen.currentResolution.height)
            {
                currentResolutionIndex = i;
            }
        }
        
        resolutionDropdown.AddOptions(options);
        resolutionDropdown.value = currentResolutionIndex;
        resolutionDropdown.RefreshShownValue();
    }
    
    void AnimateBackground()
    {
        if (backgroundImage != null)
        {
            Rect uvRect = backgroundImage.uvRect;
            uvRect.x += backgroundScrollSpeed * Time.deltaTime;
            if (uvRect.x >= 1f) uvRect.x = 0f;
            backgroundImage.uvRect = uvRect;
        }
    }
    
    void PlayButtonSound()
    {
        if (audioSource != null && buttonClickSound != null)
        {
            audioSource.PlayOneShot(buttonClickSound);
        }
    }
    
    // Button callbacks
    public void StartNewGame()
    {
        PlayButtonSound();
        Debug.Log("Starting new game...");
        
        // Clear any existing save
        PlayerPrefs.DeleteKey("GameSave");
        
        // Load game scene
        SceneManager.LoadScene("GameScene");
    }
    
    public void ShowLoadGame()
    {
        PlayButtonSound();
        
        if (PlayerPrefs.HasKey("GameSave"))
        {
            Debug.Log("Loading saved game...");
            SceneManager.LoadScene("GameScene");
        }
        else
        {
            Debug.Log("No save file found!");
        }
    }
    
    public void ShowSettings()
    {
        PlayButtonSound();
        
        if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
        if (settingsPanel != null) settingsPanel.SetActive(true);
    }
    
    public void ShowCredits()
    {
        PlayButtonSound();
        
        if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
        if (creditsPanel != null) creditsPanel.SetActive(true);
    }
    
    public void BackToMainMenu()
    {
        PlayButtonSound();
        
        if (mainMenuPanel != null) mainMenuPanel.SetActive(true);
        if (settingsPanel != null) settingsPanel.SetActive(false);
        if (creditsPanel != null) creditsPanel.SetActive(false);
        if (loadGamePanel != null) loadGamePanel.SetActive(false);
    }
    
    public void QuitGame()
    {
        PlayButtonSound();
        Debug.Log("Quitting game...");
        
        #if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
        #else
        Application.Quit();
        #endif
    }
    
    // Settings callbacks
    public void OnVolumeChanged(float value)
    {
        AudioListener.volume = value;
        PlayerPrefs.SetFloat("Volume", value);
    }
    
    public void OnMouseSensitivityChanged(float value)
    {
        PlayerPrefs.SetFloat("MouseSensitivity", value);
    }
    
    public void OnQualityChanged(int qualityIndex)
    {
        QualitySettings.SetQualityLevel(qualityIndex);
        PlayerPrefs.SetInt("Quality", qualityIndex);
    }
    
    public void OnResolutionChanged(int resolutionIndex)
    {
        Resolution[] resolutions = Screen.resolutions;
        if (resolutionIndex < resolutions.Length)
        {
            Resolution resolution = resolutions[resolutionIndex];
            Screen.SetResolution(resolution.width, resolution.height, Screen.fullScreen);
        }
    }
    
    public void OnFullscreenChanged(bool isFullscreen)
    {
        Screen.fullScreen = isFullscreen;
        PlayerPrefs.SetInt("Fullscreen", isFullscreen ? 1 : 0);
    }
}
