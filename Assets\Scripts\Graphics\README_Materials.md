# Visual Material System - Cinder of Darkness

## Overview
The Visual Material System provides URP-compatible materials for environmental and narrative effects in Cinder of Darkness. This system generates and manages 20 unique material assets across 5 categories to resolve all missing material references.

## Generated Materials

### 🔥 Fire Materials (4 total)
1. **SoftFlame** - Soft flame texture with ember emission for gentle fire effects
2. **EmberGlow** - Ember glow for weapon enchantments and magical effects
3. **TorchFlame** - Torch flame for environmental lighting and atmosphere
4. **WeaponEnchantment** - Magical weapon enchantment with purple-blue glow

### 💨 Ash Materials (4 total)
1. **FloatingAsh** - Floating ash particles with grayscale tone for ambient effects
2. **AshFog** - Semi-transparent ash fog surface for cursed zones
3. **CursedAsh** - Dark cursed ash for corrupted areas and dark magic
4. **DreamAsh** - Light dream state ash for reflective sequences

### 🩸 Blood Materials (4 total)
1. **FreshBlood** - Fresh blood with gloss and ripple effects for recent wounds
2. **DriedBlood** - Dried blood for old stains and weathered surfaces
3. **BloodStain** - Blood stain for clothes and environmental details
4. **Gore** - Gore material for intense combat scenes and boss arenas

### 👻 Spirit Materials (4 total)
1. **TranslucentSpirit** - Translucent shader with fade in/out for basic spirits
2. **SpectralGlow** - Light blue spectral edge glow for enhanced spirits
3. **GhostManifestation** - Ghost manifestation for hallucinations and regret moments
4. **DreamEcho** - Dream echo for memory sequences and past visions

### ⚔️ Combat FX Materials (5 total)
1. **SlashTrail** - Slash trails for weapon attacks and combat effects
2. **WeaponGlow** - Weapon glow for enhanced and magical weapons
3. **HitImpact** - Hit impact effects for successful attacks
4. **StrongAttackDistortion** - Strong attack distortion shader for powerful moves
5. **BlockEffect** - Block effect for defensive actions and parries

## Asset Organization
```
Assets/Materials/Effects/
├── Fire/
│   ├── SoftFlame.mat
│   ├── EmberGlow.mat
│   ├── TorchFlame.mat
│   └── WeaponEnchantment.mat
├── Ash/
│   ├── FloatingAsh.mat
│   ├── AshFog.mat
│   ├── CursedAsh.mat
│   └── DreamAsh.mat
├── Blood/
│   ├── FreshBlood.mat
│   ├── DriedBlood.mat
│   ├── BloodStain.mat
│   └── Gore.mat
├── Spirit/
│   ├── TranslucentSpirit.mat
│   ├── SpectralGlow.mat
│   ├── GhostManifestation.mat
│   └── DreamEcho.mat
├── CombatFX/
│   ├── SlashTrail.mat
│   ├── WeaponGlow.mat
│   ├── HitImpact.mat
│   ├── StrongAttackDistortion.mat
│   └── BlockEffect.mat
├── CinderMaterialAsset.asset
├── MaterialGenerator.cs
├── AutoGenerateMaterials.cs
└── README_Materials.md

Assets/Resources/
└── CinderMaterialAsset.asset (Runtime copy)
```

## Technical Specifications

### URP Compatibility
- **Shader**: Universal Render Pipeline/Lit shader
- **Fallback**: Standard shader for compatibility
- **Rendering**: Transparent blend mode for effects
- **Queue**: Render queue 3000 for proper transparency sorting

### Material Properties
- **Alpha Blending**: SrcAlpha/OneMinusSrcAlpha for proper transparency
- **Z-Write**: Disabled for transparent materials
- **Emission**: Enabled for glowing effects (fire, spirit, combat FX)
- **Metallic**: Varied based on material type (0-0.1)
- **Smoothness**: Optimized for each material type (0.1-0.9)

### Performance Optimization
- **Shader Keywords**: Only necessary keywords enabled
- **Texture Usage**: Procedural colors, no heavy textures
- **Batching**: Materials designed for efficient batching
- **Memory**: Minimal memory footprint per material

## Integration with Game Systems

### Automatic Loading
Game systems automatically load materials in this order:
1. Assigned CinderMaterialAsset in inspector
2. Resources folder: `Resources/CinderMaterialAsset.asset`
3. Project search for any CinderMaterialAsset (Editor only)
4. Fallback to default Unity materials

### System Integration

#### RegretAfterKillingSystem
- **Spirit Materials**: Ghost manifestation for spirit appearances
- **Ash Materials**: Dream ash for contemplative moments
- **Usage**: `materialAsset.GetSpiritMaterialByType(SpiritType.Ghost)`

#### CombatVisuals
- **Combat FX Materials**: Slash trails, weapon glows, hit impacts
- **Blood Materials**: Fresh blood for wounds, gore for intense scenes
- **Usage**: `materialAsset.GetCombatFXMaterialByAction(CombatAction.Slash)`

#### DreamSequenceManager
- **Spirit Materials**: Dream echo for memory sequences
- **Ash Materials**: Dream ash for ethereal atmosphere
- **Usage**: `materialAsset.GetSpiritMaterialByType(SpiritType.Dream)`

#### EnvironmentalZoneTriggers
- **Fire Materials**: Torch flames for lighting zones
- **Ash Materials**: Floating ash for cursed areas
- **Usage**: `materialAsset.GetFireMaterialByIntensity(FireIntensity.Torch)`

## Usage Examples

### Code Integration
```csharp
// Load material asset
CinderMaterialAsset materials = Resources.Load<CinderMaterialAsset>("CinderMaterialAsset");

// Get specific materials by type
Material ghostMaterial = materials.GetSpiritMaterialByType(SpiritType.Ghost);
Material slashEffect = materials.GetCombatFXMaterialByAction(CombatAction.Slash);
Material torchFlame = materials.GetFireMaterialByIntensity(FireIntensity.Torch);

// Get random materials by category
Material randomFire = materials.GetRandomFireMaterial();
Material randomSpirit = materials.GetRandomSpiritMaterial();

// Apply to renderer
Renderer renderer = GetComponent<Renderer>();
renderer.material = ghostMaterial;
```

### Particle System Integration
```csharp
// Apply ash material to particle system
ParticleSystem particles = GetComponent<ParticleSystem>();
ParticleSystem.MainModule main = particles.main;
main.startColor = Color.white;

Renderer particleRenderer = particles.GetComponent<Renderer>();
particleRenderer.material = materials.GetAshMaterialByState(AshState.Floating);
```

### Trail Renderer Integration
```csharp
// Apply slash trail to weapon
TrailRenderer trail = weapon.GetComponent<TrailRenderer>();
trail.material = materials.GetCombatFXMaterialByAction(CombatAction.Slash);
trail.time = 0.5f;
trail.widthMultiplier = 0.1f;
```

## Auto-Generation System

### AutoGenerateMaterials.cs
- ✅ Runs automatically when project loads
- ✅ Generates missing materials on-demand
- ✅ Creates 20 unique materials with proper URP settings
- ✅ Saves to both project and Resources folders
- ✅ Handles asset database refresh

### Material Creation Process
1. **Directory Creation**: Ensures proper folder structure
2. **Shader Detection**: Finds URP shaders with fallbacks
3. **Material Generation**: Creates materials with appropriate settings
4. **Property Configuration**: Sets colors, transparency, emission
5. **Asset Saving**: Saves materials to project and Resources
6. **Collection Creation**: Builds CinderMaterialAsset with references

## Material Categories and Usage

### Fire Materials
- **Soft Flame**: Gentle fire effects, magical auras
- **Ember Glow**: Weapon enchantments, magical items
- **Torch Flame**: Environmental lighting, torches
- **Weapon Enchantment**: Magical weapon effects, spells

### Ash Materials
- **Floating Ash**: Ambient particles, atmospheric effects
- **Ash Fog**: Area effects, cursed zones
- **Cursed Ash**: Dark magic, corrupted areas
- **Dream Ash**: Memory sequences, contemplative moments

### Blood Materials
- **Fresh Blood**: Recent wounds, active bleeding
- **Dried Blood**: Old stains, weathered surfaces
- **Blood Stain**: Clothing, environmental details
- **Gore**: Intense combat, boss encounters

### Spirit Materials
- **Translucent Spirit**: Basic ghost effects, fade in/out
- **Spectral Glow**: Enhanced spirits, magical beings
- **Ghost Manifestation**: Regret moments, hallucinations
- **Dream Echo**: Memory visions, past events

### Combat FX Materials
- **Slash Trail**: Weapon attack trails, sword swipes
- **Weapon Glow**: Enhanced weapons, magical items
- **Hit Impact**: Successful attack effects, damage indicators
- **Strong Attack Distortion**: Powerful moves, special abilities
- **Block Effect**: Defensive actions, parries, shields

## Performance Considerations

### Memory Usage
- Total asset size: ~5-10 MB
- Individual materials: 200-500 KB each
- Runtime memory: Minimal overhead
- Texture usage: Procedural colors only

### Rendering Performance
- **Transparent Sorting**: Proper render queue ordering
- **Batching**: Materials designed for efficient batching
- **Overdraw**: Minimized through proper alpha usage
- **Shader Complexity**: Optimized for mobile and desktop

### Optimization Features
- **Keyword Management**: Only necessary shader features enabled
- **LOD Support**: Materials work with LOD systems
- **Culling**: Proper face culling for performance
- **Compression**: Optimized for build compression

## Troubleshooting

### Common Issues

**"CinderMaterialAsset not found!"**
- Solution: Use menu `Cinder of Darkness > Generate Visual Materials`
- Or manually assign the asset in system inspectors

**Materials appear black or incorrect**
- Check if URP is properly configured in project
- Verify shader compatibility with current render pipeline
- Regenerate materials if shaders have changed

**Transparency not working**
- Ensure materials have proper blend mode settings
- Check render queue is set to 3000 or higher
- Verify alpha values are less than 1.0

**Performance issues with materials**
- Reduce number of transparent materials in scene
- Use LOD systems for distant objects
- Consider material variants for different quality levels

### Debug Tools
- Use CinderMaterialAsset.ValidateAsset() for validation
- Check CinderMaterialAsset.GetAssetStatus() for detailed info
- Monitor Unity Console for material loading messages
- Use Frame Debugger to analyze rendering performance

## Future Enhancements

### Planned Features
- **Animated Materials**: Time-based animation support
- **Weather Integration**: Materials that respond to weather
- **Dynamic Properties**: Runtime material property modification
- **Texture Support**: Optional texture maps for enhanced detail
- **Shader Graph Integration**: Custom shader graph materials

### Customization Options
- **Color Variants**: Multiple color schemes per material type
- **Intensity Levels**: Different intensity versions of effects
- **Cultural Themes**: Materials themed to different kingdoms
- **Quality Levels**: Performance-optimized material variants
- **Accessibility**: High contrast and colorblind-friendly options

## Credits
Generated automatically by Cinder of Darkness Material Asset Generator
Part of the visual effects system for enhanced atmospheric immersion
Designed for URP compatibility with performance optimization
All materials are procedurally generated and original creations
