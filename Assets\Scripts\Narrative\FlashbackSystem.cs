using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.Narrative;

namespace CinderOfDarkness.Narrative
{
    /// <summary>
    /// Flashback/Memory System for Cinder of Darkness.
    /// Manages triggered narrative flashbacks and memory sequences.
    /// </summary>
    public class FlashbackSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Flashback Settings")]
        [SerializeField] private bool enableFlashbacks = true;
        [SerializeField] private float flashbackTransitionDuration = 2f;
        [SerializeField] private float memoryFadeDuration = 1f;
        [SerializeField] private LayerMask flashbackLayer = 1 << 10;

        [Header("Visual Effects")]
        [SerializeField] private Volume postProcessVolume;
        [SerializeField] private VolumeProfile flashbackProfile;
        [SerializeField] private VolumeProfile normalProfile;
        [SerializeField] private Color flashbackTint = new Color(0.8f, 0.7f, 0.5f, 1f);
        [SerializeField] private AnimationCurve flashbackCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

        [Header("Audio")]
        [SerializeField] private AudioClip flashbackStartSound;
        [SerializeField] private AudioClip flashbackEndSound;
        [SerializeField] private AudioClip memoryTriggerSound;
        [SerializeField] private AudioSource audioSource;

        [Header("UI References")]
        [SerializeField] private GameObject flashbackUI;
        [SerializeField] private CanvasGroup flashbackOverlay;
        [SerializeField] private TMPro.TextMeshProUGUI flashbackText;
        [SerializeField] private TMPro.TextMeshProUGUI memoryTitle;

        [Header("Memory Database")]
        [SerializeField] private FlashbackData[] availableFlashbacks;
        [SerializeField] private MemoryTrigger[] memoryTriggers;
        #endregion

        #region Public Properties
        public static FlashbackSystem Instance { get; private set; }
        public bool IsInFlashback { get; private set; }
        public FlashbackData CurrentFlashback { get; private set; }
        public List<string> UnlockedMemories { get; private set; } = new List<string>();
        #endregion

        #region Private Fields
        private DynamicNarrativeSystem narrativeSystem;
        private PlayerController playerController;
        private Camera playerCamera;

        // Flashback state
        private Coroutine currentFlashbackCoroutine;
        private Vector3 originalPlayerPosition;
        private Quaternion originalPlayerRotation;
        private bool playerControlsDisabled;

        // Visual effects
        private ColorAdjustments colorAdjustments;
        private Vignette vignette;
        private ChromaticAberration chromaticAberration;

        // Memory tracking
        private Dictionary<string, bool> triggeredMemories = new Dictionary<string, bool>();
        private List<FlashbackData> availableMemories = new List<FlashbackData>();

        // Timing
        private float lastMemoryCheck;
        private const float memoryCheckInterval = 1f;
        #endregion

        #region Events
        public System.Action<FlashbackData> OnFlashbackStarted;
        public System.Action<FlashbackData> OnFlashbackEnded;
        public System.Action<string> OnMemoryUnlocked;
        public System.Action<MemoryTrigger> OnMemoryTriggered;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeFlashbackSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            SetupPostProcessing();
            LoadMemoryData();
        }

        private void Update()
        {
            if (!enableFlashbacks) return;

            CheckMemoryTriggers();
            UpdateFlashbackEffects();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the flashback system.
        /// </summary>
        private void InitializeFlashbackSystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }

            // Hide flashback UI initially
            if (flashbackUI != null)
            {
                flashbackUI.SetActive(false);
            }

            Debug.Log("Flashback System initialized");
        }

        /// <summary>
        /// Setup system references.
        /// </summary>
        private void SetupSystemReferences()
        {
            narrativeSystem = DynamicNarrativeSystem.Instance;
            playerController = FindObjectOfType<PlayerController>();
            playerCamera = Camera.main;

            if (playerCamera == null)
            {
                playerCamera = FindObjectOfType<Camera>();
            }
        }

        /// <summary>
        /// Setup post-processing effects.
        /// </summary>
        private void SetupPostProcessing()
        {
            if (postProcessVolume != null && postProcessVolume.profile != null)
            {
                postProcessVolume.profile.TryGet(out colorAdjustments);
                postProcessVolume.profile.TryGet(out vignette);
                postProcessVolume.profile.TryGet(out chromaticAberration);
            }
        }
        #endregion

        #region Memory Trigger System
        /// <summary>
        /// Check for memory triggers based on player state and narrative conditions.
        /// </summary>
        private void CheckMemoryTriggers()
        {
            if (Time.time - lastMemoryCheck < memoryCheckInterval) return;
            if (IsInFlashback) return;

            foreach (var trigger in memoryTriggers)
            {
                if (ShouldTriggerMemory(trigger))
                {
                    TriggerMemory(trigger);
                }
            }

            lastMemoryCheck = Time.time;
        }

        /// <summary>
        /// Check if a memory should be triggered.
        /// </summary>
        /// <param name="trigger">Memory trigger to check</param>
        /// <returns>True if memory should trigger</returns>
        private bool ShouldTriggerMemory(MemoryTrigger trigger)
        {
            // Check if already triggered
            if (triggeredMemories.ContainsKey(trigger.triggerId) && triggeredMemories[trigger.triggerId])
            {
                return false;
            }

            // Check trigger conditions
            switch (trigger.triggerType)
            {
                case MemoryTriggerType.Location:
                    return CheckLocationTrigger(trigger);
                case MemoryTriggerType.Item:
                    return CheckItemTrigger(trigger);
                case MemoryTriggerType.NPC:
                    return CheckNPCTrigger(trigger);
                case MemoryTriggerType.StoryFlag:
                    return CheckStoryFlagTrigger(trigger);
                case MemoryTriggerType.Combat:
                    return CheckCombatTrigger(trigger);
                case MemoryTriggerType.Time:
                    return CheckTimeTrigger(trigger);
                default:
                    return false;
            }
        }

        /// <summary>
        /// Check location-based trigger.
        /// </summary>
        private bool CheckLocationTrigger(MemoryTrigger trigger)
        {
            if (playerController == null) return false;

            float distance = Vector3.Distance(playerController.transform.position, trigger.triggerPosition);
            return distance <= trigger.triggerRadius;
        }

        /// <summary>
        /// Check item-based trigger.
        /// </summary>
        private bool CheckItemTrigger(MemoryTrigger trigger)
        {
            // Would check player inventory for specific item
            // PlayerInventory inventory = playerController.GetComponent<PlayerInventory>();
            // return inventory != null && inventory.HasItem(trigger.requiredItem);
            return false; // Placeholder
        }

        /// <summary>
        /// Check NPC interaction trigger.
        /// </summary>
        private bool CheckNPCTrigger(MemoryTrigger trigger)
        {
            // Would check if player is interacting with specific NPC
            return false; // Placeholder
        }

        /// <summary>
        /// Check story flag trigger.
        /// </summary>
        private bool CheckStoryFlagTrigger(MemoryTrigger trigger)
        {
            if (narrativeSystem == null) return false;
            return narrativeSystem.IsStoryFlagSet(trigger.requiredFlag);
        }

        /// <summary>
        /// Check combat trigger.
        /// </summary>
        private bool CheckCombatTrigger(MemoryTrigger trigger)
        {
            // Would check combat state or specific enemy type
            return false; // Placeholder
        }

        /// <summary>
        /// Check time-based trigger.
        /// </summary>
        private bool CheckTimeTrigger(MemoryTrigger trigger)
        {
            return Time.time >= trigger.triggerTime;
        }

        /// <summary>
        /// Trigger a memory sequence.
        /// </summary>
        /// <param name="trigger">Memory trigger</param>
        private void TriggerMemory(MemoryTrigger trigger)
        {
            var flashback = GetFlashbackData(trigger.flashbackId);
            if (flashback != null)
            {
                triggeredMemories[trigger.triggerId] = true;
                StartFlashback(flashback);
                OnMemoryTriggered?.Invoke(trigger);

                PlaySound(memoryTriggerSound);
                Debug.Log($"Memory triggered: {trigger.triggerId}");
            }
        }
        #endregion

        #region Flashback Management
        /// <summary>
        /// Start a flashback sequence.
        /// </summary>
        /// <param name="flashbackData">Flashback data</param>
        public void StartFlashback(FlashbackData flashbackData)
        {
            if (IsInFlashback || flashbackData == null) return;

            CurrentFlashback = flashbackData;
            IsInFlashback = true;

            if (currentFlashbackCoroutine != null)
            {
                StopCoroutine(currentFlashbackCoroutine);
            }

            currentFlashbackCoroutine = StartCoroutine(FlashbackSequence(flashbackData));
            OnFlashbackStarted?.Invoke(flashbackData);
        }

        /// <summary>
        /// End the current flashback.
        /// </summary>
        public void EndFlashback()
        {
            if (!IsInFlashback) return;

            if (currentFlashbackCoroutine != null)
            {
                StopCoroutine(currentFlashbackCoroutine);
                currentFlashbackCoroutine = null;
            }

            StartCoroutine(EndFlashbackSequence());
        }

        /// <summary>
        /// Main flashback sequence coroutine.
        /// </summary>
        /// <param name="flashbackData">Flashback data</param>
        /// <returns>Flashback coroutine</returns>
        private IEnumerator FlashbackSequence(FlashbackData flashbackData)
        {
            // Store original state
            StoreOriginalState();

            // Disable player controls
            DisablePlayerControls();

            // Play start sound
            PlaySound(flashbackStartSound);

            // Transition to flashback visuals
            yield return StartCoroutine(TransitionToFlashback());

            // Show flashback UI
            if (flashbackUI != null)
            {
                flashbackUI.SetActive(true);
            }

            // Display memory content
            yield return StartCoroutine(DisplayMemoryContent(flashbackData));

            // Wait for flashback duration or player input
            yield return StartCoroutine(WaitForFlashbackEnd(flashbackData));

            // End flashback
            yield return StartCoroutine(EndFlashbackSequence());
        }

        /// <summary>
        /// Transition to flashback visual state.
        /// </summary>
        /// <returns>Transition coroutine</returns>
        private IEnumerator TransitionToFlashback()
        {
            float elapsedTime = 0f;

            // Fade to flashback effect
            while (elapsedTime < flashbackTransitionDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = flashbackCurve.Evaluate(elapsedTime / flashbackTransitionDuration);

                ApplyFlashbackEffects(progress);
                yield return null;
            }

            // Apply flashback post-processing profile
            if (postProcessVolume != null && flashbackProfile != null)
            {
                postProcessVolume.profile = flashbackProfile;
            }
        }

        /// <summary>
        /// Display memory content (text, images, etc.).
        /// </summary>
        /// <param name="flashbackData">Flashback data</param>
        /// <returns>Display coroutine</returns>
        private IEnumerator DisplayMemoryContent(FlashbackData flashbackData)
        {
            // Update memory title
            if (memoryTitle != null)
            {
                memoryTitle.text = flashbackData.memoryTitle;
            }

            // Display memory text with typewriter effect
            if (flashbackText != null && !string.IsNullOrEmpty(flashbackData.memoryText))
            {
                yield return StartCoroutine(TypewriterEffect(flashbackData.memoryText));
            }

            // Show memory images or cinematics
            if (flashbackData.memoryImages != null && flashbackData.memoryImages.Length > 0)
            {
                yield return StartCoroutine(DisplayMemoryImages(flashbackData.memoryImages));
            }
        }

        /// <summary>
        /// Typewriter effect for memory text.
        /// </summary>
        /// <param name="text">Text to display</param>
        /// <returns>Typewriter coroutine</returns>
        private IEnumerator TypewriterEffect(string text)
        {
            if (flashbackText == null) yield break;

            flashbackText.text = "";

            for (int i = 0; i <= text.Length; i++)
            {
                flashbackText.text = text.Substring(0, i);
                yield return new WaitForSeconds(0.05f);
            }
        }

        /// <summary>
        /// Display memory images in sequence.
        /// </summary>
        /// <param name="images">Array of memory images</param>
        /// <returns>Display coroutine</returns>
        private IEnumerator DisplayMemoryImages(Sprite[] images)
        {
            // Implementation would display images in sequence
            // This is a placeholder for the image display system
            yield return new WaitForSeconds(2f);
        }

        /// <summary>
        /// Wait for flashback to end (duration or player input).
        /// </summary>
        /// <param name="flashbackData">Flashback data</param>
        /// <returns>Wait coroutine</returns>
        private IEnumerator WaitForFlashbackEnd(FlashbackData flashbackData)
        {
            float elapsedTime = 0f;

            while (elapsedTime < flashbackData.duration)
            {
                // Check for skip input
                if (Input.GetKeyDown(KeyCode.Space) || Input.GetKeyDown(KeyCode.Escape))
                {
                    break;
                }

                elapsedTime += Time.deltaTime;
                yield return null;
            }
        }

        /// <summary>
        /// End flashback sequence.
        /// </summary>
        /// <returns>End sequence coroutine</returns>
        private IEnumerator EndFlashbackSequence()
        {
            // Play end sound
            PlaySound(flashbackEndSound);

            // Hide flashback UI
            if (flashbackUI != null)
            {
                flashbackUI.SetActive(false);
            }

            // Transition back to normal visuals
            yield return StartCoroutine(TransitionFromFlashback());

            // Restore player controls
            RestorePlayerControls();

            // Restore original state
            RestoreOriginalState();

            // Mark memory as unlocked
            if (CurrentFlashback != null && !UnlockedMemories.Contains(CurrentFlashback.flashbackId))
            {
                UnlockedMemories.Add(CurrentFlashback.flashbackId);
                OnMemoryUnlocked?.Invoke(CurrentFlashback.flashbackId);
            }

            // Trigger narrative consequences
            if (CurrentFlashback != null && narrativeSystem != null)
            {
                narrativeSystem.SetStoryFlag($"memory_{CurrentFlashback.flashbackId}", true, "flashback");
            }

            OnFlashbackEnded?.Invoke(CurrentFlashback);
            CurrentFlashback = null;
            IsInFlashback = false;
        }

        /// <summary>
        /// Transition from flashback back to normal.
        /// </summary>
        /// <returns>Transition coroutine</returns>
        private IEnumerator TransitionFromFlashback()
        {
            float elapsedTime = 0f;

            // Restore normal post-processing profile
            if (postProcessVolume != null && normalProfile != null)
            {
                postProcessVolume.profile = normalProfile;
            }

            // Fade from flashback effect
            while (elapsedTime < flashbackTransitionDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = 1f - flashbackCurve.Evaluate(elapsedTime / flashbackTransitionDuration);

                ApplyFlashbackEffects(progress);
                yield return null;
            }

            // Ensure effects are fully removed
            ApplyFlashbackEffects(0f);
        }
        #endregion

        #region Visual Effects
        /// <summary>
        /// Apply flashback visual effects.
        /// </summary>
        /// <param name="intensity">Effect intensity (0-1)</param>
        private void ApplyFlashbackEffects(float intensity)
        {
            // Apply color adjustments
            if (colorAdjustments != null)
            {
                colorAdjustments.saturation.value = Mathf.Lerp(0f, -30f, intensity);
                colorAdjustments.contrast.value = Mathf.Lerp(0f, 20f, intensity);
            }

            // Apply vignette
            if (vignette != null)
            {
                vignette.intensity.value = Mathf.Lerp(0f, 0.4f, intensity);
            }

            // Apply chromatic aberration
            if (chromaticAberration != null)
            {
                chromaticAberration.intensity.value = Mathf.Lerp(0f, 0.3f, intensity);
            }

            // Apply overlay fade
            if (flashbackOverlay != null)
            {
                flashbackOverlay.alpha = Mathf.Lerp(0f, 0.2f, intensity);
            }
        }

        /// <summary>
        /// Update flashback effects during sequence.
        /// </summary>
        private void UpdateFlashbackEffects()
        {
            if (!IsInFlashback) return;

            // Could add breathing or pulsing effects here
            float pulseEffect = Mathf.Sin(Time.time * 2f) * 0.1f + 0.9f;

            if (colorAdjustments != null)
            {
                colorAdjustments.brightness.value = Mathf.Lerp(-0.1f, 0.1f, pulseEffect);
            }
        }
        #endregion

        #region Player Control Management
        /// <summary>
        /// Store original player state.
        /// </summary>
        private void StoreOriginalState()
        {
            if (playerController != null)
            {
                originalPlayerPosition = playerController.transform.position;
                originalPlayerRotation = playerController.transform.rotation;
            }
        }

        /// <summary>
        /// Disable player controls during flashback.
        /// </summary>
        private void DisablePlayerControls()
        {
            if (playerController != null)
            {
                playerController.enabled = false;
                playerControlsDisabled = true;
            }

            // Disable other input systems
            var inputManager = FindObjectOfType<InputManager>();
            if (inputManager != null)
            {
                inputManager.SetInputEnabled(false);
            }
        }

        /// <summary>
        /// Restore player controls after flashback.
        /// </summary>
        private void RestorePlayerControls()
        {
            if (playerController != null && playerControlsDisabled)
            {
                playerController.enabled = true;
                playerControlsDisabled = false;
            }

            // Re-enable other input systems
            var inputManager = FindObjectOfType<InputManager>();
            if (inputManager != null)
            {
                inputManager.SetInputEnabled(true);
            }
        }

        /// <summary>
        /// Restore original player state.
        /// </summary>
        private void RestoreOriginalState()
        {
            // Could restore player position if needed
            // For now, we keep the player where they are
        }
        #endregion

        #region Data Management
        /// <summary>
        /// Get flashback data by ID.
        /// </summary>
        /// <param name="flashbackId">Flashback ID</param>
        /// <returns>Flashback data or null</returns>
        private FlashbackData GetFlashbackData(string flashbackId)
        {
            return availableFlashbacks?.FirstOrDefault(f => f.flashbackId == flashbackId);
        }

        /// <summary>
        /// Save memory data to PlayerPrefs.
        /// </summary>
        private void SaveMemoryData()
        {
            var saveData = new MemorySaveData
            {
                unlockedMemories = UnlockedMemories.ToArray(),
                triggeredMemories = triggeredMemories
            };

            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("FlashbackMemories", json);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Load memory data from PlayerPrefs.
        /// </summary>
        private void LoadMemoryData()
        {
            string json = PlayerPrefs.GetString("FlashbackMemories", "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var saveData = JsonUtility.FromJson<MemorySaveData>(json);

                    if (saveData.unlockedMemories != null)
                    {
                        UnlockedMemories = saveData.unlockedMemories.ToList();
                    }

                    if (saveData.triggeredMemories != null)
                    {
                        triggeredMemories = saveData.triggeredMemories;
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load memory data: {e.Message}");
                }
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play audio clip.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Manually trigger a flashback by ID.
        /// </summary>
        /// <param name="flashbackId">Flashback ID</param>
        public void TriggerFlashback(string flashbackId)
        {
            var flashback = GetFlashbackData(flashbackId);
            if (flashback != null)
            {
                StartFlashback(flashback);
            }
        }

        /// <summary>
        /// Check if a memory is unlocked.
        /// </summary>
        /// <param name="memoryId">Memory ID</param>
        /// <returns>True if memory is unlocked</returns>
        public bool IsMemoryUnlocked(string memoryId)
        {
            return UnlockedMemories.Contains(memoryId);
        }

        /// <summary>
        /// Get all unlocked memories.
        /// </summary>
        /// <returns>List of unlocked memory IDs</returns>
        public List<string> GetUnlockedMemories()
        {
            return new List<string>(UnlockedMemories);
        }

        /// <summary>
        /// Reset all memory progress.
        /// </summary>
        public void ResetMemories()
        {
            UnlockedMemories.Clear();
            triggeredMemories.Clear();
            SaveMemoryData();

            Debug.Log("All memories reset");
        }

        /// <summary>
        /// Add a memory trigger at runtime.
        /// </summary>
        /// <param name="trigger">Memory trigger to add</param>
        public void AddMemoryTrigger(MemoryTrigger trigger)
        {
            var triggerList = memoryTriggers.ToList();
            triggerList.Add(trigger);
            memoryTriggers = triggerList.ToArray();
        }

        /// <summary>
        /// Remove a memory trigger.
        /// </summary>
        /// <param name="triggerId">Trigger ID to remove</param>
        public void RemoveMemoryTrigger(string triggerId)
        {
            var triggerList = memoryTriggers.ToList();
            triggerList.RemoveAll(t => t.triggerId == triggerId);
            memoryTriggers = triggerList.ToArray();
        }

        /// <summary>
        /// Get flashback statistics.
        /// </summary>
        /// <returns>Flashback statistics</returns>
        public FlashbackStats GetFlashbackStats()
        {
            return new FlashbackStats
            {
                totalFlashbacks = availableFlashbacks?.Length ?? 0,
                unlockedFlashbacks = UnlockedMemories.Count,
                completionPercentage = availableFlashbacks?.Length > 0 ?
                    (float)UnlockedMemories.Count / availableFlashbacks.Length : 0f
            };
        }

        /// <summary>
        /// Set flashback system enabled state.
        /// </summary>
        /// <param name="enabled">Enabled state</param>
        public void SetFlashbacksEnabled(bool enabled)
        {
            enableFlashbacks = enabled;
        }

        /// <summary>
        /// Skip current flashback if active.
        /// </summary>
        public void SkipCurrentFlashback()
        {
            if (IsInFlashback)
            {
                EndFlashback();
            }
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class MemorySaveData
    {
        public string[] unlockedMemories;
        public Dictionary<string, bool> triggeredMemories;
    }

    [System.Serializable]
    public class FlashbackStats
    {
        public int totalFlashbacks;
        public int unlockedFlashbacks;
        public float completionPercentage;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class FlashbackData
    {
        public string flashbackId;
        public string memoryTitle;
        public string memoryText;
        public float duration = 5f;
        public Sprite[] memoryImages;
        public AudioClip memoryAudio;
        public FlashbackType type;
        public bool isRepeatable = false;
    }

    [System.Serializable]
    public class MemoryTrigger
    {
        public string triggerId;
        public string flashbackId;
        public MemoryTriggerType triggerType;
        public Vector3 triggerPosition;
        public float triggerRadius = 5f;
        public string requiredItem;
        public string requiredFlag;
        public float triggerTime;
        public bool isOneTime = true;
    }

    public enum FlashbackType
    {
        Memory,
        Vision,
        Dream,
        Echo,
        Prophecy
    }

    public enum MemoryTriggerType
    {
        Location,
        Item,
        NPC,
        StoryFlag,
        Combat,
        Time
    }
    #endregion
}
