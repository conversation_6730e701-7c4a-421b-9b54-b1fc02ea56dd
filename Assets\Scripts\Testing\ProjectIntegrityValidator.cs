using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Final Project Integrity Validator for Cinder of Darkness
/// Validates that all systems are properly integrated and ready for production
/// </summary>
public class ProjectIntegrityValidator : MonoBehaviour
{
    [Header("Validation Results")]
    public bool projectIsReady = false;
    public List<string> validationResults = new List<string>();
    public List<string> criticalIssues = new List<string>();
    public List<string> warnings = new List<string>();
    
    [Header("System Counts")]
    public int totalSystems = 0;
    public int validatedSystems = 0;
    public int failedSystems = 0;
    
    void Start()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunFullProjectValidation());
        }
    }
    
    System.Collections.IEnumerator RunFullProjectValidation()
    {
        Debug.Log("=== CINDER OF DARKNESS - FINAL PROJECT INTEGRITY VALIDATION ===");
        
        yield return new WaitForSeconds(1f);
        
        // Clear previous results
        validationResults.Clear();
        criticalIssues.Clear();
        warnings.Clear();
        totalSystems = 0;
        validatedSystems = 0;
        failedSystems = 0;
        
        // Run comprehensive validation
        ValidateCoreSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateCombatSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateInputSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateUISystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateAudioSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateNarrativeSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateWorldSystems();
        yield return new WaitForSeconds(0.1f);
        
        ValidateUnityCompatibility();
        yield return new WaitForSeconds(0.1f);
        
        // Generate final report
        GenerateFinalReport();
    }
    
    void ValidateCoreSystems()
    {
        Debug.Log("Validating Core Player Systems...");
        
        ValidateSystem<PlayerController>("PlayerController", true);
        ValidateSystem<PlayerStats>("PlayerStats", true);
        ValidateSystem<PlayerCombat>("PlayerCombat", true);
        ValidateSystem<PsychologicalSystem>("PsychologicalSystem", true);
        ValidateSystem<CharacterProgression>("CharacterProgression", true);
    }
    
    void ValidateCombatSystems()
    {
        Debug.Log("Validating Combat Systems...");
        
        ValidateSystem<BrutalCombatSystem>("BrutalCombatSystem", false);
        ValidateSystem<DeathConsequenceSystem>("DeathConsequenceSystem", false);
        ValidateSystem<EnemyAI>("EnemyAI", false);
        ValidateSystem<EnemyHealth>("EnemyHealth", false);
        ValidateSystem<Fireball>("Fireball", false);
        ValidateSystem<ElementalMagicSystem>("ElementalMagicSystem", true);
    }
    
    void ValidateInputSystems()
    {
        Debug.Log("Validating Input Systems...");
        
        ValidateSystem<MultiInputControlSystem>("MultiInputControlSystem", true);
        ValidateSystem<UIButtonPromptSystem>("UIButtonPromptSystem", false);
        
        // Test CinderInput wrapper
        try
        {
            CinderInput testInput = new CinderInput();
            if (testInput != null)
            {
                RecordSuccess("CinderInput", "Input wrapper system validated");
            }
            else
            {
                RecordFailure("CinderInput", "Input wrapper failed to initialize", true);
            }
        }
        catch (System.Exception e)
        {
            RecordFailure("CinderInput", $"Input wrapper exception: {e.Message}", true);
        }
    }
    
    void ValidateUISystems()
    {
        Debug.Log("Validating UI Systems...");
        
        ValidateSystem<GameUI>("GameUI", true);
        ValidateSystem<DialogueSystem>("DialogueSystem", true);
    }
    
    void ValidateAudioSystems()
    {
        Debug.Log("Validating Audio Systems...");
        
        ValidateSystem<DynamicMusicalSystem>("DynamicMusicalSystem", false);
        ValidateSystem<VoiceActingSystem>("VoiceActingSystem", false);
        ValidateSystem<AudioManager>("AudioManager", false);
    }
    
    void ValidateNarrativeSystems()
    {
        Debug.Log("Validating Narrative Systems...");
        
        ValidateSystem<PhilosophicalMoralitySystem>("PhilosophicalMoralitySystem", true);
        ValidateSystem<DynamicTitleSystem>("DynamicTitleSystem", false);
        ValidateSystem<OrphanedChildCompanion>("OrphanedChildCompanion", false);
        ValidateSystem<GameManager>("GameManager", true);
    }
    
    void ValidateWorldSystems()
    {
        Debug.Log("Validating World Systems...");
        
        ValidateSystem<WorldInteractionSystem>("WorldInteractionSystem", false);
        ValidateSystem<HostilitySystem>("HostilitySystem", false);
        ValidateSystem<EconomySystem>("EconomySystem", false);
        ValidateSystem<NPCController>("NPCController", false);
    }
    
    void ValidateUnityCompatibility()
    {
        Debug.Log("Validating Unity 2022.3 LTS Compatibility...");
        
        // Check Unity version
        string unityVersion = Application.unityVersion;
        if (unityVersion.Contains("2022.3"))
        {
            RecordSuccess("Unity Version", $"Unity {unityVersion} compatibility confirmed");
        }
        else
        {
            RecordWarning("Unity Version", $"Expected Unity 2022.3.x, found {unityVersion}");
        }
        
        // Check URP
        if (UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset != null)
        {
            RecordSuccess("URP", "Universal Render Pipeline configured");
        }
        else
        {
            RecordWarning("URP", "Universal Render Pipeline not configured");
        }
        
        // Check Input System
        #if ENABLE_INPUT_SYSTEM
        RecordSuccess("Input System", "New Input System enabled");
        #else
        RecordWarning("Input System", "New Input System not enabled");
        #endif
    }
    
    void ValidateSystem<T>(string systemName, bool isCritical) where T : MonoBehaviour
    {
        totalSystems++;
        
        try
        {
            T existingSystem = FindObjectOfType<T>();
            if (existingSystem != null)
            {
                RecordSuccess(systemName, "System found and validated in scene");
                return;
            }
            
            // Try to create test instance
            GameObject testObject = new GameObject($"Test_{systemName}");
            T component = testObject.AddComponent<T>();
            
            if (component != null)
            {
                RecordSuccess(systemName, "System can be instantiated successfully");
                Destroy(testObject);
            }
            else
            {
                RecordFailure(systemName, "Failed to create system component", isCritical);
            }
        }
        catch (System.Exception e)
        {
            RecordFailure(systemName, $"System validation exception: {e.Message}", isCritical);
        }
    }
    
    void RecordSuccess(string systemName, string message)
    {
        validatedSystems++;
        validationResults.Add($"✅ {systemName}: {message}");
        Debug.Log($"✅ {systemName}: {message}");
    }
    
    void RecordFailure(string systemName, string message, bool isCritical)
    {
        failedSystems++;
        string fullMessage = $"❌ {systemName}: {message}";
        validationResults.Add(fullMessage);
        
        if (isCritical)
        {
            criticalIssues.Add(fullMessage);
            Debug.LogError(fullMessage);
        }
        else
        {
            warnings.Add(fullMessage);
            Debug.LogWarning(fullMessage);
        }
    }
    
    void RecordWarning(string systemName, string message)
    {
        string fullMessage = $"⚠️ {systemName}: {message}";
        validationResults.Add(fullMessage);
        warnings.Add(fullMessage);
        Debug.LogWarning(fullMessage);
    }
    
    void GenerateFinalReport()
    {
        Debug.Log("=== FINAL PROJECT VALIDATION REPORT ===");
        
        float successRate = (float)validatedSystems / totalSystems * 100f;
        projectIsReady = criticalIssues.Count == 0 && successRate >= 90f;
        
        Debug.Log($"📊 VALIDATION STATISTICS:");
        Debug.Log($"   Total Systems Tested: {totalSystems}");
        Debug.Log($"   Successfully Validated: {validatedSystems}");
        Debug.Log($"   Failed Systems: {failedSystems}");
        Debug.Log($"   Success Rate: {successRate:F1}%");
        Debug.Log($"   Critical Issues: {criticalIssues.Count}");
        Debug.Log($"   Warnings: {warnings.Count}");
        
        if (projectIsReady)
        {
            Debug.Log("🎉 PROJECT READY FOR PRODUCTION! 🎉");
            Debug.Log("Cinder of Darkness has passed all critical validation tests!");
            Debug.Log("The project is ready for Unity 2022.3 LTS development and deployment.");
        }
        else
        {
            Debug.LogError("❌ PROJECT NOT READY FOR PRODUCTION");
            Debug.LogError($"Critical issues found: {criticalIssues.Count}");
            foreach (string issue in criticalIssues)
            {
                Debug.LogError($"  - {issue}");
            }
        }
        
        Debug.Log("=== END VALIDATION REPORT ===");
    }
    
    [ContextMenu("Run Validation")]
    public void RunValidationManually()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunFullProjectValidation());
        }
        else
        {
            Debug.LogWarning("Validation can only be run in Play Mode for complete testing");
        }
    }
    
    public bool IsProjectReady() => projectIsReady;
    public float GetSuccessRate() => totalSystems > 0 ? (float)validatedSystems / totalSystems * 100f : 0f;
    public int GetCriticalIssueCount() => criticalIssues.Count;
    public int GetWarningCount() => warnings.Count;
}
