using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using CinderOfDarkness.Stealth;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Stealth Indicator UI for Cinder of Darkness.
    /// Shows stealth status, detection level, and enemy awareness.
    /// </summary>
    public class StealthIndicatorUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Stealth Status")]
        [SerializeField] private GameObject stealthPanel;
        [SerializeField] private Image stealthIcon;
        [SerializeField] private TextMeshProUGUI stealthStatusText;
        [SerializeField] private Slider detectionSlider;
        [SerializeField] private Image detectionFill;

        [Header("Enemy Awareness")]
        [SerializeField] private Transform enemyIndicatorsContainer;
        [SerializeField] private GameObject enemyIndicatorPrefab;
        [SerializeField] private float indicatorRange = 50f;

        [Header("Light Level")]
        [SerializeField] private Image lightLevelIndicator;
        [SerializeField] private TextMeshProUG<PERSON> lightLevelText;
        [SerializeField] private Gradient lightGradient;

        [Header("Stealth Tools")]
        [SerializeField] private Transform toolsContainer;
        [SerializeField] private GameObject toolSlotPrefab;
        [SerializeField] private KeyCode[] toolHotkeys = { KeyCode.Alpha1, KeyCode.Alpha2, KeyCode.Alpha3 };

        [Header("Visual Effects")]
        [SerializeField] private CanvasGroup stealthOverlay;
        [SerializeField] private Image vignetteEffect;
        [SerializeField] private AnimationCurve stealthFadeCurve;
        [SerializeField] private float fadeSpeed = 2f;

        [Header("Audio")]
        [SerializeField] private AudioClip stealthEnterSound;
        [SerializeField] private AudioClip stealthExitSound;
        [SerializeField] private AudioClip detectionWarningSound;
        [SerializeField] private AudioClip heartbeatSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private StealthSystem stealthSystem;
        private Camera playerCamera;
        private Transform playerTransform;

        private float currentDetectionLevel = 0f;
        private float targetDetectionLevel = 0f;
        private bool isInStealth = false;
        private float currentLightLevel = 1f;

        private Coroutine detectionAnimationCoroutine;
        private Coroutine heartbeatCoroutine;

        // Enemy indicators
        private System.Collections.Generic.List<GameObject> enemyIndicators =
            new System.Collections.Generic.List<GameObject>();
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            UpdateDetectionLevel();
            UpdateLightLevel();
            UpdateEnemyIndicators();
            HandleToolInput();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            stealthSystem = StealthSystem.Instance;
            playerCamera = Camera.main;

            // Cache player reference to avoid repeated FindObjectOfType calls
            if (playerTransform == null)
            {
                var player = FindObjectOfType<PlayerController>();
                if (player != null)
                    playerTransform = player.transform;
            }
        }

        private void SetupUI()
        {
            if (stealthPanel != null)
                stealthPanel.SetActive(false);

            if (stealthOverlay != null)
                stealthOverlay.alpha = 0f;

            SetupToolSlots();
            UpdateStealthStatus();
        }

        private void SetupEventListeners()
        {
            if (stealthSystem != null)
            {
                stealthSystem.OnStealthStateChanged += OnStealthStateChanged;
                stealthSystem.OnDetectionLevelChanged += OnDetectionLevelChanged;
                stealthSystem.OnEnemyAwarenessChanged += OnEnemyAwarenessChanged;
                stealthSystem.OnToolUsed += OnToolUsed;
            }
        }

        private void SetupToolSlots()
        {
            if (toolSlotPrefab == null || toolsContainer == null) return;

            // Create tool slots for stealth tools
            string[] toolNames = { "Smoke Bomb", "Lockpick", "Caltrops" };

            for (int i = 0; i < toolNames.Length; i++)
            {
                GameObject slot = Instantiate(toolSlotPrefab, toolsContainer);
                var toolSlot = slot.GetComponent<ToolSlotUI>();

                if (toolSlot != null)
                {
                    toolSlot.Setup(toolNames[i], i < toolHotkeys.Length ? toolHotkeys[i] : KeyCode.None);
                }
            }
        }
        #endregion

        #region Stealth Status Display
        private void UpdateStealthStatus()
        {
            if (stealthSystem == null) return;

            bool newStealthState = stealthSystem.IsInStealth;

            if (newStealthState != isInStealth)
            {
                isInStealth = newStealthState;
                UpdateStealthVisuals();
            }

            if (stealthPanel != null)
                stealthPanel.SetActive(isInStealth);

            if (stealthStatusText != null)
            {
                string status = isInStealth ? "HIDDEN" : "VISIBLE";
                stealthStatusText.text = status;
                stealthStatusText.color = isInStealth ? Color.green : Color.red;
            }

            if (stealthIcon != null)
            {
                stealthIcon.color = isInStealth ? Color.green : Color.gray;
            }
        }

        private void UpdateStealthVisuals()
        {
            if (stealthOverlay != null)
            {
                float targetAlpha = isInStealth ? 0.3f : 0f;
                StartCoroutine(FadeStealthOverlay(targetAlpha));
            }

            if (vignetteEffect != null)
            {
                vignetteEffect.gameObject.SetActive(isInStealth);
            }
        }

        private IEnumerator FadeStealthOverlay(float targetAlpha)
        {
            float startAlpha = stealthOverlay.alpha;
            float elapsed = 0f;
            float duration = 1f / fadeSpeed;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                float curveValue = stealthFadeCurve.Evaluate(progress);

                stealthOverlay.alpha = Mathf.Lerp(startAlpha, targetAlpha, curveValue);
                yield return null;
            }

            stealthOverlay.alpha = targetAlpha;
        }
        #endregion

        #region Detection Level
        private void UpdateDetectionLevel()
        {
            if (stealthSystem == null) return;

            targetDetectionLevel = stealthSystem.GetDetectionLevel();

            // Smooth detection level animation
            currentDetectionLevel = Mathf.Lerp(currentDetectionLevel, targetDetectionLevel, Time.deltaTime * 5f);

            if (detectionSlider != null)
            {
                detectionSlider.value = currentDetectionLevel;
            }

            if (detectionFill != null)
            {
                // Change color based on detection level
                if (currentDetectionLevel < 0.3f)
                    detectionFill.color = Color.green;
                else if (currentDetectionLevel < 0.7f)
                    detectionFill.color = Color.yellow;
                else
                    detectionFill.color = Color.red;
            }

            // Play warning sounds
            if (currentDetectionLevel > 0.8f && heartbeatCoroutine == null)
            {
                heartbeatCoroutine = StartCoroutine(PlayHeartbeat());
            }
            else if (currentDetectionLevel <= 0.5f && heartbeatCoroutine != null)
            {
                StopCoroutine(heartbeatCoroutine);
                heartbeatCoroutine = null;
            }
        }

        private IEnumerator PlayHeartbeat()
        {
            while (currentDetectionLevel > 0.5f)
            {
                PlaySound(heartbeatSound);
                float interval = Mathf.Lerp(1.5f, 0.5f, currentDetectionLevel);
                yield return new WaitForSeconds(interval);
            }
            heartbeatCoroutine = null;
        }
        #endregion

        #region Light Level
        private void UpdateLightLevel()
        {
            if (stealthSystem == null || playerTransform == null) return;

            currentLightLevel = stealthSystem.GetLightLevelAtPosition(playerTransform.position);

            if (lightLevelIndicator != null)
            {
                lightLevelIndicator.color = lightGradient.Evaluate(currentLightLevel);
            }

            if (lightLevelText != null)
            {
                string lightStatus = currentLightLevel < 0.3f ? "DARK" :
                                   currentLightLevel < 0.7f ? "DIM" : "BRIGHT";
                lightLevelText.text = lightStatus;
            }
        }
        #endregion

        #region Enemy Indicators
        private void UpdateEnemyIndicators()
        {
            if (playerTransform == null || playerCamera == null) return;

            ClearEnemyIndicators();

            // Find all enemies within range
            var enemies = FindObjectsOfType<EnemyDetector>();
            foreach (var enemy in enemies)
            {
                float distance = Vector3.Distance(playerTransform.position, enemy.transform.position);
                if (distance <= indicatorRange)
                {
                    CreateEnemyIndicator(enemy);
                }
            }
        }

        private void CreateEnemyIndicator(EnemyDetector enemy)
        {
            if (enemyIndicatorPrefab == null || enemyIndicatorsContainer == null) return;

            GameObject indicator = Instantiate(enemyIndicatorPrefab, enemyIndicatorsContainer);
            var enemyIndicator = indicator.GetComponent<EnemyIndicatorUI>();

            if (enemyIndicator != null)
            {
                enemyIndicator.Setup(enemy, playerCamera);
                enemyIndicators.Add(indicator);
            }
        }

        private void ClearEnemyIndicators()
        {
            foreach (var indicator in enemyIndicators)
            {
                if (indicator != null)
                    Destroy(indicator);
            }
            enemyIndicators.Clear();
        }
        #endregion

        #region Tool Input
        private void HandleToolInput()
        {
            if (stealthSystem == null) return;

            for (int i = 0; i < toolHotkeys.Length; i++)
            {
                if (Input.GetKeyDown(toolHotkeys[i]))
                {
                    UseTool(i);
                }
            }
        }

        private void UseTool(int toolIndex)
        {
            switch (toolIndex)
            {
                case 0: // Smoke Bomb
                    stealthSystem.UseSmokeBomb();
                    break;
                case 1: // Lockpick
                    stealthSystem.UseLockpick();
                    break;
                case 2: // Caltrops
                    stealthSystem.UseCaltrops();
                    break;
            }
        }
        #endregion

        #region Event Handlers
        private void OnStealthStateChanged(bool inStealth)
        {
            UpdateStealthStatus();

            if (inStealth)
                PlaySound(stealthEnterSound);
            else
                PlaySound(stealthExitSound);
        }

        private void OnDetectionLevelChanged(float detectionLevel)
        {
            if (detectionLevel > 0.7f && currentDetectionLevel <= 0.7f)
            {
                PlaySound(detectionWarningSound);
            }
        }

        private void OnEnemyAwarenessChanged(EnemyDetector enemy, DetectionState state)
        {
            // Update enemy indicator colors based on awareness state
            UpdateEnemyIndicators();
        }

        private void OnToolUsed(string toolName)
        {
            // Visual feedback for tool usage
            Debug.Log($"Used tool: {toolName}");
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class EnemyIndicatorUI : MonoBehaviour
    {
        [SerializeField] private Image indicatorImage;
        [SerializeField] private Image directionArrow;

        private EnemyDetector enemy;
        private Camera playerCamera;
        private RectTransform rectTransform;

        private void Awake()
        {
            rectTransform = GetComponent<RectTransform>();
        }

        public void Setup(EnemyDetector enemyDetector, Camera camera)
        {
            enemy = enemyDetector;
            playerCamera = camera;
        }

        private void Update()
        {
            if (enemy == null || playerCamera == null) return;

            UpdateIndicatorPosition();
            UpdateIndicatorColor();
        }

        private void UpdateIndicatorPosition()
        {
            Vector3 screenPos = playerCamera.WorldToScreenPoint(enemy.transform.position);

            if (screenPos.z > 0)
            {
                rectTransform.position = screenPos;
                directionArrow.gameObject.SetActive(false);
            }
            else
            {
                // Enemy is behind camera - show direction arrow
                Vector2 screenCenter = new Vector2(Screen.width * 0.5f, Screen.height * 0.5f);
                Vector2 direction = (new Vector2(screenPos.x, screenPos.y) - screenCenter).normalized;

                rectTransform.position = screenCenter + direction * 100f;
                directionArrow.gameObject.SetActive(true);
                directionArrow.transform.rotation = Quaternion.LookRotation(Vector3.forward, direction);
            }
        }

        private void UpdateIndicatorColor()
        {
            if (indicatorImage == null) return;

            switch (enemy.CurrentState)
            {
                case DetectionState.Calm:
                    indicatorImage.color = Color.green;
                    break;
                case DetectionState.Suspicious:
                    indicatorImage.color = Color.yellow;
                    break;
                case DetectionState.Alert:
                    indicatorImage.color = Color.red;
                    break;
                case DetectionState.Searching:
                    indicatorImage.color = Color.orange;
                    break;
            }
        }
    }

    public class ToolSlotUI : MonoBehaviour
    {
        [SerializeField] private Image toolIcon;
        [SerializeField] private TextMeshProUGUI toolNameText;
        [SerializeField] private TextMeshProUGUI hotkeyText;
        [SerializeField] private Button useButton;

        private string toolName;
        private KeyCode hotkey;

        public void Setup(string name, KeyCode key)
        {
            toolName = name;
            hotkey = key;

            if (toolNameText != null)
                toolNameText.text = toolName;

            if (hotkeyText != null)
                hotkeyText.text = hotkey != KeyCode.None ? hotkey.ToString() : "";

            if (useButton != null)
                useButton.onClick.AddListener(UseTool);
        }

        private void UseTool()
        {
            var stealthSystem = StealthSystem.Instance;
            if (stealthSystem == null) return;

            switch (toolName)
            {
                case "Smoke Bomb":
                    stealthSystem.UseSmokeBomb();
                    break;
                case "Lockpick":
                    stealthSystem.UseLockpick();
                    break;
                case "Caltrops":
                    stealthSystem.UseCaltrops();
                    break;
            }
        }
    }
    #endregion
}
