using UnityEngine;
using System.Collections;

/// <summary>
/// Central integration system for The Cinderborn's psychological journey
/// Connects all systems to create a cohesive psychological training experience
/// </summary>
public class CinderbornsJourneyIntegrator : MonoBehaviour
{
    [Header("System References")]
    public PsychologicalSystem psycheSystem;
    public PsychologicalUI psycheUI;
    public HostilitySystem hostilitySystem;
    public PlayerStats playerStats;
    public CharacterProgression characterProgression;
    public GameManager gameManager;
    
    [Header("Integration Settings")]
    public bool enablePsychologicalTraining = true;
    public bool enableMeditationSystem = true;
    public bool enableVisualEffects = true;
    public bool enableAudioEffects = true;
    
    [Header("Psychological Milestones")]
    public float traumaThresholdForHallucinations = 30f;
    public float enlightenmentThresholdForPeace = 40f;
    public float extremeTraumaThreshold = 80f;
    public float perfectBalanceThreshold = 10f; // Difference between sun/moon alignment
    
    private bool hasInitialized = false;
    private PsychologicalSystem.PsychologicalState lastPsychState;
    private float lastTraumaLevel = 0f;
    private float lastEnlightenmentLevel = 0f;
    
    void Start()
    {
        InitializeIntegration();
    }
    
    void InitializeIntegration()
    {
        // Find systems if not assigned
        if (psycheSystem == null)
            psycheSystem = FindObjectOfType<PsychologicalSystem>();
        
        if (psycheUI == null)
            psycheUI = FindObjectOfType<PsychologicalUI>();
        
        if (hostilitySystem == null)
            hostilitySystem = FindObjectOfType<HostilitySystem>();
        
        if (playerStats == null)
            playerStats = FindObjectOfType<PlayerStats>();
        
        if (characterProgression == null)
            characterProgression = FindObjectOfType<CharacterProgression>();
        
        if (gameManager == null)
            gameManager = GameManager.Instance;
        
        // Subscribe to events
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnMoralPathChanged;
            playerStats.OnLevelUp += OnPlayerLevelUp;
        }
        
        if (psycheSystem != null)
        {
            lastPsychState = psycheSystem.GetCurrentState();
            lastTraumaLevel = psycheSystem.GetTrauma();
            lastEnlightenmentLevel = psycheSystem.enlightenment;
        }
        
        hasInitialized = true;
        Debug.Log("The Cinderborn's Journey Integration System initialized");
    }
    
    void Update()
    {
        if (!hasInitialized || !enablePsychologicalTraining) return;
        
        MonitorPsychologicalChanges();
        UpdateIntegratedSystems();
        CheckForMilestones();
    }
    
    void MonitorPsychologicalChanges()
    {
        if (psycheSystem == null) return;
        
        PsychologicalSystem.PsychologicalState currentState = psycheSystem.GetCurrentState();
        float currentTrauma = psycheSystem.GetTrauma();
        float currentEnlightenment = psycheSystem.enlightenment;
        
        // Check for state changes
        if (currentState != lastPsychState)
        {
            OnPsychologicalStateChanged(lastPsychState, currentState);
            lastPsychState = currentState;
        }
        
        // Check for significant trauma changes
        if (Mathf.Abs(currentTrauma - lastTraumaLevel) > 10f)
        {
            OnTraumaLevelChanged(lastTraumaLevel, currentTrauma);
            lastTraumaLevel = currentTrauma;
        }
        
        // Check for significant enlightenment changes
        if (Mathf.Abs(currentEnlightenment - lastEnlightenmentLevel) > 10f)
        {
            OnEnlightenmentLevelChanged(lastEnlightenmentLevel, currentEnlightenment);
            lastEnlightenmentLevel = currentEnlightenment;
        }
    }
    
    void UpdateIntegratedSystems()
    {
        // Update character progression based on psychological state
        UpdateCharacterProgression();
        
        // Update hostility system based on psychological state
        UpdateHostilitySystem();
        
        // Update UI elements
        UpdatePsychologicalUI();
    }
    
    void UpdateCharacterProgression()
    {
        if (characterProgression == null || psycheSystem == null) return;
        
        PsychologicalSystem.PsychologicalState state = psycheSystem.GetCurrentState();
        float trauma = psycheSystem.GetTrauma();
        
        // Age The Cinderborn based on trauma and story progress
        if (gameManager != null)
        {
            float storyProgress = gameManager.currentStoryProgress;
            float baseAge = 15f + (storyProgress * 10f); // 15 to 25 over story
            float traumaAging = trauma * 0.05f; // Trauma ages faster
            
            gameManager.cinderbornsAge = Mathf.Clamp(baseAge + traumaAging, 15f, 30f);
            
            // Update facial hair based on age and psychological state
            CharacterProgression.FacialHairStage hairStage = CalculateFacialHairStage(gameManager.cinderbornsAge, state);
            characterProgression.SetFacialHairStage(hairStage);
        }
        
        // Update hair color based on trauma
        if (trauma > 60f)
        {
            characterProgression.SetHairColor(CharacterProgression.HairColor.Gray);
        }
        else if (trauma > 80f)
        {
            characterProgression.SetHairColor(CharacterProgression.HairColor.White);
        }
        
        // Update scars based on psychological state
        if (state == PsychologicalSystem.PsychologicalState.Corrupted || 
            state == PsychologicalSystem.PsychologicalState.Hollow)
        {
            characterProgression.AddScar();
        }
    }
    
    CharacterProgression.FacialHairStage CalculateFacialHairStage(float age, PsychologicalSystem.PsychologicalState state)
    {
        // Base stage on age
        CharacterProgression.FacialHairStage baseStage;
        
        if (age < 17f)
            baseStage = CharacterProgression.FacialHairStage.Clean;
        else if (age < 20f)
            baseStage = CharacterProgression.FacialHairStage.Stubble;
        else if (age < 23f)
            baseStage = CharacterProgression.FacialHairStage.Mustache;
        else
            baseStage = CharacterProgression.FacialHairStage.Beard;
        
        // Modify based on psychological state
        switch (state)
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
            case PsychologicalSystem.PsychologicalState.Hollow:
                // Dark path grows facial hair faster (unkempt appearance)
                return (CharacterProgression.FacialHairStage)Mathf.Min((int)baseStage + 1, 3);
            
            case PsychologicalSystem.PsychologicalState.Enlightened:
                // Enlightened path maintains clean appearance
                return (CharacterProgression.FacialHairStage)Mathf.Max((int)baseStage - 1, 0);
            
            default:
                return baseStage;
        }
    }
    
    void UpdateHostilitySystem()
    {
        if (hostilitySystem == null || psycheSystem == null) return;
        
        PsychologicalSystem.PsychologicalState state = psycheSystem.GetCurrentState();
        
        // Modify reputation based on psychological state
        switch (state)
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
            case PsychologicalSystem.PsychologicalState.Hollow:
                hostilitySystem.fearLevel = Mathf.Min(hostilitySystem.fearLevel + Time.deltaTime * 2f, 100f);
                break;
            
            case PsychologicalSystem.PsychologicalState.Enlightened:
                hostilitySystem.respectLevel = Mathf.Min(hostilitySystem.respectLevel + Time.deltaTime * 1f, 100f);
                hostilitySystem.fearLevel = Mathf.Max(hostilitySystem.fearLevel - Time.deltaTime * 1f, 0f);
                break;
        }
    }
    
    void UpdatePsychologicalUI()
    {
        if (psycheUI == null || psycheSystem == null) return;
        
        // The UI system handles its own updates, but we can trigger special effects here
        PsychologicalSystem.PsychologicalState state = psycheSystem.GetCurrentState();
        
        if (state == PsychologicalSystem.PsychologicalState.Corrupted && Random.Range(0f, 1f) < 0.01f)
        {
            psycheUI.TriggerVisionBlur();
        }
    }
    
    void CheckForMilestones()
    {
        if (psycheSystem == null || gameManager == null) return;
        
        float trauma = psycheSystem.GetTrauma();
        float enlightenment = psycheSystem.enlightenment;
        PsychologicalSystem.PsychologicalState state = psycheSystem.GetCurrentState();
        
        // Track milestones in game manager
        if (trauma > gameManager.psychologicalJourney.highestTrauma)
        {
            gameManager.psychologicalJourney.highestTrauma = trauma;
        }
        
        if (enlightenment > gameManager.psychologicalJourney.highestEnlightenment)
        {
            gameManager.psychologicalJourney.highestEnlightenment = enlightenment;
        }
        
        // Check for extreme states
        if (state == PsychologicalSystem.PsychologicalState.Hollow && !gameManager.psychologicalJourney.hasReachedHollowState)
        {
            gameManager.psychologicalJourney.hasReachedHollowState = true;
            TriggerMilestone("The Cinderborn has reached the Hollow state - all hope seems lost.");
        }
        
        if (state == PsychologicalSystem.PsychologicalState.Enlightened && !gameManager.psychologicalJourney.hasReachedEnlightenedState)
        {
            gameManager.psychologicalJourney.hasReachedEnlightenedState = true;
            TriggerMilestone("The Cinderborn has achieved Enlightenment - inner peace has been found.");
        }
        
        // Check for perfect balance
        if (playerStats != null)
        {
            float sunAlignment = playerStats.GetSunAlignment();
            float moonAlignment = playerStats.GetMoonAlignment();
            float difference = Mathf.Abs(sunAlignment - Mathf.Abs(moonAlignment));
            
            if (difference < perfectBalanceThreshold && trauma < 20f)
            {
                TriggerMilestone("The Cinderborn has achieved perfect balance between light and shadow.");
            }
        }
    }
    
    void OnPsychologicalStateChanged(PsychologicalSystem.PsychologicalState oldState, PsychologicalSystem.PsychologicalState newState)
    {
        Debug.Log($"The Cinderborn's psychological state changed: {oldState} -> {newState}");
        
        // Trigger state-specific events
        switch (newState)
        {
            case PsychologicalSystem.PsychologicalState.Corrupted:
                TriggerCorruptionEvent();
                break;
            case PsychologicalSystem.PsychologicalState.Enlightened:
                TriggerEnlightenmentEvent();
                break;
            case PsychologicalSystem.PsychologicalState.Hollow:
                TriggerHollowEvent();
                break;
        }
        
        // Notify game manager
        if (gameManager != null && gameManager.OnPsychologicalStateChanged != null)
        {
            gameManager.OnPsychologicalStateChanged.Invoke(newState);
        }
    }
    
    void OnTraumaLevelChanged(float oldTrauma, float newTrauma)
    {
        if (newTrauma > oldTrauma)
        {
            Debug.Log($"The Cinderborn's trauma increased: {oldTrauma:F1} -> {newTrauma:F1}");
            
            if (newTrauma > extremeTraumaThreshold)
            {
                TriggerExtremeTraumaEvent();
            }
        }
        else
        {
            Debug.Log($"The Cinderborn's trauma decreased: {oldTrauma:F1} -> {newTrauma:F1}");
        }
    }
    
    void OnEnlightenmentLevelChanged(float oldEnlightenment, float newEnlightenment)
    {
        if (newEnlightenment > oldEnlightenment)
        {
            Debug.Log($"The Cinderborn's enlightenment increased: {oldEnlightenment:F1} -> {newEnlightenment:F1}");
            
            if (newEnlightenment > enlightenmentThresholdForPeace)
            {
                TriggerPeaceEvent();
            }
        }
    }
    
    void OnMoralPathChanged(PlayerStats.MoralPath newPath)
    {
        Debug.Log($"The Cinderborn's moral path changed to: {newPath}");
        
        // Track moral choices
        if (gameManager != null)
        {
            switch (newPath)
            {
                case PlayerStats.MoralPath.Sun:
                    gameManager.psychologicalJourney.lightChoicesMade++;
                    break;
                case PlayerStats.MoralPath.Moon:
                    gameManager.psychologicalJourney.darkChoicesMade++;
                    break;
            }
        }
    }
    
    void OnPlayerLevelUp(int newLevel)
    {
        // Psychological growth with level progression
        if (psycheSystem != null)
        {
            psycheSystem.ReduceTrauma(2f, "Growth through experience");
        }
    }
    
    void TriggerCorruptionEvent()
    {
        ShowPsychologicalMessage("The darkness seeps deeper into The Cinderborn's soul...");
        
        if (enableVisualEffects && psycheUI != null)
        {
            psycheUI.ShowWhisperText("You are becoming what you were meant to be...");
        }
    }
    
    void TriggerEnlightenmentEvent()
    {
        ShowPsychologicalMessage("Light breaks through the darkness in The Cinderborn's heart...");
        
        if (enableVisualEffects && psycheUI != null)
        {
            psycheUI.ShowHopefulText("There is still good in this world, and you are part of it.");
        }
    }
    
    void TriggerHollowEvent()
    {
        ShowPsychologicalMessage("The Cinderborn's spirit has been broken by the weight of trauma...");
        
        if (enableVisualEffects && psycheUI != null)
        {
            psycheUI.ShowConflictedText("What am I becoming? Is there anything left of who I was?");
        }
    }
    
    void TriggerExtremeTraumaEvent()
    {
        ShowPsychologicalMessage("The Cinderborn teeters on the edge of complete psychological collapse...");
    }
    
    void TriggerPeaceEvent()
    {
        ShowPsychologicalMessage("The Cinderborn finds moments of peace amidst the chaos...");
    }
    
    void TriggerMilestone(string milestoneText)
    {
        ShowPsychologicalMessage($"MILESTONE: {milestoneText}");
        
        // Add to significant events
        if (gameManager != null)
        {
            System.Array.Resize(ref gameManager.psychologicalJourney.significantPsychologicalEvents, 
                               gameManager.psychologicalJourney.significantPsychologicalEvents.Length + 1);
            gameManager.psychologicalJourney.significantPsychologicalEvents[
                gameManager.psychologicalJourney.significantPsychologicalEvents.Length - 1] = milestoneText;
        }
    }
    
    void ShowPsychologicalMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"Psychological Event: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Public methods for external systems
    public void TriggerMeditation()
    {
        if (psycheSystem != null && psycheSystem.IsNearCampfire())
        {
            if (gameManager != null)
            {
                gameManager.psychologicalJourney.totalMeditations++;
            }
        }
    }
    
    public void TriggerHallucination()
    {
        if (gameManager != null)
        {
            gameManager.psychologicalJourney.hallucinationsExperienced++;
        }
    }
    
    public void TriggerHopefulMoment()
    {
        if (gameManager != null)
        {
            gameManager.psychologicalJourney.hopefulMomentsExperienced++;
        }
    }
    
    // Getters for external systems
    public bool IsInDarkPsychologicalState()
    {
        if (psycheSystem == null) return false;
        
        PsychologicalSystem.PsychologicalState state = psycheSystem.GetCurrentState();
        return state == PsychologicalSystem.PsychologicalState.Corrupted || 
               state == PsychologicalSystem.PsychologicalState.Hollow;
    }
    
    public bool IsInLightPsychologicalState()
    {
        if (psycheSystem == null) return false;
        
        return psycheSystem.GetCurrentState() == PsychologicalSystem.PsychologicalState.Enlightened;
    }
    
    public float GetPsychologicalBalance()
    {
        if (psycheSystem == null) return 0f;
        
        float trauma = psycheSystem.GetTrauma();
        float enlightenment = psycheSystem.enlightenment;
        
        return (enlightenment - trauma) / 100f; // -1 to 1 scale
    }
}
