using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using CinderOfDarkness.Dialogue;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Dialogue UI for Cinder of Darkness.
    /// Handles conversation display, choice selection, and character portraits.
    /// </summary>
    public class DialogueUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject dialoguePanel;
        [SerializeField] private TextMeshProUGUI speakerNameText;
        [SerializeField] private TextMeshProUGUI dialogueText;
        [SerializeField] private Image speakerPortrait;
        [SerializeField] private Transform choicesContainer;
        [SerializeField] private GameObject choiceButtonPrefab;
        [SerializeField] private Button continueButton;
        [SerializeField] private Button skipButton;

        [Header("Animation")]
        [SerializeField] private float typewriterSpeed = 0.05f;
        [SerializeField] private AnimationCurve fadeInCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float fadeInDuration = 0.5f;

        [Header("Character Portraits")]
        [SerializeField] private Sprite defaultPortrait;
        [SerializeField] private CharacterPortrait[] characterPortraits;

        [Header("Audio")]
        [SerializeField] private AudioClip dialogueStartSound;
        [SerializeField] private AudioClip dialogueEndSound;
        [SerializeField] private AudioClip choiceSelectSound;
        [SerializeField] private AudioClip typewriterSound;
        [SerializeField] private AudioSource audioSource;

        [Header("Localization")]
        [SerializeField] private bool enableRTL = false;
        [SerializeField] private Font arabicFont;
        [SerializeField] private Font englishFont;
        #endregion

        #region Private Fields
        private AdvancedDialogueSystem dialogueSystem;
        private List<GameObject> choiceButtons = new List<GameObject>();
        private Coroutine typewriterCoroutine;
        private bool isTyping = false;
        private bool isVisible = false;
        private string currentFullText = "";
        private DialogueNode currentNode;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            dialogueSystem = AdvancedDialogueSystem.Instance;
        }

        private void SetupUI()
        {
            if (dialoguePanel != null)
                dialoguePanel.SetActive(false);

            SetupLocalization();
        }

        private void SetupEventListeners()
        {
            if (continueButton != null)
                continueButton.onClick.AddListener(OnContinueClicked);

            if (skipButton != null)
                skipButton.onClick.AddListener(OnSkipClicked);

            // Subscribe to dialogue system events
            if (dialogueSystem != null)
            {
                dialogueSystem.OnDialogueStarted += OnDialogueStarted;
                dialogueSystem.OnDialogueEnded += OnDialogueEnded;
                dialogueSystem.OnDialogueNodeChanged += OnDialogueNodeChanged;
            }
        }

        private void SetupLocalization()
        {
            var localizationManager = FindObjectOfType<LocalizationManager>();
            if (localizationManager != null)
            {
                enableRTL = localizationManager.GetCurrentLanguage() == "Arabic";
                UpdateFontForLanguage();
            }
        }

        private void UpdateFontForLanguage()
        {
            Font targetFont = enableRTL ? arabicFont : englishFont;
            
            if (targetFont != null)
            {
                if (speakerNameText != null)
                    speakerNameText.font = targetFont;
                
                if (dialogueText != null)
                {
                    dialogueText.font = targetFont;
                    dialogueText.isRightToLeftText = enableRTL;
                }
            }
        }
        #endregion

        #region Dialogue Display
        public void ShowDialogue(DialogueNode node)
        {
            currentNode = node;
            
            if (!isVisible)
            {
                ShowDialoguePanel();
            }

            UpdateSpeakerInfo(node.speakerId);
            DisplayDialogueText(node.text);
            CreateChoiceButtons(node.choices);
        }

        private void ShowDialoguePanel()
        {
            if (dialoguePanel != null)
            {
                dialoguePanel.SetActive(true);
                isVisible = true;
                StartCoroutine(FadeInPanel());
                PlaySound(dialogueStartSound);
            }
        }

        private void HideDialoguePanel()
        {
            if (dialoguePanel != null)
            {
                dialoguePanel.SetActive(false);
                isVisible = false;
                PlaySound(dialogueEndSound);
            }
        }

        private IEnumerator FadeInPanel()
        {
            CanvasGroup canvasGroup = dialoguePanel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = dialoguePanel.AddComponent<CanvasGroup>();

            float elapsed = 0f;
            while (elapsed < fadeInDuration)
            {
                elapsed += Time.deltaTime;
                float alpha = fadeInCurve.Evaluate(elapsed / fadeInDuration);
                canvasGroup.alpha = alpha;
                yield return null;
            }
            canvasGroup.alpha = 1f;
        }

        private void UpdateSpeakerInfo(string speakerId)
        {
            var portrait = GetCharacterPortrait(speakerId);
            
            if (speakerNameText != null)
                speakerNameText.text = GetCharacterName(speakerId);

            if (speakerPortrait != null)
                speakerPortrait.sprite = portrait ?? defaultPortrait;
        }

        private void DisplayDialogueText(string text)
        {
            currentFullText = text;
            
            if (typewriterCoroutine != null)
                StopCoroutine(typewriterCoroutine);

            typewriterCoroutine = StartCoroutine(TypewriterEffect(text));
        }

        private IEnumerator TypewriterEffect(string text)
        {
            isTyping = true;
            dialogueText.text = "";

            for (int i = 0; i <= text.Length; i++)
            {
                dialogueText.text = text.Substring(0, i);
                
                if (i < text.Length)
                {
                    PlaySound(typewriterSound);
                    yield return new WaitForSeconds(typewriterSpeed);
                }
            }

            isTyping = false;
            UpdateContinueButton();
        }

        private void CreateChoiceButtons(DialogueChoice[] choices)
        {
            ClearChoiceButtons();

            if (choices == null || choices.Length == 0)
            {
                // No choices - show continue button
                if (continueButton != null)
                    continueButton.gameObject.SetActive(true);
                return;
            }

            // Hide continue button when choices are available
            if (continueButton != null)
                continueButton.gameObject.SetActive(false);

            foreach (var choice in choices)
            {
                CreateChoiceButton(choice);
            }
        }

        private void CreateChoiceButton(DialogueChoice choice)
        {
            if (choiceButtonPrefab == null || choicesContainer == null) return;

            GameObject buttonObj = Instantiate(choiceButtonPrefab, choicesContainer);
            Button button = buttonObj.GetComponent<Button>();
            TextMeshProUGUI buttonText = buttonObj.GetComponentInChildren<TextMeshProUGUI>();

            if (buttonText != null)
            {
                buttonText.text = choice.text;
                buttonText.font = enableRTL ? arabicFont : englishFont;
                buttonText.isRightToLeftText = enableRTL;
            }

            if (button != null)
            {
                button.onClick.AddListener(() => OnChoiceSelected(choice));
            }

            choiceButtons.Add(buttonObj);
        }

        private void ClearChoiceButtons()
        {
            foreach (var button in choiceButtons)
            {
                if (button != null)
                    Destroy(button);
            }
            choiceButtons.Clear();
        }

        private void UpdateContinueButton()
        {
            if (continueButton != null)
            {
                continueButton.interactable = !isTyping;
            }
        }
        #endregion

        #region Input Handling
        private void HandleInput()
        {
            if (!isVisible) return;

            if (Input.GetKeyDown(KeyCode.Space) || Input.GetKeyDown(KeyCode.Return))
            {
                if (isTyping)
                {
                    OnSkipClicked();
                }
                else
                {
                    OnContinueClicked();
                }
            }

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                OnDialogueEnded();
            }

            // Number key shortcuts for choices
            for (int i = 1; i <= choiceButtons.Count && i <= 9; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha0 + i))
                {
                    var button = choiceButtons[i - 1].GetComponent<Button>();
                    if (button != null && button.interactable)
                    {
                        button.onClick.Invoke();
                    }
                }
            }
        }
        #endregion

        #region Event Handlers
        private void OnDialogueStarted(string conversationId)
        {
            // Dialogue panel will be shown when first node is displayed
        }

        private void OnDialogueEnded()
        {
            HideDialoguePanel();
            ClearChoiceButtons();
            
            if (typewriterCoroutine != null)
            {
                StopCoroutine(typewriterCoroutine);
                typewriterCoroutine = null;
            }
        }

        private void OnDialogueNodeChanged(DialogueNode node)
        {
            ShowDialogue(node);
        }

        private void OnContinueClicked()
        {
            if (isTyping) return;

            if (dialogueSystem != null)
            {
                dialogueSystem.ContinueDialogue();
            }
        }

        private void OnSkipClicked()
        {
            if (isTyping && typewriterCoroutine != null)
            {
                StopCoroutine(typewriterCoroutine);
                dialogueText.text = currentFullText;
                isTyping = false;
                UpdateContinueButton();
            }
        }

        private void OnChoiceSelected(DialogueChoice choice)
        {
            PlaySound(choiceSelectSound);
            
            if (dialogueSystem != null)
            {
                dialogueSystem.SelectChoice(choice);
            }
        }
        #endregion

        #region Helper Methods
        private Sprite GetCharacterPortrait(string speakerId)
        {
            foreach (var portrait in characterPortraits)
            {
                if (portrait.characterId == speakerId)
                    return portrait.portrait;
            }
            return defaultPortrait;
        }

        private string GetCharacterName(string speakerId)
        {
            foreach (var portrait in characterPortraits)
            {
                if (portrait.characterId == speakerId)
                    return portrait.characterName;
            }
            return speakerId;
        }

        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    [System.Serializable]
    public class CharacterPortrait
    {
        public string characterId;
        public string characterName;
        public Sprite portrait;
    }
    #endregion
}
