using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;
using CinderOfDarkness.Magic;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Magic Tree UI for Cinder of Darkness.
    /// Displays spell progression, traits, and magical abilities.
    /// </summary>
    public class MagicTreeUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI References")]
        [SerializeField] private GameObject magicTreePanel;
        [SerializeField] private Transform spellNodesContainer;
        [SerializeField] private GameObject spellNodePrefab;
        [SerializeField] private Button closeButton;
        [SerializeField] private TextMeshProUGUI magicPointsText;
        [SerializeField] private Slider magicPointsSlider;

        [Header("Spell Details")]
        [SerializeField] private GameObject spellDetailsPanel;
        [SerializeField] private TextMeshProUGUI spellNameText;
        [SerializeField] private TextMeshProUGUI spellDescriptionText;
        [SerializeField] private TextMeshProUGUI spellLevelText;
        [SerializeField] private TextMeshProUGUI spellExperienceText;
        [SerializeField] private Image spellIcon;
        [SerializeField] private Transform traitsContainer;
        [SerializeField] private GameObject traitPrefab;

        [Header("Element Tabs")]
        [SerializeField] private Button fireTabButton;
        [SerializeField] private Button waterTabButton;
        [SerializeField] private Button earthTabButton;
        [SerializeField] private Button airTabButton;
        [SerializeField] private Button darkTabButton;
        [SerializeField] private Button lightTabButton;
        [SerializeField] private Image elementIndicator;

        [Header("Spell Actions")]
        [SerializeField] private Button learnSpellButton;
        [SerializeField] private Button upgradeSpellButton;
        [SerializeField] private Button equipSpellButton;
        [SerializeField] private TextMeshProUGUI learnCostText;

        [Header("Audio")]
        [SerializeField] private AudioClip openSound;
        [SerializeField] private AudioClip closeSound;
        [SerializeField] private AudioClip spellSelectSound;
        [SerializeField] private AudioClip spellLearnSound;
        [SerializeField] private AudioClip tabSwitchSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private MagicEvolutionSystem magicSystem;
        private List<GameObject> spellNodes = new List<GameObject>();
        private List<GameObject> traitNodes = new List<GameObject>();
        private ElementType currentElement = ElementType.Fire;
        private PlayerSpell selectedSpell;
        private bool isVisible = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
            UpdateMagicPointsDisplay();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            magicSystem = MagicEvolutionSystem.Instance;
        }

        private void SetupUI()
        {
            if (magicTreePanel != null)
                magicTreePanel.SetActive(false);

            if (spellDetailsPanel != null)
                spellDetailsPanel.SetActive(false);

            UpdateElementTabs();
            RefreshSpellTree();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseMagicTree);

            if (learnSpellButton != null)
                learnSpellButton.onClick.AddListener(LearnSelectedSpell);

            if (upgradeSpellButton != null)
                upgradeSpellButton.onClick.AddListener(UpgradeSelectedSpell);

            if (equipSpellButton != null)
                equipSpellButton.onClick.AddListener(EquipSelectedSpell);

            // Element tab buttons
            if (fireTabButton != null)
                fireTabButton.onClick.AddListener(() => SetElement(ElementType.Fire));

            if (waterTabButton != null)
                waterTabButton.onClick.AddListener(() => SetElement(ElementType.Water));

            if (earthTabButton != null)
                earthTabButton.onClick.AddListener(() => SetElement(ElementType.Earth));

            if (airTabButton != null)
                airTabButton.onClick.AddListener(() => SetElement(ElementType.Air));

            if (darkTabButton != null)
                darkTabButton.onClick.AddListener(() => SetElement(ElementType.Dark));

            if (lightTabButton != null)
                lightTabButton.onClick.AddListener(() => SetElement(ElementType.Light));

            // Subscribe to magic system events
            if (magicSystem != null)
            {
                magicSystem.OnSpellLearned += OnSpellLearned;
                magicSystem.OnSpellEvolved += OnSpellEvolved;
                magicSystem.OnTraitUnlocked += OnTraitUnlocked;
                magicSystem.OnMagicPointsChanged += OnMagicPointsChanged;
            }
        }
        #endregion

        #region UI Management
        public void ToggleMagicTree()
        {
            if (isVisible)
                CloseMagicTree();
            else
                OpenMagicTree();
        }

        public void OpenMagicTree()
        {
            if (magicTreePanel != null)
            {
                magicTreePanel.SetActive(true);
                isVisible = true;
                RefreshSpellTree();
                PlaySound(openSound);
            }
        }

        public void CloseMagicTree()
        {
            if (magicTreePanel != null)
            {
                magicTreePanel.SetActive(false);
                isVisible = false;
                PlaySound(closeSound);
            }

            if (spellDetailsPanel != null)
                spellDetailsPanel.SetActive(false);
        }

        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.K))
            {
                ToggleMagicTree();
            }

            if (Input.GetKeyDown(KeyCode.Escape) && isVisible)
            {
                CloseMagicTree();
            }

            // Element switching with number keys
            if (isVisible)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1)) SetElement(ElementType.Fire);
                if (Input.GetKeyDown(KeyCode.Alpha2)) SetElement(ElementType.Water);
                if (Input.GetKeyDown(KeyCode.Alpha3)) SetElement(ElementType.Earth);
                if (Input.GetKeyDown(KeyCode.Alpha4)) SetElement(ElementType.Air);
                if (Input.GetKeyDown(KeyCode.Alpha5)) SetElement(ElementType.Dark);
                if (Input.GetKeyDown(KeyCode.Alpha6)) SetElement(ElementType.Light);
            }
        }

        private void SetElement(ElementType element)
        {
            currentElement = element;
            UpdateElementTabs();
            RefreshSpellTree();
            PlaySound(tabSwitchSound);
        }

        private void UpdateElementTabs()
        {
            // Update button states
            if (fireTabButton != null)
                fireTabButton.interactable = currentElement != ElementType.Fire;

            if (waterTabButton != null)
                waterTabButton.interactable = currentElement != ElementType.Water;

            if (earthTabButton != null)
                earthTabButton.interactable = currentElement != ElementType.Earth;

            if (airTabButton != null)
                airTabButton.interactable = currentElement != ElementType.Air;

            if (darkTabButton != null)
                darkTabButton.interactable = currentElement != ElementType.Dark;

            if (lightTabButton != null)
                lightTabButton.interactable = currentElement != ElementType.Light;

            // Update element indicator
            if (elementIndicator != null)
            {
                switch (currentElement)
                {
                    case ElementType.Fire:
                        elementIndicator.color = Color.red;
                        break;
                    case ElementType.Water:
                        elementIndicator.color = Color.blue;
                        break;
                    case ElementType.Earth:
                        elementIndicator.color = new Color(0.6f, 0.4f, 0.2f);
                        break;
                    case ElementType.Air:
                        elementIndicator.color = Color.cyan;
                        break;
                    case ElementType.Dark:
                        elementIndicator.color = Color.black;
                        break;
                    case ElementType.Light:
                        elementIndicator.color = Color.yellow;
                        break;
                }
            }
        }
        #endregion

        #region Spell Tree Display
        private void RefreshSpellTree()
        {
            ClearSpellNodes();

            if (magicSystem == null) return;

            var spells = GetSpellsForElement(currentElement);
            foreach (var spell in spells)
            {
                CreateSpellNode(spell);
            }

            // Update connections between spells
            UpdateSpellConnections();
        }

        private List<PlayerSpell> GetSpellsForElement(ElementType element)
        {
            if (magicSystem == null) return new List<PlayerSpell>();

            return magicSystem.LearnedSpells.Values
                .Where(s => s.spellData.element == element)
                .ToList();
        }

        private void CreateSpellNode(PlayerSpell spell)
        {
            if (spellNodePrefab == null || spellNodesContainer == null) return;

            GameObject node = Instantiate(spellNodePrefab, spellNodesContainer);
            var spellNode = node.GetComponent<SpellNodeUI>();

            if (spellNode != null)
            {
                spellNode.Setup(spell, this);
                spellNodes.Add(node);
            }

            // Position node based on spell tier/level
            PositionSpellNode(node, spell);
        }

        private void PositionSpellNode(GameObject node, PlayerSpell spell)
        {
            // Position based on spell level and element
            float x = spell.level * 150f;
            float y = GetElementYOffset(spell.spellData.element);
            
            node.transform.localPosition = new Vector3(x, y, 0);
        }

        private float GetElementYOffset(ElementType element)
        {
            switch (element)
            {
                case ElementType.Fire: return 100f;
                case ElementType.Water: return 50f;
                case ElementType.Earth: return 0f;
                case ElementType.Air: return -50f;
                case ElementType.Dark: return -100f;
                case ElementType.Light: return -150f;
                default: return 0f;
            }
        }

        private void UpdateSpellConnections()
        {
            // Draw lines between connected spells
            // This would use Unity's LineRenderer or UI lines
        }

        private void ClearSpellNodes()
        {
            foreach (var node in spellNodes)
            {
                if (node != null)
                    Destroy(node);
            }
            spellNodes.Clear();
        }

        public void SelectSpell(PlayerSpell spell)
        {
            selectedSpell = spell;
            UpdateSpellDetails();
            PlaySound(spellSelectSound);
        }

        private void UpdateSpellDetails()
        {
            if (selectedSpell == null)
            {
                if (spellDetailsPanel != null)
                    spellDetailsPanel.SetActive(false);
                return;
            }

            if (spellDetailsPanel != null)
                spellDetailsPanel.SetActive(true);

            if (spellNameText != null)
                spellNameText.text = selectedSpell.spellData.spellName;

            if (spellDescriptionText != null)
                spellDescriptionText.text = selectedSpell.spellData.description;

            if (spellLevelText != null)
                spellLevelText.text = $"Level: {selectedSpell.level}";

            if (spellExperienceText != null)
            {
                float nextLevelExp = 100f * (selectedSpell.level + 1);
                spellExperienceText.text = $"Experience: {selectedSpell.experience:F0}/{nextLevelExp:F0}";
            }

            if (spellIcon != null)
                spellIcon.sprite = selectedSpell.spellData.icon;

            UpdateTraitDisplay();
            UpdateActionButtons();
        }

        private void UpdateTraitDisplay()
        {
            ClearTraitNodes();

            if (selectedSpell == null) return;

            foreach (var traitId in selectedSpell.unlockedTraits)
            {
                CreateTraitNode(traitId);
            }
        }

        private void CreateTraitNode(string traitId)
        {
            if (traitPrefab == null || traitsContainer == null) return;

            GameObject node = Instantiate(traitPrefab, traitsContainer);
            var traitNode = node.GetComponent<TraitNodeUI>();

            if (traitNode != null)
            {
                // Get trait data and setup node
                traitNode.Setup(traitId);
                traitNodes.Add(node);
            }
        }

        private void ClearTraitNodes()
        {
            foreach (var node in traitNodes)
            {
                if (node != null)
                    Destroy(node);
            }
            traitNodes.Clear();
        }

        private void UpdateActionButtons()
        {
            if (selectedSpell == null) return;

            bool isLearned = magicSystem.LearnedSpells.ContainsKey(selectedSpell.spellData.spellId);

            if (learnSpellButton != null)
            {
                learnSpellButton.gameObject.SetActive(!isLearned);
                if (learnCostText != null)
                    learnCostText.text = $"Cost: {selectedSpell.spellData.learnCost} MP";
            }

            if (upgradeSpellButton != null)
            {
                upgradeSpellButton.gameObject.SetActive(isLearned && selectedSpell.level < 10);
            }

            if (equipSpellButton != null)
            {
                equipSpellButton.gameObject.SetActive(isLearned);
            }
        }

        private void UpdateMagicPointsDisplay()
        {
            if (magicSystem == null) return;

            int currentMP = magicSystem.MagicPoints;

            if (magicPointsText != null)
                magicPointsText.text = $"Magic Points: {currentMP}";

            if (magicPointsSlider != null)
            {
                // Assuming max MP for display purposes
                int maxMP = 1000;
                magicPointsSlider.value = (float)currentMP / maxMP;
            }
        }
        #endregion

        #region Spell Actions
        private void LearnSelectedSpell()
        {
            if (selectedSpell == null || magicSystem == null) return;

            bool learned = magicSystem.LearnSpell(selectedSpell.spellData.spellId);
            if (learned)
            {
                PlaySound(spellLearnSound);
                UpdateSpellDetails();
            }
        }

        private void UpgradeSelectedSpell()
        {
            if (selectedSpell == null || magicSystem == null) return;

            // This would trigger spell evolution
            Debug.Log($"Upgrading spell: {selectedSpell.spellData.spellName}");
        }

        private void EquipSelectedSpell()
        {
            if (selectedSpell == null) return;

            // This would equip the spell to a hotbar slot
            Debug.Log($"Equipped spell: {selectedSpell.spellData.spellName}");
        }
        #endregion

        #region Event Handlers
        private void OnSpellLearned(PlayerSpell spell)
        {
            if (isVisible && spell.spellData.element == currentElement)
            {
                RefreshSpellTree();
            }
        }

        private void OnSpellEvolved(PlayerSpell spell, int newLevel)
        {
            if (isVisible && selectedSpell != null && 
                selectedSpell.spellData.spellId == spell.spellData.spellId)
            {
                UpdateSpellDetails();
            }
        }

        private void OnTraitUnlocked(SpellTrait trait)
        {
            if (isVisible && selectedSpell != null)
            {
                UpdateTraitDisplay();
            }
        }

        private void OnMagicPointsChanged(int newAmount)
        {
            // Magic points display is updated in Update()
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }

    #region Supporting Classes
    public class SpellNodeUI : MonoBehaviour
    {
        [SerializeField] private Image spellIcon;
        [SerializeField] private TextMeshProUGUI spellNameText;
        [SerializeField] private TextMeshProUGUI levelText;
        [SerializeField] private Button selectButton;
        [SerializeField] private Image backgroundImage;

        private PlayerSpell spell;
        private MagicTreeUI magicTreeUI;

        public void Setup(PlayerSpell spellData, MagicTreeUI treeUI)
        {
            spell = spellData;
            magicTreeUI = treeUI;

            if (spellIcon != null)
                spellIcon.sprite = spell.spellData.icon;

            if (spellNameText != null)
                spellNameText.text = spell.spellData.spellName;

            if (levelText != null)
                levelText.text = spell.level.ToString();

            if (selectButton != null)
                selectButton.onClick.AddListener(() => magicTreeUI.SelectSpell(spell));

            // Color based on element
            if (backgroundImage != null)
            {
                switch (spell.spellData.element)
                {
                    case ElementType.Fire:
                        backgroundImage.color = new Color(1f, 0.3f, 0.3f, 0.8f);
                        break;
                    case ElementType.Water:
                        backgroundImage.color = new Color(0.3f, 0.3f, 1f, 0.8f);
                        break;
                    case ElementType.Earth:
                        backgroundImage.color = new Color(0.6f, 0.4f, 0.2f, 0.8f);
                        break;
                    case ElementType.Air:
                        backgroundImage.color = new Color(0.3f, 1f, 1f, 0.8f);
                        break;
                    case ElementType.Dark:
                        backgroundImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
                        break;
                    case ElementType.Light:
                        backgroundImage.color = new Color(1f, 1f, 0.3f, 0.8f);
                        break;
                }
            }
        }
    }

    public class TraitNodeUI : MonoBehaviour
    {
        [SerializeField] private Image traitIcon;
        [SerializeField] private TextMeshProUGUI traitNameText;

        public void Setup(string traitId)
        {
            if (traitNameText != null)
                traitNameText.text = traitId.Replace("_", " ");

            // Set icon based on trait type
            // This would use a trait database
        }
    }
    #endregion
}
