using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class ForbiddenWordsSystem : MonoBehaviour
{
    [Header("Forbidden Words")]
    public List<ForbiddenWord> forbiddenWords = new List<ForbiddenWord>();
    public List<SacredLocation> sacredLocations = new List<SacredLocation>();
    
    [Header("Consequence Tracking")]
    public int totalViolations = 0;
    public float curseLevel = 0f;
    public List<string> triggeredCurses = new List<string>();
    public List<string> unlockedSecrets = new List<string>();
    
    [Header("Audio & Visual")]
    public AudioClip[] curseActivationSounds;
    public AudioClip[] secretUnlockSounds;
    public ParticleSystem curseEffect;
    public ParticleSystem secretEffect;
    
    private PsychologicalSystem psycheSystem;
    private HostilitySystem hostilitySystem;
    private GameManager gameManager;
    private bool isListeningForWords = true;
    
    [System.Serializable]
    public class ForbiddenWord
    {
        [Header("Word Identity")]
        public string word;
        public string[] variations; // Different forms of the word
        public ForbiddenType type;
        public SeverityLevel severity;
        
        [Header("Context")]
        public string[] forbiddenInLocations;
        public string[] allowedInLocations;
        public bool requiresSpecificNPC;
        public string specificNPCName;
        
        [Header("Consequences")]
        public ConsequenceType consequenceType;
        public float consequenceMagnitude;
        public string consequenceDescription;
        public string[] triggeredEvents;
        
        [Header("Lore")]
        public string wordOrigin;
        public string whyForbidden;
        public string culturalSignificance;
        
        public enum ForbiddenType
        {
            Blasphemy,      // Religious/sacred violations
            AncientName,    // Names that should not be spoken
            CursedPhrase,   // Phrases that invoke dark magic
            SecretWord,     // Words that unlock hidden areas
            TabooSubject,   // Culturally forbidden topics
            TrueName        // True names of powerful beings
        }
        
        public enum SeverityLevel
        {
            Minor,          // Small consequences
            Moderate,       // Noticeable effects
            Severe,         // Major consequences
            Catastrophic    // World-changing effects
        }
        
        public enum ConsequenceType
        {
            Curse,
            Hostility,
            SecretUnlock,
            AreaSeal,
            NPCReaction,
            WorldChange,
            PowerLoss,
            PowerGain
        }
    }
    
    [System.Serializable]
    public class SacredLocation
    {
        public string locationName;
        public GameObject locationObject;
        public LocationType type;
        public float sanctityLevel;
        public string[] protectedWords;
        public string[] guardianSpirits;
        
        public enum LocationType
        {
            Temple,
            Graveyard,
            AncientRuin,
            SacredGrove,
            ForbiddenLibrary,
            DemonSummoning,
            HolyShrine
        }
    }
    
    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        hostilitySystem = GetComponent<HostilitySystem>();
        gameManager = GameManager.Instance;
        
        InitializeForbiddenWords();
        InitializeSacredLocations();
    }
    
    void Update()
    {
        if (isListeningForWords)
        {
            CheckForForbiddenWordInput();
        }
    }
    
    void InitializeForbiddenWords()
    {
        // Blasphemous words in sacred places
        ForbiddenWord blasphemy = new ForbiddenWord
        {
            word = "VOID",
            variations = new string[] { "void", "Void", "VOID", "the void", "endless void" },
            type = ForbiddenWord.ForbiddenType.Blasphemy,
            severity = ForbiddenWord.SeverityLevel.Severe,
            forbiddenInLocations = new string[] { "Temple", "HolyShrine", "Graveyard" },
            consequenceType = ForbiddenWord.ConsequenceType.Curse,
            consequenceMagnitude = 50f,
            consequenceDescription = "Speaking of the void in sacred places invokes the curse of emptiness",
            wordOrigin = "The primordial emptiness before creation",
            whyForbidden = "The void seeks to reclaim all that exists",
            culturalSignificance = "Represents the ultimate fear of nothingness"
        };
        
        // Ancient names that should not be spoken
        ForbiddenWord ancientName = new ForbiddenWord
        {
            word = "MALACHAR",
            variations = new string[] { "Malachar", "MALACHAR", "malachar the destroyer" },
            type = ForbiddenWord.ForbiddenType.AncientName,
            severity = ForbiddenWord.SeverityLevel.Catastrophic,
            consequenceType = ForbiddenWord.ConsequenceType.WorldChange,
            consequenceMagnitude = 100f,
            consequenceDescription = "Speaking the true name of the Destroyer weakens the seals that bind him",
            triggeredEvents = new string[] { "DemonStirrings", "SealWeakening", "DarkOmens" },
            wordOrigin = "The true name of an ancient demon lord",
            whyForbidden = "His name holds power even in exile",
            culturalSignificance = "The ultimate taboo across all cultures"
        };
        
        // Secret words that unlock hidden areas
        ForbiddenWord secretWord = new ForbiddenWord
        {
            word = "ASHBORN",
            variations = new string[] { "ashborn", "Ashborn", "ASHBORN", "born of ash" },
            type = ForbiddenWord.ForbiddenType.SecretWord,
            severity = ForbiddenWord.SeverityLevel.Moderate,
            forbiddenInLocations = new string[] { "AncientRuin", "ForbiddenLibrary" },
            consequenceType = ForbiddenWord.ConsequenceType.SecretUnlock,
            consequenceMagnitude = 25f,
            consequenceDescription = "The ancient word of power opens sealed passages",
            triggeredEvents = new string[] { "SecretDoorOpen", "AncientKnowledgeRevealed" },
            wordOrigin = "The first word spoken by The Cinderborn's ancestors",
            whyForbidden = "It reveals the true heritage of The Cinderborn",
            culturalSignificance = "A word of power that shapes reality"
        };
        
        // Cursed phrases that invoke dark magic
        ForbiddenWord cursedPhrase = new ForbiddenWord
        {
            word = "BLOOD FOR POWER",
            variations = new string[] { "blood for power", "Blood for Power", "BLOOD FOR POWER" },
            type = ForbiddenWord.ForbiddenType.CursedPhrase,
            severity = ForbiddenWord.SeverityLevel.Severe,
            consequenceType = ForbiddenWord.ConsequenceType.PowerGain,
            consequenceMagnitude = 75f,
            consequenceDescription = "The dark pact grants power at a terrible cost",
            triggeredEvents = new string[] { "DarkPactFormed", "SoulCorruption", "PowerSurge" },
            wordOrigin = "The binding phrase of blood magic",
            whyForbidden = "It creates an unbreakable pact with dark forces",
            culturalSignificance = "The ultimate corruption of natural magic"
        };
        
        // Taboo subjects in certain cultures
        ForbiddenWord tabooSubject = new ForbiddenWord
        {
            word = "THE FIRST FLAME",
            variations = new string[] { "first flame", "First Flame", "THE FIRST FLAME", "original fire" },
            type = ForbiddenWord.ForbiddenType.TabooSubject,
            severity = ForbiddenWord.SeverityLevel.Minor,
            requiresSpecificNPC = true,
            specificNPCName = "Drenari Elder",
            consequenceType = ForbiddenWord.ConsequenceType.NPCReaction,
            consequenceMagnitude = 30f,
            consequenceDescription = "The Drenari consider discussion of the First Flame to be sacrilege",
            wordOrigin = "The source of all fire magic",
            whyForbidden = "The Drenari believe it should remain unspoken",
            culturalSignificance = "Sacred to fire-worshipping cultures"
        };
        
        forbiddenWords.Add(blasphemy);
        forbiddenWords.Add(ancientName);
        forbiddenWords.Add(secretWord);
        forbiddenWords.Add(cursedPhrase);
        forbiddenWords.Add(tabooSubject);
        
        Debug.Log($"Initialized {forbiddenWords.Count} forbidden words - speak carefully in sacred places");
    }
    
    void InitializeSacredLocations()
    {
        // Find and categorize sacred locations
        GameObject[] temples = GameObject.FindGameObjectsWithTag("Temple");
        GameObject[] graveyards = GameObject.FindGameObjectsWithTag("Graveyard");
        GameObject[] ruins = GameObject.FindGameObjectsWithTag("AncientRuin");
        
        foreach (GameObject temple in temples)
        {
            SacredLocation location = new SacredLocation
            {
                locationName = temple.name,
                locationObject = temple,
                type = SacredLocation.LocationType.Temple,
                sanctityLevel = 100f,
                protectedWords = new string[] { "VOID", "MALACHAR", "BLOOD FOR POWER" },
                guardianSpirits = new string[] { "Temple Guardian", "Sacred Flame" }
            };
            sacredLocations.Add(location);
        }
        
        foreach (GameObject graveyard in graveyards)
        {
            SacredLocation location = new SacredLocation
            {
                locationName = graveyard.name,
                locationObject = graveyard,
                type = SacredLocation.LocationType.Graveyard,
                sanctityLevel = 75f,
                protectedWords = new string[] { "VOID", "MALACHAR" },
                guardianSpirits = new string[] { "Restless Spirits", "Guardian Wraith" }
            };
            sacredLocations.Add(location);
        }
        
        foreach (GameObject ruin in ruins)
        {
            SacredLocation location = new SacredLocation
            {
                locationName = ruin.name,
                locationObject = ruin,
                type = SacredLocation.LocationType.AncientRuin,
                sanctityLevel = 50f,
                protectedWords = new string[] { "ASHBORN" },
                guardianSpirits = new string[] { "Ancient Watcher", "Stone Sentinel" }
            };
            sacredLocations.Add(location);
        }
    }
    
    void CheckForForbiddenWordInput()
    {
        // This would integrate with the dialogue system to check player input
        // For demonstration, we'll simulate word detection
        
        // In a real implementation, this would hook into the dialogue system
        // and check each player response for forbidden words
    }
    
    public void ProcessSpokenWord(string spokenText, string currentLocation, string nearbyNPC = "")
    {
        foreach (ForbiddenWord forbiddenWord in forbiddenWords)
        {
            if (ContainsForbiddenWord(spokenText, forbiddenWord))
            {
                if (IsWordForbiddenInContext(forbiddenWord, currentLocation, nearbyNPC))
                {
                    TriggerForbiddenWordConsequence(forbiddenWord, currentLocation);
                }
            }
        }
    }
    
    bool ContainsForbiddenWord(string text, ForbiddenWord forbiddenWord)
    {
        string lowerText = text.ToLower();
        
        // Check main word
        if (lowerText.Contains(forbiddenWord.word.ToLower()))
            return true;
        
        // Check variations
        foreach (string variation in forbiddenWord.variations)
        {
            if (lowerText.Contains(variation.ToLower()))
                return true;
        }
        
        return false;
    }
    
    bool IsWordForbiddenInContext(ForbiddenWord word, string location, string nearbyNPC)
    {
        // Check if word is forbidden in current location
        if (word.forbiddenInLocations != null)
        {
            foreach (string forbiddenLocation in word.forbiddenInLocations)
            {
                if (location.Contains(forbiddenLocation))
                    return true;
            }
        }
        
        // Check if word is allowed in current location (overrides forbidden)
        if (word.allowedInLocations != null)
        {
            foreach (string allowedLocation in word.allowedInLocations)
            {
                if (location.Contains(allowedLocation))
                    return false;
            }
        }
        
        // Check if specific NPC is required
        if (word.requiresSpecificNPC && !string.IsNullOrEmpty(word.specificNPCName))
        {
            return nearbyNPC.Contains(word.specificNPCName);
        }
        
        return false;
    }
    
    void TriggerForbiddenWordConsequence(ForbiddenWord word, string location)
    {
        totalViolations++;
        
        ShowForbiddenMessage($"You have spoken the forbidden word: {word.word}");
        
        StartCoroutine(ForbiddenWordSequence(word, location));
    }
    
    IEnumerator ForbiddenWordSequence(ForbiddenWord word, string location)
    {
        // Dramatic pause
        Time.timeScale = 0.5f;
        
        ShowForbiddenMessage($"The air grows heavy as the forbidden word echoes...");
        yield return new WaitForSecondsRealtime(2f);
        
        ShowForbiddenMessage(word.consequenceDescription);
        yield return new WaitForSecondsRealtime(3f);
        
        // Apply consequences
        ApplyWordConsequence(word, location);
        
        // Return to normal time
        Time.timeScale = 1f;
    }
    
    void ApplyWordConsequence(ForbiddenWord word, string location)
    {
        switch (word.consequenceType)
        {
            case ForbiddenWord.ConsequenceType.Curse:
                ApplyCurse(word);
                break;
            case ForbiddenWord.ConsequenceType.Hostility:
                ApplyHostility(word);
                break;
            case ForbiddenWord.ConsequenceType.SecretUnlock:
                UnlockSecret(word, location);
                break;
            case ForbiddenWord.ConsequenceType.NPCReaction:
                TriggerNPCReaction(word);
                break;
            case ForbiddenWord.ConsequenceType.WorldChange:
                TriggerWorldChange(word);
                break;
            case ForbiddenWord.ConsequenceType.PowerLoss:
                ApplyPowerLoss(word);
                break;
            case ForbiddenWord.ConsequenceType.PowerGain:
                ApplyPowerGain(word);
                break;
        }
        
        // Trigger associated events
        if (word.triggeredEvents != null)
        {
            foreach (string eventName in word.triggeredEvents)
            {
                gameManager?.TriggerEvent(eventName);
            }
        }
    }
    
    void ApplyCurse(ForbiddenWord word)
    {
        curseLevel += word.consequenceMagnitude;
        triggeredCurses.Add(word.word);
        
        // Apply curse effects
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(word.consequenceMagnitude, $"Cursed by speaking {word.word}");
        }
        
        // Visual effect
        if (curseEffect != null)
        {
            curseEffect.Play();
        }
        
        // Audio effect
        if (curseActivationSounds.Length > 0)
        {
            AudioSource.PlayClipAtPoint(curseActivationSounds[Random.Range(0, curseActivationSounds.Length)], 
                                      transform.position);
        }
        
        ShowForbiddenMessage($"A curse settles upon you. Curse Level: {curseLevel}");
    }
    
    void ApplyHostility(ForbiddenWord word)
    {
        if (hostilitySystem != null)
        {
            hostilitySystem.ModifyGlobalReputation(-word.consequenceMagnitude);
        }
        
        ShowForbiddenMessage("Your words have angered those around you.");
    }
    
    void UnlockSecret(ForbiddenWord word, string location)
    {
        unlockedSecrets.Add($"{word.word}_{location}");
        
        // Find and unlock secret areas
        GameObject[] secretDoors = GameObject.FindGameObjectsWithTag("SecretDoor");
        foreach (GameObject door in secretDoors)
        {
            if (Vector3.Distance(transform.position, door.transform.position) < 20f)
            {
                SecretDoor doorComponent = door.GetComponent<SecretDoor>();
                if (doorComponent != null && doorComponent.requiredWord == word.word)
                {
                    doorComponent.UnlockDoor();
                }
            }
        }
        
        // Visual effect
        if (secretEffect != null)
        {
            secretEffect.Play();
        }
        
        // Audio effect
        if (secretUnlockSounds.Length > 0)
        {
            AudioSource.PlayClipAtPoint(secretUnlockSounds[Random.Range(0, secretUnlockSounds.Length)], 
                                      transform.position);
        }
        
        ShowForbiddenMessage($"The word of power {word.word} has unlocked ancient secrets!");
    }
    
    void TriggerNPCReaction(ForbiddenWord word)
    {
        // Find nearby NPCs and trigger reactions
        NPCController[] nearbyNPCs = FindObjectsOfType<NPCController>();
        
        foreach (NPCController npc in nearbyNPCs)
        {
            float distance = Vector3.Distance(transform.position, npc.transform.position);
            if (distance < 10f)
            {
                npc.ReactToForbiddenWord(word.word, word.consequenceMagnitude);
            }
        }
        
        ShowForbiddenMessage("Your words have caused a strong reaction from those nearby.");
    }
    
    void TriggerWorldChange(ForbiddenWord word)
    {
        ShowForbiddenMessage($"The very fabric of reality trembles at the speaking of {word.word}");
        
        // Major world changes
        if (word.word == "MALACHAR")
        {
            // Weaken demon seals
            gameManager?.ModifyWorldState("DemonSealStrength", -word.consequenceMagnitude);
            ShowForbiddenMessage("Ancient seals weaken... something stirs in the depths...");
        }
    }
    
    void ApplyPowerLoss(ForbiddenWord word)
    {
        // Reduce player power temporarily or permanently
        PlayerStats playerStats = GetComponent<PlayerStats>();
        if (playerStats != null)
        {
            playerStats.ModifyMaxHealth(-word.consequenceMagnitude);
        }
        
        ShowForbiddenMessage($"Speaking {word.word} has drained your life force.");
    }
    
    void ApplyPowerGain(ForbiddenWord word)
    {
        // Grant dark power at a cost
        PlayerStats playerStats = GetComponent<PlayerStats>();
        if (playerStats != null)
        {
            playerStats.ModifyMaxHealth(word.consequenceMagnitude);
        }
        
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(word.consequenceMagnitude * 0.5f, "Power gained through dark means");
        }
        
        ShowForbiddenMessage($"Dark power flows through you, but at what cost?");
    }
    
    public void AddForbiddenWord(string word, ForbiddenWord.ForbiddenType type, ForbiddenWord.SeverityLevel severity)
    {
        ForbiddenWord newWord = new ForbiddenWord
        {
            word = word,
            type = type,
            severity = severity,
            consequenceType = ForbiddenWord.ConsequenceType.Curse,
            consequenceMagnitude = (float)severity * 25f
        };
        
        forbiddenWords.Add(newWord);
    }
    
    public void RemoveCurse(string curseWord)
    {
        if (triggeredCurses.Contains(curseWord))
        {
            triggeredCurses.Remove(curseWord);
            curseLevel = Mathf.Max(0f, curseLevel - 25f);
            ShowForbiddenMessage($"The curse of {curseWord} has been lifted.");
        }
    }
    
    void ShowForbiddenMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 5f));
        }
        
        Debug.Log($"Forbidden Words: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSecondsRealtime(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public int GetTotalViolations() => totalViolations;
    public float GetCurseLevel() => curseLevel;
    public List<string> GetTriggeredCurses() => triggeredCurses;
    public List<string> GetUnlockedSecrets() => unlockedSecrets;
    public List<ForbiddenWord> GetForbiddenWords() => forbiddenWords;
}

public class SecretDoor : MonoBehaviour
{
    public string requiredWord;
    public bool isUnlocked = false;
    public GameObject doorModel;
    public AudioClip unlockSound;
    
    public void UnlockDoor()
    {
        if (isUnlocked) return;
        
        isUnlocked = true;
        
        if (doorModel != null)
        {
            doorModel.SetActive(false);
        }
        
        if (unlockSound != null)
        {
            AudioSource.PlayClipAtPoint(unlockSound, transform.position);
        }
        
        Debug.Log($"Secret door unlocked by word: {requiredWord}");
    }
}
