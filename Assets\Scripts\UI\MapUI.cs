using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using CinderOfDarkness.Map;

namespace CinderOfDarkness.UI
{
    /// <summary>
    /// Map UI for Cinder of Darkness.
    /// Displays world map, mini-map, markers, and fog of war.
    /// </summary>
    public class MapUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Map Panel")]
        [SerializeField] private GameObject mapPanel;
        [SerializeField] private RawImage mapImage;
        [SerializeField] private Transform markersContainer;
        [SerializeField] private GameObject markerPrefab;
        [SerializeField] private Button closeButton;
        [SerializeField] private Slider zoomSlider;

        [Header("Mini Map")]
        [SerializeField] private GameObject miniMapPanel;
        [SerializeField] private RawImage miniMapImage;
        [SerializeField] private Transform miniMapMarkersContainer;
        [SerializeField] private GameObject miniMapMarkerPrefab;
        [SerializeField] private Image playerIndicator;
        [SerializeField] private Toggle miniMapToggle;

        [Header("Marker Creation")]
        [SerializeField] private Button addMarkerButton;
        [SerializeField] private TMP_InputField markerNameInput;
        [SerializeField] private TMP_Dropdown markerTypeDropdown;
        [SerializeField] private Button confirmMarkerButton;
        [SerializeField] private Button cancelMarkerButton;
        [SerializeField] private GameObject markerCreationPanel;

        [Header("Map Settings")]
        [SerializeField] private Vector2 mapSize = new Vector2(1024, 1024);
        [SerializeField] private float zoomMin = 0.5f;
        [SerializeField] private float zoomMax = 3f;
        [SerializeField] private float panSpeed = 2f;

        [Header("Audio")]
        [SerializeField] private AudioClip mapOpenSound;
        [SerializeField] private AudioClip mapCloseSound;
        [SerializeField] private AudioClip markerPlaceSound;
        [SerializeField] private AudioClip zoomSound;
        [SerializeField] private AudioSource audioSource;
        #endregion

        #region Private Fields
        private WorldMapSystem mapSystem;
        private Camera playerCamera;
        private Transform playerTransform;
        
        private List<GameObject> mapMarkers = new List<GameObject>();
        private List<GameObject> miniMapMarkers = new List<GameObject>();
        
        private bool isMapVisible = false;
        private bool isCreatingMarker = false;
        private Vector3 pendingMarkerPosition;
        private float currentZoom = 1f;
        private Vector2 mapOffset = Vector2.zero;
        
        // Input handling
        private bool isDragging = false;
        private Vector2 lastMousePosition;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }

        private void Start()
        {
            SetupReferences();
            SetupUI();
            SetupEventListeners();
        }

        private void Update()
        {
            HandleInput();
            UpdateMiniMap();
            UpdatePlayerIndicator();
        }
        #endregion

        #region Initialization
        private void SetupReferences()
        {
            mapSystem = WorldMapSystem.Instance;
            playerCamera = Camera.main;
            
            var player = FindObjectOfType<PlayerController>();
            if (player != null)
                playerTransform = player.transform;
        }

        private void SetupUI()
        {
            if (mapPanel != null)
                mapPanel.SetActive(false);

            if (miniMapPanel != null)
                miniMapPanel.SetActive(true);

            if (markerCreationPanel != null)
                markerCreationPanel.SetActive(false);

            SetupZoomSlider();
            SetupMarkerTypeDropdown();
            RefreshMapMarkers();
        }

        private void SetupEventListeners()
        {
            if (closeButton != null)
                closeButton.onClick.AddListener(CloseMap);

            if (addMarkerButton != null)
                addMarkerButton.onClick.AddListener(StartMarkerCreation);

            if (confirmMarkerButton != null)
                confirmMarkerButton.onClick.AddListener(ConfirmMarkerCreation);

            if (cancelMarkerButton != null)
                cancelMarkerButton.onClick.AddListener(CancelMarkerCreation);

            if (zoomSlider != null)
                zoomSlider.onValueChanged.AddListener(OnZoomChanged);

            if (miniMapToggle != null)
                miniMapToggle.onValueChanged.AddListener(OnMiniMapToggled);

            // Subscribe to map system events
            if (mapSystem != null)
            {
                mapSystem.OnMarkerAdded += OnMarkerAdded;
                mapSystem.OnMarkerRemoved += OnMarkerRemoved;
                mapSystem.OnFogOfWarUpdated += OnFogOfWarUpdated;
            }
        }

        private void SetupZoomSlider()
        {
            if (zoomSlider != null)
            {
                zoomSlider.minValue = zoomMin;
                zoomSlider.maxValue = zoomMax;
                zoomSlider.value = currentZoom;
            }
        }

        private void SetupMarkerTypeDropdown()
        {
            if (markerTypeDropdown != null)
            {
                markerTypeDropdown.ClearOptions();
                var options = new List<string> { "Custom", "Quest", "Shop", "Enemy", "Treasure", "Landmark" };
                markerTypeDropdown.AddOptions(options);
            }
        }
        #endregion

        #region Map Display
        public void ToggleMap()
        {
            if (isMapVisible)
                CloseMap();
            else
                OpenMap();
        }

        public void OpenMap()
        {
            if (mapPanel != null)
            {
                mapPanel.SetActive(true);
                isMapVisible = true;
                RefreshMapMarkers();
                PlaySound(mapOpenSound);
                
                // Pause game time
                Time.timeScale = 0f;
            }
        }

        public void CloseMap()
        {
            if (mapPanel != null)
            {
                mapPanel.SetActive(false);
                isMapVisible = false;
                PlaySound(mapCloseSound);
                
                // Resume game time
                Time.timeScale = 1f;
            }

            if (isCreatingMarker)
                CancelMarkerCreation();
        }

        private void UpdateMiniMap()
        {
            if (!miniMapPanel.activeInHierarchy) return;

            // Update mini map based on player position
            if (playerTransform != null && miniMapImage != null)
            {
                Vector2 playerPos = new Vector2(playerTransform.position.x, playerTransform.position.z);
                Vector2 normalizedPos = playerPos / mapSize;
                
                // Update mini map texture offset to center on player
                miniMapImage.uvRect = new Rect(
                    normalizedPos.x - 0.1f,
                    normalizedPos.y - 0.1f,
                    0.2f,
                    0.2f
                );
            }
        }

        private void UpdatePlayerIndicator()
        {
            if (playerIndicator != null && playerTransform != null)
            {
                // Update player indicator rotation
                playerIndicator.transform.rotation = Quaternion.Euler(0, 0, -playerTransform.eulerAngles.y);
            }
        }

        private void RefreshMapMarkers()
        {
            ClearMapMarkers();
            
            if (mapSystem == null) return;

            var markers = mapSystem.GetAllMarkers();
            foreach (var marker in markers)
            {
                CreateMapMarker(marker);
                CreateMiniMapMarker(marker);
            }
        }

        private void CreateMapMarker(MapMarker marker)
        {
            if (markerPrefab == null || markersContainer == null) return;

            GameObject markerObj = Instantiate(markerPrefab, markersContainer);
            var markerUI = markerObj.GetComponent<MapMarkerUI>();
            
            if (markerUI != null)
            {
                markerUI.Setup(marker);
                mapMarkers.Add(markerObj);
            }

            // Position marker on map
            Vector2 normalizedPos = WorldToMapPosition(marker.worldPosition);
            markerObj.transform.localPosition = new Vector3(
                normalizedPos.x * mapSize.x,
                normalizedPos.y * mapSize.y,
                0
            );
        }

        private void CreateMiniMapMarker(MapMarker marker)
        {
            if (miniMapMarkerPrefab == null || miniMapMarkersContainer == null) return;

            GameObject markerObj = Instantiate(miniMapMarkerPrefab, miniMapMarkersContainer);
            var markerUI = markerObj.GetComponent<MapMarkerUI>();
            
            if (markerUI != null)
            {
                markerUI.Setup(marker);
                miniMapMarkers.Add(markerObj);
            }

            // Position marker on mini map
            Vector2 normalizedPos = WorldToMapPosition(marker.worldPosition);
            markerObj.transform.localPosition = new Vector3(
                normalizedPos.x * 200f, // Mini map size
                normalizedPos.y * 200f,
                0
            );
        }

        private void ClearMapMarkers()
        {
            foreach (var marker in mapMarkers)
            {
                if (marker != null)
                    Destroy(marker);
            }
            mapMarkers.Clear();

            foreach (var marker in miniMapMarkers)
            {
                if (marker != null)
                    Destroy(marker);
            }
            miniMapMarkers.Clear();
        }

        private Vector2 WorldToMapPosition(Vector3 worldPos)
        {
            return new Vector2(
                (worldPos.x + mapSize.x * 0.5f) / mapSize.x,
                (worldPos.z + mapSize.y * 0.5f) / mapSize.y
            );
        }

        private Vector3 MapToWorldPosition(Vector2 mapPos)
        {
            return new Vector3(
                (mapPos.x * mapSize.x) - mapSize.x * 0.5f,
                0,
                (mapPos.y * mapSize.y) - mapSize.y * 0.5f
            );
        }
        #endregion

        #region Input Handling
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.M))
            {
                ToggleMap();
            }

            if (Input.GetKeyDown(KeyCode.Tab))
            {
                if (miniMapToggle != null)
                    miniMapToggle.isOn = !miniMapToggle.isOn;
            }

            if (!isMapVisible) return;

            HandleMapPanning();
            HandleMapZoom();
            HandleMarkerCreation();
        }

        private void HandleMapPanning()
        {
            if (Input.GetMouseButtonDown(0))
            {
                isDragging = true;
                lastMousePosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                isDragging = false;
            }

            if (isDragging)
            {
                Vector2 mouseDelta = (Vector2)Input.mousePosition - lastMousePosition;
                mapOffset += mouseDelta * panSpeed / currentZoom;
                lastMousePosition = Input.mousePosition;
                
                // Apply offset to map
                if (mapImage != null)
                {
                    mapImage.transform.localPosition = mapOffset;
                }
            }
        }

        private void HandleMapZoom()
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scroll) > 0.01f)
            {
                currentZoom = Mathf.Clamp(currentZoom + scroll, zoomMin, zoomMax);
                
                if (zoomSlider != null)
                    zoomSlider.value = currentZoom;
                
                ApplyZoom();
                PlaySound(zoomSound);
            }
        }

        private void HandleMarkerCreation()
        {
            if (isCreatingMarker && Input.GetMouseButtonDown(1))
            {
                // Right click to place marker
                Vector2 mousePos = Input.mousePosition;
                RectTransform mapRect = mapImage.rectTransform;
                
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    mapRect, mousePos, null, out Vector2 localPoint))
                {
                    Vector2 normalizedPos = new Vector2(
                        (localPoint.x + mapRect.rect.width * 0.5f) / mapRect.rect.width,
                        (localPoint.y + mapRect.rect.height * 0.5f) / mapRect.rect.height
                    );
                    
                    pendingMarkerPosition = MapToWorldPosition(normalizedPos);
                    
                    if (markerCreationPanel != null)
                        markerCreationPanel.SetActive(true);
                }
            }
        }
        #endregion

        #region Marker Management
        private void StartMarkerCreation()
        {
            isCreatingMarker = true;
            // Show instruction text or change cursor
        }

        private void ConfirmMarkerCreation()
        {
            if (mapSystem == null) return;

            string markerName = markerNameInput != null ? markerNameInput.text : "Custom Marker";
            MarkerType markerType = (MarkerType)markerTypeDropdown.value;

            mapSystem.AddMarker(System.Guid.NewGuid().ToString(), pendingMarkerPosition, markerType, markerName);
            
            CancelMarkerCreation();
            PlaySound(markerPlaceSound);
        }

        private void CancelMarkerCreation()
        {
            isCreatingMarker = false;
            
            if (markerCreationPanel != null)
                markerCreationPanel.SetActive(false);
            
            if (markerNameInput != null)
                markerNameInput.text = "";
        }
        #endregion

        #region Event Handlers
        private void OnMarkerAdded(MapMarker marker)
        {
            if (isMapVisible)
            {
                CreateMapMarker(marker);
            }
            CreateMiniMapMarker(marker);
        }

        private void OnMarkerRemoved(string markerId)
        {
            RefreshMapMarkers();
        }

        private void OnFogOfWarUpdated(Vector2Int gridPosition)
        {
            // Update fog of war display
            // This would integrate with a fog of war texture system
        }

        private void OnZoomChanged(float zoom)
        {
            currentZoom = zoom;
            ApplyZoom();
        }

        private void OnMiniMapToggled(bool enabled)
        {
            if (miniMapPanel != null)
                miniMapPanel.SetActive(enabled);
        }

        private void ApplyZoom()
        {
            if (mapImage != null)
            {
                mapImage.transform.localScale = Vector3.one * currentZoom;
            }
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion
    }
}
