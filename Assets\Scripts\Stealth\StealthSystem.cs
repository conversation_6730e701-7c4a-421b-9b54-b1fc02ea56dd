using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Collections;

namespace CinderOfDarkness.Stealth
{
    /// <summary>
    /// Stealth and Assassination System for Cinder of Darkness.
    /// Manages stealth mechanics, detection, and assassination gameplay.
    /// </summary>
    public class StealthSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("Stealth Settings")]
        [SerializeField] private bool enableStealth = true;
        [SerializeField] private float crouchSpeedMultiplier = 0.5f;
        [SerializeField] private float noiseReductionCrouched = 0.3f;
        [SerializeField] private float shadowVisibilityReduction = 0.5f;

        [Header("Detection System")]
        [SerializeField] private LayerMask enemyLayer = 1 << 8;
        [SerializeField] private LayerMask obstacleLayer = 1 << 9;
        [SerializeField] private float detectionUpdateInterval = 0.1f;
        [SerializeField] private AnimationCurve detectionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

        [Header("Light System")]
        [SerializeField] private float lightDetectionRadius = 10f;
        [SerializeField] private float darkZoneThreshold = 0.3f;
        [SerializeField] private LayerMask lightLayer = 1 << 10;

        [Header("Stealth Tools")]
        [SerializeField] private StealthTool[] availableTools;
        [SerializeField] private int maxToolUses = 3;

        [Header("Assassination")]
        [SerializeField] private float assassinationRange = 2f;
        [SerializeField] private float assassinationAngle = 45f;
        [SerializeField] private float assassinationDuration = 2f;
        [SerializeField] private LayerMask assassinationTargets = -1;

        [Header("UI References")]
        [SerializeField] private GameObject stealthUI;
        [SerializeField] private UnityEngine.UI.Slider detectionMeter;
        [SerializeField] private UnityEngine.UI.Image stealthIndicator;
        [SerializeField] private TMPro.TextMeshProUGUI toolCountText;
        [SerializeField] private Transform toolContainer;

        [Header("Audio")]
        [SerializeField] private AudioClip[] footstepSounds;
        [SerializeField] private AudioClip[] crouchedFootstepSounds;
        [SerializeField] private AudioClip assassinationSound;
        [SerializeField] private AudioClip detectedSound;
        [SerializeField] private AudioSource audioSource;

        [Header("Visual Effects")]
        [SerializeField] private GameObject smokeBombEffect;
        [SerializeField] private GameObject assassinationEffect;
        [SerializeField] private Material stealthMaterial;
        #endregion

        #region Public Properties
        public static StealthSystem Instance { get; private set; }
        public bool IsInStealth { get; private set; }
        public bool IsCrouching { get; private set; }
        public float CurrentDetectionLevel { get; private set; }
        public StealthState CurrentStealthState { get; private set; } = StealthState.Hidden;
        public Dictionary<string, int> ToolUses { get; private set; } = new Dictionary<string, int>();
        #endregion

        #region Private Fields
        private PlayerController playerController;
        private CharacterController characterController;
        private Animator playerAnimator;

        // Detection system
        private List<EnemyDetector> nearbyEnemies = new List<EnemyDetector>();
        private float lastDetectionUpdate;
        private float detectionDecayRate = 0.5f;

        // Light system
        private List<Light> nearbyLights = new List<Light>();
        private float currentLightLevel = 1f;

        // Movement tracking
        private Vector3 lastPosition;
        private float movementNoise = 0f;
        private float lastFootstepTime;
        private float footstepInterval = 0.5f;

        // Assassination system
        private List<Transform> assassinationTargets = new List<Transform>();
        private Transform currentAssassinationTarget;
        private bool isPerformingAssassination = false;

        // Tool system
        private Dictionary<string, StealthTool> toolDatabase = new Dictionary<string, StealthTool>();
        private List<ActiveStealthEffect> activeEffects = new List<ActiveStealthEffect>();

        // Performance optimization
        private float lastLightUpdate;
        private float lastTargetScan;
        private const float lightUpdateInterval = 0.2f;
        private const float targetScanInterval = 0.3f;
        #endregion

        #region Events
        public System.Action<StealthState> OnStealthStateChanged;
        public System.Action<float> OnDetectionLevelChanged;
        public System.Action<Transform> OnAssassinationTargetFound;
        public System.Action<Transform> OnAssassinationCompleted;
        public System.Action<string> OnToolUsed;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeStealthSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SetupSystemReferences();
            InitializeTools();
            SetupUI();
        }

        private void Update()
        {
            if (!enableStealth) return;

            HandleStealthInput();
            UpdateDetectionSystem();
            UpdateLightSystem();
            UpdateMovementTracking();
            UpdateAssassinationTargets();
            UpdateActiveEffects();
            UpdateUI();
        }
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the stealth system.
        /// </summary>
        private void InitializeStealthSystem()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }

            Debug.Log("Stealth System initialized");
        }

        /// <summary>
        /// Setup system references.
        /// </summary>
        private void SetupSystemReferences()
        {
            playerController = FindObjectOfType<PlayerController>();
            characterController = GetComponent<CharacterController>();
            playerAnimator = GetComponent<Animator>();

            if (playerController == null)
            {
                Debug.LogWarning("PlayerController not found for StealthSystem");
            }
        }

        /// <summary>
        /// Initialize stealth tools.
        /// </summary>
        private void InitializeTools()
        {
            foreach (var tool in availableTools)
            {
                toolDatabase[tool.toolId] = tool;
                ToolUses[tool.toolId] = maxToolUses;
            }
        }

        /// <summary>
        /// Setup UI components.
        /// </summary>
        private void SetupUI()
        {
            if (stealthUI != null)
            {
                stealthUI.SetActive(true);
            }

            UpdateStealthIndicator();
            UpdateToolDisplay();
        }
        #endregion

        #region Stealth State Management
        /// <summary>
        /// Enter stealth mode.
        /// </summary>
        public void EnterStealth()
        {
            if (IsInStealth) return;

            IsInStealth = true;
            CurrentStealthState = StealthState.Hidden;

            // Apply stealth visual effects
            ApplyStealthVisuals();

            OnStealthStateChanged?.Invoke(CurrentStealthState);
            Debug.Log("Entered stealth mode");
        }

        /// <summary>
        /// Exit stealth mode.
        /// </summary>
        public void ExitStealth()
        {
            if (!IsInStealth) return;

            IsInStealth = false;
            CurrentStealthState = StealthState.Visible;
            CurrentDetectionLevel = 0f;

            // Remove stealth visual effects
            RemoveStealthVisuals();

            OnStealthStateChanged?.Invoke(CurrentStealthState);
            Debug.Log("Exited stealth mode");
        }

        /// <summary>
        /// Toggle crouch state.
        /// </summary>
        public void ToggleCrouch()
        {
            IsCrouching = !IsCrouching;

            if (playerAnimator != null)
            {
                playerAnimator.SetBool("IsCrouching", IsCrouching);
            }

            // Adjust character controller height
            if (characterController != null)
            {
                characterController.height = IsCrouching ? 1f : 2f;
                characterController.center = new Vector3(0, characterController.height * 0.5f, 0);
            }

            Debug.Log($"Crouch state: {IsCrouching}");
        }

        /// <summary>
        /// Update stealth state based on detection level.
        /// </summary>
        private void UpdateStealthState()
        {
            StealthState newState = CurrentStealthState;

            if (!IsInStealth)
            {
                newState = StealthState.Visible;
            }
            else if (CurrentDetectionLevel >= 1f)
            {
                newState = StealthState.Detected;
            }
            else if (CurrentDetectionLevel >= 0.7f)
            {
                newState = StealthState.Suspicious;
            }
            else if (CurrentDetectionLevel >= 0.3f)
            {
                newState = StealthState.Searching;
            }
            else
            {
                newState = StealthState.Hidden;
            }

            if (newState != CurrentStealthState)
            {
                CurrentStealthState = newState;
                OnStealthStateChanged?.Invoke(CurrentStealthState);

                if (CurrentStealthState == StealthState.Detected)
                {
                    PlaySound(detectedSound);
                    ExitStealth();
                }
            }
        }
        #endregion

        #region Detection System
        /// <summary>
        /// Update detection system.
        /// </summary>
        private void UpdateDetectionSystem()
        {
            if (Time.time - lastDetectionUpdate < detectionUpdateInterval) return;

            UpdateNearbyEnemies();
            CalculateDetectionLevel();
            UpdateStealthState();

            lastDetectionUpdate = Time.time;
        }

        /// <summary>
        /// Update list of nearby enemies.
        /// </summary>
        private void UpdateNearbyEnemies()
        {
            nearbyEnemies.Clear();

            Collider[] enemyColliders = Physics.OverlapSphere(transform.position, 20f, enemyLayer);
            foreach (var collider in enemyColliders)
            {
                var detector = collider.GetComponent<EnemyDetector>();
                if (detector != null && detector.CanDetectPlayer())
                {
                    nearbyEnemies.Add(detector);
                }
            }
        }

        /// <summary>
        /// Calculate overall detection level.
        /// </summary>
        private void CalculateDetectionLevel()
        {
            float maxDetection = 0f;

            foreach (var enemy in nearbyEnemies)
            {
                float detection = CalculateEnemyDetection(enemy);
                maxDetection = Mathf.Max(maxDetection, detection);
            }

            // Apply detection decay
            if (maxDetection < CurrentDetectionLevel)
            {
                CurrentDetectionLevel = Mathf.Lerp(CurrentDetectionLevel, maxDetection, detectionDecayRate * Time.deltaTime);
            }
            else
            {
                CurrentDetectionLevel = maxDetection;
            }

            OnDetectionLevelChanged?.Invoke(CurrentDetectionLevel);
        }

        /// <summary>
        /// Calculate detection level for specific enemy.
        /// </summary>
        /// <param name="enemy">Enemy detector</param>
        /// <returns>Detection level (0-1)</returns>
        private float CalculateEnemyDetection(EnemyDetector enemy)
        {
            if (!IsInStealth) return 1f;

            float detection = 0f;

            // Distance factor
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            float distanceFactor = 1f - Mathf.Clamp01(distance / enemy.DetectionRange);

            // Line of sight
            if (!HasLineOfSight(enemy.transform.position, transform.position))
            {
                distanceFactor *= 0.1f; // Greatly reduce detection if no line of sight
            }

            // Angle factor
            Vector3 directionToPlayer = (transform.position - enemy.transform.position).normalized;
            float angle = Vector3.Angle(enemy.transform.forward, directionToPlayer);
            float angleFactor = 1f - Mathf.Clamp01(angle / enemy.FieldOfView);

            // Light factor
            float lightFactor = GetLightExposure();

            // Movement factor
            float movementFactor = GetMovementDetection();

            // Crouch factor
            float crouchFactor = IsCrouching ? noiseReductionCrouched : 1f;

            detection = distanceFactor * angleFactor * lightFactor * movementFactor * crouchFactor;
            detection = detectionCurve.Evaluate(detection);

            return Mathf.Clamp01(detection);
        }

        /// <summary>
        /// Check line of sight between two points.
        /// </summary>
        /// <param name="from">Start position</param>
        /// <param name="to">End position</param>
        /// <returns>True if line of sight exists</returns>
        private bool HasLineOfSight(Vector3 from, Vector3 to)
        {
            Vector3 direction = to - from;
            return !Physics.Raycast(from, direction.normalized, direction.magnitude, obstacleLayer);
        }

        /// <summary>
        /// Get light exposure factor.
        /// </summary>
        /// <returns>Light exposure (0-1)</returns>
        private float GetLightExposure()
        {
            return Mathf.Clamp01(currentLightLevel - (IsInShadow() ? shadowVisibilityReduction : 0f));
        }

        /// <summary>
        /// Get movement detection factor.
        /// </summary>
        /// <returns>Movement detection (0-1)</returns>
        private float GetMovementDetection()
        {
            return Mathf.Clamp01(movementNoise);
        }

        /// <summary>
        /// Check if player is in shadow.
        /// </summary>
        /// <returns>True if in shadow</returns>
        private bool IsInShadow()
        {
            return currentLightLevel < darkZoneThreshold;
        }
        #endregion

        #region Light System
        /// <summary>
        /// Update light system.
        /// </summary>
        private void UpdateLightSystem()
        {
            if (Time.time - lastLightUpdate < lightUpdateInterval) return;

            UpdateNearbyLights();
            CalculateLightLevel();

            lastLightUpdate = Time.time;
        }

        /// <summary>
        /// Update list of nearby lights.
        /// </summary>
        private void UpdateNearbyLights()
        {
            nearbyLights.Clear();

            Collider[] lightColliders = Physics.OverlapSphere(transform.position, lightDetectionRadius, lightLayer);
            foreach (var collider in lightColliders)
            {
                var light = collider.GetComponent<Light>();
                if (light != null && light.enabled)
                {
                    nearbyLights.Add(light);
                }
            }
        }

        /// <summary>
        /// Calculate current light level.
        /// </summary>
        private void CalculateLightLevel()
        {
            float totalLight = 0f;

            foreach (var light in nearbyLights)
            {
                float distance = Vector3.Distance(transform.position, light.transform.position);
                float lightContribution = light.intensity * (1f - Mathf.Clamp01(distance / light.range));
                totalLight += lightContribution;
            }

            // Add ambient light
            totalLight += RenderSettings.ambientIntensity * 0.5f;

            currentLightLevel = Mathf.Clamp01(totalLight);
        }
        #endregion

        #region Movement Tracking
        /// <summary>
        /// Update movement tracking.
        /// </summary>
        private void UpdateMovementTracking()
        {
            Vector3 currentPosition = transform.position;
            Vector3 movement = currentPosition - lastPosition;
            float movementSpeed = movement.magnitude / Time.deltaTime;

            // Calculate noise based on movement speed and crouch state
            float speedNoise = movementSpeed * 0.1f;
            if (IsCrouching)
            {
                speedNoise *= noiseReductionCrouched;
            }

            movementNoise = Mathf.Lerp(movementNoise, speedNoise, Time.deltaTime * 2f);

            // Play footstep sounds
            if (movementSpeed > 0.1f && Time.time - lastFootstepTime > footstepInterval)
            {
                PlayFootstepSound();
                lastFootstepTime = Time.time;
            }

            lastPosition = currentPosition;
        }

        /// <summary>
        /// Play footstep sound based on movement state.
        /// </summary>
        private void PlayFootstepSound()
        {
            AudioClip[] soundArray = IsCrouching ? crouchedFootstepSounds : footstepSounds;

            if (soundArray != null && soundArray.Length > 0)
            {
                AudioClip footstepClip = soundArray[Random.Range(0, soundArray.Length)];
                float volume = IsCrouching ? 0.3f : 0.7f;

                if (audioSource != null)
                {
                    audioSource.PlayOneShot(footstepClip, volume);
                }
            }
        }
        #endregion

        #region Assassination System
        /// <summary>
        /// Update assassination targets.
        /// </summary>
        private void UpdateAssassinationTargets()
        {
            if (Time.time - lastTargetScan < targetScanInterval) return;

            FindAssassinationTargets();
            UpdateCurrentTarget();

            lastTargetScan = Time.time;
        }

        /// <summary>
        /// Find potential assassination targets.
        /// </summary>
        private void FindAssassinationTargets()
        {
            assassinationTargets.Clear();

            Collider[] targets = Physics.OverlapSphere(transform.position, assassinationRange, assassinationTargets);
            foreach (var target in targets)
            {
                if (CanAssassinate(target.transform))
                {
                    assassinationTargets.Add(target.transform);
                }
            }
        }

        /// <summary>
        /// Check if target can be assassinated.
        /// </summary>
        /// <param name="target">Target transform</param>
        /// <returns>True if target can be assassinated</returns>
        private bool CanAssassinate(Transform target)
        {
            if (!IsInStealth || isPerformingAssassination) return false;

            // Check distance
            float distance = Vector3.Distance(transform.position, target.position);
            if (distance > assassinationRange) return false;

            // Check angle (behind target)
            Vector3 directionToPlayer = (transform.position - target.position).normalized;
            Vector3 targetForward = target.forward;
            float angle = Vector3.Angle(-targetForward, directionToPlayer);

            if (angle > assassinationAngle) return false;

            // Check if target is unaware
            var detector = target.GetComponent<EnemyDetector>();
            if (detector != null && detector.IsAware()) return false;

            return true;
        }

        /// <summary>
        /// Update current assassination target.
        /// </summary>
        private void UpdateCurrentTarget()
        {
            Transform newTarget = null;
            float closestDistance = float.MaxValue;

            foreach (var target in assassinationTargets)
            {
                float distance = Vector3.Distance(transform.position, target.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    newTarget = target;
                }
            }

            if (newTarget != currentAssassinationTarget)
            {
                currentAssassinationTarget = newTarget;
                if (currentAssassinationTarget != null)
                {
                    OnAssassinationTargetFound?.Invoke(currentAssassinationTarget);
                }
            }
        }

        /// <summary>
        /// Perform assassination on current target.
        /// </summary>
        public void PerformAssassination()
        {
            if (currentAssassinationTarget == null || isPerformingAssassination) return;

            StartCoroutine(AssassinationSequence(currentAssassinationTarget));
        }

        /// <summary>
        /// Assassination sequence coroutine.
        /// </summary>
        /// <param name="target">Target to assassinate</param>
        /// <returns>Assassination coroutine</returns>
        private IEnumerator AssassinationSequence(Transform target)
        {
            isPerformingAssassination = true;

            // Disable player movement
            if (playerController != null)
            {
                playerController.enabled = false;
            }

            // Play assassination animation
            if (playerAnimator != null)
            {
                playerAnimator.SetTrigger("Assassinate");
            }

            // Visual effect
            if (assassinationEffect != null)
            {
                Instantiate(assassinationEffect, target.position, Quaternion.identity);
            }

            // Audio effect
            PlaySound(assassinationSound);

            // Wait for animation
            yield return new WaitForSeconds(assassinationDuration);

            // Kill target
            var health = target.GetComponent<Health>();
            if (health != null)
            {
                health.TakeDamage(float.MaxValue); // Instant kill
            }

            // Re-enable player movement
            if (playerController != null)
            {
                playerController.enabled = true;
            }

            OnAssassinationCompleted?.Invoke(target);
            isPerformingAssassination = false;
            currentAssassinationTarget = null;

            Debug.Log($"Assassinated target: {target.name}");
        }
        #endregion

        #region Tool System
        /// <summary>
        /// Use stealth tool.
        /// </summary>
        /// <param name="toolId">Tool ID</param>
        /// <param name="targetPosition">Target position</param>
        public bool UseTool(string toolId, Vector3 targetPosition)
        {
            if (!toolDatabase.ContainsKey(toolId)) return false;
            if (!ToolUses.ContainsKey(toolId) || ToolUses[toolId] <= 0) return false;

            var tool = toolDatabase[toolId];

            // Consume tool use
            ToolUses[toolId]--;

            // Execute tool effect
            ExecuteToolEffect(tool, targetPosition);

            OnToolUsed?.Invoke(toolId);
            UpdateToolDisplay();

            Debug.Log($"Used tool: {tool.toolName}");
            return true;
        }

        /// <summary>
        /// Execute tool effect.
        /// </summary>
        /// <param name="tool">Stealth tool</param>
        /// <param name="position">Effect position</param>
        private void ExecuteToolEffect(StealthTool tool, Vector3 position)
        {
            switch (tool.toolType)
            {
                case StealthToolType.SmokeBomb:
                    CreateSmokeBomb(tool, position);
                    break;
                case StealthToolType.Lockpick:
                    UseLockpick(tool, position);
                    break;
                case StealthToolType.NoiseDistraction:
                    CreateNoiseDistraction(tool, position);
                    break;
                case StealthToolType.FlashBang:
                    CreateFlashBang(tool, position);
                    break;
                case StealthToolType.Caltrops:
                    CreateCaltrops(tool, position);
                    break;
            }

            // Play use sound
            PlaySound(tool.useSound);
        }

        /// <summary>
        /// Create smoke bomb effect.
        /// </summary>
        /// <param name="tool">Smoke bomb tool</param>
        /// <param name="position">Effect position</param>
        private void CreateSmokeBomb(StealthTool tool, Vector3 position)
        {
            GameObject smokeEffect = null;

            if (tool.effectPrefab != null)
            {
                smokeEffect = Instantiate(tool.effectPrefab, position, Quaternion.identity);
            }
            else if (smokeBombEffect != null)
            {
                smokeEffect = Instantiate(smokeBombEffect, position, Quaternion.identity);
            }

            var activeEffect = new ActiveStealthEffect
            {
                effectId = System.Guid.NewGuid().ToString(),
                effectType = StealthToolType.SmokeBomb,
                startTime = Time.time,
                duration = tool.effectDuration,
                position = position,
                effectObject = smokeEffect
            };

            activeEffects.Add(activeEffect);

            // Reduce detection in smoke area
            StartCoroutine(SmokeEffectCoroutine(activeEffect, tool.effectRadius));
        }

        /// <summary>
        /// Smoke effect coroutine.
        /// </summary>
        /// <param name="effect">Active effect</param>
        /// <param name="radius">Effect radius</param>
        /// <returns>Smoke effect coroutine</returns>
        private IEnumerator SmokeEffectCoroutine(ActiveStealthEffect effect, float radius)
        {
            while (Time.time - effect.startTime < effect.duration)
            {
                // Reduce detection for player in smoke
                float distance = Vector3.Distance(transform.position, effect.position);
                if (distance <= radius)
                {
                    CurrentDetectionLevel *= 0.5f; // Reduce detection by half in smoke
                }

                yield return new WaitForSeconds(0.1f);
            }
        }

        /// <summary>
        /// Use lockpick tool.
        /// </summary>
        /// <param name="tool">Lockpick tool</param>
        /// <param name="position">Target position</param>
        private void UseLockpick(StealthTool tool, Vector3 position)
        {
            // Find lockable objects nearby
            Collider[] lockables = Physics.OverlapSphere(position, 2f);
            foreach (var collider in lockables)
            {
                var lockable = collider.GetComponent<LockableObject>();
                if (lockable != null && lockable.IsLocked)
                {
                    lockable.Unlock();
                    Debug.Log($"Unlocked: {collider.name}");
                    break;
                }
            }
        }

        /// <summary>
        /// Create noise distraction.
        /// </summary>
        /// <param name="tool">Noise tool</param>
        /// <param name="position">Effect position</param>
        private void CreateNoiseDistraction(StealthTool tool, Vector3 position)
        {
            // Alert nearby enemies to investigate position
            Collider[] enemies = Physics.OverlapSphere(position, tool.effectRadius, enemyLayer);
            foreach (var enemy in enemies)
            {
                var detector = enemy.GetComponent<EnemyDetector>();
                if (detector != null)
                {
                    detector.InvestigatePosition(position);
                }
            }

            if (tool.effectPrefab != null)
            {
                GameObject effect = Instantiate(tool.effectPrefab, position, Quaternion.identity);
                Destroy(effect, tool.effectDuration);
            }
        }

        /// <summary>
        /// Create flash bang effect.
        /// </summary>
        /// <param name="tool">Flash bang tool</param>
        /// <param name="position">Effect position</param>
        private void CreateFlashBang(StealthTool tool, Vector3 position)
        {
            // Stun nearby enemies
            Collider[] enemies = Physics.OverlapSphere(position, tool.effectRadius, enemyLayer);
            foreach (var enemy in enemies)
            {
                var detector = enemy.GetComponent<EnemyDetector>();
                if (detector != null)
                {
                    detector.Stun(tool.effectDuration);
                }
            }

            if (tool.effectPrefab != null)
            {
                GameObject effect = Instantiate(tool.effectPrefab, position, Quaternion.identity);
                Destroy(effect, 2f);
            }
        }

        /// <summary>
        /// Create caltrops effect.
        /// </summary>
        /// <param name="tool">Caltrops tool</param>
        /// <param name="position">Effect position</param>
        private void CreateCaltrops(StealthTool tool, Vector3 position)
        {
            GameObject caltropsEffect = null;

            if (tool.effectPrefab != null)
            {
                caltropsEffect = Instantiate(tool.effectPrefab, position, Quaternion.identity);
            }

            var activeEffect = new ActiveStealthEffect
            {
                effectId = System.Guid.NewGuid().ToString(),
                effectType = StealthToolType.Caltrops,
                startTime = Time.time,
                duration = tool.effectDuration,
                position = position,
                effectObject = caltropsEffect
            };

            activeEffects.Add(activeEffect);

            // Add damage trigger
            if (caltropsEffect != null)
            {
                var trigger = caltropsEffect.GetComponent<CaltropsTrigger>();
                if (trigger == null)
                {
                    trigger = caltropsEffect.AddComponent<CaltropsTrigger>();
                }
                trigger.Initialize(tool.effectRadius);
            }
        }

        /// <summary>
        /// Update active stealth effects.
        /// </summary>
        private void UpdateActiveEffects()
        {
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                var effect = activeEffects[i];

                if (Time.time - effect.startTime > effect.duration)
                {
                    if (effect.effectObject != null)
                    {
                        Destroy(effect.effectObject);
                    }
                    activeEffects.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Refill tool uses.
        /// </summary>
        /// <param name="toolId">Tool ID</param>
        /// <param name="amount">Amount to refill</param>
        public void RefillTool(string toolId, int amount)
        {
            if (ToolUses.ContainsKey(toolId))
            {
                ToolUses[toolId] = Mathf.Min(ToolUses[toolId] + amount, maxToolUses);
                UpdateToolDisplay();
            }
        }
        #endregion

        #region Visual Effects
        /// <summary>
        /// Apply stealth visual effects.
        /// </summary>
        private void ApplyStealthVisuals()
        {
            var renderer = GetComponent<Renderer>();
            if (renderer != null && stealthMaterial != null)
            {
                renderer.material = stealthMaterial;
            }

            // Reduce opacity
            var renderers = GetComponentsInChildren<Renderer>();
            foreach (var r in renderers)
            {
                var materials = r.materials;
                for (int i = 0; i < materials.Length; i++)
                {
                    var material = materials[i];
                    if (material.HasProperty("_Color"))
                    {
                        Color color = material.color;
                        color.a = 0.7f;
                        material.color = color;
                    }
                }
            }
        }

        /// <summary>
        /// Remove stealth visual effects.
        /// </summary>
        private void RemoveStealthVisuals()
        {
            // Restore opacity
            var renderers = GetComponentsInChildren<Renderer>();
            foreach (var r in renderers)
            {
                var materials = r.materials;
                for (int i = 0; i < materials.Length; i++)
                {
                    var material = materials[i];
                    if (material.HasProperty("_Color"))
                    {
                        Color color = material.color;
                        color.a = 1f;
                        material.color = color;
                    }
                }
            }
        }
        #endregion

        #region Input Handling
        /// <summary>
        /// Handle stealth input.
        /// </summary>
        private void HandleStealthInput()
        {
            // Toggle stealth
            if (Input.GetKeyDown(KeyCode.LeftControl))
            {
                if (IsInStealth)
                {
                    ExitStealth();
                }
                else
                {
                    EnterStealth();
                }
            }

            // Toggle crouch
            if (Input.GetKeyDown(KeyCode.C))
            {
                ToggleCrouch();
            }

            // Assassination
            if (Input.GetKeyDown(KeyCode.F) && currentAssassinationTarget != null)
            {
                PerformAssassination();
            }

            // Tool usage
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                UseTool("smoke_bomb", transform.position + transform.forward * 3f);
            }
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                UseTool("lockpick", transform.position + transform.forward * 2f);
            }
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                UseTool("noise_distraction", transform.position + transform.forward * 5f);
            }
        }
        #endregion

        #region UI Management
        /// <summary>
        /// Update UI elements.
        /// </summary>
        private void UpdateUI()
        {
            UpdateDetectionMeter();
            UpdateStealthIndicator();
        }

        /// <summary>
        /// Update detection meter.
        /// </summary>
        private void UpdateDetectionMeter()
        {
            if (detectionMeter != null)
            {
                detectionMeter.value = CurrentDetectionLevel;

                // Change color based on detection level
                var fillImage = detectionMeter.fillRect.GetComponent<UnityEngine.UI.Image>();
                if (fillImage != null)
                {
                    if (CurrentDetectionLevel < 0.3f)
                        fillImage.color = Color.green;
                    else if (CurrentDetectionLevel < 0.7f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }

        /// <summary>
        /// Update stealth indicator.
        /// </summary>
        private void UpdateStealthIndicator()
        {
            if (stealthIndicator != null)
            {
                Color indicatorColor = Color.white;

                switch (CurrentStealthState)
                {
                    case StealthState.Hidden:
                        indicatorColor = Color.green;
                        break;
                    case StealthState.Searching:
                        indicatorColor = Color.yellow;
                        break;
                    case StealthState.Suspicious:
                        indicatorColor = Color.orange;
                        break;
                    case StealthState.Detected:
                        indicatorColor = Color.red;
                        break;
                    case StealthState.Visible:
                        indicatorColor = Color.white;
                        break;
                }

                stealthIndicator.color = indicatorColor;
            }
        }

        /// <summary>
        /// Update tool display.
        /// </summary>
        private void UpdateToolDisplay()
        {
            if (toolCountText != null)
            {
                string toolText = "";
                foreach (var kvp in ToolUses)
                {
                    var tool = toolDatabase[kvp.Key];
                    toolText += $"{tool.toolName}: {kvp.Value}\n";
                }
                toolCountText.text = toolText;
            }
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play sound effect.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Get current stealth effectiveness.
        /// </summary>
        /// <returns>Stealth effectiveness (0-1)</returns>
        public float GetStealthEffectiveness()
        {
            if (!IsInStealth) return 0f;

            float effectiveness = 1f - CurrentDetectionLevel;

            // Modifiers
            if (IsCrouching) effectiveness += 0.2f;
            if (IsInShadow()) effectiveness += 0.3f;
            if (movementNoise < 0.1f) effectiveness += 0.1f;

            return Mathf.Clamp01(effectiveness);
        }

        /// <summary>
        /// Check if tool is available.
        /// </summary>
        /// <param name="toolId">Tool ID</param>
        /// <returns>True if tool is available</returns>
        public bool IsToolAvailable(string toolId)
        {
            return ToolUses.ContainsKey(toolId) && ToolUses[toolId] > 0;
        }

        /// <summary>
        /// Get tool uses remaining.
        /// </summary>
        /// <param name="toolId">Tool ID</param>
        /// <returns>Uses remaining</returns>
        public int GetToolUses(string toolId)
        {
            return ToolUses.ContainsKey(toolId) ? ToolUses[toolId] : 0;
        }

        /// <summary>
        /// Force detection state (for testing).
        /// </summary>
        /// <param name="detectionLevel">Detection level to set</param>
        public void ForceDetectionLevel(float detectionLevel)
        {
            CurrentDetectionLevel = Mathf.Clamp01(detectionLevel);
            UpdateStealthState();
        }

        /// <summary>
        /// Reset stealth system.
        /// </summary>
        public void ResetStealthSystem()
        {
            ExitStealth();
            IsCrouching = false;
            CurrentDetectionLevel = 0f;

            // Reset tool uses
            foreach (var tool in availableTools)
            {
                ToolUses[tool.toolId] = maxToolUses;
            }

            // Clear active effects
            foreach (var effect in activeEffects)
            {
                if (effect.effectObject != null)
                {
                    Destroy(effect.effectObject);
                }
            }
            activeEffects.Clear();

            UpdateToolDisplay();
            Debug.Log("Stealth system reset");
        }

        /// <summary>
        /// Get stealth statistics.
        /// </summary>
        /// <returns>Stealth statistics</returns>
        public StealthStats GetStealthStats()
        {
            return new StealthStats
            {
                currentDetectionLevel = CurrentDetectionLevel,
                stealthEffectiveness = GetStealthEffectiveness(),
                isInStealth = IsInStealth,
                isCrouching = IsCrouching,
                currentLightLevel = currentLightLevel,
                movementNoise = movementNoise,
                nearbyEnemyCount = nearbyEnemies.Count,
                availableAssassinationTargets = assassinationTargets.Count
            };
        }
        #endregion
    }

    #region Additional Data Structures
    [System.Serializable]
    public class StealthStats
    {
        public float currentDetectionLevel;
        public float stealthEffectiveness;
        public bool isInStealth;
        public bool isCrouching;
        public float currentLightLevel;
        public float movementNoise;
        public int nearbyEnemyCount;
        public int availableAssassinationTargets;
    }
    #endregion

    #region Data Structures
    [System.Serializable]
    public class StealthTool
    {
        public string toolId;
        public string toolName;
        public StealthToolType toolType;
        public float effectDuration;
        public float effectRadius;
        public GameObject effectPrefab;
        public AudioClip useSound;
        public Sprite toolIcon;
    }

    [System.Serializable]
    public class ActiveStealthEffect
    {
        public string effectId;
        public StealthToolType effectType;
        public float startTime;
        public float duration;
        public Vector3 position;
        public GameObject effectObject;
    }

    public enum StealthState
    {
        Hidden,
        Searching,
        Suspicious,
        Detected,
        Visible
    }

    public enum StealthToolType
    {
        SmokeBomb,
        Lockpick,
        NoiseDistraction,
        FlashBang,
        Caltrops
    }
    #endregion
}
