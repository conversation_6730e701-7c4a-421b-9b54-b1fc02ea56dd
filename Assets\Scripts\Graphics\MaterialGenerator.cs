using UnityEngine;
using UnityEditor;
using System.IO;

#if UNITY_EDITOR
/// <summary>
/// Generates visual material assets for Cinder of Darkness
/// Creates URP-compatible materials for environmental and narrative effects
/// </summary>
public class MaterialGenerator : EditorWindow
{
    [MenuItem("Cinder of Darkness/Generate Visual Materials")]
    public static void ShowWindow()
    {
        GetWindow<MaterialGenerator>("Material Generator");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Visual Material Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("Generates URP-compatible materials for environmental and narrative effects", EditorStyles.helpBox);
        GUILayout.Space(10);
        
        if (GUILayout.Button("Generate All Materials"))
        {
            GenerateAllMaterials();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Generate Fire Materials"))
        {
            GenerateFireMaterials();
        }
        
        if (GUILayout.<PERSON><PERSON>("Generate Ash Materials"))
        {
            GenerateAshMaterials();
        }
        
        if (GUILayout.Button("Generate Blood Materials"))
        {
            GenerateBloodMaterials();
        }
        
        if (GUILayout.Button("Generate Spirit Materials"))
        {
            GenerateSpiritMaterials();
        }
        
        if (GUILayout.Button("Generate Combat FX Materials"))
        {
            GenerateCombatFXMaterials();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Assign to Systems"))
        {
            AssignMaterialsToSystems();
        }
    }
    
    void GenerateAllMaterials()
    {
        Debug.Log("Generating all visual materials for Cinder of Darkness...");
        
        CreateMaterialDirectories();
        GenerateFireMaterials();
        GenerateAshMaterials();
        GenerateBloodMaterials();
        GenerateSpiritMaterials();
        GenerateCombatFXMaterials();
        
        AssetDatabase.Refresh();
        Debug.Log("All visual materials generated successfully!");
    }
    
    void CreateMaterialDirectories()
    {
        string[] directories = {
            "Assets/Materials",
            "Assets/Materials/Effects",
            "Assets/Materials/Effects/Fire",
            "Assets/Materials/Effects/Ash",
            "Assets/Materials/Effects/Blood",
            "Assets/Materials/Effects/Spirit",
            "Assets/Materials/Effects/CombatFX",
            "Assets/Materials/Effects/Textures"
        };
        
        foreach (string dir in directories)
        {
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
        }
    }
    
    void GenerateFireMaterials()
    {
        Debug.Log("Generating fire materials...");
        
        // Soft Flame Material
        Material softFlame = CreateFireMaterial("SoftFlame", 
            new Color(1f, 0.6f, 0.2f, 0.8f), 
            new Color(1f, 0.4f, 0.1f) * 2f, 
            0.7f);
        SaveMaterial(softFlame, "Assets/Materials/Effects/Fire/SoftFlame.mat");
        
        // Ember Glow Material
        Material emberGlow = CreateFireMaterial("EmberGlow", 
            new Color(1f, 0.3f, 0.1f, 0.6f), 
            new Color(1f, 0.2f, 0.05f) * 1.5f, 
            0.5f);
        SaveMaterial(emberGlow, "Assets/Materials/Effects/Fire/EmberGlow.mat");
        
        // Torch Flame Material
        Material torchFlame = CreateFireMaterial("TorchFlame", 
            new Color(1f, 0.7f, 0.3f, 0.9f), 
            new Color(1f, 0.5f, 0.2f) * 3f, 
            0.8f);
        SaveMaterial(torchFlame, "Assets/Materials/Effects/Fire/TorchFlame.mat");
        
        // Weapon Enchantment Material
        Material weaponEnchant = CreateFireMaterial("WeaponEnchantment", 
            new Color(0.8f, 0.4f, 1f, 0.7f), 
            new Color(0.6f, 0.2f, 1f) * 2.5f, 
            0.6f);
        SaveMaterial(weaponEnchant, "Assets/Materials/Effects/Fire/WeaponEnchantment.mat");
    }
    
    void GenerateAshMaterials()
    {
        Debug.Log("Generating ash materials...");
        
        // Floating Ash Particles Material
        Material floatingAsh = CreateAshMaterial("FloatingAsh", 
            new Color(0.7f, 0.7f, 0.7f, 0.3f), 
            0.3f);
        SaveMaterial(floatingAsh, "Assets/Materials/Effects/Ash/FloatingAsh.mat");
        
        // Ash Fog Material
        Material ashFog = CreateAshMaterial("AshFog", 
            new Color(0.5f, 0.5f, 0.5f, 0.2f), 
            0.2f);
        SaveMaterial(ashFog, "Assets/Materials/Effects/Ash/AshFog.mat");
        
        // Cursed Ash Material
        Material cursedAsh = CreateAshMaterial("CursedAsh", 
            new Color(0.4f, 0.3f, 0.3f, 0.4f), 
            0.4f);
        SaveMaterial(cursedAsh, "Assets/Materials/Effects/Ash/CursedAsh.mat");
        
        // Dream State Ash Material
        Material dreamAsh = CreateAshMaterial("DreamAsh", 
            new Color(0.8f, 0.8f, 0.9f, 0.25f), 
            0.25f);
        SaveMaterial(dreamAsh, "Assets/Materials/Effects/Ash/DreamAsh.mat");
    }
    
    void GenerateBloodMaterials()
    {
        Debug.Log("Generating blood materials...");
        
        // Fresh Blood Material
        Material freshBlood = CreateBloodMaterial("FreshBlood", 
            new Color(0.8f, 0.1f, 0.1f, 1f), 
            0.8f, 
            true);
        SaveMaterial(freshBlood, "Assets/Materials/Effects/Blood/FreshBlood.mat");
        
        // Dried Blood Material
        Material driedBlood = CreateBloodMaterial("DriedBlood", 
            new Color(0.4f, 0.1f, 0.1f, 1f), 
            0.2f, 
            false);
        SaveMaterial(driedBlood, "Assets/Materials/Effects/Blood/DriedBlood.mat");
        
        // Blood Stain Material
        Material bloodStain = CreateBloodMaterial("BloodStain", 
            new Color(0.3f, 0.05f, 0.05f, 0.8f), 
            0.1f, 
            false);
        SaveMaterial(bloodStain, "Assets/Materials/Effects/Blood/BloodStain.mat");
        
        // Gore Material
        Material gore = CreateBloodMaterial("Gore", 
            new Color(0.6f, 0.1f, 0.1f, 1f), 
            0.5f, 
            true);
        SaveMaterial(gore, "Assets/Materials/Effects/Blood/Gore.mat");
    }
    
    void GenerateSpiritMaterials()
    {
        Debug.Log("Generating spirit materials...");
        
        // Translucent Spirit Material
        Material translucentSpirit = CreateSpiritMaterial("TranslucentSpirit", 
            new Color(0.7f, 0.9f, 1f, 0.3f), 
            new Color(0.5f, 0.8f, 1f) * 0.5f, 
            0.3f);
        SaveMaterial(translucentSpirit, "Assets/Materials/Effects/Spirit/TranslucentSpirit.mat");
        
        // Spectral Glow Material
        Material spectralGlow = CreateSpiritMaterial("SpectralGlow", 
            new Color(0.6f, 0.8f, 1f, 0.4f), 
            new Color(0.4f, 0.7f, 1f) * 1f, 
            0.4f);
        SaveMaterial(spectralGlow, "Assets/Materials/Effects/Spirit/SpectralGlow.mat");
        
        // Ghost Manifestation Material
        Material ghostManifestation = CreateSpiritMaterial("GhostManifestation", 
            new Color(0.8f, 0.9f, 1f, 0.25f), 
            new Color(0.6f, 0.8f, 1f) * 0.3f, 
            0.25f);
        SaveMaterial(ghostManifestation, "Assets/Materials/Effects/Spirit/GhostManifestation.mat");
        
        // Dream Echo Material
        Material dreamEcho = CreateSpiritMaterial("DreamEcho", 
            new Color(0.9f, 0.8f, 1f, 0.2f), 
            new Color(0.8f, 0.6f, 1f) * 0.4f, 
            0.2f);
        SaveMaterial(dreamEcho, "Assets/Materials/Effects/Spirit/DreamEcho.mat");
    }
    
    void GenerateCombatFXMaterials()
    {
        Debug.Log("Generating combat FX materials...");
        
        // Slash Trail Material
        Material slashTrail = CreateCombatFXMaterial("SlashTrail", 
            new Color(1f, 1f, 0.8f, 0.6f), 
            new Color(1f, 0.9f, 0.7f) * 1.5f, 
            true);
        SaveMaterial(slashTrail, "Assets/Materials/Effects/CombatFX/SlashTrail.mat");
        
        // Weapon Glow Material
        Material weaponGlow = CreateCombatFXMaterial("WeaponGlow", 
            new Color(0.9f, 0.9f, 1f, 0.8f), 
            new Color(0.8f, 0.8f, 1f) * 2f, 
            false);
        SaveMaterial(weaponGlow, "Assets/Materials/Effects/CombatFX/WeaponGlow.mat");
        
        // Hit Impact Material
        Material hitImpact = CreateCombatFXMaterial("HitImpact", 
            new Color(1f, 0.7f, 0.3f, 0.9f), 
            new Color(1f, 0.5f, 0.2f) * 3f, 
            true);
        SaveMaterial(hitImpact, "Assets/Materials/Effects/CombatFX/HitImpact.mat");
        
        // Strong Attack Distortion Material
        Material strongAttack = CreateCombatFXMaterial("StrongAttackDistortion", 
            new Color(1f, 0.3f, 0.3f, 0.7f), 
            new Color(1f, 0.2f, 0.2f) * 2.5f, 
            true);
        SaveMaterial(strongAttack, "Assets/Materials/Effects/CombatFX/StrongAttackDistortion.mat");
        
        // Block Effect Material
        Material blockEffect = CreateCombatFXMaterial("BlockEffect", 
            new Color(0.8f, 0.8f, 1f, 0.8f), 
            new Color(0.6f, 0.6f, 1f) * 1.8f, 
            false);
        SaveMaterial(blockEffect, "Assets/Materials/Effects/CombatFX/BlockEffect.mat");
    }
    
    Material CreateFireMaterial(string name, Color baseColor, Color emissionColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.2f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    Material CreateAshMaterial(string name, Color baseColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.1f);
        
        return material;
    }
    
    Material CreateBloodMaterial(string name, Color baseColor, float smoothness, bool isWet)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to opaque or transparent based on alpha
        if (baseColor.a < 1f)
        {
            material.SetFloat("_Surface", 1); // Transparent
            material.SetFloat("_Blend", 0); // Alpha blend
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.renderQueue = 3000;
        }
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", isWet ? 0.1f : 0f);
        material.SetFloat("_Smoothness", smoothness);
        
        return material;
    }
    
    Material CreateSpiritMaterial(string name, Color baseColor, Color emissionColor, float alpha)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", 0.8f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    Material CreateCombatFXMaterial(string name, Color baseColor, Color emissionColor, bool hasDistortion)
    {
        Material material = new Material(GetURPShader("Lit"));
        material.name = name;
        
        // Set rendering mode to transparent
        material.SetFloat("_Surface", 1); // Transparent
        material.SetFloat("_Blend", 0); // Alpha blend
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = 3000;
        
        // Set colors
        material.SetColor("_BaseColor", baseColor);
        material.SetColor("_EmissionColor", emissionColor);
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Smoothness", hasDistortion ? 0.9f : 0.5f);
        
        // Enable emission
        material.EnableKeyword("_EMISSION");
        
        return material;
    }
    
    Shader GetURPShader(string shaderName)
    {
        // Try to find URP shader first
        Shader urpShader = Shader.Find($"Universal Render Pipeline/{shaderName}");
        if (urpShader != null)
            return urpShader;
        
        // Fallback to built-in shader
        Shader builtinShader = Shader.Find($"Standard");
        if (builtinShader != null)
            return builtinShader;
        
        // Final fallback
        return Shader.Find("Sprites/Default");
    }
    
    void SaveMaterial(Material material, string path)
    {
        AssetDatabase.CreateAsset(material, path);
        Debug.Log($"Created material: {material.name} at {path}");
    }
    
    void AssignMaterialsToSystems()
    {
        Debug.Log("Assigning materials to game systems...");
        
        // Create material asset collection
        CreateMaterialAssetCollection();
        
        Debug.Log("Materials are ready for assignment to game systems!");
    }
    
    void CreateMaterialAssetCollection()
    {
        // Create a ScriptableObject to hold all material references
        CinderMaterialAsset asset = ScriptableObject.CreateInstance<CinderMaterialAsset>();
        
        // Load fire materials
        asset.softFlame = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/SoftFlame.mat");
        asset.emberGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/EmberGlow.mat");
        asset.torchFlame = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/TorchFlame.mat");
        asset.weaponEnchantment = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Fire/WeaponEnchantment.mat");
        
        // Load ash materials
        asset.floatingAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/FloatingAsh.mat");
        asset.ashFog = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/AshFog.mat");
        asset.cursedAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/CursedAsh.mat");
        asset.dreamAsh = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Ash/DreamAsh.mat");
        
        // Load blood materials
        asset.freshBlood = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/FreshBlood.mat");
        asset.driedBlood = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/DriedBlood.mat");
        asset.bloodStain = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/BloodStain.mat");
        asset.gore = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Blood/Gore.mat");
        
        // Load spirit materials
        asset.translucentSpirit = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/TranslucentSpirit.mat");
        asset.spectralGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/SpectralGlow.mat");
        asset.ghostManifestation = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/GhostManifestation.mat");
        asset.dreamEcho = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/Spirit/DreamEcho.mat");
        
        // Load combat FX materials
        asset.slashTrail = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/SlashTrail.mat");
        asset.weaponGlow = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/WeaponGlow.mat");
        asset.hitImpact = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/HitImpact.mat");
        asset.strongAttackDistortion = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/StrongAttackDistortion.mat");
        asset.blockEffect = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/Effects/CombatFX/BlockEffect.mat");
        
        string assetPath = "Assets/Materials/Effects/CinderMaterialAsset.asset";
        AssetDatabase.CreateAsset(asset, assetPath);
        
        // Also create a copy in Resources folder for runtime loading
        string resourcesDir = "Assets/Resources";
        if (!Directory.Exists(resourcesDir))
        {
            Directory.CreateDirectory(resourcesDir);
        }
        
        string resourcesPath = "Assets/Resources/CinderMaterialAsset.asset";
        AssetDatabase.CopyAsset(assetPath, resourcesPath);
        
        AssetDatabase.SaveAssets();
        
        Debug.Log($"Created CinderMaterialAsset at {assetPath}");
        Debug.Log($"Also created Resources copy at {resourcesPath} for runtime loading");
    }
}
#endif
