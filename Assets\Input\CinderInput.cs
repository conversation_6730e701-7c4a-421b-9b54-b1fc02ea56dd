using UnityEngine;
using UnityEngine.InputSystem;
using System;

/// <summary>
/// Generated C# wrapper for CinderInputActions
/// Provides easy access to input actions for Cinder of Darkness
/// </summary>
public class CinderInput : IDisposable
{
    private InputActionAsset m_Asset;
    private InputActionMap m_Gameplay;
    private InputActionMap m_Menu;
    
    // Gameplay Actions
    private InputAction m_Move;
    private InputAction m_Look;
    private InputAction m_Jump;
    private InputAction m_Attack;
    private InputAction m_HeavyAttack;
    private InputAction m_Block;
    private InputAction m_Dodge;
    private InputAction m_Interact;
    private InputAction m_SwitchWeapon;
    private InputAction m_SwitchMagic;
    private InputAction m_Magic1;
    private InputAction m_Magic2;
    private InputAction m_Magic3;
    private InputAction m_Magic4;
    private InputAction m_OpenMenu;
    
    // Menu Actions
    private InputAction m_Navigate;
    private InputAction m_Confirm;
    private InputAction m_Cancel;
    private InputAction m_CloseMenu;
    
    public CinderInput()
    {
        m_Asset = Resources.Load<InputActionAsset>("CinderInputActions");
        if (m_Asset == null)
        {
            Debug.LogError("CinderInputActions asset not found in Resources folder!");
            return;
        }
        
        // Get action maps
        m_Gameplay = m_Asset.FindActionMap("Gameplay", throwIfNotFound: true);
        m_Menu = m_Asset.FindActionMap("Menu", throwIfNotFound: true);
        
        // Get gameplay actions
        m_Move = m_Gameplay.FindAction("Move", throwIfNotFound: true);
        m_Look = m_Gameplay.FindAction("Look", throwIfNotFound: true);
        m_Jump = m_Gameplay.FindAction("Jump", throwIfNotFound: true);
        m_Attack = m_Gameplay.FindAction("Attack", throwIfNotFound: true);
        m_HeavyAttack = m_Gameplay.FindAction("HeavyAttack", throwIfNotFound: true);
        m_Block = m_Gameplay.FindAction("Block", throwIfNotFound: true);
        m_Dodge = m_Gameplay.FindAction("Dodge", throwIfNotFound: true);
        m_Interact = m_Gameplay.FindAction("Interact", throwIfNotFound: true);
        m_SwitchWeapon = m_Gameplay.FindAction("SwitchWeapon", throwIfNotFound: true);
        m_SwitchMagic = m_Gameplay.FindAction("SwitchMagic", throwIfNotFound: true);
        m_Magic1 = m_Gameplay.FindAction("Magic1", throwIfNotFound: true);
        m_Magic2 = m_Gameplay.FindAction("Magic2", throwIfNotFound: true);
        m_Magic3 = m_Gameplay.FindAction("Magic3", throwIfNotFound: true);
        m_Magic4 = m_Gameplay.FindAction("Magic4", throwIfNotFound: true);
        m_OpenMenu = m_Gameplay.FindAction("OpenMenu", throwIfNotFound: true);
        
        // Get menu actions
        m_Navigate = m_Menu.FindAction("Navigate", throwIfNotFound: true);
        m_Confirm = m_Menu.FindAction("Confirm", throwIfNotFound: true);
        m_Cancel = m_Menu.FindAction("Cancel", throwIfNotFound: true);
        m_CloseMenu = m_Menu.FindAction("CloseMenu", throwIfNotFound: true);
    }
    
    public void Dispose()
    {
        m_Asset?.Dispose();
    }
    
    public InputActionAsset Asset => m_Asset;
    
    // Action Maps
    public InputActionMap Gameplay => m_Gameplay;
    public InputActionMap Menu => m_Menu;
    
    // Gameplay Actions
    public InputAction Move => m_Move;
    public InputAction Look => m_Look;
    public InputAction Jump => m_Jump;
    public InputAction Attack => m_Attack;
    public InputAction HeavyAttack => m_HeavyAttack;
    public InputAction Block => m_Block;
    public InputAction Dodge => m_Dodge;
    public InputAction Interact => m_Interact;
    public InputAction SwitchWeapon => m_SwitchWeapon;
    public InputAction SwitchMagic => m_SwitchMagic;
    public InputAction Magic1 => m_Magic1;
    public InputAction Magic2 => m_Magic2;
    public InputAction Magic3 => m_Magic3;
    public InputAction Magic4 => m_Magic4;
    public InputAction OpenMenu => m_OpenMenu;
    
    // Menu Actions
    public InputAction Navigate => m_Navigate;
    public InputAction Confirm => m_Confirm;
    public InputAction Cancel => m_Cancel;
    public InputAction CloseMenu => m_CloseMenu;
    
    // Convenience methods for common input patterns
    public Vector2 GetMoveInput() => m_Move.ReadValue<Vector2>();
    public Vector2 GetLookInput() => m_Look.ReadValue<Vector2>();
    public bool IsJumpPressed() => m_Jump.WasPressedThisFrame();
    public bool IsAttackPressed() => m_Attack.WasPressedThisFrame();
    public bool IsHeavyAttackPressed() => m_HeavyAttack.WasPressedThisFrame();
    public bool IsBlockHeld() => m_Block.IsPressed();
    public bool IsDodgePressed() => m_Dodge.WasPressedThisFrame();
    public bool IsInteractPressed() => m_Interact.WasPressedThisFrame();
    public float GetWeaponSwitchInput() => m_SwitchWeapon.ReadValue<float>();
    public float GetMagicSwitchInput() => m_SwitchMagic.ReadValue<float>();
    public bool IsOpenMenuPressed() => m_OpenMenu.WasPressedThisFrame();
    
    // Menu input convenience methods
    public Vector2 GetNavigateInput() => m_Navigate.ReadValue<Vector2>();
    public bool IsConfirmPressed() => m_Confirm.WasPressedThisFrame();
    public bool IsCancelPressed() => m_Cancel.WasPressedThisFrame();
    public bool IsCloseMenuPressed() => m_CloseMenu.WasPressedThisFrame();
    
    // Magic input convenience methods
    public bool IsMagic1Pressed() => m_Magic1.WasPressedThisFrame();
    public bool IsMagic2Pressed() => m_Magic2.WasPressedThisFrame();
    public bool IsMagic3Pressed() => m_Magic3.WasPressedThisFrame();
    public bool IsMagic4Pressed() => m_Magic4.WasPressedThisFrame();
    
    // Action map control
    public void EnableGameplay()
    {
        m_Gameplay.Enable();
        m_Menu.Disable();
    }
    
    public void EnableMenu()
    {
        m_Menu.Enable();
        m_Gameplay.Disable();
    }
    
    public void EnableAll()
    {
        m_Gameplay.Enable();
        m_Menu.Enable();
    }
    
    public void DisableAll()
    {
        m_Gameplay.Disable();
        m_Menu.Disable();
    }
    
    // Device detection
    public bool IsUsingGamepad()
    {
        var lastDevice = InputSystem.GetDevice<InputDevice>();
        return lastDevice is Gamepad;
    }
    
    public bool IsUsingKeyboardMouse()
    {
        var lastDevice = InputSystem.GetDevice<InputDevice>();
        return lastDevice is Keyboard || lastDevice is Mouse;
    }
    
    // Get current control scheme
    public string GetCurrentControlScheme()
    {
        if (IsUsingGamepad())
            return "Gamepad";
        else
            return "Keyboard&Mouse";
    }
    
    // Vibration support for gamepads
    public void SetGamepadVibration(float lowFrequency, float highFrequency, float duration = 0.2f)
    {
        var gamepad = Gamepad.current;
        if (gamepad != null)
        {
            gamepad.SetMotorSpeeds(lowFrequency, highFrequency);
            
            // Stop vibration after duration
            if (duration > 0)
            {
                var stopTime = Time.unscaledTime + duration;
                CoroutineRunner.Instance.StartCoroutine(StopVibrationAfterDelay(stopTime));
            }
        }
    }
    
    private System.Collections.IEnumerator StopVibrationAfterDelay(float stopTime)
    {
        yield return new WaitUntil(() => Time.unscaledTime >= stopTime);
        StopGamepadVibration();
    }
    
    public void StopGamepadVibration()
    {
        var gamepad = Gamepad.current;
        if (gamepad != null)
        {
            gamepad.SetMotorSpeeds(0f, 0f);
        }
    }
    
    // Input remapping support
    public void RemapBinding(InputAction action, int bindingIndex, string newPath)
    {
        action.ApplyBindingOverride(bindingIndex, newPath);
    }
    
    public void ResetBinding(InputAction action, int bindingIndex)
    {
        action.RemoveBindingOverride(bindingIndex);
    }
    
    public void ResetAllBindings()
    {
        m_Asset.RemoveAllBindingOverrides();
    }
    
    // Save/Load binding overrides
    public string GetBindingOverrides()
    {
        return m_Asset.SaveBindingOverridesAsJson();
    }
    
    public void LoadBindingOverrides(string overrides)
    {
        m_Asset.LoadBindingOverridesFromJson(overrides);
    }
}

/// <summary>
/// Simple coroutine runner for input system
/// </summary>
public class CoroutineRunner : MonoBehaviour
{
    private static CoroutineRunner _instance;
    public static CoroutineRunner Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("CoroutineRunner");
                _instance = go.AddComponent<CoroutineRunner>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }
}
