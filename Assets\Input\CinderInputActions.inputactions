{"name": "CinderInputActions", "maps": [{"name": "Gameplay", "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "actions": [{"name": "Move", "type": "Value", "id": "11111111-1111-1111-1111-111111111111", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "22222222-2222-2222-2222-222222222222", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "33333333-3333-3333-3333-333333333333", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Attack", "type": "<PERSON><PERSON>", "id": "44444444-4444-4444-4444-444444444444", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "HeavyAttack", "type": "<PERSON><PERSON>", "id": "55555555-5555-5555-5555-555555555555", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Block", "type": "<PERSON><PERSON>", "id": "66666666-6666-6666-6666-666666666666", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Dodge", "type": "<PERSON><PERSON>", "id": "77777777-7777-7777-7777-777777777777", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "88888888-8888-8888-8888-888888888888", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "SwitchWeapon", "type": "Value", "id": "99999999-9999-9999-9999-999999999999", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "SwitchMagic", "type": "Value", "id": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Magic1", "type": "<PERSON><PERSON>", "id": "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Magic2", "type": "<PERSON><PERSON>", "id": "cccccccc-cccc-cccc-cccc-cccccccccccc", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Magic3", "type": "<PERSON><PERSON>", "id": "dddddddd-dddd-dddd-dddd-dddddddddddd", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Magic4", "type": "<PERSON><PERSON>", "id": "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "OpenMenu", "type": "<PERSON><PERSON>", "id": "ffffffff-ffff-ffff-ffff-ffffffffffff", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "WASD", "id": "10101010-1010-1010-1010-101010101010", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "20202020-2020-2020-2020-202020202020", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "30303030-3030-3030-3030-303030303030", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "40404040-4040-4040-4040-404040404040", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "50505050-5050-5050-5050-505050505050", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "60606060-6060-6060-6060-606060606060", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "StickDeadzone(min=0.125,max=0.925)", "groups": "Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "70707070-7070-7070-7070-707070707070", "path": "<Mouse>/delta", "interactions": "", "processors": "ScaleVector2(x=0.05,y=0.05)", "groups": "Keyboard&Mouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "80808080-8080-8080-8080-808080808080", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "StickDeadzone(min=0.125,max=0.925),ScaleVector2(x=300,y=300)", "groups": "Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "90909090-9090-9090-9090-909090909090", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a0a0a0a0-a0a0-a0a0-a0a0-a0a0a0a0a0a0", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b0b0b0b0-b0b0-b0b0-b0b0-b0b0b0b0b0b0", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c0c0c0c0-c0c0-c0c0-c0c0-c0c0c0c0c0c0", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d0d0d0d0-d0d0-d0d0-d0d0-d0d0d0d0d0d0", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "HeavyAttack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e0e0e0e0-e0e0-e0e0-e0e0-e0e0e0e0e0e0", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "HeavyAttack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f0f0f0f0-f0f0-f0f0-f0f0-f0f0f0f0f0f0", "path": "<Keyboard>/q", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Block", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "01010101-0101-0101-0101-010101010101", "path": "<Gamepad>/leftShoulder", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Block", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "02020202-0202-0202-0202-020202020202", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Dodge", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "03030303-0303-0303-0303-030303030303", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Dodge", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "04040404-0404-0404-0404-040404040404", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "05050505-0505-0505-0505-050505050505", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "06060606-0606-0606-0606-060606060606", "path": "<Mouse>/scroll/y", "interactions": "", "processors": "Clamp(min=-1,max=1)", "groups": "Keyboard&Mouse", "action": "SwitchWeapon", "isComposite": false, "isPartOfComposite": false}, {"name": "D-Pa<PERSON>", "id": "07070707-0707-0707-0707-070707070707", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "SwitchWeapon", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "08080808-0808-0808-0808-080808080808", "path": "<Gamepad>/dpad/left", "interactions": "", "processors": "", "groups": "Gamepad", "action": "SwitchWeapon", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "09090909-0909-0909-0909-090909090909", "path": "<Gamepad>/dpad/right", "interactions": "", "processors": "", "groups": "Gamepad", "action": "SwitchWeapon", "isComposite": false, "isPartOfComposite": true}, {"name": "D-Pad Vertical", "id": "0a0a0a0a-0a0a-0a0a-0a0a-0a0a0a0a0a0a", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "SwitchMagic", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "0b0b0b0b-0b0b-0b0b-0b0b-0b0b0b0b0b0b", "path": "<Gamepad>/dpad/down", "interactions": "", "processors": "", "groups": "Gamepad", "action": "SwitchMagic", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "0c0c0c0c-0c0c-0c0c-0c0c-0c0c0c0c0c0c", "path": "<Gamepad>/dpad/up", "interactions": "", "processors": "", "groups": "Gamepad", "action": "SwitchMagic", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "0d0d0d0d-0d0d-0d0d-0d0d-0d0d0d0d0d0d", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Magic1", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0e0e0e0e-0e0e-0e0e-0e0e-0e0e0e0e0e0e", "path": "<Keyboard>/2", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Magic2", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0f0f0f0f-0f0f-0f0f-0f0f-0f0f0f0f0f0f", "path": "<Keyboard>/3", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Magic3", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "10101010-1010-1010-1010-101010101011", "path": "<Keyboard>/4", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Magic4", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "11111111-1111-1111-1111-111111111112", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "OpenMenu", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "12121212-1212-1212-1212-121212121212", "path": "<Gamepad>/start", "interactions": "", "processors": "", "groups": "Gamepad", "action": "OpenMenu", "isComposite": false, "isPartOfComposite": false}]}, {"name": "<PERSON><PERSON>", "id": "menu1111-1111-1111-1111-111111111111", "actions": [{"name": "Navigate", "type": "Value", "id": "nav11111-1111-1111-1111-111111111111", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Confirm", "type": "<PERSON><PERSON>", "id": "conf1111-1111-1111-1111-111111111111", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "canc1111-1111-1111-1111-111111111111", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "CloseMenu", "type": "<PERSON><PERSON>", "id": "clos1111-1111-1111-1111-111111111111", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Arrow Keys", "id": "arrow111-1111-1111-1111-111111111111", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "arrowup1-1111-1111-1111-111111111111", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "arrowdn1-1111-1111-1111-111111111111", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "arrowlf1-1111-1111-1111-111111111111", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "arrowrt1-1111-1111-1111-111111111111", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "dpad1111-1111-1111-1111-111111111111", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "lstick11-1111-1111-1111-111111111111", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "enter111-1111-1111-1111-111111111111", "path": "<Keyboard>/enter", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Confirm", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "btnsouth-1111-1111-1111-111111111111", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Confirm", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "backsp11-1111-1111-1111-111111111111", "path": "<Keyboard>/backspace", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "btneast1-1111-1111-1111-111111111111", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "escape11-1111-1111-1111-111111111111", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "CloseMenu", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "start111-1111-1111-1111-111111111111", "path": "<Gamepad>/start", "interactions": "", "processors": "", "groups": "Gamepad", "action": "CloseMenu", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}]}