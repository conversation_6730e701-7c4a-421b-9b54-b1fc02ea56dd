using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections.Generic;
using System.Collections;

public class WorldExplorationSystem : MonoBehaviour
{
    [Header("World Regions")]
    public WorldRegion[] availableRegions;
    public List<string> unlockedRegions = new List<string>();
    public string currentRegion = "Starting Village";
    
    [Header("Hidden Realms")]
    public HiddenRealm[] hiddenRealms;
    public List<string> discoveredRealms = new List<string>();
    
    [Header("Travel System")]
    public GameObject worldMapUI;
    public Transform[] travelPoints;
    public float travelCost = 50f; // Stamina cost
    
    private PlayerStats playerStats;
    private GameManager gameManager;
    private AtmosphericSystem atmosphericSystem;
    
    [System.Serializable]
    public class WorldRegion
    {
        [Header("Basic Info")]
        public string regionName;
        public string culturalName;
        public RegionType type;
        public string description;
        
        [Header("Environment")]
        public BiomeType biome;
        public WeatherPattern weatherPattern;
        public GameObject regionPrefab;
        public Material skyboxMaterial;
        
        [Header("Inhabitants")]
        public CulturalGroup[] cultures;
        public CreatureType[] nativeCreatures;
        public string[] uniqueNPCs;
        
        [Header("Unlock Requirements")]
        public int minimumLevel;
        public string[] requiredQuests;
        public PlayerStats.MoralPath preferredPath;
        
        [Header("Lore")]
        public string history;
        public string currentSituation;
        public string[] legendsAndMyths;
        
        public enum RegionType
        {
            Mainland,
            Island,
            Desert,
            Mountains,
            Forest,
            Coastal,
            Frozen,
            Volcanic,
            Mystical
        }
        
        public enum BiomeType
        {
            Temperate,
            Desert,
            Arctic,
            Tropical,
            Volcanic,
            Swamp,
            Grassland,
            Ocean,
            Underground
        }
        
        public enum WeatherPattern
        {
            Stable,
            Seasonal,
            Stormy,
            Mystical,
            Extreme
        }
        
        public enum CulturalGroup
        {
            Arabic,
            Greek,
            Norse,
            Celtic,
            Japanese,
            Chinese,
            Persian,
            Egyptian,
            Slavic,
            Mixed
        }
        
        public enum CreatureType
        {
            Natural,
            Mystical,
            Demonic,
            Angelic,
            Undead,
            Elemental,
            Draconic,
            Fey
        }
    }
    
    [System.Serializable]
    public class HiddenRealm
    {
        [Header("Realm Info")]
        public string realmName;
        public string culturalName;
        public RealmType type;
        public string description;
        
        [Header("Access")]
        public AccessMethod accessMethod;
        public string[] unlockRequirements;
        public GameObject portalPrefab;
        public Vector3 portalLocation;
        
        [Header("Environment")]
        public Color atmosphereColor;
        public float gravityMultiplier;
        public bool hasSpecialPhysics;
        public string[] uniqueFeatures;
        
        [Header("Inhabitants")]
        public string[] realmBeings;
        public BossData realmBoss;
        
        public enum RealmType
        {
            Hell,           // Demonic realm
            CloudCity,      // Celestial realm
            Atlantis,       // Sunken city of Atlas
            Shadowlands,    // Realm of shadows
            Dreamscape,     // Mental realm
            Void,           // Empty space between worlds
            Purgatory,      // Realm of judgment
            Paradise        // Perfect realm
        }
        
        public enum AccessMethod
        {
            Portal,
            Death,
            Meditation,
            SpecialItem,
            QuestCompletion,
            MoralChoice,
            SecretPath
        }
    }
    
    [System.Serializable]
    public class BossData
    {
        public string bossName;
        public string title;
        public float health;
        public string[] abilities;
        public string defeatReward;
    }
    
    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        gameManager = GameManager.Instance;
        atmosphericSystem = FindObjectOfType<AtmosphericSystem>();
        
        InitializeWorldRegions();
        InitializeHiddenRealms();
        LoadExplorationProgress();
        
        // Start with starting village unlocked
        if (!unlockedRegions.Contains("Starting Village"))
        {
            unlockedRegions.Add("Starting Village");
        }
    }
    
    void Update()
    {
        HandleWorldMapInput();
        CheckForNewRegionUnlocks();
        CheckForHiddenRealmDiscovery();
    }
    
    void InitializeWorldRegions()
    {
        // Desert Region - Arabic Culture
        WorldRegion desert = new WorldRegion
        {
            regionName = "Qadesh Wastes",
            culturalName = "صحراء القدس",
            type = WorldRegion.RegionType.Desert,
            biome = WorldRegion.BiomeType.Desert,
            weatherPattern = WorldRegion.WeatherPattern.Extreme,
            description = "Vast desert lands where the sun burns eternal and ancient secrets lie buried beneath shifting sands.",
            history = "Once the heart of a great empire, now home to nomadic tribes and forgotten ruins.",
            currentSituation = "Tribal conflicts over water sources and ancient artifacts.",
            minimumLevel = 8,
            preferredPath = PlayerStats.MoralPath.Moon
        };
        
        desert.cultures = new WorldRegion.CulturalGroup[] { WorldRegion.CulturalGroup.Arabic, WorldRegion.CulturalGroup.Persian };
        desert.nativeCreatures = new WorldRegion.CreatureType[] { WorldRegion.CreatureType.Elemental, WorldRegion.CreatureType.Demonic };
        desert.legendsAndMyths = new string[]
        {
            "The City of Brass lies hidden beneath the dunes",
            "Djinn still grant wishes to those pure of heart",
            "The Well of Souls connects all desert oases"
        };
        
        // Frozen North - Norse Culture
        WorldRegion frozen = new WorldRegion
        {
            regionName = "Jotunheim Reaches",
            culturalName = "Jötunheimr",
            type = WorldRegion.RegionType.Frozen,
            biome = WorldRegion.BiomeType.Arctic,
            weatherPattern = WorldRegion.WeatherPattern.Stormy,
            description = "Frozen wasteland where giants once walked and the aurora dances with ancient magic.",
            history = "Homeland of the frost giants, now mostly abandoned after the great war.",
            currentSituation = "Surviving clans struggle against eternal winter and awakening ancient evils.",
            minimumLevel = 15,
            preferredPath = PlayerStats.MoralPath.Eclipse
        };
        
        frozen.cultures = new WorldRegion.CulturalGroup[] { WorldRegion.CulturalGroup.Norse, WorldRegion.CulturalGroup.Slavic };
        frozen.nativeCreatures = new WorldRegion.CreatureType[] { WorldRegion.CreatureType.Undead, WorldRegion.CreatureType.Elemental };
        
        // Ocean Archipelago - Mixed Cultures
        WorldRegion ocean = new WorldRegion
        {
            regionName = "Cerulean Archipelago",
            culturalName = "Νησιά του Κυανού",
            type = WorldRegion.RegionType.Island,
            biome = WorldRegion.BiomeType.Ocean,
            weatherPattern = WorldRegion.WeatherPattern.Seasonal,
            description = "Chain of mystical islands where different cultures have found refuge from the mainland conflicts.",
            history = "Formed by ancient cataclysm, became sanctuary for displaced peoples.",
            currentSituation = "Peaceful trade confederation threatened by rising sea levels and sea monsters.",
            minimumLevel = 12,
            preferredPath = PlayerStats.MoralPath.Sun
        };
        
        ocean.cultures = new WorldRegion.CulturalGroup[] { WorldRegion.CulturalGroup.Greek, WorldRegion.CulturalGroup.Japanese, WorldRegion.CulturalGroup.Mixed };
        ocean.nativeCreatures = new WorldRegion.CreatureType[] { WorldRegion.CreatureType.Mystical, WorldRegion.CreatureType.Fey };
        
        availableRegions = new WorldRegion[] { desert, frozen, ocean };
    }
    
    void InitializeHiddenRealms()
    {
        // Hell Realm
        HiddenRealm hell = new HiddenRealm
        {
            realmName = "The Burning Depths",
            culturalName = "جهنم",
            type = HiddenRealm.RealmType.Hell,
            description = "Realm of eternal fire where demons forge weapons and souls are tested.",
            accessMethod = HiddenRealm.AccessMethod.Portal,
            atmosphereColor = new Color(1f, 0.3f, 0f),
            gravityMultiplier = 1.2f,
            hasSpecialPhysics = true
        };
        
        hell.unlockRequirements = new string[] { "DefeatDemonLord", "MoonAlignment50" };
        hell.realmBeings = new string[] { "Ifrit Overlords", "Fallen Angels", "Tortured Souls" };
        hell.uniqueFeatures = new string[] { "Rivers of molten metal", "Floating platforms of obsidian", "Eternal flames that burn cold" };
        
        // Cloud City Realm
        HiddenRealm cloudCity = new HiddenRealm
        {
            realmName = "Celestial Spires",
            culturalName = "مدينة السحاب",
            type = HiddenRealm.RealmType.CloudCity,
            description = "Floating city of light where angels dwell and divine judgment is passed.",
            accessMethod = HiddenRealm.AccessMethod.Meditation,
            atmosphereColor = new Color(1f, 1f, 0.8f),
            gravityMultiplier = 0.6f,
            hasSpecialPhysics = true
        };
        
        cloudCity.unlockRequirements = new string[] { "SunAlignment75", "CompleteTrialOfLight" };
        cloudCity.realmBeings = new string[] { "Seraphim", "Purified Souls", "Light Elementals" };
        cloudCity.uniqueFeatures = new string[] { "Bridges of solid light", "Fountains of liquid starlight", "Gardens that sing with harmony" };
        
        // Atlantis Realm
        HiddenRealm atlantis = new HiddenRealm
        {
            realmName = "Sunken Atlas",
            culturalName = "Ἀτλαντὶς νῆσος",
            type = HiddenRealm.RealmType.Atlantis,
            description = "The lost city of Atlas, preserved beneath the waves with ancient technology and wisdom.",
            accessMethod = HiddenRealm.AccessMethod.SpecialItem,
            atmosphereColor = new Color(0.2f, 0.6f, 1f),
            gravityMultiplier = 0.8f,
            hasSpecialPhysics = true
        };
        
        atlantis.unlockRequirements = new string[] { "FindAtlanteanKey", "SolveOceanRiddle" };
        atlantis.realmBeings = new string[] { "Atlantean Survivors", "Water Elementals", "Ancient Guardians" };
        atlantis.uniqueFeatures = new string[] { "Crystal technology", "Underwater breathing chambers", "Time-dilated research halls" };
        
        hiddenRealms = new HiddenRealm[] { hell, cloudCity, atlantis };
    }
    
    void LoadExplorationProgress()
    {
        string unlockedData = PlayerPrefs.GetString("UnlockedRegions", "");
        if (!string.IsNullOrEmpty(unlockedData))
        {
            unlockedRegions = new List<string>(unlockedData.Split(','));
        }
        
        string discoveredData = PlayerPrefs.GetString("DiscoveredRealms", "");
        if (!string.IsNullOrEmpty(discoveredData))
        {
            discoveredRealms = new List<string>(discoveredData.Split(','));
        }
        
        currentRegion = PlayerPrefs.GetString("CurrentRegion", "Starting Village");
    }
    
    void HandleWorldMapInput()
    {
        if (Input.GetKeyDown(KeyCode.M) && !IsInCombat())
        {
            ToggleWorldMap();
        }
    }
    
    bool IsInCombat()
    {
        // Check if player is in combat
        return false; // Placeholder
    }
    
    void ToggleWorldMap()
    {
        if (worldMapUI != null)
        {
            bool isActive = worldMapUI.activeInHierarchy;
            worldMapUI.SetActive(!isActive);
            
            if (!isActive)
            {
                Time.timeScale = 0f;
                Cursor.lockState = CursorLockMode.None;
            }
            else
            {
                Time.timeScale = 1f;
                Cursor.lockState = CursorLockMode.Locked;
            }
        }
    }
    
    void CheckForNewRegionUnlocks()
    {
        foreach (WorldRegion region in availableRegions)
        {
            if (!unlockedRegions.Contains(region.regionName) && CanUnlockRegion(region))
            {
                UnlockRegion(region);
            }
        }
    }
    
    bool CanUnlockRegion(WorldRegion region)
    {
        // Check level requirement
        if (playerStats.GetCurrentLevel() < region.minimumLevel)
            return false;
        
        // Check quest requirements
        foreach (string quest in region.requiredQuests)
        {
            if (!gameManager.IsQuestCompleted(quest))
                return false;
        }
        
        // Check moral path preference (not required, but influences unlock)
        if (region.preferredPath != PlayerStats.MoralPath.Eclipse && 
            region.preferredPath != playerStats.GetCurrentPath())
        {
            // Require higher level if not on preferred path
            if (playerStats.GetCurrentLevel() < region.minimumLevel + 5)
                return false;
        }
        
        return true;
    }
    
    void UnlockRegion(WorldRegion region)
    {
        unlockedRegions.Add(region.regionName);
        SaveExplorationProgress();
        
        ShowRegionUnlocked(region);
    }
    
    void ShowRegionUnlocked(WorldRegion region)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = $"New Region Discovered: {region.regionName}\n{region.description}";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 6f));
        }
    }
    
    void CheckForHiddenRealmDiscovery()
    {
        foreach (HiddenRealm realm in hiddenRealms)
        {
            if (!discoveredRealms.Contains(realm.realmName) && CanDiscoverRealm(realm))
            {
                DiscoverHiddenRealm(realm);
            }
        }
    }
    
    bool CanDiscoverRealm(HiddenRealm realm)
    {
        foreach (string requirement in realm.unlockRequirements)
        {
            if (!CheckRealmRequirement(requirement))
                return false;
        }
        return true;
    }
    
    bool CheckRealmRequirement(string requirement)
    {
        if (requirement.StartsWith("SunAlignment"))
        {
            float requiredAlignment = float.Parse(requirement.Substring(12));
            return playerStats.GetSunAlignment() >= requiredAlignment;
        }
        else if (requirement.StartsWith("MoonAlignment"))
        {
            float requiredAlignment = float.Parse(requirement.Substring(13));
            return playerStats.GetMoonAlignment() >= requiredAlignment;
        }
        else
        {
            // Check quest completion
            return gameManager.IsQuestCompleted(requirement);
        }
    }
    
    void DiscoverHiddenRealm(HiddenRealm realm)
    {
        discoveredRealms.Add(realm.realmName);
        SaveExplorationProgress();
        
        ShowHiddenRealmDiscovered(realm);
        CreateRealmPortal(realm);
    }
    
    void ShowHiddenRealmDiscovered(HiddenRealm realm)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            string message = $"Hidden Realm Discovered: {realm.realmName}\n{realm.description}";
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 8f));
        }
    }
    
    void CreateRealmPortal(HiddenRealm realm)
    {
        if (realm.portalPrefab != null)
        {
            GameObject portal = Instantiate(realm.portalPrefab, realm.portalLocation, Quaternion.identity);
            portal.name = $"Portal_to_{realm.realmName}";
            
            // Add portal interaction
            PortalController portalController = portal.AddComponent<PortalController>();
            portalController.targetRealm = realm.realmName;
            portalController.explorationSystem = this;
        }
    }
    
    public void TravelToRegion(string regionName)
    {
        if (!unlockedRegions.Contains(regionName))
        {
            Debug.LogWarning($"Region {regionName} is not unlocked!");
            return;
        }
        
        if (playerStats.GetCurrentStamina() < travelCost)
        {
            Debug.LogWarning("Not enough stamina to travel!");
            return;
        }
        
        // Consume stamina
        playerStats.ConsumeStamina(travelCost);
        
        // Change region
        currentRegion = regionName;
        SaveExplorationProgress();
        
        // Load new region
        StartCoroutine(LoadRegion(regionName));
    }
    
    IEnumerator LoadRegion(string regionName)
    {
        // Show loading screen
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"Traveling to {regionName}...");
        }
        
        yield return new WaitForSeconds(2f);
        
        // Apply region-specific settings
        WorldRegion region = GetRegionByName(regionName);
        if (region != null)
        {
            ApplyRegionSettings(region);
        }
        
        // Hide loading screen
        if (gameUI != null)
        {
            gameUI.HideInteractionPrompt();
        }
        
        Debug.Log($"Arrived in {regionName}");
    }
    
    void ApplyRegionSettings(WorldRegion region)
    {
        // Change skybox
        if (region.skyboxMaterial != null)
        {
            RenderSettings.skybox = region.skyboxMaterial;
        }
        
        // Update atmospheric system
        if (atmosphericSystem != null)
        {
            switch (region.weatherPattern)
            {
                case WorldRegion.WeatherPattern.Stormy:
                    atmosphericSystem.ForceWeather(AtmosphericSystem.WeatherType.Storm);
                    break;
                case WorldRegion.WeatherPattern.Extreme:
                    atmosphericSystem.ForceWeather(AtmosphericSystem.WeatherType.Dust);
                    break;
            }
        }
        
        // Spawn region-specific content
        SpawnRegionContent(region);
    }
    
    void SpawnRegionContent(WorldRegion region)
    {
        // Spawn region prefab if available
        if (region.regionPrefab != null)
        {
            Instantiate(region.regionPrefab, Vector3.zero, Quaternion.identity);
        }
        
        // Spawn cultural NPCs and creatures
        // This would be implemented based on the specific region data
    }
    
    WorldRegion GetRegionByName(string regionName)
    {
        foreach (WorldRegion region in availableRegions)
        {
            if (region.regionName == regionName)
                return region;
        }
        return null;
    }
    
    System.Collections.IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    void SaveExplorationProgress()
    {
        PlayerPrefs.SetString("UnlockedRegions", string.Join(",", unlockedRegions));
        PlayerPrefs.SetString("DiscoveredRealms", string.Join(",", discoveredRealms));
        PlayerPrefs.SetString("CurrentRegion", currentRegion);
        PlayerPrefs.Save();
    }
    
    // Getters
    public List<string> GetUnlockedRegions() => unlockedRegions;
    public List<string> GetDiscoveredRealms() => discoveredRealms;
    public string GetCurrentRegion() => currentRegion;
}

public class PortalController : MonoBehaviour
{
    public string targetRealm;
    public WorldExplorationSystem explorationSystem;
    
    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"Press E to enter {targetRealm}");
            }
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.HideInteractionPrompt();
            }
        }
    }
    
    void OnTriggerStay(Collider other)
    {
        if (other.CompareTag("Player") && Input.GetKeyDown(KeyCode.E))
        {
            EnterRealm();
        }
    }
    
    void EnterRealm()
    {
        // Transition to hidden realm
        Debug.Log($"Entering {targetRealm}...");
        // This would load the hidden realm scene
    }
}
