using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using System;

/// <summary>
/// Manages input configuration, remapping, and settings for Cinder of Darkness
/// Provides runtime input customization and device-specific settings
/// </summary>
public class InputConfigurationManager : MonoBehaviour
{
    [Header("Input System")]
    public CinderInput cinderInput;
    public MultiInputControlSystem multiInputSystem;
    
    [Header("Sensitivity Settings")]
    [Range(0.1f, 3f)]
    public float mouseSensitivity = 1f;
    
    [Range(0.1f, 3f)]
    public float gamepadSensitivity = 1f;
    
    [Header("Dead Zone Settings")]
    [Range(0f, 0.5f)]
    public float leftStickDeadZone = 0.125f;
    
    [Range(0f, 0.5f)]
    public float rightStickDeadZone = 0.125f;
    
    [Range(0f, 0.5f)]
    public float triggerDeadZone = 0.1f;
    
    [Header("Inversion Settings")]
    public bool invertMouseY = false;
    public bool invertGamepadY = false;
    public bool invertMouseX = false;
    public bool invertGamepadX = false;
    
    [Header("Vibration Settings")]
    [Range(0f, 1f)]
    public float vibrationIntensity = 1f;
    public bool vibrationEnabled = true;
    
    [Header("Auto-Save Settings")]
    public bool autoSaveSettings = true;
    public string settingsKey = "CinderInputSettings";
    
    // Events
    public event Action<string> OnControlSchemeChanged;
    public event Action OnSettingsChanged;
    
    // Private fields
    private Dictionary<string, string> bindingOverrides = new Dictionary<string, string>();
    private string currentControlScheme = "Keyboard&Mouse";
    
    void Start()
    {
        InitializeInputConfiguration();
        LoadSettings();
    }
    
    void OnDestroy()
    {
        if (autoSaveSettings)
        {
            SaveSettings();
        }
    }
    
    void InitializeInputConfiguration()
    {
        // Get references if not assigned
        if (cinderInput == null)
        {
            var multiInput = FindObjectOfType<MultiInputControlSystem>();
            if (multiInput != null)
            {
                cinderInput = multiInput.cinderInput;
            }
        }
        
        if (multiInputSystem == null)
        {
            multiInputSystem = FindObjectOfType<MultiInputControlSystem>();
        }
        
        // Apply initial settings
        ApplyAllSettings();
        
        Debug.Log("Input Configuration Manager initialized");
    }
    
    void Update()
    {
        // Monitor control scheme changes
        if (cinderInput != null)
        {
            string newScheme = cinderInput.GetCurrentControlScheme();
            if (newScheme != currentControlScheme)
            {
                currentControlScheme = newScheme;
                OnControlSchemeChanged?.Invoke(currentControlScheme);
                Debug.Log($"Control scheme changed to: {currentControlScheme}");
            }
        }
    }
    
    // Sensitivity Management
    public void SetMouseSensitivity(float sensitivity)
    {
        mouseSensitivity = Mathf.Clamp(sensitivity, 0.1f, 3f);
        ApplySensitivitySettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetGamepadSensitivity(float sensitivity)
    {
        gamepadSensitivity = Mathf.Clamp(sensitivity, 0.1f, 3f);
        ApplySensitivitySettings();
        OnSettingsChanged?.Invoke();
    }
    
    // Dead Zone Management
    public void SetLeftStickDeadZone(float deadZone)
    {
        leftStickDeadZone = Mathf.Clamp(deadZone, 0f, 0.5f);
        ApplyDeadZoneSettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetRightStickDeadZone(float deadZone)
    {
        rightStickDeadZone = Mathf.Clamp(deadZone, 0f, 0.5f);
        ApplyDeadZoneSettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetTriggerDeadZone(float deadZone)
    {
        triggerDeadZone = Mathf.Clamp(deadZone, 0f, 0.5f);
        ApplyDeadZoneSettings();
        OnSettingsChanged?.Invoke();
    }
    
    // Inversion Settings
    public void SetMouseYInversion(bool invert)
    {
        invertMouseY = invert;
        ApplyInversionSettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetGamepadYInversion(bool invert)
    {
        invertGamepadY = invert;
        ApplyInversionSettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetMouseXInversion(bool invert)
    {
        invertMouseX = invert;
        ApplyInversionSettings();
        OnSettingsChanged?.Invoke();
    }
    
    public void SetGamepadXInversion(bool invert)
    {
        invertGamepadX = invert;
        ApplyInversionSettings();
        OnSettingsChanged?.Invoke();
    }
    
    // Vibration Settings
    public void SetVibrationEnabled(bool enabled)
    {
        vibrationEnabled = enabled;
        if (multiInputSystem != null)
        {
            multiInputSystem.vibrationEnabled = enabled;
        }
        OnSettingsChanged?.Invoke();
    }
    
    public void SetVibrationIntensity(float intensity)
    {
        vibrationIntensity = Mathf.Clamp01(intensity);
        if (multiInputSystem != null)
        {
            multiInputSystem.vibrationIntensity = intensity;
        }
        OnSettingsChanged?.Invoke();
    }
    
    // Input Remapping
    public void StartRemapping(string actionName, Action<bool> onComplete = null)
    {
        if (cinderInput == null)
        {
            onComplete?.Invoke(false);
            return;
        }
        
        StartCoroutine(PerformRemapping(actionName, onComplete));
    }
    
    private System.Collections.IEnumerator PerformRemapping(string actionName, Action<bool> onComplete)
    {
        Debug.Log($"Starting remapping for action: {actionName}");
        
        // Find the action
        InputAction action = FindActionByName(actionName);
        if (action == null)
        {
            Debug.LogWarning($"Action '{actionName}' not found!");
            onComplete?.Invoke(false);
            yield break;
        }
        
        // Wait for input
        bool remappingComplete = false;
        bool remappingSuccessful = false;
        
        var rebindOperation = action.PerformInteractiveRebinding()
            .WithControlsExcluding("Mouse")
            .OnMatchWaitForAnother(0.1f)
            .OnComplete(operation =>
            {
                remappingComplete = true;
                remappingSuccessful = true;
                operation.Dispose();
            })
            .OnCancel(operation =>
            {
                remappingComplete = true;
                remappingSuccessful = false;
                operation.Dispose();
            })
            .Start();
        
        // Wait for completion
        yield return new WaitUntil(() => remappingComplete);
        
        if (remappingSuccessful)
        {
            Debug.Log($"Successfully remapped action: {actionName}");
            SaveBindingOverrides();
        }
        
        onComplete?.Invoke(remappingSuccessful);
    }
    
    public void ResetBinding(string actionName)
    {
        if (cinderInput == null) return;
        
        InputAction action = FindActionByName(actionName);
        if (action != null)
        {
            action.RemoveAllBindingOverrides();
            Debug.Log($"Reset binding for action: {actionName}");
            SaveBindingOverrides();
            OnSettingsChanged?.Invoke();
        }
    }
    
    public void ResetAllBindings()
    {
        if (cinderInput != null)
        {
            cinderInput.ResetAllBindings();
            bindingOverrides.Clear();
            Debug.Log("Reset all input bindings to defaults");
            SaveBindingOverrides();
            OnSettingsChanged?.Invoke();
        }
    }
    
    // Helper Methods
    private InputAction FindActionByName(string actionName)
    {
        if (cinderInput == null) return null;
        
        // Check gameplay actions
        var gameplayMap = cinderInput.Gameplay;
        var action = gameplayMap.FindAction(actionName);
        if (action != null) return action;
        
        // Check menu actions
        var menuMap = cinderInput.Menu;
        action = menuMap.FindAction(actionName);
        if (action != null) return action;
        
        return null;
    }
    
    private void ApplyAllSettings()
    {
        ApplySensitivitySettings();
        ApplyDeadZoneSettings();
        ApplyInversionSettings();
        ApplyVibrationSettings();
    }
    
    private void ApplySensitivitySettings()
    {
        // Apply mouse sensitivity
        if (cinderInput != null)
        {
            var lookAction = cinderInput.Look;
            if (lookAction != null)
            {
                // Apply sensitivity through processor
                string mouseProcessor = $"ScaleVector2(x={mouseSensitivity * 0.05f},y={mouseSensitivity * 0.05f})";
                string gamepadProcessor = $"StickDeadzone(min={rightStickDeadZone},max=0.925),ScaleVector2(x={gamepadSensitivity * 300f},y={gamepadSensitivity * 300f})";
                
                // Note: In a full implementation, you would apply these processors to specific bindings
                Debug.Log($"Applied sensitivity - Mouse: {mouseSensitivity}, Gamepad: {gamepadSensitivity}");
            }
        }
    }
    
    private void ApplyDeadZoneSettings()
    {
        // Apply dead zone settings to gamepad sticks
        Debug.Log($"Applied dead zones - Left: {leftStickDeadZone}, Right: {rightStickDeadZone}, Trigger: {triggerDeadZone}");
    }
    
    private void ApplyInversionSettings()
    {
        // Apply inversion settings
        Debug.Log($"Applied inversions - Mouse Y: {invertMouseY}, Gamepad Y: {invertGamepadY}");
    }
    
    private void ApplyVibrationSettings()
    {
        if (multiInputSystem != null)
        {
            multiInputSystem.vibrationEnabled = vibrationEnabled;
            multiInputSystem.vibrationIntensity = vibrationIntensity;
        }
    }
    
    // Save/Load System
    public void SaveSettings()
    {
        var settings = new InputSettings
        {
            mouseSensitivity = this.mouseSensitivity,
            gamepadSensitivity = this.gamepadSensitivity,
            leftStickDeadZone = this.leftStickDeadZone,
            rightStickDeadZone = this.rightStickDeadZone,
            triggerDeadZone = this.triggerDeadZone,
            invertMouseY = this.invertMouseY,
            invertGamepadY = this.invertGamepadY,
            invertMouseX = this.invertMouseX,
            invertGamepadX = this.invertGamepadX,
            vibrationEnabled = this.vibrationEnabled,
            vibrationIntensity = this.vibrationIntensity,
            bindingOverrides = this.bindingOverrides
        };
        
        string json = JsonUtility.ToJson(settings, true);
        PlayerPrefs.SetString(settingsKey, json);
        PlayerPrefs.Save();
        
        SaveBindingOverrides();
        
        Debug.Log("Input settings saved");
    }
    
    public void LoadSettings()
    {
        if (PlayerPrefs.HasKey(settingsKey))
        {
            string json = PlayerPrefs.GetString(settingsKey);
            var settings = JsonUtility.FromJson<InputSettings>(json);
            
            mouseSensitivity = settings.mouseSensitivity;
            gamepadSensitivity = settings.gamepadSensitivity;
            leftStickDeadZone = settings.leftStickDeadZone;
            rightStickDeadZone = settings.rightStickDeadZone;
            triggerDeadZone = settings.triggerDeadZone;
            invertMouseY = settings.invertMouseY;
            invertGamepadY = settings.invertGamepadY;
            invertMouseX = settings.invertMouseX;
            invertGamepadX = settings.invertGamepadX;
            vibrationEnabled = settings.vibrationEnabled;
            vibrationIntensity = settings.vibrationIntensity;
            bindingOverrides = settings.bindingOverrides ?? new Dictionary<string, string>();
            
            ApplyAllSettings();
            LoadBindingOverrides();
            
            Debug.Log("Input settings loaded");
        }
        else
        {
            Debug.Log("No saved input settings found, using defaults");
        }
    }
    
    private void SaveBindingOverrides()
    {
        if (cinderInput != null)
        {
            string overrides = cinderInput.GetBindingOverrides();
            PlayerPrefs.SetString(settingsKey + "_Bindings", overrides);
            PlayerPrefs.Save();
        }
    }
    
    private void LoadBindingOverrides()
    {
        if (cinderInput != null)
        {
            string key = settingsKey + "_Bindings";
            if (PlayerPrefs.HasKey(key))
            {
                string overrides = PlayerPrefs.GetString(key);
                cinderInput.LoadBindingOverrides(overrides);
            }
        }
    }
    
    // Public API for UI
    public string GetCurrentControlScheme() => currentControlScheme;
    public bool IsUsingGamepad() => cinderInput?.IsUsingGamepad() ?? false;
    public bool IsUsingKeyboardMouse() => cinderInput?.IsUsingKeyboardMouse() ?? false;
    
    public float GetMouseSensitivity() => mouseSensitivity;
    public float GetGamepadSensitivity() => gamepadSensitivity;
    public float GetLeftStickDeadZone() => leftStickDeadZone;
    public float GetRightStickDeadZone() => rightStickDeadZone;
    public float GetTriggerDeadZone() => triggerDeadZone;
    public bool GetMouseYInversion() => invertMouseY;
    public bool GetGamepadYInversion() => invertGamepadY;
    public bool GetVibrationEnabled() => vibrationEnabled;
    public float GetVibrationIntensity() => vibrationIntensity;
}

[System.Serializable]
public class InputSettings
{
    public float mouseSensitivity = 1f;
    public float gamepadSensitivity = 1f;
    public float leftStickDeadZone = 0.125f;
    public float rightStickDeadZone = 0.125f;
    public float triggerDeadZone = 0.1f;
    public bool invertMouseY = false;
    public bool invertGamepadY = false;
    public bool invertMouseX = false;
    public bool invertGamepadX = false;
    public bool vibrationEnabled = true;
    public float vibrationIntensity = 1f;
    public Dictionary<string, string> bindingOverrides = new Dictionary<string, string>();
}
