using UnityEngine;
using UnityEditor;
using System.IO;

#if UNITY_EDITOR
/// <summary>
/// Generates placeholder audio assets for Cinder of Darkness
/// Creates functional audio clips for testing and development
/// </summary>
public class AudioAssetGenerator : EditorWindow
{
    [MenuItem("Cinder of Darkness/Generate Audio Assets")]
    public static void ShowWindow()
    {
        GetWindow<AudioAssetGenerator>("Audio Asset Generator");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Cinder of Darkness - Audio Asset Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        if (GUILayout.Button("Generate All Audio Assets"))
        {
            GenerateAllAudioAssets();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.But<PERSON>("Generate Spirit Whispers"))
        {
            GenerateSpiritWhispers();
        }
        
        if (GUILayout.Button("Generate Ancient Melodies"))
        {
            GenerateAncientMelodies();
        }
        
        if (GUILayout.But<PERSON>("Generate Narrative Music"))
        {
            GenerateNarrativeMusic();
        }
        
        if (GUILayout.But<PERSON>("Generate Atmospheric Sounds"))
        {
            GenerateAtmosphericSounds();
        }
    }
    
    void GenerateAllAudioAssets()
    {
        Debug.Log("Generating all audio assets for Cinder of Darkness...");
        
        CreateAudioDirectories();
        GenerateSpiritWhispers();
        GenerateAncientMelodies();
        GenerateNarrativeMusic();
        GenerateAtmosphericSounds();
        GenerateCosmicSounds();
        GenerateContemplativeSounds();
        GenerateRegretSounds();
        
        AssetDatabase.Refresh();
        Debug.Log("All audio assets generated successfully!");
    }
    
    void CreateAudioDirectories()
    {
        string[] directories = {
            "Assets/Audio",
            "Assets/Audio/Whispers",
            "Assets/Audio/Melodies",
            "Assets/Audio/Narrative",
            "Assets/Audio/Atmospheric",
            "Assets/Audio/Cosmic",
            "Assets/Audio/Contemplative",
            "Assets/Audio/Regret",
            "Assets/Audio/Cultural",
            "Assets/Audio/Combat",
            "Assets/Audio/UI"
        };
        
        foreach (string dir in directories)
        {
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
        }
    }
    
    void GenerateSpiritWhispers()
    {
        Debug.Log("Generating spirit whisper audio clips...");
        
        // Generate 8 spirit whisper variations
        for (int i = 1; i <= 8; i++)
        {
            AudioClip whisper = GenerateWhisperClip($"spirit_whisper_{i:D2}", 3f + (i * 0.5f));
            SaveAudioClip(whisper, $"Assets/Audio/Whispers/spirit_whisper_{i:D2}.wav");
        }
        
        // Generate dying breath sounds
        for (int i = 1; i <= 4; i++)
        {
            AudioClip breath = GenerateBreathClip($"dying_breath_{i:D2}", 2f + (i * 0.3f));
            SaveAudioClip(breath, $"Assets/Audio/Whispers/dying_breath_{i:D2}.wav");
        }
    }
    
    void GenerateAncientMelodies()
    {
        Debug.Log("Generating ancient melody audio clips...");
        
        // Generate 6 ancient melodies
        for (int i = 1; i <= 6; i++)
        {
            AudioClip melody = GenerateMelodyClip($"ancient_melody_{i:D2}", 15f + (i * 5f));
            SaveAudioClip(melody, $"Assets/Audio/Melodies/ancient_melody_{i:D2}.wav");
        }
        
        // Generate humming variations
        for (int i = 1; i <= 4; i++)
        {
            AudioClip humming = GenerateHummingClip($"ancient_humming_{i:D2}", 8f + (i * 2f));
            SaveAudioClip(humming, $"Assets/Audio/Melodies/ancient_humming_{i:D2}.wav");
        }
    }
    
    void GenerateNarrativeMusic()
    {
        Debug.Log("Generating narrative music stingers...");
        
        // Generate emotional stingers
        string[] emotions = { "sorrow", "hope", "dread", "revelation", "peace", "conflict" };
        
        foreach (string emotion in emotions)
        {
            AudioClip stinger = GenerateStingerClip($"stinger_{emotion}", 4f);
            SaveAudioClip(stinger, $"Assets/Audio/Narrative/stinger_{emotion}.wav");
        }
        
        // Generate transition music
        for (int i = 1; i <= 3; i++)
        {
            AudioClip transition = GenerateTransitionClip($"transition_{i:D2}", 6f + (i * 2f));
            SaveAudioClip(transition, $"Assets/Audio/Narrative/transition_{i:D2}.wav");
        }
    }
    
    void GenerateAtmosphericSounds()
    {
        Debug.Log("Generating atmospheric sound effects...");
        
        // Generate wind variations
        for (int i = 1; i <= 5; i++)
        {
            AudioClip wind = GenerateWindClip($"wind_ambient_{i:D2}", 20f + (i * 5f));
            SaveAudioClip(wind, $"Assets/Audio/Atmospheric/wind_ambient_{i:D2}.wav");
        }
        
        // Generate fire crackling
        for (int i = 1; i <= 3; i++)
        {
            AudioClip fire = GenerateFireClip($"fire_crackle_{i:D2}", 12f + (i * 3f));
            SaveAudioClip(fire, $"Assets/Audio/Atmospheric/fire_crackle_{i:D2}.wav");
        }
        
        // Generate ash falling
        AudioClip ashFall = GenerateAshFallClip("ash_falling", 30f);
        SaveAudioClip(ashFall, "Assets/Audio/Atmospheric/ash_falling.wav");
    }
    
    void GenerateCosmicSounds()
    {
        Debug.Log("Generating cosmic encounter audio...");
        
        // Cosmic presence sound
        AudioClip cosmicPresence = GenerateCosmicPresenceClip("cosmic_presence", 45f);
        SaveAudioClip(cosmicPresence, "Assets/Audio/Cosmic/cosmic_presence.wav");
        
        // Reality distortion
        AudioClip distortion = GenerateDistortionClip("reality_distortion", 15f);
        SaveAudioClip(distortion, "Assets/Audio/Cosmic/reality_distortion.wav");
        
        // Unknown voice
        AudioClip unknownVoice = GenerateUnknownVoiceClip("unknown_voice", 8f);
        SaveAudioClip(unknownVoice, "Assets/Audio/Cosmic/unknown_voice.wav");
    }
    
    void GenerateContemplativeSounds()
    {
        Debug.Log("Generating contemplative audio...");
        
        // Water flowing
        AudioClip waterFlow = GenerateWaterFlowClip("water_flow", 25f);
        SaveAudioClip(waterFlow, "Assets/Audio/Contemplative/water_flow.wav");
        
        // Temple bells
        for (int i = 1; i <= 3; i++)
        {
            AudioClip bell = GenerateTempleBellClip($"temple_bell_{i:D2}", 5f + (i * 2f));
            SaveAudioClip(bell, $"Assets/Audio/Contemplative/temple_bell_{i:D2}.wav");
        }
        
        // Children laughter
        for (int i = 1; i <= 4; i++)
        {
            AudioClip laughter = GenerateChildLaughterClip($"child_laughter_{i:D2}", 3f + (i * 1f));
            SaveAudioClip(laughter, $"Assets/Audio/Contemplative/child_laughter_{i:D2}.wav");
        }
    }
    
    void GenerateRegretSounds()
    {
        Debug.Log("Generating regret system audio...");
        
        // Inner monologue whispers
        for (int i = 1; i <= 6; i++)
        {
            AudioClip monologue = GenerateMonologueClip($"inner_monologue_{i:D2}", 4f + (i * 1f));
            SaveAudioClip(monologue, $"Assets/Audio/Regret/inner_monologue_{i:D2}.wav");
        }
        
        // Haunting melodies
        for (int i = 1; i <= 3; i++)
        {
            AudioClip haunting = GenerateHauntingMelodyClip($"haunting_melody_{i:D2}", 12f + (i * 4f));
            SaveAudioClip(haunting, $"Assets/Audio/Regret/haunting_melody_{i:D2}.wav");
        }
        
        // Silence ambience
        AudioClip silence = GenerateSilenceAmbienceClip("silence_ambience", 60f);
        SaveAudioClip(silence, "Assets/Audio/Regret/silence_ambience.wav");
    }
    
    // Audio generation methods using Unity's AudioClip.Create
    AudioClip GenerateWhisperClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate ethereal whisper-like sound
            data[i] = Mathf.Sin(time * 200f + Mathf.Sin(time * 3f)) * 0.1f * 
                     Mathf.Exp(-time * 0.5f) * (1f + 0.3f * Mathf.PerlinNoise(time * 10f, 0f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateBreathClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate breathing sound
            data[i] = Mathf.Sin(time * 0.5f * Mathf.PI) * 0.05f * 
                     (1f + 0.5f * Mathf.PerlinNoise(time * 20f, 0f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateMelodyClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        float[] notes = { 261.63f, 293.66f, 329.63f, 349.23f, 392.00f, 440.00f, 493.88f }; // C major scale
        
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            int noteIndex = Mathf.FloorToInt(time * 2f) % notes.Length;
            float frequency = notes[noteIndex];
            
            // Generate ancient melody with reverb-like effect
            data[i] = Mathf.Sin(time * frequency * 2f * Mathf.PI) * 0.15f * 
                     Mathf.Exp(-((time % 0.5f) * 4f)) * 
                     (1f + 0.2f * Mathf.Sin(time * 5f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateHummingClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate humming sound
            data[i] = Mathf.Sin(time * 220f * 2f * Mathf.PI) * 0.1f * 
                     (1f + 0.3f * Mathf.Sin(time * 3f)) * 
                     Mathf.Exp(-time * 0.1f);
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateStingerClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate dramatic stinger
            data[i] = (Mathf.Sin(time * 80f * 2f * Mathf.PI) + 
                      Mathf.Sin(time * 120f * 2f * Mathf.PI)) * 0.2f * 
                     Mathf.Exp(-time * 2f);
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateTransitionClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate transition music
            data[i] = Mathf.Sin(time * 110f * 2f * Mathf.PI) * 0.1f * 
                     (1f - time / length) * 
                     (1f + 0.4f * Mathf.Sin(time * 0.5f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateWindClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate wind sound using noise
            data[i] = (Mathf.PerlinNoise(time * 50f, 0f) - 0.5f) * 0.15f * 
                     (1f + 0.5f * Mathf.Sin(time * 0.3f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateFireClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate fire crackling
            data[i] = (Mathf.PerlinNoise(time * 100f, 0f) - 0.5f) * 0.2f * 
                     (1f + 0.8f * Mathf.PerlinNoise(time * 20f, 1f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateAshFallClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate subtle ash falling sound
            data[i] = (Mathf.PerlinNoise(time * 30f, 0f) - 0.5f) * 0.05f * 
                     (1f + 0.3f * Mathf.Sin(time * 0.1f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateCosmicPresenceClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate cosmic presence sound
            data[i] = Mathf.Sin(time * 30f * 2f * Mathf.PI) * 0.3f * 
                     (1f + 0.5f * Mathf.Sin(time * 0.1f)) * 
                     (1f + 0.3f * Mathf.PerlinNoise(time * 5f, 0f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateDistortionClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate reality distortion effect
            data[i] = (Mathf.PerlinNoise(time * 80f, 0f) - 0.5f) * 0.25f * 
                     Mathf.Sin(time * 10f) * 
                     (1f + 0.7f * Mathf.Sin(time * 0.3f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateUnknownVoiceClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate otherworldly voice
            data[i] = Mathf.Sin(time * 60f * 2f * Mathf.PI) * 0.2f * 
                     (1f + 0.5f * Mathf.Sin(time * 3f)) * 
                     Mathf.Exp(-time * 0.3f);
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateWaterFlowClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate water flowing sound
            data[i] = (Mathf.PerlinNoise(time * 60f, 0f) - 0.5f) * 0.12f * 
                     (1f + 0.4f * Mathf.Sin(time * 2f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateTempleBellClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate temple bell sound
            data[i] = Mathf.Sin(time * 440f * 2f * Mathf.PI) * 0.3f * 
                     Mathf.Exp(-time * 1.5f) * 
                     (1f + 0.2f * Mathf.Sin(time * 880f * 2f * Mathf.PI));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateChildLaughterClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate child laughter
            data[i] = Mathf.Sin(time * 800f * 2f * Mathf.PI) * 0.15f * 
                     (1f + 0.8f * Mathf.Sin(time * 15f)) * 
                     Mathf.Exp(-time * 0.8f);
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateMonologueClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate inner monologue whisper
            data[i] = Mathf.Sin(time * 150f * 2f * Mathf.PI) * 0.08f * 
                     (1f + 0.4f * Mathf.Sin(time * 4f)) * 
                     (1f + 0.3f * Mathf.PerlinNoise(time * 8f, 0f));
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateHauntingMelodyClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate haunting melody
            data[i] = (Mathf.Sin(time * 220f * 2f * Mathf.PI) + 
                      Mathf.Sin(time * 330f * 2f * Mathf.PI)) * 0.1f * 
                     (1f + 0.5f * Mathf.Sin(time * 0.5f)) * 
                     Mathf.Exp(-time * 0.2f);
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    AudioClip GenerateSilenceAmbienceClip(string name, float length)
    {
        int sampleRate = 44100;
        int samples = Mathf.RoundToInt(sampleRate * length);
        AudioClip clip = AudioClip.Create(name, samples, 1, sampleRate, false);
        
        float[] data = new float[samples];
        for (int i = 0; i < samples; i++)
        {
            float time = (float)i / sampleRate;
            // Generate very subtle silence ambience
            data[i] = (Mathf.PerlinNoise(time * 5f, 0f) - 0.5f) * 0.02f;
        }
        
        clip.SetData(data, 0);
        return clip;
    }
    
    void SaveAudioClip(AudioClip clip, string path)
    {
        // In a real implementation, this would save the audio clip to disk
        // For now, we'll create the asset in the project
        AssetDatabase.CreateAsset(clip, path);
    }
}
#endif
