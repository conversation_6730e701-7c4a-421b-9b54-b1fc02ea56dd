using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Collections;
using TMPro;

/// <summary>
/// Event UI System for Dynamic World Events in Cinder of Darkness
/// Handles event notifications, player interaction prompts, and event journal integration
/// </summary>
public class EventUI : MonoBehaviour
{
    [Header("Event Notification UI")]
    public GameObject eventNotificationPrefab;
    public Transform notificationParent;
    public float notificationDuration = 5f;
    public int maxNotifications = 3;
    
    [<PERSON><PERSON>("Event Interaction UI")]
    public GameObject eventInteractionPanel;
    public TextMeshProUGUI eventTitleText;
    public TextMeshProUGUI eventDescriptionText;
    public Image eventIconImage;
    public Transform interactionButtonsParent;
    public GameObject interactionButtonPrefab;
    
    [Header("Event Journal UI")]
    public GameObject eventJournalPanel;
    public Transform activeEventsParent;
    public Transform completedEventsParent;
    public GameObject eventEntryPrefab;
    public Button journalToggleButton;
    
    [Header("World Map Integration")]
    public GameObject worldMapEventMarkerPrefab;
    public Transform worldMapParent;
    public Sprite[] eventTypeIcons;
    
    [Header("Minimap Integration")]
    public GameObject minimapEventMarkerPrefab;
    public Transform minimapParent;
    public float minimapMarkerScale = 0.5f;
    
    [Header("Audio")]
    public AudioSource audioSource;
    public AudioClip eventNotificationSound;
    public AudioClip eventCompletedSound;
    public AudioClip eventFailedSound;
    
    // Static instance
    private static EventUI instance;
    public static EventUI Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<EventUI>();
                if (instance == null)
                {
                    GameObject go = new GameObject("EventUI");
                    instance = go.AddComponent<EventUI>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // UI state management
    private List<GameObject> activeNotifications = new List<GameObject>();
    private Dictionary<string, GameObject> worldMapMarkers = new Dictionary<string, GameObject>();
    private Dictionary<string, GameObject> minimapMarkers = new Dictionary<string, GameObject>();
    private EventManager.ActiveEvent currentInteractionEvent;
    private bool isJournalOpen = false;
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeEventUI();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        SetupUI();
    }
    
    void InitializeEventUI()
    {
        Debug.Log("Event UI initialized");
    }
    
    void SetupUI()
    {
        // Hide panels initially
        if (eventInteractionPanel != null)
            eventInteractionPanel.SetActive(false);
        
        if (eventJournalPanel != null)
            eventJournalPanel.SetActive(false);
        
        // Setup journal toggle button
        if (journalToggleButton != null)
            journalToggleButton.onClick.AddListener(ToggleEventJournal);
        
        // Find audio source if not assigned
        if (audioSource == null)
            audioSource = GetComponent<AudioSource>();
        
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
    }
    
    /// <summary>
    /// Show event notification
    /// </summary>
    public void ShowEventNotification(EventManager.ActiveEvent activeEvent)
    {
        if (eventNotificationPrefab == null || notificationParent == null) return;
        
        // Remove oldest notification if at max capacity
        if (activeNotifications.Count >= maxNotifications)
        {
            var oldestNotification = activeNotifications[0];
            activeNotifications.RemoveAt(0);
            Destroy(oldestNotification);
        }
        
        // Create notification
        GameObject notification = Instantiate(eventNotificationPrefab, notificationParent);
        activeNotifications.Add(notification);
        
        // Setup notification content
        SetupNotificationContent(notification, activeEvent);
        
        // Play notification sound
        PlayNotificationSound(eventNotificationSound);
        
        // Auto-remove after duration
        StartCoroutine(RemoveNotificationAfterDelay(notification, notificationDuration));
        
        // Create world map marker
        CreateWorldMapMarker(activeEvent);
        
        // Create minimap marker
        CreateMinimapMarker(activeEvent);
    }
    
    /// <summary>
    /// Setup notification content
    /// </summary>
    void SetupNotificationContent(GameObject notification, EventManager.ActiveEvent activeEvent)
    {
        // Set event title
        var titleText = notification.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
            titleText.text = activeEvent.eventData.eventName;
        
        // Set event description
        var descText = notification.transform.Find("Description")?.GetComponent<TextMeshProUGUI>();
        if (descText != null)
            descText.text = GetEventNotificationText(activeEvent);
        
        // Set event icon
        var iconImage = notification.transform.Find("Icon")?.GetComponent<Image>();
        if (iconImage != null && activeEvent.eventData.eventIcon != null)
            iconImage.sprite = activeEvent.eventData.eventIcon;
        
        // Set severity color
        var backgroundImage = notification.GetComponent<Image>();
        if (backgroundImage != null)
            backgroundImage.color = GetSeverityColor(activeEvent.eventData.severity);
        
        // Setup interaction button
        var interactButton = notification.transform.Find("InteractButton")?.GetComponent<Button>();
        if (interactButton != null && activeEvent.eventData.allowsPlayerIntervention)
        {
            interactButton.gameObject.SetActive(true);
            interactButton.onClick.AddListener(() => ShowEventInteraction(activeEvent));
        }
        else if (interactButton != null)
        {
            interactButton.gameObject.SetActive(false);
        }
    }
    
    /// <summary>
    /// Get notification text based on event
    /// </summary>
    string GetEventNotificationText(EventManager.ActiveEvent activeEvent)
    {
        string baseText = activeEvent.eventData.eventDescription;
        
        // Add affected areas
        if (activeEvent.eventData.affectedRealms.Length > 0)
        {
            baseText += $"\nAffected: {string.Join(", ", activeEvent.eventData.affectedRealms)}";
        }
        
        // Add duration info
        float remainingTime = activeEvent.GetRemainingTime();
        if (remainingTime > 0)
        {
            int days = Mathf.FloorToInt(remainingTime);
            baseText += $"\nDuration: {days} days remaining";
        }
        
        return baseText;
    }
    
    /// <summary>
    /// Get color based on event severity
    /// </summary>
    Color GetSeverityColor(WorldEvent.EventSeverity severity)
    {
        switch (severity)
        {
            case WorldEvent.EventSeverity.Minor:
                return new Color(0.2f, 0.8f, 0.2f, 0.8f); // Green
            case WorldEvent.EventSeverity.Moderate:
                return new Color(0.8f, 0.8f, 0.2f, 0.8f); // Yellow
            case WorldEvent.EventSeverity.Major:
                return new Color(0.8f, 0.4f, 0.2f, 0.8f); // Orange
            case WorldEvent.EventSeverity.Critical:
                return new Color(0.8f, 0.2f, 0.2f, 0.8f); // Red
            default:
                return new Color(0.5f, 0.5f, 0.5f, 0.8f); // Gray
        }
    }
    
    /// <summary>
    /// Remove notification after delay
    /// </summary>
    IEnumerator RemoveNotificationAfterDelay(GameObject notification, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (notification != null)
        {
            activeNotifications.Remove(notification);
            
            // Fade out animation
            var canvasGroup = notification.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = notification.AddComponent<CanvasGroup>();
            
            float fadeTime = 0.5f;
            float elapsed = 0f;
            
            while (elapsed < fadeTime)
            {
                canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsed / fadeTime);
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            Destroy(notification);
        }
    }
    
    /// <summary>
    /// Show event interaction panel
    /// </summary>
    public void ShowEventInteraction(EventManager.ActiveEvent activeEvent)
    {
        if (eventInteractionPanel == null) return;
        
        currentInteractionEvent = activeEvent;
        eventInteractionPanel.SetActive(true);
        
        // Setup event info
        if (eventTitleText != null)
            eventTitleText.text = activeEvent.eventData.eventName;
        
        if (eventDescriptionText != null)
            eventDescriptionText.text = activeEvent.eventData.eventDescription;
        
        if (eventIconImage != null && activeEvent.eventData.eventIcon != null)
            eventIconImage.sprite = activeEvent.eventData.eventIcon;
        
        // Setup interaction buttons
        SetupInteractionButtons(activeEvent);
        
        // Pause game time (optional)
        Time.timeScale = 0f;
    }
    
    /// <summary>
    /// Setup interaction buttons
    /// </summary>
    void SetupInteractionButtons(EventManager.ActiveEvent activeEvent)
    {
        if (interactionButtonsParent == null || interactionButtonPrefab == null) return;
        
        // Clear existing buttons
        foreach (Transform child in interactionButtonsParent)
        {
            Destroy(child.gameObject);
        }
        
        // Create buttons for each interaction option
        foreach (var option in activeEvent.eventData.interventionOptions)
        {
            GameObject buttonObj = Instantiate(interactionButtonPrefab, interactionButtonsParent);
            
            var button = buttonObj.GetComponent<Button>();
            var buttonText = buttonObj.GetComponentInChildren<TextMeshProUGUI>();
            
            if (buttonText != null)
                buttonText.text = option.optionText;
            
            if (button != null)
            {
                button.onClick.AddListener(() => SelectInteractionOption(option.optionId));
            }
            
            // Check requirements
            bool canUse = CheckInteractionRequirements(option);
            button.interactable = canUse;
            
            if (!canUse)
            {
                buttonText.color = Color.gray;
            }
        }
    }
    
    /// <summary>
    /// Check if player meets interaction requirements
    /// </summary>
    bool CheckInteractionRequirements(WorldEvent.PlayerInteractionOption option)
    {
        // Check requirements (would integrate with player stats, inventory, etc.)
        foreach (var requirement in option.requirements)
        {
            // Implementation would check specific requirements
            // For now, assume all requirements are met
        }
        
        return true;
    }
    
    /// <summary>
    /// Select interaction option
    /// </summary>
    void SelectInteractionOption(string optionId)
    {
        if (currentInteractionEvent == null) return;
        
        // Send interaction to event manager
        EventManager.PlayerInteractWithEvent(currentInteractionEvent.eventId, optionId);
        
        // Close interaction panel
        CloseEventInteraction();
        
        // Show result notification
        ShowInteractionResult(optionId);
    }
    
    /// <summary>
    /// Close event interaction panel
    /// </summary>
    void CloseEventInteraction()
    {
        if (eventInteractionPanel != null)
            eventInteractionPanel.SetActive(false);
        
        currentInteractionEvent = null;
        
        // Resume game time
        Time.timeScale = 1f;
    }
    
    /// <summary>
    /// Show interaction result
    /// </summary>
    void ShowInteractionResult(string optionId)
    {
        // Create a result notification
        // Implementation would show the outcome of the player's choice
        Debug.Log($"Player chose interaction option: {optionId}");
    }
    
    /// <summary>
    /// Toggle event journal
    /// </summary>
    public void ToggleEventJournal()
    {
        isJournalOpen = !isJournalOpen;
        
        if (eventJournalPanel != null)
            eventJournalPanel.SetActive(isJournalOpen);
        
        if (isJournalOpen)
        {
            RefreshEventJournal();
        }
    }
    
    /// <summary>
    /// Refresh event journal content
    /// </summary>
    void RefreshEventJournal()
    {
        RefreshActiveEvents();
        RefreshCompletedEvents();
    }
    
    /// <summary>
    /// Refresh active events in journal
    /// </summary>
    void RefreshActiveEvents()
    {
        if (activeEventsParent == null || eventEntryPrefab == null) return;
        
        // Clear existing entries
        foreach (Transform child in activeEventsParent)
        {
            Destroy(child.gameObject);
        }
        
        // Add active events
        var activeEvents = EventManager.GetActiveEvents();
        foreach (var activeEvent in activeEvents)
        {
            CreateEventJournalEntry(activeEvent, activeEventsParent, true);
        }
    }
    
    /// <summary>
    /// Refresh completed events in journal
    /// </summary>
    void RefreshCompletedEvents()
    {
        if (completedEventsParent == null || eventEntryPrefab == null) return;
        
        // Clear existing entries
        foreach (Transform child in completedEventsParent)
        {
            Destroy(child.gameObject);
        }
        
        // Add completed events (last 10)
        var eventHistory = EventManager.GetEventHistory();
        var recentEvents = eventHistory.GetRange(Mathf.Max(0, eventHistory.Count - 10), Mathf.Min(10, eventHistory.Count));
        
        foreach (var completedEvent in recentEvents)
        {
            CreateCompletedEventEntry(completedEvent, completedEventsParent);
        }
    }
    
    /// <summary>
    /// Create event journal entry
    /// </summary>
    void CreateEventJournalEntry(EventManager.ActiveEvent activeEvent, Transform parent, bool isActive)
    {
        GameObject entry = Instantiate(eventEntryPrefab, parent);
        
        // Setup entry content
        var titleText = entry.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
            titleText.text = activeEvent.eventData.eventName;
        
        var descText = entry.transform.Find("Description")?.GetComponent<TextMeshProUGUI>();
        if (descText != null)
            descText.text = activeEvent.eventData.eventDescription;
        
        var progressText = entry.transform.Find("Progress")?.GetComponent<TextMeshProUGUI>();
        if (progressText != null && isActive)
        {
            float remainingTime = activeEvent.GetRemainingTime();
            int days = Mathf.FloorToInt(remainingTime);
            progressText.text = $"{days} days remaining";
        }
        
        var iconImage = entry.transform.Find("Icon")?.GetComponent<Image>();
        if (iconImage != null && activeEvent.eventData.eventIcon != null)
            iconImage.sprite = activeEvent.eventData.eventIcon;
        
        // Setup interact button for active events
        var interactButton = entry.transform.Find("InteractButton")?.GetComponent<Button>();
        if (interactButton != null && isActive && activeEvent.eventData.allowsPlayerIntervention)
        {
            interactButton.gameObject.SetActive(true);
            interactButton.onClick.AddListener(() => ShowEventInteraction(activeEvent));
        }
        else if (interactButton != null)
        {
            interactButton.gameObject.SetActive(false);
        }
    }
    
    /// <summary>
    /// Create completed event entry
    /// </summary>
    void CreateCompletedEventEntry(EventManager.CompletedEvent completedEvent, Transform parent)
    {
        GameObject entry = Instantiate(eventEntryPrefab, parent);
        
        // Setup entry content
        var titleText = entry.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
            titleText.text = completedEvent.eventName;
        
        var statusText = entry.transform.Find("Status")?.GetComponent<TextMeshProUGUI>();
        if (statusText != null)
        {
            statusText.text = completedEvent.finalState.ToString();
            statusText.color = GetStateColor(completedEvent.finalState);
        }
        
        var timeText = entry.transform.Find("Time")?.GetComponent<TextMeshProUGUI>();
        if (timeText != null)
        {
            float duration = completedEvent.endTime - completedEvent.startTime;
            int days = Mathf.FloorToInt(duration / 24f);
            timeText.text = $"Duration: {days} days";
        }
        
        // Hide interact button for completed events
        var interactButton = entry.transform.Find("InteractButton")?.GetComponent<Button>();
        if (interactButton != null)
            interactButton.gameObject.SetActive(false);
    }
    
    /// <summary>
    /// Get color for event state
    /// </summary>
    Color GetStateColor(EventManager.EventState state)
    {
        switch (state)
        {
            case EventManager.EventState.Completed:
                return Color.green;
            case EventManager.EventState.Failed:
                return Color.red;
            case EventManager.EventState.Cancelled:
                return Color.yellow;
            default:
                return Color.white;
        }
    }
    
    /// <summary>
    /// Create world map marker
    /// </summary>
    void CreateWorldMapMarker(EventManager.ActiveEvent activeEvent)
    {
        if (worldMapEventMarkerPrefab == null || worldMapParent == null) return;
        
        // Only create markers for events affecting specific areas
        if (activeEvent.eventData.affectedRealms.Length == 0) return;
        
        GameObject marker = Instantiate(worldMapEventMarkerPrefab, worldMapParent);
        worldMapMarkers[activeEvent.eventId] = marker;
        
        // Position marker based on affected realm
        // Implementation would position based on realm coordinates
        
        // Setup marker icon
        var iconImage = marker.GetComponent<Image>();
        if (iconImage != null)
        {
            iconImage.sprite = GetEventTypeIcon(activeEvent.eventData.eventType);
            iconImage.color = GetSeverityColor(activeEvent.eventData.severity);
        }
        
        // Setup marker tooltip
        var tooltip = marker.GetComponent<Tooltip>();
        if (tooltip == null)
            tooltip = marker.AddComponent<Tooltip>();
        
        tooltip.tooltipText = $"{activeEvent.eventData.eventName}\n{activeEvent.eventData.eventDescription}";
    }
    
    /// <summary>
    /// Create minimap marker
    /// </summary>
    void CreateMinimapMarker(EventManager.ActiveEvent activeEvent)
    {
        if (minimapEventMarkerPrefab == null || minimapParent == null) return;
        
        GameObject marker = Instantiate(minimapEventMarkerPrefab, minimapParent);
        marker.transform.localScale = Vector3.one * minimapMarkerScale;
        minimapMarkers[activeEvent.eventId] = marker;
        
        // Setup marker icon
        var iconImage = marker.GetComponent<Image>();
        if (iconImage != null)
        {
            iconImage.sprite = GetEventTypeIcon(activeEvent.eventData.eventType);
            iconImage.color = GetSeverityColor(activeEvent.eventData.severity);
        }
    }
    
    /// <summary>
    /// Get icon for event type
    /// </summary>
    Sprite GetEventTypeIcon(WorldEvent.EventType eventType)
    {
        if (eventTypeIcons == null || eventTypeIcons.Length == 0) return null;
        
        int index = (int)eventType;
        if (index >= 0 && index < eventTypeIcons.Length)
        {
            return eventTypeIcons[index];
        }
        
        return eventTypeIcons[0]; // Default icon
    }
    
    /// <summary>
    /// Remove event markers
    /// </summary>
    public void RemoveEventMarkers(string eventId)
    {
        // Remove world map marker
        if (worldMapMarkers.ContainsKey(eventId))
        {
            Destroy(worldMapMarkers[eventId]);
            worldMapMarkers.Remove(eventId);
        }
        
        // Remove minimap marker
        if (minimapMarkers.ContainsKey(eventId))
        {
            Destroy(minimapMarkers[eventId]);
            minimapMarkers.Remove(eventId);
        }
    }
    
    /// <summary>
    /// Play notification sound
    /// </summary>
    void PlayNotificationSound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// Show event completion notification
    /// </summary>
    public void ShowEventCompletionNotification(string eventName, EventManager.EventState finalState)
    {
        // Create completion notification
        string message = "";
        AudioClip soundClip = null;
        
        switch (finalState)
        {
            case EventManager.EventState.Completed:
                message = $"Event Resolved: {eventName}";
                soundClip = eventCompletedSound;
                break;
            case EventManager.EventState.Failed:
                message = $"Event Failed: {eventName}";
                soundClip = eventFailedSound;
                break;
            case EventManager.EventState.Cancelled:
                message = $"Event Cancelled: {eventName}";
                break;
        }
        
        // Show notification (implementation would create a simple notification)
        Debug.Log(message);
        PlayNotificationSound(soundClip);
    }
    
    // Public API
    public static void ShowEventNotificationStatic(EventManager.ActiveEvent activeEvent)
    {
        Instance.ShowEventNotification(activeEvent);
    }
    
    public static void ShowEventInteractionStatic(EventManager.ActiveEvent activeEvent)
    {
        Instance.ShowEventInteraction(activeEvent);
    }
    
    public static void RemoveEventMarkersStatic(string eventId)
    {
        Instance.RemoveEventMarkers(eventId);
    }
    
    public static void ShowEventCompletionStatic(string eventName, EventManager.EventState finalState)
    {
        Instance.ShowEventCompletionNotification(eventName, finalState);
    }
}
