using UnityEngine;
using System.Collections;

public class PlayerCombat : MonoBehaviour
{
    [Header("Weapon Settings")]
    public WeaponType currentWeapon = WeaponType.LightSword;
    public Transform weaponHolder;
    public GameObject lightSwordPrefab;
    public GameObject heavyMacePrefab;

    [Header("Combat Settings")]
    public float attackCooldown = 1f;
    public float magicCooldown = 2f;
    public float attackRange = 2f;
    public LayerMask enemyLayer;

    [Header("Magic Settings")]
    public GameObject fireballPrefab;
    public Transform magicCastPoint;
    public float fireballSpeed = 20f;

    private PlayerStats playerStats;
    private PlayerController playerController;
    private Animator animator;
    private bool isAttacking = false;
    private bool isCastingMagic = false;
    private float lastAttackTime;
    private float lastMagicTime;
    private GameObject currentWeaponObject;

    // Events for combat system integration
    public System.Action<Vector3, float, GameObject> OnAttackHit;
    public System.Action<GameObject> OnEnemyKilled;

    public enum WeaponType
    {
        LightSword,  // Fast, low damage
        HeavyMace    // Slow, high damage
    }

    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        playerController = GetComponent<PlayerController>();
        animator = GetComponent<Animator>();

        EquipWeapon(currentWeapon);
    }

    void Update()
    {
        HandleWeaponSwitching();
    }

    void HandleWeaponSwitching()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            EquipWeapon(WeaponType.LightSword);
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            EquipWeapon(WeaponType.HeavyMace);
        }
    }

    public void PerformAttack()
    {
        if (isAttacking || Time.time < lastAttackTime + attackCooldown)
            return;

        if (!playerStats.CanAttack())
        {
            Debug.Log("Not enough stamina to attack!");
            return;
        }

        StartCoroutine(AttackCoroutine());
    }

    IEnumerator AttackCoroutine()
    {
        isAttacking = true;
        lastAttackTime = Time.time;

        // Consume stamina
        playerStats.ConsumeStamina(playerStats.attackStaminaCost);

        // Play attack animation
        if (animator != null)
        {
            animator.SetTrigger("Attack");
        }

        // Different attack properties based on weapon
        float damage = playerStats.attackDamage;
        float range = attackRange;

        switch (currentWeapon)
        {
            case WeaponType.LightSword:
                damage *= 0.8f; // Lower damage
                range *= 1.0f;  // Normal range
                break;
            case WeaponType.HeavyMace:
                damage *= 1.5f; // Higher damage
                range *= 0.8f;  // Shorter range
                break;
        }

        // Wait for attack animation timing
        yield return new WaitForSeconds(0.3f);

        // Perform raycast attack
        PerformMeleeAttack(damage, range);

        // Wait for attack to finish
        yield return new WaitForSeconds(attackCooldown - 0.3f);

        isAttacking = false;
    }

    void PerformMeleeAttack(float damage, float range)
    {
        Vector3 attackDirection = playerController.GetCameraForward();
        Vector3 attackOrigin = transform.position + Vector3.up * 1.5f;

        RaycastHit hit;
        if (Physics.Raycast(attackOrigin, attackDirection, out hit, range, enemyLayer))
        {
            // Check if we hit an enemy
            EnemyHealth enemyHealth = hit.collider.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                enemyHealth.TakeDamage(damage);
                Debug.Log($"Hit enemy for {damage} damage!");

                // Trigger hit event
                OnAttackHit?.Invoke(hit.point, damage, hit.collider.gameObject);

                // Check if enemy was killed
                if (enemyHealth.GetCurrentHealth() <= 0)
                {
                    OnEnemyKilled?.Invoke(hit.collider.gameObject);
                }

                // Add screen shake or hit effects here
                CreateHitEffect(hit.point);
            }
        }

        // Debug visualization
        Debug.DrawRay(attackOrigin, attackDirection * range, Color.red, 1f);
    }

    public void CastMagic()
    {
        if (isCastingMagic || Time.time < lastMagicTime + magicCooldown)
            return;

        if (!playerStats.CanCastMagic())
        {
            Debug.Log("Not enough mana to cast magic!");
            return;
        }

        StartCoroutine(MagicCoroutine());
    }

    IEnumerator MagicCoroutine()
    {
        isCastingMagic = true;
        lastMagicTime = Time.time;

        // Consume mana
        playerStats.ConsumeMana(playerStats.magicManaCost);

        // Play magic animation
        if (animator != null)
        {
            animator.SetTrigger("CastMagic");
        }

        // Wait for cast time
        yield return new WaitForSeconds(0.5f);

        // Cast fireball
        CastFireball();

        // Wait for cooldown
        yield return new WaitForSeconds(magicCooldown - 0.5f);

        isCastingMagic = false;
    }

    void CastFireball()
    {
        if (fireballPrefab == null || magicCastPoint == null)
        {
            Debug.LogWarning("Fireball prefab or cast point not assigned!");
            return;
        }

        Vector3 castDirection = playerController.GetCameraForward();
        GameObject fireball = Instantiate(fireballPrefab, magicCastPoint.position, Quaternion.LookRotation(castDirection));

        // Add velocity to fireball
        Rigidbody fireballRb = fireball.GetComponent<Rigidbody>();
        if (fireballRb != null)
        {
            fireballRb.velocity = castDirection * fireballSpeed;
        }

        // Set fireball damage
        Fireball fireballScript = fireball.GetComponent<Fireball>();
        if (fireballScript != null)
        {
            fireballScript.SetDamage(playerStats.magicDamage);
        }

        Debug.Log("Fireball cast!");
    }

    public void EquipWeapon(WeaponType weaponType)
    {
        // Destroy current weapon
        if (currentWeaponObject != null)
        {
            DestroyImmediate(currentWeaponObject);
        }

        currentWeapon = weaponType;

        // Instantiate new weapon
        GameObject weaponPrefab = null;
        switch (weaponType)
        {
            case WeaponType.LightSword:
                weaponPrefab = lightSwordPrefab;
                break;
            case WeaponType.HeavyMace:
                weaponPrefab = heavyMacePrefab;
                break;
        }

        if (weaponPrefab != null && weaponHolder != null)
        {
            currentWeaponObject = Instantiate(weaponPrefab, weaponHolder);
        }

        Debug.Log($"Equipped weapon: {weaponType}");
    }

    void CreateHitEffect(Vector3 position)
    {
        // Create particle effect or visual feedback
        // For prototype, just a simple debug sphere
        GameObject hitEffect = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        hitEffect.transform.position = position;
        hitEffect.transform.localScale = Vector3.one * 0.2f;
        hitEffect.GetComponent<Renderer>().material.color = Color.red;

        // Destroy after short time
        Destroy(hitEffect, 0.5f);
    }

    public bool IsAttacking() => isAttacking;
    public bool IsCastingMagic() => isCastingMagic;
    public WeaponType GetCurrentWeapon() => currentWeapon;
}
