using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class WorldInteractionSystem : MonoBehaviour
{
    [Header("Destructible Environment")]
    public LayerMask destructibleLayers = -1;
    public float fireSpreadRadius = 5f;
    public float fireSpreadSpeed = 2f;
    public GameObject firePrefab;
    
    [Header("Emotional Interactions")]
    public float childInteractionRange = 3f;
    public AudioClip[] childLaughterSounds;
    public AudioClip[] reflectiveMusic;
    
    [Header("Cooking System")]
    public GameObject[] cookingStations;
    public CookingRecipe[] availableRecipes;
    public float healthRestoreMultiplier = 1.5f;
    
    [Header("Training System")]
    public GameObject[] trainingDummies;
    public float muscleGrowthRate = 0.1f;
    public float maxMuscleGrowth = 2f;
    public TrainingExercise[] availableExercises;
    
    [Header("World Atmosphere")]
    public bool worldIsDarkAndMelancholic = true;
    public Color melancholicSkyColor = new Color(0.4f, 0.4f, 0.5f);
    public Color happinessColor = new Color(1f, 0.9f, 0.7f);
    
    private PlayerStats playerStats;
    private PsychologicalSystem psycheSystem;
    private List<GameObject> activeFires = new List<GameObject>();
    private float currentMuscleGrowth = 0f;
    
    [System.Serializable]
    public class CookingRecipe
    {
        public string recipeName;
        public string[] requiredIngredients;
        public float healthRestore;
        public float staminaRestore;
        public float cookingTime;
        public string description;
        public GameObject resultPrefab;
    }
    
    [System.Serializable]
    public class TrainingExercise
    {
        public string exerciseName;
        public ExerciseType type;
        public float muscleGrowthAmount;
        public float staminaCost;
        public float duration;
        public string description;
        
        public enum ExerciseType
        {
            Strength,
            Agility,
            Endurance,
            Combat,
            Meditation
        }
    }
    
    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        psycheSystem = GetComponent<PsychologicalSystem>();
        
        InitializeWorldAtmosphere();
        SetupInteractionSystems();
    }
    
    void Update()
    {
        CheckForInteractions();
        UpdateFireSpread();
        UpdateWorldAtmosphere();
    }
    
    void InitializeWorldAtmosphere()
    {
        // Set the world's dark, melancholic tone
        if (worldIsDarkAndMelancholic)
        {
            RenderSettings.ambientLight = melancholicSkyColor;
            RenderSettings.fog = true;
            RenderSettings.fogColor = melancholicSkyColor;
            RenderSettings.fogStartDistance = 20f;
            RenderSettings.fogEndDistance = 100f;
        }
        
        Debug.Log("World atmosphere initialized - dark, melancholic, filled with trauma and hatred");
    }
    
    void SetupInteractionSystems()
    {
        // Find and setup cooking stations
        cookingStations = GameObject.FindGameObjectsWithTag("CookingStation");
        
        // Find and setup training dummies
        trainingDummies = GameObject.FindGameObjectsWithTag("TrainingDummy");
        
        Debug.Log($"Found {cookingStations.Length} cooking stations and {trainingDummies.Length} training dummies");
    }
    
    void CheckForInteractions()
    {
        // Check for child interactions
        CheckChildInteractions();
        
        // Check for cooking interactions
        CheckCookingInteractions();
        
        // Check for training interactions
        CheckTrainingInteractions();
        
        // Check for destructible interactions
        if (Input.GetKeyDown(KeyCode.F))
        {
            CheckDestructibleInteractions();
        }
    }
    
    void CheckChildInteractions()
    {
        // Find children in range
        Collider[] nearbyObjects = Physics.OverlapSphere(transform.position, childInteractionRange);
        
        foreach (Collider obj in nearbyObjects)
        {
            NPCController npc = obj.GetComponent<NPCController>();
            if (npc != null && npc.npcType == NPCController.NPCType.Child)
            {
                if (Input.GetKeyDown(KeyCode.E))
                {
                    InteractWithChild(npc);
                }
                
                // Show interaction prompt
                GameUI gameUI = FindObjectOfType<GameUI>();
                if (gameUI != null)
                {
                    gameUI.ShowInteractionPrompt("Press E to play with the child");
                }
            }
        }
    }
    
    void InteractWithChild(NPCController child)
    {
        // Emotional reflection when seeing children play
        if (psycheSystem != null)
        {
            psycheSystem.ReduceTrauma(5f, "Finding joy in a child's innocence");
        }
        
        // Play child laughter
        if (childLaughterSounds.Length > 0)
        {
            AudioSource.PlayClipAtPoint(childLaughterSounds[Random.Range(0, childLaughterSounds.Length)], 
                                      child.transform.position);
        }
        
        // Show reflective dialogue
        string[] reflectiveThoughts = {
            "Their laughter reminds me of what innocence looks like...",
            "In their eyes, I see the world as it should be - free from hatred.",
            "Perhaps there is still hope for this world, if children can still smile.",
            "I must protect this innocence. It's what makes us human.",
            "Their joy is a light in this dark world. I will not let it be extinguished."
        };
        
        string thought = reflectiveThoughts[Random.Range(0, reflectiveThoughts.Length)];
        ShowInteractionMessage(thought);
        
        // Temporarily brighten the world around the child
        StartCoroutine(CreateHappinessAura(child.transform.position));
        
        Debug.Log("The Cinderborn reflects on innocence and hope");
    }
    
    IEnumerator CreateHappinessAura(Vector3 position)
    {
        // Create a temporary light source representing happiness
        GameObject happinessLight = new GameObject("HappinessAura");
        happinessLight.transform.position = position;
        
        Light light = happinessLight.AddComponent<Light>();
        light.color = happinessColor;
        light.intensity = 2f;
        light.range = 10f;
        light.type = LightType.Point;
        
        // Fade in
        float duration = 3f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float intensity = Mathf.Lerp(0f, 2f, elapsed / duration);
            light.intensity = intensity;
            yield return null;
        }
        
        // Hold for a moment
        yield return new WaitForSeconds(5f);
        
        // Fade out
        elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float intensity = Mathf.Lerp(2f, 0f, elapsed / duration);
            light.intensity = intensity;
            yield return null;
        }
        
        Destroy(happinessLight);
    }
    
    void CheckCookingInteractions()
    {
        foreach (GameObject station in cookingStations)
        {
            if (station != null && Vector3.Distance(transform.position, station.transform.position) < 2f)
            {
                if (Input.GetKeyDown(KeyCode.C))
                {
                    StartCooking(station);
                }
                
                GameUI gameUI = FindObjectOfType<GameUI>();
                if (gameUI != null)
                {
                    gameUI.ShowInteractionPrompt("Press C to cook food");
                }
            }
        }
    }
    
    void StartCooking(GameObject cookingStation)
    {
        // Show cooking menu
        ShowCookingMenu();
    }
    
    void ShowCookingMenu()
    {
        // This would open a cooking UI
        ShowInteractionMessage("Cooking restores both body and spirit...");
        
        // For now, just cook a basic meal
        CookBasicMeal();
    }
    
    void CookBasicMeal()
    {
        if (playerStats != null)
        {
            float healthRestore = 50f * healthRestoreMultiplier;
            playerStats.RestoreHealth(healthRestore);
            
            ShowInteractionMessage($"A warm meal restores {healthRestore} health and brings comfort to the soul.");
            
            // Cooking provides psychological comfort
            if (psycheSystem != null)
            {
                psycheSystem.ReduceTrauma(3f, "Finding peace in simple cooking");
            }
        }
    }
    
    void CheckTrainingInteractions()
    {
        foreach (GameObject dummy in trainingDummies)
        {
            if (dummy != null && Vector3.Distance(transform.position, dummy.transform.position) < 2f)
            {
                if (Input.GetKeyDown(KeyCode.T))
                {
                    StartTraining(dummy);
                }
                
                GameUI gameUI = FindObjectOfType<GameUI>();
                if (gameUI != null)
                {
                    gameUI.ShowInteractionPrompt("Press T to train");
                }
            }
        }
    }
    
    void StartTraining(GameObject trainingDummy)
    {
        // Show training options
        ShowTrainingMenu();
    }
    
    void ShowTrainingMenu()
    {
        // For now, just do basic strength training
        PerformStrengthTraining();
    }
    
    void PerformStrengthTraining()
    {
        if (playerStats != null && playerStats.GetCurrentStamina() >= 20f)
        {
            // Consume stamina
            playerStats.ConsumeStamina(20f);
            
            // Increase muscle growth
            currentMuscleGrowth = Mathf.Min(currentMuscleGrowth + muscleGrowthRate, maxMuscleGrowth);
            
            // Apply physical changes
            ApplyMuscleGrowth();
            
            ShowInteractionMessage($"Training builds strength. Muscle growth: {currentMuscleGrowth:F1}/{maxMuscleGrowth}");
            
            // Training provides mental discipline
            if (psycheSystem != null)
            {
                psycheSystem.ReduceTrauma(1f, "Finding discipline through training");
            }
        }
        else
        {
            ShowInteractionMessage("Too tired to train effectively. Rest first.");
        }
    }
    
    void ApplyMuscleGrowth()
    {
        // Apply visual muscle growth to character model
        CharacterProgression characterProgression = GetComponent<CharacterProgression>();
        if (characterProgression != null)
        {
            float muscleScale = 1f + (currentMuscleGrowth / maxMuscleGrowth) * 0.3f; // Up to 30% size increase
            characterProgression.SetMuscleScale(muscleScale);
        }
        
        // Apply stat bonuses
        if (playerStats != null)
        {
            float strengthBonus = currentMuscleGrowth * 5f; // 5 strength per muscle growth point
            playerStats.AddTemporaryStatBonus("Muscle", strengthBonus, 0f, 0f);
        }
    }
    
    void CheckDestructibleInteractions()
    {
        // Raycast to find destructible objects
        RaycastHit hit;
        if (Physics.Raycast(transform.position, transform.forward, out hit, 5f, destructibleLayers))
        {
            DestructibleObject destructible = hit.collider.GetComponent<DestructibleObject>();
            if (destructible != null)
            {
                DestroyObject(destructible);
            }
        }
    }
    
    void DestroyObject(DestructibleObject destructible)
    {
        if (destructible.isFlammable)
        {
            // Start fire
            StartFire(destructible.transform.position);
        }
        
        // Destroy the object
        destructible.DestroyObject();
        
        ShowInteractionMessage($"Destroyed {destructible.objectName}");
        
        // Destruction can be cathartic or traumatic depending on context
        if (destructible.isInnocentProperty)
        {
            if (psycheSystem != null)
            {
                psycheSystem.AddTrauma(2f, "Destroying innocent property");
            }
        }
        else
        {
            if (psycheSystem != null)
            {
                psycheSystem.ReduceTrauma(1f, "Cathartic destruction");
            }
        }
    }
    
    void StartFire(Vector3 position)
    {
        if (firePrefab != null)
        {
            GameObject fire = Instantiate(firePrefab, position, Quaternion.identity);
            activeFires.Add(fire);
            
            // Fire spreads over time
            StartCoroutine(SpreadFire(fire));
        }
    }
    
    IEnumerator SpreadFire(GameObject originalFire)
    {
        yield return new WaitForSeconds(fireSpreadSpeed);
        
        if (originalFire != null)
        {
            // Find nearby flammable objects
            Collider[] nearbyObjects = Physics.OverlapSphere(originalFire.transform.position, fireSpreadRadius);
            
            foreach (Collider obj in nearbyObjects)
            {
                DestructibleObject destructible = obj.GetComponent<DestructibleObject>();
                if (destructible != null && destructible.isFlammable && !destructible.isDestroyed)
                {
                    // Spread fire
                    StartFire(destructible.transform.position);
                    destructible.DestroyObject();
                }
            }
        }
    }
    
    void UpdateFireSpread()
    {
        // Remove destroyed fires
        activeFires.RemoveAll(fire => fire == null);
    }
    
    void UpdateWorldAtmosphere()
    {
        // The world remains dark and melancholic, with brief moments of happiness
        if (worldIsDarkAndMelancholic)
        {
            // Gradually return to melancholic atmosphere
            RenderSettings.ambientLight = Color.Lerp(RenderSettings.ambientLight, melancholicSkyColor, Time.deltaTime * 0.1f);
        }
    }
    
    void ShowInteractionMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 4f));
        }
        
        Debug.Log($"World Interaction: {message}");
    }
    
    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }
    
    // Getters
    public float GetMuscleGrowth() => currentMuscleGrowth;
    public int GetActiveFireCount() => activeFires.Count;
}

public class DestructibleObject : MonoBehaviour
{
    [Header("Destructible Properties")]
    public string objectName = "Object";
    public bool isFlammable = false;
    public bool isInnocentProperty = false;
    public bool isDestroyed = false;
    
    [Header("Destruction Effects")]
    public GameObject destructionEffect;
    public AudioClip destructionSound;
    public GameObject[] debrisPrefabs;
    
    public void DestroyObject()
    {
        if (isDestroyed) return;
        
        isDestroyed = true;
        
        // Play destruction effects
        if (destructionEffect != null)
        {
            Instantiate(destructionEffect, transform.position, transform.rotation);
        }
        
        if (destructionSound != null)
        {
            AudioSource.PlayClipAtPoint(destructionSound, transform.position);
        }
        
        // Create debris
        if (debrisPrefabs.Length > 0)
        {
            for (int i = 0; i < Random.Range(2, 5); i++)
            {
                GameObject debris = Instantiate(debrisPrefabs[Random.Range(0, debrisPrefabs.Length)], 
                                              transform.position + Random.insideUnitSphere * 2f, 
                                              Random.rotation);
                
                // Add physics to debris
                Rigidbody debrisRb = debris.GetComponent<Rigidbody>();
                if (debrisRb == null)
                {
                    debrisRb = debris.AddComponent<Rigidbody>();
                }
                
                debrisRb.AddForce(Random.insideUnitSphere * 300f);
                
                // Remove debris after time
                Destroy(debris, Random.Range(10f, 30f));
            }
        }
        
        // Destroy the original object
        Destroy(gameObject);
    }
}
