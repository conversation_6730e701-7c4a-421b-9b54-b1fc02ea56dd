using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Collections;
using TMPro;
using System.Linq;

/// <summary>
/// World Map Event Indicators for Dynamic World Events in Cinder of Darkness
/// Displays active and incoming events on the world map with visual indicators
/// </summary>
public class WorldMapEventIndicators : MonoBehaviour
{
    [Header("World Map References")]
    public Transform worldMapContainer;
    public Camera worldMapCamera;
    public Canvas worldMapCanvas;
    
    [Header("Event Indicator Prefabs")]
    public GameObject activeEventIndicatorPrefab;
    public GameObject upcomingEventIndicatorPrefab;
    public GameObject completedEventIndicatorPrefab;
    
    [Header("Indicator Settings")]
    public float indicatorScale = 1f;
    public float pulseSpeed = 2f;
    public float pulseIntensity = 0.3f;
    public bool enableIndicatorAnimation = true;
    
    [Header("Area Coordinates")]
    public AreaCoordinateSet areaCoordinates;
    
    [Header("Tooltip System")]
    public GameObject eventTooltipPrefab;
    public float tooltipDelay = 0.5f;
    
    [Header("Visual Effects")]
    public ParticleSystem eventParticleEffect;
    public AudioSource eventIndicatorAudio;
    
    // Static instance
    private static WorldMapEventIndicators instance;
    public static WorldMapEventIndicators Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<WorldMapEventIndicators>();
                if (instance == null)
                {
                    GameObject go = new GameObject("WorldMapEventIndicators");
                    instance = go.AddComponent<WorldMapEventIndicators>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Indicator management
    private Dictionary<string, GameObject> activeIndicators = new Dictionary<string, GameObject>();
    private Dictionary<string, Coroutine> indicatorAnimations = new Dictionary<string, Coroutine>();
    private GameObject currentTooltip;
    private Coroutine tooltipCoroutine;
    
    /// <summary>
    /// Area coordinate mapping for world map positioning
    /// </summary>
    [System.Serializable]
    public class AreaCoordinateSet
    {
        public AreaCoordinate[] areaCoordinates;
        
        public Vector2 GetCoordinates(string areaName)
        {
            var area = areaCoordinates.FirstOrDefault(a => a.areaName == areaName);
            return area?.mapPosition ?? Vector2.zero;
        }
        
        public bool HasArea(string areaName)
        {
            return areaCoordinates.Any(a => a.areaName == areaName);
        }
    }
    
    /// <summary>
    /// Individual area coordinate
    /// </summary>
    [System.Serializable]
    public class AreaCoordinate
    {
        public string areaName;
        public Vector2 mapPosition;
        public float indicatorSize = 1f;
    }
    
    /// <summary>
    /// Event indicator types
    /// </summary>
    public enum IndicatorType
    {
        Active,
        Upcoming,
        Completed
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeIndicatorSystem();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        StartCoroutine(UpdateIndicators());
        SubscribeToEventSystem();
    }
    
    void InitializeIndicatorSystem()
    {
        // Find required components if not assigned
        if (worldMapContainer == null)
            worldMapContainer = GameObject.Find("WorldMapContainer")?.transform;
        
        if (worldMapCamera == null)
            worldMapCamera = GameObject.Find("WorldMapCamera")?.GetComponent<Camera>();
        
        if (worldMapCanvas == null)
            worldMapCanvas = GetComponentInParent<Canvas>();
        
        Debug.Log("World Map Event Indicators initialized");
    }
    
    void SubscribeToEventSystem()
    {
        // Subscribe to event manager notifications
        // EventManager.OnEventStarted += OnEventStarted;
        // EventManager.OnEventCompleted += OnEventCompleted;
        // EventManager.OnEventEscalated += OnEventEscalated;
    }
    
    /// <summary>
    /// Update indicators continuously
    /// </summary>
    IEnumerator UpdateIndicators()
    {
        while (true)
        {
            RefreshAllIndicators();
            yield return new WaitForSeconds(2f);
        }
    }
    
    /// <summary>
    /// Refresh all event indicators
    /// </summary>
    void RefreshAllIndicators()
    {
        // Get current events
        var activeEvents = EventManager.GetActiveEvents();
        var currentEventIds = new HashSet<string>(activeEvents.Select(e => e.eventId));
        
        // Remove indicators for events that are no longer active
        var indicatorsToRemove = activeIndicators.Keys.Where(id => !currentEventIds.Contains(id)).ToList();
        foreach (var eventId in indicatorsToRemove)
        {
            RemoveIndicator(eventId);
        }
        
        // Add or update indicators for active events
        foreach (var activeEvent in activeEvents)
        {
            if (!activeIndicators.ContainsKey(activeEvent.eventId))
            {
                CreateEventIndicator(activeEvent, IndicatorType.Active);
            }
            else
            {
                UpdateEventIndicator(activeEvent);
            }
        }
    }
    
    /// <summary>
    /// Create event indicator on world map
    /// </summary>
    void CreateEventIndicator(EventManager.ActiveEvent activeEvent, IndicatorType indicatorType)
    {
        if (worldMapContainer == null) return;
        
        // Get position for event
        Vector2 mapPosition = GetEventMapPosition(activeEvent);
        if (mapPosition == Vector2.zero) return;
        
        // Select appropriate prefab
        GameObject prefab = GetIndicatorPrefab(indicatorType);
        if (prefab == null) return;
        
        // Create indicator
        GameObject indicator = Instantiate(prefab, worldMapContainer);
        indicator.transform.localPosition = new Vector3(mapPosition.x, mapPosition.y, 0);
        indicator.transform.localScale = Vector3.one * indicatorScale;
        
        // Setup indicator content
        SetupIndicatorContent(indicator, activeEvent, indicatorType);
        
        // Store indicator reference
        activeIndicators[activeEvent.eventId] = indicator;
        
        // Start animation if enabled
        if (enableIndicatorAnimation)
        {
            var animationCoroutine = StartCoroutine(AnimateIndicator(indicator, activeEvent.eventData.severity));
            indicatorAnimations[activeEvent.eventId] = animationCoroutine;
        }
        
        // Play creation effect
        PlayIndicatorCreationEffect(mapPosition, activeEvent.eventData.eventType);
    }
    
    /// <summary>
    /// Get map position for event
    /// </summary>
    Vector2 GetEventMapPosition(EventManager.ActiveEvent activeEvent)
    {
        if (areaCoordinates == null) return Vector2.zero;
        
        // Try to get position from affected realms
        foreach (var realm in activeEvent.eventData.affectedRealms)
        {
            if (areaCoordinates.HasArea(realm))
            {
                return areaCoordinates.GetCoordinates(realm);
            }
        }
        
        // Try affected towns
        foreach (var town in activeEvent.eventData.affectedTowns)
        {
            if (areaCoordinates.HasArea(town))
            {
                return areaCoordinates.GetCoordinates(town);
            }
        }
        
        // Try affected zones
        foreach (var zone in activeEvent.eventData.affectedZones)
        {
            if (areaCoordinates.HasArea(zone))
            {
                return areaCoordinates.GetCoordinates(zone);
            }
        }
        
        return Vector2.zero;
    }
    
    /// <summary>
    /// Get appropriate indicator prefab
    /// </summary>
    GameObject GetIndicatorPrefab(IndicatorType indicatorType)
    {
        switch (indicatorType)
        {
            case IndicatorType.Active:
                return activeEventIndicatorPrefab;
            case IndicatorType.Upcoming:
                return upcomingEventIndicatorPrefab;
            case IndicatorType.Completed:
                return completedEventIndicatorPrefab;
            default:
                return activeEventIndicatorPrefab;
        }
    }
    
    /// <summary>
    /// Setup indicator content
    /// </summary>
    void SetupIndicatorContent(GameObject indicator, EventManager.ActiveEvent activeEvent, IndicatorType indicatorType)
    {
        // Setup event icon
        var iconImage = indicator.transform.Find("EventIcon")?.GetComponent<Image>();
        if (iconImage != null)
        {
            var eventUIManager = WorldEventUIManager.Instance;
            if (eventUIManager != null && eventUIManager.eventTypeIcons != null)
            {
                iconImage.sprite = eventUIManager.eventTypeIcons.GetIcon(activeEvent.eventData.eventType);
            }
        }
        
        // Setup severity indicator
        var severityRing = indicator.transform.Find("SeverityRing")?.GetComponent<Image>();
        if (severityRing != null)
        {
            severityRing.color = GetSeverityColor(activeEvent.eventData.severity);
        }
        
        // Setup progress indicator
        var progressFill = indicator.transform.Find("ProgressFill")?.GetComponent<Image>();
        if (progressFill != null && indicatorType == IndicatorType.Active)
        {
            progressFill.fillAmount = activeEvent.progress;
        }
        
        // Setup tooltip trigger
        var tooltipTrigger = indicator.GetComponent<EventTooltipTrigger>();
        if (tooltipTrigger == null)
            tooltipTrigger = indicator.AddComponent<EventTooltipTrigger>();
        
        tooltipTrigger.Initialize(activeEvent, this);
    }
    
    /// <summary>
    /// Update existing event indicator
    /// </summary>
    void UpdateEventIndicator(EventManager.ActiveEvent activeEvent)
    {
        if (!activeIndicators.ContainsKey(activeEvent.eventId)) return;
        
        var indicator = activeIndicators[activeEvent.eventId];
        if (indicator == null) return;
        
        // Update progress
        var progressFill = indicator.transform.Find("ProgressFill")?.GetComponent<Image>();
        if (progressFill != null)
        {
            progressFill.fillAmount = activeEvent.progress;
        }
        
        // Update position if event moved
        Vector2 newPosition = GetEventMapPosition(activeEvent);
        if (newPosition != Vector2.zero)
        {
            indicator.transform.localPosition = new Vector3(newPosition.x, newPosition.y, 0);
        }
    }
    
    /// <summary>
    /// Remove event indicator
    /// </summary>
    void RemoveIndicator(string eventId)
    {
        if (activeIndicators.ContainsKey(eventId))
        {
            var indicator = activeIndicators[eventId];
            if (indicator != null)
            {
                // Stop animation
                if (indicatorAnimations.ContainsKey(eventId))
                {
                    StopCoroutine(indicatorAnimations[eventId]);
                    indicatorAnimations.Remove(eventId);
                }
                
                // Play removal effect
                StartCoroutine(RemoveIndicatorWithEffect(indicator));
            }
            
            activeIndicators.Remove(eventId);
        }
    }
    
    /// <summary>
    /// Remove indicator with fade effect
    /// </summary>
    IEnumerator RemoveIndicatorWithEffect(GameObject indicator)
    {
        var canvasGroup = indicator.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = indicator.AddComponent<CanvasGroup>();
        
        float fadeTime = 0.5f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            canvasGroup.alpha = Mathf.Lerp(1f, 0f, elapsed / fadeTime);
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        Destroy(indicator);
    }
    
    /// <summary>
    /// Animate indicator
    /// </summary>
    IEnumerator AnimateIndicator(GameObject indicator, WorldEvent.EventSeverity severity)
    {
        var baseScale = indicator.transform.localScale;
        float animationSpeed = pulseSpeed * GetSeveritySpeedMultiplier(severity);
        
        while (indicator != null)
        {
            float pulse = 1f + Mathf.Sin(Time.time * animationSpeed) * pulseIntensity;
            indicator.transform.localScale = baseScale * pulse;
            
            yield return null;
        }
    }
    
    /// <summary>
    /// Get severity speed multiplier for animation
    /// </summary>
    float GetSeveritySpeedMultiplier(WorldEvent.EventSeverity severity)
    {
        switch (severity)
        {
            case WorldEvent.EventSeverity.Minor:
                return 0.5f;
            case WorldEvent.EventSeverity.Moderate:
                return 1f;
            case WorldEvent.EventSeverity.Major:
                return 1.5f;
            case WorldEvent.EventSeverity.Critical:
                return 2f;
            default:
                return 1f;
        }
    }
    
    /// <summary>
    /// Get severity color
    /// </summary>
    Color GetSeverityColor(WorldEvent.EventSeverity severity)
    {
        switch (severity)
        {
            case WorldEvent.EventSeverity.Minor:
                return Color.green;
            case WorldEvent.EventSeverity.Moderate:
                return Color.yellow;
            case WorldEvent.EventSeverity.Major:
                return new Color(1f, 0.5f, 0f); // Orange
            case WorldEvent.EventSeverity.Critical:
                return Color.red;
            default:
                return Color.white;
        }
    }
    
    /// <summary>
    /// Play indicator creation effect
    /// </summary>
    void PlayIndicatorCreationEffect(Vector2 position, WorldEvent.EventType eventType)
    {
        // Play particle effect
        if (eventParticleEffect != null)
        {
            eventParticleEffect.transform.position = new Vector3(position.x, position.y, 0);
            eventParticleEffect.Play();
        }
        
        // Play audio effect
        if (eventIndicatorAudio != null)
        {
            var eventUIManager = WorldEventUIManager.Instance;
            if (eventUIManager != null && eventUIManager.eventAudioSet != null)
            {
                var audioClip = eventUIManager.eventAudioSet.GetEventSound(eventType);
                if (audioClip != null)
                {
                    eventIndicatorAudio.PlayOneShot(audioClip, 0.3f);
                }
            }
        }
    }
    
    /// <summary>
    /// Show event tooltip
    /// </summary>
    public void ShowEventTooltip(EventManager.ActiveEvent activeEvent, Vector3 position)
    {
        if (eventTooltipPrefab == null) return;
        
        // Hide existing tooltip
        HideEventTooltip();
        
        // Create new tooltip
        currentTooltip = Instantiate(eventTooltipPrefab, worldMapCanvas.transform);
        currentTooltip.transform.position = position;
        
        // Setup tooltip content
        var titleText = currentTooltip.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
        if (titleText != null)
            titleText.text = activeEvent.eventData.eventName;
        
        var descText = currentTooltip.transform.Find("Description")?.GetComponent<TextMeshProUGUI>();
        if (descText != null)
            descText.text = activeEvent.eventData.eventDescription;
        
        var progressText = currentTooltip.transform.Find("Progress")?.GetComponent<TextMeshProUGUI>();
        if (progressText != null)
        {
            float remainingTime = activeEvent.GetRemainingTime();
            int days = Mathf.FloorToInt(remainingTime);
            progressText.text = $"{days} days remaining";
        }
        
        var severityText = currentTooltip.transform.Find("Severity")?.GetComponent<TextMeshProUGUI>();
        if (severityText != null)
        {
            severityText.text = activeEvent.eventData.severity.ToString();
            severityText.color = GetSeverityColor(activeEvent.eventData.severity);
        }
    }
    
    /// <summary>
    /// Hide event tooltip
    /// </summary>
    public void HideEventTooltip()
    {
        if (currentTooltip != null)
        {
            Destroy(currentTooltip);
            currentTooltip = null;
        }
        
        if (tooltipCoroutine != null)
        {
            StopCoroutine(tooltipCoroutine);
            tooltipCoroutine = null;
        }
    }
    
    /// <summary>
    /// Handle event started
    /// </summary>
    void OnEventStarted(EventManager.ActiveEvent activeEvent)
    {
        CreateEventIndicator(activeEvent, IndicatorType.Active);
    }
    
    /// <summary>
    /// Handle event completed
    /// </summary>
    void OnEventCompleted(EventManager.CompletedEvent completedEvent)
    {
        RemoveIndicator(completedEvent.eventId);
    }
    
    /// <summary>
    /// Create default area coordinates
    /// </summary>
    [ContextMenu("Create Default Area Coordinates")]
    void CreateDefaultAreaCoordinates()
    {
        areaCoordinates = new AreaCoordinateSet();
        areaCoordinates.areaCoordinates = new AreaCoordinate[]
        {
            new AreaCoordinate { areaName = "KingdomOfSouthernBolt", mapPosition = new Vector2(100, 200) },
            new AreaCoordinate { areaName = "ForestOfShadows", mapPosition = new Vector2(-150, 100) },
            new AreaCoordinate { areaName = "Ashlands", mapPosition = new Vector2(0, -100) },
            new AreaCoordinate { areaName = "WhisperingDunes", mapPosition = new Vector2(200, -50) },
            new AreaCoordinate { areaName = "HollowCradle", mapPosition = new Vector2(-100, -150) }
        };
        
        Debug.Log("Created default area coordinates");
    }
    
    // Public API
    public static void ShowEventTooltipStatic(EventManager.ActiveEvent activeEvent, Vector3 position)
    {
        Instance.ShowEventTooltip(activeEvent, position);
    }
    
    public static void HideEventTooltipStatic()
    {
        Instance.HideEventTooltip();
    }
}

/// <summary>
/// Event tooltip trigger component
/// </summary>
public class EventTooltipTrigger : MonoBehaviour, UnityEngine.EventSystems.IPointerEnterHandler, UnityEngine.EventSystems.IPointerExitHandler
{
    private EventManager.ActiveEvent activeEvent;
    private WorldMapEventIndicators indicatorSystem;
    
    public void Initialize(EventManager.ActiveEvent activeEvent, WorldMapEventIndicators indicatorSystem)
    {
        this.activeEvent = activeEvent;
        this.indicatorSystem = indicatorSystem;
    }
    
    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData eventData)
    {
        if (indicatorSystem != null && activeEvent != null)
        {
            indicatorSystem.ShowEventTooltip(activeEvent, transform.position);
        }
    }
    
    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData eventData)
    {
        if (indicatorSystem != null)
        {
            indicatorSystem.HideEventTooltip();
        }
    }
}
