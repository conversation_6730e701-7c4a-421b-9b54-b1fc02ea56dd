using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Master controller for Cinder of Darkness - integrates all expanded systems
/// Manages the complete experience of The Cinderborn's journey
/// </summary>
public class CinderOfDarknessGameController : MonoBehaviour
{
    [Header("Core Systems")]
    public PhilosophicalMoralitySystem moralitySystem;
    public ElementalMagicSystem magicSystem;
    public DeathConsequenceSystem deathSystem;
    public WorldInteractionSystem worldSystem;
    public EconomySystem economySystem;
    public UniqueSideQuests questSystem;
    public MinimalistHUD hudSystem;

    [Header("Integration Settings")]
    public bool enableAllSystems = true;
    public bool debugMode = false;

    [Header("Game State")]
    public GamePhase currentPhase = GamePhase.EarlyGame;
    public float totalPlayTime = 0f;
    public int majorChoicesMade = 0;
    public bool hasReachedEndgame = false;

    [Header("The Cinderborn's Journey")]
    public CinderbornsJourneyData journeyData = new CinderbornsJourneyData();

    public enum GamePhase
    {
        EarlyGame,      // Learning systems, basic choices
        MidGame,        // Complex moral decisions, system mastery
        LateGame,       // Consequences manifest, path solidifies
        Endgame,        // Final choices, ultimate consequences
        NewGamePlus     // Replay with knowledge and power
    }

    [System.Serializable]
    public class CinderbornsJourneyData
    {
        [Header("Moral Journey")]
        public int evilChoicesMade = 0;
        public int goodChoicesMade = 0;
        public int neutralChoicesMade = 0;
        public float currentMoralWeight = 0f;

        [Header("Combat & Death")]
        public int totalKills = 0;
        public int innocentKills = 0;
        public int villainKills = 0;
        public int bossesDefeated = 0;
        public float totalDamageDealt = 0f;
        public float totalDamageTaken = 0f;

        [Header("Magic & Elements")]
        public bool hasDiscoveredWater = false;
        public bool hasDiscoveredWind = false;
        public bool hasDiscoveredEarth = false;
        public int combinationSpellsCast = 0;
        public float totalManaUsed = 0f;

        [Header("World Interaction")]
        public int childrenPlayedWith = 0;
        public int mealsCooked = 0;
        public int trainingSessionsCompleted = 0;
        public int buildingsDestroyed = 0;
        public float muscleGrowthAchieved = 0f;

        [Header("Economy & Items")]
        public float totalGoldEarned = 0f;
        public float totalGoldSpent = 0f;
        public int rareItemsFound = 0;
        public int legendaryItemsUnlocked = 0;
        public int itemsCrafted = 0;

        [Header("Quests & Exploration")]
        public int sideQuestsCompleted = 0;
        public int uniqueQuestsCompleted = 0;
        public int regionsExplored = 0;
        public int hiddenRealmsDiscovered = 0;
        public float totalDistanceTraveled = 0f;

        [Header("Psychological Journey")]
        public float highestTrauma = 0f;
        public float highestEnlightenment = 0f;
        public int hallucinationsExperienced = 0;
        public int meditationSessionsCompleted = 0;
        public bool hasReachedHollowState = false;
        public bool hasReachedEnlightenedState = false;
    }

    void Start()
    {
        InitializeGameController();
        ConnectAllSystems();
        StartGameJourney();
    }

    void Update()
    {
        if (!enableAllSystems) return;

        UpdateGamePhase();
        UpdateJourneyData();
        MonitorSystemIntegration();

        #if UNITY_EDITOR
        HandleDebugInput();
        #endif
    }

    void InitializeGameController()
    {
        // Find all systems if not assigned
        if (moralitySystem == null)
            moralitySystem = FindObjectOfType<PhilosophicalMoralitySystem>();

        if (magicSystem == null)
            magicSystem = FindObjectOfType<ElementalMagicSystem>();

        if (deathSystem == null)
            deathSystem = FindObjectOfType<DeathConsequenceSystem>();

        if (worldSystem == null)
            worldSystem = FindObjectOfType<WorldInteractionSystem>();

        if (economySystem == null)
            economySystem = FindObjectOfType<EconomySystem>();

        if (questSystem == null)
            questSystem = FindObjectOfType<UniqueSideQuests>();

        if (hudSystem == null)
            hudSystem = FindObjectOfType<MinimalistHUD>();

        Debug.Log("Cinder of Darkness Game Controller initialized - all systems connected");
    }

    void ConnectAllSystems()
    {
        // Connect morality system to other systems
        if (moralitySystem != null)
        {
            // Morality affects magic discovery
            ConnectMoralityToMagic();

            // Morality affects economy pricing
            ConnectMoralityToEconomy();

            // Morality affects quest availability
            ConnectMoralityToQuests();
        }

        // Connect death system to psychological effects
        if (deathSystem != null)
        {
            ConnectDeathToPsychology();
        }

        // Connect world interactions to character growth
        if (worldSystem != null)
        {
            ConnectWorldToCharacter();
        }

        Debug.Log("All systems connected and integrated");
    }

    void ConnectMoralityToMagic()
    {
        // Evil path might discover earth magic easier (destruction)
        // Good path might discover water magic easier (healing)
        // Neutral path discovers wind magic easier (freedom)
    }

    void ConnectMoralityToEconomy()
    {
        // Already handled in economy system through price modifiers
    }

    void ConnectMoralityToQuests()
    {
        // Quest availability based on moral path
    }

    void ConnectDeathToPsychology()
    {
        // Death consequences affect psychological state
    }

    void ConnectWorldToCharacter()
    {
        // World interactions affect character development
    }

    void StartGameJourney()
    {
        currentPhase = GamePhase.EarlyGame;
        totalPlayTime = 0f;

        ShowGameMessage("The Cinderborn's journey begins...");
        ShowGameMessage("Every choice will shape who you become.");

        // Initialize starting conditions
        if (magicSystem != null)
        {
            ShowGameMessage("Fire flows through your veins - it is your birthright.");
        }

        if (moralitySystem != null)
        {
            ShowGameMessage("You are neither hero nor villain. You are the ash between fire and void.");
        }
    }

    void UpdateGamePhase()
    {
        totalPlayTime += Time.deltaTime;

        GamePhase newPhase = CalculateCurrentPhase();
        if (newPhase != currentPhase)
        {
            TransitionToPhase(newPhase);
        }
    }

    GamePhase CalculateCurrentPhase()
    {
        // Base phase on story progress and player actions
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null) return currentPhase;

        float storyProgress = gameManager.currentStoryProgress;

        if (storyProgress < 0.25f)
            return GamePhase.EarlyGame;
        else if (storyProgress < 0.6f)
            return GamePhase.MidGame;
        else if (storyProgress < 0.9f)
            return GamePhase.LateGame;
        else if (storyProgress < 1f)
            return GamePhase.Endgame;
        else
            return GamePhase.NewGamePlus;
    }

    void TransitionToPhase(GamePhase newPhase)
    {
        GamePhase oldPhase = currentPhase;
        currentPhase = newPhase;

        Debug.Log($"Game phase transition: {oldPhase} -> {newPhase}");

        switch (newPhase)
        {
            case GamePhase.MidGame:
                OnEnterMidGame();
                break;
            case GamePhase.LateGame:
                OnEnterLateGame();
                break;
            case GamePhase.Endgame:
                OnEnterEndgame();
                break;
            case GamePhase.NewGamePlus:
                OnEnterNewGamePlus();
                break;
        }
    }

    void OnEnterMidGame()
    {
        ShowGameMessage("The Cinderborn's choices begin to have weight...");

        // Unlock more complex systems
        if (magicSystem != null)
        {
            // Chance to discover new elements
        }

        if (questSystem != null)
        {
            // Unlock more complex side quests
        }
    }

    void OnEnterLateGame()
    {
        ShowGameMessage("The consequences of your path become clear...");

        // World reacts more strongly to player's choices
        if (moralitySystem != null)
        {
            float fearLevel = moralitySystem.GetFearLevel();
            float admirationLevel = moralitySystem.GetAdmirationLevel();

            if (fearLevel > 75f)
            {
                ShowGameMessage("The world trembles at your approach...");
            }
            else if (admirationLevel > 75f)
            {
                ShowGameMessage("Hope returns to the world through your actions...");
            }
        }
    }

    void OnEnterEndgame()
    {
        hasReachedEndgame = true;
        ShowGameMessage("The final chapter of The Cinderborn's journey approaches...");

        // Unlock endgame content
        if (economySystem != null)
        {
            // Unlock mythical items
        }

        if (questSystem != null)
        {
            // Unlock final redemption quest
        }
    }

    void OnEnterNewGamePlus()
    {
        ShowGameMessage("The cycle begins anew, but The Cinderborn remembers...");

        // Prepare for New Game+ features
        PrepareNewGamePlus();
    }

    void PrepareNewGamePlus()
    {
        // Save current journey data
        SaveJourneyData();

        // Prepare bonuses for next playthrough
        PlayerPrefs.SetInt("NewGamePlusUnlocked", 1);
        PlayerPrefs.SetFloat("PreviousJourneyScore", CalculateJourneyScore());

        ShowGameMessage("New Game+ unlocked! Start again with knowledge and power.");
    }

    void UpdateJourneyData()
    {
        // Update all journey statistics
        if (deathSystem != null)
        {
            journeyData.totalKills = deathSystem.GetTotalKills();
            journeyData.innocentKills = deathSystem.GetInnocentsKilled();
            journeyData.villainKills = deathSystem.GetVillainsKilled();
        }

        if (economySystem != null)
        {
            journeyData.totalGoldEarned = economySystem.GetTotalWealth();
            journeyData.rareItemsFound = CountRareItems();
        }

        if (questSystem != null)
        {
            journeyData.sideQuestsCompleted = questSystem.GetCompletedQuestCount();
        }

        if (worldSystem != null)
        {
            journeyData.muscleGrowthAchieved = worldSystem.GetMuscleGrowth();
        }

        // Update moral choice tracking
        if (moralitySystem != null)
        {
            UpdateMoralChoiceData();
        }
    }

    void UpdateMoralChoiceData()
    {
        switch (moralitySystem.GetCurrentPath())
        {
            case PhilosophicalMoralitySystem.MoralPath.Evil:
                journeyData.currentMoralWeight = -moralitySystem.evilProgression;
                break;
            case PhilosophicalMoralitySystem.MoralPath.Good:
                journeyData.currentMoralWeight = moralitySystem.goodProgression;
                break;
            default:
                journeyData.currentMoralWeight = 0f;
                break;
        }
    }

    int CountRareItems()
    {
        if (economySystem == null) return 0;

        int count = 0;
        foreach (var item in economySystem.GetInventory())
        {
            if (item.rarity >= EconomySystem.InventoryItem.ItemRarity.Rare)
            {
                count++;
            }
        }
        return count;
    }

    void MonitorSystemIntegration()
    {
        // Check for system conflicts or issues
        if (debugMode)
        {
            CheckSystemHealth();
        }
    }

    void CheckSystemHealth()
    {
        // Verify all systems are functioning correctly
        bool allSystemsHealthy = true;

        if (moralitySystem == null)
        {
            Debug.LogWarning("Morality system not found!");
            allSystemsHealthy = false;
        }

        if (magicSystem == null)
        {
            Debug.LogWarning("Magic system not found!");
            allSystemsHealthy = false;
        }

        // Continue for other systems...

        if (allSystemsHealthy && debugMode)
        {
            Debug.Log("All systems healthy and integrated");
        }
    }

    void HandleDebugInput()
    {
        if (!debugMode) return;

        // Debug shortcuts for testing
        if (Input.GetKeyDown(KeyCode.F1))
        {
            ShowJourneyStatistics();
        }

        if (Input.GetKeyDown(KeyCode.F2))
        {
            AdvanceGamePhase();
        }

        if (Input.GetKeyDown(KeyCode.F3))
        {
            TestAllSystems();
        }
    }

    void ShowJourneyStatistics()
    {
        Debug.Log("=== THE CINDERBORN'S JOURNEY STATISTICS ===");
        Debug.Log($"Total Play Time: {totalPlayTime:F1} seconds");
        Debug.Log($"Current Phase: {currentPhase}");
        Debug.Log($"Total Kills: {journeyData.totalKills}");
        Debug.Log($"Innocent Kills: {journeyData.innocentKills}");
        Debug.Log($"Villain Kills: {journeyData.villainKills}");
        Debug.Log($"Moral Weight: {journeyData.currentMoralWeight:F2}");
        Debug.Log($"Rare Items: {journeyData.rareItemsFound}");
        Debug.Log($"Quests Completed: {journeyData.sideQuestsCompleted}");
        Debug.Log($"Muscle Growth: {journeyData.muscleGrowthAchieved:F2}");
    }

    void AdvanceGamePhase()
    {
        GamePhase nextPhase = (GamePhase)(((int)currentPhase + 1) % System.Enum.GetValues(typeof(GamePhase)).Length);
        TransitionToPhase(nextPhase);
    }

    void TestAllSystems()
    {
        Debug.Log("Testing all systems...");

        if (moralitySystem != null)
        {
            moralitySystem.OnMoralChoice(true, 0.5f);
            Debug.Log("Morality system test: Evil choice made");
        }

        if (magicSystem != null)
        {
            magicSystem.DiscoverElement(ElementalMagicSystem.ElementType.Water);
            Debug.Log("Magic system test: Water element discovered");
        }

        // Test other systems...
    }

    float CalculateJourneyScore()
    {
        float score = 0f;

        // Positive scoring
        score += journeyData.villainKills * 10f;
        score += journeyData.sideQuestsCompleted * 50f;
        score += journeyData.childrenPlayedWith * 25f;
        score += journeyData.rareItemsFound * 100f;

        // Negative scoring
        score -= journeyData.innocentKills * 100f;
        score -= journeyData.buildingsDestroyed * 10f;

        // Moral path bonuses
        if (moralitySystem != null)
        {
            switch (moralitySystem.GetCurrentPath())
            {
                case PhilosophicalMoralitySystem.MoralPath.Good:
                    score += 1000f;
                    break;
                case PhilosophicalMoralitySystem.MoralPath.Evil:
                    score += 500f; // Evil is harder but still valid
                    break;
            }
        }

        return Mathf.Max(score, 0f);
    }

    void SaveJourneyData()
    {
        // Save journey data to PlayerPrefs for New Game+
        PlayerPrefs.SetFloat("JourneyScore", CalculateJourneyScore());
        PlayerPrefs.SetInt("TotalKills", journeyData.totalKills);
        PlayerPrefs.SetInt("InnocentKills", journeyData.innocentKills);
        PlayerPrefs.SetInt("QuestsCompleted", journeyData.sideQuestsCompleted);
        PlayerPrefs.SetFloat("MoralWeight", journeyData.currentMoralWeight);
        PlayerPrefs.Save();
    }

    void ShowGameMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt(message);
            StartCoroutine(HideMessageAfterDelay(gameUI, 5f));
        }

        Debug.Log($"Game Controller: {message}");
    }

    IEnumerator HideMessageAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    // Public methods for external systems
    public void OnMajorChoiceMade(bool isEvil, float weight)
    {
        majorChoicesMade++;

        if (isEvil)
        {
            journeyData.evilChoicesMade++;
        }
        else
        {
            journeyData.goodChoicesMade++;
        }

        Debug.Log($"Major choice made: {(isEvil ? "Evil" : "Good")} (Weight: {weight})");
    }

    public void OnElementDiscovered(ElementalMagicSystem.ElementType element)
    {
        switch (element)
        {
            case ElementalMagicSystem.ElementType.Water:
                journeyData.hasDiscoveredWater = true;
                break;
            case ElementalMagicSystem.ElementType.Wind:
                journeyData.hasDiscoveredWind = true;
                break;
            case ElementalMagicSystem.ElementType.Earth:
                journeyData.hasDiscoveredEarth = true;
                break;
        }

        ShowGameMessage($"The Cinderborn has discovered the power of {element}!");
    }

    public void OnRareItemFound(string itemName)
    {
        journeyData.rareItemsFound++;
        ShowGameMessage($"Legendary discovery: {itemName} has been found!");
    }

    // Getters
    public GamePhase GetCurrentPhase() => currentPhase;
    public CinderbornsJourneyData GetJourneyData() => journeyData;
    public float GetTotalPlayTime() => totalPlayTime;
    public bool HasReachedEndgame() => hasReachedEndgame;
    public float GetJourneyScore() => CalculateJourneyScore();
}
