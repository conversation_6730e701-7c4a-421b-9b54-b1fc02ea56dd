using UnityEngine;
using UnityEngine.Audio;
using System.Collections;
using System.Collections.Generic;

public class PsychologicalSystem : MonoBehaviour
{
    [Header("Mental State")]
    public float mentalStability = 50f; // 0-100
    public float trauma = 0f; // 0-100
    public float enlightenment = 0f; // 0-100
    public PsychologicalState currentState = PsychologicalState.Balanced;

    [Header("Visual Effects")]
    public GameObject[] hallucinationPrefabs;
    public ParticleSystem shadowParticles;
    public ParticleSystem lightParticles;
    public Material[] eyeMaterials; // Different eye colors based on state
    public Material[] hairMaterials; // Gray/white hair from trauma

    [Header("Audio")]
    public AudioMixerGroup darkAudioMixer;
    public AudioMixerGroup lightAudioMixer;
    public AudioClip[] whisperSounds;
    public AudioClip[] hopefulVoices;
    public AudioClip[] regretfulMemories;

    [Head<PERSON>("Meditation")]
    public GameObject meditationUI;
    public Transform[] campfireLocations;
    public float meditationRange = 3f;

    [<PERSON><PERSON>("The Cinderborn's Philosophy")]
    public string[] stoicQuotes = {
        "I am not a hero or villain. I am the ash, and to ash I shall return.",
        "Pain is the teacher that never lies.",
        "In darkness, we find what light truly means.",
        "The blade remembers every life it takes.",
        "Strength without purpose is mere destruction.",
        "I respect all paths to truth, but trust none that demand blind faith.",
        "The supreme force flows through all things, but speaks through none.",
        "Fire purifies, nature endures, shadow conceals, light reveals.",
        "Those who worship only power become slaves to it.",
        "In the end, we all return to the same ash."
    };

    [Header("Dark Path Effects")]
    public GameObject[] hallucinationVariants;
    public AudioClip[] darkWhispers;
    public AudioClip[] regretfulMemories;
    public Color darkDialogueColor = new Color(0.8f, 0.2f, 0.2f);
    public float hallucinationFrequency = 0.005f;

    [Header("Redemption Path Effects")]
    public AudioClip[] hopefulThoughts;
    public AudioClip[] calmingAmbience;
    public Color lightDialogueColor = new Color(0.9f, 0.9f, 0.7f);
    public Light redemptionLight;
    public ParticleSystem peaceAura;

    private PlayerStats playerStats;
    private CharacterProgression characterProgression;
    private AudioSource psycheAudioSource;
    private Camera playerCamera;
    private List<GameObject> activeHallucinations = new List<GameObject>();
    private bool isNearCampfire = false;
    private float lastQuoteTime = 0f;

    public enum PsychologicalState
    {
        Enlightened,    // High light alignment, low trauma
        Balanced,       // Moderate alignment, moderate trauma
        Tormented,      // High trauma, conflicted
        Corrupted,      // High dark alignment, high trauma
        Hollow          // Extreme trauma, lost humanity
    }

    void Start()
    {
        playerStats = GetComponent<PlayerStats>();
        characterProgression = GetComponent<CharacterProgression>();
        psycheAudioSource = gameObject.AddComponent<AudioSource>();
        playerCamera = Camera.main;

        // Subscribe to events
        if (playerStats != null)
        {
            playerStats.OnPathChanged += OnMoralPathChanged;
        }

        // Initialize meditation UI
        if (meditationUI != null)
            meditationUI.SetActive(false);

        StartCoroutine(PsychologicalUpdateLoop());
    }

    void Update()
    {
        UpdatePsychologicalState();
        HandleMeditationInput();
        CheckCampfireProximity();
        UpdateVisualEffects();

        // Occasional philosophical quotes
        if (Time.time - lastQuoteTime > Random.Range(120f, 300f))
        {
            if (Random.Range(0f, 1f) < 0.3f)
            {
                SpeakPhilosophicalQuote();
            }
        }
    }

    void UpdatePsychologicalState()
    {
        // Calculate psychological state based on alignment and trauma
        float sunAlignment = playerStats.GetSunAlignment();
        float moonAlignment = playerStats.GetMoonAlignment();

        if (trauma > 80f)
        {
            currentState = PsychologicalState.Hollow;
        }
        else if (trauma > 60f && Mathf.Abs(moonAlignment) > 50f)
        {
            currentState = PsychologicalState.Corrupted;
        }
        else if (trauma > 40f)
        {
            currentState = PsychologicalState.Tormented;
        }
        else if (sunAlignment > 60f && trauma < 30f)
        {
            currentState = PsychologicalState.Enlightened;
        }
        else
        {
            currentState = PsychologicalState.Balanced;
        }

        // Update mental stability
        mentalStability = Mathf.Clamp(100f - trauma, 0f, 100f);

        // Apply gameplay effects
        ApplyPsychologicalEffects();
    }

    void ApplyPsychologicalEffects()
    {
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
            case PsychologicalState.Hollow:
                // Dark path effects
                ApplyStaminaDrain();
                TriggerDarkHallucinations();
                ApplyVisionBlur();
                PlayDarkWhispers();
                ApplyAggressivePosture();
                ModifyDialogueColor(darkDialogueColor);
                break;

            case PsychologicalState.Enlightened:
                // Redemption path effects
                ApplyPrecisionBonus();
                ApplyCalmnessEffects();
                PlayHopefulThoughts();
                ApplyPeacefulPosture();
                ModifyDialogueColor(lightDialogueColor);
                ActivateRedemptionEffects();
                break;

            case PsychologicalState.Tormented:
                // Mixed effects - internal conflict
                ApplyUnstableEffects();
                TriggerConflictedThoughts();
                break;

            case PsychologicalState.Balanced:
                // Neutral state with slight bonuses
                ApplyBalancedEffects();
                break;
        }

        UpdateCharacterAppearance();
        UpdateEnvironmentalEffects();
    }

    void ApplyStaminaDrain()
    {
        if (playerStats != null)
        {
            float drainRate = (trauma / 100f) * 5f; // Up to 5 stamina per second
            playerStats.ConsumeStamina(drainRate * Time.deltaTime);
        }
    }

    void ApplyVisionBlur()
    {
        // Apply post-processing blur effect
        if (Random.Range(0f, 1f) < 0.01f) // 1% chance per frame
        {
            StartCoroutine(BlurVision());
        }
    }

    IEnumerator BlurVision()
    {
        // Create blur overlay
        GameObject blurOverlay = new GameObject("VisionBlur");
        Canvas canvas = blurOverlay.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 100;

        UnityEngine.UI.Image blurImage = blurOverlay.AddComponent<UnityEngine.UI.Image>();
        blurImage.color = new Color(0f, 0f, 0f, 0.3f);

        float duration = Random.Range(0.5f, 2f);
        yield return new WaitForSeconds(duration);

        Destroy(blurOverlay);
    }

    void TriggerDarkHallucinations()
    {
        if (Random.Range(0f, 1f) < hallucinationFrequency && hallucinationVariants.Length > 0)
        {
            CreateDarkHallucination();
        }
    }

    void CreateDarkHallucination()
    {
        Vector3 hallucinationPos = transform.position + Random.insideUnitSphere * 15f;
        hallucinationPos.y = transform.position.y;

        GameObject hallucination = Instantiate(
            hallucinationVariants[Random.Range(0, hallucinationVariants.Length)],
            hallucinationPos,
            Quaternion.identity
        );

        // Make hallucination more menacing for dark path
        Renderer hallucinationRenderer = hallucination.GetComponent<Renderer>();
        if (hallucinationRenderer != null)
        {
            Color color = hallucinationRenderer.material.color;
            color = Color.Lerp(color, Color.red, 0.5f);
            color.a = 0.7f;
            hallucinationRenderer.material.color = color;
        }

        // Add shadowy movement
        StartCoroutine(AnimateHallucination(hallucination));

        activeHallucinations.Add(hallucination);

        // Remove hallucination after time
        StartCoroutine(RemoveHallucination(hallucination, Random.Range(5f, 12f)));
    }

    IEnumerator AnimateHallucination(GameObject hallucination)
    {
        Vector3 startPos = hallucination.transform.position;
        float duration = Random.Range(3f, 8f);
        float elapsed = 0f;

        while (elapsed < duration && hallucination != null)
        {
            elapsed += Time.deltaTime;

            // Make it move menacingly toward player
            Vector3 targetPos = Vector3.Lerp(startPos, transform.position, elapsed / duration * 0.3f);
            targetPos += Vector3.up * Mathf.Sin(elapsed * 2f) * 0.5f;

            hallucination.transform.position = targetPos;
            hallucination.transform.LookAt(transform.position);

            yield return null;
        }
    }

    void PlayDarkWhispers()
    {
        if (Random.Range(0f, 1f) < 0.01f && darkWhispers.Length > 0) // 1% chance per frame
        {
            AudioClip whisper = darkWhispers[Random.Range(0, darkWhispers.Length)];
            psycheAudioSource.PlayOneShot(whisper, 0.3f);

            // Show whisper text briefly
            ShowWhisperText(GetDarkWhisperText());
        }
    }

    string GetDarkWhisperText()
    {
        string[] whisperTexts = {
            "You enjoy the killing...",
            "They deserved to die...",
            "Power feels good, doesn't it?",
            "Why show mercy to the weak?",
            "You are becoming what you were meant to be...",
            "The darkness welcomes you...",
            "Let the rage consume you...",
            "They fear you... good..."
        };

        return whisperTexts[Random.Range(0, whisperTexts.Length)];
    }

    void PlayHopefulThoughts()
    {
        if (Random.Range(0f, 1f) < 0.008f && hopefulThoughts.Length > 0) // Slightly less frequent than whispers
        {
            AudioClip thought = hopefulThoughts[Random.Range(0, hopefulThoughts.Length)];
            psycheAudioSource.PlayOneShot(thought, 0.4f);

            // Show hopeful text
            ShowHopefulText(GetHopefulThoughtText());
        }
    }

    string GetHopefulThoughtText()
    {
        string[] hopefulTexts = {
            "There is still good in this world...",
            "I can choose to be better...",
            "Every life has value...",
            "Mercy is strength, not weakness...",
            "I will not become the monster...",
            "Peace is possible...",
            "I can break this cycle...",
            "There is light within the darkness..."
        };

        return hopefulTexts[Random.Range(0, hopefulTexts.Length)];
    }

    void ApplyAggressivePosture()
    {
        Animator animator = GetComponent<Animator>();
        if (animator != null)
        {
            animator.SetFloat("Aggression", 1f);
            animator.SetFloat("Tension", trauma / 100f);
            animator.SetBool("IsAggressive", true);
        }
    }

    void ApplyPeacefulPosture()
    {
        Animator animator = GetComponent<Animator>();
        if (animator != null)
        {
            animator.SetFloat("Aggression", 0f);
            animator.SetFloat("Peace", enlightenment / 100f);
            animator.SetBool("IsAggressive", false);
            animator.SetBool("IsPeaceful", true);
        }
    }

    void ModifyDialogueColor(Color color)
    {
        // This would be used by the dialogue system to change text color
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            // gameUI.SetDialogueColor(color);
        }
    }

    void ActivateRedemptionEffects()
    {
        // Soft lighting
        if (redemptionLight != null)
        {
            redemptionLight.enabled = true;
            redemptionLight.intensity = Mathf.Lerp(0f, 2f, enlightenment / 100f);
            redemptionLight.color = Color.Lerp(Color.white, new Color(1f, 0.9f, 0.7f), enlightenment / 100f);
        }

        // Peace aura
        if (peaceAura != null && !peaceAura.isPlaying)
        {
            peaceAura.Play();
            var main = peaceAura.main;
            main.startColor = new Color(0.9f, 0.9f, 0.7f, 0.3f);
        }

        // Calming ambient music
        if (calmingAmbience.Length > 0 && Random.Range(0f, 1f) < 0.005f)
        {
            AudioClip ambient = calmingAmbience[Random.Range(0, calmingAmbience.Length)];
            psycheAudioSource.PlayOneShot(ambient, 0.2f);
        }
    }

    void TriggerConflictedThoughts()
    {
        if (Random.Range(0f, 1f) < 0.01f)
        {
            string[] conflictedTexts = {
                "What am I becoming?",
                "Is this who I want to be?",
                "The line between justice and vengeance blurs...",
                "I fight monsters... but am I becoming one?",
                "Every choice has a price...",
                "The path forward is unclear..."
            };

            string text = conflictedTexts[Random.Range(0, conflictedTexts.Length)];
            ShowConflictedText(text);
        }
    }

    void ApplyBalancedEffects()
    {
        // Slight bonuses for balanced state
        if (playerStats != null)
        {
            // Small stamina regeneration bonus
            playerStats.RegenerateStamina(1f * Time.deltaTime);
        }
    }

    void UpdateEnvironmentalEffects()
    {
        // Modify environment based on psychological state
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
            case PsychologicalState.Hollow:
                // Darken environment
                RenderSettings.ambientLight = Color.Lerp(RenderSettings.ambientLight, new Color(0.3f, 0.2f, 0.2f), Time.deltaTime * 0.1f);
                break;

            case PsychologicalState.Enlightened:
                // Brighten environment
                RenderSettings.ambientLight = Color.Lerp(RenderSettings.ambientLight, new Color(0.8f, 0.8f, 0.7f), Time.deltaTime * 0.1f);
                break;
        }
    }

    void CreateHallucination()
    {
        Vector3 hallucinationPos = transform.position + Random.insideUnitSphere * 10f;
        hallucinationPos.y = transform.position.y;

        GameObject hallucination = Instantiate(
            hallucinationPrefabs[Random.Range(0, hallucinationPrefabs.Length)],
            hallucinationPos,
            Quaternion.identity
        );

        // Make hallucination semi-transparent
        Renderer hallucinationRenderer = hallucination.GetComponent<Renderer>();
        if (hallucinationRenderer != null)
        {
            Color color = hallucinationRenderer.material.color;
            color.a = 0.5f;
            hallucinationRenderer.material.color = color;
        }

        activeHallucinations.Add(hallucination);

        // Play whisper sound
        if (whisperSounds.Length > 0)
        {
            psycheAudioSource.PlayOneShot(whisperSounds[Random.Range(0, whisperSounds.Length)]);
        }

        // Remove hallucination after time
        StartCoroutine(RemoveHallucination(hallucination, Random.Range(3f, 8f)));
    }

    IEnumerator RemoveHallucination(GameObject hallucination, float delay)
    {
        yield return new WaitForSeconds(delay);

        if (hallucination != null)
        {
            activeHallucinations.Remove(hallucination);
            Destroy(hallucination);
        }
    }

    void ApplyPrecisionBonus()
    {
        // Increase combat precision and unlock special dialogue
        PlayerCombat combat = GetComponent<PlayerCombat>();
        if (combat != null)
        {
            // Apply precision bonus (would be implemented in combat system)
        }
    }

    void ApplyCalmnessEffects()
    {
        // Play calming ambient sounds
        if (lightParticles != null && !lightParticles.isPlaying)
        {
            lightParticles.Play();
        }

        // Adjust audio mixer for peaceful sounds
        if (lightAudioMixer != null)
        {
            psycheAudioSource.outputAudioMixerGroup = lightAudioMixer;
        }
    }

    void ApplyUnstableEffects()
    {
        // Random between positive and negative effects
        if (Random.Range(0f, 1f) < 0.5f)
        {
            ApplyPrecisionBonus();
        }
        else
        {
            ApplyStaminaDrain();
        }
    }

    void UpdateCharacterAppearance()
    {
        if (characterProgression == null) return;

        // Update hair color based on trauma
        if (hairMaterials.Length > 0)
        {
            int hairIndex = Mathf.FloorToInt((trauma / 100f) * (hairMaterials.Length - 1));
            hairIndex = Mathf.Clamp(hairIndex, 0, hairMaterials.Length - 1);

            // Apply hair material (would need renderer reference)
        }

        // Update eye color based on psychological state
        if (eyeMaterials.Length > 0)
        {
            int eyeIndex = (int)currentState;
            if (eyeIndex < eyeMaterials.Length)
            {
                // Apply eye material (would need renderer reference)
            }
        }

        // Update posture and facial expressions
        UpdatePostureAndExpression();
    }

    void UpdatePostureAndExpression()
    {
        Animator animator = GetComponent<Animator>();
        if (animator != null)
        {
            // Set animation parameters based on psychological state
            animator.SetFloat("MentalStability", mentalStability / 100f);
            animator.SetFloat("Trauma", trauma / 100f);
            animator.SetInt("PsychState", (int)currentState);
        }
    }

    void UpdateVisualEffects()
    {
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
            case PsychologicalState.Hollow:
                if (shadowParticles != null && !shadowParticles.isPlaying)
                {
                    shadowParticles.Play();
                }
                if (lightParticles != null && lightParticles.isPlaying)
                {
                    lightParticles.Stop();
                }
                break;

            case PsychologicalState.Enlightened:
                if (lightParticles != null && !lightParticles.isPlaying)
                {
                    lightParticles.Play();
                }
                if (shadowParticles != null && shadowParticles.isPlaying)
                {
                    shadowParticles.Stop();
                }
                break;

            default:
                if (shadowParticles != null && shadowParticles.isPlaying)
                {
                    shadowParticles.Stop();
                }
                if (lightParticles != null && lightParticles.isPlaying)
                {
                    lightParticles.Stop();
                }
                break;
        }
    }

    void CheckCampfireProximity()
    {
        bool nearCampfire = false;

        foreach (Transform campfire in campfireLocations)
        {
            if (campfire != null)
            {
                float distance = Vector3.Distance(transform.position, campfire.position);
                if (distance <= meditationRange)
                {
                    nearCampfire = true;
                    break;
                }
            }
        }

        if (nearCampfire != isNearCampfire)
        {
            isNearCampfire = nearCampfire;

            if (isNearCampfire)
            {
                ShowMeditationPrompt();
            }
            else
            {
                HideMeditationPrompt();
            }
        }
    }

    void HandleMeditationInput()
    {
        if (isNearCampfire && Input.GetKeyDown(KeyCode.M))
        {
            StartMeditation();
        }
    }

    void ShowMeditationPrompt()
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt("Press M to Meditate and Reflect");
        }
    }

    void HideMeditationPrompt()
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.HideInteractionPrompt();
        }
    }

    void StartMeditation()
    {
        if (meditationUI != null)
        {
            meditationUI.SetActive(true);
            Time.timeScale = 0f;
            Cursor.lockState = CursorLockMode.None;

            // Trigger meditation sequence
            StartCoroutine(MeditationSequence());
        }
    }

    IEnumerator MeditationSequence()
    {
        // Create immersive meditation environment
        CreateMeditationEnvironment();

        // Phase 1: Settling the mind
        yield return StartCoroutine(MeditationPhase1_Settling());

        // Phase 2: Reflection on choices
        yield return StartCoroutine(MeditationPhase2_Reflection());

        // Phase 3: Voices from the past
        yield return StartCoroutine(MeditationPhase3_Voices());

        // Phase 4: Emotional flashbacks
        yield return StartCoroutine(MeditationPhase4_Flashbacks());

        // Phase 5: Resolution and insight
        yield return StartCoroutine(MeditationPhase5_Resolution());

        // Apply meditation benefits
        ApplyMeditationEffects();

        // End meditation
        EndMeditation();
    }

    void CreateMeditationEnvironment()
    {
        // Dim the world
        RenderSettings.ambientLight = Color.Lerp(RenderSettings.ambientLight, new Color(0.2f, 0.2f, 0.3f), 1f);

        // Add meditation particles
        if (lightParticles != null)
        {
            lightParticles.Play();
            var main = lightParticles.main;
            main.startColor = new Color(0.8f, 0.8f, 1f, 0.3f);
        }

        // Play ambient meditation sound
        if (calmingAmbience.Length > 0)
        {
            psycheAudioSource.clip = calmingAmbience[Random.Range(0, calmingAmbience.Length)];
            psycheAudioSource.loop = true;
            psycheAudioSource.volume = 0.3f;
            psycheAudioSource.Play();
        }
    }

    IEnumerator MeditationPhase1_Settling()
    {
        ShowMeditationText("The Cinderborn sits by the fire, letting the flames quiet the mind...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("Breathe in... breathe out... let the chaos of the world fade away...");
        yield return new WaitForSecondsRealtime(3f);
    }

    IEnumerator MeditationPhase2_Reflection()
    {
        ShowMeditationText("In the stillness, memories of recent choices surface...");
        yield return new WaitForSecondsRealtime(2f);

        // Show recent moral choices
        string recentChoice = GetRecentMoralChoice();
        if (!string.IsNullOrEmpty(recentChoice))
        {
            ShowMeditationText($"The memory of {recentChoice} weighs on the mind...");
            yield return new WaitForSecondsRealtime(4f);

            // Player's internal response based on psychological state
            string internalResponse = GetInternalResponse(recentChoice);
            ShowMeditationText(internalResponse);
            yield return new WaitForSecondsRealtime(3f);
        }
    }

    IEnumerator MeditationPhase3_Voices()
    {
        ShowMeditationText("Voices from the past echo in the silence...");
        yield return new WaitForSecondsRealtime(2f);

        // Play voices based on psychological state
        string[] voices = GetVoicesFromPast();
        foreach (string voice in voices)
        {
            ShowMeditationText($"\"{voice}\"");

            // Play corresponding audio if available
            if (currentState == PsychologicalState.Corrupted && regretfulMemories.Length > 0)
            {
                psycheAudioSource.PlayOneShot(regretfulMemories[Random.Range(0, regretfulMemories.Length)], 0.5f);
            }
            else if (currentState == PsychologicalState.Enlightened && hopefulThoughts.Length > 0)
            {
                psycheAudioSource.PlayOneShot(hopefulThoughts[Random.Range(0, hopefulThoughts.Length)], 0.5f);
            }

            yield return new WaitForSecondsRealtime(4f);
        }
    }

    IEnumerator MeditationPhase4_Flashbacks()
    {
        ShowMeditationText("Emotional memories surface, vivid and raw...");
        yield return new WaitForSecondsRealtime(2f);

        // Trigger emotional flashback based on trauma level
        if (trauma > 50f)
        {
            yield return StartCoroutine(TriggerTraumaticFlashback());
        }
        else if (enlightenment > 50f)
        {
            yield return StartCoroutine(TriggerHopefulFlashback());
        }
        else
        {
            yield return StartCoroutine(TriggerMixedFlashback());
        }
    }

    IEnumerator TriggerTraumaticFlashback()
    {
        ShowMeditationText("The faces of those who have fallen by your hand appear in the flames...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("Their eyes... filled with fear, pain, accusation...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("'Why?' they seem to ask. 'Was there no other way?'");
        yield return new WaitForSecondsRealtime(3f);

        // Add trauma for confronting memories
        AddTrauma(5f, "Confronting painful memories in meditation");
    }

    IEnumerator TriggerHopefulFlashback()
    {
        ShowMeditationText("Memories of mercy shown, lives spared, kindness given...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("The grateful faces of those you've helped shine in the firelight...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("'Thank you,' they whisper. 'You showed us there is still good in this world.'");
        yield return new WaitForSecondsRealtime(3f);

        // Reduce trauma for positive memories
        ReduceTrauma(5f, "Finding peace in memories of compassion");
    }

    IEnumerator TriggerMixedFlashback()
    {
        ShowMeditationText("A tapestry of choices unfolds... light and shadow intertwined...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("Each decision a thread in the fabric of who you are becoming...");
        yield return new WaitForSecondsRealtime(3f);

        ShowMeditationText("The pattern is not yet complete. The final design remains unwritten.");
        yield return new WaitForSecondsRealtime(3f);
    }

    IEnumerator MeditationPhase5_Resolution()
    {
        ShowMeditationText("As the meditation deepens, insight begins to emerge...");
        yield return new WaitForSecondsRealtime(2f);

        string insight = GetMeditationInsight();
        ShowMeditationText(insight);
        yield return new WaitForSecondsRealtime(4f);

        ShowMeditationText("The fire crackles softly. The mind finds a moment of peace.");
        yield return new WaitForSecondsRealtime(3f);
    }

    string GetRecentMoralChoice()
    {
        // This would track recent player choices
        string[] recentChoices = {
            "sparing the wounded enemy",
            "showing mercy to the bandit",
            "choosing violence over diplomacy",
            "protecting the innocent villager",
            "taking revenge on the betrayer"
        };

        return recentChoices[Random.Range(0, recentChoices.Length)];
    }

    string GetInternalResponse(string choice)
    {
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
                return "Was I too weak? Should I have been more ruthless?";
            case PsychologicalState.Enlightened:
                return "I acted with compassion. This is the path I choose.";
            case PsychologicalState.Tormented:
                return "I don't know if I made the right choice... the doubt gnaws at me.";
            default:
                return "Every choice shapes who I become. I must choose wisely.";
        }
    }

    string[] GetVoicesFromPast()
    {
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
                return new string[] {
                    "You have become everything you once fought against...",
                    "The darkness has claimed another soul...",
                    "Power corrupts, and you are proof of this truth..."
                };
            case PsychologicalState.Enlightened:
                return new string[] {
                    "You have found the strength to choose compassion...",
                    "In your mercy, others find hope...",
                    "The light within you grows stronger..."
                };
            case PsychologicalState.Tormented:
                return new string[] {
                    "Which path will you choose?",
                    "The struggle between light and dark defines you...",
                    "Your heart is a battlefield..."
                };
            default:
                return new string[] {
                    "The path ahead is yours to choose...",
                    "Balance is the hardest path to walk...",
                    "In stillness, find your truth..."
                };
        }
    }

    string GetMeditationInsight()
    {
        switch (currentState)
        {
            case PsychologicalState.Corrupted:
                return "The darkness whispers that power is all that matters... but is this truly who I wish to be?";
            case PsychologicalState.Enlightened:
                return "In choosing mercy, I choose to be more than my circumstances. This is my strength.";
            case PsychologicalState.Tormented:
                return "I am torn between what I was and what I could become. The choice is still mine to make.";
            case PsychologicalState.Balanced:
                return "I walk the line between light and shadow. In balance, I find my truth.";
            default:
                return "I am the ash between fire and void. In this space, I define myself.";
        }
    }

    void ShowMeditationText(string text)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            // This would display in a special meditation UI panel
            Debug.Log($"Meditation: {text}");
        }
    }

    void ShowWhisperText(string text)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"[Whisper] {text}");
            StartCoroutine(HideQuoteAfterDelay(gameUI, 2f));
        }
    }

    void ShowHopefulText(string text)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"[Inner Voice] {text}");
            StartCoroutine(HideQuoteAfterDelay(gameUI, 3f));
        }
    }

    void ShowConflictedText(string text)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowInteractionPrompt($"[Doubt] {text}");
            StartCoroutine(HideQuoteAfterDelay(gameUI, 2.5f));
        }
    }

    void ShowReflectionText()
    {
        string[] reflections = {
            "The weight of my choices... each decision carves deeper into my soul.",
            "In this moment of stillness, I remember who I was before the darkness.",
            "The faces of those I've helped... and those I've failed.",
            "Power corrupts, but powerlessness destroys. Where is the balance?",
            "I am the ash between light and shadow, seeking my true form."
        };

        string reflection = reflections[Random.Range(0, reflections.Length)];

        // Display reflection in UI
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            // Would display in meditation UI panel
            Debug.Log($"Reflection: {reflection}");
        }
    }

    void ApplyMeditationEffects()
    {
        // Reduce trauma slightly
        trauma = Mathf.Max(0f, trauma - Random.Range(5f, 15f));

        // Restore mental stability
        mentalStability = Mathf.Min(100f, mentalStability + Random.Range(10f, 20f));

        // Slight alignment shift toward balance
        if (playerStats != null)
        {
            float sunAlignment = playerStats.GetSunAlignment();
            float moonAlignment = playerStats.GetMoonAlignment();

            if (Mathf.Abs(sunAlignment) > Mathf.Abs(moonAlignment))
            {
                playerStats.AddMoralChoice(-sunAlignment * 0.1f, moonAlignment * 0.05f);
            }
            else
            {
                playerStats.AddMoralChoice(sunAlignment * 0.05f, -moonAlignment * 0.1f);
            }
        }

        Debug.Log("Meditation complete. Mental state improved.");
    }

    void EndMeditation()
    {
        if (meditationUI != null)
        {
            meditationUI.SetActive(false);
        }

        Time.timeScale = 1f;
        Cursor.lockState = CursorLockMode.Locked;
    }

    void SpeakPhilosophicalQuote()
    {
        if (stoicQuotes.Length > 0)
        {
            string quote = stoicQuotes[Random.Range(0, stoicQuotes.Length)];

            GameUI gameUI = FindObjectOfType<GameUI>();
            if (gameUI != null)
            {
                gameUI.ShowInteractionPrompt($"The Cinderborn: \"{quote}\"");
                StartCoroutine(HideQuoteAfterDelay(gameUI, 4f));
            }

            lastQuoteTime = Time.time;
            Debug.Log($"The Cinderborn reflects: \"{quote}\"");
        }
    }

    IEnumerator HideQuoteAfterDelay(GameUI gameUI, float delay)
    {
        yield return new WaitForSeconds(delay);
        gameUI.HideInteractionPrompt();
    }

    IEnumerator PsychologicalUpdateLoop()
    {
        while (true)
        {
            yield return new WaitForSeconds(1f);

            // Gradual trauma healing over time if in positive state
            if (currentState == PsychologicalState.Enlightened)
            {
                trauma = Mathf.Max(0f, trauma - 0.1f);
            }

            // Gradual trauma increase if in negative state
            if (currentState == PsychologicalState.Corrupted)
            {
                trauma = Mathf.Min(100f, trauma + 0.05f);
            }
        }
    }

    void OnMoralPathChanged(PlayerStats.MoralPath newPath)
    {
        // Add trauma for extreme moral shifts
        switch (newPath)
        {
            case PlayerStats.MoralPath.Moon:
                if (trauma < 20f) // First time going dark
                {
                    AddTrauma(15f, "The darkness whispers... and I listen.");
                }
                break;
            case PlayerStats.MoralPath.Sun:
                if (trauma > 50f) // Redemption from darkness
                {
                    ReduceTrauma(10f, "Perhaps there is still light within me.");
                }
                break;
        }
    }

    public void AddTrauma(float amount, string reason = "")
    {
        trauma = Mathf.Min(100f, trauma + amount);

        if (!string.IsNullOrEmpty(reason))
        {
            Debug.Log($"Trauma increased: {reason}");
        }

        // Play trauma sound
        if (regretfulMemories.Length > 0)
        {
            psycheAudioSource.PlayOneShot(regretfulMemories[Random.Range(0, regretfulMemories.Length)]);
        }
    }

    public void ReduceTrauma(float amount, string reason = "")
    {
        trauma = Mathf.Max(0f, trauma - amount);

        if (!string.IsNullOrEmpty(reason))
        {
            Debug.Log($"Trauma reduced: {reason}");
        }
    }

    // Getters
    public PsychologicalState GetCurrentState() => currentState;
    public float GetMentalStability() => mentalStability;
    public float GetTrauma() => trauma;
    public bool IsNearCampfire() => isNearCampfire;
}
