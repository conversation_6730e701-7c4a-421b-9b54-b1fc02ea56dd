using UnityEngine;
using System.Collections.Generic;

namespace CinderOfDarkness.Tutorial
{
    /// <summary>
    /// Individual tutorial step with completion conditions and interactive elements.
    /// Supports different input devices and provides flexible completion criteria.
    /// </summary>
    [System.Serializable]
    public class TutorialStep
    {
        #region Serialized Fields
        [Header("Step Identity")]
        [SerializeField] private string stepId;
        [SerializeField] private string title;
        [SerializeField] private int stepIndex;

        [Header("Descriptions")]
        [SerializeField] [TextArea(3, 6)] private string keyboardDescription;
        [SerializeField] [TextArea(3, 6)] private string gamepadDescription;

        [Head<PERSON>("Visual Elements")]
        [SerializeField] private Sprite stepImage;
        [SerializeField] private GameObject highlightTarget;
        [SerializeField] private Vector3 highlightOffset = Vector3.zero;

        [Header("Completion Conditions")]
        [SerializeField] private TutorialCompletionType completionType = TutorialCompletionType.Manual;
        [SerializeField] private float timeRequirement = 0f;
        [SerializeField] private string requiredInput = "";
        [SerializeField] private string requiredAction = "";
        [SerializeField] private GameObject requiredTarget;
        [SerializeField] private float requiredDistance = 2f;

        [Header("Button Prompts")]
        [SerializeField] private TutorialButtonPrompt[] buttonPrompts;

        [Header("Audio")]
        [SerializeField] private AudioClip stepStartSound;
        [SerializeField] private AudioClip stepCompleteSound;
        [SerializeField] private AudioClip voiceOverClip;

        [Header("Advanced Settings")]
        [SerializeField] private bool pauseGameDuringStep = false;
        [SerializeField] private bool allowSkipping = true;
        [SerializeField] private float minimumStepDuration = 1f;
        [SerializeField] private bool repeatUntilCompleted = false;
        #endregion

        #region Public Properties
        public string StepId => stepId;
        public string Title => title;
        public int StepIndex => stepIndex;
        public string KeyboardDescription => keyboardDescription;
        public string GamepadDescription => gamepadDescription;
        public Sprite StepImage => stepImage;
        public TutorialButtonPrompt[] ButtonPrompts => buttonPrompts;
        public bool IsActive { get; private set; }
        #endregion

        #region Private Fields
        private float stepStartTime;
        private bool hasMetConditions;
        private PlayerController playerController;
        private PlayerCombat playerCombat;
        private Transform playerTransform;
        private GameObject highlightObject;
        private bool isInitialized;
        #endregion

        #region Events
        public System.Action<TutorialStep> OnStepStarted;
        public System.Action<TutorialStep> OnStepCompleted;
        public System.Action<TutorialStep> OnConditionMet;
        #endregion

        #region Initialization
        /// <summary>
        /// Initialize the tutorial step.
        /// </summary>
        /// <param name="index">Step index in tutorial sequence</param>
        public void Initialize(int index)
        {
            if (isInitialized) return;

            stepIndex = index;
            
            // Find player components
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerController = player.GetComponent<PlayerController>();
                playerCombat = player.GetComponent<PlayerCombat>();
                playerTransform = player.transform;
            }

            // Initialize button prompts
            if (buttonPrompts != null)
            {
                foreach (var prompt in buttonPrompts)
                {
                    if (string.IsNullOrEmpty(prompt.PromptId))
                    {
                        prompt.PromptId = $"{stepId}_{prompt.Action}";
                    }
                }
            }

            isInitialized = true;
        }
        #endregion

        #region Step Control
        /// <summary>
        /// Start the tutorial step.
        /// </summary>
        public void StartStep()
        {
            if (IsActive) return;

            IsActive = true;
            stepStartTime = Time.time;
            hasMetConditions = false;

            // Pause game if required
            if (pauseGameDuringStep)
            {
                Time.timeScale = 0f;
            }

            // Create highlight if target specified
            CreateHighlight();

            // Play step start sound
            PlayStepSound(stepStartSound);

            // Play voice over
            PlayVoiceOver();

            OnStepStarted?.Invoke(this);
        }

        /// <summary>
        /// Complete the tutorial step.
        /// </summary>
        public void CompleteStep()
        {
            if (!IsActive) return;

            IsActive = false;

            // Resume game if it was paused
            if (pauseGameDuringStep)
            {
                Time.timeScale = 1f;
            }

            // Remove highlight
            RemoveHighlight();

            // Play step complete sound
            PlayStepSound(stepCompleteSound);

            OnStepCompleted?.Invoke(this);
        }

        /// <summary>
        /// Update the tutorial step.
        /// </summary>
        public void UpdateStep()
        {
            if (!IsActive) return;

            // Check completion conditions
            CheckCompletionConditions();

            // Update highlight position if following target
            UpdateHighlight();
        }

        /// <summary>
        /// Check if step is completed.
        /// </summary>
        /// <returns>True if step completion conditions are met</returns>
        public bool IsCompleted()
        {
            if (!IsActive) return false;

            // Check minimum duration
            if (Time.time - stepStartTime < minimumStepDuration)
            {
                return false;
            }

            return hasMetConditions;
        }
        #endregion

        #region Completion Conditions
        /// <summary>
        /// Check various completion conditions.
        /// </summary>
        private void CheckCompletionConditions()
        {
            switch (completionType)
            {
                case TutorialCompletionType.Manual:
                    // Completed manually via UI
                    break;

                case TutorialCompletionType.Timer:
                    CheckTimerCondition();
                    break;

                case TutorialCompletionType.Input:
                    CheckInputCondition();
                    break;

                case TutorialCompletionType.Action:
                    CheckActionCondition();
                    break;

                case TutorialCompletionType.Position:
                    CheckPositionCondition();
                    break;

                case TutorialCompletionType.Interaction:
                    CheckInteractionCondition();
                    break;

                case TutorialCompletionType.Combat:
                    CheckCombatCondition();
                    break;

                case TutorialCompletionType.Combined:
                    CheckCombinedConditions();
                    break;
            }
        }

        /// <summary>
        /// Check timer-based completion.
        /// </summary>
        private void CheckTimerCondition()
        {
            if (Time.time - stepStartTime >= timeRequirement)
            {
                hasMetConditions = true;
                OnConditionMet?.Invoke(this);
            }
        }

        /// <summary>
        /// Check input-based completion.
        /// </summary>
        private void CheckInputCondition()
        {
            if (string.IsNullOrEmpty(requiredInput)) return;

            bool inputDetected = false;

            // Check keyboard input
            if (Input.GetKeyDown(requiredInput))
            {
                inputDetected = true;
            }

            // Check for common input mappings
            switch (requiredInput.ToLower())
            {
                case "move":
                    inputDetected = Input.GetAxis("Horizontal") != 0 || Input.GetAxis("Vertical") != 0;
                    break;
                case "jump":
                    inputDetected = Input.GetKeyDown(KeyCode.Space);
                    break;
                case "attack":
                    inputDetected = Input.GetMouseButtonDown(0);
                    break;
                case "interact":
                    inputDetected = Input.GetKeyDown(KeyCode.E);
                    break;
            }

            if (inputDetected)
            {
                hasMetConditions = true;
                OnConditionMet?.Invoke(this);
            }
        }

        /// <summary>
        /// Check action-based completion.
        /// </summary>
        private void CheckActionCondition()
        {
            if (string.IsNullOrEmpty(requiredAction)) return;

            bool actionCompleted = false;

            switch (requiredAction.ToLower())
            {
                case "move":
                    actionCompleted = playerController != null && playerController.IsMoving();
                    break;
                case "jump":
                    actionCompleted = playerController != null && playerController.IsJumping();
                    break;
                case "attack":
                    actionCompleted = playerCombat != null && playerCombat.IsAttacking();
                    break;
                case "block":
                    actionCompleted = playerCombat != null && playerCombat.IsBlocking();
                    break;
                case "dodge":
                    actionCompleted = playerController != null && playerController.IsDodging();
                    break;
            }

            if (actionCompleted)
            {
                hasMetConditions = true;
                OnConditionMet?.Invoke(this);
            }
        }

        /// <summary>
        /// Check position-based completion.
        /// </summary>
        private void CheckPositionCondition()
        {
            if (requiredTarget == null || playerTransform == null) return;

            float distance = Vector3.Distance(playerTransform.position, requiredTarget.transform.position);
            if (distance <= requiredDistance)
            {
                hasMetConditions = true;
                OnConditionMet?.Invoke(this);
            }
        }

        /// <summary>
        /// Check interaction-based completion.
        /// </summary>
        private void CheckInteractionCondition()
        {
            // This would typically be triggered by interaction events
            // Implementation depends on your interaction system
        }

        /// <summary>
        /// Check combat-based completion.
        /// </summary>
        private void CheckCombatCondition()
        {
            if (playerCombat == null) return;

            // Check if player has dealt damage, taken damage, etc.
            // Implementation depends on your combat system
        }

        /// <summary>
        /// Check combined conditions.
        /// </summary>
        private void CheckCombinedConditions()
        {
            // Check multiple conditions simultaneously
            bool allConditionsMet = true;

            // Timer condition
            if (timeRequirement > 0)
            {
                allConditionsMet &= (Time.time - stepStartTime >= timeRequirement);
            }

            // Input condition
            if (!string.IsNullOrEmpty(requiredInput))
            {
                // Implementation for input check
            }

            // Position condition
            if (requiredTarget != null && playerTransform != null)
            {
                float distance = Vector3.Distance(playerTransform.position, requiredTarget.transform.position);
                allConditionsMet &= (distance <= requiredDistance);
            }

            if (allConditionsMet)
            {
                hasMetConditions = true;
                OnConditionMet?.Invoke(this);
            }
        }
        #endregion

        #region Highlighting
        /// <summary>
        /// Create highlight effect for tutorial target.
        /// </summary>
        private void CreateHighlight()
        {
            if (highlightTarget == null) return;

            // Create highlight object (could be a glowing outline, arrow, etc.)
            highlightObject = CreateHighlightEffect(highlightTarget);
        }

        /// <summary>
        /// Update highlight position and effects.
        /// </summary>
        private void UpdateHighlight()
        {
            if (highlightObject == null || highlightTarget == null) return;

            // Update highlight position to follow target
            highlightObject.transform.position = highlightTarget.transform.position + highlightOffset;
        }

        /// <summary>
        /// Remove highlight effect.
        /// </summary>
        private void RemoveHighlight()
        {
            if (highlightObject != null)
            {
                Object.Destroy(highlightObject);
                highlightObject = null;
            }
        }

        /// <summary>
        /// Create highlight effect GameObject.
        /// </summary>
        /// <param name="target">Target to highlight</param>
        /// <returns>Highlight GameObject</returns>
        private GameObject CreateHighlightEffect(GameObject target)
        {
            // Create a simple highlight effect
            GameObject highlight = new GameObject($"TutorialHighlight_{stepId}");
            highlight.transform.position = target.transform.position + highlightOffset;

            // Add visual components (outline, glow, particles, etc.)
            // This is a simplified implementation - you'd want more sophisticated highlighting
            
            return highlight;
        }
        #endregion

        #region Audio
        /// <summary>
        /// Play step-related sound.
        /// </summary>
        /// <param name="clip">Audio clip to play</param>
        private void PlayStepSound(AudioClip clip)
        {
            if (clip != null && TutorialManager.Instance != null)
            {
                // Play through tutorial manager's audio source
                AudioSource audioSource = TutorialManager.Instance.GetComponent<AudioSource>();
                if (audioSource != null)
                {
                    audioSource.PlayOneShot(clip);
                }
            }
        }

        /// <summary>
        /// Play voice over for the step.
        /// </summary>
        private void PlayVoiceOver()
        {
            if (voiceOverClip != null)
            {
                PlayStepSound(voiceOverClip);
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// Force complete the step.
        /// </summary>
        public void ForceComplete()
        {
            hasMetConditions = true;
        }

        /// <summary>
        /// Reset step to initial state.
        /// </summary>
        public void Reset()
        {
            IsActive = false;
            hasMetConditions = false;
            RemoveHighlight();
        }

        /// <summary>
        /// Check if step can be skipped.
        /// </summary>
        /// <returns>True if step can be skipped</returns>
        public bool CanSkip()
        {
            return allowSkipping;
        }

        /// <summary>
        /// Get step progress (0-1).
        /// </summary>
        /// <returns>Step progress</returns>
        public float GetProgress()
        {
            if (!IsActive) return 0f;
            if (hasMetConditions) return 1f;

            switch (completionType)
            {
                case TutorialCompletionType.Timer:
                    return Mathf.Clamp01((Time.time - stepStartTime) / timeRequirement);

                case TutorialCompletionType.Position:
                    if (requiredTarget != null && playerTransform != null)
                    {
                        float distance = Vector3.Distance(playerTransform.position, requiredTarget.transform.position);
                        return Mathf.Clamp01(1f - (distance / requiredDistance));
                    }
                    break;
            }

            return 0f;
        }
        #endregion
    }

    #region Enums
    /// <summary>
    /// Tutorial step completion types.
    /// </summary>
    public enum TutorialCompletionType
    {
        Manual,         // Completed manually via UI
        Timer,          // Completed after time duration
        Input,          // Completed when specific input is detected
        Action,         // Completed when specific action is performed
        Position,       // Completed when player reaches position
        Interaction,    // Completed when player interacts with object
        Combat,         // Completed when combat condition is met
        Combined        // Completed when multiple conditions are met
    }
    #endregion
}
