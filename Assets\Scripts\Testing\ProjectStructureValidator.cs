using System.Collections.Generic;
using System.IO;
using UnityEngine;

/// <summary>
/// Validates the professional project structure organization for Cinder of Darkness.
/// Ensures all files are properly organized according to industry standards.
/// </summary>
public class ProjectStructureValidator : MonoBehaviour
{
    #region Serialized Fields
    [Header("Validation Settings")]
    [SerializeField] private bool runValidationOnStart = true;
    [SerializeField] private bool showDetailedResults = true;
    
    [Head<PERSON>("Validation Results")]
    [SerializeField] private List<string> structureResults = new List<string>();
    [SerializeField] private List<string> organizationIssues = new List<string>();
    [SerializeField] private int totalDirectoriesChecked;
    [SerializeField] private int properlyOrganizedDirectories;
    [SerializeField] private float organizationScore;
    #endregion

    #region Expected Structure
    private readonly Dictionary<string, string[]> expectedStructure = new Dictionary<string, string[]>
    {
        ["Scripts"] = new[] { "Audio", "Build", "Cinematics", "Combat", "Documentation", "Equipment", 
                              "Graphics", "Input", "Magic", "Managers", "Modding", "NPCs", "Narrative", 
                              "Player", "Systems", "Testing", "UI", "World" },
        ["Scenes"] = new[] { "Realms" },
        ["Audio"] = new[] { "Music", "SFX" },
        ["Art"] = new[] { "Characters", "Environments", "Icons", "FX" },
        ["Settings"] = new string[0], // Files only
        ["Localization"] = new string[0],
        ["Editor"] = new string[0],
        ["Plugins"] = new string[0],
        ["Prefabs"] = new string[0],
        ["Materials"] = new string[0]
    };

    private readonly Dictionary<string, string[]> expectedSubdirectories = new Dictionary<string, string[]>
    {
        ["Scripts/Systems"] = new[] { "Analytics", "Events", "Localization", "Save", "Scene", "Steam" },
        ["Scripts/Modding"] = new[] { "ArenaEditor", "Community", "Tools" },
        ["Audio/Music"] = new[] { "Melodies" },
        ["Audio/SFX"] = new[] { "Whispers" }
    };
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize validation if enabled.
    /// </summary>
    private void Start()
    {
        if (runValidationOnStart)
        {
            StartCoroutine(RunStructureValidation());
        }
    }
    #endregion

    #region Validation Methods
    /// <summary>
    /// Run comprehensive project structure validation.
    /// </summary>
    /// <returns>Validation coroutine</returns>
    private System.Collections.IEnumerator RunStructureValidation()
    {
        Debug.Log("=== CINDER OF DARKNESS - PROJECT STRUCTURE VALIDATION ===");
        
        ClearResults();
        yield return new WaitForSeconds(0.5f);
        
        ValidateMainDirectories();
        yield return new WaitForSeconds(0.1f);
        
        ValidateSubdirectories();
        yield return new WaitForSeconds(0.1f);
        
        ValidateFileOrganization();
        yield return new WaitForSeconds(0.1f);
        
        CalculateOrganizationScore();
        DisplayResults();
    }

    /// <summary>
    /// Clear previous validation results.
    /// </summary>
    private void ClearResults()
    {
        structureResults.Clear();
        organizationIssues.Clear();
        totalDirectoriesChecked = 0;
        properlyOrganizedDirectories = 0;
    }

    /// <summary>
    /// Validate main directory structure.
    /// </summary>
    private void ValidateMainDirectories()
    {
        string assetsPath = Application.dataPath;
        
        foreach (var expectedDir in expectedStructure.Keys)
        {
            totalDirectoriesChecked++;
            string dirPath = Path.Combine(assetsPath, expectedDir);
            
            if (Directory.Exists(dirPath))
            {
                properlyOrganizedDirectories++;
                structureResults.Add($"✅ {expectedDir}/ - Directory exists");
                
                // Check subdirectories
                ValidateDirectoryContents(expectedDir, dirPath);
            }
            else
            {
                structureResults.Add($"❌ {expectedDir}/ - Directory missing");
                organizationIssues.Add($"Missing main directory: {expectedDir}");
            }
        }
    }

    /// <summary>
    /// Validate directory contents against expected structure.
    /// </summary>
    /// <param name="dirName">Directory name</param>
    /// <param name="dirPath">Full directory path</param>
    private void ValidateDirectoryContents(string dirName, string dirPath)
    {
        if (!expectedStructure.ContainsKey(dirName)) return;
        
        string[] expectedSubdirs = expectedStructure[dirName];
        
        foreach (string expectedSubdir in expectedSubdirs)
        {
            string subdirPath = Path.Combine(dirPath, expectedSubdir);
            if (Directory.Exists(subdirPath))
            {
                structureResults.Add($"  ✅ {dirName}/{expectedSubdir}/ - Subdirectory exists");
            }
            else
            {
                structureResults.Add($"  ❌ {dirName}/{expectedSubdir}/ - Subdirectory missing");
                organizationIssues.Add($"Missing subdirectory: {dirName}/{expectedSubdir}");
            }
        }
    }

    /// <summary>
    /// Validate specialized subdirectories.
    /// </summary>
    private void ValidateSubdirectories()
    {
        string assetsPath = Application.dataPath;
        
        foreach (var kvp in expectedSubdirectories)
        {
            string relativePath = kvp.Key;
            string[] expectedDirs = kvp.Value;
            string fullPath = Path.Combine(assetsPath, relativePath);
            
            if (Directory.Exists(fullPath))
            {
                foreach (string expectedDir in expectedDirs)
                {
                    totalDirectoriesChecked++;
                    string subdirPath = Path.Combine(fullPath, expectedDir);
                    
                    if (Directory.Exists(subdirPath))
                    {
                        properlyOrganizedDirectories++;
                        structureResults.Add($"✅ {relativePath}/{expectedDir}/ - Specialized directory exists");
                    }
                    else
                    {
                        structureResults.Add($"❌ {relativePath}/{expectedDir}/ - Specialized directory missing");
                        organizationIssues.Add($"Missing specialized directory: {relativePath}/{expectedDir}");
                    }
                }
            }
        }
    }

    /// <summary>
    /// Validate file organization within directories.
    /// </summary>
    private void ValidateFileOrganization()
    {
        // Check for key files in expected locations
        ValidateKeyFiles();
        
        // Check for empty directories that should be cleaned up
        CheckForEmptyDirectories();
    }

    /// <summary>
    /// Validate that key files are in their expected locations.
    /// </summary>
    private void ValidateKeyFiles()
    {
        string assetsPath = Application.dataPath;
        
        // Key files that should exist
        Dictionary<string, string> keyFiles = new Dictionary<string, string>
        {
            ["Scripts/Player/PlayerController.cs"] = "Core player controller",
            ["Scripts/Managers/GameManager.cs"] = "Main game manager",
            ["Scenes/MainMenu.unity"] = "Main menu scene",
            ["Settings/CinderOfDarknessURP.asset"] = "URP settings",
            ["README_ProjectStructure.md"] = "Project documentation"
        };

        foreach (var kvp in keyFiles)
        {
            string filePath = Path.Combine(assetsPath, kvp.Key);
            if (File.Exists(filePath))
            {
                structureResults.Add($"✅ {kvp.Key} - {kvp.Value} found");
            }
            else
            {
                structureResults.Add($"❌ {kvp.Key} - {kvp.Value} missing");
                organizationIssues.Add($"Missing key file: {kvp.Key}");
            }
        }
    }

    /// <summary>
    /// Check for empty directories that should be cleaned up.
    /// </summary>
    private void CheckForEmptyDirectories()
    {
        string assetsPath = Application.dataPath;
        
        // Directories that should not be empty
        string[] importantDirs = { "Scripts", "Scenes" };
        
        foreach (string dir in importantDirs)
        {
            string dirPath = Path.Combine(assetsPath, dir);
            if (Directory.Exists(dirPath))
            {
                if (IsDirectoryEmpty(dirPath))
                {
                    organizationIssues.Add($"Important directory is empty: {dir}");
                }
            }
        }
    }

    /// <summary>
    /// Check if a directory is empty (no files or subdirectories).
    /// </summary>
    /// <param name="path">Directory path to check</param>
    /// <returns>True if directory is empty</returns>
    private bool IsDirectoryEmpty(string path)
    {
        return Directory.GetFiles(path, "*", SearchOption.AllDirectories).Length == 0;
    }

    /// <summary>
    /// Calculate overall organization score.
    /// </summary>
    private void CalculateOrganizationScore()
    {
        if (totalDirectoriesChecked > 0)
        {
            organizationScore = ((float)properlyOrganizedDirectories / totalDirectoriesChecked) * 100f;
        }
        else
        {
            organizationScore = 0f;
        }
    }

    /// <summary>
    /// Display comprehensive validation results.
    /// </summary>
    private void DisplayResults()
    {
        Debug.Log("📊 PROJECT STRUCTURE VALIDATION RESULTS:");
        Debug.Log($"   Total Directories Checked: {totalDirectoriesChecked}");
        Debug.Log($"   Properly Organized: {properlyOrganizedDirectories}");
        Debug.Log($"   Organization Score: {organizationScore:F1}%");
        Debug.Log($"   Issues Found: {organizationIssues.Count}");
        
        if (showDetailedResults)
        {
            Debug.Log("\n📁 DIRECTORY STRUCTURE:");
            foreach (string result in structureResults)
            {
                Debug.Log($"   {result}");
            }
            
            if (organizationIssues.Count > 0)
            {
                Debug.Log("\n⚠️ ORGANIZATION ISSUES:");
                foreach (string issue in organizationIssues)
                {
                    Debug.Log($"   • {issue}");
                }
            }
        }
        
        if (organizationScore >= 90f)
        {
            Debug.Log("\n🎉 EXCELLENT PROJECT ORGANIZATION! 🎉");
            Debug.Log("✅ Professional directory structure");
            Debug.Log("✅ Industry-standard layout");
            Debug.Log("✅ Ready for team collaboration");
        }
        else if (organizationScore >= 75f)
        {
            Debug.Log("\n👍 GOOD PROJECT ORGANIZATION");
            Debug.Log("✅ Solid foundation established");
            Debug.Log("🔄 Minor improvements needed");
        }
        else
        {
            Debug.Log("\n⚠️ PROJECT ORGANIZATION NEEDS IMPROVEMENT");
            Debug.Log("🔄 Significant restructuring required");
        }
        
        Debug.Log("\n=== END PROJECT STRUCTURE VALIDATION ===");
    }
    #endregion

    #region Public API
    /// <summary>
    /// Manually trigger structure validation.
    /// </summary>
    [ContextMenu("Run Structure Validation")]
    public void RunValidationManually()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunStructureValidation());
        }
        else
        {
            Debug.LogWarning("Structure validation can only be run in Play Mode");
        }
    }

    /// <summary>
    /// Get the current organization score.
    /// </summary>
    /// <returns>Organization score as percentage</returns>
    public float GetOrganizationScore() => organizationScore;

    /// <summary>
    /// Get the number of organization issues found.
    /// </summary>
    /// <returns>Count of issues</returns>
    public int GetIssueCount() => organizationIssues.Count;
    #endregion
}
