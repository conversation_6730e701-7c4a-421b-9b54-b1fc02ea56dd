using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Unified cursor management system for Cinder of Darkness
/// Resolves conflicts between different systems controlling cursor state
/// </summary>
public class CursorManager : MonoBehaviour
{
    public enum CursorState
    {
        Gameplay,       // Locked and hidden for gameplay
        Menu,           // Visible and free for menus
        Dialogue,       // Visible and free for dialogue
        Inventory,      // Visible and free for inventory
        Settings,       // Visible and free for settings
        Loading         // Hidden but not locked
    }
    
    [Header("Cursor Settings")]
    public CursorState defaultState = CursorState.Gameplay;
    public bool overrideSystemCursor = true;
    
    [Header("Custom Cursors")]
    public Texture2D gameplayCursor;
    public Texture2D menuCursor;
    public Texture2D interactionCursor;
    public Vector2 cursorHotspot = Vector2.zero;
    
    // Static instance for global access
    private static CursorManager instance;
    public static CursorManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CursorManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("CursorManager");
                    instance = go.AddComponent<CursorManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    // Current state tracking
    private CursorState currentState;
    private CursorState previousState;
    private bool isInitialized = false;
    
    // Input system reference
    private CinderInput cinderInput;
    private MultiInputControlSystem inputSystem;
    
    void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCursorManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        // Get input system references
        inputSystem = FindObjectOfType<MultiInputControlSystem>();
        if (inputSystem != null)
        {
            cinderInput = inputSystem.cinderInput;
        }
        
        // Set initial state
        SetCursorState(defaultState);
        isInitialized = true;
    }
    
    void InitializeCursorManager()
    {
        currentState = defaultState;
        previousState = defaultState;
        
        Debug.Log("CursorManager initialized");
    }
    
    void Update()
    {
        if (!isInitialized) return;
        
        // Auto-detect state changes based on input device
        if (cinderInput != null)
        {
            bool usingGamepad = cinderInput.IsUsingGamepad();
            
            // Automatically hide cursor when using gamepad in gameplay
            if (usingGamepad && currentState == CursorState.Gameplay)
            {
                ApplyCursorSettings(false, CursorLockMode.Locked, null);
            }
            else if (!usingGamepad && currentState == CursorState.Gameplay)
            {
                ApplyCursorSettings(false, CursorLockMode.Locked, gameplayCursor);
            }
        }
    }
    
    // Static methods for easy access
    public static void SetState(CursorState state)
    {
        Instance.SetCursorState(state);
    }
    
    public static void RestorePreviousState()
    {
        Instance.SetCursorState(Instance.previousState);
    }
    
    public static CursorState GetCurrentState()
    {
        return Instance.currentState;
    }
    
    public static bool IsInGameplayMode()
    {
        return Instance.currentState == CursorState.Gameplay;
    }
    
    public static bool IsInMenuMode()
    {
        CursorState state = Instance.currentState;
        return state == CursorState.Menu || state == CursorState.Dialogue || 
               state == CursorState.Inventory || state == CursorState.Settings;
    }
    
    // Core cursor management
    public void SetCursorState(CursorState newState)
    {
        if (currentState == newState) return;
        
        previousState = currentState;
        currentState = newState;
        
        ApplyStateSettings(newState);
        
        Debug.Log($"Cursor state changed: {previousState} -> {currentState}");
    }
    
    void ApplyStateSettings(CursorState state)
    {
        switch (state)
        {
            case CursorState.Gameplay:
                ApplyCursorSettings(false, CursorLockMode.Locked, gameplayCursor);
                break;
                
            case CursorState.Menu:
                ApplyCursorSettings(true, CursorLockMode.None, menuCursor);
                break;
                
            case CursorState.Dialogue:
                ApplyCursorSettings(true, CursorLockMode.None, menuCursor);
                break;
                
            case CursorState.Inventory:
                ApplyCursorSettings(true, CursorLockMode.None, menuCursor);
                break;
                
            case CursorState.Settings:
                ApplyCursorSettings(true, CursorLockMode.None, menuCursor);
                break;
                
            case CursorState.Loading:
                ApplyCursorSettings(false, CursorLockMode.None, null);
                break;
        }
    }
    
    void ApplyCursorSettings(bool visible, CursorLockMode lockMode, Texture2D cursorTexture)
    {
        // Set visibility and lock mode
        Cursor.visible = visible;
        Cursor.lockState = lockMode;
        
        // Set custom cursor if provided and override is enabled
        if (overrideSystemCursor && cursorTexture != null)
        {
            Cursor.SetCursor(cursorTexture, cursorHotspot, CursorMode.Auto);
        }
        else if (overrideSystemCursor && cursorTexture == null)
        {
            // Reset to default cursor
            Cursor.SetCursor(null, Vector2.zero, CursorMode.Auto);
        }
    }
    
    // Convenience methods for common scenarios
    public static void EnterGameplay()
    {
        SetState(CursorState.Gameplay);
    }
    
    public static void EnterMenu()
    {
        SetState(CursorState.Menu);
    }
    
    public static void EnterDialogue()
    {
        SetState(CursorState.Dialogue);
    }
    
    public static void EnterInventory()
    {
        SetState(CursorState.Inventory);
    }
    
    public static void EnterSettings()
    {
        SetState(CursorState.Settings);
    }
    
    public static void EnterLoading()
    {
        SetState(CursorState.Loading);
    }
    
    // Temporary cursor override (for specific interactions)
    public static void SetTemporaryCursor(Texture2D cursor, float duration = 1f)
    {
        Instance.StartCoroutine(Instance.TemporaryCursorCoroutine(cursor, duration));
    }
    
    System.Collections.IEnumerator TemporaryCursorCoroutine(Texture2D tempCursor, float duration)
    {
        // Store current cursor
        CursorState originalState = currentState;
        
        // Apply temporary cursor
        if (overrideSystemCursor && tempCursor != null)
        {
            Cursor.SetCursor(tempCursor, cursorHotspot, CursorMode.Auto);
        }
        
        // Wait for duration
        yield return new WaitForSeconds(duration);
        
        // Restore original state
        ApplyStateSettings(originalState);
    }
    
    // Force cursor settings (for emergency situations)
    public static void ForceCursorVisible()
    {
        Cursor.visible = true;
        Cursor.lockState = CursorLockMode.None;
    }
    
    public static void ForceCursorHidden()
    {
        Cursor.visible = false;
        Cursor.lockState = CursorLockMode.Locked;
    }
    
    // Integration with other systems
    public void OnPlayerControllerStateChanged(bool isControllerActive)
    {
        if (isControllerActive && currentState != CursorState.Gameplay)
        {
            // Player controller is active, should be in gameplay mode
            SetCursorState(CursorState.Gameplay);
        }
    }
    
    public void OnDialogueSystemStateChanged(bool isDialogueActive)
    {
        if (isDialogueActive)
        {
            SetCursorState(CursorState.Dialogue);
        }
        else if (currentState == CursorState.Dialogue)
        {
            // Return to previous state when dialogue ends
            RestorePreviousState();
        }
    }
    
    public void OnMenuSystemStateChanged(bool isMenuActive)
    {
        if (isMenuActive)
        {
            SetCursorState(CursorState.Menu);
        }
        else if (currentState == CursorState.Menu)
        {
            // Return to gameplay when menu closes
            SetCursorState(CursorState.Gameplay);
        }
    }
    
    public void OnInventorySystemStateChanged(bool isInventoryActive)
    {
        if (isInventoryActive)
        {
            SetCursorState(CursorState.Inventory);
        }
        else if (currentState == CursorState.Inventory)
        {
            // Return to previous state when inventory closes
            RestorePreviousState();
        }
    }
    
    // Debug and utility methods
    public void LogCurrentState()
    {
        Debug.Log($"Cursor State: {currentState}");
        Debug.Log($"Cursor Visible: {Cursor.visible}");
        Debug.Log($"Cursor Lock State: {Cursor.lockState}");
    }
    
    public static void ResetToDefault()
    {
        Instance.SetCursorState(Instance.defaultState);
    }
    
    // Validation
    public bool ValidateState()
    {
        bool isValid = true;
        
        switch (currentState)
        {
            case CursorState.Gameplay:
                if (Cursor.visible || Cursor.lockState != CursorLockMode.Locked)
                {
                    Debug.LogWarning("Cursor state mismatch in Gameplay mode!");
                    isValid = false;
                }
                break;
                
            case CursorState.Menu:
            case CursorState.Dialogue:
            case CursorState.Inventory:
            case CursorState.Settings:
                if (!Cursor.visible || Cursor.lockState != CursorLockMode.None)
                {
                    Debug.LogWarning($"Cursor state mismatch in {currentState} mode!");
                    isValid = false;
                }
                break;
        }
        
        return isValid;
    }
    
    // Auto-correction
    public void CorrectCursorState()
    {
        if (!ValidateState())
        {
            Debug.Log("Correcting cursor state...");
            ApplyStateSettings(currentState);
        }
    }
    
    void OnApplicationFocus(bool hasFocus)
    {
        if (hasFocus)
        {
            // Restore cursor state when application regains focus
            ApplyStateSettings(currentState);
        }
    }
    
    void OnApplicationPause(bool pauseStatus)
    {
        if (!pauseStatus)
        {
            // Restore cursor state when application unpauses
            ApplyStateSettings(currentState);
        }
    }
}
