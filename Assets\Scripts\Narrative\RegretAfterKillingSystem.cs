using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class RegretAfterKillingSystem : MonoBehaviour
{
    [Header("Regret System Configuration")]
    public bool enableRegretPrompts = true;
    public float promptDisplayTime = 10f;
    public float contemplationDuration = 30f;

    [Header("Eligible Targets")]
    public List<RegretTarget> regretTargets = new List<RegretTarget>();
    public string[] majorCharacterTags = { "Boss", "Ally", "SignificantNPC", "Child", "Innocent" };

    [Header("Audio Elements")]
    public AudioClip[] innerMonologues;
    public AudioClip[] spiritWhispers;
    public AudioClip[] hauntingMelodies;
    public AudioClip silenceAmbience;

    [Header("Spirit Whisper Asset")]
    public SpiritWhisperAsset spiritWhisperAsset;
    private float lastWhisperTime = 0f;

    [Header("Visual Effects")]
    public GameObject regretAura;
    public ParticleSystem memoryFragments;
    public Light contemplativeLight;
    public Material ghostlyMaterial;

    private PsychologicalSystem psycheSystem;
    private DeathConsequenceSystem deathSystem;
    private DynamicMusicalSystem musicalSystem;
    private List<RegretMoment> regretHistory = new List<RegretMoment>();
    private bool isContemplating = false;

    [System.Serializable]
    public class RegretTarget
    {
        [Header("Target Identity")]
        public string characterName;
        public GameObject characterObject;
        public TargetType type;
        public RelationshipLevel relationship;

        [Header("Regret Content")]
        public string[] innerMonologueLines;
        public string[] spiritDialogue;
        public RegretIntensity intensity;
        public bool canManifestSpirit = false;

        [Header("Psychological Impact")]
        public float traumaIncrease;
        public float guiltWeight;
        public float memoryStrength;
        public string[] unlockedReflections;

        public enum TargetType
        {
            Boss,
            Ally,
            SignificantNPC,
            Child,
            Innocent,
            Mentor,
            Enemy,
            Stranger
        }

        public enum RelationshipLevel
        {
            Stranger,
            Acquaintance,
            Friend,
            CloseAlly,
            Mentor,
            Family,
            Enemy,
            Nemesis
        }

        public enum RegretIntensity
        {
            Mild,      // Brief contemplation
            Moderate,  // Extended reflection
            Severe,    // Deep psychological impact
            Profound   // Life-changing moment
        }
    }

    [System.Serializable]
    public class RegretMoment
    {
        public string characterName;
        public RegretTarget.TargetType targetType;
        public float timestamp;
        public Vector3 deathLocation;
        public string[] contemplationText;
        public bool spiritManifested;
        public float psychologicalImpact;
    }

    void Start()
    {
        psycheSystem = GetComponent<PsychologicalSystem>();
        deathSystem = GetComponent<DeathConsequenceSystem>();
        musicalSystem = GetComponent<DynamicMusicalSystem>();

        // Subscribe to death events
        if (deathSystem != null)
        {
            deathSystem.OnCharacterDeath += OnCharacterKilled;
        }

        // Load spirit whisper asset if not assigned
        if (spiritWhisperAsset == null)
        {
            spiritWhisperAsset = Resources.Load<SpiritWhisperAsset>("Audio/Whispers/SpiritWhisperAsset");
            if (spiritWhisperAsset == null)
            {
                Debug.LogWarning("SpiritWhisperAsset not found! Please assign it manually or generate spirit whispers.");
            }
        }

        // Initialize spirit whispers array from asset
        if (spiritWhisperAsset != null && spiritWhispers.Length == 0)
        {
            spiritWhispers = spiritWhisperAsset.spiritWhispers;
            Debug.Log($"Loaded {spiritWhispers.Length} spirit whisper clips from asset");
        }

        InitializeRegretTargets();
    }

    void OnDestroy()
    {
        // Unsubscribe from events
        if (deathSystem != null)
        {
            deathSystem.OnCharacterDeath -= OnCharacterKilled;
        }
    }

    void InitializeRegretTargets()
    {
        // Find all potential regret targets in the scene
        GameObject[] potentialTargets = GameObject.FindGameObjectsWithTag("Character");

        foreach (GameObject target in potentialTargets)
        {
            CharacterController character = target.GetComponent<CharacterController>();
            if (character != null && ShouldTrackForRegret(character))
            {
                RegretTarget regretTarget = CreateRegretTarget(character);
                regretTargets.Add(regretTarget);
            }
        }

        Debug.Log($"Regret After Killing System initialized - tracking {regretTargets.Count} potential targets");
    }

    bool ShouldTrackForRegret(CharacterController character)
    {
        // Check if character has any of the major character tags
        foreach (string tag in majorCharacterTags)
        {
            if (character.gameObject.CompareTag(tag))
                return true;
        }

        // Check if character has significant story importance
        NPCController npc = character.GetComponent<NPCController>();
        if (npc != null && npc.storyImportance > 50f)
            return true;

        return false;
    }

    RegretTarget CreateRegretTarget(CharacterController character)
    {
        RegretTarget target = new RegretTarget
        {
            characterName = character.name,
            characterObject = character.gameObject,
            type = DetermineTargetType(character),
            relationship = DetermineRelationshipLevel(character),
            intensity = DetermineRegretIntensity(character),
            canManifestSpirit = Random.Range(0f, 1f) < 0.3f, // 30% chance
            innerMonologueLines = GenerateInnerMonologue(character),
            spiritDialogue = GenerateSpiritDialogue(character),
            traumaIncrease = CalculateTraumaIncrease(character),
            guiltWeight = CalculateGuiltWeight(character),
            memoryStrength = CalculateMemoryStrength(character)
        };

        return target;
    }

    RegretTarget.TargetType DetermineTargetType(CharacterController character)
    {
        if (character.CompareTag("Boss"))
            return RegretTarget.TargetType.Boss;
        else if (character.CompareTag("Ally"))
            return RegretTarget.TargetType.Ally;
        else if (character.CompareTag("Child"))
            return RegretTarget.TargetType.Child;
        else if (character.CompareTag("Innocent"))
            return RegretTarget.TargetType.Innocent;
        else
            return RegretTarget.TargetType.SignificantNPC;
    }

    RegretTarget.RelationshipLevel DetermineRelationshipLevel(CharacterController character)
    {
        // This would integrate with relationship systems
        NPCController npc = character.GetComponent<NPCController>();
        if (npc != null)
        {
            float relationship = npc.GetRelationshipLevel();

            if (relationship > 80f)
                return RegretTarget.RelationshipLevel.Family;
            else if (relationship > 60f)
                return RegretTarget.RelationshipLevel.CloseAlly;
            else if (relationship > 40f)
                return RegretTarget.RelationshipLevel.Friend;
            else if (relationship > 20f)
                return RegretTarget.RelationshipLevel.Acquaintance;
            else if (relationship < -40f)
                return RegretTarget.RelationshipLevel.Enemy;
            else if (relationship < -60f)
                return RegretTarget.RelationshipLevel.Nemesis;
        }

        return RegretTarget.RelationshipLevel.Stranger;
    }

    RegretTarget.RegretIntensity DetermineRegretIntensity(CharacterController character)
    {
        if (character.CompareTag("Child") || character.CompareTag("Innocent"))
            return RegretTarget.RegretIntensity.Profound;
        else if (character.CompareTag("Ally"))
            return RegretTarget.RegretIntensity.Severe;
        else if (character.CompareTag("Boss"))
            return RegretTarget.RegretIntensity.Moderate;
        else
            return RegretTarget.RegretIntensity.Mild;
    }

    string[] GenerateInnerMonologue(CharacterController character)
    {
        List<string> monologue = new List<string>();

        switch (DetermineTargetType(character))
        {
            case RegretTarget.TargetType.Child:
                monologue.AddRange(new string[]
                {
                    "They were just a child... innocent of the world's cruelties.",
                    "What have I become? What monster kills the innocent?",
                    "Their eyes... they trusted me, even at the end.",
                    "This weight... it will never leave my soul."
                });
                break;

            case RegretTarget.TargetType.Ally:
                monologue.AddRange(new string[]
                {
                    "We fought side by side... and now they lie still by my hand.",
                    "Was there another way? Could I have saved them from this fate?",
                    "They believed in me, trusted me... and I failed them.",
                    "Their blood stains more than my hands - it stains my very essence."
                });
                break;

            case RegretTarget.TargetType.Boss:
                monologue.AddRange(new string[]
                {
                    "Even in victory, I feel hollow. Was this justice or vengeance?",
                    "They were formidable... perhaps they deserved a better end.",
                    "In their final moments, I saw not a monster, but a being like myself.",
                    "Power means nothing if it only brings emptiness."
                });
                break;

            case RegretTarget.TargetType.Innocent:
                monologue.AddRange(new string[]
                {
                    "They never raised a weapon against me... yet I struck them down.",
                    "Innocent blood cries out from the earth. What answer can I give?",
                    "I am become death, destroyer of the blameless.",
                    "How many more innocents will fall before this ends?"
                });
                break;
        }

        return monologue.ToArray();
    }

    string[] GenerateSpiritDialogue(CharacterController character)
    {
        List<string> dialogue = new List<string>();

        switch (DetermineTargetType(character))
        {
            case RegretTarget.TargetType.Child:
                dialogue.AddRange(new string[]
                {
                    "Why did you hurt me? I just wanted to play...",
                    "Mama said you were supposed to protect people like me.",
                    "I forgive you... but you have to forgive yourself too.",
                    "Don't let my death make you forget how to be kind."
                });
                break;

            case RegretTarget.TargetType.Ally:
                dialogue.AddRange(new string[]
                {
                    "I don't blame you... you did what you thought was right.",
                    "Remember me not for how I died, but for how I lived.",
                    "The path ahead is dark... but you must not walk it alone.",
                    "My death has meaning only if you learn from it."
                });
                break;

            case RegretTarget.TargetType.Boss:
                dialogue.AddRange(new string[]
                {
                    "You have won... but victory tastes of ash, does it not?",
                    "I was not always the monster you fought. Remember that.",
                    "Power corrupts... beware the path I walked.",
                    "In defeating me, you have become what I once was."
                });
                break;
        }

        return dialogue.ToArray();
    }

    float CalculateTraumaIncrease(CharacterController character)
    {
        switch (DetermineTargetType(character))
        {
            case RegretTarget.TargetType.Child:
                return 50f;
            case RegretTarget.TargetType.Innocent:
                return 40f;
            case RegretTarget.TargetType.Ally:
                return 35f;
            case RegretTarget.TargetType.Boss:
                return 15f;
            default:
                return 20f;
        }
    }

    float CalculateGuiltWeight(CharacterController character)
    {
        float baseGuilt = CalculateTraumaIncrease(character);
        RegretTarget.RelationshipLevel relationship = DetermineRelationshipLevel(character);

        switch (relationship)
        {
            case RegretTarget.RelationshipLevel.Family:
                return baseGuilt * 2f;
            case RegretTarget.RelationshipLevel.CloseAlly:
                return baseGuilt * 1.5f;
            case RegretTarget.RelationshipLevel.Friend:
                return baseGuilt * 1.3f;
            case RegretTarget.RelationshipLevel.Enemy:
                return baseGuilt * 0.5f;
            case RegretTarget.RelationshipLevel.Nemesis:
                return baseGuilt * 0.3f;
            default:
                return baseGuilt;
        }
    }

    float CalculateMemoryStrength(CharacterController character)
    {
        return CalculateGuiltWeight(character) / 100f * Random.Range(0.8f, 1.2f);
    }

    void OnCharacterKilled(GameObject killedCharacter, bool wasPlayerKill)
    {
        if (!wasPlayerKill || !enableRegretPrompts) return;

        RegretTarget target = GetRegretTarget(killedCharacter);
        if (target != null)
        {
            StartCoroutine(TriggerRegretSequence(target, killedCharacter.transform.position));
        }
    }

    RegretTarget GetRegretTarget(GameObject character)
    {
        foreach (RegretTarget target in regretTargets)
        {
            if (target.characterObject == character)
                return target;
        }
        return null;
    }

    IEnumerator TriggerRegretSequence(RegretTarget target, Vector3 deathLocation)
    {
        // Wait a moment for the death to register
        yield return new WaitForSeconds(2f);

        // Show the regret prompt
        yield return StartCoroutine(ShowRegretPrompt(target, deathLocation));
    }

    IEnumerator ShowRegretPrompt(RegretTarget target, Vector3 deathLocation)
    {
        ShowRegretMessage("The silence that follows is deafening...");
        yield return new WaitForSeconds(3f);

        ShowRegretMessage("Sit by the body");
        ShowRegretMessage("Press [R] to reflect on what you have done");

        float elapsed = 0f;
        bool playerChoseToSit = false;

        while (elapsed < promptDisplayTime && !playerChoseToSit)
        {
            if (Input.GetKeyDown(KeyCode.R))
            {
                playerChoseToSit = true;
                break;
            }

            elapsed += Time.deltaTime;
            yield return null;
        }

        if (playerChoseToSit)
        {
            yield return StartCoroutine(RegretContemplationSequence(target, deathLocation));
        }
        else
        {
            // Player chose not to reflect - still apply some psychological impact
            ApplyMinimalRegretImpact(target);
        }
    }

    IEnumerator RegretContemplationSequence(RegretTarget target, Vector3 deathLocation)
    {
        isContemplating = true;

        // Disable player movement
        DisablePlayerMovement();

        // Apply visual effects
        ApplyRegretVisualEffects(deathLocation);

        // Trigger musical response
        if (musicalSystem != null)
        {
            musicalSystem.TriggerEmotionalCue(DynamicMusicalSystem.EmotionalCue.EmotionalTrigger.EmotionalMoment);
        }

        ShowRegretMessage("You kneel beside the body, the weight of your actions settling upon you...");
        yield return new WaitForSeconds(4f);

        // Inner monologue sequence
        yield return StartCoroutine(InnerMonologueSequence(target));

        // Possible spirit manifestation
        if (target.canManifestSpirit && Random.Range(0f, 1f) < 0.7f)
        {
            yield return StartCoroutine(SpiritManifestationSequence(target));
        }

        // Apply psychological impact
        ApplyRegretImpact(target);

        // Record the regret moment
        RecordRegretMoment(target, deathLocation);

        // End contemplation
        yield return StartCoroutine(EndContemplationSequence());

        isContemplating = false;
    }

    IEnumerator InnerMonologueSequence(RegretTarget target)
    {
        ShowRegretMessage("Your thoughts turn inward, heavy with the weight of what you have done...");
        yield return new WaitForSeconds(3f);

        foreach (string line in target.innerMonologueLines)
        {
            ShowRegretMessage($"\"{ line}\"");
            yield return new WaitForSeconds(4f);
        }

        // Play inner monologue audio
        if (innerMonologues.Length > 0)
        {
            AudioClip monologue = innerMonologues[Random.Range(0, innerMonologues.Length)];
            AudioSource.PlayClipAtPoint(monologue, transform.position, 0.6f);
        }
    }

    IEnumerator SpiritManifestationSequence(RegretTarget target)
    {
        ShowRegretMessage("The air grows cold... something stirs in the space between life and death...");
        yield return new WaitForSeconds(3f);

        // Create spirit manifestation effect
        CreateSpiritManifestation(target);

        ShowRegretMessage("A presence makes itself known - not threatening, but sorrowful...");
        yield return new WaitForSeconds(4f);

        // Spirit dialogue
        foreach (string line in target.spiritDialogue)
        {
            ShowRegretMessage($"Spirit: \"{line}\"");
            yield return new WaitForSeconds(5f);
        }

        // Play spirit whisper audio with enhanced system
        PlaySpiritWhisper();

        ShowRegretMessage("The presence fades, leaving only memory and the echo of forgiveness...");
        yield return new WaitForSeconds(4f);

        // Remove spirit manifestation
        RemoveSpiritManifestation();
    }

    void CreateSpiritManifestation(RegretTarget target)
    {
        // Create a ghostly representation
        if (target.characterObject != null && ghostlyMaterial != null)
        {
            GameObject spirit = Instantiate(target.characterObject, target.characterObject.transform.position, target.characterObject.transform.rotation);

            // Apply ghostly material
            Renderer[] renderers = spirit.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.material = ghostlyMaterial;
            }

            // Make it fade in and out
            StartCoroutine(FadeSpiritManifestation(spirit));
        }
    }

    IEnumerator FadeSpiritManifestation(GameObject spirit)
    {
        float fadeTime = 15f;
        float elapsed = 0f;

        Renderer[] renderers = spirit.GetComponentsInChildren<Renderer>();

        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float alpha = Mathf.Sin(elapsed / fadeTime * Mathf.PI) * 0.5f + 0.3f;

            foreach (Renderer renderer in renderers)
            {
                Color color = renderer.material.color;
                color.a = alpha;
                renderer.material.color = color;
            }

            yield return null;
        }

        Destroy(spirit);
    }

    void RemoveSpiritManifestation()
    {
        // Clean up any remaining spirit effects
    }

    IEnumerator EndContemplationSequence()
    {
        ShowRegretMessage("You rise slowly, forever changed by this moment of reflection...");
        yield return new WaitForSeconds(3f);

        ShowRegretMessage("The body lies still, but their memory will live on in your choices.");
        yield return new WaitForSeconds(3f);

        // Remove visual effects
        RemoveRegretVisualEffects();

        // Re-enable player movement
        EnablePlayerMovement();
    }

    void ApplyRegretVisualEffects(Vector3 location)
    {
        // Apply contemplative lighting
        if (contemplativeLight != null)
        {
            contemplativeLight.transform.position = location + Vector3.up * 2f;
            contemplativeLight.enabled = true;
            contemplativeLight.color = new Color(0.6f, 0.6f, 0.8f);
            contemplativeLight.intensity = 0.5f;
        }

        // Start memory fragment effects
        if (memoryFragments != null)
        {
            memoryFragments.transform.position = location;
            memoryFragments.Play();
        }

        // Apply regret aura
        if (regretAura != null)
        {
            regretAura.transform.position = transform.position;
            regretAura.SetActive(true);
        }
    }

    void RemoveRegretVisualEffects()
    {
        if (contemplativeLight != null)
        {
            contemplativeLight.enabled = false;
        }

        if (memoryFragments != null)
        {
            memoryFragments.Stop();
        }

        if (regretAura != null)
        {
            regretAura.SetActive(false);
        }
    }

    void ApplyRegretImpact(RegretTarget target)
    {
        // Apply psychological impact
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(target.traumaIncrease, $"Regret over killing {target.characterName}");
            psycheSystem.AddGuilt(target.guiltWeight, target.characterName);
            psycheSystem.AddMemory(target.characterName, target.memoryStrength);
        }

        // Unlock reflective dialogue options
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            foreach (string reflection in target.unlockedReflections)
            {
                gameManager.UnlockDialogueOption(reflection);
            }

            gameManager.UnlockDialogueOption($"RegretOver_{target.characterName}");
        }
    }

    void ApplyMinimalRegretImpact(RegretTarget target)
    {
        // Apply reduced impact for not reflecting
        if (psycheSystem != null)
        {
            psycheSystem.AddTrauma(target.traumaIncrease * 0.5f, $"Unprocessed guilt over {target.characterName}");
            psycheSystem.AddSuppressedGuilt(target.guiltWeight * 0.3f);
        }
    }

    void RecordRegretMoment(RegretTarget target, Vector3 location)
    {
        RegretMoment moment = new RegretMoment
        {
            characterName = target.characterName,
            targetType = target.type,
            timestamp = Time.time,
            deathLocation = location,
            contemplationText = target.innerMonologueLines,
            spiritManifested = target.canManifestSpirit,
            psychologicalImpact = target.traumaIncrease + target.guiltWeight
        };

        regretHistory.Add(moment);

        Debug.Log($"Regret moment recorded: {target.characterName} - Impact: {moment.psychologicalImpact}");
    }

    void DisablePlayerMovement()
    {
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.SetMovementEnabled(false);
        }
    }

    void EnablePlayerMovement()
    {
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.SetMovementEnabled(true);
        }
    }

    void PlaySpiritWhisper()
    {
        // Check cooldown to prevent audio spam
        if (Time.time - lastWhisperTime < (spiritWhisperAsset?.cooldownTime ?? 2f))
        {
            return;
        }

        AudioClip whisperClip = null;
        float volume = 0.3f;
        float pitch = 1f;

        // Use spirit whisper asset if available
        if (spiritWhisperAsset != null)
        {
            whisperClip = spiritWhisperAsset.GetRandomWhisper();
            volume = spiritWhisperAsset.defaultVolume;
            pitch = spiritWhisperAsset.GetRandomPitch();
        }
        // Fallback to legacy array
        else if (spiritWhispers.Length > 0)
        {
            whisperClip = spiritWhispers[Random.Range(0, spiritWhispers.Length)];
            pitch = Random.Range(0.8f, 1.2f);
        }

        if (whisperClip != null)
        {
            // Create temporary audio source for 3D positioned audio
            GameObject tempAudioSource = new GameObject("SpiritWhisperAudio");
            tempAudioSource.transform.position = transform.position;

            AudioSource audioSource = tempAudioSource.AddComponent<AudioSource>();
            audioSource.clip = whisperClip;
            audioSource.volume = volume;
            audioSource.pitch = pitch;
            audioSource.spatialBlend = 0.7f; // Partially 3D
            audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
            audioSource.maxDistance = 15f;
            audioSource.Play();

            // Destroy the temporary audio source after the clip finishes
            Destroy(tempAudioSource, whisperClip.length + 1f);

            lastWhisperTime = Time.time;

            Debug.Log($"Playing spirit whisper: {whisperClip.name} (Volume: {volume:F2}, Pitch: {pitch:F2})");
        }
        else
        {
            Debug.LogWarning("No spirit whisper clips available to play!");
        }
    }

    void ShowRegretMessage(string message)
    {
        GameUI gameUI = FindObjectOfType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowRegretText(message); // Special regret text display
        }

        Debug.Log($"Regret: {message}");
    }

    // Public interface
    public void AddRegretTarget(GameObject character)
    {
        CharacterController controller = character.GetComponent<CharacterController>();
        if (controller != null && !HasRegretTarget(character))
        {
            RegretTarget target = CreateRegretTarget(controller);
            regretTargets.Add(target);
        }
    }

    public bool HasRegretTarget(GameObject character)
    {
        return GetRegretTarget(character) != null;
    }

    public void ForceRegretMoment(GameObject character)
    {
        RegretTarget target = GetRegretTarget(character);
        if (target != null)
        {
            StartCoroutine(TriggerRegretSequence(target, character.transform.position));
        }
    }

    // Getters
    public bool IsContemplating() => isContemplating;
    public List<RegretMoment> GetRegretHistory() => regretHistory;
    public int GetRegretCount() => regretHistory.Count;
    public float GetTotalRegretImpact()
    {
        float total = 0f;
        foreach (RegretMoment moment in regretHistory)
        {
            total += moment.psychologicalImpact;
        }
        return total;
    }
}
