#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

/// <summary>
/// Texture Optimization System for Cinder of Darkness
/// Automatically optimizes textures for build size and performance while maintaining visual quality
/// </summary>
public class TextureOptimizer : EditorWindow
{
    [MenuItem("Cinder of Darkness/Build Tools/Optimize Textures")]
    public static void ShowWindow()
    {
        GetWindow<TextureOptimizer>("Texture Optimizer");
    }
    
    private bool optimizeUITextures = true;
    private bool optimizeMaterialTextures = true;
    private bool optimizeEffectTextures = true;
    private bool createBackups = true;
    private bool preserveAlpha = true;
    
    private List<string> processedTextures = new List<string>();
    private List<string> optimizedTextures = new List<string>();
    private long originalSize = 0;
    private long optimizedSize = 0;
    
    void OnGUI()
    {
        GUILayout.Label("Texture Optimization for Release Build", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        optimizeUITextures = EditorGUILayout.Toggle("Optimize UI Textures", optimizeUITextures);
        optimizeMaterialTextures = EditorGUILayout.Toggle("Optimize Material Textures", optimizeMaterialTextures);
        optimizeEffectTextures = EditorGUILayout.Toggle("Optimize Effect Textures", optimizeEffectTextures);
        
        GUILayout.Space(10);
        preserveAlpha = EditorGUILayout.Toggle("Preserve Alpha Channels", preserveAlpha);
        createBackups = EditorGUILayout.Toggle("Create Backup Files", createBackups);
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("Optimize All Textures", GUILayout.Height(30)))
        {
            OptimizeAllTextures();
        }
        
        if (GUILayout.Button("Restore from Backups", GUILayout.Height(30)))
        {
            RestoreFromBackups();
        }
        
        GUILayout.Space(20);
        
        if (processedTextures.Count > 0)
        {
            GUILayout.Label($"Processed {processedTextures.Count} textures", EditorStyles.helpBox);
            GUILayout.Label($"Optimized {optimizedTextures.Count} textures", EditorStyles.helpBox);
            
            if (originalSize > 0 && optimizedSize > 0)
            {
                float savings = ((float)(originalSize - optimizedSize) / originalSize) * 100f;
                GUILayout.Label($"Size reduction: {savings:F1}% ({FormatBytes(originalSize - optimizedSize)} saved)", EditorStyles.helpBox);
            }
        }
    }
    
    void OptimizeAllTextures()
    {
        processedTextures.Clear();
        optimizedTextures.Clear();
        originalSize = 0;
        optimizedSize = 0;
        
        List<string> texturePaths = new List<string>();
        
        if (optimizeUITextures)
        {
            texturePaths.AddRange(Directory.GetFiles("Assets/UI", "*.png", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/UI", "*.jpg", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/UI", "*.tga", SearchOption.AllDirectories));
        }
        
        if (optimizeMaterialTextures)
        {
            texturePaths.AddRange(Directory.GetFiles("Assets/Materials", "*.png", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/Materials", "*.jpg", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/Materials", "*.tga", SearchOption.AllDirectories));
        }
        
        if (optimizeEffectTextures)
        {
            texturePaths.AddRange(Directory.GetFiles("Assets/Effects", "*.png", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/Effects", "*.jpg", SearchOption.AllDirectories));
            texturePaths.AddRange(Directory.GetFiles("Assets/Effects", "*.tga", SearchOption.AllDirectories));
        }
        
        foreach (string texturePath in texturePaths)
        {
            OptimizeTexture(texturePath);
        }
        
        AssetDatabase.Refresh();
        
        Debug.Log($"Texture optimization completed. Processed {processedTextures.Count} textures, optimized {optimizedTextures.Count} textures.");
    }
    
    void OptimizeTexture(string texturePath)
    {
        processedTextures.Add(texturePath);
        
        TextureImporter importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
        if (importer == null) return;
        
        // Create backup if requested
        if (createBackups)
        {
            CreateTextureBackup(texturePath, importer);
        }
        
        // Calculate original size
        Texture2D originalTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
        if (originalTexture != null)
        {
            originalSize += GetTextureMemorySize(originalTexture);
        }
        
        bool wasModified = false;
        
        // Optimize based on texture type and usage
        TextureOptimizationSettings settings = GetOptimizationSettings(texturePath, importer);
        
        if (ApplyOptimizationSettings(importer, settings))
        {
            wasModified = true;
        }
        
        if (wasModified)
        {
            importer.SaveAndReimport();
            optimizedTextures.Add(texturePath);
            
            // Calculate optimized size
            Texture2D optimizedTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
            if (optimizedTexture != null)
            {
                optimizedSize += GetTextureMemorySize(optimizedTexture);
            }
        }
    }
    
    TextureOptimizationSettings GetOptimizationSettings(string texturePath, TextureImporter importer)
    {
        var settings = new TextureOptimizationSettings();
        
        string fileName = Path.GetFileName(texturePath).ToLower();
        string directory = Path.GetDirectoryName(texturePath).ToLower();
        
        // UI Textures
        if (directory.Contains("ui"))
        {
            settings.textureType = TextureImporterType.Sprite;
            settings.compressionFormat = TextureImporterFormat.DXT5; // Preserve UI quality
            settings.maxTextureSize = 1024;
            settings.generateMipMaps = false;
        }
        // Material Textures
        else if (directory.Contains("materials"))
        {
            settings.textureType = TextureImporterType.Default;
            settings.compressionFormat = HasAlphaChannel(importer) ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
            settings.maxTextureSize = 512;
            settings.generateMipMaps = true;
        }
        // Effect Textures
        else if (directory.Contains("effects"))
        {
            settings.textureType = TextureImporterType.Default;
            settings.compressionFormat = TextureImporterFormat.DXT5; // Effects often need alpha
            settings.maxTextureSize = 256;
            settings.generateMipMaps = false;
        }
        // Normal Maps
        else if (fileName.Contains("normal") || fileName.Contains("_n"))
        {
            settings.textureType = TextureImporterType.NormalMap;
            settings.compressionFormat = TextureImporterFormat.DXT5;
            settings.maxTextureSize = 512;
            settings.generateMipMaps = true;
        }
        // Default settings
        else
        {
            settings.textureType = TextureImporterType.Default;
            settings.compressionFormat = HasAlphaChannel(importer) ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
            settings.maxTextureSize = 512;
            settings.generateMipMaps = true;
        }
        
        return settings;
    }
    
    bool ApplyOptimizationSettings(TextureImporter importer, TextureOptimizationSettings settings)
    {
        bool wasModified = false;
        
        if (importer.textureType != settings.textureType)
        {
            importer.textureType = settings.textureType;
            wasModified = true;
        }
        
        if (importer.mipmapEnabled != settings.generateMipMaps)
        {
            importer.mipmapEnabled = settings.generateMipMaps;
            wasModified = true;
        }
        
        // Platform-specific settings
        var platformSettings = importer.GetPlatformTextureSettings("Standalone");
        
        if (platformSettings.format != settings.compressionFormat)
        {
            platformSettings.format = settings.compressionFormat;
            platformSettings.overridden = true;
            wasModified = true;
        }
        
        if (platformSettings.maxTextureSize != settings.maxTextureSize)
        {
            platformSettings.maxTextureSize = settings.maxTextureSize;
            platformSettings.overridden = true;
            wasModified = true;
        }
        
        if (wasModified)
        {
            importer.SetPlatformTextureSettings(platformSettings);
        }
        
        return wasModified;
    }
    
    bool HasAlphaChannel(TextureImporter importer)
    {
        return importer.DoesSourceTextureHaveAlpha() && preserveAlpha;
    }
    
    void CreateTextureBackup(string texturePath, TextureImporter importer)
    {
        string backupPath = texturePath + ".backup";
        
        // Create a backup of the import settings
        var backupData = new TextureBackupData
        {
            textureType = importer.textureType,
            mipmapEnabled = importer.mipmapEnabled,
            platformSettings = importer.GetPlatformTextureSettings("Standalone")
        };
        
        string backupJson = JsonUtility.ToJson(backupData, true);
        File.WriteAllText(backupPath, backupJson);
    }
    
    void RestoreFromBackups()
    {
        string[] backupFiles = Directory.GetFiles("Assets", "*.backup", SearchOption.AllDirectories);
        
        foreach (string backupFile in backupFiles)
        {
            string originalFile = backupFile.Replace(".backup", "");
            if (File.Exists(originalFile))
            {
                RestoreTextureFromBackup(originalFile, backupFile);
                File.Delete(backupFile);
            }
        }
        
        AssetDatabase.Refresh();
        Debug.Log($"Restored {backupFiles.Length} textures from backups.");
    }
    
    void RestoreTextureFromBackup(string texturePath, string backupPath)
    {
        TextureImporter importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
        if (importer == null) return;
        
        string backupJson = File.ReadAllText(backupPath);
        TextureBackupData backupData = JsonUtility.FromJson<TextureBackupData>(backupJson);
        
        importer.textureType = backupData.textureType;
        importer.mipmapEnabled = backupData.mipmapEnabled;
        importer.SetPlatformTextureSettings(backupData.platformSettings);
        
        importer.SaveAndReimport();
    }
    
    long GetTextureMemorySize(Texture2D texture)
    {
        if (texture == null) return 0;
        
        // Rough estimation of texture memory usage
        int pixelCount = texture.width * texture.height;
        int bytesPerPixel = 4; // Assume RGBA32 for estimation
        
        return pixelCount * bytesPerPixel;
    }
    
    string FormatBytes(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024f:F1} KB";
        return $"{bytes / (1024f * 1024f):F1} MB";
    }
    
    [System.Serializable]
    public class TextureOptimizationSettings
    {
        public TextureImporterType textureType;
        public TextureImporterFormat compressionFormat;
        public int maxTextureSize;
        public bool generateMipMaps;
    }
    
    [System.Serializable]
    public class TextureBackupData
    {
        public TextureImporterType textureType;
        public bool mipmapEnabled;
        public TextureImporterPlatformSettings platformSettings;
    }
}
#endif
