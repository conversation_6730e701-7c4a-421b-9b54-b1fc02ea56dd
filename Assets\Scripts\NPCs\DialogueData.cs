using UnityEngine;
using System.Collections.Generic;

[CreateAssetMenu(fileName = "New Dialogue", menuName = "MOMO/Dialogue Tree")]
public class DialogueData : ScriptableObject
{
    public DialogueTree dialogueTree;
    
    public static DialogueTree CreateVillagerDialogue()
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Village Elder";
        dialogue.nodes = new List<DialogueNode>();
        
        // Node 0 - Initial greeting
        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Elder Theron";
        node0.dialogueText = "Ah, young one... I sense a darkness within thee, yet also a flicker of light. The path ahead is treacherous, but thy choices shall forge thy destiny.";
        node0.choices = new List<DialogueChoice>();
        
        // Choice 1 - Respectful
        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "I seek guidance, <PERSON>. The world has shown me only cruelty.";
        choice1.nextDialogueIndex = 1;
        choice1.sunAlignmentChange = 5f;
        choice1.moonAlignmentChange = 0f;
        node0.choices.Add(choice1);
        
        // Choice 2 - Defiant
        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I need no guidance from those who stood by while I suffered.";
        choice2.nextDialogueIndex = 2;
        choice2.sunAlignmentChange = 0f;
        choice2.moonAlignmentChange = 10f;
        node0.choices.Add(choice2);
        
        // Choice 3 - Neutral
        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "I'm just trying to survive. What do you know of my path?";
        choice3.nextDialogueIndex = 3;
        choice3.sunAlignmentChange = 2f;
        choice3.moonAlignmentChange = 2f;
        node0.choices.Add(choice3);
        
        dialogue.nodes.Add(node0);
        
        // Node 1 - Sun path response
        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Elder Theron";
        node1.dialogueText = "Thy humility honors thee, child. The path of the Sun teaches that even in darkness, one can choose to be a beacon of hope. Perhaps... thou art destined for greater things than this village can offer.";
        node1.choices = new List<DialogueChoice>();
        
        DialogueChoice choice1_1 = new DialogueChoice();
        choice1_1.choiceText = "Tell me more about this path of the Sun.";
        choice1_1.nextDialogueIndex = 4;
        choice1_1.sunAlignmentChange = 5f;
        node1.choices.Add(choice1_1);
        
        DialogueChoice choice1_2 = new DialogueChoice();
        choice1_2.choiceText = "I must go. Thank you for your words.";
        choice1_2.nextDialogueIndex = -1; // End dialogue
        node1.choices.Add(choice1_2);
        
        dialogue.nodes.Add(node1);
        
        // Node 2 - Moon path response
        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Elder Theron";
        node2.dialogueText = "Thy anger burns bright, young one. The path of the Moon whispers that power comes to those who seize it, that the world respects only strength. But beware... for that path leads to solitude.";
        node2.choices = new List<DialogueChoice>();
        
        DialogueChoice choice2_1 = new DialogueChoice();
        choice2_1.choiceText = "Solitude is better than false companionship.";
        choice2_1.nextDialogueIndex = 5;
        choice2_1.moonAlignmentChange = 10f;
        node2.choices.Add(choice2_1);
        
        DialogueChoice choice2_2 = new DialogueChoice();
        choice2_2.choiceText = "Perhaps you're right. I should reconsider.";
        choice2_2.nextDialogueIndex = 6;
        choice2_2.sunAlignmentChange = 5f;
        choice2_2.moonAlignmentChange = -5f;
        node2.choices.Add(choice2_2);
        
        dialogue.nodes.Add(node2);
        
        // Node 3 - Neutral response
        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Elder Theron";
        node3.dialogueText = "Survival... yes, that is the first lesson. But know this - there are ancient powers stirring in this realm. The Sages of old watch and wait. Thy bloodline may be more significant than thou dost realize.";
        node3.choices = new List<DialogueChoice>();
        
        DialogueChoice choice3_1 = new DialogueChoice();
        choice3_1.choiceText = "My bloodline? What do you mean?";
        choice3_1.nextDialogueIndex = 7;
        node3.choices.Add(choice3_1);
        
        DialogueChoice choice3_2 = new DialogueChoice();
        choice3_2.choiceText = "I care nothing for ancient powers.";
        choice3_2.nextDialogueIndex = -1;
        choice3_2.moonAlignmentChange = 3f;
        node3.choices.Add(choice3_2);
        
        dialogue.nodes.Add(node3);
        
        // Node 4 - Sun path explanation
        DialogueNode node4 = new DialogueNode();
        node4.speakerName = "Elder Theron";
        node4.dialogueText = "The Sun's path teaches compassion, justice, and sacrifice for the greater good. Those who walk it become protectors of the innocent, even when the innocent have wronged them. It is the hardest path, for it requires forgiveness.";
        node4.isEndNode = true;
        node4.triggersEvent = true;
        node4.eventName = "BecomeAlly";
        dialogue.nodes.Add(node4);
        
        // Node 5 - Moon path commitment
        DialogueNode node5 = new DialogueNode();
        node5.speakerName = "Elder Theron";
        node5.dialogueText = "Then thou hast chosen thy path. The Moon grants power to those who embrace their darkness. But remember, young one - power without purpose is merely destruction.";
        node5.isEndNode = true;
        dialogue.nodes.Add(node5);
        
        // Node 6 - Redemption
        DialogueNode node6 = new DialogueNode();
        node6.speakerName = "Elder Theron";
        node6.dialogueText = "Wisdom comes to those who can change their minds. The Eclipse path may suit thee - balancing light and shadow, using both compassion and strength as the situation demands.";
        node6.isEndNode = true;
        dialogue.nodes.Add(node6);
        
        // Node 7 - Bloodline hint
        DialogueNode node7 = new DialogueNode();
        node7.speakerName = "Elder Theron";
        node7.dialogueText = "I have said too much already. Seek the ancient ruins to the north when thou art ready. The truth of thy heritage awaits, but only when thou hast proven thyself worthy.";
        node7.isEndNode = true;
        node7.triggersEvent = true;
        node7.eventName = "GiveQuest";
        dialogue.nodes.Add(node7);
        
        return dialogue;
    }
    
    public static DialogueTree CreateMerchantDialogue()
    {
        DialogueTree dialogue = new DialogueTree();
        dialogue.treeName = "Suspicious Merchant";
        dialogue.nodes = new List<DialogueNode>();
        
        // Node 0 - Initial greeting
        DialogueNode node0 = new DialogueNode();
        node0.speakerName = "Gareth the Trader";
        node0.dialogueText = "Well, well... if it isn't the village outcast. I've got wares that might interest someone in thy... position. Weapons, potions, information... all for the right price.";
        node0.choices = new List<DialogueChoice>();
        
        DialogueChoice choice1 = new DialogueChoice();
        choice1.choiceText = "What kind of information?";
        choice1.nextDialogueIndex = 1;
        node0.choices.Add(choice1);
        
        DialogueChoice choice2 = new DialogueChoice();
        choice2.choiceText = "I don't trust merchants who prey on desperation.";
        choice2.nextDialogueIndex = 2;
        choice2.sunAlignmentChange = 3f;
        node0.choices.Add(choice2);
        
        DialogueChoice choice3 = new DialogueChoice();
        choice3.choiceText = "Show me your wares.";
        choice3.nextDialogueIndex = 3;
        node0.choices.Add(choice3);
        
        dialogue.nodes.Add(node0);
        
        // Node 1 - Information
        DialogueNode node1 = new DialogueNode();
        node1.speakerName = "Gareth the Trader";
        node1.dialogueText = "Ah, a seeker of knowledge! I know of secret paths through the Darkwood, of hidden caches left by fallen adventurers... and of certain nobles who might pay handsomely for thy... services.";
        node1.choices = new List<DialogueChoice>();
        
        DialogueChoice choice1_1 = new DialogueChoice();
        choice1_1.choiceText = "What kind of services?";
        choice1_1.nextDialogueIndex = 4;
        choice1_1.moonAlignmentChange = 5f;
        node1.choices.Add(choice1_1);
        
        DialogueChoice choice1_2 = new DialogueChoice();
        choice1_2.choiceText = "I'm not interested in serving nobles.";
        choice1_2.nextDialogueIndex = 5;
        choice1_2.sunAlignmentChange = 2f;
        node1.choices.Add(choice1_2);
        
        dialogue.nodes.Add(node1);
        
        // Node 2 - Moral stance
        DialogueNode node2 = new DialogueNode();
        node2.speakerName = "Gareth the Trader";
        node2.dialogueText = "Desperation? Ha! I offer opportunity to those the world has forgotten. But if thou art too proud for my help... well, pride won't fill thy belly or sharpen thy blade.";
        node2.choices = new List<DialogueChoice>();
        
        DialogueChoice choice2_1 = new DialogueChoice();
        choice2_1.choiceText = "You're right. I need all the help I can get.";
        choice2_1.nextDialogueIndex = 3;
        choice2_1.moonAlignmentChange = 3f;
        choice2_1.sunAlignmentChange = -2f;
        node2.choices.Add(choice2_1);
        
        DialogueChoice choice2_2 = new DialogueChoice();
        choice2_2.choiceText = "I'll find my own way.";
        choice2_2.nextDialogueIndex = -1;
        choice2_2.sunAlignmentChange = 5f;
        node2.choices.Add(choice2_2);
        
        dialogue.nodes.Add(node2);
        
        // Node 3 - Trade
        DialogueNode node3 = new DialogueNode();
        node3.speakerName = "Gareth the Trader";
        node3.dialogueText = "Excellent! I have healing potions, enchanted weapons, and even a map to a hidden treasure. What catches thy interest?";
        node3.isEndNode = true;
        node3.triggersEvent = true;
        node3.eventName = "TradeItems";
        dialogue.nodes.Add(node3);
        
        // Node 4 - Dark services
        DialogueNode node4 = new DialogueNode();
        node4.speakerName = "Gareth the Trader";
        node4.dialogueText = "Ah, a pragmatist! Lord Blackthorne seeks someone to... resolve a dispute with a rival. No questions asked, good coin paid. Interested?";
        node4.choices = new List<DialogueChoice>();
        
        DialogueChoice choice4_1 = new DialogueChoice();
        choice4_1.choiceText = "I accept. Give me the details.";
        choice4_1.nextDialogueIndex = 6;
        choice4_1.moonAlignmentChange = 15f;
        node4.choices.Add(choice4_1);
        
        DialogueChoice choice4_2 = new DialogueChoice();
        choice4_2.choiceText = "I won't be anyone's assassin.";
        choice4_2.nextDialogueIndex = 7;
        choice4_2.sunAlignmentChange = 10f;
        node4.choices.Add(choice4_2);
        
        dialogue.nodes.Add(node4);
        
        // Node 5 - Reject nobles
        DialogueNode node5 = new DialogueNode();
        node5.speakerName = "Gareth the Trader";
        node5.dialogueText = "Admirable, if foolish. The nobles care nothing for thee anyway. But perhaps I can interest thee in something else... a weapon that once belonged to a legendary hero?";
        node5.isEndNode = true;
        node5.triggersEvent = true;
        node5.eventName = "TradeItems";
        dialogue.nodes.Add(node5);
        
        // Node 6 - Accept dark quest
        DialogueNode node6 = new DialogueNode();
        node6.speakerName = "Gareth the Trader";
        node6.dialogueText = "Excellent! Thou art to eliminate Sir Roderick at the old mill tonight. Make it look like bandits. Here's thy advance payment... and remember, we never spoke.";
        node6.isEndNode = true;
        node6.triggersEvent = true;
        node6.eventName = "BecomeEnemy";
        dialogue.nodes.Add(node6);
        
        // Node 7 - Refuse assassination
        DialogueNode node7 = new DialogueNode();
        node7.speakerName = "Gareth the Trader";
        node7.dialogueText = "Pity. Thou could have been useful. But perhaps it's better this way... I'd hate to have to eliminate thee later for knowing too much.";
        node7.isEndNode = true;
        node7.triggersEvent = true;
        node7.eventName = "Betray";
        dialogue.nodes.Add(node7);
        
        return dialogue;
    }
}
